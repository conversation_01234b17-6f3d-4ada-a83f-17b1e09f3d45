# 固件管理系统后端开发文档

## 📋 文档概述

本文档描述固件管理系统后端的开发方案，采用与现有项目一致的架构模式，遵循项目开发规范，确保代码质量和可维护性。

## 🏗️ 项目架构设计

### 架构原则
- **页面对应模式**: 一个前端页面对应一个后端路由文件
- **简洁实用**: 避免过度设计，专注核心业务
- **ORM优先**: 充分利用SQLAlchemy ORM优势
- **规范统一**: 严格遵循现有项目开发规范

### 整体架构图
```
固件管理后端架构
├── routes/                    # 路由层（按页面分文件）
│   ├── firmware_all.py       # 所有固件页面API
│   ├── firmware_pending.py   # 待审核页面API  
│   ├── firmware_usage.py     # 使用记录页面API
│   └── firmware_obsolete.py  # 作废版本页面API
├── models/                    # 数据模型层
│   └── firmware.py           # 固件相关数据模型
└── utils/                     # 工具层
    └── firmware_utils.py     # 固件工具函数
```

## 📂 详细项目结构

### 1. 数据模型层 (models/firmware.py)

#### 1.1 模型设计
```python
# models/firmware.py
from database.db_manager import db
from sqlalchemy import JSON
from datetime import datetime

class Firmware(db.Model):
    """固件主表模型"""
    __tablename__ = 'firmware'
    
    # 基本信息
    serial_number = db.Column(db.String(20), primary_key=True, comment='ERP流水号')
    name = db.Column(db.String(50), nullable=False, comment='固件名称')
    version = db.Column(db.String(20), nullable=False, comment='版本号')
    status = db.Column(db.Enum('active', 'pending', 'rejected', 'obsolete'), 
                      nullable=False, default='pending', comment='状态')
    source = db.Column(db.Enum('新发布', '升级', '修改'), 
                      nullable=False, default='新发布', comment='来源类型')
    
    # 业务信息
    products = db.Column(JSON, nullable=False, comment='适用产品JSON数组')
    version_requirements = db.Column(db.Text, comment='版本使用要求')
    description = db.Column(db.Text, nullable=False, comment='变更内容')
    developer = db.Column(db.String(20), nullable=False, comment='研发者')
    uploader = db.Column(db.String(20), nullable=False, comment='上传者')
    parent_sn = db.Column(db.String(20), db.ForeignKey('firmware.serial_number'), comment='父版本')
    
    # 审核信息（性能优化冗余）
    approver_name = db.Column(db.String(20), comment='审核者姓名')
    reject_reason = db.Column(db.Text, comment='拒绝理由')
    
    # 统计信息
    download_count = db.Column(db.Integer, nullable=False, default=0, comment='下载次数')
    usage_count = db.Column(db.Integer, nullable=False, default=0, comment='使用次数')
    
    # 时间信息
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='创建时间')
    approve_time = db.Column(db.DateTime, comment='审核通过时间')
    obsolete_time = db.Column(db.DateTime, comment='作废时间')
    update_time = db.Column(db.DateTime, nullable=False, default=datetime.now, 
                           onupdate=datetime.now, comment='更新时间')
    
    # 软删除标记
    is_deleted = db.Column(db.Boolean, nullable=False, default=False, comment='软删除标记')
    
    # 关系定义
    children = db.relationship('Firmware', backref=db.backref('parent', remote_side=[serial_number]))
    files = db.relationship('FirmwareFile', backref='firmware', lazy='dynamic')
    download_records = db.relationship('DownloadRecord', backref='firmware', lazy='dynamic')
    approval_flows = db.relationship('ApprovalFlow', backref='firmware', lazy='dynamic')
    
    def to_dict(self):
        """转换为字典格式，供API返回"""
        return {
            'serialNumber': self.serial_number,
            'name': self.name,
            'version': self.version,
            'status': self.status,
            'source': self.source,
            'products': self.products or [],
            'versionRequirements': self.version_requirements,
            'description': self.description,
            'developer': self.developer,
            'uploader': self.uploader,
            'approverName': self.approver_name,
            'downloadCount': self.download_count,
            'usageCount': self.usage_count,
            'createTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'approveTime': self.approve_time.strftime('%Y-%m-%d %H:%M:%S') if self.approve_time else None,
            'obsoleteTime': self.obsolete_time.strftime('%Y-%m-%d %H:%M:%S') if self.obsolete_time else None
        }

class FirmwareFile(db.Model):
    """固件文件表模型"""
    __tablename__ = 'firmware_file'
    
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    serial_number = db.Column(db.String(20), db.ForeignKey('firmware.serial_number'), 
                             nullable=False, comment='关联固件')
    file_path = db.Column(db.String(500), nullable=False, comment='文件路径')
    original_filename = db.Column(db.String(255), comment='原始文件名')
    file_hash = db.Column(db.String(64), nullable=False, comment='SHA256哈希')
    file_size = db.Column(db.Numeric(5, 2), nullable=False, comment='文件大小MB')
    upload_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='上传时间')
    is_deleted = db.Column(db.Boolean, nullable=False, default=False, comment='软删除标记')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now)

class DownloadRecord(db.Model):
    """下载记录表模型"""
    __tablename__ = 'download_record'
    
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    serial_number = db.Column(db.String(20), db.ForeignKey('firmware.serial_number'), 
                             nullable=False, comment='固件流水号')
    work_order = db.Column(db.String(50), nullable=False, comment='工单号')
    product_code = db.Column(db.String(50), nullable=False, comment='产品编码')
    product_model = db.Column(db.String(50), nullable=False, comment='产品型号')
    burn_count = db.Column(db.Integer, nullable=False, comment='烧录数量')
    software_version = db.Column(db.String(20), nullable=False, comment='软件版本')
    build_time = db.Column(db.DateTime, nullable=False, comment='构建时间')
    backplane_version = db.Column(db.String(20), nullable=False, comment='背板版本')
    io_version = db.Column(db.String(20), nullable=False, comment='高速IO版本')
    user_name = db.Column(db.String(20), nullable=False, comment='使用人')
    notes = db.Column(db.Text, comment='备注信息')
    is_deleted = db.Column(db.Boolean, nullable=False, default=False, comment='软删除标记')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now)

class ApprovalFlow(db.Model):
    """审核流水表模型"""
    __tablename__ = 'approval_flow'
    
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    serial_number = db.Column(db.String(20), db.ForeignKey('firmware.serial_number'), 
                             nullable=False, comment='固件流水号')
    action = db.Column(db.Enum('submit', 'approve', 'reject', 'obsolete'), 
                      nullable=False, comment='操作类型')
    operator_name = db.Column(db.String(20), nullable=False, comment='操作人姓名')
    notes = db.Column(db.Text, comment='备注')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now)
```

### 2. 路由层设计

#### 2.1 所有固件页面API (routes/firmware_all.py)
```python
# routes/firmware_all.py
from flask import Blueprint, request, jsonify, current_app
from models.firmware import Firmware, FirmwareFile, DownloadRecord, ApprovalFlow
from database.db_manager import DatabaseManager
from utils.firmware_utils import FirmwareUtils
import logging

logger = logging.getLogger(__name__)
firmware_all_bp = Blueprint('firmware_all', __name__)
db_manager = DatabaseManager()

@firmware_all_bp.route('/api/firmware/all/list', methods=['GET'])
def get_firmware_list():
    """获取所有固件列表（已生效和审核退回）"""
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        search_query = request.args.get('search', '', type=str)
        
        # 使用ORM查询
        query = Firmware.query.filter(
            Firmware.is_deleted == False,
            Firmware.status.in_(['active', 'rejected'])
        )
        
        # 搜索过滤
        if search_query:
            query = query.filter(
                db.or_(
                    Firmware.serial_number.contains(search_query),
                    Firmware.name.contains(search_query),
                    Firmware.developer.contains(search_query)
                )
            )
        
        # 分页查询
        pagination = query.paginate(page=page, per_page=page_size, error_out=False)
        
        return jsonify({
            'code': 200,
            'message': '查询成功',
            'data': {
                'items': [firmware.to_dict() for firmware in pagination.items],
                'total': pagination.total,
                'page': page,
                'page_size': page_size,
                'total_pages': pagination.pages
            }
        })
        
    except Exception as e:
        logger.error(f"获取固件列表失败: {str(e)}")
        return jsonify({'code': 500, 'message': '服务器内部错误'}), 500

@firmware_all_bp.route('/api/firmware/all/<serial_number>', methods=['GET'])
def get_firmware_detail(serial_number):
    """获取固件详情"""
    try:
        firmware = Firmware.query.filter_by(
            serial_number=serial_number, 
            is_deleted=False
        ).first()
        
        if not firmware:
            return jsonify({'code': 404, 'message': '固件不存在'}), 404
            
        return jsonify({
            'code': 200,
            'message': '查询成功',
            'data': firmware.to_dict()
        })
        
    except Exception as e:
        logger.error(f"获取固件详情失败: {str(e)}")
        return jsonify({'code': 500, 'message': '服务器内部错误'}), 500

@firmware_all_bp.route('/api/firmware/all/upload', methods=['POST'])
def upload_firmware():
    """上传新固件"""
    try:
        data = request.get_json()
        
        # 数据验证
        required_fields = ['serialNumber', 'name', 'version', 'developer', 'description']
        if not all(field in data for field in required_fields):
            return jsonify({'code': 400, 'message': '缺少必要字段'}), 400
        
        # 检查流水号重复
        existing = Firmware.query.filter_by(
            serial_number=data['serialNumber']
        ).first()
        if existing:
            return jsonify({'code': 400, 'message': 'ERP流水号已存在'}), 400
        
        # 创建固件记录
        firmware = Firmware(
            serial_number=data['serialNumber'],
            name=data['name'],
            version=data['version'],
            developer=data['developer'],
            uploader=data.get('uploader', '当前用户'),
            description=data['description'],
            products=data.get('products', []),
            version_requirements=data.get('versionRequirements', ''),
            source=data.get('source', '新发布'),
            parent_sn=data.get('parentSn')
        )
        
        db_manager.session.add(firmware)
        
        # 记录审核流水
        approval = ApprovalFlow(
            serial_number=data['serialNumber'],
            action='submit',
            operator_name=data.get('uploader', '当前用户'),
            notes='提交审核'
        )
        db_manager.session.add(approval)
        
        db_manager.session.commit()
        
        logger.info(f"固件上传成功: {data['serialNumber']}")
        return jsonify({'code': 200, 'message': '上传成功，已进入审核流程'})
        
    except Exception as e:
        db_manager.session.rollback()
        logger.error(f"固件上传失败: {str(e)}")
        return jsonify({'code': 500, 'message': '上传失败'}), 500

@firmware_all_bp.route('/api/firmware/all/<serial_number>/download', methods=['POST'])
def download_firmware(serial_number):
    """下载固件"""
    try:
        data = request.get_json()
        
        # 验证固件存在且状态为active
        firmware = Firmware.query.filter_by(
            serial_number=serial_number,
            status='active',
            is_deleted=False
        ).first()
        
        if not firmware:
            return jsonify({'code': 404, 'message': '固件不存在或未生效'}), 404
        
        # 创建下载记录
        download_record = DownloadRecord(
            serial_number=serial_number,
            work_order=data['workOrder'],
            product_code=data['productCode'],
            product_model=data['productModel'],
            burn_count=data['burnCount'],
            software_version=data['softwareVersion'],
            build_time=data['buildTime'],
            backplane_version=data['backplaneVersion'],
            io_version=data['highSpeedIOVersion'],
            user_name=data.get('userName', '当前用户'),
            notes=data.get('notes', '')
        )
        
        # 更新下载统计
        firmware.download_count += 1
        firmware.usage_count += data['burnCount']
        
        db_manager.session.add(download_record)
        db_manager.session.commit()
        
        logger.info(f"固件下载成功: {serial_number}")
        return jsonify({'code': 200, 'message': '下载成功'})
        
    except Exception as e:
        db_manager.session.rollback()
        logger.error(f"固件下载失败: {str(e)}")
        return jsonify({'code': 500, 'message': '下载失败'}), 500

@firmware_all_bp.route('/api/firmware/all/<serial_number>/history', methods=['GET'])
def get_version_history(serial_number):
    """获取版本历史"""
    try:
        # 使用ORM查询版本链
        history_list = []
        current_sn = serial_number
        
        while current_sn:
            firmware = Firmware.query.filter_by(
                serial_number=current_sn,
                is_deleted=False
            ).first()
            
            if firmware:
                history_list.append(firmware.to_dict())
                current_sn = firmware.parent_sn
            else:
                break
        
        return jsonify({
            'code': 200,
            'message': '查询成功',
            'data': history_list
        })
        
    except Exception as e:
        logger.error(f"获取版本历史失败: {str(e)}")
        return jsonify({'code': 500, 'message': '查询失败'}), 500
```

#### 2.2 待审核页面API (routes/firmware_pending.py)
```python
# routes/firmware_pending.py
from flask import Blueprint, request, jsonify
from models.firmware import Firmware, ApprovalFlow
from database.db_manager import DatabaseManager
from datetime import datetime
import logging

logger = logging.getLogger(__name__)
firmware_pending_bp = Blueprint('firmware_pending', __name__)
db_manager = DatabaseManager()

@firmware_pending_bp.route('/api/firmware/pending/list', methods=['GET'])
def get_pending_list():
    """获取待审核固件列表"""
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        
        # 使用ORM查询待审核固件
        query = Firmware.query.filter(
            Firmware.status == 'pending',
            Firmware.is_deleted == False
        ).order_by(Firmware.create_time.desc())
        
        pagination = query.paginate(page=page, per_page=page_size, error_out=False)
        
        return jsonify({
            'code': 200,
            'message': '查询成功',
            'data': {
                'items': [firmware.to_dict() for firmware in pagination.items],
                'total': pagination.total,
                'page': page,
                'page_size': page_size,
                'total_pages': pagination.pages
            }
        })
        
    except Exception as e:
        logger.error(f"获取待审核列表失败: {str(e)}")
        return jsonify({'code': 500, 'message': '服务器内部错误'}), 500

@firmware_pending_bp.route('/api/firmware/pending/<serial_number>/approve', methods=['POST'])
def approve_firmware(serial_number):
    """审核通过固件"""
    try:
        data = request.get_json()
        approver_name = data.get('approverName', '当前用户')
        
        # 查询待审核固件
        firmware = Firmware.query.filter_by(
            serial_number=serial_number,
            status='pending',
            is_deleted=False
        ).first()
        
        if not firmware:
            return jsonify({'code': 404, 'message': '固件不存在或已处理'}), 404
        
        # 更新固件状态
        firmware.status = 'active'
        firmware.approver_name = approver_name
        firmware.approve_time = datetime.now()
        
        # 如果是升级版本，作废旧版本
        if firmware.parent_sn:
            parent_firmware = Firmware.query.filter_by(
                serial_number=firmware.parent_sn
            ).first()
            if parent_firmware and parent_firmware.status == 'active':
                parent_firmware.status = 'obsolete'
                parent_firmware.obsolete_time = datetime.now()
        
        # 记录审核流水
        approval = ApprovalFlow(
            serial_number=serial_number,
            action='approve',
            operator_name=approver_name,
            notes='审核通过'
        )
        db_manager.session.add(approval)
        
        db_manager.session.commit()
        
        logger.info(f"固件审核通过: {serial_number}")
        return jsonify({'code': 200, 'message': '审核通过'})
        
    except Exception as e:
        db_manager.session.rollback()
        logger.error(f"审核通过失败: {str(e)}")
        return jsonify({'code': 500, 'message': '审核失败'}), 500

@firmware_pending_bp.route('/api/firmware/pending/<serial_number>/reject', methods=['POST'])
def reject_firmware(serial_number):
    """审核拒绝固件"""
    try:
        data = request.get_json()
        reject_reason = data.get('rejectReason', '')
        approver_name = data.get('approverName', '当前用户')
        
        if not reject_reason:
            return jsonify({'code': 400, 'message': '请输入拒绝理由'}), 400
        
        # 查询待审核固件
        firmware = Firmware.query.filter_by(
            serial_number=serial_number,
            status='pending',
            is_deleted=False
        ).first()
        
        if not firmware:
            return jsonify({'code': 404, 'message': '固件不存在或已处理'}), 404
        
        # 更新固件状态
        firmware.status = 'rejected'
        firmware.approver_name = approver_name
        firmware.reject_reason = reject_reason
        
        # 记录审核流水
        approval = ApprovalFlow(
            serial_number=serial_number,
            action='reject',
            operator_name=approver_name,
            notes=reject_reason
        )
        db_manager.session.add(approval)
        
        db_manager.session.commit()
        
        logger.info(f"固件审核拒绝: {serial_number}")
        return jsonify({'code': 200, 'message': '已拒绝该固件'})
        
    except Exception as e:
        db_manager.session.rollback()
        logger.error(f"审核拒绝失败: {str(e)}")
        return jsonify({'code': 500, 'message': '操作失败'}), 500
```

#### 2.3 使用记录页面API (routes/firmware_usage.py)
```python
# routes/firmware_usage.py  
from flask import Blueprint, request, jsonify
from models.firmware import DownloadRecord, Firmware
from database.db_manager import DatabaseManager
from sqlalchemy import func
import logging

logger = logging.getLogger(__name__)
firmware_usage_bp = Blueprint('firmware_usage', __name__)
db_manager = DatabaseManager()

@firmware_usage_bp.route('/api/firmware/usage/records', methods=['GET'])
def get_usage_records():
    """获取使用记录列表"""
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        search_query = request.args.get('search', '', type=str)
        
        # 使用ORM查询，JOIN获取固件信息
        query = db_manager.session.query(DownloadRecord, Firmware).join(
            Firmware, DownloadRecord.serial_number == Firmware.serial_number
        ).filter(DownloadRecord.is_deleted == False)
        
        # 搜索过滤
        if search_query:
            query = query.filter(
                db.or_(
                    DownloadRecord.work_order.contains(search_query),
                    DownloadRecord.product_code.contains(search_query),
                    DownloadRecord.product_model.contains(search_query),
                    DownloadRecord.user_name.contains(search_query)
                )
            )
        
        # 按时间倒序
        query = query.order_by(DownloadRecord.create_time.desc())
        
        # 分页
        pagination = query.paginate(page=page, per_page=page_size, error_out=False)
        
        # 组装返回数据
        items = []
        for record, firmware in pagination.items:
            item = {
                'id': record.id,
                'serialNumber': record.serial_number,
                'firmwareName': firmware.name,
                'firmwareVersion': firmware.version,
                'workOrder': record.work_order,
                'productCode': record.product_code,
                'productModel': record.product_model,
                'burnCount': record.burn_count,
                'softwareVersion': record.software_version,
                'buildTime': record.build_time.strftime('%Y-%m-%d %H:%M:%S'),
                'backplaneVersion': record.backplane_version,
                'ioVersion': record.io_version,
                'userName': record.user_name,
                'notes': record.notes,
                'usageTime': record.create_time.strftime('%Y-%m-%d %H:%M:%S')
            }
            items.append(item)
        
        return jsonify({
            'code': 200,
            'message': '查询成功',
            'data': {
                'items': items,
                'total': pagination.total,
                'page': page,
                'page_size': page_size,
                'total_pages': pagination.pages
            }
        })
        
    except Exception as e:
        logger.error(f"获取使用记录失败: {str(e)}")
        return jsonify({'code': 500, 'message': '服务器内部错误'}), 500

@firmware_usage_bp.route('/api/firmware/usage/statistics', methods=['GET'])
def get_usage_statistics():
    """获取使用统计信息"""
    try:
        # 使用ORM聚合查询
        total_records = DownloadRecord.query.filter_by(is_deleted=False).count()
        total_firmware_count = db_manager.session.query(func.count(func.distinct(DownloadRecord.serial_number))).filter_by(is_deleted=False).scalar()
        total_production = db_manager.session.query(func.sum(DownloadRecord.burn_count)).filter_by(is_deleted=False).scalar() or 0
        
        return jsonify({
            'code': 200,
            'message': '查询成功',
            'data': {
                'totalRecords': total_records,
                'firmwareCount': total_firmware_count,
                'totalProduction': total_production
            }
        })
        
    except Exception as e:
        logger.error(f"获取使用统计失败: {str(e)}")
        return jsonify({'code': 500, 'message': '查询失败'}), 500
```

#### 2.4 作废版本页面API (routes/firmware_obsolete.py)
```python
# routes/firmware_obsolete.py
from flask import Blueprint, request, jsonify
from models.firmware import Firmware
from database.db_manager import DatabaseManager
from sqlalchemy import func
import logging

logger = logging.getLogger(__name__)
firmware_obsolete_bp = Blueprint('firmware_obsolete', __name__)
db_manager = DatabaseManager()

@firmware_obsolete_bp.route('/api/firmware/obsolete/list', methods=['GET'])
def get_obsolete_list():
    """获取作废固件列表"""
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        search_query = request.args.get('search', '', type=str)
        
        # 使用ORM查询作废固件
        query = Firmware.query.filter(
            Firmware.status == 'obsolete',
            Firmware.is_deleted == False
        )
        
        # 搜索过滤
        if search_query:
            query = query.filter(
                db.or_(
                    Firmware.serial_number.contains(search_query),
                    Firmware.name.contains(search_query),
                    Firmware.developer.contains(search_query)
                )
            )
        
        # 按作废时间倒序
        query = query.order_by(Firmware.obsolete_time.desc())
        
        pagination = query.paginate(page=page, per_page=page_size, error_out=False)
        
        # 计算生效天数
        items = []
        for firmware in pagination.items:
            item = firmware.to_dict()
            if firmware.approve_time and firmware.obsolete_time:
                days = (firmware.obsolete_time - firmware.approve_time).days
                item['effectiveDays'] = days
            else:
                item['effectiveDays'] = 0
            items.append(item)
        
        return jsonify({
            'code': 200,
            'message': '查询成功',
            'data': {
                'items': items,
                'total': pagination.total,
                'page': page,
                'page_size': page_size,
                'total_pages': pagination.pages
            }
        })
        
    except Exception as e:
        logger.error(f"获取作废列表失败: {str(e)}")
        return jsonify({'code': 500, 'message': '服务器内部错误'}), 500
```

### 3. 工具层设计 (utils/firmware_utils.py)

```python
# utils/firmware_utils.py
import hashlib
import os
from werkzeug.utils import secure_filename
import pandas as pd
from datetime import datetime

class FirmwareUtils:
    """固件工具类"""
    
    ALLOWED_EXTENSIONS = {'.bin', '.hex', '.zip', '.tar.gz'}
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    
    @staticmethod
    def validate_file(file):
        """验证上传文件"""
        if not file or not file.filename:
            return False, "请选择文件"
        
        # 检查文件扩展名
        filename = secure_filename(file.filename)
        ext = os.path.splitext(filename)[1].lower()
        if ext not in FirmwareUtils.ALLOWED_EXTENSIONS:
            return False, f"不支持的文件格式，支持格式: {', '.join(FirmwareUtils.ALLOWED_EXTENSIONS)}"
        
        # 检查文件大小
        file.seek(0, os.SEEK_END)
        size = file.tell()
        file.seek(0)
        if size > FirmwareUtils.MAX_FILE_SIZE:
            return False, f"文件大小超限，最大支持 {FirmwareUtils.MAX_FILE_SIZE // 1024 // 1024}MB"
        
        return True, "验证通过"
    
    @staticmethod
    def calculate_file_hash(file_path):
        """计算文件SHA256哈希值"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    @staticmethod
    def generate_file_path(serial_number, original_filename):
        """生成文件存储路径"""
        now = datetime.now()
        year = now.strftime('%Y')
        month = now.strftime('%m')
        
        # 构建存储路径: uploads/firmware/年/月/流水号/文件名
        relative_path = f"firmware/{year}/{month}/{serial_number}"
        full_dir = os.path.join("uploads", relative_path)
        
        # 确保目录存在
        os.makedirs(full_dir, exist_ok=True)
        
        filename = secure_filename(original_filename)
        file_path = os.path.join(full_dir, filename)
        
        return file_path, os.path.join(relative_path, filename)
    
    @staticmethod
    def export_to_excel(data_list, sheet_name, filename=None):
        """导出数据到Excel"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{sheet_name}_{timestamp}.xlsx"
        
        # 创建DataFrame
        df = pd.DataFrame(data_list)
        
        # 导出到Excel
        export_path = os.path.join("temp", filename)
        os.makedirs("temp", exist_ok=True)
        
        with pd.ExcelWriter(export_path, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 设置列宽
            worksheet = writer.sheets[sheet_name]
            for idx, col in enumerate(df.columns):
                worksheet.set_column(idx, idx, max(len(col), 15))
        
        return export_path
    
    @staticmethod
    def validate_serial_number(serial_number):
        """验证ERP流水号格式"""
        if not serial_number or len(serial_number) > 20:
            return False, "ERP流水号不能为空且长度不能超过20位"
        
        # 可以添加更多格式验证规则
        return True, "验证通过"
```

## 🔧 实施方案

### 实施步骤

#### 第一阶段：基础架构搭建
1. **数据模型创建** (1天)
   - 创建 `models/firmware.py`
   - 定义所有数据模型和关系
   - 执行数据库迁移

2. **工具类实现** (1天)
   - 创建 `utils/firmware_utils.py`
   - 实现文件处理、验证、导出等工具函数

#### 第二阶段：核心功能开发
3. **所有固件页面API** (2天)
   - 实现 `routes/firmware_all.py`
   - 固件列表、详情、上传、下载、版本历史

4. **待审核页面API** (1天)
   - 实现 `routes/firmware_pending.py`
   - 审核通过、审核拒绝功能

#### 第三阶段：扩展功能开发  
5. **使用记录页面API** (1天)
   - 实现 `routes/firmware_usage.py`
   - 使用记录查询、统计分析

6. **作废版本页面API** (1天)
   - 实现 `routes/firmware_obsolete.py`
   - 作废版本查询、生效天数计算

#### 第四阶段：集成测试
7. **蓝图注册和测试** (1天)
   - 在 `app.py` 中注册所有蓝图
   - 接口测试和前后端联调

### 蓝图注册
```python
# 在 app.py 中添加
from routes.firmware_all import firmware_all_bp
from routes.firmware_pending import firmware_pending_bp
from routes.firmware_usage import firmware_usage_bp
from routes.firmware_obsolete import firmware_obsolete_bp

# 注册蓝图
app.register_blueprint(firmware_all_bp)
app.register_blueprint(firmware_pending_bp)
app.register_blueprint(firmware_usage_bp)
app.register_blueprint(firmware_obsolete_bp)
```

## 📋 开发规范

### 命名规范
- **Python文件**: snake_case (firmware_all.py)
- **类名**: PascalCase (FirmwareUtils)
- **函数/变量**: snake_case (get_firmware_list)
- **API路径**: kebab-case (/api/firmware/all/list)
- **数据库字段**: snake_case (serial_number)
- **前端字段**: camelCase (serialNumber)

### 代码质量要求
- 遵循PEP 8编码规范
- 充分利用SQLAlchemy ORM，避免原生SQL
- 添加详细的中文注释和日志
- 统一的错误处理和响应格式
- 必要的数据验证和安全检查

### 测试要求
- 每个API接口需要编写基础测试用例
- 模型层需要单元测试覆盖
- 关键业务逻辑需要集成测试

## 🎯 预期收益

### 开发效率
- **总工作量**: 约7个工作日
- **并行开发**: 支持多人协作
- **维护成本**: 低（页面独立，影响范围可控）

### 性能表现
- **查询性能**: 充分利用ORM和索引优化
- **并发支持**: Flask + SQLAlchemy 成熟方案
- **扩展性**: 模块化设计便于功能扩展

### 代码质量
- **可读性**: 清晰的模块划分和命名规范
- **可维护性**: 职责单一，耦合度低
- **可测试性**: 良好的分层架构便于测试

本方案充分考虑了现有项目的架构特点和开发习惯，确保新功能能够无缝集成到现有系统中。 