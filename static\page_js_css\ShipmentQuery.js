// 使用IIFE创建模块作用域
(function() {
    // 分页状态管理
    window.shipmentQueryState = {
        currentPage: 1,
        pageSize: 10,
        pageSizeOptions: [10, 20, 50, 100],
        totalCount: 0,
        totalPages: 0,
        queryType: 'productSN', // 默认查询类型：产品SN号
        queryValue: '', // 查询值
        sortField: '', // 添加排序字段
        sortOrder: 'asc', // 添加排序顺序
        isInitialSearch: true, // 添加标志，用于跟踪是否是初始查询
        selectedRecords: new Set(), // 添加用于跟踪选中记录的集合
        allSelected: false // 添加用于跟踪是否全选的标志
    };

    function initShipmentQueryPage() {
        // 加载SweetAlert2库
        if (!document.getElementById('sweetalert2-script')) {
            const script = document.createElement('script');
            script.id = 'sweetalert2-script';
            script.src = '/static/lib/sweetalert2/sweetalert2.min.js';
            document.head.appendChild(script);
            
            // 如果需要CSS（通常SweetAlert2的CSS也需要加载）
            if (!document.getElementById('sweetalert2-css')) {
                const link = document.createElement('link');
                link.id = 'sweetalert2-css';
                link.rel = 'stylesheet';
                link.href = '/static/lib/sweetalert2/sweetalert2.min.css';
                document.head.appendChild(link);
            }
        }
        
        const content = document.getElementById('content');
        content.innerHTML = createShipmentQueryHTML();
        
        // 初始化事件监听
        initEventListeners();
    }

    function createShipmentQueryHTML() {
        return `
            <section class="shipment-query">
                <header>
                    <h1 class="shipment-query__title">查询类型</h1>
                </header>
                
                <div class="shipment-query__header">
                    <div class="shipment-query__type">
                        <div class="shipment-query__type-options">
                            <label class="shipment-query__type-option">
                                <input type="radio" name="query-type" value="productSN" class="shipment-query__type-radio" checked>
                                <span class="shipment-query__type-text">产品SN号</span>
                            </label>
                            <label class="shipment-query__type-option">
                                <input type="radio" name="query-type" value="boxNumber" class="shipment-query__type-radio">
                                <span class="shipment-query__type-text">箱单流水号</span>
                            </label>
                            <label class="shipment-query__type-option">
                                <input type="radio" name="query-type" value="shipmentNumber" class="shipment-query__type-radio">
                                <span class="shipment-query__type-text">出库单号</span>
                            </label>
                            <label class="shipment-query__type-option">
                                <input type="radio" name="query-type" value="customerName" class="shipment-query__type-radio">
                                <span class="shipment-query__type-text">客户名称</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="shipment-query__filter">
                        <button type="button" class="shipment-query__filter-btn">
                            <span class="shipment-query__filter-text">高级查询</span>
                            <i class="fas fa-chevron-down shipment-query__filter-icon"></i>
                        </button>
                    </div>
                </div>
                
                <div class="shipment-query__advanced-filter" style="display: none;">
                    <div class="shipment-query__date-range">
                        <div class="shipment-query__date-field">
                            <label class="shipment-query__date-label">开始日期</label>
                            <input type="date" class="shipment-query__date-input" id="start-date">
                        </div>
                        <div class="shipment-query__date-field">
                            <label class="shipment-query__date-label">结束日期</label>
                            <input type="date" class="shipment-query__date-input" id="end-date">
                        </div>
                    </div>
                </div>
                
                <form class="shipment-query__form">
                    <div class="shipment-query__form-group">
                        <input type="text" id="query-input" class="shipment-query__input" placeholder="请输入产品SN号">
                    </div>
                    
                    <div class="shipment-query__buttons">
                        <button type="button" id="search-btn" class="shipment-query__btn shipment-query__btn--search">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <button type="button" id="reset-btn" class="shipment-query__btn shipment-query__btn--reset">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                        <button type="button" id="export-btn" class="shipment-query__btn shipment-query__btn--export" style="display: none;">
                            <i class="fas fa-file-export"></i> 导出数据
                        </button>
                    </div>
                </form>
                
                <div class="shipment-query__results">
                    <div class="shipment-query__table-container">
                        <table class="shipment-query__table">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="select-all-checkbox" class="shipment-query__checkbox">
                                    </th>
                                    <th>序号</th>
                                    <th class="sortable" onclick="window.sortBy('sn_number')">
                                        产品SN号 <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" onclick="window.sortBy('box_serial_number')">
                                        箱单流水号 <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" onclick="window.sortBy('shipping_number')">
                                        出库单号 <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" onclick="window.sortBy('customer_name')">
                                        客户名称 <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" onclick="window.sortBy('created_at')">
                                        出库时间 <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" onclick="window.sortBy('created_user')">
                                        操作人 <i class="fas fa-sort"></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="results-body">
                                <!-- 结果将通过JavaScript动态添加 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="shipment-query__pagination">
                        <div class="shipment-query__pagination-info">
                            <span>每页</span>
                            <select id="page-size" class="shipment-query__page-size">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>条</span>
                            <span class="shipment-query__total">共 <span id="total-count">0</span> 条</span>
                        </div>
                        <div class="shipment-query__pagination-controls" id="pagination-controls">
                            <button class="shipment-query__pagination-btn" title="首页">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button class="shipment-query__pagination-btn" title="上一页">
                                <i class="fas fa-angle-left"></i>
                            </button>
                            <button class="shipment-query__pagination-btn active">1</button>
                            <button class="shipment-query__pagination-btn" title="下一页">
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button class="shipment-query__pagination-btn" title="末页">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        `;
    }

    function initEventListeners() {
        // 查询类型切换
        const queryTypeRadios = document.querySelectorAll('input[name="query-type"]');
        const queryInput = document.getElementById('query-input');
        
        queryTypeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                const queryType = e.target.value;
                window.shipmentQueryState.queryType = queryType;
                
                // 根据查询类型更新输入框占位符
                switch(queryType) {
                    case 'boxNumber':
                        queryInput.placeholder = '请输入箱单流水号';
                        break;
                    case 'shipmentNumber':
                        queryInput.placeholder = '请输入出库单号';
                        break;
                    case 'customerName':
                        queryInput.placeholder = '请输入客户名称';
                        break;
                    case 'productSN':
                        queryInput.placeholder = '请输入产品SN号';
                        break;
                }
            });
        });
        
        // 高级查询按钮点击事件
        const filterBtn = document.querySelector('.shipment-query__filter-btn');
        const advancedFilter = document.querySelector('.shipment-query__advanced-filter');
        
        filterBtn.addEventListener('click', () => {
            const isVisible = advancedFilter.style.display !== 'none';
            advancedFilter.style.display = isVisible ? 'none' : 'block';
            filterBtn.classList.toggle('active');
        });
        
        // 日期选择器初始化
        const startDate = document.getElementById('start-date');
        const endDate = document.getElementById('end-date');
        
        // 清空默认日期值
        startDate.value = '';
        endDate.value = '';
        
        // 确保结束日期不能早于开始日期
        startDate.addEventListener('change', () => {
            if (endDate.value && startDate.value > endDate.value) {
                endDate.value = startDate.value;
            }
            endDate.min = startDate.value;
        });
        
        // 确保开始日期不能晚于结束日期
        endDate.addEventListener('change', () => {
            if (startDate.value && endDate.value < startDate.value) {
                startDate.value = endDate.value;
            }
            startDate.max = endDate.value;
        });
        
        // 查询按钮点击事件
        const searchBtn = document.getElementById('search-btn');
        searchBtn.addEventListener('click', () => {
            const queryValue = queryInput.value.trim();
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            
            // 检查是否至少有一个查询条件（查询值或日期范围）
            if (!queryValue && !startDate && !endDate) {
                Swal.fire({
                    icon: 'warning',
                    title: '请输入至少一个查询条件',
                    text: '可以输入查询内容或选择日期范围',
                    showConfirmButton: false,
                    timer: 2000
                });
                return;
            }
            
            window.shipmentQueryState.queryValue = queryValue;
            window.shipmentQueryState.currentPage = 1; // 重置为第一页
            window.shipmentQueryState.isInitialSearch = true; // 设置为初始查询
            searchShipmentRecords();
        });
        
        // 重置按钮点击事件
        const resetBtn = document.getElementById('reset-btn');
        resetBtn.addEventListener('click', () => {
            queryInput.value = '';
            window.shipmentQueryState.queryValue = '';
            
            // 重置查询类型为默认值
            const defaultRadio = document.querySelector('input[value="productSN"]');
            defaultRadio.checked = true;
            window.shipmentQueryState.queryType = 'productSN';
            queryInput.placeholder = '请输入产品SN号';
            
            // 清空日期输入
            if (document.getElementById('start-date')) {
                document.getElementById('start-date').value = '';
            }
            if (document.getElementById('end-date')) {
                document.getElementById('end-date').value = '';
            }
            
            // 清空结果
            document.getElementById('results-body').innerHTML = '';
            document.getElementById('total-count').textContent = '0';
            document.getElementById('pagination-controls').innerHTML = '';
            window.shipmentQueryState.totalCount = 0;
            window.shipmentQueryState.totalPages = 0;
            
            // 隐藏导出按钮
            hideExportButton();
        });
        
        // 添加全选复选框事件监听
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', handleSelectAll);
        }
        
        // 导出按钮点击事件
        const exportBtn = document.getElementById('export-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', exportData);
        }
        
        // 每页显示数量变更事件
        const pageSizeSelect = document.getElementById('page-size');
        pageSizeSelect.addEventListener('change', (e) => {
            window.shipmentQueryState.pageSize = parseInt(e.target.value);
            window.shipmentQueryState.currentPage = 1; // 重置为第一页
            
            // 检查是否已经有查询结果（通过总记录数判断）
            if (window.shipmentQueryState.totalCount > 0) {
                searchShipmentRecords();
            }
        });
        
        // 回车键触发查询
        queryInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchBtn.click();
            }
        });
    }

    function searchShipmentRecords() {
        const { queryType, queryValue, currentPage, pageSize, sortField, sortOrder, isInitialSearch } = window.shipmentQueryState;
        
        // 获取高级查询面板的显示状态
        const advancedFilterVisible = document.querySelector('.shipment-query__advanced-filter').style.display !== 'none';
        
        // 只有当高级查询面板显示时才获取日期范围
        const startDate = advancedFilterVisible ? document.getElementById('start-date').value : '';
        const endDate = advancedFilterVisible ? document.getElementById('end-date').value : '';
        
        // 添加加载指示器
        const tableContainer = document.querySelector('.shipment-query__table-container');
        if (!document.querySelector('.table-loading-indicator')) {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'table-loading-indicator';
            loadingIndicator.innerHTML = '<div class="loading-spinner"></div>';
            tableContainer.appendChild(loadingIndicator);
        }
        
        // 构建查询参数
        const queryParams = new URLSearchParams({
            type: queryType,
            value: queryValue,
            page: currentPage,
            pageSize: pageSize
        });
        
        // 添加排序参数
        if (sortField && sortField.trim() !== '') {
            queryParams.append('sort_field', sortField);
            queryParams.append('sort_order', sortOrder);
        }
        
        // 如果高级查询面板显示且有日期范围，添加到查询参数中
        if (advancedFilterVisible) {
            if (startDate) {
                queryParams.append('startDate', startDate);
            }
            if (endDate) {
                queryParams.append('endDate', endDate);
            }
        }
        
        // 输出调试信息
        Logger.info('查询参数:', {
            queryType,
            queryValue,
            advancedFilterVisible,
            startDate: advancedFilterVisible ? startDate : '未使用',
            endDate: advancedFilterVisible ? endDate : '未使用'
        });
        
        // 发送API请求
        fetch(`/api/shipment-query/search?${queryParams.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    renderResults(data.records, data.totalCount);
                    // 更新排序图标
                    if (sortField) {
                        updateSortIcons(sortField);
                    }
                    
                    // 只在初始查询时显示查询结果总数通知
                    if (isInitialSearch) {
                        if (data.totalCount > 0) {
                            Swal.fire({
                                icon: 'success',
                                title: '查询成功',
                                text: `共找到 ${data.totalCount} 条匹配记录`,
                                position: 'center',
                                showConfirmButton: false,
                                timer: 2000
                            });
                            
                            // 显示导出按钮
                            showExportButton();
                        } else {
                            Swal.fire({
                                icon: 'info',
                                title: '未找到记录',
                                text: '没有找到匹配的记录，请尝试其他查询条件',
                                position: 'center',
                                showConfirmButton: false,
                                timer: 2000
                            });
                            
                            // 隐藏导出按钮
                            hideExportButton();
                        }
                        
                        // 重置标志，后续的排序和分页操作不会显示通知
                        window.shipmentQueryState.isInitialSearch = false;
                    }
                    
                    // 如果不是初始查询但有数据，确保导出按钮显示
                    if (!isInitialSearch && data.totalCount > 0) {
                        showExportButton();
                    }
                } else {
                    showError(data.message || '查询失败');
                    // 显示错误提示
                    Swal.fire({
                        icon: 'error',
                        title: '查询失败',
                        text: data.message || '查询失败，请稍后重试',
                        confirmButtonText: '确定'
                    });
                    
                    // 隐藏导出按钮
                    hideExportButton();
                }
            })
            .catch(error => {
                Logger.error('查询出错:', error);
                showError('查询出错，请稍后再试');
                // 显示错误提示
                Swal.fire({
                    icon: 'error',
                    title: '查询出错',
                    text: error.message || '请稍后再试',
                    confirmButtonText: '确定'
                });
                
                // 隐藏导出按钮
                hideExportButton();
            })
            .finally(() => {
                // 移除加载指示器
                const loadingIndicator = document.querySelector('.table-loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.remove();
                }
            });
    }

    function renderResults(records, totalCount) {
        const resultsBody = document.getElementById('results-body');
        const totalCountElement = document.getElementById('total-count');
        
        // 更新状态
        window.shipmentQueryState.totalCount = totalCount;
        window.shipmentQueryState.totalPages = Math.ceil(totalCount / window.shipmentQueryState.pageSize);
        
        // 清空选中记录集合（当重新加载数据时）
        window.shipmentQueryState.selectedRecords.clear();
        
        // 重置全选复选框
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
            window.shipmentQueryState.allSelected = false;
        }
        
        // 更新总数显示
        totalCountElement.textContent = totalCount;
        
        // 清空结果表格
        resultsBody.innerHTML = '';
        
        if (records.length === 0) {
            resultsBody.innerHTML = '<tr><td colspan="8" class="shipment-query__no-data">未找到匹配的记录</td></tr>';
            // 清空分页控件
            document.getElementById('pagination-controls').innerHTML = '';
            return;
        }
        
        // 计算序号起始值
        const startIndex = (window.shipmentQueryState.currentPage - 1) * window.shipmentQueryState.pageSize + 1;
        
        // 渲染结果
        records.forEach((record, index) => {
            const row = document.createElement('tr');
            
            // 格式化日期 - 显示年月日
            const createdAt = new Date(record.created_at);
            const formattedDate = `${createdAt.getFullYear()}-${String(createdAt.getMonth() + 1).padStart(2, '0')}-${String(createdAt.getDate()).padStart(2, '0')}`;
            
            // 使用记录的唯一标识作为复选框的data-record-id
            const recordId = record.id || `${record.shipping_number}-${record.sn_number}`;
            
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="shipment-query__checkbox row-checkbox" data-record-id="${recordId}">
                </td>
                <td>${startIndex + index}</td>
                <td>${record.sn_number || '-'}</td>
                <td>${record.box_serial_number}</td>
                <td>${record.shipping_number}</td>
                <td>${record.customer_name}</td>
                <td>${formattedDate}</td>
                <td>${record.created_user}</td>
            `;
            
            resultsBody.appendChild(row);
        });
        
        // 为新添加的复选框添加事件监听器
        document.querySelectorAll('.row-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', handleRowSelect);
        });
        
        // 更新分页控件
        updatePagination();
        
        // 更新导出按钮文本
        updateExportButtonText();
    }

    function updatePagination() {
        const paginationControls = document.getElementById('pagination-controls');
        const { currentPage, totalPages } = window.shipmentQueryState;
        
        // 如果总页数小于等于0，不显示分页
        if (totalPages <= 0) {
            paginationControls.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 首页按钮
        paginationHTML += `
            <button class="shipment-query__pagination-btn ${currentPage === 1 ? 'disabled' : ''}" 
                    onclick="window.goToFirstPage()" ${currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-left"></i>
            </button>
        `;
        
        // 上一页按钮
        paginationHTML += `
            <button class="shipment-query__pagination-btn ${currentPage === 1 ? 'disabled' : ''}" 
                    onclick="window.goToPrevPage()" ${currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-left"></i>
            </button>
        `;
        
        // 页码按钮
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);
        
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="shipment-query__pagination-btn ${i === currentPage ? 'active' : ''}" 
                        onclick="window.goToPage(${i})">
                    ${i}
                </button>
            `;
        }
        
        // 下一页按钮
        paginationHTML += `
            <button class="shipment-query__pagination-btn ${currentPage === totalPages ? 'disabled' : ''}" 
                    onclick="window.goToNextPage()" ${currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-right"></i>
            </button>
        `;
        
        // 末页按钮
        paginationHTML += `
            <button class="shipment-query__pagination-btn ${currentPage === totalPages ? 'disabled' : ''}" 
                    onclick="window.goToLastPage()" ${currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-double-right"></i>
            </button>
        `;
        
        paginationControls.innerHTML = paginationHTML;
    }

    function showError(message) {
        const resultsBody = document.getElementById('results-body');
        resultsBody.innerHTML = `<tr><td colspan="8" class="shipment-query__error">${message}</td></tr>`;
        
        // 重置分页
        document.getElementById('total-count').textContent = '0';
        document.getElementById('pagination-controls').innerHTML = '';
        window.shipmentQueryState.totalCount = 0;
        window.shipmentQueryState.totalPages = 0;
    }

    // 分页相关函数
    function goToPage(page) {
        window.shipmentQueryState.currentPage = page;
        window.shipmentQueryState.isInitialSearch = false; // 设置为非初始查询
        searchShipmentRecords();
    }

    function goToFirstPage() {
        window.shipmentQueryState.currentPage = 1;
        window.shipmentQueryState.isInitialSearch = false; // 设置为非初始查询
        searchShipmentRecords();
    }

    function goToPrevPage() {
        if (window.shipmentQueryState.currentPage > 1) {
            window.shipmentQueryState.currentPage--;
            window.shipmentQueryState.isInitialSearch = false; // 设置为非初始查询
            searchShipmentRecords();
        }
    }

    function goToNextPage() {
        if (window.shipmentQueryState.currentPage < window.shipmentQueryState.totalPages) {
            window.shipmentQueryState.currentPage++;
            window.shipmentQueryState.isInitialSearch = false; // 设置为非初始查询
            searchShipmentRecords();
        }
    }

    function goToLastPage() {
        window.shipmentQueryState.currentPage = window.shipmentQueryState.totalPages;
        window.shipmentQueryState.isInitialSearch = false; // 设置为非初始查询
        searchShipmentRecords();
    }

    // 添加排序功能
    function sortBy(field) {
        // 更新排序状态
        if (window.shipmentQueryState.sortField === field) {
            // 如果点击的是当前排序字段，切换排序顺序
            window.shipmentQueryState.sortOrder = window.shipmentQueryState.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果是新的排序字段，设置为升序
            window.shipmentQueryState.sortField = field;
            window.shipmentQueryState.sortOrder = 'asc';
        }
        
        // 重置到第一页
        window.shipmentQueryState.currentPage = 1;
        
        // 设置为非初始查询
        window.shipmentQueryState.isInitialSearch = false;
        
        // 更新排序图标并重新加载数据
        updateSortIcons(field);
        searchShipmentRecords();
    }

    // 更新排序图标
    function updateSortIcons(activeField) {
        const headers = document.querySelectorAll('.shipment-query__table th.sortable');
        headers.forEach(header => {
            const icon = header.querySelector('i');
            if (icon) {
                const field = header.getAttribute('onclick').match(/sortBy\('([^']+)'\)/)[1];
                if (field === activeField) {
                    // 更新激活的排序图标
                    icon.className = window.shipmentQueryState.sortOrder === 'asc' 
                        ? 'fas fa-sort-up' 
                        : 'fas fa-sort-down';
                    header.classList.add('active');
                } else {
                    // 重置其他列的图标
                    icon.className = 'fas fa-sort';
                    header.classList.remove('active');
                }
            }
        });
    }

    // 显示导出按钮
    function showExportButton() {
        const exportBtn = document.getElementById('export-btn');
        if (exportBtn) {
            exportBtn.style.display = 'inline-flex';
        }
    }

    // 隐藏导出按钮
    function hideExportButton() {
        const exportBtn = document.getElementById('export-btn');
        if (exportBtn) {
            exportBtn.style.display = 'none';
        }
    }

    // 处理全选/取消全选
    function handleSelectAll(e) {
        const isChecked = e.target.checked;
        window.shipmentQueryState.allSelected = isChecked;
        
        // 更新所有行的复选框状态
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
            
            // 获取记录ID
            const recordId = checkbox.getAttribute('data-record-id');
            
            if (isChecked) {
                window.shipmentQueryState.selectedRecords.add(recordId);
            } else {
                window.shipmentQueryState.selectedRecords.delete(recordId);
            }
        });
        
        // 更新导出按钮文本
        updateExportButtonText();
    }

    // 处理单行选择
    function handleRowSelect(e) {
        const checkbox = e.target;
        const recordId = checkbox.getAttribute('data-record-id');
        
        if (checkbox.checked) {
            window.shipmentQueryState.selectedRecords.add(recordId);
        } else {
            window.shipmentQueryState.selectedRecords.delete(recordId);
            
            // 如果取消选中某一行，则全选也应取消
            document.getElementById('select-all-checkbox').checked = false;
            window.shipmentQueryState.allSelected = false;
        }
        
        // 检查是否所有行都被选中
        const allCheckboxes = document.querySelectorAll('.row-checkbox');
        const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
        
        if (allChecked && allCheckboxes.length > 0) {
            document.getElementById('select-all-checkbox').checked = true;
            window.shipmentQueryState.allSelected = true;
        }
        
        // 更新导出按钮文本
        updateExportButtonText();
    }

    // 更新导出按钮文本
    function updateExportButtonText() {
        const exportBtn = document.getElementById('export-btn');
        if (exportBtn) {
            const selectedCount = window.shipmentQueryState.selectedRecords.size;
            if (selectedCount > 0) {
                exportBtn.innerHTML = `<i class="fas fa-file-export"></i> 导出选中(${selectedCount})`;
            } else {
                exportBtn.innerHTML = `<i class="fas fa-file-export"></i> 导出数据`;
            }
        }
    }

    // 导出数据功能
    function exportData() {
        // 获取当前查询结果
        const { totalCount, selectedRecords } = window.shipmentQueryState;
        
        if (totalCount <= 0) {
            Swal.fire({
                icon: 'warning',
                title: '无数据可导出',
                text: '请先查询数据',
                confirmButtonText: '确定'
            });
            return;
        }
        
        // 显示加载中提示
        Swal.fire({
            title: '正在准备导出...',
            text: '请稍候',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        // 构建导出查询参数
        const { queryType, queryValue, sortField, sortOrder } = window.shipmentQueryState;
        
        const queryParams = new URLSearchParams({
            type: queryType,
            value: queryValue,
            export: 'true'
        });
        
        // 添加排序参数
        if (sortField && sortField.trim() !== '') {
            queryParams.append('sort_field', sortField);
            queryParams.append('sort_order', sortOrder);
        }
        
        // 如果有选中的记录，添加到查询参数
        if (selectedRecords.size > 0) {
            const selectedIds = Array.from(selectedRecords);
            queryParams.append('selected_records', JSON.stringify(selectedIds));
        }
        
        // 获取高级查询面板的显示状态
        const advancedFilterVisible = document.querySelector('.shipment-query__advanced-filter').style.display !== 'none';
        
        // 只有当高级查询面板显示时才获取日期范围
        if (advancedFilterVisible) {
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            
            if (startDate) {
                queryParams.append('startDate', startDate);
            }
            if (endDate) {
                queryParams.append('endDate', endDate);
            }
        }
        
        // 发送导出请求
        fetch(`/api/shipment-query/export?${queryParams.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('导出失败');
                }
                return response.blob();
            })
            .then(blob => {
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                
                // 生成文件名
                const date = new Date();
                const timestamp = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}${String(date.getHours()).padStart(2, '0')}${String(date.getMinutes()).padStart(2, '0')}`;
                
                // 如果有选中记录，在文件名中标注
                const filenameSuffix = selectedRecords.size > 0 ? `_已选${selectedRecords.size}条` : '';
                a.download = `出库记录_${timestamp}${filenameSuffix}.xlsx`;
                
                // 触发下载
                document.body.appendChild(a);
                a.click();
                
                // 清理
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                // 显示成功提示
                Swal.fire({
                    icon: 'success',
                    title: '导出成功',
                    text: selectedRecords.size > 0 ? `已成功导出${selectedRecords.size}条选中数据` : '数据已成功导出',
                    showConfirmButton: false,
                    timer: 2000
                });
            })
            .catch(error => {
                Logger.error('导出错误:', error);
                Swal.fire({
                    icon: 'error',
                    title: '导出失败',
                    text: error.message || '请稍后再试',
                    confirmButtonText: '确定'
                });
            });
    }

    // 将需要的函数挂载到window对象
    window.initShipmentQueryPage = initShipmentQueryPage;
    window.goToPage = goToPage;
    window.goToFirstPage = goToFirstPage;
    window.goToPrevPage = goToPrevPage;
    window.goToNextPage = goToNextPage;
    window.goToLastPage = goToLastPage;
    window.sortBy = sortBy;
    window.updateSortIcons = updateSortIcons;
    window.handleSelectAll = handleSelectAll;
    window.handleRowSelect = handleRowSelect;
})();
