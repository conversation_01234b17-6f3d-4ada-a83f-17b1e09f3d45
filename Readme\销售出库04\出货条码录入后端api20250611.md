# 出货条码录入后端API功能总结

## API概览

出货条码录入系统包含以下后端API接口，实现完整的SN号录入、验证、管理和导出功能：

### 核心功能API
1. `POST /api/shipment-barcode/add-sn` - 添加单个SN号
2. `GET /api/shipment-barcode/get-sn-list` - 获取SN列表（含状态信息）
3. `DELETE /api/shipment-barcode/delete-sn/<id>` - 删除SN号
4. `POST /api/shipment-barcode/check-sn-info` - SN信息检查（版本状态+批次状态）

### 数据管理API
5. `POST /api/shipment-barcode/export-excel` - 导出Excel文件
6. `POST /api/shipment-barcode/import-excel` - 导入Excel文件
7. `POST /api/shipment-barcode/check-duplicate` - 重复性检查

### 集成验证API
8. `POST /api/work-order/validate-sn-customer` - SN客户验证（复用现有API）

---

## 详细API说明

### 1. 添加单个SN号
**接口**: `POST /api/shipment-barcode/add-sn`

#### 功能描述
- 向指定订单添加单个SN号记录
- 自动创建订单（如不存在）
- 检测重复SN号并提供警告

#### 请求参数
```json
{
    "boxNumber": "箱单流水号",
    "shipmentNumber": "出库单号", 
    "customerName": "客户名称",
    "snNumber": "产品SN号"
}
```

#### 成功响应
```json
{
    "success": true,
    "message": "SN号添加成功",
    "data": {
        "recordId": 123,
        "snNumber": "SN12345",
        "createdAt": "2024-01-01 10:30:00"
    }
}
```

#### 重复SN警告响应
```json
{
    "success": true,
    "message": "SN号添加成功", 
    "warning": "SN号 SN12345 已存在于系统中",
    "hasDuplicates": true
}
```

#### 错误提示
- `未登录或登录已过期` - Token验证失败
- `请填写所有必填字段` - 缺少必要参数
- `数据库错误，请联系管理员` - 数据库操作异常
- `服务器错误，请联系管理员` - 系统异常

---

### 2. 获取SN列表
**接口**: `GET /api/shipment-barcode/get-sn-list`

#### 功能描述
- 根据箱单流水号和出库单号获取SN列表
- 包含版本状态和批次状态信息
- 支持实时状态计算

#### 请求参数
```
?boxNumber=箱单流水号&shipmentNumber=出库单号&workOrder=工单号
```

#### 成功响应
```json
{
    "success": true,
    "data": [
        {
            "recordId": 123,
            "snNumber": "SN12345",
            "createdAt": "2024-01-01 10:30:00",
            "versionStatus": "正常",      // 新增：版本状态
            "batchStatus": "正常"        // 新增：批次状态
        }
    ]
}
```

#### 状态说明
- **版本状态**: 
  - `正常` - 固件版本状态为active
  - `异常、需升级` - 固件版本状态非active
  - `未知` - 无法获取版本信息
- **批次状态**:
  - `正常` - 实际工单号与期望工单号匹配
  - `异常` - 工单号不匹配
  - 空白 - 工单号为"无"，跳过验证

#### 错误提示
- `流水和出库单号不能为空` - 缺少必要参数
- `服务器错误，请联系管理员` - 系统异常

---

### 3. 删除SN号
**接口**: `DELETE /api/shipment-barcode/delete-sn/<record_id>`

#### 功能描述
- 删除指定的SN记录
- 验证用户权限

#### 成功响应
```json
{
    "success": true,
    "message": "SN号删除成功"
}
```

#### 错误提示
- `未登录或登录已过期` - Token验证失败
- `未找到该SN记录` - 记录不存在
- `服务器错误，请联系管理员` - 系统异常

---

### 4. SN信息检查（双重验证）
**接口**: `POST /api/shipment-barcode/check-sn-info`

#### 功能描述
- **版本状态检查**: SN号 → 测试记录表 → 工单号 → download_record表 → firmware表 → status字段
- **批次状态检查**: 实际工单号与期望工单号比对
- 提供智能提示和警告

#### 请求参数
```json
{
    "sn": "产品SN号",
    "workOrder": "期望工单号"  // 新增参数，默认"无"
}
```

#### 成功响应
```json
{
    "success": true,
    "sn": "SN12345",
    "work_order": "WO001",
    "version_status": {
        "is_active": false,
        "message": "软件版本异常"  // 仅在异常时存在
    },
    "batch_status": {
        "status": "异常",
        "message": "批次号异常"   // 仅在异常时存在
    }
}
```

#### 前端提示处理
- **版本异常**: 弹出"软件版本异常"提示（5秒自动关闭）
- **批次异常**: 弹出"批次号异常"提示（5秒自动关闭）
- **非阻塞式**: 提示不阻止用户继续操作

#### 错误提示
- `请提供SN号` - 缺少SN参数
- `未找到SN号 [xxx] 对应的工单记录` - SN在测试记录中不存在
- `服务器错误，请联系管理员` - 系统异常

---

### 5. 导出Excel文件
**接口**: `POST /api/shipment-barcode/export-excel`

#### 功能描述
- 导出包含所有SN记录的Excel文件
- 包含版本状态和批次状态列
- 支持完整的产品溯源信息

#### 请求参数
```json
{
    "boxNumber": "箱单流水号",
    "shipmentNumber": "出库单号",
    "customerName": "客户名称",
    "workOrder": "工单号"  // 新增：用于批次状态计算
}
```

#### 导出列信息
| 列名 | 说明 | 数据来源 |
|------|------|----------|
| 序号 | 自动编号 | 自动生成 |
| 产品SN号 | SN号 | SNRecord表 |
| **版本状态** | **版本检查结果** | **实时计算** |
| **批次状态** | **批次验证结果** | **实时计算** |
| 录入时间 | 创建时间 | SNRecord表 |
| 储存状态 | 固定"完成" | 固定值 |
| 箱单流水号 | 流水号 | Order表 |
| 出库单号 | 出库单号 | Order表 |
| 客户名称 | 客户名称 | Order表 |

#### 成功响应
- 返回Excel文件流
- 文件名格式: `出货条码_箱单号_出库单号_日期.xlsx`

#### 错误提示
- `缺少必要参数` - 参数不完整
- `未找到相关订单` - 订单不存在
- `导出失败，请联系管理员` - 系统异常

---

### 6. 导入Excel文件
**接口**: `POST /api/shipment-barcode/import-excel`

#### 功能描述
- 批量导入SN号记录
- 支持Excel文件格式(.xlsx, .xls)
- 自动跳过重复SN号

#### 请求参数
- **Form Data**:
  - `file`: Excel文件
  - `boxNumber`: 箱单流水号
  - `shipmentNumber`: 出库单号
  - `customerName`: 客户名称

#### Excel格式要求
- 必须包含"产品SN号"列
- 支持标准Excel格式

#### 成功响应
```json
{
    "success": true,
    "message": "导入成功",
    "importCount": 150  // 实际导入的记录数
}
```

#### 错误提示
- `未找到上传的文件` - 没有文件上传
- `未选择文件` - 文件为空
- `缺少必要参数` - 表单参数不完整
- `Excel文件格式不正确，请确保包含产品SN号列` - 格式错误
- `未登录或登录已过期` - Token验证失败
- `导入失败，请检查文件格式或联系管理员` - 系统异常

---

### 7. 重复性检查
**接口**: `POST /api/shipment-barcode/check-duplicate`

#### 功能描述
- 检查字段值是否重复
- 目前主要用于SN号重复检查

#### 请求参数
```json
{
    "field": "字段名",  // snNumber, boxNumber, shipmentNumber
    "value": "检查值"
}
```

#### 成功响应
```json
{
    "success": true,
    "exists": false  // true表示已存在，false表示不存在
}
```

#### 检查逻辑
- `snNumber`: 检查SN号是否已存在
- `boxNumber`: 目前返回false（不再检查唯一性）
- `shipmentNumber`: 目前返回false（不再检查唯一性）

#### 错误提示
- `缺少必要参数` - 参数不完整
- `无效的字段名` - 不支持的字段
- `服务器错误，请联系管理员` - 系统异常

---

### 8. SN客户验证（集成API）
**接口**: `POST /api/work-order/validate-sn-customer`

#### 功能描述
- 验证SN号是否属于指定客户
- 支持智能客户检测
- 系统复用现有API，避免重复开发

#### 请求参数
```json
{
    "sn_number": "产品SN号",
    "shipment_customer_name": "出库客户名称"  // 或 "_CHECK_CUSTOMER_" 用于客户检测
}
```

#### 特殊用法：客户检测
- 使用`"_CHECK_CUSTOMER_"`作为客户名称
- 从错误信息中提取实际客户名称
- 自动开启客户检查开关

#### 验证成功响应
```json
{
    "success": true,
    "valid": true,
    "message": "验证通过"
}
```

#### 验证失败响应
```json
{
    "success": true,
    "valid": false,
    "message": "SN [SN12345] 不属于客户 [客户A]，实际属于工单客户 [客户B]"
}
```

#### 前端处理逻辑
- **验证失败**: 弹出警告提示，不阻止录入（仅提示）
- **客户检测**: 自动开启客户检查开关并提示用户
- **API错误**: 记录日志，不阻断操作流程

#### 错误提示
- `SN [xxx] 未在生产记录中找到` - SN在coupler_table和cpu_table中不存在
- `服务器错误` - API调用失败
- `验证出错` - 网络连接异常

---

## 性能优化特性

### 1. 智能查询优化
- **条件化执行**: 批次状态仅在工单号≠"无"时计算
- **API复用**: 客户检测复用现有验证接口
- **状态缓存**: 避免重复的数据库查询

### 2. 前端性能优化
- **批次状态显示**: 空白状态不渲染，减少DOM操作
- **智能跳过**: 开关已开启时跳过客户检测
- **非阻塞提示**: 所有提示不阻断用户操作

### 3. 数据库查询优化
- **核心函数复用**: `get_sn_version_status_core()`统一处理版本和批次状态
- **批量操作**: 列表页面批量计算状态信息
- **索引利用**: 优化查询性能

---

## 错误处理机制

### 1. 用户友好提示
- **明确错误信息**: 所有错误都有具体的中文提示
- **操作指导**: 错误信息包含解决建议
- **非阻塞设计**: 单个功能错误不影响其他功能

### 2. 系统容错
- **优雅降级**: API失败时系统降级为基础功能
- **日志记录**: 详细的错误日志便于问题排查
- **事务保护**: 数据操作使用事务确保一致性

### 3. 网络异常处理
- **超时处理**: 合理的超时设置
- **重试机制**: 关键操作支持自动重试
- **状态保持**: 网络异常不丢失用户输入

---

## 数据流程图

### 完整的SN录入流程
```
用户输入SN → 
├─ checkSnInfo() [前端协调]
│  ├─ checkVersionStatus() → /api/shipment-barcode/check-sn-info
│  │  ├─ 版本状态检查 → 版本异常提示
│  │  └─ 批次状态检查 → 批次异常提示
│  └─ handleCustomerCheckToggle() → /api/work-order/validate-sn-customer
│     ├─ 客户检测 → 自动开启开关
│     └─ 客户验证 → 客户不匹配提示
├─ validateSnAgainstCustomer() [可选] → /api/work-order/validate-sn-customer
│  └─ 客户验证 → 仅提示不阻止
└─ addSNToDatabase() → /api/shipment-barcode/add-sn
   ├─ 添加成功 → 重新加载列表
   ├─ 重复SN → 成功提示+警告
   └─ 添加失败 → 错误提示
```

### 状态信息获取流程
```
页面加载/刷新 →
├─ loadSNList() → /api/shipment-barcode/get-sn-list?workOrder=xxx
│  ├─ 批量获取SN记录
│  ├─ 每个SN调用 get_sn_version_status_core()
│  │  ├─ 版本状态: SN → 工单号 → download_record → firmware → status
│  │  └─ 批次状态: 实际工单号 vs 输入工单号
│  └─ 返回包含双重状态的记录列表
└─ updateSNList() → 渲染表格（含状态样式）
```

---

## 总结

出货条码录入系统的后端API设计完整、功能强大，具有以下特点：

✅ **功能完整**: 涵盖SN录入、验证、管理、导出的全流程  
✅ **双重验证**: 版本状态 + 批次状态，确保产品质量  
✅ **性能优化**: 智能查询、条件执行、API复用  
✅ **用户友好**: 详细提示、非阻塞设计、优雅降级  
✅ **系统集成**: 复用现有API，避免重复开发  
✅ **错误处理**: 完善的异常处理和用户提示机制  

该API体系为出货条码录入提供了可靠、高效、易用的后端支持，能够满足复杂的生产环境需求。 



修改完的总结
-------
我来总结出货条码录入页面的所有后端API功能，并追加到相应文档中。首先让我检查是否已存在该文档。
Ran tool
Ran tool
Read file: routes/shipment_barcode.py
现在我需要查看其他相关的API端点，特别是客户验证相关的。
Ran tool
现在让我创建一个全面的出货条码录入后端API文档。
完美！我已经成功创建了`出货条码录入后端api.md`文档，全面总结了出货条码录入页面的所有后端查询功能。

## 📋 文档内容总结

### 🎯 涵盖的8个主要API接口：

1. **核心功能API (4个)**：
   - `POST /api/shipment-barcode/add-sn` - 添加单个SN号
   - `GET /api/shipment-barcode/get-sn-list` - 获取SN列表（含双重状态）
   - `DELETE /api/shipment-barcode/delete-sn/<id>` - 删除SN号
   - `POST /api/shipment-barcode/check-sn-info` - SN信息检查（版本+批次）

2. **数据管理API (3个)**：
   - `POST /api/shipment-barcode/export-excel` - 导出Excel
   - `POST /api/shipment-barcode/import-excel` - 导入Excel
   - `POST /api/shipment-barcode/check-duplicate` - 重复性检查

3. **集成验证API (1个)**：
   - `POST /api/work-order/validate-sn-customer` - SN客户验证（复用现有API）

### 📊 每个API的详细信息包含：

- **功能描述** - 明确说明API的作用和用途
- **请求参数** - 完整的参数格式和示例
- **成功响应** - 标准返回格式和数据结构
- **错误提示** - 所有可能的错误信息和处理方式
- **特殊逻辑** - 性能优化、智能处理等特性

### 🚀 文档特色功能：

1. **双重状态检查详述** - 版本状态和批次状态的完整说明
2. **性能优化特性** - 条件化执行、API复用、智能跳过等
3. **错误处理机制** - 用户友好提示、系统容错、网络异常处理
4. **数据流程图** - 完整的SN录入流程和状态获取流程
5. **实际提示内容** - 所有用户可能看到的具体提示信息

### 💡 技术亮点总结：

- **智能化设计** - 条件化执行、自动开关管理、客户检测
- **性能优化** - API复用、状态缓存、批量操作
- **用户体验** - 非阻塞提示、详细错误信息、优雅降级
- **系统集成** - 复用现有API、避免重复开发

这份文档为开发团队提供了完整的后端API参考，包含了所有功能细节、错误处理和性能优化特性，是出货条码录入系统后端开发和维护的重要技术文档。
-----