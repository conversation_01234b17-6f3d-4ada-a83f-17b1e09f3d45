<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>生产管理系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- Tailwind CSS for CPU Controller Vue -->
    <!-- 下载地址: https://cdn.tailwindcss.com/3.4.16 -->
    <script src="/static/lib/tailwindcss/tailwind.min.js"></script>
    <!-- Lucide Icons for CPU Controller Vue -->
    <!-- 下载地址: https://cdn.jsdelivr.net/npm/lucide@0.469.0/dist/umd/lucide.js -->
    <script src="/static/lib/lucide/lucide.min.js"></script>
    <!-- 全局日志记录工具 -->
    <script src="/static/js/utils/Logger.js"></script>
    <link rel="stylesheet" href="/static/style.css">
    <!-- 其他现有的标签 -->
    <link rel="stylesheet" href="/static/page_js_css/sharedStyles.css">
    <link rel="stylesheet" href="/static/page_js_css/IOModule.css">
<link rel="stylesheet" href="/static/page_js_css/BoardTest.css">
    <!-- 添加新的CSS引用 -->
    <link rel="stylesheet" href="/static/page_js_css/barcode-comparison.css">
    <!-- 先加载基础样式 -->
    <link rel="stylesheet" href="/static/page_js_css/BatchQuery.css">
    <!-- 再加载故障品查询的特定样式 -->
    <link rel="stylesheet" href="/static/page_js_css/FaultQuery.css">
    <!-- 在其他CSS引用之前添加 Sweetalert2 CSS -->
    <link rel="stylesheet" href="/static/lib/sweetalert2/sweetalert2.min.css">
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">
    <link rel="stylesheet" href="/static/page_js_css/ShipmentBarcode.css">
    <link rel="stylesheet" href="/static/page_js_css/ShipmentQuery.css">
    <link rel="stylesheet" href="/static/page_js_css/SNPrintRecord.css">
    <link rel="stylesheet" href="/static/page_js_css/MarkingRecord.css">
    <!-- CPU Controller Vue Modern UI -->
    <link rel="stylesheet" href="/static/page_js_css/CPUControllerVue.css">
</head>
<body>
    <div class="app-wrapper">
        <div class="sidebar-container">
            <div class="logo">
                <img src="/static/images/kmlclogo.png" alt="logo">
                <span id="current-user">Vue Admin</span>
            </div>
            <div class="menu">
                <!-- 首页 -->
                <div class="menu-item active" data-page="dashboard">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </div>

                <!-- 基础信息 -->
                <div class="menu-item" data-page="basic-info">
                    <i class="fas fa-database"></i>
                    <span>基础信息</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="submenu">
                    <div class="menu-item" data-page="product-management">
                        <i class="fas fa-box"></i>
                        <span>产品管理</span>
                    </div>
                    <div class="menu-item" data-page="order-management">
                        <i class="fas fa-clipboard-list"></i>
                        <span>工单管理</span>
                    </div>
                </div>

                <!-- 固件管理 (一级菜单) -->
                <div class="menu-item" data-page="firmware-management-main">
                    <i class="fas fa-hdd"></i>
                    <span>固件管理</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="submenu">
                    <div class="menu-item" data-page="all-firmware">
                        <i class="fas fa-list"></i>
                        <span>所有固件</span>
                    </div>
                    <div class="menu-item" data-page="pending-firmware">
                        <i class="fas fa-hourglass-half"></i>
                        <span>待审核固件</span>
                    </div>
                    <div class="menu-item" data-page="obsolete-firmware">
                        <i class="fas fa-ban"></i>
                        <span>作废版本</span>
                    </div>
                    <div class="menu-item" data-page="version-comparison">
                        <i class="fas fa-code-branch"></i>
                        <span>版本比对</span>
                    </div>
                    <div class="menu-item" data-page="usage-record">
                        <i class="fas fa-history"></i>
                        <span>使用记录</span>
                    </div>
                </div>

                <!-- 激光打标 -->
                <div class="menu-item" data-page="laser-marking">
                    <i class="fas fa-cogs"></i>
                    <span>激光打标</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="submenu">
                    <div class="menu-item" data-page="sn-print-record">
                        <i class="fas fa-print"></i>
                        <span>SN打印</span>
                    </div>
                    <div class="menu-item" data-page="marking-record">
                        <i class="fas fa-tag"></i>
                        <span>打印记录</span>
                    </div>
                </div>

                <!-- 扫码绑定 -->
                <div class="menu-item" data-page="barcode-binding">
                    <i class="fas fa-link"></i>
                    <span>扫码绑定</span>
                </div>
                
                <!-- 单板测试 -->
                <div class="menu-item" data-page="board-test">
                    <i class="fas fa-microchip"></i>
                    <span>单板测试</span>
                </div>

                <!-- 成品测试 -->
                <div class="menu-item" data-page="product-test">
                    <i class="fas fa-vial"></i>
                    <span>成品测试</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="submenu">
                    <div class="menu-item" data-page="cpu-controller" style="display: none;">
                        <i class="fas fa-microchip"></i>
                        <span>CPU_控制器</span>
                    </div>
                    <div class="menu-item" data-page="cpu-controller-vue">
                        <i class="fas fa-microchip"></i>
                        <span>CPU_控制器V</span>
                    </div>
                    <div class="menu-item" data-page="io-module" style="display: none;">
                        <i class="fas fa-plug"></i>
                        <span>IO模块</span>
                    </div>
                    <div class="menu-item" data-page="io-module-vue">
                        <i class="fas fa-plug"></i>
                        <span>IO模块V</span>
                    </div>
                    <div class="menu-item" data-page="coupler" style="display: none;">
                        <i class="fas fa-link"></i>
                        <span>耦合器</span>
                    </div>
                    <div class="menu-item" data-page="coupler-vue">
                        <i class="fas fa-link"></i>
                        <span>耦合器V</span>
                    </div>
                </div>

                <!-- 故障品录入 -->
                <div class="menu-item" data-page="fault-entry">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>故障品录入</span>
                </div>

                <!-- 条码比对 -->
                <div class="menu-item" data-page="barcode-comparison">
                    <i class="fas fa-barcode"></i>
                    <span>条码比对</span>
                </div>

                <!-- 故障码 -->
                <div class="menu-item" data-page="fault-code">
                    <i class="fas fa-bug"></i>
                    <span>故障码查询</span>
                </div>

                <!-- 文档中心 -->
                <div class="menu-item" data-page="document-center">
                    <i class="fas fa-file-alt"></i>
                    <span>文档中心</span>
                </div>

                <!-- 质检记录 -->
                <div class="menu-item" data-page="quality-inspection">
                    <i class="fas fa-clipboard-check"></i>
                    <span>质检记录</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="submenu">
                    <div class="menu-item" data-page="self-inspection">
                        <i class="fas fa-tasks"></i>
                        <span>检验过程</span>
                    </div>
                    <div class="menu-item" data-page="inspection-record">
                        <i class="fas fa-clipboard-list"></i>
                        <span>检验记录</span>
                    </div>
                </div>

                <!-- 销售记录 -->
                <div class="menu-item" data-page="sales-record">
                    <i class="fas fa-shopping-cart"></i>
                    <span>销售记录</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="submenu">
                    <div class="menu-item" data-page="shipment-barcode">
                        <i class="fas fa-barcode"></i>
                        <span>出货条码录入</span>
                    </div>
                    <div class="menu-item" data-page="shipment-query">
                        <i class="fas fa-search"></i>
                        <span>出货记录查询</span>
                    </div>
                </div>

                <!-- 数据统计 -->
                <div class="menu-item" data-page="data-statistics">
                    <i class="fas fa-chart-bar"></i>
                    <span>数据统计</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="submenu">
                    <div class="menu-item" data-page="batch-query">
                        <i class="fas fa-search"></i>
                        <span>产品批次查询</span>
                    </div>
                    <div class="menu-item" data-page="fault-query">
                        <i class="fas fa-bug"></i>
                        <span>故障品查询</span>
                    </div>
                    <div class="menu-item" data-page="product-test-query">
                        <i class="fas fa-clipboard-check"></i>
                        <span>成品测试查询</span>
                    </div>
                    <div class="menu-item" data-page="after-sales-query">
                        <i class="fas fa-headset"></i>
                        <span>售后查询</span>
                    </div>
                </div>

                <!-- 关于 -->
                <div class="menu-item" data-page="about">
                    <i class="fas fa-info-circle"></i>
                    <span>关于</span>
                </div>
            </div>
        </div>
        <div class="main-container">
            <div class="fixed-header">
                <header class="navbar">
                    <div class="navbar-left">
                        <div class="hamburger">
                            <i class="fas fa-bars"></i>
                        </div>
                        <div class="breadcrumb">
                            <span>首页</span>
                        </div>
                    </div>
                    <div class="navbar-right">
                        <div class="navbar-item">
                            <i class="fas fa-expand" id="fullscreen-toggle"></i>
                        </div>
                        <div class="navbar-item">
                            <i class="fas fa-th-large" id="size-select"></i>
                        </div>
                        <div class="navbar-item">
                            <select id="language-select">
                                <option value="zh">中文</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                        <div class="navbar-item user-dropdown">
                            <img src="/static/images/avatar.gif" alt="User Avatar" class="avatar">
                            <div class="dropdown-content">
                                <a href="#" id="profile-link">个人信息</a>
                                <a href="#logout">退出登录</a>
                            </div>
                        </div>
                    </div>
                </header>
                <div class="tags-view-container">
                    <div class="tags-view-wrapper">
                        <!-- 标签将由 JavaScript 动态添加 -->
                    </div>
                </div>
                <div class="breadcrumb-container">
                    <div class="breadcrumb">
                        <!-- 面包屑导航内容 -->
                    </div>
                </div>
            </div>
            <main class="app-main">
                <div id="content">
                    <!-- Dashboard content will be dynamically loaded -->
                </div>
                <div id="io-module-content"></div>
                <div id="board-test-content"></div>
            </main>
        </div>
    </div>

    <!-- 在 <body> 标签的末尾，在 </div> 和 <script> 标签之间添加以下代码 -->
    <div class="context-menu" id="tagContextMenu">
        <div class="context-menu-item" data-action="refresh">刷新</div>
        <div class="context-menu-item" data-action="close">关闭</div>
        <div class="context-menu-item" data-action="closeOthers">关闭其他</div>
        <div class="context-menu-item" data-action="closeAll">关闭所有</div>
    </div>

    <script src="/static/script.js"></script>
    <script src="/static/page_js_css/sharedComponents.js"></script>
    <script src="/static/page_js_css/IOModule.js"></script>
    <script src="/static/page_js_css/BoardTest.js"></script>
    <!-- 在底部添加新的JS引用 -->
    <script src="/static/page_js_css/barcode-comparison.js"></script>
    <script src="/static/page_js_css/BarcodeBinding.js"></script>
    <script src="/static/page_js_css/ShipmentBarcode.js"></script>
    <script src="/static/page_js_css/ScanSN.js"></script>
    <script src="/static/page_js_css/SNPrintRecord.js"></script>
    <script src="/static/page_js_css/MarkingRecord.js"></script>

    <!-- 在 body 标签结束前添加模态框用户修改密码 -->
    <div id="profile-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>修改密码</h2>
            <form id="profile-form">
                <div class="form-group">
                    <label for="old-password">原密码</label>
                    <input type="password" id="old-password" name="oldPassword" placeholder="输入原密码" required>
                </div>
                <div class="form-group">
                    <label for="new-password">新密码</label>
                    <input type="password" id="new-password" name="password" placeholder="输入新密码" required>
                </div>
                <div class="form-group">
                    <label for="confirm-password">确认新密码</label>
                    <input type="password" id="confirm-password" placeholder="再次输入新密码" required>
                </div>
                <div class="form-buttons">
                    <button type="submit">保存</button>
                    <button type="button" class="cancel">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 在其他脚本引用之前添加 -->
    <script src="/static/page_js_css/sharedPagination.js"></script>
    <script src="/static/page_js_css/Dashboard.js"></script>

    <!-- 在其他CSS引用后添加 -->
    <script src="/static/lib/echarts/echarts-5.5.0.min.js"></script>

    <!-- 在其他脚本引用之前添加 Sweetalert2 -->
    <script src="/static/lib/sweetalert2/sweetalert2.min.js"></script>
    <script src="/static/js/utils/sweetalert.js"></script>

    <!-- 文件完整性验证工具 -->
    <script src="/static/js/file-hash-verifier.js"></script>

    <script src="/static/lib/html2canvas.min.js"></script>

</body>
</html>
