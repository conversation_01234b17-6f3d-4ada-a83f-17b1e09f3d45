#!/bin/bash

# KMLC PLC 数据库自动备份脚本
# 用途：每日自动备份数据库，并管理备份文件
# 版本：1.0.0

# 加载配置
DB_NAME="kmlc_plc"
DB_USER="root"
DB_PASSWORD="kmlc@3302133"
DB_HOST="*************"
DB_PORT="3309"

# 备份配置
BACKUP_ROOT="/var/backups/kmlc_plc/database"  # 独立的备份根目录
DAILY_BACKUP_DIR="${BACKUP_ROOT}/daily"       # 每日备份目录
WEEKLY_BACKUP_DIR="${BACKUP_ROOT}/weekly"     # 每周备份目录
MONTHLY_BACKUP_DIR="${BACKUP_ROOT}/monthly"   # 每月备份目录

# 保留时间配置
DAILY_RETAIN_DAYS=7      # 保留7天的每日备份
WEEKLY_RETAIN_WEEKS=4    # 保留4周的每周备份
MONTHLY_RETAIN_MONTHS=6  # 保留6个月的每月备份

# 日志配置
LOG_DIR="/var/log/kmlc_plc/backup"
LOG_FILE="${LOG_DIR}/backup_$(date +%Y%m).log"

# 创建必要的目录
create_directories() {
    local dirs=("$DAILY_BACKUP_DIR" "$WEEKLY_BACKUP_DIR" "$MONTHLY_BACKUP_DIR" "$LOG_DIR")
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            chown -R www-data:www-data "$dir"
            chmod 755 "$dir"
        fi
    done
}

# 日志函数
log_message() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message" | tee -a "$LOG_FILE"
}

# 检查备份目录大小
check_backup_size() {
    local total_size=$(du -sh "$BACKUP_ROOT" 2>/dev/null | cut -f1)
    log_message "当前备份总大小: $total_size"
}

# 执行备份
do_backup() {
    local backup_type=$1
    local backup_dir=$2
    local filename="kmlc_plc_${backup_type}_$(date +%Y%m%d_%H%M%S).sql"
    local backup_path="${backup_dir}/${filename}"
    
    log_message "开始 $backup_type 备份..."
    
    # 创建备份
    if mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
        --single-transaction --quick --lock-tables=false \
        --routines --triggers --events \
        "$DB_NAME" > "$backup_path"; then
        
        # 压缩备份文件
        gzip "$backup_path"
        backup_path="${backup_path}.gz"
        
        # 设置权限
        chown www-data:www-data "$backup_path"
        chmod 644 "$backup_path"
        
        # 计算文件大小和MD5
        local size=$(du -h "$backup_path" | cut -f1)
        local md5=$(md5sum "$backup_path" | cut -d' ' -f1)
        
        log_message "$backup_type 备份完成: $backup_path (大小: $size, MD5: $md5)"
        return 0
    else
        log_message "错误: $backup_type 备份失败"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    local type=$1
    local dir=$2
    local days=$3
    
    log_message "清理 $type 旧备份文件 ($days 天前)..."
    find "$dir" -name "*.sql.gz" -type f -mtime +"$days" -delete
    log_message "$type 清理完成"
}

# 验证备份
verify_backup() {
    local backup_file=$1
    local temp_dir="/tmp/db_verify"
    
    log_message "验证最新备份: $backup_file"
    
    # 创建临时目录
    mkdir -p "$temp_dir"
    
    # 解压并验证备份文件
    gunzip -c "$backup_file" | mysql -h "$DB_HOST" -P "$DB_PORT" \
        -u "$DB_USER" -p"$DB_PASSWORD" --force "$DB_NAME" > "$temp_dir/verify.log" 2>&1
    
    if [ $? -eq 0 ]; then
        log_message "备份验证成功"
        rm -rf "$temp_dir"
        return 0
    else
        log_message "错误: 备份验证失败，详情请查看 $temp_dir/verify.log"
        return 1
    fi
}

# 主备份流程
main() {
    local start_time=$(date +%s)
    
    # 创建必要的目录
    create_directories
    
    # 记录开始时间
    log_message "开始数据库备份流程..."
    
    # 检查磁盘空间
    local free_space=$(df -h "$BACKUP_ROOT" | awk 'NR==2 {print $4}')
    log_message "可用磁盘空间: $free_space"
    
    # 执行每日备份
    do_backup "daily" "$DAILY_BACKUP_DIR"
    
    # 每周日执行周备份
    if [ "$(date +%u)" = "7" ]; then
        do_backup "weekly" "$WEEKLY_BACKUP_DIR"
    fi
    
    # 每月1号执行月备份
    if [ "$(date +%d)" = "01" ]; then
        do_backup "monthly" "$MONTHLY_BACKUP_DIR"
    fi
    
    # 清理旧备份
    cleanup_old_backups "daily" "$DAILY_BACKUP_DIR" "$DAILY_RETAIN_DAYS"
    cleanup_old_backups "weekly" "$WEEKLY_BACKUP_DIR" "$((WEEKLY_RETAIN_WEEKS * 7))"
    cleanup_old_backups "monthly" "$MONTHLY_BACKUP_DIR" "$((MONTHLY_RETAIN_MONTHS * 30))"
    
    # 验证最新备份
    local latest_backup=$(ls -t "${DAILY_BACKUP_DIR}"/*.sql.gz | head -n1)
    if [ -n "$latest_backup" ]; then
        verify_backup "$latest_backup"
    fi
    
    # 检查备份大小
    check_backup_size
    
    # 计算总耗时
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    log_message "备份流程完成，总耗时: ${duration} 秒"
}

# 错误处理
set -e
trap 'log_message "错误: 备份脚本在第 $LINENO 行失败"' ERR

# 运行主程序
main

# 退出前清理日志
find "$LOG_DIR" -name "*.log" -type f -mtime +90 -delete 