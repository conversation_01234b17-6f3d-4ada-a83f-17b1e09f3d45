# IO模块测试系统功能架构文档

## 系统概述

IO模块测试系统是一个基于Web的产品测试管理平台，主要用于IO模块的质量检测和数据记录。系统采用前后端分离架构，通过严格的验证流程确保测试数据的准确性和完整性。

## 核心功能架构

### 1. 工单管理子系统

#### 1.1 工单验证机制
- **输入防抖处理**：500ms延迟避免频繁API调用
- **工单状态检查**：验证工单是否完成测试前阶段
- **数据自动填充**：根据工单号自动填充产品信息
- **PCBA检查配置**：根据工单设置PCBA绑定检查标志

#### 1.2 工单验证流程
```
用户输入工单号 → 防抖处理 → API查询 → 状态验证 → 自动填充字段
                                    ↓
                            test_stage_completed检查
                                    ↓
                            设置requiresPcbaCheck标志
```

#### 1.3 验证规则
- 工单号必须存在于系统中
- 工单必须完成测试前阶段外观检验
- 验证失败时清空相关字段并提示用户

### 2. 产品检测子系统

#### 2.1 PCBA绑定检测
- **条件检测**：根据工单配置决定是否需要PCBA检查
- **SN号验证**：通过API检查产品SN号的PCBA绑定状态
- **失败处理**：未绑定时清空输入并重新获取焦点

#### 2.2 检测状态判断逻辑
```
SN号输入失焦 → 检查requiresPcbaCheck标志
                        ↓
                需要检查？ → API调用检查绑定状态
                        ↓
                已绑定？ → 继续版本信息获取
                        ↓
                未绑定 → 警告提示并清空输入
```

#### 2.3 产品状态支持
- **新品(new)**：全新产品测试
- **维修(used)**：维修后产品测试
- **返工(refurbished)**：返工产品测试

### 3. 版本管理子系统

#### 3.1 版本信息获取
- **自动获取**：根据SN号和产品状态自动获取版本信息
- **手动输入**：用户手动输入IO软件版本和构建日期
- **状态联动**：产品状态变化时清空并重新获取版本信息

#### 3.2 版本比较验证
- **严格匹配**：自动获取版本与手动输入版本必须完全一致
- **双重验证**：软件版本和构建日期都需要验证
- **错误处理**：不一致时阻止提交并定位到错误字段

#### 3.3 版本管理流程
```
产品SN号确认 → 获取产品状态 → API获取版本信息 → 自动填充
                                        ↓
用户手动输入版本信息 → 提交时版本一致性验证 → 通过/拒绝
```

### 4. 测试提交子系统

#### 4.1 数据验证层次
1. **必填字段验证**：10个核心字段完整性检查
2. **SN号长度验证**：与预设字节数精确匹配
3. **版本一致性验证**：自动获取与手动输入对比
4. **业务逻辑验证**：产品状态、测试结果等业务规则

#### 4.2 数据转换处理
- **测试结果映射**：通过/不通过 → 1/2数字格式
- **产品状态映射**：new/used/refurbished → 1/2/3数字格式
- **维修返工计数**：根据产品状态自动设置计数器

#### 4.3 提交后状态管理
- **字段保持**：保留基础信息字段值
- **选择性清空**：清空产品SN号等变化字段
- **UI状态控制**：按钮禁用、焦点设置、样式更新

## 数据流架构

### 输入数据流
```
用户输入 → 前端验证 → 防抖处理 → API调用 → 后端验证 → 数据库操作
```

### 状态管理流
```
全局状态(ioModuleState) → 组件状态 → UI状态 → 用户反馈
```

### 错误处理流
```
异常捕获 → 错误分类 → 用户提示 → 状态恢复 → 焦点管理
```

## API接口架构

### 核心API端点
- **工单查询**：`/api/work-order/by-number`
- **PCBA检查**：`/api/io-module/check-sn`
- **版本获取**：`/api/io-module/get-version-info`
- **测试提交**：`/api/io-module/submit-test`
- **用户信息**：`/api/io-module/get-current-user`

### API调用模式
- **同步调用**：用户信息获取
- **异步调用**：工单查询、PCBA检查、版本获取
- **防抖调用**：工单号输入处理

## 用户体验设计

### 交互优化
- **防抖输入**：减少不必要的API调用
- **自动填充**：减少用户重复输入
- **智能焦点**：错误时自动定位到问题字段
- **状态保持**：提交后保留常用字段值

### 视觉反馈
- **输入状态**：绿色背景标识已填充字段
- **错误提示**：SweetAlert弹窗提示错误信息
- **加载状态**：API调用时的加载反馈
- **成功确认**：操作成功的明确提示

### 错误处理
- **分层验证**：前端验证 + 后端验证
- **友好提示**：中文错误信息，明确指出问题
- **快速恢复**：错误后快速回到正常操作状态

## 技术架构特点

### 前端技术栈
- **原生JavaScript**：核心逻辑实现
- **防抖机制**：优化用户输入体验
- **事件驱动**：基于DOM事件的交互模式
- **状态管理**：全局状态对象管理

### 后端集成
- **RESTful API**：标准HTTP接口
- **JSON数据格式**：统一数据交换格式
- **错误码标准化**：统一的错误处理机制

### 数据安全
- **输入验证**：前后端双重验证
- **数据编码**：URL编码防止注入攻击
- **错误隔离**：异常不影响系统稳定性

## 业务流程总览

```
工单输入 → 工单验证 → 产品信息自动填充
    ↓
SN号输入 → PCBA检查 → 版本信息获取
    ↓
版本验证 → 测试项目选择 → 数据完整性检查
    ↓
提交处理 → 数据转换 → 后端存储
    ↓
状态重置 → 下一轮测试准备
```

## 系统优势

1. **数据准确性**：多层验证确保数据质量
2. **操作效率**：自动化填充减少人工输入
3. **用户友好**：直观的错误提示和状态反馈
4. **扩展性强**：模块化设计便于功能扩展
5. **稳定可靠**：完善的错误处理和状态管理

## 维护要点

- **API接口稳定性**：确保后端接口的向后兼容
- **数据格式一致性**：前后端数据格式保持同步
- **错误处理完整性**：覆盖所有可能的异常情况
- **用户体验持续优化**：根据使用反馈改进交互设计
