# -*- coding: utf-8 -*-
import sys
import os
import json
import time
import socket
import requests
from requests.auth import HTTPBasicAuth

class PingThread:
    def __init__(self, ip_address):
        self.ip_address = ip_address
        self.is_running = True

    def run(self):
        success_count = 0
        start_time = time.time()
        while self.is_running:
            success = self.tcp_ping(self.ip_address, 8090, timeout=2)
            if success:
                success_count += 1
                if success_count >= 10 and (time.time() - start_time) >= 10:
                    break
            else:
                success_count = 0
                start_time = time.time()
            time.sleep(1)

    def tcp_ping(self, host, port, timeout=2):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except socket.error:
            return False

    def stop(self):
        self.is_running = False

class CPUInfoManager:
    def __init__(self):
        self.ip_address = "*************"
        self.username = 'admin'
        self.password = 'admin'

    def get_info(self, url, timeout=2):
        try:
            response = requests.get(url, auth=(self.username, self.password), timeout=timeout)
            response.raise_for_status()
            data = response.json()
            if data.get("error") is False:
                inner_data = next(iter(data["data"].values()))
                return inner_data
            else:
                print("响应包含错误信息")
        except requests.Timeout:
            raise TimeoutError("请求超时")
        except requests.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err}")
        except Exception as err:
            print(f"Other error occurred: {err}")
        return None

    def start_query(self):
        url_info = {
            'eth0': f"http://{self.ip_address}:8090/cgi-bin/netinfo?key=eth0",
            'eth1': f"http://{self.ip_address}:8090/cgi-bin/netinfo?key=eth1",
            'eth2': f"http://{self.ip_address}:8090/cgi-bin/netinfo?key=eth2",
            'device': f"http://{self.ip_address}:8090/cgi-bin/devinfo?key=device",
        }

        netinfo_data = {}

        print("正在查询设备信息...")

        try:
            for key in url_info:
                data = self.get_info(url_info[key])
                if data is not None:
                    if "netinfo" in url_info[key]:
                        print(f"\n{key} 信息：")
                        for sub_key, sub_value in data.items():
                            print(f"{sub_key}: {sub_value}")
                        netinfo_data[key] = data
                    elif key == 'device':
                        print("\n设备信息：")
                        for sub_key, sub_value in data.items():
                            if sub_key in ['serialnumber', 'busversion', 'softwareversion', 'higspeedIOversion', 'builddate', 'odminfo', 'devicename']:
                                print(f"{sub_key}: {sub_value}\n")
                                if sub_key == 'busversion' and sub_value == 'V0.0.0.0':
                                    print("错误: 背板异常，重新烧录背板程序")
                                if sub_key == 'serialnumber' and sub_value == '-':
                                    print("错误: 核对Mac地址是否正确烧录")
                                if sub_key == 'softwareversion':
                                    expected_version = "预期版本"
                                    if expected_version and sub_value != expected_version:
                                        print(f"错误: 软件版本错误\n预期: {expected_version}\n实际: {sub_value}")
                                if sub_key == 'builddate':
                                    expected_date = "预期日期"
                                    if expected_date and sub_value != expected_date:
                                        print(f"错误: 构建日期错误\n预期: {expected_date}\n实际: {sub_value}")
                                if sub_key == 'devicename':
                                    expected_odm = "预期ODM信息"
                                    if expected_odm and sub_value != expected_odm:
                                        print(f"错误: 检查ODM信息\n预期: {expected_odm}\n实际: {sub_value}")
                            else:
                                print(f"{sub_key}: {sub_value}")
        except TimeoutError:
            print("错误: 请求超时，请检查网络连接")
            return

        for key, data in netinfo_data.items():
            if data:
                last_key = list(data)[-1]
                hex_value = data[last_key].split(":")[3] if ":" in data[last_key] else "N/A"
                print(f"\n{last_key}: {data[last_key]} 倒数第三个数：{hex_value}")

        if netinfo_data:
            print("网络正常")
        else:
            print("网络异常，请在此检查该网口")

        # 获取内存信息
        self.get_memory_info()

    def reset_device(self):
        reset_url = f"http://{self.ip_address}:8090/cgi-bin/reset?key=reset"

        try:
            reset_response = requests.get(reset_url, auth=(self.username, self.password), timeout=5)
            reset_response.raise_for_status()
            print("重置设备成功")
        except requests.Timeout:
            print("错误: 重置设备请求超时，请检查网络连接")
        except requests.HTTPError as http_err:
            print(f"执行恢复出厂设置操作时发生HTTP错误: {http_err}")
        except Exception as err:
            print(f"执行恢复出厂设置操作时发生其他错误: {err}")

    def reboot_device(self):
        reboot_url = f"http://{self.ip_address}:8090/cgi-bin/reboot?key=reboot"

        try:
            reboot_response = requests.get(reboot_url, auth=(self.username, self.password), timeout=5)
            reboot_response.raise_for_status()
            print("设备已重新启动")
        except requests.Timeout:
            print("错误: 重新启动设备请求超时，请检查网络连接")
        except requests.HTTPError as http_err:
            print(f"执行重新启动操作时发生HTTP错误: {http_err}")
        except Exception as err:
            print(f"执行重新启动操作时发生其他错误: {err}")

    def write_m_zone(self, range_value, value):
        url = f"http://{self.ip_address}:8090/cgi-bin/memory"
        auth_info = HTTPBasicAuth(self.username, self.password)

        try:
            range_value = int(range_value)
        except ValueError:
            print("错误: 请输入有效的整数作为M区范围值")
            return

        try:
            value = int(value)
        except ValueError:
            print("错误: 请输入有效的整数作为M区value值")
            return

        print(f"开始写入M区，地址范围：0 - {range_value}, value: {value}")

        for address in range(range_value):
            payload = {
                "address": address,
                "value": value,
                "addressType": 2,
                "startIndex": {}
            }

            try:
                response = requests.post(url, json=payload, auth=auth_info, params={'key': 'memory'})
                if response.status_code == 200:
                    json_data = response.json()
                    print(f"地址 {address} 写入成功")
                else:
                    print(f"请求失败, 状态码: {response.status_code}, 地址: {address}")
            except Exception as e:
                print(f"地址 {address} 写入时出错: {e}")

        print("所有地址写入完成。")

    def get_memory_info(self):
        url = f"http://{self.ip_address}:8090/cgi-bin/memory"
        params = {
            'key': 'memory',
            'type': '2',
            'startAddress': '0',
            'count': '256',
            'startIndex': '0',
            'allcount': '256'
        }
        auth_info = HTTPBasicAuth(self.username, self.password)

        try:
            response = requests.get(url, params=params, auth=auth_info, cookies=None, headers={'Cache-Control': 'no-cache'})
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    values = json_data['data']['memory'][0]['value'][:19]
                    print("\n内存信息:")
                    print(str(values))

                    if any(value != 0 for value in values):
                        print("提示: 清除M区数据")

                    return values  # 返回内存数据

                except Exception as e:
                    print(f"解析JSON数据时出错: {e}")
            else:
                print(f"请求失败,状态码: {response.status_code}")
        except Exception as e:
            print(f"获取内存信息时出错: {e}")
        return []  # 返回空列表以防止错误

    def load_program(self):
        print("开始加载程序...")

        file_path = "path_to_your_file.zip"  # 替换为实际文件路径
        if not file_path:
            print("未选择文件，操作取消。")
            return

        if self.upload_file(file_path):
            print("文件上传成功")
            file_name = os.path.basename(file_path)
            if self.update_app(file_name):
                print("更新请求已成功发送和执行")
                self.start_ping_process()
            else:
                print("更新失败，请检查设备日志以获取更多信息")
                self.start_ping_process()
        else:
            print("文件上传失败，请检查错误信息")

    def start_ping_process(self):
        print("开始检测连接...")
        self.ping_thread = PingThread(self.ip_address)
        self.ping_thread.run()

    def upload_file(self, file_path):
        try:
            upload_url = f"http://{self.ip_address}:8090/cgi-bin/upload"
            file_name = os.path.basename(file_path)
            boundary = '----WebKitFormBoundary' + ''.join(['1' for _ in range(16)])

            headers = {
                'Content-Type': f'multipart/form-data; boundary={boundary}',
            }

            with open(file_path, 'rb') as file:
                form = (
                    f'--{boundary}\r\n'
                    f'Content-Disposition: form-data; name="file"; filename="{file_name}"\r\n'
                    f'Content-Type: application/x-zip-compressed\r\n\r\n'
                )
                form_end = f'\r\n--{boundary}--\r\n'

                body = form.encode('utf-8') + file.read() + form_end.encode('utf-8')

            response = requests.post(upload_url, data=body, headers=headers, auth=(self.username, self.password))

            print(f"上传响应状态码: {response.status_code}")
            print(f"上传响应内容: {response.text}")

            if response.status_code == 200 and response.json().get('error') == False:
                return True
            else:
                return False
        except Exception as e:
            print(f"上传过程中发生错误: {str(e)}")
            return False

    def update_app(self, file_name):
        try:
            update_url = f"http://{self.ip_address}:8090/cgi-bin/system?key=updateapp"
            headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': f'http://{self.ip_address}:8090',
                'Referer': f'http://{self.ip_address}:8090/'
            }
            data = {"package": file_name}

            response = requests.post(update_url, json=data, headers=headers, auth=(self.username, self.password))

            print(f"更新响应状态码: {response.status_code}")
            print(f"更新响应内容: {response.text}")

            response_data = json.loads(response.text)
            if response.status_code == 200 and not response_data.get('error', True):
                return True
            else:
                print(f"更新请求失败: {response_data.get('message', '未知错误')}")
                return False
        except Exception as e:
            print(f"更新过程中发生错误: {str(e)}")
            return False

if __name__ == "__main__":
    manager = CPUInfoManager()
    manager.start_query()
    manager.reset_device()
    manager.reboot_device()
    manager.write_m_zone(50, 0)
    manager.load_program()