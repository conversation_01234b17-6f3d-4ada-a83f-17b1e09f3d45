from flask import Blueprint, request, jsonify
from database.db_manager import DatabaseManager
from models.shipment import Order, SNRecord
from sqlalchemy import or_, and_
import logging

# 创建蓝图
shipment_query_bp = Blueprint('shipment_query', __name__)
logger = logging.getLogger(__name__)

@shipment_query_bp.route('/search', methods=['GET'])
def search_shipment_records():
    try:
        # 获取查询参数
        query_type = request.args.get('type', 'boxNumber')
        query_value = request.args.get('value', '').strip()
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        sort_field = request.args.get('sort_field')
        sort_order = request.args.get('sort_order', 'asc')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        
        # 日志输出查询参数
        logger.info(f"Search parameters: type={query_type}, value={query_value}, start_date={start_date}, end_date={end_date}")
        
        # 检查是否至少有一个查询条件
        if not query_value and not start_date and not end_date:
            return jsonify({
                'success': False,
                'message': '请输入至少一个查询条件'
            }), 400
        
        # 计算分页偏移量
        offset = (page - 1) * page_size
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 基础查询
            query = session.query(
                Order.box_serial_number,
                Order.shipping_number,
                Order.customer_name,
                SNRecord.sn_number,
                Order.created_at,
                Order.created_user
            ).outerjoin(  # 改为outer join
                SNRecord, Order.order_id == SNRecord.order_id
            )
            
            # 构建过滤条件列表
            filter_conditions = []
            
            # 添加日期范围条件
            if start_date:
                try:
                    from datetime import datetime
                    start_datetime = datetime.strptime(start_date, '%Y-%m-%d').strftime('%Y-%m-%d 00:00:00')
                    filter_conditions.append(Order.created_at >= start_datetime)
                except Exception as e:
                    logger.error(f"Error parsing start_date: {e}")
            
            if end_date:
                try:
                    from datetime import datetime
                    end_datetime = datetime.strptime(end_date, '%Y-%m-%d').strftime('%Y-%m-%d 23:59:59')
                    filter_conditions.append(Order.created_at <= end_datetime)
                except Exception as e:
                    logger.error(f"Error parsing end_date: {e}")
            
            # 添加查询条件（如果有查询值）
            if query_value:
                if query_type == 'boxNumber':
                    filter_conditions.append(Order.box_serial_number.like(f'%{query_value}%'))
                elif query_type == 'shipmentNumber':
                    filter_conditions.append(Order.shipping_number.like(f'%{query_value}%'))
                elif query_type == 'customerName':
                    filter_conditions.append(Order.customer_name.like(f'%{query_value}%'))
                elif query_type == 'productSN':
                    filter_conditions.append(SNRecord.sn_number.like(f'%{query_value}%'))
            
            # 应用所有过滤条件
            if filter_conditions:
                query = query.filter(and_(*filter_conditions))
            
            # 添加排序
            if sort_field:
                # 定义字段映射
                sort_field_mapping = {
                    'box_serial_number': Order.box_serial_number,
                    'shipping_number': Order.shipping_number,
                    'customer_name': Order.customer_name,
                    'sn_number': SNRecord.sn_number,
                    'created_at': Order.created_at,
                    'created_user': Order.created_user
                }
                
                # 获取排序字段
                sort_column = sort_field_mapping.get(sort_field)
                if sort_column is not None:
                    # 应用排序
                    if sort_order == 'desc':
                        query = query.order_by(sort_column.desc())
                    else:
                        query = query.order_by(sort_column.asc())
                else:
                    # 默认排序
                    query = query.order_by(Order.created_at.desc())
            else:
                # 默认排序
                query = query.order_by(Order.created_at.desc())
            
            # 输出生成的SQL语句用于调试
            logger.info(f"Generated SQL: {query}")
            
            # 获取总记录数
            total_count = query.count()
            logger.info(f"Total count: {total_count}")
            
            # 获取分页数据
            records = query.offset(offset).limit(page_size).all()
            
            # 转换为字典列表
            result = []
            for record in records:
                result.append({
                    'box_serial_number': record.box_serial_number,
                    'shipping_number': record.shipping_number,
                    'customer_name': record.customer_name,
                    'sn_number': record.sn_number,
                    'created_at': record.created_at.isoformat(),
                    'created_user': record.created_user
                })
            
            return jsonify({
                'success': True,
                'records': result,
                'totalCount': total_count
            })
            
    except Exception as e:
        logger.error(f"Error in search_shipment_records: {str(e)}")
        # 返回更详细的错误信息
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@shipment_query_bp.route('/export', methods=['GET'])
def export_shipment_records():
    try:
        # 获取查询参数
        query_type = request.args.get('type', 'boxNumber')
        query_value = request.args.get('value', '').strip()
        sort_field = request.args.get('sort_field')
        sort_order = request.args.get('sort_order', 'asc')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        
        # 获取选中记录的ID列表
        selected_records_json = request.args.get('selected_records', '')
        selected_records = []
        
        if selected_records_json:
            try:
                import json
                selected_records = json.loads(selected_records_json)
                logger.info(f"Selected records: {selected_records}")
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing selected_records: {e}")
        
        # 日志输出查询参数
        logger.info(f"Export parameters: type={query_type}, value={query_value}, start_date={start_date}, end_date={end_date}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 基础查询
            query = session.query(
                Order.box_serial_number,
                Order.shipping_number,
                Order.customer_name,
                SNRecord.sn_number,
                Order.created_at,
                Order.created_user
            ).outerjoin(
                SNRecord, Order.order_id == SNRecord.order_id
            )
            
            # 构建过滤条件列表
            filter_conditions = []
            
            # 添加日期范围条件
            if start_date:
                try:
                    from datetime import datetime
                    start_datetime = datetime.strptime(start_date, '%Y-%m-%d').strftime('%Y-%m-%d 00:00:00')
                    filter_conditions.append(Order.created_at >= start_datetime)
                except Exception as e:
                    logger.error(f"Error parsing start_date: {e}")
            
            if end_date:
                try:
                    from datetime import datetime
                    end_datetime = datetime.strptime(end_date, '%Y-%m-%d').strftime('%Y-%m-%d 23:59:59')
                    filter_conditions.append(Order.created_at <= end_datetime)
                except Exception as e:
                    logger.error(f"Error parsing end_date: {e}")
            
            # 添加查询条件（如果有查询值）
            if query_value:
                if query_type == 'boxNumber':
                    filter_conditions.append(Order.box_serial_number.like(f'%{query_value}%'))
                elif query_type == 'shipmentNumber':
                    filter_conditions.append(Order.shipping_number.like(f'%{query_value}%'))
                elif query_type == 'customerName':
                    filter_conditions.append(Order.customer_name.like(f'%{query_value}%'))
                elif query_type == 'productSN':
                    filter_conditions.append(SNRecord.sn_number.like(f'%{query_value}%'))
            
            # 应用所有过滤条件
            if filter_conditions:
                query = query.filter(and_(*filter_conditions))
            
            # 添加排序
            if sort_field:
                # 定义字段映射
                sort_field_mapping = {
                    'box_serial_number': Order.box_serial_number,
                    'shipping_number': Order.shipping_number,
                    'customer_name': Order.customer_name,
                    'sn_number': SNRecord.sn_number,
                    'created_at': Order.created_at,
                    'created_user': Order.created_user
                }
                
                # 获取排序字段
                sort_column = sort_field_mapping.get(sort_field)
                if sort_column is not None:
                    # 应用排序
                    if sort_order == 'desc':
                        query = query.order_by(sort_column.desc())
                    else:
                        query = query.order_by(sort_column.asc())
                else:
                    # 默认排序
                    query = query.order_by(Order.created_at.desc())
            else:
                # 默认排序
                query = query.order_by(Order.created_at.desc())
            
            # 获取所有记录（不分页）
            all_records = query.all()
            
            # 如果有选中的记录，只导出选中的记录
            if selected_records:
                filtered_records = []
                for record in all_records:
                    # 构建记录ID，格式与前端一致：shipping_number-sn_number
                    record_id = f"{record.shipping_number}-{record.sn_number}" if record.sn_number else f"{record.shipping_number}-"
                    if record_id in selected_records:
                        filtered_records.append(record)
                records = filtered_records
                logger.info(f"Exporting {len(filtered_records)} selected records out of {len(all_records)} total records")
            else:
                # 导出所有记录
                records = all_records
                logger.info(f"Exporting all {len(records)} records")
            
            # 创建Excel文件
            import xlsxwriter
            from io import BytesIO
            from datetime import datetime
            
            # 创建一个内存中的Excel文件
            output = BytesIO()
            workbook = xlsxwriter.Workbook(output)
            worksheet = workbook.add_worksheet('出库记录')
            
            # 设置表头样式
            header_format = workbook.add_format({
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#f3f4f6',
                'border': 1
            })
            
            # 设置数据行样式
            data_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            # 写入表头
            headers = ['序号', '产品SN号', '箱单流水号', '出库单号', '客户名称', '出库时间', '操作人']
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)
            
            # 写入数据行
            for row, record in enumerate(records, start=1):
                # 格式化日期 - 显示年月日
                created_at = record.created_at
                formatted_date = created_at.strftime('%Y-%m-%d')
                
                worksheet.write(row, 0, row, data_format)  # 序号
                worksheet.write(row, 1, record.sn_number or '-', data_format)  # 产品SN号
                worksheet.write(row, 2, record.box_serial_number, data_format)  # 箱单流水号
                worksheet.write(row, 3, record.shipping_number, data_format)  # 出库单号
                worksheet.write(row, 4, record.customer_name, data_format)  # 客户名称
                worksheet.write(row, 5, formatted_date, data_format)  # 出库时间
                worksheet.write(row, 6, record.created_user, data_format)  # 操作人
            
            # 调整列宽
            for col, width in enumerate([8, 20, 20, 20, 20, 15, 15]):
                worksheet.set_column(col, col, width)
            
            workbook.close()
            output.seek(0)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            # 如果有选中记录，在文件名中标注
            filename_suffix = f"_已选{len(records)}条" if selected_records else ""
            filename = f'出库记录_{timestamp}{filename_suffix}.xlsx'
            
            from flask import send_file
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )
            
    except Exception as e:
        logger.error(f"Error in export_shipment_records: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500
