// 使用 window 对象来存储全局状态，避免重复声明
if (typeof window.documentCenterState === 'undefined') {
    window.documentCenterState = {
        isActive: false,
        resizeHandler: null,
        currentItems: null,
        currentFolder: []
    };
}

function initDocumentCenter() {
    window.documentCenterState.isActive = true;
    const documentCenterContent = document.getElementById('document-center-content');
    if (!documentCenterContent) {
        Logger.error('Document center content element not found');
        return;
    }

    // 初始化数据
    if (!window.documentCenterState.currentItems) {
        window.documentCenterState.currentItems = [
            { id: 1, name: "市场部", type: "folder", children: [
                { id: 2, name: "项目报告.pdf", type: "pdf", size: "2.5 MB", date: "2023-05-15", url: "https://example.com/project-report.pdf", author: "张三" },
                { id: 3, name: "会议记录.docx", type: "word", size: "1.2 MB", date: "2023-05-14", url: "https://example.com/meeting-notes.docx", author: "李四" },
            ]},
            { id: 4, name: "技术部", type: "folder", children: [
                { id: 5, name: "产品演示.mp4", type: "video", size: "15.7 MB", date: "2023-05-13", url: "https://example.com/product-demo.mp4", author: "王五" },
                { id: 6, name: "代码库", type: "folder", children: [
                    { id: 7, name: "源代码.zip", type: "zip", size: "50 MB", date: "2023-05-12", url: "https://example.com/source-code.zip", author: "赵六" },
                ]},
            ]},
            { id: 8, name: "财务报表.xlsx", type: "excel", size: "3.8 MB", date: "2023-05-12", url: "https://example.com/financial-report.xlsx", author: "钱七" },
            { id: 9, name: "产品图片.jpg", type: "image", size: "1.5 MB", date: "2023-05-11", url: "https://example.com/product-image.jpg", author: "孙八" },
        ];
    }
    
    let currentItems = window.documentCenterState.currentItems;
    let currentFolder = window.documentCenterState.currentFolder;
    let isLoading = false;

    documentCenterContent.innerHTML = `
        <div class="dc-container">
            <!-- <h1 class="dc-title">文档中心</h1> -->
            <div class="dc-flex dc-flex-col dc-gap-4 dc-mb-6">
                <div class="dc-flex dc-gap-4">
                    <div class="dc-select-wrapper">
                        <select id="fileType" class="dc-select">
                            <option value="all">所有类型</option>
                            <option value="pdf">PDF</option>
                            <option value="word">Word</option>
                            <option value="video">视频</option>
                            <option value="excel">Excel</option>
                            <option value="image">图片</option>
                        </select>
                    </div>
                    <div class="dc-search-wrapper">
                        <span class="dc-search-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                            </svg>
                        </span>
                        <input id="search" type="text" placeholder="搜索文件..." class="dc-search-input">
                    </div>
                </div>
                <div class="dc-flex dc-gap-4">
                    <button id="uploadBtn" class="dc-btn dc-btn-primary">
                        <span>⬆️</span> 上传
                    </button>
                    <button id="newFolderBtn" class="dc-btn dc-btn-secondary">
                        <span>➕</span> 新建文件夹
                    </button>
                </div>
                <div class="dc-flex">
                    <button id="backBtn" class="dc-btn dc-btn-secondary" style="margin-right: 1rem;" disabled>
                        <span>⬅️</span> 返回上级
                    </button>
                    <div id="breadcrumb" class="dc-breadcrumb"></div>
                </div>
            </div>
            <table class="dc-table">
                <thead>
                    <tr>
                        <th style="width: 300px;">名称</th>
                        <th style="width: 100px;">类型</th>
                        <th style="width: 100px;">大小</th>
                        <th style="width: 150px;">上传日期</th>
                        <th style="width: 100px;">作者</th>
                        <th style="width: 150px;">操作</th>
                    </tr>
                </thead>
                <tbody id="fileList"></tbody>
            </table>
        </div>

        <div id="newFolderDialog" class="dc-dialog-overlay">
            <div class="dc-dialog-content">
                <h2 class="dc-title">新建文件夹</h2>
                <label for="folderName" style="display: block; margin-bottom: 0.5rem;">文件夹名称</label>
                <input id="folderName" type="text" class="dc-input" style="margin-bottom: 1rem;">
                <div style="text-align: right;">
                    <button id="createFolderBtn" class="dc-btn dc-btn-primary">确定</button>
                </div>
            </div>
        </div>

        <div id="previewDialog" class="dc-dialog-overlay">
            <div class="dc-dialog-content">
                <h2 id="previewTitle" class="dc-title"></h2>
                <div id="previewContent" style="max-height: 600px; overflow: auto;"></div>
            </div>
        </div>
    `;

    function renderItems() {
        const fileList = documentCenterContent.querySelector('#fileList');
        if (!fileList) {
            Logger.error('File list element not found');
            return;
        }

        const fileType = documentCenterContent.querySelector('#fileType').value;
        const search = documentCenterContent.querySelector('#search').value.toLowerCase();

        const fragment = document.createDocumentFragment();

        const filteredItems = currentItems.filter(item => 
            item.name.toLowerCase().includes(search) &&
            (fileType === "all" || item.type === fileType || item.type === "folder")
        );

        filteredItems.forEach(item => {
            const row = document.createElement('tr');
            
            // 定义文件类型图标
            const getFileIcon = (type) => {
                const icons = {
                    folder: `<svg class="dc-icon dc-icon-folder" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                    </svg>`,
                    pdf: `<svg class="dc-icon dc-icon-pdf" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"></path>
                        <path d="M14 2v6h6"></path>
                        <path d="M16 13H8"></path>
                        <path d="M16 17H8"></path>
                        <path d="M10 9H8"></path>
                    </svg>`,
                    word: `<svg class="dc-icon dc-icon-word" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"></path>
                        <path d="M14 2v6h6"></path>
                        <path d="M8 13h8"></path>
                        <path d="M8 17h8"></path>
                        <path d="M8 9h2"></path>
                    </svg>`,
                    excel: `<svg class="dc-icon dc-icon-excel" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"></path>
                        <path d="M14 2v6h6"></path>
                        <path d="M8 13h8"></path>
                        <path d="M8 17h8"></path>
                        <path d="M8 9h2"></path>
                    </svg>`,
                    image: `<svg class="dc-icon dc-icon-image" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <path d="M21 15l-5-5L5 21"></path>
                    </svg>`,
                    video: `<svg class="dc-icon dc-icon-video" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18"></rect>
                        <path d="M10 8l6 4-6 4V8z"></path>
                    </svg>`
                };
                return icons[type] || icons['folder'];
            };

            row.innerHTML = `
                <td>
                    ${item.type === "folder" 
                        ? `<button class="dc-folder-btn" data-id="${item.id}">
                            ${getFileIcon('folder')}
                            <span>${item.name}</span>
                            <svg class="dc-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 18l6-6-6-6"></path>
                            </svg>
                           </button>`
                        : `<div class="dc-folder-btn">
                            ${getFileIcon(item.type)}
                            <span>${item.name}</span>
                           </div>`
                    }
                </td>
                <td>${item.type === "folder" ? "文件夹" : item.type.toUpperCase()}</td>
                <td>${item.size || "-"}</td>
                <td>${item.date || "-"}</td>
                <td>${item.author || "-"}</td>
                <td class="dc-action-buttons">
                    ${item.type !== "folder" ? `
                        <button class="dc-btn-icon preview-btn" data-id="${item.id}" title="预览">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </button>
                        <button class="dc-btn-icon download-btn" data-id="${item.id}" title="下载">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"></path>
                                <path d="M7 10l5 5 5-5"></path>
                                <path d="M12 15V3"></path>
                            </svg>
                        </button>
                    ` : ''}
                    <button class="dc-btn-icon delete-btn" data-id="${item.id}" title="删除">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                            <path d="M10 11v6"></path>
                            <path d="M14 11v6"></path>
                        </svg>
                    </button>
                </td>
            `;
            fragment.appendChild(row);
        });

        fileList.innerHTML = '';
        fileList.appendChild(fragment);

        // 添加事件监听器
        documentCenterContent.querySelectorAll('.dc-folder-btn').forEach(btn => {
            btn.addEventListener('click', () => navigateToFolder(parseInt(btn.dataset.id)));
        });

        documentCenterContent.querySelectorAll('.preview-btn').forEach(btn => {
            btn.addEventListener('click', () => previewFile(parseInt(btn.dataset.id)));
        });

        documentCenterContent.querySelectorAll('.download-btn').forEach(btn => {
            btn.addEventListener('click', () => downloadFile(parseInt(btn.dataset.id)));
        });

        documentCenterContent.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', () => deleteItem(parseInt(btn.dataset.id)));
        });
    }

    function navigateToFolder(folderId) {
        const folder = currentItems.find(item => item.id === folderId);
        if (folder && folder.children) {
            currentFolder.push(folderId);
            currentItems = folder.children;
            renderItems();
            updateBreadcrumb();
            updateBackButton();
        }
    }

    function updateCurrentItems() {
        let current = window.documentCenterState.currentItems;
        for (let folderId of currentFolder) {
            current = current.find(item => item.id === folderId).children;
        }
        currentItems = current;
    }

    function updateBreadcrumb() {
        const breadcrumb = documentCenterContent.querySelector('#breadcrumb');
        breadcrumb.innerHTML = '';

        let breadcrumbs = [{id: 'root', name: '根目录'}];
        let current = window.documentCenterState.currentItems;
        for (let folderId of currentFolder) {
            let folder = current.find(item => item.id === folderId);
            breadcrumbs.push(folder);
            current = folder.children;
        }

        breadcrumbs.forEach((item, index) => {
            const link = document.createElement('button');
            link.className = 'dc-breadcrumb-btn';
            link.textContent = item.name;
            link.onclick = () => navigateToBreadcrumb(index);

            breadcrumb.appendChild(link);

            if (index < breadcrumbs.length - 1) {
                const separator = document.createElement('span');
                separator.textContent = ' > ';
                breadcrumb.appendChild(separator);
            }
        });
    }

    function navigateToBreadcrumb(index) {
        currentFolder = currentFolder.slice(0, index);
        updateCurrentItems();
        renderItems();
        updateBreadcrumb();
        updateBackButton();
    }

    function updateBackButton() {
        const backBtn = documentCenterContent.querySelector('#backBtn');
        backBtn.disabled = currentFolder.length === 0;
    }

    function navigateToParent() {
        if (currentFolder.length > 0) {
            currentFolder.pop();
            updateCurrentItems();
            renderItems();
            updateBreadcrumb();
            updateBackButton();
        }
    }

    function previewFile(fileId) {
        const file = currentItems.find(item => item.id === fileId);
        if (!file) return;

        const previewDialog = documentCenterContent.querySelector('#previewDialog');
        const previewTitle = documentCenterContent.querySelector('#previewTitle');
        const previewContent = documentCenterContent.querySelector('#previewContent');

        previewTitle.textContent = file.name;

        let content = '';
        if (file.type === "pdf") {
            content = `<iframe src="${file.url}" style="width: 100%; height: 600px;"></iframe>`;
        } else if (file.type === "video") {
            content = `<video src="${file.url}" controls style="width: 100%; max-height: 600px;"></video>`;
        } else if (file.type === "image") {
            content = `<img src="${file.url}" alt="${file.name}" style="max-width: 100%; max-height: 600px;">`;
        } else if (file.type === "word" || file.type === "excel") {
            content = `<iframe src="https://docs.google.com/viewer?url=${encodeURIComponent(file.url)}&embedded=true" style="width: 100%; height: 600px;"></iframe>`;
        } else {
            content = '<div>无法预览此文件类型</div>';
        }

        previewContent.innerHTML = content;
        previewDialog.classList.add('active');
    }

    function downloadFile(fileId) {
        setLoading(true);
        setTimeout(() => {
            alert(`下载文件ID: ${fileId}`);
            setLoading(false);
        }, 1000);
    }

    function deleteItem(itemId) {
        setLoading(true);
        setTimeout(() => {
            currentItems = currentItems.filter(item => item.id !== itemId);
            renderItems();
            setLoading(false);
        }, 500);
    }

    function setLoading(loading) {
        isLoading = loading;
        const buttons = documentCenterContent.querySelectorAll('button');
        buttons.forEach(button => button.disabled = loading);
    }

    documentCenterContent.querySelector('#uploadBtn').addEventListener('click', () => {
        setLoading(true);
        setTimeout(() => {
            alert("文件上传功能将在实际应用中实现");
            setLoading(false);
        }, 1000);
    });

    documentCenterContent.querySelector('#newFolderBtn').addEventListener('click', () => {
        documentCenterContent.querySelector('#newFolderDialog').classList.add('active');
    });

    documentCenterContent.querySelector('#createFolderBtn').addEventListener('click', () => {
        const folderName = documentCenterContent.querySelector('#folderName').value;
        if (!folderName) return;

        const newFolder = {
            id: Date.now(),
            name: folderName,
            type: "folder",
            children: []
        };

        setLoading(true);
        setTimeout(() => {
            currentItems.push(newFolder);
            renderItems();
            documentCenterContent.querySelector('#newFolderDialog').classList.remove('active');
            documentCenterContent.querySelector('#folderName').value = '';
            setLoading(false);
        }, 500);
    });

    documentCenterContent.querySelector('#backBtn').addEventListener('click', navigateToParent);

    documentCenterContent.querySelector('#fileType').addEventListener('change', renderItems);
    documentCenterContent.querySelector('#search').addEventListener('input', renderItems);

    documentCenterContent.querySelectorAll('.dc-dialog-overlay').forEach(dialog => {
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                dialog.classList.remove('active');
            }
        });
    });

    function adjustLayoutForMobile() {
        if (!window.documentCenterState.isActive) return;
        
        const documentCenterContent = document.getElementById('document-center-content');
        if (!documentCenterContent) return;
    }

    if (window.documentCenterState.resizeHandler) {
        window.removeEventListener('resize', window.documentCenterState.resizeHandler);
    }

    window.documentCenterState.resizeHandler = adjustLayoutForMobile;
    window.addEventListener('resize', window.documentCenterState.resizeHandler);
    
    adjustLayoutForMobile();
    renderItems();
    updateBreadcrumb();
    updateBackButton();
}

function cleanupDocumentCenter() {
    window.documentCenterState.isActive = false;
    if (window.documentCenterState.resizeHandler) {
        window.removeEventListener('resize', window.documentCenterState.resizeHandler);
        window.documentCenterState.resizeHandler = null;
    }
}

window.initDocumentCenter = initDocumentCenter;
window.cleanupDocumentCenter = cleanupDocumentCenter;
