# models/firmware.py
from database.db_manager import Base
from sqlalchemy import Column, String, Text, Integer, DateTime, Boolean, Enum, ForeignKey, JSON, Numeric, BigInteger
from sqlalchemy.orm import relationship
from datetime import datetime

class Firmware(Base):
    """固件主表模型"""
    __tablename__ = 'firmware'
    
    # 基本信息
    serial_number = Column(String(30), primary_key=True, comment='ERP流水号')
    name = Column(String(80), nullable=False, comment='固件名称')
    version = Column(String(50), nullable=False, comment='版本号')
    status = Column(Enum('active', 'pending', 'rejected', 'obsolete', name='firmware_status'), 
                      nullable=False, default='pending', comment='状态')
    source = Column(Enum('新发布', '升级', '修改', name='firmware_source'), 
                      nullable=False, default='新发布', comment='来源类型')
    
    # 业务信息
    products = Column(JSON, nullable=False, comment='适用产品JSON数组')
    version_requirements = Column(Text, comment='版本使用要求')
    description = Column(Text, nullable=False, comment='变更内容')
    developer = Column(String(20), nullable=False, comment='研发者')
    uploader = Column(String(20), nullable=False, comment='上传者')
    parent_sn = Column(String(30), ForeignKey('firmware.serial_number'), comment='父版本')
    
    # 技术规格信息
    build_time = Column(String(50), comment='构建时间')
    backplane_version = Column(String(50), comment='背板总线版本')
    io_version = Column(String(50), comment='高速IO版本')
    
    # 审核信息（性能优化冗余）
    approver_name = Column(String(20), comment='审核者姓名')
    reject_reason = Column(Text, comment='拒绝理由')
    
    # 统计信息
    download_count = Column(Integer, nullable=False, default=0, comment='下载次数')
    usage_count = Column(Integer, nullable=False, default=0, comment='使用次数')
    
    # 时间信息
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    approve_time = Column(DateTime, comment='审核通过时间')
    obsolete_time = Column(DateTime, comment='作废时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, comment='更新时间')
    
    # 软删除标记
    is_deleted = Column(Boolean, nullable=False, default=False, comment='软删除标记')
    
    # 关系定义
    children = relationship('Firmware', backref='parent', remote_side=[serial_number])
    files = relationship('FirmwareFile', back_populates='firmware')
    download_records = relationship('DownloadRecord', back_populates='firmware')
    approval_flows = relationship('ApprovalFlow', back_populates='firmware')
    
    def to_dict(self):
        """转换为字典格式，供API返回"""
        return {
            'serialNumber': self.serial_number,
            'name': self.name,
            'version': self.version,
            'status': self.status,
            'source': self.source,
            'products': self.products or [],
            'versionRequirements': self.version_requirements,
            'description': self.description,
            'developer': self.developer,
            'uploader': self.uploader,
            'approverName': self.approver_name,
            'downloadCount': self.download_count,
            'usageCount': self.usage_count,
            'createTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'uploadTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'approveTime': self.approve_time.strftime('%Y-%m-%d %H:%M:%S') if self.approve_time else None,
            'obsoleteTime': self.obsolete_time.strftime('%Y-%m-%d %H:%M:%S') if self.obsolete_time else None,
            'parentSn': self.parent_sn,
            'rejectReason': self.reject_reason,
            # 技术规格信息
            'buildTime': self.build_time,
            'backplaneVersion': self.backplane_version,
            'ioVersion': self.io_version
        }

class FirmwareFile(Base):
    """固件文件表模型"""
    __tablename__ = 'firmware_file'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    serial_number = Column(String(30), ForeignKey('firmware.serial_number'), 
                             nullable=False, comment='关联固件')
    file_path = Column(String(500), nullable=False, comment='文件路径')
    original_filename = Column(String(255), comment='原始文件名')
    file_hash = Column(String(64), nullable=False, comment='SHA256哈希')
    file_size = Column(Numeric(10, 2), nullable=False, comment='文件大小MB')
    upload_time = Column(DateTime, nullable=False, default=datetime.now, comment='上传时间')
    is_deleted = Column(Boolean, nullable=False, default=False, comment='软删除标记')
    create_time = Column(DateTime, nullable=False, default=datetime.now)
    
    # 关系定义
    firmware = relationship('Firmware', back_populates='files')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'serialNumber': self.serial_number,
            'filePath': self.file_path,
            'originalFilename': self.original_filename,
            'fileHash': self.file_hash,
            'fileSize': float(self.file_size),
            'uploadTime': self.upload_time.strftime('%Y-%m-%d %H:%M:%S') if self.upload_time else None
        }

class DownloadRecord(Base):
    """下载记录表模型"""
    __tablename__ = 'download_record'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    serial_number = Column(String(30), ForeignKey('firmware.serial_number'), 
                             nullable=False, comment='固件流水号')
    work_order = Column(String(50), nullable=False, comment='工单号')
    product_code = Column(String(50), nullable=False, comment='产品编码')
    product_model = Column(String(50), nullable=False, comment='产品型号')
    burn_count = Column(Integer, nullable=False, comment='烧录数量')
    software_version = Column(String(50), nullable=False, comment='软件版本')
    build_time = Column(String(30), comment='构建时间')
    backplane_version = Column(String(50), nullable=False, comment='背板版本')
    io_version = Column(String(50), nullable=False, comment='高速IO版本')
    user_name = Column(String(20), nullable=False, comment='使用人')
    notes = Column(Text, comment='备注信息')
    is_deleted = Column(Boolean, nullable=False, default=False, comment='软删除标记')
    create_time = Column(DateTime, nullable=False, default=datetime.now)
    
    # 关系定义
    firmware = relationship('Firmware', back_populates='download_records')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'serialNumber': self.serial_number,
            'workOrder': self.work_order,
            'productCode': self.product_code,
            'productModel': self.product_model,
            'burnCount': self.burn_count,
            'softwareVersion': self.software_version,
            'buildTime': self.build_time or '',
            'backplaneVersion': self.backplane_version,
            'ioVersion': self.io_version,
            'userName': self.user_name,
            'notes': self.notes,
            'usageTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None
        }

class ApprovalFlow(Base):
    """审核流水表模型"""
    __tablename__ = 'approval_flow'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    serial_number = Column(String(30), ForeignKey('firmware.serial_number'), 
                             nullable=False, comment='固件流水号')
    action = Column(Enum('submit', 'approve', 'reject', 'obsolete', name='approval_action'), 
                      nullable=False, comment='操作类型')
    operator_name = Column(String(20), nullable=False, comment='操作人姓名')
    notes = Column(Text, comment='备注')
    create_time = Column(DateTime, nullable=False, default=datetime.now)
    
    # 关系定义
    firmware = relationship('Firmware', back_populates='approval_flows')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'serialNumber': self.serial_number,
            'action': self.action,
            'operatorName': self.operator_name,
            'notes': self.notes,
            'createTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None
        }

class FirmwareSerialDetail(Base):
    """固件序列号录入明细表模型"""
    __tablename__ = 'firmware_serial_detail'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    work_order = Column(String(50), nullable=False, comment='工单号(关联字段)')
    serial_type = Column(Enum('SN', 'PCBA', name='serial_type_enum'), 
                          nullable=False, comment='序列号类型')
    firmware_sn = Column(String(50), nullable=True, default=None, comment='SN号(唯一)')
    firmware_pcba = Column(String(50), nullable=True, default='N/A', comment='PCBA序列号')
    firmware_hash = Column(String(64), nullable=True, default='N/A', comment='PCBA序列号SHA256哈希值')
    serial_digits = Column(Integer, nullable=False, comment='序列号位数要求')
    input_user = Column(String(20), nullable=False, comment='录入人员')
    input_time = Column(DateTime, nullable=False, default=datetime.now, comment='录入时间')
    is_deleted = Column(Boolean, nullable=False, default=False, comment='软删除标记')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式，遵循前端驼峰命名规范"""
        return {
            'id': self.id,
            'workOrder': self.work_order,
            'serialType': self.serial_type,
            'firmwareSn': self.firmware_sn,
            'firmwarePcba': self.firmware_pcba if self.firmware_pcba != 'N/A' else None,
            'firmwareHash': self.firmware_hash if self.firmware_hash != 'N/A' else None,
            'serialDigits': self.serial_digits,
            'inputUser': self.input_user,
            'inputTime': self.input_time.strftime('%Y-%m-%d %H:%M:%S') if self.input_time else None,
            'createTime': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None
        }
    
    def __repr__(self):
        """模型字符串表示"""
        return f'<FirmwareSerialDetail(id={self.id}, work_order={self.work_order}, serial_type={self.serial_type})>' 