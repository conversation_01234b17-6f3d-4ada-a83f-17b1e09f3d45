# SN打印记录页面使用手册

## 一、功能概述

SN打印记录页面是用于生成、验证和管理产品序列号 (SN) 的专用工具。它能帮助操作人员高效、准确地完成从工单信息获取、SN配置、批量SN生成、逐个扫描验证直至数据实时入库的全过程。本手册将指导您如何使用此页面的各项功能。

## 二、页面布局概览

SN打印记录页面主要分为以下几个区域：

1.  **基本信息区**：位于页面左上方，用于填写或显示操作人员、加工单号、产品型号、产品编码和生产数量。
2.  **SN配置区**：位于页面右上方，用于配置订货号、产品SN号位数、流水号长度、生产日期和生产周期。
3.  **操作与首件SN区**：位于基本信息和SN配置区下方，包含"首件SN"的显示与输入，以及"生成序列号"、"重置表单"、"导出Excel"等主要操作按钮。
4.  **SN列表与验证区**：在生成序列号后出现，分为左右两栏：
    *   **左侧SN号列表**：展示生成的SN序号、完整的SN号以及当前的验证状态（待验证、已验证）。
    *   **右侧扫描验证**：提供SN扫描/输入框、实时扫描状态反馈、验证进度条、最近扫描记录查询，以及"重置扫描"和"完成验证"按钮。

## 三、详细操作步骤

请按照以下步骤使用SN打印记录页面：

### 1. 进入页面与信息自动填充

*   **操作人员**：进入页面后，系统会自动获取当前登录的用户名并填充到"操作人员"字段，此字段通常为只读。
*   **日期 (YYMMDD)**：系统会自动填充当前日期到"日期"字段，格式为年(后两位)月日，例如 `240730`。
*   **周期**：根据自动填充的日期，系统会自动计算并填充对应的ISO 8601标准周数到"周期"字段。

### 2. 填写加工单号以获取工单信息

*   在"基本信息区"找到"**加工单号**"输入框。
*   手动输入准确的加工单号。
*   **联动效应**：
    1.  输入加工单号后，系统会自动向服务器请求该工单的详细信息。
    2.  成功获取后，"产品型号"、"产品编码"、"生产数量"以及"SN配置区"的"产品SN号位数"会自动填充。
    3.  当"产品编码"填充后，系统会继续自动查询并填充"SN配置区"的"**订货号**"。
    4.  一旦"订货号"、产品编码、年份（来自日期字段）、周期等关键信息准备就绪，系统会自动计算并生成一个推荐的"**首件SN**"显示在操作区的输入框中。
*   **快捷操作**：在"加工单号"输入框内输入完毕后，按 `Enter` (回车键)，系统将在尝试加载工单信息后，自动将光标聚焦到"首件SN"输入框，方便您检查或修改。

### 3. 检查与调整SN配置信息

在系统自动填充大部分SN配置信息后，请仔细检查并根据实际需求进行调整：

*   **订货号**：通常根据产品编码自动获取。如果需要，可以手动修改。
*   **产品SN号位数**：通常根据工单信息自动填充，显示了最终SN的总长度。
*   **流水号长度**：指SN中末尾的序列数字的长度，默认为4位（例如 `0001`）。您可以根据需要修改此值（例如改为3位，则为`001`）。
*   **日期 (YYMMDD)**：默认为当天。如果需要生成非当日的SN，请手动修改。修改此日期会自动触发"周期"字段的重新计算。
*   **周期**：根据"日期"自动计算。如果实际生产周期与系统计算不符，您也可以手动修改此值。

### 4. 确认或修改"首件SN"

"首件SN"是生成整批序列号的起始号，非常关键。

*   **自动生成**：系统会根据以上所有配置（订货号、日期中的年份、周期、以及通过后端获取的基于当前产品和周期的下一个可用流水号）自动生成一个推荐的"首件SN"。
*   **手动修改**：
    *   您可以直接在"首件SN"输入框中修改自动生成的SN。
    *   **系统验证**：当您手动修改"首件SN"后，系统会进行以下校验：
        1.  **长度校验**：检查修改后的SN总长度是否与"产品SN号位数"字段设定的长度一致。
        2.  **前缀校验**：检查修改后的SN是否以"订货号"字段的内容开头。
        3.  **日期与周期同步**：系统会尝试从您输入的SN中提取年份和周期信息（通常在订货号之后，流水号之前）。如果提取出的年份/周期与当前"日期"/"周期"字段不一致，系统会自动更新"日期"和"周期"字段以匹配您输入的SN。
        4.  **周期匹配警告**：如果从SN中提取的周期与系统根据当前"日期"字段计算出的标准ISO周期不一致，系统会弹窗提示"周期不匹配"，并询问您是"继续使用（SN中的周期）"还是"使用系统周期"。请谨慎选择。
*   **快捷生成**：在"首件SN"输入框中，确认或修改完毕后，直接按 `Enter` (回车键)，如果所有验证通过，系统将直接执行"生成序列号"的操作。

### 5. 生成批量序列号

*   确保所有基本信息和SN配置无误，并且"首件SN"已确认。
*   点击"**生成序列号**"按钮。
*   系统会根据"首件SN"和"生产数量"字段设定的数量，生成一批连续的SN。
*   操作成功后，页面下方会展开显示"SN号列表"和"扫描验证"区域。生成的SN会显示在列表中，初始状态为"待验证"。

### 6. 进行扫描验证（核心步骤：验证即保存）

此步骤用于核对打印出的SN标签是否与系统生成的一致，并记录验证结果。

*   **扫描/输入SN**：
    *   在"扫描验证"区域右侧的"请扫描或输入SN号"输入框中，使用条码扫描枪扫描您要验证的SN标签。
    *   如果没有扫描枪，也可以手动输入SN号，然后按 `Enter` (回车键) 或点击旁边的"验证"按钮。
*   **查看验证结果**：
    *   **即时反馈**：输入框下方的"扫描状态"区域会立刻显示本次验证的结果：
        *   `SN: [SN号] 验证成功并已保存` (绿色)：表示此SN在生成的列表中，是首次验证，并且已成功记录到数据库。对应SN在左侧列表中的状态会变为"已验证"（绿色）。
        *   `SN: [SN号] 重复扫描` (红色)：表示此SN之前已经被成功验证过。
        *   `SN: [SN号] 不在列表中` (红色)：表示此SN不是本批次生成的SN。
        *   `SN: [SN号] 验证成功但保存失败 (网络错误)` (黄色)：表示此SN在列表中且为首次扫描，但由于网络或其他原因未能成功保存到数据库。此时SN在列表中状态会变为"已验证"但带有警告色，请关注并联系IT支持。
    *   **列表状态更新**：左侧"SN号列表"中对应SN的"状态"列会根据验证结果更新。
    *   **进度更新**：
        *   "已扫描: [数量] / [总数]"会实时更新。
        *   "验证进度"条会直观显示已成功验证的SN占总数的百分比。
    *   **最近扫描记录**："最近扫描记录"列表会显示最近几次扫描的SN号、扫描时间及验证状态（成功/失败图标）。
*   **持续扫描**：重复以上步骤，直到验证完所有SN。扫描输入框在每次验证后会自动清空并重新聚焦，方便连续操作。

### 7. 完成所有SN的验证

*   当"验证进度"条达到100%，即所有SN都已成功验证（状态为绿色"已验证"）时：
    *   系统会自动弹窗提示"验证完成，所有序列号已验证成功"。
    *   "**完成验证**"按钮将变为可用状态。
*   点击"**完成验证**"按钮：
    *   系统会再次弹窗确认"所有SN号已验证并保存，共 [数量] 条记录"。
    *   点击确认后，系统会自动执行一次"**重置表单**"操作（详见下一步），清空当前批次信息，方便您开始新的SN生成和验证任务。

### 8. 使用其他辅助功能

*   **重置表单**：
    *   在任何时候，如果您想清除当前填写的所有信息并重新开始，可以点击"**重置表单**"按钮。
    *   系统会弹窗要求确认。
    *   确认后，除了"操作人员"、"日期"、"周期"和"流水号长度"这几个字段会保留其当前值外，其他如加工单信息、产品信息、首件SN、生成的SN列表及所有扫描记录都会被清空。
*   **重置扫描**：
    *   如果在对一批已生成的SN进行扫描验证的过程中，您希望清除当前的扫描进度和记录，重新开始对这批SN的验证（例如，中途被打断或发现起始扫描有误），可以点击"扫描验证"区域的"**重置扫描**"按钮。
    *   这会清空所有已扫描SN的状态（列表中的状态会从"已验证"恢复为"待验证"）、清除扫描计数、进度条和最近扫描记录。已生成的SN列表本身会保留。
*   **导出Excel**：
    *   在生成SN列表后，如果您需要将这批SN导出存档，可以点击"**导出Excel**"按钮。
    *   系统会提示"序列号列表已导出到Excel文件"。请留意浏览器是否提示下载文件。

## 四、重要提示与注意事项

*   **验证即保存**：本系统的核心特性之一。每个SN在扫描验证成功后，其信息会**立即自动保存**到数据库，无需在所有SN验证完毕后进行统一的"提交"或"保存"操作。这能有效防止数据意外丢失。
*   **SN流水号的连续性**：
    *   系统支持智能流水号管理。对于**相同的产品编码和订货号**，在**相同的年份和周期**内，生成的SN流水号会在不同加工单之间**连续累加**。
    *   例如：工单A（产品P001，订货号ABC，24年第30周）生成的SN流水号是 `0001` 到 `0010`。那么，后续的工单B（同样是产品P001，订货号ABC，24年第30周）在生成SN时，其"首件SN"的流水号会自动从 `0011` 开始。
    *   当进入新的周期（例如24年第31周）或新的年份（例如25年），即使产品编码和订货号相同，流水号也会从该产品在新周期/年份下的第一个可用流水号（通常是 `0001`，除非该周期/年份下已有记录）重新开始。
*   **信息准确性**：请务必确保输入的"加工单号"、"订货号"（如果手动修改）以及其他配置信息的准确性，这将直接影响生成的SN是否正确。
*   **网络连接**：由于"验证即保存"依赖网络将数据写入数据库，请确保操作时网络连接稳定。如遇提示保存失败，请及时联系IT技术支持。
*   **浏览器兼容性**：建议使用主流现代浏览器（如Chrome、Edge、Firefox的最新版本）以获得最佳体验。

希望本手册能帮助您顺利使用SN打印记录页面。如有任何疑问，请联系相关技术支持人员。 