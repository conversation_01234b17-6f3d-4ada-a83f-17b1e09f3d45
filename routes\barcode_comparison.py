from flask import Blueprint, jsonify, request
from database.db_manager import DatabaseManager
from sqlalchemy import text

barcode_comparison_bp = Blueprint('barcode_comparison', __name__)

@barcode_comparison_bp.route('/search', methods=['GET'])
def get_passed_products():
    try:
        # 获取工单号
        order_number = request.args.get('orderNumber')
        if not order_number:
            return jsonify({
                'success': False,
                'message': '请提供工单号'
            }), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 构建SQL查询
            sql = text("""
                SELECT 
                    ROW_NUMBER() OVER (ORDER BY test_time DESC) as id,
                    work_order as order_number,
                    pro_model as product_model,
                    pro_sn as serial_number,
                    test_status as test_result
                FROM (
                    SELECT work_order, pro_model, pro_sn, test_status, test_time
                    FROM cpu_table 
                    WHERE work_order LIKE :order_number 
                    AND UPPER(test_status) = 'PASS'
                    
                    UNION ALL
                    
                    SELECT work_order, pro_model, pro_sn, test_status, test_time
                    FROM coupler_table 
                    WHERE work_order LIKE :order_number 
                    AND UPPER(test_status) = 'PASS'
                ) combined_data
                ORDER BY test_time DESC
            """)

            results = session.execute(sql, {'order_number': f'%{order_number}%'})
            
            # 处理查询结果
            data = []
            for row in results:
                data.append({
                    'id': row.id,
                    'orderNumber': row.order_number,
                    'productModel': row.product_model,
                    'serialNumber': row.serial_number,
                    'testResult': row.test_result
                })

            return jsonify({
                'success': True,
                'data': data,
                'message': f'找到 {len(data)} 条记录'
            })

    except Exception as e:
        print(f"查询出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500

@barcode_comparison_bp.route('/update-comparison-result', methods=['POST'])
def update_comparison_result():
    try:
        data = request.get_json()
        sn = data.get('serialNumber')
        result = data.get('result')
        comparison_user = data.get('comparisonUser')  # 获取比对人员
        
        if not sn or not result or not comparison_user:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 先查询 cpu_table
            check_cpu_sql = text("SELECT pro_sn FROM cpu_table WHERE pro_sn = :sn")
            cpu_exists = session.execute(check_cpu_sql, {'sn': sn}).fetchone()
            
            if cpu_exists:
                # 更新 cpu_table
                update_sql = text("""
                    UPDATE cpu_table 
                    SET comparison_result = :result,
                        comparison_time = CURRENT_TIMESTAMP,
                        comparison_user = :comparison_user
                    WHERE pro_sn = :sn
                """)
                session.execute(update_sql, {
                    'result': result, 
                    'sn': sn,
                    'comparison_user': comparison_user
                })
            else:
                # 检查 coupler_table
                check_coupler_sql = text("SELECT pro_sn FROM coupler_table WHERE pro_sn = :sn")
                coupler_exists = session.execute(check_coupler_sql, {'sn': sn}).fetchone()
                
                if coupler_exists:
                    # 更新 coupler_table
                    update_sql = text("""
                        UPDATE coupler_table 
                        SET comparison_result = :result,
                            comparison_time = CURRENT_TIMESTAMP,
                            comparison_user = :comparison_user
                        WHERE pro_sn = :sn
                    """)
                    session.execute(update_sql, {
                        'result': result, 
                        'sn': sn,
                        'comparison_user': comparison_user
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': f'未找到SN号为 {sn} 的记录'
                    }), 404

            session.commit()
            return jsonify({
                'success': True,
                'message': '比对结果更新成功'
            })

    except Exception as e:
        print(f"更新比对结果失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新失败：{str(e)}'
        }), 500
