// 待审核固件页面Vue应用
(function() {
    'use strict';
    
    Logger.log('Loading Pending Firmware Vue App...');
    
    // 检查共享分页功能是否已加载
    if (!window.FirmwarePagination) {
        Logger.error('FirmwarePagination not loaded. Please include firmware-common-paging.js first.');
        return;
    }
    
    if (!window.Vue || !window.ElementPlus) {
        Logger.error('Vue or ElementPlus not loaded');
        return;
    }

    const { createApp, ref, reactive, computed, onMounted, onUnmounted } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    
    const PendingFirmwareApp = {
        setup() {
            // ===== 响应式数据 =====
            const searchQuery = ref('');
            const sortProp = ref('');
            const sortOrder = ref('');
            const loading = ref(false);
            
            // 对话框状态
            const pendingDetailDialogVisible = ref(false);
            
            // 当前用户信息
            const currentUser = reactive({
                id: '001',
                name: '管理员',
                role: 'admin'
            });
            
            // 待审核详情数据
            const pendingDetails = reactive({
                id: '',
                serialNumber: '',
                name: '',
                version: '',
                developer: '',
                uploadTime: '',
                uploader: '',
                products: [],
                versionRequirements: '',
                description: '',
                source: '',
                oldSerialNumber: '',
                filePath: '',
                // 新增技术规格字段
                buildTime: '',
                backplaneVersion: '',
                ioVersion: ''
            });
            
            // 状态映射
            const statusMap = reactive({
                'active': '已生效',
                'pending': '待审核',
                'rejected': '审核退回',
                'obsolete': '已作废'
            });
            
            const sourceMap = reactive({
                '新发布': 'primary',
                '升级': 'success',
                '修改': 'warning'
            });
            
            // 获取全局数据管理器
            const dataManager = window.FirmwareDataManager;
            
            // 待审核固件列表（响应式）
            const pendingFirmwareList = ref([]);
            
            // 加载数据 - 改为异步
            const loadData = async () => {
                loading.value = true;
                try {
                    const data = await dataManager.getPendingFirmware();
                    pendingFirmwareList.value = data || [];
                    Logger.log('[Pending Firmware] 数据加载成功:', data.length, '条记录');
                } catch (error) {
                    Logger.error('[Pending Firmware] 数据加载失败:', error);
                    ElMessage.error('数据加载失败: ' + error.message);
                    pendingFirmwareList.value = [];
                } finally {
                    loading.value = false;
                }
            };
            
            // 事件监听器
            let eventListeners = [];
            
            // ===== 计算属性 =====
            const filteredPendingList = computed(() => {
                let list = [...pendingFirmwareList.value];
                
                // 搜索过滤
                if (searchQuery.value) {
                    const query = searchQuery.value.toLowerCase();
                    list = list.filter(item => 
                        item.serialNumber.toLowerCase().includes(query) ||
                        item.name.toLowerCase().includes(query) ||
                        item.version.toLowerCase().includes(query) ||
                        item.developer.toLowerCase().includes(query) ||
                        item.uploader.toLowerCase().includes(query) ||
                        item.source.toLowerCase().includes(query) ||
                        (item.products && item.products.some(p => p.model.toLowerCase().includes(query)))
                    );
                }
                
                // 排序
                if (sortProp.value && sortOrder.value) {
                    list.sort((a, b) => {
                        let aVal = a[sortProp.value];
                        let bVal = b[sortProp.value];
                        
                        if (sortProp.value === 'products') {
                            aVal = a.products ? a.products.map(p => p.model).join(', ') : '';
                            bVal = b.products ? b.products.map(p => p.model).join(', ') : '';
                        }
                        
                        if (typeof aVal === 'string') {
                            aVal = aVal.toLowerCase();
                            bVal = bVal.toLowerCase();
                        }
                        
                        if (aVal < bVal) return sortOrder.value === 'ascending' ? -1 : 1;
                        if (aVal > bVal) return sortOrder.value === 'ascending' ? 1 : -1;
                        return 0;
                    });
                }
                
                return list;
            });
            
            // 使用共享分页功能（在filteredPendingList定义之后）
            const pagination = window.FirmwarePagination.usePagination({
                dataList: filteredPendingList,
                defaultPageSize: 10,
                pageSizeOptions: [10, 20, 50, 100]
            });
            
            // 当前页显示的数据（使用共享分页功能）
            const paginatedPendingList = pagination.paginatedData;
            
            // ===== 方法 =====
            
            // 搜索相关
            const handleSearch = () => {
                Logger.log('搜索:', searchQuery.value);
                // 搜索时重置到第一页
                pagination.resetToFirstPage();
            };
            
            const handleSearchClear = () => {
                searchQuery.value = '';
                // 清空搜索时重置到第一页
                pagination.resetToFirstPage();
            };
            
            // 排序
            const handleSortChange = (column) => {
                sortProp.value = column.prop;
                sortOrder.value = column.order;
                Logger.log('排序变更:', column.prop, column.order);
            };
            
            // 显示详情
            const showPendingDetails = (row) => {
                Object.assign(pendingDetails, {
                    id: row.id,
                    serialNumber: row.serialNumber,
                    name: row.name,
                    version: row.version,
                    developer: row.developer,
                    uploadTime: row.uploadTime,
                    uploader: row.uploader,
                    products: row.products || [],
                    versionRequirements: row.versionRequirements || '',
                    description: row.description || '',
                    source: row.source || '',
                    oldSerialNumber: row.oldSerialNumber || '无',
                    filePath: row.filePath || '',
                    // 新增技术规格字段
                    buildTime: row.buildTime || '',
                    backplaneVersion: row.backplaneVersion || '',
                    ioVersion: row.ioVersion || ''
                });
                pendingDetailDialogVisible.value = true;
            };
            
            // 单个审核操作 - 改为异步
            const approveFirmware = async (row) => {
                try {
                    await ElMessageBox.confirm(
                        `确定要审核通过固件 "${row.name} ${row.version}" 吗？`, 
                        '审核确认', 
                        {
                            confirmButtonText: '确定通过',
                            cancelButtonText: '取消',
                            type: 'warning',
                            confirmButtonClass: 'el-button--success'
                        }
                    );
                    
                    loading.value = true;
                    
                    // 调用API审核通过
                    await dataManager.approveFirmware(row.serialNumber, currentUser.name);
                    
                    ElMessage.success({
                        message: `固件 "${row.name} ${row.version}" 已审核通过`,
                        duration: 3000
                    });
                    
                    await loadData(); // 刷新数据
                } catch (error) {
                    if (error === 'cancel') {
                        Logger.log('取消审核');
                        return;
                    }
                    Logger.error('审核通过失败:', error);
                    ElMessage.error('审核失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            };
            
            const rejectFirmware = async (row) => {
                try {
                    const { value } = await ElMessageBox.prompt(
                        '请输入拒绝理由：', 
                        '审核拒绝', 
                        {
                            confirmButtonText: '确定拒绝',
                            cancelButtonText: '取消',
                            inputType: 'textarea',
                            inputPlaceholder: '请详细说明拒绝原因...',
                            confirmButtonClass: 'el-button--danger'
                        }
                    );
                    
                    if (!value || value.trim() === '') {
                        ElMessage.warning('请输入拒绝理由');
                        return;
                    }
                    
                    loading.value = true;
                    
                    // 调用API审核拒绝
                    await dataManager.rejectFirmware(row.serialNumber, value, currentUser.name);
                    
                    ElMessage.success({
                        message: `固件 "${row.name} ${row.version}" 已拒绝，拒绝理由：${value}`,
                        duration: 4000
                    });
                    
                    await loadData(); // 刷新数据
                } catch (error) {
                    if (error === 'cancel') {
                        Logger.log('取消拒绝');
                        return;
                    }
                    Logger.error('审核拒绝失败:', error);
                    ElMessage.error('拒绝失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            };
            
            // 导出数据
            const exportData = () => {
                if (window.FirmwareUtils && window.FirmwareUtils.exportToExcel) {
                    const exportData = filteredPendingList.value.map(item => ({
                        'ERP流水号': item.serialNumber,
                        '固件名称': item.name,
                        '软件版本': item.version,
                        '构建日期': item.buildTime || '',
                        '背板总线版本': item.backplaneVersion || '',
                        '高速IO版本': item.ioVersion || '',
                        '类型': item.source,
                        '研发者': item.developer,
                        '适用产品': item.products ? item.products.map(p => p.model).join(', ') : '',
                        '上传时间': item.uploadTime,
                        '上传者': item.uploader,
                        '状态': statusMap[item.status]
                    }));
                    
                    window.FirmwareUtils.exportToExcel(exportData, '待审核固件列表');
                } else {
                    ElMessage.info('正在导出待审核固件数据...');
                }
            };
            
            // 刷新数据
            const refreshData = async () => {
                await loadData();
                ElMessage.success('数据已刷新');
            };
            
            // 生命周期
            onMounted(async () => {
                Logger.log('Pending Firmware page mounted');
                await loadData();
                
                // 监听数据更新事件
                const onDataUpdate = async () => {
                    Logger.log('[Pending Firmware] 收到数据更新事件，重新加载数据');
                    await loadData();
                };
                
                eventListeners = [
                    { event: 'firmware-added', handler: onDataUpdate },
                    { event: 'firmware-updated', handler: onDataUpdate },
                    { event: 'firmware-approved', handler: onDataUpdate },
                    { event: 'firmware-rejected', handler: onDataUpdate },
                    { event: 'version-updated', handler: onDataUpdate }
                ];
                
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.on(event, handler);
                });
            });
            
            onUnmounted(() => {
                // 清理事件监听器
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.off(event, handler);
                });
            });
            
            return {
                // 响应式数据
                searchQuery,
                loading,
                currentUser,
                statusMap,
                sourceMap,
                filteredPendingList,
                paginatedPendingList,
                
                // 分页数据（使用共享分页功能）
                ...pagination,
                
                // 对话框状态
                pendingDetailDialogVisible,
                pendingDetails,
                
                // 方法
                handleSearch,
                handleSearchClear,
                handleSortChange,
                showPendingDetails,
                approveFirmware,
                rejectFirmware,
                exportData,
                refreshData
            };
        },
        
        template: `
        <div class="firmware-container pending-firmware firmware-page">
            <div class="pending-firmware__header">
                <div class="pending-firmware__header-left">
                    <h3 class="pending-firmware__title">待审核固件</h3>
                    <span class="firmware-page__tip pending-firmware__tip">
                        （单击ERP流水号，查看概要）
                    </span>
                </div>
                <div class="pending-firmware__stats">
                    <el-tag type="warning" size="large">
                        待审核: {{ filteredPendingList.length }} 个
                    </el-tag>
                </div>
            </div>
            
            <div class="pending-firmware__search-bar">
                <el-input
                    class="pending-firmware__search-input"
                    placeholder="请输入ERP流水号、固件名称、版本号、研发者或适用产品进行搜索"
                    v-model="searchQuery"
                    clearable
                    @clear="handleSearchClear"
                    @keyup.enter="handleSearch">
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
                
                <div class="pending-firmware__actions">
                    <el-button type="info" @click="refreshData" :loading="loading" class="firmware-page__action-button firmware-page__action-button--info">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                    <el-button type="primary" @click="exportData" class="firmware-page__action-button firmware-page__action-button--primary">
                        <el-icon><Download /></el-icon>
                        导出数据
                    </el-button>
                </div>
            </div>
            
            <div class="pending-firmware__table-container">
                <el-table 
                    :data="paginatedPendingList" 
                    style="width: 100%"
                    border
                    size="small"
                    v-loading="loading"
                    @sort-change="handleSortChange"
                    :default-sort="{prop: 'uploadTime', order: 'descending'}">
                    
                    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                    
                    <el-table-column prop="source" label="类型" width="80" sortable="custom">
                        <template #default="scope">
                            <el-tag :type="sourceMap[scope.row.source]" size="small">
                                {{ scope.row.source }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="oldSerialNumber" label="旧ERP流水号" width="150" sortable="custom" show-overflow-tooltip>
                        <template #default="scope">
                            <span :class="{ 'pending-firmware__no-old-serial': scope.row.oldSerialNumber === '无' }">
                                {{ scope.row.oldSerialNumber }}
                            </span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="serialNumber" label="ERP流水号" width="150" sortable="custom" show-overflow-tooltip>
                        <template #default="scope">
                            <el-link 
                                type="primary"
                                @click="showPendingDetails(scope.row)"
                                class="pending-firmware__serial-link">
                                {{ scope.row.serialNumber }}
                            </el-link>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="name" label="固件名称" min-width="150" sortable="custom" show-overflow-tooltip></el-table-column>
                    
                    <el-table-column prop="version" label="软件版本" width="150" sortable="custom">
                        <template #default="scope">
                            <el-tag type="warning" effect="plain">{{ scope.row.version }}</el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="buildTime" label="构建日期" width="150" sortable="custom" show-overflow-tooltip>
                        <template #default="scope">
                            {{ scope.row.buildTime || '-' }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="backplaneVersion" label="背板总线版本" width="140" sortable="custom" show-overflow-tooltip>
                        <template #default="scope">
                            {{ scope.row.backplaneVersion || '-' }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="ioVersion" label="高速IO版本" width="130" sortable="custom" show-overflow-tooltip>
                        <template #default="scope">
                            {{ scope.row.ioVersion || '-' }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="products" label="适用产品" width="200" sortable="custom" show-overflow-tooltip>
                        <template #default="scope">
                            {{ scope.row.products.map(p => p.model).join(', ') }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="description" label="变更内容" width="200" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="developer" label="研发者" width="100" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="uploadTime" label="上传时间" width="160" sortable="custom"></el-table-column>
                    <el-table-column prop="uploader" label="上传者" width="100" sortable="custom" show-overflow-tooltip></el-table-column>
                    
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="scope">
                            <el-button 
                                size="small" 
                                type="success"
                                @click="approveFirmware(scope.row)"
                                class="pending-firmware__action-button">
                                通过
                            </el-button>
                            <el-button 
                                size="small" 
                                type="danger"
                                @click="rejectFirmware(scope.row)"
                                class="pending-firmware__action-button">
                                拒绝
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页组件（使用共享模板） -->
                <div class="table-footer">
                    <div class="table-length">
                        <div class="page-size-selector">
                            每页
                            <select @change="handleSizeChange($event.target.value)">
                                <option 
                                    v-for="size in pageSizes" 
                                    :key="size" 
                                    :value="size" 
                                    :selected="size === pageSize">
                                    {{ size }}
                                </option>
                            </select>
                            条
                        </div>
                        <div class="total-count">
                            共计 <span>{{ totalCount }}</span> 条
                        </div>
                    </div>
                    <div class="table-pagination">
                        <button 
                            class="btn btn-icon" 
                            @click="goToFirstPage" 
                            :disabled="currentPage === 1">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button 
                            class="btn btn-icon" 
                            @click="goToPrevPage" 
                            :disabled="currentPage === 1">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <div class="pagination-pages">
                            <span v-if="startPage > 1" class="pagination-ellipsis">...</span>
                            <button 
                                v-for="page in visiblePages" 
                                :key="page"
                                class="btn btn-page" 
                                :class="{ active: page === currentPage }"
                                @click="goToPage(page)">
                                {{ page }}
                            </button>
                            <span v-if="endPage < totalPages" class="pagination-ellipsis">...</span>
                        </div>
                        <button 
                            class="btn btn-icon" 
                            @click="goToNextPage" 
                            :disabled="currentPage === totalPages">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button 
                            class="btn btn-icon" 
                            @click="goToLastPage" 
                            :disabled="currentPage === totalPages">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 待审核详情对话框 -->
            <el-dialog
                title="待审核固件详情"
                v-model="pendingDetailDialogVisible"
                width="60%"
                class="pending-firmware__detail-dialog">
                
                <div class="pending-firmware__detail-content">
                    <div class="pending-firmware__detail-header">
                        <el-tag type="warning" size="large" class="pending-firmware__status-tag">
                            待审核
                        </el-tag>
                        <span class="pending-firmware__source">
                            类型：<el-tag size="small" :type="sourceMap[pendingDetails.source]">{{ pendingDetails.source }}</el-tag>
                        </span>
                    </div>
                    
                    <div class="pending-firmware__detail-body">
                        <div class="pending-firmware__detail-left">
                            <div class="pending-firmware__detail-section">
                                <div class="pending-firmware__section-header">基本信息</div>
                                <div class="pending-firmware__section-content">
                                    <div class="pending-firmware__info-item">
                                        <span class="pending-firmware__info-label">ERP流水号：</span>
                                        <span class="pending-firmware__info-value">{{ pendingDetails.serialNumber }}</span>
                                    </div>
                                    <div class="pending-firmware__info-item">
                                        <span class="pending-firmware__info-label">固件名称：</span>
                                        <span class="pending-firmware__info-value">{{ pendingDetails.name }}</span>
                                    </div>
                                    <div class="pending-firmware__info-item">
                                        <span class="pending-firmware__info-label">软件版本：</span>
                                        <span class="pending-firmware__info-value">{{ pendingDetails.version }}</span>
                                    </div>
                                    <div class="pending-firmware__info-item">
                                        <span class="pending-firmware__info-label">构建日期：</span>
                                        <span class="pending-firmware__info-value">{{ pendingDetails.buildTime || '暂无' }}</span>
                                    </div>
                                    <div class="pending-firmware__info-item">
                                        <span class="pending-firmware__info-label">背板总线版本：</span>
                                        <span class="pending-firmware__info-value">{{ pendingDetails.backplaneVersion || '暂无' }}</span>
                                    </div>
                                    <div class="pending-firmware__info-item">
                                        <span class="pending-firmware__info-label">高速IO版本：</span>
                                        <span class="pending-firmware__info-value">{{ pendingDetails.ioVersion || '暂无' }}</span>
                                    </div>
                                    <div class="pending-firmware__info-item">
                                        <span class="pending-firmware__info-label">研发者：</span>
                                        <span class="pending-firmware__info-value">{{ pendingDetails.developer }}</span>
                                    </div>
                                    <div class="pending-firmware__info-item" v-if="pendingDetails.oldSerialNumber && pendingDetails.oldSerialNumber !== '无'">
                                        <span class="pending-firmware__info-label">替换版本：</span>
                                        <span class="pending-firmware__info-value pending-firmware__info-highlight">{{ pendingDetails.oldSerialNumber }}</span>
                                    </div>
                                    <div class="pending-firmware__info-item">
                                        <span class="pending-firmware__info-label">上传者：</span>
                                        <span class="pending-firmware__info-value">{{ pendingDetails.uploader }}</span>
                                    </div>
                                    <div class="pending-firmware__info-item">
                                        <span class="pending-firmware__info-label">上传时间：</span>
                                        <span class="pending-firmware__info-value">{{ pendingDetails.uploadTime }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="pending-firmware__detail-right">
                            <div class="pending-firmware__detail-section">
                                <div class="pending-firmware__section-header">适用产品</div>
                                <div class="pending-firmware__section-content">
                                    <div class="pending-firmware__product-list">
                                        <el-tag 
                                            v-for="product in pendingDetails.products" 
                                            :key="product.code"
                                            class="pending-firmware__product-tag">
                                            {{ product.model }}
                                        </el-tag>
                                        <span v-if="!pendingDetails.products.length" class="pending-firmware__empty-text">暂无产品信息</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="pending-firmware__detail-section">
                                <div class="pending-firmware__section-header">版本使用要求</div>
                                <div class="pending-firmware__section-content">
                                    <pre class="pending-firmware__requirements">{{ pendingDetails.versionRequirements || '暂无版本使用要求信息' }}</pre>
                                </div>
                            </div>
                            
                            <div class="pending-firmware__detail-section">
                                <div class="pending-firmware__section-header">变更内容</div>
                                <div class="pending-firmware__section-content">
                                    <div class="pending-firmware__description">{{ pendingDetails.description || '暂无变更内容描述' }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <template #footer>
                    <div class="pending-firmware__detail-footer">
                        <el-button @click="pendingDetailDialogVisible = false">关闭</el-button>
                        <el-button 
                            type="danger"
                            @click="rejectFirmware(pendingDetails); pendingDetailDialogVisible = false">
                            拒绝审核
                        </el-button>
                        <el-button 
                            type="success"
                            @click="approveFirmware(pendingDetails); pendingDetailDialogVisible = false">
                            通过审核
                        </el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
        `
    };

    const app = createApp(PendingFirmwareApp);
        
    // 注册Element Plus图标 - 添加安全检查
    if (window.ElementPlusIconsVue && typeof window.ElementPlusIconsVue === 'object') {
        for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
            app.component(key, component);
        }
    }
    
    app.use(ElementPlus);

    const mountApp = () => {
        try {
            app.mount('#pending-firmware-app-container');
            Logger.log('Pending Firmware Vue app mounted successfully');
            
            // 保存应用实例供后续使用
            window.currentFirmwareApp = app;
        } catch (error) {
            Logger.error('Failed to mount Pending Firmware Vue app:', error);
        }
    };

    // Defer mounting until the DOM is fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', mountApp);
    } else {
        mountApp();
    }
})(); 