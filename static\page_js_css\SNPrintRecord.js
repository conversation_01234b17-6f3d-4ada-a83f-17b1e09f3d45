// 使用IIFE创建模块作用域
(function() {
    // 状态变量
    let snList = [];
    let currentWeek = '';
    let scannedSNs = new Set(); // 存储已扫描的SN
    let recentScans = []; // 存储最近的扫描记录
    const MAX_RECENT_SCANS = 5; // 最多显示的最近扫描记录数
    
    // **新增：语音播报函数**
    function speakText(text) {
        try {
            // 检查是否启用语音和浏览器支持
            const voiceEnabled = localStorage.getItem('snPrintRecord_voiceEnabled') !== 'false'; // 默认开启
            if (!voiceEnabled || !('speechSynthesis' in window)) {
                return;
            }
            
            // 停止当前播放的语音（避免重叠）
            window.speechSynthesis.cancel();
            
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN'; // 设置中文语音
            utterance.rate = 1.2; // 语速稍快
            utterance.volume = 0.8; // 音量
            utterance.pitch = 1.0; // 音调
            
            window.speechSynthesis.speak(utterance);
        } catch (error) {
            Logger.warn('语音播报失败:', error);
            // 静默失败，不影响主要功能
        }
    }
    
    // **新增：切换语音开关函数**
    function toggleVoice() {
        const currentState = localStorage.getItem('snPrintRecord_voiceEnabled') !== 'false';
        const newState = !currentState;
        localStorage.setItem('snPrintRecord_voiceEnabled', newState.toString());
        
        // 更新按钮状态
        updateVoiceButtonStatus();
        
        // 播放测试音
        if (newState) {
            speakText('语音提示已开启');
        }
    }
    
    // **新增：更新语音按钮状态**
    function updateVoiceButtonStatus() {
        const voiceBtn = document.getElementById('toggle-voice-btn');
        const voiceEnabled = localStorage.getItem('snPrintRecord_voiceEnabled') !== 'false';
        
        if (voiceBtn) {
            voiceBtn.innerHTML = voiceEnabled 
                ? '<i class="fas fa-volume-up"></i> 语音已开启' 
                : '<i class="fas fa-volume-mute"></i> 语音已关闭';
            voiceBtn.className = voiceEnabled 
                ? 'sn-print-record__btn sn-print-record__btn--info'
                : 'sn-print-record__btn sn-print-record__btn--secondary';
        }
    }
    
    // 添加SN生成相关的状态管理
    window.snPrintRecordState = window.snPrintRecordState || {};
    window.snPrintRecordState = {
        ...window.snPrintRecordState,
        nextSequence: 1,
        currentProductCode: '',
        currentOrderNumber: '',
        currentYear: '',
        currentWeek: ''
    };
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 新的异步函数：根据加工单号获取工单详情
    async function fetchWorkOrderDetails(processOrderNo) {
        const productModelInput = document.getElementById('product-model');
        const productCodeInput = document.getElementById('product-code');
        const productionQuantityInput = document.getElementById('production-quantity');
        const snLengthInput = document.getElementById('sn-length'); // 获取SN号位数输入框
        const orderNumberInput = document.getElementById('order-number'); // 获取订货号输入框

        if (!processOrderNo) {
            if (productModelInput) productModelInput.value = '';
            if (productCodeInput) productCodeInput.value = '';
            if (productionQuantityInput) productionQuantityInput.value = '';
            if (snLengthInput) snLengthInput.value = ''; // 清空输入框，不设置默认值
            if (orderNumberInput) orderNumberInput.value = ''; // 清空订货号输入框
            return;
        }

        try {
            const response = await fetch(`/api/sn-print-record/get-work-order-details?processOrder=${encodeURIComponent(processOrderNo)}`);
            const data = await response.json();

            if (data.success && data.order) {
                if (productModelInput) productModelInput.value = data.order.ord_productModel || '';
                if (productCodeInput) productCodeInput.value = data.order.ord_productCode || '';
                if (productionQuantityInput) productionQuantityInput.value = data.order.ord_productionQuantity || '';
                
                // 填充SN号位数，与其他字段处理方式一致
                if (snLengthInput) {
                    snLengthInput.value = data.order.ord_snlenth || '';
                }
                
                // 处理产品类型ID
                if (data.order.ord_product_type_id) {
                    window.snPrintRecordState = window.snPrintRecordState || {};
                    window.snPrintRecordState.productTypeId = data.order.ord_product_type_id;
                }
                
                // 获取订货号
                const productCode = data.order.ord_productCode;
                if (productCode && orderNumberInput) {
                    try {
                        // 调用API获取订货号
                        const orderResponse = await fetch(`/api/product-by-code?code=${encodeURIComponent(productCode)}`);
                        const orderData = await orderResponse.json();
                        
                        if (orderData.success && orderData.product && orderData.product.orderNumber) {
                            orderNumberInput.value = orderData.product.orderNumber;
                            
                            // 所有基本信息都已填充后，自动生成首件SN号
                            setTimeout(() => {
                                updateFirstSN(); // 自动调用更新首件SN号函数
                            }, 300); // 延迟一小段时间确保所有值都已更新
                            
                        } else {
                            orderNumberInput.value = '';
                            Logger.warn('未找到产品订货号:', orderData.message);
                        }
                    } catch (orderError) {
                        Logger.error('获取订货号失败:', orderError);
                        orderNumberInput.value = '';
                    }
                }
                
            } else {
                if (productModelInput) productModelInput.value = '';
                if (productCodeInput) productCodeInput.value = '';
                if (productionQuantityInput) productionQuantityInput.value = '';
                if (snLengthInput) snLengthInput.value = ''; // 清空输入框，不设置默认值
                if (orderNumberInput) orderNumberInput.value = ''; // 清空订货号输入框
                
                if (data.message && !data.success) {
                    SweetAlert.warning(data.message);
                }
            }
        } catch (error) {
            Logger.error('调用获取工单详情API时出错:', error);
            if (productModelInput) productModelInput.value = '';
            if (productCodeInput) productCodeInput.value = '';
            if (productionQuantityInput) productionQuantityInput.value = '';
            if (snLengthInput) snLengthInput.value = ''; // 清空输入框，不设置默认值
            if (orderNumberInput) orderNumberInput.value = ''; // 清空订货号输入框
            
            SweetAlert.error('查询工单信息失败，请检查网络或联系管理员。');
        }
    }
    
    // 初始化SN打印记录页面
    async function initSNPrintRecordPage() {
        const content = document.getElementById('content');
        content.innerHTML = createSNPrintRecordHTML();
        
        // 初始化事件监听
        initEventListeners();
        
        // 设置当前日期
        setCurrentDate();

        // 自动获取并填充操作人员
        try {
            const response = await fetch('/api/sn-print-record/get-current-user');
            const data = await response.json();
            const operatorInput = document.getElementById('operator');
            if (operatorInput) {
                if (data.success && data.username) {
                    operatorInput.value = data.username;
                    operatorInput.readOnly = true; // 设置为只读
                } else {
                    Logger.warn('获取当前用户信息失败:', data.message);
                    operatorInput.value = ''; // 获取失败时清空
                    operatorInput.readOnly = false; // 允许用户手动输入以防万一
                    SweetAlert.warning(data.message || '无法获取用户信息，请尝试重新登录或手动输入操作员。');
                }
            }
        } catch (error) {
            Logger.error('调用获取用户信息API时出错:', error);
            const operatorInput = document.getElementById('operator');
            if (operatorInput) {
                operatorInput.value = '';
                operatorInput.readOnly = false;
            }
            SweetAlert.error('获取用户信息失败，请检查网络或联系管理员。');
        }
        
        // **新增：初始化语音按钮状态**
        setTimeout(() => {
            updateVoiceButtonStatus();
        }, 100);
    }
    
    // 创建页面HTML结构
    function createSNPrintRecordHTML() {
        return `
            <section class="sn-print-record">
                <!-- 水平布局容器 -->
                <div class="sn-print-record__horizontal-layout">
                    <!-- 基本信息区域 -->
                    <div class="sn-print-record__card sn-print-record__card--left">
                        <div class="sn-print-record__card-header">
                            <i class="fas fa-info-circle"></i>
                            <h2>基本信息</h2>
                        </div>
                        
                        <form class="sn-print-record__form">
                            <div class="sn-print-record__row">
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="operator">操作人员</label>
                                    <input type="text" id="operator" class="sn-print-record__input" placeholder="请输入操作人员姓名">
                                </div>
                                
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="process-order">加工单号</label>
                                    <input type="text" id="process-order" class="sn-print-record__input" placeholder="请输入加工单号">
                                </div>
                            </div>
                            
                            <div class="sn-print-record__row">
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="product-model">产品型号</label>
                                    <input type="text" id="product-model" class="sn-print-record__input" placeholder="请输入产品型号">
                                </div>
                                
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="product-code">产品编码</label>
                                    <input type="text" id="product-code" class="sn-print-record__input" placeholder="请输入产品编码">
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- SN配置区域 -->
                    <div class="sn-print-record__card sn-print-record__card--right">
                        <div class="sn-print-record__card-header">
                            <i class="fas fa-cog"></i>
                            <h2>SN配置</h2>
                        </div>
                        
                        <form class="sn-print-record__form">
                            <div class="sn-print-record__row">
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="order-number">订货号</label>
                                    <input type="text" id="order-number" class="sn-print-record__input" placeholder="请输入订货号">
                                </div>
                                
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="sn-length">产品SN号位数</label>
                                    <input type="number" id="sn-length" class="sn-print-record__input" placeholder="请输入SN号位数">
                                </div>
                                
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="sequence-length">流水号长度</label>
                                    <input type="number" id="sequence-length" class="sn-print-record__input" placeholder="请输入流水号长度" min="1" value="4">
                                    <div class="sn-print-record__input-hint">默认为4位数字</div>
                                </div>
                            </div>
                            
                            <div class="sn-print-record__row">
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="production-quantity">生产数量</label>
                                    <input type="number" id="production-quantity" class="sn-print-record__input" placeholder="请输入生产数量" min="1">
                                </div>
                                
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="date-code">日期 (YYMMDD)</label>
                                    <input type="text" id="date-code" class="sn-print-record__input" placeholder="如：250429" maxlength="6">
                                    <div class="sn-print-record__input-hint" id="date-hint"></div>
                                </div>
                                
                                <div class="sn-print-record__form-group">
                                    <label class="sn-print-record__label" for="week-number">周期</label>
                                    <input type="text" id="week-number" class="sn-print-record__input" placeholder="根据日期自动计算周期">
                                    <div class="sn-print-record__input-hint" id="week-hint">基于ISO 8601标准计算</div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- 操作按钮区域 -->
                <div class="sn-print-record__actions-container">
                    <div class="sn-print-record__actions">
                        <div class="sn-print-record__first-sn-wrapper">
                            <div class="sn-print-record__first-sn-row">
                                <label class="sn-print-record__label" for="first-sn">首件SN</label>
                                <input type="text" id="first-sn" class="sn-print-record__input sn-print-record__input--highlight" placeholder="基于配置生成或手动输入">
                            </div>
                        </div>
                        
                        <div class="sn-print-record__buttons-wrapper">
                            <button type="button" id="generate-sn-btn" class="sn-print-record__btn sn-print-record__btn--primary">
                                <i class="fas fa-cog"></i> 生成序列号
                            </button>
                            <button type="button" id="reset-form-btn" class="sn-print-record__btn sn-print-record__btn--secondary">
                                <i class="fas fa-redo"></i> 重置表单
                            </button>
                            <button type="button" id="export-excel-btn" class="sn-print-record__btn sn-print-record__btn--success">
                                <i class="fas fa-file-excel"></i> 导出Excel
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 序列号和扫描验证区域 - 两栏布局 -->
                <div class="sn-print-record__twin-layout" id="sn-verification-layout" style="display: none;">
                    <!-- 左侧：SN号列表 -->
                    <div class="sn-print-record__card sn-print-record__card--list">
                        <div class="sn-print-record__card-header">
                            <i class="fas fa-list-ol"></i>
                            <h2>SN号列表</h2>
                            <span class="sn-print-record__count">共计 <span id="sn-count">0</span> 个序列号</span>
                        </div>
                        
                        <div class="sn-print-record__table-container">
                            <table class="sn-print-record__table">
                                <thead>
                                    <tr>
                                        <th width="15%">序号</th>
                                        <th width="70%">序列号</th>
                                        <th width="15%">状态</th>
                                    </tr>
                                </thead>
                                <tbody id="sn-list-body">
                                    <!-- 序列号列表会动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 右侧：扫描验证区域 -->
                    <div class="sn-print-record__card sn-print-record__card--verify">
                        <div class="sn-print-record__card-header">
                            <i class="fas fa-barcode"></i>
                            <h2>扫描验证</h2>
                            <span class="sn-print-record__status">
                                已扫描: <span id="scanned-count">0</span> / <span id="total-count">0</span>
                            </span>
                        </div>
                        
                        <div class="sn-print-record__verify-container">
                            <!-- 扫描输入框 -->
                            <div class="sn-print-record__scan-input-wrapper">
                                <div class="sn-print-record__scan-input-group">
                                    <input type="text" id="scan-input" class="sn-print-record__input sn-print-record__input--scan" placeholder="请扫描或输入SN号" autofocus>
                                    <button type="button" id="manual-verify-btn" class="sn-print-record__btn sn-print-record__btn--primary">
                                        <i class="fas fa-check"></i> 验证
                                    </button>
                                </div>
                                <div id="scan-status" class="sn-print-record__scan-status">
                                    准备就绪，请开始扫描...
                                </div>
                            </div>
                            
                            <!-- 验证进度 -->
                            <div class="sn-print-record__progress-container">
                                <div class="sn-print-record__progress-label">验证进度:</div>
                                <div class="sn-print-record__progress-bar-container">
                                    <div id="progress-bar" class="sn-print-record__progress-bar" style="width: 0%;">0%</div>
                                </div>
                            </div>
                            
                            <!-- 最近验证记录 -->
                            <div class="sn-print-record__recent-scans">
                                <h3>最近扫描记录</h3>
                                <div id="recent-scans-list" class="sn-print-record__recent-list">
                                    <!-- 最近扫描记录会动态生成 -->
                                    <div class="sn-print-record__empty-message">尚无扫描记录</div>
                                </div>
                            </div>
                            
                            <!-- 提交按钮 -->
                            <div class="sn-print-record__verify-actions">
                                <button type="button" id="toggle-voice-btn" class="sn-print-record__btn sn-print-record__btn--info">
                                    <i class="fas fa-volume-up"></i> 语音已开启
                                </button>
                                <button type="button" id="reset-scan-btn" class="sn-print-record__btn sn-print-record__btn--secondary">
                                    <i class="fas fa-undo"></i> 重置扫描
                                </button>
                                <button type="button" id="complete-verify-btn" class="sn-print-record__btn sn-print-record__btn--success" disabled>
                                    <i class="fas fa-check-circle"></i> 完成验证
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 原来的序列号列表（现在隐藏，被上面的双栏布局替代） -->
                <div class="sn-print-record__card" id="sn-list-card" style="display: none;">
                    <div class="sn-print-record__card-header">
                        <i class="fas fa-list-ol"></i>
                        <h2>SN号列表</h2>
                        <span class="sn-print-record__count">共计 <span id="sn-list-count">0</span> 个序列号</span>
                    </div>
                    
                    <div class="sn-print-record__table-container">
                        <table class="sn-print-record__table">
                            <thead>
                                <tr>
                                    <th width="15%">序号</th>
                                    <th width="85%">序列号</th>
                                </tr>
                            </thead>
                            <tbody id="sn-list-body-old">
                                <!-- 序列号列表会动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        `;
    }
    
    // 初始化事件监听
    function initEventListeners() {
        // 日期输入变化时计算周期
        const dateInput = document.getElementById('date-code');
        if (dateInput) {
            dateInput.addEventListener('input', calculateWeekNumber);
        }
        
        // 生成序列号按钮
        const generateBtn = document.getElementById('generate-sn-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', handleGenerateSN);
        }
        
        // 重置表单按钮
        const resetBtn = document.getElementById('reset-form-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', resetForm);
        }
        
        // 导出Excel按钮
        const exportBtn = document.getElementById('export-excel-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', exportToExcel);
        }
        
        // 监听表单变化，用于生成首件SN预览
        const formInputs = document.querySelectorAll('.sn-print-record__input');
        formInputs.forEach(input => {
            if (input.id !== 'first-sn') { // 排除首件SN输入框，允许手动编辑
                input.addEventListener('input', updateFirstSN);
            }
        });
        
        // **新增：监听订货号变化，动态添加特殊格式样式**
        const orderNumberInput = document.getElementById('order-number');
        if (orderNumberInput) {
            orderNumberInput.addEventListener('input', function() {
                const firstSNInput = document.getElementById('first-sn');
                const firstSNWrapper = firstSNInput?.closest('.sn-print-record__first-sn-wrapper');
                
                if (this.value.trim().includes('#')) {
                    // 添加特殊格式样式
                    if (firstSNInput) {
                        firstSNInput.classList.add('sn-print-record__input--special-format');
                        firstSNInput.placeholder = `请手动输入首件SN（格式如：${this.value.trim()}2522#0001）`;
                        
                        // 添加特殊格式提示
                        let hintElement = document.querySelector('.sn-print-record__special-format-hint');
                        if (!hintElement) {
                            hintElement = document.createElement('div');
                            hintElement.className = 'sn-print-record__special-format-hint';
                            hintElement.textContent = '检测到特殊格式订货号，请手动输入完整的首件SN';
                            if (firstSNWrapper) {
                                firstSNWrapper.appendChild(hintElement);
                            }
                        }
                    }
                } else {
                    // 移除特殊格式样式
                    if (firstSNInput) {
                        firstSNInput.classList.remove('sn-print-record__input--special-format');
                        firstSNInput.placeholder = '基于配置生成或手动输入';
                        
                        // 移除特殊格式提示
                        const hintElement = document.querySelector('.sn-print-record__special-format-hint');
                        if (hintElement) {
                            hintElement.remove();
                        }
                    }
                }
            });
        }
        
        // 为首件SN输入框添加回车键处理
        const firstSNInput = document.getElementById('first-sn');
        if (firstSNInput) {
            firstSNInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    // 验证手动输入的SN号
                    const isValid = validateManualSN();
                    if (isValid === true) { // 只有当返回true时才生成序列号，如果是Promise则由Promise处理
                        handleGenerateSN();
                    }
                }
            });
        }
        
        // 扫描输入框的回车事件监听
        const scanInput = document.getElementById('scan-input');
        if (scanInput) {
            scanInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const snValue = scanInput.value.trim();
                    verifySN(snValue).then(() => {
                        scanInput.value = '';
                        scanInput.focus();
                    });
                }
            });
        }
        
        // 手动验证按钮
        const manualVerifyBtn = document.getElementById('manual-verify-btn');
        if (manualVerifyBtn) {
            manualVerifyBtn.addEventListener('click', function() {
                const scanInput = document.getElementById('scan-input');
                if (scanInput) {
                    const snValue = scanInput.value.trim();
                    verifySN(snValue).then(() => {
                        scanInput.value = '';
                        scanInput.focus();
                    });
                }
            });
        }
        
        // 重置扫描按钮
        const resetScanBtn = document.getElementById('reset-scan-btn');
        if (resetScanBtn) {
            resetScanBtn.addEventListener('click', resetScanVerification);
        }
        
        // 完成验证按钮
        const completeVerifyBtn = document.getElementById('complete-verify-btn');
        if (completeVerifyBtn) {
            completeVerifyBtn.addEventListener('click', completeVerification);
        }
        
        // **新增：语音切换按钮事件监听**
        const toggleVoiceBtn = document.getElementById('toggle-voice-btn');
        if (toggleVoiceBtn) {
            toggleVoiceBtn.addEventListener('click', toggleVoice);
        }

        // 加工单号输入事件监听 (联动获取产品信息)
        const processOrderInput = document.getElementById('process-order');
        if (processOrderInput) {
            // 防抖包装 fetchWorkOrderDetails 函数
            const debouncedFetchDetails = debounce(fetchWorkOrderDetails, 500);
            
            processOrderInput.addEventListener('input', function() {
                // 'this' 在这里正确指向 processOrderInput 元素
                const processOrderNo = this.value.trim();
                debouncedFetchDetails(processOrderNo); // 将获取到的值传递给防抖函数
            });
            
            // 添加回车键事件，回车时聚焦到首件SN输入框
            processOrderInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    // 等待工单信息加载完成后，自动聚焦到首件SN输入框
                    setTimeout(() => {
                        const firstSNInput = document.getElementById('first-sn');
                        if (firstSNInput) {
                            // 选中首件SN的全部内容，方便编辑
                            firstSNInput.select();
                            firstSNInput.focus();
                        }
                    }, 800); // 给fetchWorkOrderDetails一些时间来完成
                }
            });
        }
    }
    
    // 设置当前日期（YYMMDD格式）
    function setCurrentDate() {
        const dateInput = document.getElementById('date-code');
        if (!dateInput) return;
        
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        
        const formattedDate = year + month + day;
        dateInput.value = formattedDate;
        
        // 计算当前周期
        calculateWeekNumber();
    }
    
    // 计算周期（ISO 8601标准）
    function calculateWeekNumber() {
        const dateInput = document.getElementById('date-code');
        const weekInput = document.getElementById('week-number');
        const weekHint = document.getElementById('week-hint');
        const dateHint = document.getElementById('date-hint');
        
        if (!dateInput || !weekInput) return;
        
        const dateValue = dateInput.value.trim();
        
        // 清除之前的提示
        if (dateHint) dateHint.textContent = '';
        if (weekHint) weekHint.textContent = '基于ISO 8601标准计算';
        
        // 验证日期格式
        if (dateValue.length !== 6 || !/^\d{6}$/.test(dateValue)) {
            if (dateHint) dateHint.textContent = '请输入6位数字日期（YYMMDD）';
            weekInput.value = '';
            window.systemWeek = ''; // 清除系统周期
            return;
        }
        
        // 解析日期
        const year = parseInt('20' + dateValue.substr(0, 2), 10);
        const month = parseInt(dateValue.substr(2, 2), 10) - 1; // 月份从0开始
        const day = parseInt(dateValue.substr(4, 2), 10);
        
        // 验证日期有效性
        const date = new Date(year, month, day);
        if (date.getFullYear() !== year || date.getMonth() !== month || date.getDate() !== day) {
            if (dateHint) dateHint.textContent = '无效日期';
            weekInput.value = '';
            window.systemWeek = ''; // 清除系统周期
            return;
        }
        
        // 计算ISO周数
        const isoWeek = getISOWeek(date);
        currentWeek = isoWeek.toString().padStart(2, '0');
        window.systemWeek = currentWeek; // 保存系统计算的周期，供全局使用
        weekInput.value = currentWeek;
        
        // 在成功计算周期后添加
        if (weekInput.value && weekInput.value !== '') {
            updateFirstSN();
        }
    }
    
    // 获取ISO 8601标准周数
    function getISOWeek(date) {
        const target = new Date(date.valueOf());
        const dayNumber = (date.getDay() + 6) % 7; // 将星期天视为6，星期一视为0
        target.setDate(target.getDate() - dayNumber + 3); // 将日期调整到当周的星期四
        const firstThursday = target.valueOf(); // 当周的星期四
        target.setMonth(0, 1); // 1月1日
        
        // 如果1月1日不是星期四，则设置为当年的第一个星期四
        if (target.getDay() !== 4) {
            target.setMonth(0, 1 + ((4 - target.getDay()) + 7) % 7);
        }
        
        // 计算周数
        return 1 + Math.ceil((firstThursday - target) / 604800000); // 604800000 = 7 * 24 * 60 * 60 * 1000
    }
    
    // 更新首件SN预览
    async function updateFirstSN() {
        const orderNumber = document.getElementById('order-number').value.trim();
        const dateValue = document.getElementById('date-code').value.trim();
        const weekValue = document.getElementById('week-number').value.trim();
        const firstSNInput = document.getElementById('first-sn');
        const sequenceLengthInput = document.getElementById('sequence-length');
        const productCodeInput = document.getElementById('product-code');
        
        if (!orderNumber || !dateValue || !weekValue || !firstSNInput || !sequenceLengthInput || !productCodeInput) {
            if (firstSNInput) firstSNInput.value = '';
            return;
        }
        
        // **新增：如果订货号包含#，跳过自动生成，需要用户手动输入**
        if (orderNumber.includes('#')) {
            if (firstSNInput && firstSNInput.value.trim() === '') {
                firstSNInput.placeholder = `请手动输入首件SN（格式如：${orderNumber}2522#0001）`;
            }
            return; // 不进行自动生成
        }
        
        const productCode = productCodeInput.value.trim();
        const year = dateValue.substr(0, 2);
        const sequenceLength = parseInt(sequenceLengthInput.value.trim(), 10) || 4;

        // 检查是否需要重新获取流水号
        const shouldFetchNewSequence = 
            productCode !== window.snPrintRecordState.currentProductCode ||
            year !== window.snPrintRecordState.currentYear ||
            weekValue !== window.snPrintRecordState.currentWeek ||
            orderNumber !== window.snPrintRecordState.currentOrderNumber;

        if (shouldFetchNewSequence) {
            // 更新状态
            window.snPrintRecordState.currentProductCode = productCode;
            window.snPrintRecordState.currentYear = year;
            window.snPrintRecordState.currentWeek = weekValue;
            window.snPrintRecordState.currentOrderNumber = orderNumber;
            
            // 获取新的流水号
            window.snPrintRecordState.nextSequence = await fetchNextSequence(
                productCode,
                year,
                weekValue,
                orderNumber
            );
        }

        // 生成首件SN
        const nextSequence = window.snPrintRecordState.nextSequence;
        const firstSN = orderNumber + year + weekValue + nextSequence.toString().padStart(sequenceLength, '0');
        firstSNInput.value = firstSN;
    }
    
    // 添加获取下一个可用流水号的函数
    async function fetchNextSequence(productCode, year, week, orderNumber) {
        if (!productCode || !year || !week || !orderNumber) {
            Logger.warn('获取下一流水号缺少必要参数');
            return 1; // 默认从1开始
        }

        try {
            const response = await fetch(
                `/api/sn-print-record/get-next-sequence?` +
                `productCode=${encodeURIComponent(productCode)}&` +
                `year=${encodeURIComponent(year)}&` +
                `week=${encodeURIComponent(week)}&` +
                `orderNumber=${encodeURIComponent(orderNumber)}`
            );
            
            const data = await response.json();
            if (data.success) {
                // 验证返回的前缀是否匹配预期
                const expectedPrefix = orderNumber + year + week;
                if (data.snPrefix !== expectedPrefix) {
                    Logger.warn('SN前缀不匹配，使用默认值1');
                    return 1;
                }
                return data.nextSequence;
            } else {
                Logger.warn('获取下一流水号失败:', data.message);
                return 1;
            }
        } catch (error) {
            Logger.error('调用获取下一流水号API时出错:', error);
            return 1;
        }
    }
    
    // 验证手动输入的SN号
    function validateManualSN() {
        const firstSNInput = document.getElementById('first-sn');
        const orderNumberInput = document.getElementById('order-number');
        const weekNumberInput = document.getElementById('week-number');
        const snLengthInput = document.getElementById('sn-length');
        const dateInput = document.getElementById('date-code');
        
        if (!firstSNInput || !orderNumberInput || !weekNumberInput || !snLengthInput || !dateInput) {
            return false;
        }
        
        const manualSN = firstSNInput.value.trim();
        const orderNumber = orderNumberInput.value.trim();
        const snLength = parseInt(snLengthInput.value.trim(), 10) || 0;
        
        // 如果首件SN为空，自动生成
        if (!manualSN) {
            updateFirstSN();
            return true;
        }
        
        // 检查SN长度是否与配置的SN长度一致
        if (snLength > 0 && manualSN.length !== snLength) {
            Swal.fire({
                title: 'SN号长度错误',
                text: `手动输入的SN号长度(${manualSN.length})与配置的SN长度(${snLength})不一致`,
                icon: 'error',
                confirmButtonText: '确定'
            });
            return false;
        }
        
        // **新增：处理包含#的订货号的特殊逻辑**
        if (orderNumber.includes('#')) {
            // 对于包含#的订货号，验证SN是否以订货号开头
            if (!manualSN.startsWith(orderNumber)) {
                Swal.fire({
                    title: 'SN号格式错误',
                    text: `手动输入的SN号必须以订货号(${orderNumber})开头`,
                    icon: 'error',
                    confirmButtonText: '确定'
                });
                return false;
            }
            
            // 对于特殊格式，不需要进行年份和周期的验证，直接通过
            Logger.log(`特殊订货号格式(${orderNumber})，跳过年份和周期验证`);
            return true;
        }
        
        // **以下是原有的常规验证逻辑**
        // 检查SN号前缀是否为订货号
        if (!manualSN.startsWith(orderNumber)) {
            Swal.fire({
                title: 'SN号格式错误',
                text: `手动输入的SN号必须以订货号(${orderNumber})开头`,
                icon: 'error',
                confirmButtonText: '确定'
            });
            return false;
        }
        
        // 从手动输入的SN中提取年份和周期
        const orderLen = orderNumber.length;
        const extractedYear = manualSN.substr(orderLen, 2); // 订货号后面的2位是年份
        const extractedWeek = manualSN.substr(orderLen + 2, 2); // 年份后面的2位是周期
        
        // 获取当前设置的年份和周期
        const currentYear = dateInput.value.substr(0, 2); 
        const currentWeek = weekNumberInput.value;
        
        // 如果提取的年份与当前设置的年份不一致，更新日期输入
        if (extractedYear !== currentYear) {
            const monthDay = dateInput.value.substr(2, 4); // 保留月日部分
            dateInput.value = extractedYear + monthDay;
            Logger.log(`根据首件SN更新年份为: ${extractedYear}`);
        }
        
        // 如果提取的周期与当前设置的周期不一致，更新周期输入
        if (extractedWeek !== currentWeek) {
            // 更新周期输入框的值
            weekNumberInput.value = extractedWeek;
            Logger.log(`根据首件SN更新周期为: ${extractedWeek}`);
        }
        
        // 获取系统计算的周期
        let systemWeek = window.systemWeek;
        
        // 如果系统周期未设置，重新计算一次
        if (!systemWeek) {
            const today = new Date();
            const isoWeek = getISOWeek(today);
            systemWeek = isoWeek.toString().padStart(2, '0');
            window.systemWeek = systemWeek; // 保存起来
        }
        
        // 如果设置的周期与系统标准周期不一致，显示警告
        if (extractedWeek !== systemWeek) {
            return new Promise((resolve) => {
                Swal.fire({
                    title: '周期不匹配',
                    text: `SN号中的周期(${extractedWeek})与系统计算的当前周期(${systemWeek})不一致，请确认是否继续使用`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: '继续使用',
                    cancelButtonText: '使用系统周期'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // 用户确认使用不匹配的周期，保持原值
                        resolve(true);
                    } else {
                        // 用户选择使用系统周期
                        weekNumberInput.value = systemWeek;
                        // 重新生成SN，使用系统周期
                        updateFirstSN();
                        resolve(false);
                    }
                });
            });
        }
        
        // 通过所有验证
        return true;
    }
    
    // 处理生成序列号按钮点击
    function handleGenerateSN() {
        // 验证表单
        if (!validateForm()) {
            return;
        }
        
        // 验证手动输入的SN号，它可能返回Promise或布尔值
        const validationResult = validateManualSN();
        
        // 处理验证结果
        if (validationResult instanceof Promise) {
            // 如果是Promise，等待用户确认结果
            validationResult.then(isValid => {
                if (isValid) {
                    generateSNList(); // 用户确认后生成序列号
                }
            });
        } else if (validationResult) {
            // 如果是布尔值且为true，直接生成序列号
            generateSNList();
        }
    }
    
    // 生成序列号列表（从handleGenerateSN中拆分出来的逻辑）
    function generateSNList() {
        // 获取表单数据
        const orderNumber = document.getElementById('order-number').value.trim();
        const quantityInput = document.getElementById('production-quantity');
        const quantity = parseInt(quantityInput.value.trim(), 10);
        const sequenceLengthInput = document.getElementById('sequence-length');
        const sequenceLength = parseInt(sequenceLengthInput.value.trim(), 10) || 4;
        
        // 清空之前的SN列表和扫描记录
        snList = [];
        resetScanVerification();
        
        // 获取手动输入的首件SN
        const firstSNInput = document.getElementById('first-sn');
        const firstSN = firstSNInput.value.trim();
        
        // 检查首件SN是否已设置
        if (!firstSN) {
            Swal.fire({
                title: '首件SN未设置',
                text: '请先设置首件SN',
                icon: 'error',
                confirmButtonText: '确定'
            });
            return;
        }
        
        // **新增：处理包含#的订货号的特殊逻辑**
        if (orderNumber.includes('#')) {
            // 找到最后一个#的位置，从该位置开始提取流水号
            const lastHashIndex = firstSN.lastIndexOf('#');
            if (lastHashIndex === -1) {
                Swal.fire({
                    title: 'SN格式错误',
                    text: '特殊订货号格式的SN必须包含#分隔符',
                    icon: 'error',
                    confirmButtonText: '确定'
                });
                return;
            }
            
            // 提取SN前缀（包含最后一个#）和流水号部分
            const snPrefix = firstSN.substring(0, lastHashIndex + 1); // 包含最后一个#
            const sequenceStr = firstSN.substring(lastHashIndex + 1); // #后面的流水号部分
            
            // 验证流水号是否为数字
            const sequenceStart = parseInt(sequenceStr, 10);
            if (isNaN(sequenceStart)) {
                Swal.fire({
                    title: 'SN格式错误',
                    text: '流水号部分必须是数字',
                    icon: 'error',
                    confirmButtonText: '确定'
                });
                return;
            }
            
            // 生成特殊格式的序列号数组
            for (let i = 0; i < quantity; i++) {
                const sequenceNumber = (sequenceStart + i).toString().padStart(sequenceStr.length, '0');
                const sn = snPrefix + sequenceNumber;
                snList.push(sn);
            }
            
            Logger.log(`特殊格式SN生成完成，前缀: ${snPrefix}，起始流水号: ${sequenceStart}，数量: ${quantity}`);
        } else {
            // **以下是原有的常规生成逻辑**
            // 从首件SN中提取组成部分
            const orderLen = orderNumber.length;
            const extractedYear = firstSN.substr(orderLen, 2);
            const extractedWeek = firstSN.substr(orderLen + 2, 2);
            
            // 使用首件SN中的年份和周期
            const snPrefix = orderNumber + extractedYear + extractedWeek;
            
            // 提取首件SN中的序列号部分
            const sequenceStart = parseInt(firstSN.substring(snPrefix.length), 10) || 1;
            
            // 生成序列号数组
            for (let i = 0; i < quantity; i++) {
                const sequenceNumber = (sequenceStart + i).toString().padStart(sequenceLength, '0');
                const sn = snPrefix + sequenceNumber; // 使用从首件SN提取的前缀
                snList.push(sn);
            }
        }
        
        // 显示序列号列表
        displaySNList();
        
        // 显示成功提示
        Swal.fire({
            title: '生成成功',
            text: `成功生成 ${quantity} 个序列号`,
            icon: 'success',
            confirmButtonText: '确定'
        });
    }
    
    // 验证表单
    function validateForm() {
        // 获取必填字段
        const operator = document.getElementById('operator').value.trim();
        const processOrder = document.getElementById('process-order').value.trim();
        const productModel = document.getElementById('product-model').value.trim();
        const productCode = document.getElementById('product-code').value.trim();
        const quantity = document.getElementById('production-quantity').value.trim();
        const orderNumber = document.getElementById('order-number').value.trim();
        const dateValue = document.getElementById('date-code').value.trim();
        const weekValue = document.getElementById('week-number').value.trim();
        const sequenceLength = document.getElementById('sequence-length').value.trim();
        
        // 验证必填字段
        if (!operator || !processOrder || !productModel || !productCode || !quantity || !orderNumber || !dateValue || !weekValue || !sequenceLength) {
            Swal.fire({
                title: '表单不完整',
                text: '请填写所有必填字段',
                icon: 'warning',
                confirmButtonText: '确定'
            });
            return false;
        }
        
        // 验证生产数量
        const quantityNumber = parseInt(quantity, 10);
        if (isNaN(quantityNumber) || quantityNumber <= 0) {
            Swal.fire({
                title: '输入错误',
                text: '生产数量必须是正整数',
                icon: 'error',
                confirmButtonText: '确定'
            });
            return false;
        }
        
        // 验证流水号长度
        const sequenceLengthNumber = parseInt(sequenceLength, 10);
        if (isNaN(sequenceLengthNumber) || sequenceLengthNumber <= 0) {
            Swal.fire({
                title: '输入错误',
                text: '流水号长度必须是正整数',
                icon: 'error',
                confirmButtonText: '确定'
            });
            return false;
        }
        
        // 验证日期格式
        if (dateValue.length !== 6 || !/^\d{6}$/.test(dateValue)) {
            Swal.fire({
                title: '日期格式错误',
                text: '请输入6位数字日期（YYMMDD）',
                icon: 'error',
                confirmButtonText: '确定'
            });
            return false;
        }
        
        return true;
    }
    
    // 显示序列号列表
    function displaySNList() {
        const snVerificationLayout = document.getElementById('sn-verification-layout');
        const snListBody = document.getElementById('sn-list-body');
        const snCount = document.getElementById('sn-count');
        const totalCount = document.getElementById('total-count');
        const tableContainer = document.querySelector('.sn-print-record__table-container');
        
        if (!snVerificationLayout || !snListBody || !snCount || !totalCount || snList.length === 0) {
            return;
        }
        
        // 显示序列号列表和验证区域
        snVerificationLayout.style.display = 'flex';
        
        // 更新序列号数量
        snCount.textContent = snList.length;
        totalCount.textContent = snList.length;
        
        // 清空表格
        snListBody.innerHTML = '';
        
        // 填充表格
        snList.forEach((sn, index) => {
            const row = document.createElement('tr');
            row.setAttribute('data-sn', sn);
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${sn}</td>
                <td class="sn-pending">待验证</td>
            `;
            snListBody.appendChild(row);
        });
        
        // 滚动到序列号列表
        snVerificationLayout.scrollIntoView({ behavior: 'smooth' });
        
        // 确保表格滚动到顶部
        if (tableContainer) {
            tableContainer.scrollTop = 0;
        }
        
        // 初始化进度条
        updateProgressBar();
        
        // 设置焦点到扫描输入框
        const scanInput = document.getElementById('scan-input');
        if (scanInput) {
            scanInput.focus();
        }
    }
    
    // 重置表单
    function resetForm() {
        Swal.fire({
            title: '确认重置',
            text: '确定要重置表单吗？除操作人员、日期、周期和流水号长度外，其他数据将被清空',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                // 存储需要保留的字段值
                const operatorValue = document.getElementById('operator').value;
                const dateCodeValue = document.getElementById('date-code').value;
                const weekNumberValue = document.getElementById('week-number').value;
                const sequenceLengthValue = document.getElementById('sequence-length').value;
                
                // 清空表单字段，但排除需要保留的字段
                const formInputs = document.querySelectorAll('.sn-print-record__input');
                formInputs.forEach(input => {
                    if (input.id !== 'scan-input' && 
                        input.id !== 'operator' && 
                        input.id !== 'date-code' && 
                        input.id !== 'week-number' && 
                        input.id !== 'sequence-length') {
                        input.value = '';
                    }
                });
                
                // 恢复保留的字段值
                document.getElementById('operator').value = operatorValue;
                document.getElementById('date-code').value = dateCodeValue;
                document.getElementById('week-number').value = weekNumberValue;
                document.getElementById('sequence-length').value = sequenceLengthValue;
                
                // 如果日期被清空，重新设置日期
                if (!document.getElementById('date-code').value) {
                    setCurrentDate();
                }
                
                // 隐藏序列号列表
                const snVerificationLayout = document.getElementById('sn-verification-layout');
                if (snVerificationLayout) {
                    snVerificationLayout.style.display = 'none';
                }
                
                // 清空SN列表和扫描记录
                snList = [];
                resetScanVerification();

                // 重置SN生成相关的状态，确保下次能正确获取流水号
                if (window.snPrintRecordState) {
                    window.snPrintRecordState.currentProductCode = '';
                    window.snPrintRecordState.currentOrderNumber = '';
                    window.snPrintRecordState.currentYear = '';
                    window.snPrintRecordState.currentWeek = '';
                    window.snPrintRecordState.nextSequence = 1; // 重置为初始值
                }
                
                // **新增：清理特殊格式相关的样式和提示**
                const firstSNInput = document.getElementById('first-sn');
                if (firstSNInput) {
                    firstSNInput.classList.remove('sn-print-record__input--special-format');
                    firstSNInput.placeholder = '基于配置生成或手动输入';
                }
                
                // 移除特殊格式提示
                const hintElement = document.querySelector('.sn-print-record__special-format-hint');
                if (hintElement) {
                    hintElement.remove();
                }
                
                Swal.fire({
                    title: '已重置',
                    text: '表单已重置',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    }
    
    // 导出Excel
    function exportToExcel() {
        // 验证是否已生成序列号
        if (snList.length === 0) {
            Swal.fire({
                title: '导出失败',
                text: '请先生成序列号',
                icon: 'error',
                confirmButtonText: '确定'
            });
            return;
        }
        
        // 模拟导出Excel操作
        Swal.fire({
            title: '导出成功',
            text: '序列号列表已导出到Excel文件',
            icon: 'success',
            confirmButtonText: '确定'
        });
        
        // 此处实际项目中应调用后端API导出Excel或使用前端库如sheetjs
    }
    
    // 验证扫描的SN
    async function verifySN(scannedSN) {
        if (!scannedSN) {
            updateScanStatus('请输入或扫描SN号', 'error');
            return;
        }
        
        // 先检查SN是否在列表中和是否已扫描
        const isValid = snList.includes(scannedSN);
        const alreadyScanned = scannedSNs.has(scannedSN);
        const currentTime = new Date().toLocaleTimeString();
        let statusMessage = '';
        let statusClass = '';
        let scanItemClass = '';
        let iconClass = '';
        
        if (isValid) {
            if (alreadyScanned) {
                statusMessage = `SN: ${scannedSN} 重复扫描`;
                statusClass = 'error';
                scanItemClass = 'error';
                iconClass = 'error';
                speakText('条码错误'); // **新增：重复扫描语音提示**
            } else {
                try {
                    // 获取表单数据用于保存记录，并对指定字段进行trim处理
                    const processOrder = document.getElementById('process-order').value.trim();
                    const productModel = document.getElementById('product-model').value.trim();
                    const productCode = document.getElementById('product-code').value.trim();
                    const quantityValue = document.getElementById('production-quantity').value.trim();
                    const quantity = parseInt(quantityValue, 10);
                    const operator = document.getElementById('operator').value.trim();
                    
                    // 保存单个验证成功的SN到数据库
                    const response = await fetch('/api/sn-print-record/save-single-verified-sn', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            processOrder: processOrder,
                            productModel: productModel,
                            productCode: productCode,
                            quantity: quantity,
                            operator: operator,
                            sn: scannedSN
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // 保存成功，更新状态
                        statusMessage = `SN: ${scannedSN} 验证成功并已保存`;
                        statusClass = 'success';
                        scanItemClass = 'success';
                        iconClass = 'success';
                        speakText('条码正确'); // **新增：验证成功语音提示**
                        
                        // 更新已扫描集合
                        scannedSNs.add(scannedSN);
                        
                        // 更新表格中的状态
                        const row = document.querySelector(`tr[data-sn="${scannedSN}"]`);
                        if (row) {
                            const statusCell = row.cells[2];
                            statusCell.textContent = '已验证';
                            statusCell.className = 'sn-verified';
                        }
                        
                        // 更新计数和进度条
                        updateScanCounts();
                        updateProgressBar();
                        
                        // 检查是否已全部验证
                        checkAllVerified();
                    } else {
                        // 保存失败
                        statusMessage = `SN: ${scannedSN} 验证失败: ${data.message}`;
                        statusClass = 'error';
                        scanItemClass = 'error';
                        iconClass = 'error';
                        speakText('条码错误'); // **新增：保存失败语音提示**
                    }
                } catch (error) {
                    Logger.error('保存SN记录时出错:', error);
                    
                    // 网络错误情况下，继续本地验证流程，但显示警告
                    statusMessage = `SN: ${scannedSN} 验证成功但保存失败 (网络错误)`;
                    statusClass = 'warning';
                    scanItemClass = 'warning';
                    iconClass = 'warning';
                    speakText('条码正确'); // **新增：网络错误但验证成功的语音提示**
                    
                    // 更新已扫描集合
                    scannedSNs.add(scannedSN);
                    
                    // 更新表格中的状态
                    const row = document.querySelector(`tr[data-sn="${scannedSN}"]`);
                    if (row) {
                        const statusCell = row.cells[2];
                        statusCell.textContent = '已验证';
                        statusCell.className = 'sn-verified sn-verified-warning';
                    }
                    
                    // 更新计数和进度条
                    updateScanCounts();
                    updateProgressBar();
                    
                    // 检查是否已全部验证
                    checkAllVerified();
                }
            }
        } else {
            statusMessage = `SN: ${scannedSN} 不在列表中`;
            statusClass = 'error';
            scanItemClass = 'error';
            iconClass = 'error';
            speakText('条码错误'); // **新增：SN不在列表中语音提示**
        }
        
        // 更新扫描状态
        updateScanStatus(statusMessage, statusClass);
        
        // 添加到最近扫描记录
        addToRecentScans({
            sn: scannedSN,
            time: currentTime,
            isValid: isValid && !alreadyScanned && statusClass === 'success',
            class: scanItemClass,
            iconClass: iconClass
        });
    }
    
    // 更新扫描状态提示
    function updateScanStatus(message, statusClass) {
        const scanStatus = document.getElementById('scan-status');
        if (scanStatus) {
            scanStatus.textContent = message;
            scanStatus.className = 'sn-print-record__scan-status';
            if (statusClass) {
                scanStatus.classList.add(statusClass);
            }
        }
    }
    
    // 添加到最近扫描记录
    function addToRecentScans(scanRecord) {
        // 将新记录添加到开头
        recentScans.unshift(scanRecord);
        
        // 保持记录数量不超过限制
        if (recentScans.length > MAX_RECENT_SCANS) {
            recentScans.pop();
        }
        
        // 更新显示
        updateRecentScansList();
    }
    
    // 更新最近扫描记录列表
    function updateRecentScansList() {
        const recentScansList = document.getElementById('recent-scans-list');
        if (!recentScansList) return;
        
        if (recentScans.length === 0) {
            recentScansList.innerHTML = '<div class="sn-print-record__empty-message">尚无扫描记录</div>';
            return;
        }
        
        recentScansList.innerHTML = '';
        
        recentScans.forEach(scan => {
            const scanItem = document.createElement('div');
            scanItem.className = `sn-print-record__scan-item ${scan.class}`;
            
            scanItem.innerHTML = `
                <i class="fas ${scan.isValid ? 'fa-check-circle' : 'fa-times-circle'} ${scan.iconClass}"></i>
                <span class="sn-print-record__scan-item-sn">${scan.sn}</span>
                <span class="sn-print-record__scan-item-time">${scan.time}</span>
            `;
            
            recentScansList.appendChild(scanItem);
        });
    }
    
    // 更新扫描计数
    function updateScanCounts() {
        const scannedCount = document.getElementById('scanned-count');
        if (scannedCount) {
            scannedCount.textContent = scannedSNs.size;
        }
    }
    
    // 更新进度条
    function updateProgressBar() {
        const progressBar = document.getElementById('progress-bar');
        if (!progressBar || snList.length === 0) return;
        
        const percentage = Math.floor((scannedSNs.size / snList.length) * 100);
        progressBar.style.width = `${percentage}%`;
        progressBar.textContent = `${percentage}%`;
    }
    
    // 检查是否所有SN都已验证
    function checkAllVerified() {
        const completeVerifyBtn = document.getElementById('complete-verify-btn');
        if (!completeVerifyBtn) return;
        
        if (scannedSNs.size === snList.length) {
            // 全部验证完成
            completeVerifyBtn.disabled = false;
            
            // 显示完成提示
            Swal.fire({
                title: '验证完成',
                text: '所有序列号已验证成功',
                icon: 'success',
                confirmButtonText: '确定'
            });
        } else {
            completeVerifyBtn.disabled = true;
        }
    }
    
    // 重置扫描验证
    function resetScanVerification() {
        // 清空已扫描集合和最近扫描记录
        scannedSNs.clear();
        recentScans = [];
        
        // 更新扫描状态和计数
        updateScanStatus('准备就绪，请开始扫描...', '');
        updateScanCounts();
        updateProgressBar();
        updateRecentScansList();
        
        // 重置表格中的状态
        const rows = document.querySelectorAll('#sn-list-body tr');
        rows.forEach(row => {
            const statusCell = row.cells[2];
            if (statusCell) {
                statusCell.textContent = '待验证';
                statusCell.className = 'sn-pending';
            }
        });
        
        // 禁用完成验证按钮
        const completeVerifyBtn = document.getElementById('complete-verify-btn');
        if (completeVerifyBtn) {
            completeVerifyBtn.disabled = true;
        }
        
        // 设置焦点到扫描输入框
        const scanInput = document.getElementById('scan-input');
        if (scanInput) {
            scanInput.value = '';
            scanInput.focus();
        }
    }
    
    // 完成验证
    function completeVerification() {
        if (scannedSNs.size !== snList.length) {
            Swal.fire({
                title: '验证未完成',
                text: '还有序列号未验证，请完成所有序列号的验证',
                icon: 'warning',
                confirmButtonText: '确定'
            });
            return;
        }
        
        // 所有SN都已验证并保存到数据库，提示用户并重置表单
        Swal.fire({
            title: '验证完成',
            text: `所有SN号已验证并保存，共${scannedSNs.size}条记录`,
            icon: 'success',
            confirmButtonText: '确定'
        }).then(() => {
            // 重置页面，准备新的验证
            resetForm();
        });
    }
    
    // 确保将新的updateFirstSN函数暴露给window对象
    window.updateFirstSN = updateFirstSN;
    
    // 将初始化函数暴露给window对象
    window.initSNPrintRecordPage = initSNPrintRecordPage;
})(); 