from flask import Blueprint, jsonify, request, current_app
from jwt import decode
from functools import wraps
from database.db_manager import DatabaseManager
from datetime import datetime
from sqlalchemy import text
from models.assembly import CompleteProduct
from models.firmware import FirmwareSerialDetail, DownloadRecord

io_module_bp = Blueprint('io_module', __name__)

def get_current_user():
    token = request.cookies.get('token')
    if token:
        try:
            payload = decode(token, current_app.config['JWT_SECRET'], algorithms=["HS256"])
            return payload.get('username')
        except:
            return None
    return None

@io_module_bp.route('/get-current-user', methods=['GET'])
def get_current_user_route():
    username = get_current_user()
    return jsonify({
        'success': True,
        'username': username
    })

@io_module_bp.route('/submit-test', methods=['POST'])
def submit_test():
    try:
        data = request.get_json()
        print("Received data:", data)
        db = DatabaseManager()
        
        pro_status = data.get('pro_status')
        if pro_status is None:
            return jsonify({
                'success': False,
                'message': '产品状态不能为空'
            }), 400

        with db.get_session() as session:
            # 首先检查 SN 号是否存在
            check_sql = text("SELECT pro_status, maintenance, rework FROM coupler_table WHERE pro_sn = :pro_sn")
            result = session.execute(check_sql, {'pro_sn': data['pro_sn']}).fetchone()

            if result:
                # SN号已存在
                if pro_status == 1:  # 新品
                    return jsonify({
                        'success': False,
                        'message': f'产品SN号 {data["pro_sn"]} 已存在，新品不能使用重复的SN号'
                    }), 400
                
                # 获取现有的维修和返工次数
                existing_maintenance = result[1]
                existing_rework = result[2]
                
                # 更新维修或返工次数
                if pro_status == 2:  # 维修
                    maintenance = existing_maintenance + 1
                    rework = existing_rework
                else:  # 返工
                    maintenance = existing_maintenance
                    rework = existing_rework + 1

                # 更新现有记录
                update_sql = text("""
                    UPDATE coupler_table 
                    SET tester = :tester,
                        work_order = :work_order,
                        work_qty = :work_qty,
                        pro_model = :pro_model,
                        pro_status = :pro_status,
                        pro_code = :pro_code,
                        pro_batch = :pro_batch,
                        remarks = :remarks,
                        maintenance = :maintenance,
                        rework = :rework,
                        couplersw_version = :couplersw_version,
                        coupler_date = :coupler_date,
                        backplane = :backplane,
                        body_io = :body_io,
                        led_tube = :led_tube,
                        led_bulb = :led_bulb,
                        net_port = :net_port,
                        test_status = :test_status,
                        product_type = :product_type,
                        test_time = CURRENT_TIMESTAMP,
                        comparison_time = CASE 
                            WHEN :pro_status IN (2, 3) THEN NULL 
                            ELSE comparison_time 
                        END,
                        comparison_result = CASE 
                            WHEN :pro_status IN (2, 3) THEN NULL 
                            ELSE comparison_result 
                        END
                    WHERE pro_sn = :pro_sn
                """)
            else:
                # SN号不存在（首次写入）
                if pro_status == 1:  # 新品
                    maintenance = 0
                    rework = 0
                elif pro_status == 2:  # 维修
                    maintenance = 1  # 首次维修设为1
                    rework = 0
                else:  # 返工
                    maintenance = 0
                    rework = 1  # 首次返工设为1
                update_sql = text("""
                    INSERT INTO coupler_table (
                        tester, work_order, work_qty, pro_model, pro_status,
                        pro_code,
                        pro_sn, pro_batch, remarks, rework, maintenance,
                        couplersw_version, coupler_date, backplane, body_io,
                        led_tube, led_bulb, net_port, test_status, product_type
                    ) VALUES (
                        :tester, :work_order, :work_qty, :pro_model,
                        :pro_status, :pro_code,
                        :pro_sn, :pro_batch, :remarks,
                        :rework, :maintenance, :couplersw_version,
                        :coupler_date, :backplane, :body_io, :led_tube,
                        :led_bulb, :net_port, :test_status, :product_type
                    )
                """)

            # 处理测试结果
            test_results = {
                '通过': 1,
                '不通过': 2
            }
            
            # 检查每个测试项目的结果
            test_items = {
                'backplane': data['backplane_result'],
                'body_io': data['body_io_result'],
                'led_bulb': data['led_bulb_result']
            }

            # 收集未通过的测试项目
            failed_tests = {k: v for k, v in test_items.items() if v == '不通过'}
            
            # 检查是否所有测试都通过
            test_status = 'pass' if not failed_tests else 'ng'
            
            # 准备 coupler_table 的参数
            params = {
                'tester': data['tester'],
                'work_order': data['work_order'],
                'work_qty': int(data['work_qty']),
                'pro_model': data['pro_model'],
                'pro_code': data['pro_code'],
                'pro_status': pro_status,
                'pro_sn': data['pro_sn'],
                'pro_batch': data.get('pro_batch', 'N/A'),
                'remarks': data.get('remarks', 'N/A'),
                'rework': rework,
                'maintenance': maintenance,
                'couplersw_version': data['io_version'],  # IO模块版本
                'coupler_date': data['io_build_date'],    # IO模块日期
                'backplane': test_results[data['backplane_result']],
                'body_io': test_results[data['body_io_result']],
                'led_tube': 1,  # IO模块没有这项测试，设为默认值
                'led_bulb': test_results[data['led_bulb_result']],
                'net_port': 1,  # IO模块没有这项测试，设为默认值
                'test_status': test_status,
                'product_type': 'io_module'  # 确保添加产品类型
            }
            
            print("Parameters for SQL:", params)
            
            # 执行 coupler_table 的更新或插入
            session.execute(update_sql, params)

            # 如果有未通过的测试项目，写入故障表
            if failed_tests:
                # 准备故障表的错误字段参数
                error_params = {
                    'burn_err': 0,
                    'power_err': 0,
                    'backplane_err': 2 if 'backplane' in failed_tests else 0,
                    'body_io_err': 2 if 'body_io' in failed_tests else 0,
                    'led_tube_err': 0,  # IO模块没有这项测试
                    'led_bulb_err': 2 if 'led_bulb' in failed_tests else 0,
                    'net_port_err': 0,  # IO模块没有这项测试
                    'rs485_1_err': 0,
                    'rs485_2_err': 0,
                    'rs232_err': 0,
                    'canbus_err': 0,
                    'ethercat_err': 0,
                    'usb_drive_err': 0,
                    'sd_slot_err': 0,
                    'debug_port_err': 0,
                    'dip_switch_err': 0,
                    'reset_btn_err': 0,
                    'other_err': 'N/A'
                }

                # 准备故障表的基本参数
                fault_params = {
                    'tester': data['tester'],
                    'work_order': data['work_order'],
                    'work_qty': int(data['work_qty']),
                    'pro_model': data['pro_model'],
                    'pro_status': pro_status,
                    'pro_sn': data['pro_sn'],
                    'pro_batch': data.get('pro_batch', 'N/A'),
                    'remarks': data.get('remarks', 'N/A'),
                    'rework': rework,
                    'maintenance': maintenance,
                    'pro_code': data['pro_code'],
                    **error_params
                }

                # 插入故障记录
                fault_sql = text("""
                    INSERT INTO faultEntry_table (
                        tester, work_order, work_qty, pro_model, pro_status,
                        pro_sn, pro_batch, remarks, rework, maintenance,
                        pro_code,
                        burn_err, power_err, backplane_err, body_io_err,
                        led_tube_err, led_bulb_err, net_port_err, rs485_1_err,
                        rs485_2_err, rs232_err, canbus_err, ethercat_err,
                        usb_drive_err, sd_slot_err, debug_port_err, dip_switch_err,
                        reset_btn_err, other_err
                    ) VALUES (
                        :tester, :work_order, :work_qty, :pro_model, :pro_status,
                        :pro_sn, :pro_batch, :remarks, :rework, :maintenance,
                        :pro_code,
                        :burn_err, :power_err, :backplane_err, :body_io_err,
                        :led_tube_err, :led_bulb_err, :net_port_err, :rs485_1_err,
                        :rs485_2_err, :rs232_err, :canbus_err, :ethercat_err,
                        :usb_drive_err, :sd_slot_err, :debug_port_err, :dip_switch_err,
                        :reset_btn_err, :other_err
                    )
                """)
                
                print("Fault Parameters for SQL:", fault_params)
                session.execute(fault_sql, fault_params)

            session.commit()
            
            action = "更新" if result else "创建"
            return jsonify({
                'success': True,
                'message': f'测试信息{action}成功' + ('，并已记录故障信息' if failed_tests else '')
            })
        
    except Exception as e:
        print(f"Error details: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交失败：{str(e)}'
        }), 500

@io_module_bp.route('/test', methods=['POST'])
def test():
    data = request.get_json()
    # 处理 IO 模块测试逻辑
    return jsonify({'status': 'success'})

@io_module_bp.route('/get-status', methods=['GET'])
def get_status():
    # 获取 IO 模块状态
    return jsonify({
        'status': 'success',
        'data': {
            'input_status': [1, 0, 1, 0],
            'output_status': [0, 1, 0, 1]
        }
    })

@io_module_bp.route('/check-sn', methods=['GET'])
def check_sn():
    """
    检查SN号是否已经绑定PCBA
    
    路由说明：
    - 方法：GET
    - URL：/api/io-module/check-sn
    - 参数：sn (查询参数)
    
    功能描述：
    1. 接收SN号作为参数
    2. 在complete_products表中查询该SN号
    3. 返回查询结果，表明该SN号是否已绑定PCBA
    """
    try:
        # 获取查询参数中的SN号
        sn = request.args.get('sn')
        if not sn:
            return jsonify({
                'success': False,
                'message': '请提供SN号'
            }), 400
            
        # 创建数据库连接
        db = DatabaseManager()
        with db.get_session() as session:
            # 在complete_products表中查询SN号
            result = session.query(CompleteProduct).filter(
                CompleteProduct.product_sn == sn
            ).first()
            
            # 根据查询结果返回不同的响应
            if result:
                return jsonify({
                    'success': True,
                    'exists': True,
                    'message': 'SN号已绑定PCBA'
                })
            else:
                return jsonify({
                    'success': True,
                    'exists': False,
                    'message': 'SN号未绑定PCBA'
                })
                
    except Exception as e:
        # 异常处理
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500

@io_module_bp.route('/get-version-info', methods=['GET'])
def get_version_info():
    """
    根据产品SN号获取软件版本信息
    
    支持两种查询模式：
    1. 新品模式（product_status=new）：三表联查
       product_sn → complete_products → assembly_id → firmware_serial_detail → work_order → download_record
    2. 返工/维修模式（product_status=used/refurbished）：两表联查
       product_sn → firmware_serial_detail → work_order → download_record
    """
    try:
        # 获取查询参数
        product_sn = request.args.get('product_sn')
        product_status = request.args.get('product_status', 'new')  # 默认为新品
        
        if not product_sn:
            return jsonify({
                'success': False,
                'message': '请提供产品SN号'
            }), 400
            
        # 创建数据库连接
        db = DatabaseManager()
        with db.get_session() as session:
            result = None
            
            if product_status == 'new':
                # 新品模式：三表联查
                # 1. 从complete_products获取assembly_id
                # 2. 从firmware_serial_detail获取work_order (使用assembly_id作为firmware_hash)
                # 3. 从download_record获取software_version和build_time
                result = session.query(
                    DownloadRecord.software_version,
                    DownloadRecord.build_time
                ).join(
                    FirmwareSerialDetail, 
                    DownloadRecord.work_order == FirmwareSerialDetail.work_order
                ).join(
                    CompleteProduct,
                    FirmwareSerialDetail.firmware_hash == CompleteProduct.assembly_id
                ).filter(
                    CompleteProduct.product_sn == product_sn
                ).order_by(
                    DownloadRecord.create_time.desc()  # 获取最新的记录
                ).first()
            
            elif product_status in ['used', 'refurbished']:
                # 返工/维修模式：两表联查
                # 1. 从firmware_serial_detail获取work_order (使用product_sn作为firmware_sn)
                # 2. 从download_record获取software_version和build_time
                result = session.query(
                    DownloadRecord.software_version,
                    DownloadRecord.build_time
                ).join(
                    FirmwareSerialDetail,
                    DownloadRecord.work_order == FirmwareSerialDetail.work_order
                ).filter(
                    FirmwareSerialDetail.firmware_sn == product_sn
                ).order_by(
                    DownloadRecord.create_time.desc()  # 获取最新的记录
                ).first()
            
            if result:
                return jsonify({
                    'success': True,
                    'data': {
                        'software_version': result.software_version,
                        'build_time': result.build_time
                    },
                    'message': f'版本信息获取成功（{product_status}模式）'
                })
            else:
                return jsonify({
                    'success': True,
                    'data': {
                        'software_version': '',
                        'build_time': ''
                    },
                    'message': f'未找到相关版本信息（{product_status}模式）'
                })
                
    except Exception as e:
        # 异常处理
        return jsonify({
            'success': False,
            'message': f'查询版本信息失败：{str(e)}'
        }), 500
