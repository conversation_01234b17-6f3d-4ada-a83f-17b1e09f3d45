from flask import Blueprint, jsonify, request
from database.db_manager import DatabaseManager
from services.assembly_service import AssemblyService
import logging

logger = logging.getLogger(__name__)
barcode_binding_bp = Blueprint('barcode_binding', __name__)

def check_assembly_stage_completed(session, order_number):
    """
    检查工单的组装前阶段是否完成
    
    Args:
        session: 数据库会话
        order_number: 工单号
        
    Returns:
        tuple: (is_completed, error_message)
    """
    try:
        from models.modelquality_inspection import QualityInspectionWorkOrder
        from models.modelwork_order import WorkOrder
        
        # 根据工单号查找工单管理表中的工单
        work_order = session.query(WorkOrder).filter(
            WorkOrder.dbord_processOrderNo == order_number
        ).first()
        
        if not work_order:
            return False, '未找到对应的工单'
        
        # 根据工单号查找质检工单
        quality_work_order = session.query(QualityInspectionWorkOrder).filter(
            QualityInspectionWorkOrder.work_order_no == order_number
        ).first()
        
        if not quality_work_order or not quality_work_order.assembly_stage_completed:
            return False, '该工单未完成组装前阶段外观检验，不允许绑定外壳'
        
        return True, None
        
    except Exception as e:
        logger.error("检查组装阶段状态失败: %s", str(e), exc_info=True)
        return False, f'检查组装阶段状态失败：{str(e)}'

@barcode_binding_bp.route('/bind_controller', methods=['POST'])
def bind_controller():
    try:
        data = request.get_json()
        logger.info("接收到的数据: %s", data)
        
        # 验证必填字段
        required_fields = ['tester', 'productModel', 'orderNumber', 'productQuantity', 'type', 'productCode']
        if not all(data.get(field) for field in required_fields):
            return jsonify({
                'success': False,
                'message': '缺少必要信息'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            service = AssemblyService(session)
            assembly = service.create_board_assembly(data)
            
            return jsonify({
                'success': True,
                'message': '控制器单板绑定成功',
                'assembly_id': assembly.assembly_id
            })
            
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        logger.error("绑定失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'绑定失败：{str(e)}'
        }), 500

@barcode_binding_bp.route('/bind_shell', methods=['POST'])
def bind_shell():
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['assembly_id', 'shell_sn', 'tester', 'order_number', 
                         'product_quantity', 'product_model', 'productCode']
        if not all(data.get(field) for field in required_fields):
            return jsonify({
                'success': False,
                'message': '缺少必要信息'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            # 检查工单的组装前阶段是否完成
            is_completed, error_message = check_assembly_stage_completed(session, data['order_number'])
            
            if not is_completed:
                return jsonify({
                    'success': False,
                    'message': error_message
                }), 400
            
            service = AssemblyService(session)
            service.bind_shell(data['assembly_id'], data)
            
            return jsonify({
                'success': True,
                'message': '外壳绑定成功'
            })
            
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        logger.error("绑定失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'绑定失败：{str(e)}'
        }), 500

@barcode_binding_bp.route('/search', methods=['GET'])
def search_binding():
    try:
        sn = request.args.get('sn')
        if not sn:
            return jsonify({
                'success': False,
                'message': '请提供序列号'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            service = AssemblyService(session)
            result = service.search_assembly(sn)
            
            if result:
                return jsonify({
                    'success': True,
                    'data': result
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '未找到相关绑定信息'
                }), 404
                
    except Exception as e:
        logger.error("查询失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500

@barcode_binding_bp.route('/update_binding', methods=['POST'])
def update_binding():
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['assembly_id', 'board_position', 'new_sn']
        if not all(data.get(field) for field in required_fields):
            return jsonify({
                'success': False,
                'message': '缺少必要信息'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            service = AssemblyService(session)
            assembly = service.update_board_binding(
                data['assembly_id'],
                data['board_position'],
                data['new_sn']
            )
            
            return jsonify({
                'success': True,
                'message': '板子更换成功',
                'assembly_id': assembly.assembly_id
            })
            
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        logger.error("更新失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'更新失败：{str(e)}'
        }), 500

@barcode_binding_bp.route('/update_shell', methods=['POST'])
def update_shell():
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['assembly_id', 'new_shell_sn']
        if not all(data.get(field) for field in required_fields):
            return jsonify({
                'success': False,
                'message': '缺少必要信息'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            service = AssemblyService(session)
            complete_product = service.update_shell_binding(
                data['assembly_id'],
                data['new_shell_sn']
            )
            
            return jsonify({
                'success': True,
                'message': '外壳SN更新成功'
            })
            
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        logger.error("更新失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'更新失败：{str(e)}'
        }), 500

@barcode_binding_bp.route('/unbind_all', methods=['POST'])
def unbind_all():
    try:
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('assembly_id'):
            return jsonify({
                'success': False,
                'message': '缺少assembly_id'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            service = AssemblyService(session)
            service.unbind_all(data['assembly_id'])
            
            return jsonify({
                'success': True,
                'message': '解绑成功'
            })
            
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        logger.error("解绑失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'解绑失败：{str(e)}'
        }), 500

@barcode_binding_bp.route('/bind_io', methods=['POST'])
def bind_io():
    try:
        data = request.get_json()
        logger.info("接收到的IO模块数据: %s", data)
        
        # 验证必填字段
        required_fields = ['tester', 'order_number', 'product_quantity', 
                         'product_model', 'type', 'board_a', 'shell_sn', 'productCode']
        if not all(data.get(field) for field in required_fields):
            return jsonify({
                'success': False,
                'message': '缺少必要信息'
            }), 400
            
        # 如果是类型B，还需要验证board_b
        if data['type'] == 'B' and not data.get('board_b'):
            return jsonify({
                'success': False,
                'message': '类型B需要两块板子'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            # 检查工单的组装前阶段是否完成
            is_completed, error_message = check_assembly_stage_completed(session, data['order_number'])
            
            if not is_completed:
                return jsonify({
                    'success': False,
                    'message': error_message
                }), 400
            
            service = AssemblyService(session)
            assembly, complete_product = service.create_io_assembly(data)
            
            return jsonify({
                'success': True,
                'message': 'IO模块绑定成功',
                'assembly_id': assembly.assembly_id
            })
            
    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400
    except Exception as e:
        logger.error("IO模块绑定失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'绑定失败：{str(e)}'
        }), 500

@barcode_binding_bp.route('/binding_progress', methods=['GET'])
def get_binding_progress():
    try:
        order_number = request.args.get('orderNumber')
        if not order_number:
            return jsonify({
                'success': False,
                'message': '请提供工单号'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            service = AssemblyService(session)
            board_count, shell_count, total_quantity = service.get_binding_progress(order_number)
            
            return jsonify({
                'success': True,
                'data': {
                    'boardCount': board_count,
                    'shellCount': shell_count,
                    'totalQuantity': total_quantity
                }
            })
                
    except Exception as e:
        logger.error("获取绑定进度失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取绑定进度失败：{str(e)}'
        }), 500
