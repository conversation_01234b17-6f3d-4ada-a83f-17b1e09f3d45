// 文件完整性验证工具
class FileHashVerifier {
    constructor() {
        this.init();
    }

    init() {
        this.createUI();
        this.bindEvents();
    }

    createUI() {
        // 如果页面上已经有验证工具，就不重复创建
        if (document.getElementById('file-hash-verifier')) return;

        const verifierHTML = `
            <div id="file-hash-verifier" class="hash-verifier">
                <div class="hash-verifier__header">
                    <h3>文件完整性验证工具</h3>
                    <button class="hash-verifier__close" onclick="FileHashVerifier.hide()">×</button>
                </div>
                <div class="hash-verifier__content">
                    <div class="hash-verifier__section">
                        <label>选择要验证的文件：</label>
                        <input type="file" id="verifier-file-input" class="hash-verifier__file-input">
                    </div>
                    
                    <div class="hash-verifier__section">
                        <label>服务器提供的Hash值：</label>
                        <input type="text" id="server-hash-input" placeholder="请输入服务器Hash值" class="hash-verifier__text-input">
                    </div>
                    
                    <div class="hash-verifier__section">
                        <button id="verify-button" class="hash-verifier__button">开始验证</button>
                    </div>
                    
                    <div class="hash-verifier__result" id="verification-result"></div>
                </div>
            </div>
            <div id="hash-verifier-overlay" class="hash-verifier__overlay"></div>
        `;

        document.body.insertAdjacentHTML('beforeend', verifierHTML);
        this.addStyles();
    }

    addStyles() {
        if (document.getElementById('hash-verifier-styles')) return;

        const styles = `
            <style id="hash-verifier-styles">
                .hash-verifier {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    z-index: 10000;
                    width: 700px;
                    max-width: 95vw;
                    display: none;
                }
                
                .hash-verifier__overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    z-index: 9999;
                    display: none;
                }
                
                .hash-verifier__header {
                    padding: 20px;
                    border-bottom: 1px solid #eee;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .hash-verifier__header h3 {
                    margin: 0;
                    color: #333;
                }
                
                .hash-verifier__close {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                }
                
                .hash-verifier__close:hover {
                    color: #333;
                }
                
                .hash-verifier__content {
                    padding: 20px;
                }
                
                .hash-verifier__section {
                    margin-bottom: 20px;
                }
                
                .hash-verifier__section label {
                    display: block;
                    margin-bottom: 8px;
                    font-weight: 500;
                    color: #333;
                }
                
                .hash-verifier__file-input,
                .hash-verifier__text-input {
                    width: 100%;
                    padding: 10px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    box-sizing: border-box;
                }
                
                .hash-verifier__button {
                    background: #409eff;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                }
                
                .hash-verifier__button:hover {
                    background: #337ab7;
                }
                
                .hash-verifier__button:disabled {
                    background: #ccc;
                    cursor: not-allowed;
                }
                
                .hash-verifier__result {
                    margin-top: 20px;
                    padding: 15px;
                    border-radius: 4px;
                    display: none;
                }
                
                .hash-verifier__result--success {
                    background: #f0f9eb;
                    border: 1px solid #e1f3d8;
                    color: #67c23a;
                }
                
                .hash-verifier__result--error {
                    background: #fef0f0;
                    border: 1px solid #fde2e2;
                    color: #f56c6c;
                }
                
                .hash-verifier__result--info {
                    background: #ecf5ff;
                    border: 1px solid #d9ecff;
                    color: #409eff;
                }
                
                .hash-verifier--visible {
                    display: block;
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.id === 'verify-button') {
                this.verifyFile();
            }
            if (e.target.id === 'hash-verifier-overlay') {
                FileHashVerifier.hide();
            }
        });
    }

    async verifyFile() {
        const fileInput = document.getElementById('verifier-file-input');
        const serverHashInput = document.getElementById('server-hash-input');
        const resultDiv = document.getElementById('verification-result');
        const button = document.getElementById('verify-button');

        const file = fileInput.files[0];
        const serverHash = serverHashInput.value.trim();

        if (!file) {
            this.showResult('error', '请选择要验证的文件');
            return;
        }

        if (!serverHash) {
            this.showResult('error', '请输入服务器Hash值');
            return;
        }

        button.disabled = true;
        button.textContent = '验证中...';

        try {
            this.showResult('info', '正在计算文件Hash值，请稍候...');
            
            const fileHash = await this.calculateFileHash(file);
            
            if (fileHash.toLowerCase() === serverHash.toLowerCase()) {
                this.showResult('success', `✅ 文件完整性验证通过！
                <br>文件Hash: ${fileHash}
                <br>服务器Hash: ${serverHash}
                <br>文件未被损坏或篡改。`);
            } else {
                this.showResult('error', `❌ 文件完整性验证失败！
                <br>文件Hash: ${fileHash}
                <br>服务器Hash: ${serverHash}
                <br>文件可能已损坏或被篡改，建议重新下载。`);
            }
        } catch (error) {
            this.showResult('error', '验证失败: ' + error.message);
        } finally {
            button.disabled = false;
            button.textContent = '开始验证';
        }
    }

    async calculateFileHash(file) {
        // 检查环境兼容性
        if (!window.crypto || !window.crypto.subtle) {
            throw new Error('当前环境不支持加密API，请使用HTTPS或localhost访问');
        }
        
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async function(e) {
                try {
                    const arrayBuffer = e.target.result;
                    const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
                    const hashArray = Array.from(new Uint8Array(hashBuffer));
                    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                    resolve(hashHex);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file);
        });
    }

    showResult(type, message) {
        const resultDiv = document.getElementById('verification-result');
        resultDiv.className = `hash-verifier__result hash-verifier__result--${type}`;
        resultDiv.innerHTML = message;
        resultDiv.style.display = 'block';
    }

    static show(serverHash = '') {
        const verifier = document.getElementById('file-hash-verifier');
        const overlay = document.getElementById('hash-verifier-overlay');
        
        if (verifier && overlay) {
            verifier.classList.add('hash-verifier--visible');
            overlay.classList.add('hash-verifier--visible');
            
            if (serverHash) {
                document.getElementById('server-hash-input').value = serverHash;
            }
        }
    }

    static hide() {
        const verifier = document.getElementById('file-hash-verifier');
        const overlay = document.getElementById('hash-verifier-overlay');
        
        if (verifier && overlay) {
            verifier.classList.remove('hash-verifier--visible');
            overlay.classList.remove('hash-verifier--visible');
        }
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    new FileHashVerifier();
});

// 导出到全局
window.FileHashVerifier = FileHashVerifier; 