from sqlalchemy import Column, String, Integer, DateTime, SmallInteger
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class LaserStandard(Base):
    """激光打标表模型"""
    __tablename__ = 'laser_standard'
    
    # 由于原表没有主键，添加联合主键
    laser_order_no = Column(String(50), primary_key=True, comment='加工单号')
    laser_sn = Column(String(100), primary_key=True, comment='SN号')
    
    laser_product_model = Column(String(50), nullable=False, comment='产品型号')
    laser_sn_status = Column(SmallInteger, default=1, comment='SN状态，1或2')
    laser_product_code = Column(String(50), nullable=False, comment='产品编码')
    laser_quantity = Column(Integer, nullable=False, comment='生产数量')
    laser_operator = Column(String(50), nullable=False, comment='操作人员')
    laser_operation_time = Column(DateTime, nullable=False, default=datetime.now, comment='操作时间')
    
    def __repr__(self):
        return f"<LaserStandard(order_no='{self.laser_order_no}', sn='{self.laser_sn}')>" 