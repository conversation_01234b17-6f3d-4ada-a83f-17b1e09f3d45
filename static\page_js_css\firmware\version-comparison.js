// 版本比对页面Vue应用
(function() {
    'use strict';
    
    Logger.log('Loading Version Comparison Vue App...');
    
    // 检查共享分页功能是否已加载
    if (!window.FirmwarePagination) {
        Logger.error('FirmwarePagination not loaded. Please include firmware-common-paging.js first.');
        return;
    }
    
    // 检查依赖
    if (!window.Vue || !window.ElementPlus) {
        Logger.error('Vue or ElementPlus not loaded');
        return;
    }

    const { createApp, ref, reactive, computed, onMounted, onUnmounted, nextTick } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    
    const VersionComparisonApp = {
        setup() {
            // 响应式数据
            const loading = ref(false);
            const baselineLoading = ref(false);
            const targetLoading = ref(false);
            
            // 输入框引用
            const baselineInputRef = ref(null);
            const targetInputRef = ref(null);
            
            // 语音播报函数
            const speakAlert = (message) => {
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(message);
                    utterance.lang = 'zh-CN';
                    utterance.rate = 1;
                    utterance.volume = 0.8;
                    speechSynthesis.speak(utterance);
                }
            };
            
            // 基准版本数据
            const baselineForm = reactive({
                serialNumber: '',
                firmwareInfo: null
            });
            
            // 比对基准值
            const baselineValues = reactive({
                softwareVersion: '',
                buildTime: '',
                highSpeedIO: '',
                backplaneVersion: '',
                workOrderNumber: '无'
            });
            
            // 待比对版本数据
            const targetForm = reactive({
                serialNumber: '',
                firmwareInfo: null
            });
            
            // 比对结果
            const comparisonResult = ref(null);
            
            // 比对记录列表
            const comparisonRecords = ref([]);
            
            // 统计数据
            const statistics = computed(() => {
                const total = comparisonRecords.value.length;
                const correct = comparisonRecords.value.filter(record => record.result === 'success').length;
                const error = total - correct;
                const accuracy = total > 0 ? Math.round((correct / total) * 100) : 0;
                
                return {
                    total,
                    correct,
                    error,
                    accuracy
                };
            });
            
            // 获取全局数据管理器
            const dataManager = window.FirmwareDataManager;
            
            // 事件监听器
            let eventListeners = [];
            
            // 根据SN号查询基准版本信息
            const queryBaselineInfo = async (serialNumber) => {
                const response = await fetch(`/api/version-comparison/baseline/${encodeURIComponent(serialNumber)}`);
                
                // 首先检查网络响应是否成功
                if (!response.ok) {
                    let result;
                    try {
                        // 尝试解析JSON响应体，即使是404错误
                        result = await response.json();
                    } catch (e) {
                        // 如果JSON解析失败，则抛出通用错误
                        throw new Error(`网络错误: ${response.status} ${response.statusText}`);
                    }

                    // 如果响应体中有fallback_data，则使用它
                    if (result && result.data && result.data.fallback_data) {
                        const fallbackData = result.data.fallback_data;
                        
                        ElMessage.warning({
                            message: result.message || '未找到活跃标准固件版本，已显示该SN号的实际版本信息（状态：非最新）',
                            duration: 8000,
                            showClose: true
                        });
                        
                        speakAlert('版本错误'); // 提示版本错误
                        
                        return {
                            serialNumber: fallbackData.basic_info?.sn || serialNumber,
                            workOrder: fallbackData.basic_info?.work_order || '未知',
                            dataSource: fallbackData.basic_info?.data_source || '测试记录',
                            description: fallbackData.basic_info?.description || '非最新版本',
                            firmwareName: fallbackData.firmware_info?.firmware_name || '未知固件',
                            developer: fallbackData.firmware_info?.developer || '未知',
                            status: 'inactive', // 明确标记为非最新
                            softwareVersion: fallbackData.version_values?.software_version || '未知',
                            buildTime: fallbackData.version_values?.build_time || '未知',
                            highSpeedIO: fallbackData.version_values?.io_version || '未知',
                            backplaneVersion: fallbackData.version_values?.backplane_version || '未知'
                        };
                    }
                    // 如果没有fallback_data，则抛出错误信息
                    throw new Error(result.message || `查询基准版本失败: ${response.status}`);
                }
                
                // 如果响应成功 (2xx)
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    const firmwareInfo = data.firmware_info;
                    
                    if (firmwareInfo.status && firmwareInfo.status !== 'active') {
                        ElMessage.warning({
                            message: '当前产品的版本不是最新的，建议到"所有固件"页面查询该产品相关版本信息并手动填入到"比对基准值"输入框',
                            duration: 8000,
                            showClose: true
                        });
                        speakAlert('版本错误'); // 提示版本错误
                    }
                    
                    return {
                        serialNumber: data.basic_info.sn,
                        workOrder: data.basic_info.work_order,
                        dataSource: data.basic_info.data_source,
                        description: data.basic_info.description,
                        firmwareName: firmwareInfo.firmware_name,
                        developer: firmwareInfo.developer,
                        status: firmwareInfo.status || 'active',
                        softwareVersion: data.baseline_values.software_version,
                        buildTime: data.baseline_values.build_time,
                        highSpeedIO: data.baseline_values.io_version,
                        backplaneVersion: data.baseline_values.backplane_version
                    };
                } else {
                    // 即使响应成功，业务也可能失败
                    throw new Error(result.message || '查询基准版本信息时发生未知错误');
                }
            };
            
            // 根据SN号查询待比对版本信息
            const queryTargetInfo = async (serialNumber) => {
                try {
                    const response = await fetch(`/api/version-comparison/target/${encodeURIComponent(serialNumber)}`);
                    const result = await response.json();
                    
                    if (result.success) {
                        const data = result.data;
                        return {
                            serialNumber: data.basic_info.sn,
                            model: data.basic_info.product_model,
                            workOrder: data.basic_info.work_order,
                            status: data.basic_info.test_status,
                            timestamp: data.basic_info.test_time,
                            tester: data.basic_info.tester,
                            productType: data.version_info.product_type,
                            batchNumber: data.basic_info.batch_number,
                            customerName: data.basic_info.customer_name,
                            softwareVersion: data.version_values.software_version,
                            buildTime: data.version_values.build_time,
                            highSpeedIO: data.version_values.io_version,
                            backplaneVersion: data.version_values.backplane_version
                        };
                    } else {
                        throw new Error(result.message || '查询待比对版本信息失败');
                    }
                } catch (error) {
                    Logger.error('查询待比对版本信息失败:', error);
                    throw error;
                }
            };
            
            // 查询基准版本
            const queryBaseline = async () => {
                if (!baselineForm.serialNumber.trim()) {
                    ElMessage.warning('请输入基准SN号');
                    return;
                }
                
                try {
                    baselineLoading.value = true;
                    const result = await queryBaselineInfo(baselineForm.serialNumber.trim());
                    baselineForm.firmwareInfo = result;
                    
                    // 自动填充比对基准值
                    baselineValues.softwareVersion = result.softwareVersion;
                    baselineValues.buildTime = result.buildTime;
                    baselineValues.highSpeedIO = result.highSpeedIO;
                    baselineValues.backplaneVersion = result.backplaneVersion;
                    // 批次号(工单号)保持默认值"无"
                    
                    ElMessage.success('基准版本查询成功');
                    
                    // 如果固件状态为"非最新"，1秒后自动清除输入框
                    if (result.status && result.status !== 'active') {
                        setTimeout(() => {
                            baselineForm.serialNumber = '';
                            
                            // 聚焦到基准SN号输入框
                            nextTick(() => {
                                if (baselineInputRef.value) {
                                    baselineInputRef.value.focus();
                                }
                            });
                        }, 1000);
                    }
                } catch (error) {
                    Logger.error('查询基准版本失败:', error);
                    ElMessage.error('查询失败: ' + error.message);
                    baselineForm.firmwareInfo = null;
                } finally {
                    baselineLoading.value = false;
                }
            };
            
            // 查询并比对目标版本
            const compareTarget = async () => {
                if (!targetForm.serialNumber.trim()) {
                    ElMessage.warning('请输入待比对SN号');
                    return;
                }
                
                // 检查比对基准值是否足够进行比对（至少批次号(工单号)不为空）
                const hasValidBaseline = baselineValues.workOrderNumber && baselineValues.workOrderNumber.trim() !== '';
                if (!hasValidBaseline) {
                    ElMessage.warning('请先设置比对基准值中的批次号(工单号)');
                    return;
                }
                
                try {
                    targetLoading.value = true;
                    const result = await queryTargetInfo(targetForm.serialNumber.trim());
                    targetForm.firmwareInfo = result;
                    
                    // 执行版本比对
                    await performComparison(result);
                    
                    ElMessage.success('比对完成');
                } catch (error) {
                    Logger.error('查询待比对版本失败:', error);
                    ElMessage.error('查询失败: ' + error.message);
                    targetForm.firmwareInfo = null;
                    comparisonResult.value = null;
                } finally {
                    targetLoading.value = false;
                }
            };
            
            // 执行版本比对
            const performComparison = async (targetInfo) => {
                try {
                    const baseline = baselineValues;
                    const target = targetInfo;
                    
                    // 准备比对数据
                    const comparisonData = {
                        baseline: {
                            software_version: baseline.softwareVersion,
                            build_time: baseline.buildTime,
                            backplane_version: baseline.backplaneVersion,
                            io_version: baseline.highSpeedIO,
                            work_order: baseline.workOrderNumber
                        },
                        target: {
                            sn: target.serialNumber,
                            software_version: target.softwareVersion,
                            build_time: target.buildTime,
                            backplane_version: target.backplaneVersion,
                            io_version: target.highSpeedIO,
                            work_order: target.workOrder
                        }
                    };
                    
                    // 调用后端比对API
                    const response = await fetch('/api/version-comparison/compare', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(comparisonData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        const comparisonResults = result.data.comparison_results;
                        const summary = result.data.summary;
                        
                        // 转换为前端格式
                        const comparisons = comparisonResults.map(item => ({
                            label: item.label === '工单号' ? '批次号(工单号)' : item.label,
                            baseline: item.baseline_value,
                            target: item.target_value,
                            isCorrect: item.is_match
                        }));
                        
                        comparisonResult.value = {
                            overall: summary.overall_status === 'PASS' ? 'success' : 'error',
                            items: comparisons
                        };
                        
                        // 语音播报比对结果
                        speakAlert(summary.all_matched ? '版本正确' : '版本错误');
                        
                        // 添加到比对记录
                        addComparisonRecord(targetInfo, summary.all_matched);
                        
                        // 版本正确时，2秒后自动清除并聚焦
                        if (summary.all_matched) {
                            setTimeout(() => {
                                targetForm.serialNumber = '';
                                targetForm.firmwareInfo = null;
                                comparisonResult.value = null;
                                
                                // 聚焦到待比对SN号输入框
                                nextTick(() => {
                                    if (targetInputRef.value) {
                                        targetInputRef.value.focus();
                                    }
                                });
                            }, 2000);
                        } else {
                            // 版本错误时，1秒后只清除输入框，保留查询信息和比对结果
                            setTimeout(() => {
                                targetForm.serialNumber = '';
                                
                                // 聚焦到待比对SN号输入框
                                nextTick(() => {
                                    if (targetInputRef.value) {
                                        targetInputRef.value.focus();
                                    }
                                });
                            }, 1000);
                        }
                    } else {
                        throw new Error(result.message || '版本比对失败');
                    }
                    
                } catch (error) {
                    Logger.error('版本比对失败:', error);
                    ElMessage.error('版本比对失败: ' + error.message);
                    
                    // 降级到本地比对
                    const baseline = baselineValues;
                    const target = targetInfo;
                    
                    const comparisons = [
                        {
                            label: '软件版本',
                            baseline: baseline.softwareVersion,
                            target: target.softwareVersion,
                            isCorrect: baseline.softwareVersion === target.softwareVersion
                        },
                        {
                            label: '构建日期',
                            baseline: baseline.buildTime,
                            target: target.buildTime,
                            isCorrect: baseline.buildTime === target.buildTime
                        },
                        {
                            label: '背板总线版本',
                            baseline: baseline.backplaneVersion,
                            target: target.backplaneVersion,
                            isCorrect: baseline.backplaneVersion === target.backplaneVersion
                        },
                        {
                            label: '高速IO版本',
                            baseline: baseline.highSpeedIO,
                            target: target.highSpeedIO,
                            isCorrect: baseline.highSpeedIO === target.highSpeedIO
                        },
                        {
                            label: '批次号(工单号)',
                            baseline: baseline.workOrderNumber,
                            target: target.workOrder,
                            isCorrect: baseline.workOrderNumber === '无' ? true : baseline.workOrderNumber === target.workOrder
                        }
                    ];
                    
                    const isAllCorrect = comparisons.every(comp => comp.isCorrect);
                    
                    comparisonResult.value = {
                        overall: isAllCorrect ? 'success' : 'error',
                        items: comparisons
                    };
                    
                    // 语音播报比对结果 (降级模式)
                    speakAlert(isAllCorrect ? '版本正确' : '版本错误');
                    
                    // 添加到比对记录
                    addComparisonRecord(targetInfo, isAllCorrect);
                    
                    // 版本正确时，2秒后自动清除并聚焦 (降级模式)
                    if (isAllCorrect) {
                        setTimeout(() => {
                            targetForm.serialNumber = '';
                            targetForm.firmwareInfo = null;
                            comparisonResult.value = null;
                            
                            // 聚焦到待比对SN号输入框
                            nextTick(() => {
                                if (targetInputRef.value) {
                                    targetInputRef.value.focus();
                                }
                            });
                        }, 2000);
                    } else {
                        // 版本错误时，1秒后只清除输入框，保留查询信息和比对结果 (降级模式)
                        setTimeout(() => {
                            targetForm.serialNumber = '';
                            
                            // 聚焦到待比对SN号输入框
                            nextTick(() => {
                                if (targetInputRef.value) {
                                    targetInputRef.value.focus();
                                }
                            });
                        }, 1000);
                    }
                }
            };
            
            // 添加比对记录 (同一SN号只记录一次)
            const addComparisonRecord = (targetInfo, isCorrect) => {
                const targetSN = targetInfo.serialNumber;
                
                // 检查是否已存在相同SN号的记录
                const existingIndex = comparisonRecords.value.findIndex(record => record.targetSN === targetSN);
                
                const record = {
                    id: existingIndex >= 0 ? comparisonRecords.value[existingIndex].id : Date.now(),
                    comparisonTime: new Date().toLocaleString('zh-CN'),
                    baselineSN: baselineForm.serialNumber,
                    targetSN: targetSN,
                    workOrder: targetInfo.workOrder,
                    batchNumber: targetInfo.batchNumber || '无',
                    customerName: targetInfo.customerName || '未知客户',
                    productModel: targetInfo.model,
                    softwareVersion: targetInfo.softwareVersion,
                    buildTime: targetInfo.buildTime,
                    highSpeedIO: targetInfo.highSpeedIO,
                    backplaneVersion: targetInfo.backplaneVersion,
                    testStatus: targetInfo.status,
                    result: isCorrect ? 'success' : 'error'
                };
                
                if (existingIndex >= 0) {
                    // 更新现有记录
                    comparisonRecords.value[existingIndex] = record;
                } else {
                    // 添加新记录
                    comparisonRecords.value.unshift(record);
                }
            };
            
            // 重置当前数据
            const resetCurrentData = () => {
                baselineForm.serialNumber = '';
                baselineForm.firmwareInfo = null;
                targetForm.serialNumber = '';
                targetForm.firmwareInfo = null;
                comparisonResult.value = null;
                
                // 重置基准值
                baselineValues.softwareVersion = '';
                baselineValues.buildTime = '';
                baselineValues.highSpeedIO = '';
                baselineValues.backplaneVersion = '';
                baselineValues.workOrderNumber = '无';
                
                ElMessage.success('当前数据已重置');
            };
            
            // 清空比对记录
            const clearRecords = async () => {
                try {
                    await ElMessageBox.confirm(
                        '确定要清空所有比对记录吗？此操作不可恢复。',
                        '清空确认',
                        {
                            confirmButtonText: '确定清空',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );
                    
                    comparisonRecords.value = [];
                    ElMessage.success('比对记录已清空');
                } catch (error) {
                    // 用户取消操作
                }
            };
            
            // 导出比对记录
            const exportRecords = () => {
                if (comparisonRecords.value.length === 0) {
                    ElMessage.warning('暂无数据可导出');
                    return;
                }
                
                try {
                    // 构建CSV数据
                    const headers = [
                        '比对时间',
                        '批次号(工单号)',
                        '批次号',
                        '客户名称',
                        '产品型号',
                        'SN号',
                        '软件版本',
                        '构建日期', 
                        '背板总线版本',
                        '高速IO版本',
                        '测试状态',
                        '比对结果'
                    ];
                    
                    const csvData = [
                        headers.join(','),
                        ...comparisonRecords.value.map(record => [
                            record.comparisonTime,
                            record.workOrder || '',
                            record.batchNumber || '无',
                            record.customerName || '未知客户',
                            record.productModel,
                            record.targetSN,
                            record.softwareVersion,
                            record.buildTime,
                            record.backplaneVersion,
                            record.highSpeedIO,
                            record.testStatus,
                            record.result === 'success' ? '正确' : '错误'
                        ].join(','))
                    ].join('\n');
                    
                    // 添加BOM头以支持中文
                    const csvContent = '\uFEFF' + csvData;
                    
                    // 创建下载链接
                    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                    const link = document.createElement('a');
                    const url = URL.createObjectURL(blob);
                    link.setAttribute('href', url);
                    link.setAttribute('download', `版本比对记录_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.csv`);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    
                    ElMessage.success('导出成功');
                } catch (error) {
                    Logger.error('导出失败:', error);
                    ElMessage.error('导出失败');
                }
            };
            

            
            // 生命周期
            onMounted(() => {
                Logger.log('Version Comparison page mounted');
                
                // 监听数据更新事件
                const onDataUpdate = () => {
                    Logger.log('Data updated in version comparison page');
                };
                
                eventListeners = [
                    { event: 'global-data-refresh', handler: onDataUpdate }
                ];
                
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.on(event, handler);
                });
            });
            
            onUnmounted(() => {
                // 清理事件监听器
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.off(event, handler);
                });
            });
            
            return {
                // 数据
                loading,
                baselineLoading,
                targetLoading,
                baselineForm,
                baselineValues,
                targetForm,
                comparisonResult,
                comparisonRecords,
                statistics,
                
                // 方法
                queryBaseline,
                compareTarget,
                resetCurrentData,
                clearRecords,
                exportRecords,
                
                // 引用
                baselineInputRef,
                targetInputRef
            };
        },
        
        template: `
        <main class="firmware-container version-comparison firmware-page" role="main">
            <!-- 主要内容区域 -->
            <section class="version-comparison__main">
                <!-- 基准版本查询区域 -->
                <article class="version-comparison__card version-comparison__baseline">
                    <header class="version-comparison__card-header">
                        <el-icon><Search /></el-icon>
                        <div>
                            <h2 class="version-comparison__card-title">基准版本查询</h2>
                            <p class="version-comparison__card-subtitle">设置比对基准</p>
                        </div>
                    </header>
                    
                    <div class="version-comparison__baseline-query">
                        <div class="version-comparison__baseline-input">
                            <el-input 
                                ref="baselineInputRef"
                                v-model="baselineForm.serialNumber" 
                                placeholder="基准SN号 (如: SN001)"
                                @keyup.enter="queryBaseline"
                                clearable
                                aria-label="基准SN号输入">
                            </el-input>
                            <el-button 
                                type="primary" 
                                @click="queryBaseline"
                                :loading="baselineLoading"
                                aria-label="查询基准版本">
                                查询
                            </el-button>
                        </div>
                        
                        <div class="version-comparison__baseline-info" 
                             :class="{ 'version-comparison__baseline-info--empty': !baselineForm.firmwareInfo }">
                            <template v-if="baselineForm.firmwareInfo">
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">固件状态:</span>
                                    <el-tag 
                                        :type="baselineForm.firmwareInfo.status === 'active' ? 'success' : 'warning'"
                                        size="small">
                                        {{ baselineForm.firmwareInfo.status === 'active' ? '最新' : '非最新' }}
                                    </el-tag>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">软件版本:</span>
                                    <span class="version-comparison__info-value">{{ baselineForm.firmwareInfo.softwareVersion }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">构建日期:</span>
                                    <span class="version-comparison__info-value">{{ baselineForm.firmwareInfo.buildTime }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">背板总线版本:</span>
                                    <span class="version-comparison__info-value">{{ baselineForm.firmwareInfo.backplaneVersion }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">高速IO版本:</span>
                                    <span class="version-comparison__info-value">{{ baselineForm.firmwareInfo.highSpeedIO }}</span>
                                </div>
                            </template>
                            <template v-else>
                                <p>请查询基准版本信息</p>
                            </template>
                        </div>
                    </div>
                    
                    <section class="version-comparison__baseline-values">
                        <h3 class="version-comparison__baseline-section-title">比对基准值</h3>
                        
                        <div class="version-comparison__form-item">
                            <label class="version-comparison__form-label" for="baseline-software-version">软件版本</label>
                            <el-input 
                                id="baseline-software-version"
                                v-model="baselineValues.softwareVersion" 
                                size="small">
                            </el-input>
                        </div>
                        
                        <div class="version-comparison__form-item">
                            <label class="version-comparison__form-label" for="baseline-build-time">构建日期</label>
                            <el-input 
                                id="baseline-build-time"
                                v-model="baselineValues.buildTime" 
                                size="small">
                            </el-input>
                        </div>
                        
                        <div class="version-comparison__form-item">
                            <label class="version-comparison__form-label" for="baseline-backplane-version">背板总线版本</label>
                            <el-input 
                                id="baseline-backplane-version"
                                v-model="baselineValues.backplaneVersion" 
                                size="small">
                            </el-input>
                        </div>
                        
                        <div class="version-comparison__form-item">
                            <label class="version-comparison__form-label" for="baseline-high-speed-io">高速IO版本</label>
                            <el-input 
                                id="baseline-high-speed-io"
                                v-model="baselineValues.highSpeedIO" 
                                size="small">
                            </el-input>
                        </div>
                        
                        <div class="version-comparison__form-item">
                            <label class="version-comparison__form-label" for="baseline-work-order">批次号(工单号)</label>
                            <el-input 
                                id="baseline-work-order"
                                v-model="baselineValues.workOrderNumber" 
                                size="small">
                            </el-input>
                        </div>
                    </section>
                </article>
                
                <!-- 待比对版本查询区域 -->
                <article class="version-comparison__card version-comparison__target">
                    <header class="version-comparison__card-header">
                        <el-icon><Search /></el-icon>
                        <div>
                            <h2 class="version-comparison__card-title">待比对版本查询</h2>
                            <p class="version-comparison__card-subtitle">输入待比对SN号</p>
                        </div>
                    </header>
                    
                    <div class="version-comparison__target-query">
                        <div class="version-comparison__target-input">
                            <el-input 
                                ref="targetInputRef"
                                v-model="targetForm.serialNumber" 
                                placeholder="待比对SN号 (如: SN002)"
                                @keyup.enter="compareTarget"
                                clearable
                                aria-label="待比对SN号输入">
                            </el-input>
                            <el-button 
                                type="primary" 
                                @click="compareTarget"
                                :loading="targetLoading"
                                aria-label="执行版本比对">
                                比对
                            </el-button>
                        </div>
                        
                        <div class="version-comparison__target-info" 
                             :class="{ 'version-comparison__target-info--empty': !targetForm.firmwareInfo }">
                            <template v-if="targetForm.firmwareInfo">
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">批次号(工单号):</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.workOrder }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">批次号:</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.batchNumber || '无' }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">客户名称:</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.customerName || '未知客户' }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">产品型号:</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.model }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">软件版本:</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.softwareVersion }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">构建日期:</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.buildTime }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">背板总线版本:</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.backplaneVersion }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">高速IO版本:</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.highSpeedIO }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">测试状态:</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.status }}</span>
                                </div>
                                <div class="version-comparison__info-item">
                                    <span class="version-comparison__info-label">测试时间:</span>
                                    <span class="version-comparison__info-value">{{ targetForm.firmwareInfo.timestamp }}</span>
                                </div>
                            </template>
                            <template v-else>
                                <p>请输入待比对SN号进行查询</p>
                            </template>
                        </div>
                    </div>
                    
                    <section v-if="comparisonResult" class="version-comparison__comparison-result">
                        <header class="version-comparison__result-header">
                            <h3>比对结果</h3>
                            <el-tag 
                                :type="comparisonResult.overall === 'success' ? 'success' : 'danger'"
                                class="version-comparison__result-badge"
                                :aria-label="comparisonResult.overall === 'success' ? '版本正确' : '版本错误'">
                                {{ comparisonResult.overall === 'success' ? '版本正确' : '版本错误' }}
                            </el-tag>
                        </header>
                        
                        <div class="version-comparison__result-items">
                            <div v-for="item in comparisonResult.items" 
                                 :key="item.label"
                                 class="version-comparison__result-item">
                                <span class="version-comparison__result-label">{{ item.label }}:</span>
                                <el-tag 
                                    :type="item.isCorrect ? 'success' : 'danger'"
                                    size="small"
                                    class="version-comparison__result-status"
                                    :aria-label="item.isCorrect ? '正确' : '错误'">
                                    {{ item.isCorrect ? '正确' : '错误' }}
                                </el-tag>
                            </div>
                        </div>
                    </section>
                </article>
                
                <!-- 操作面板区域 -->
                <aside class="version-comparison__card version-comparison__panel">
                    <header class="version-comparison__card-header">
                        <el-icon><Setting /></el-icon>
                        <div>
                            <h2 class="version-comparison__card-title">操作面板</h2>
                            <p class="version-comparison__card-subtitle">系统操作和统计</p>
                        </div>
                    </header>
                    
                    <section class="version-comparison__panel-section">
                        <div class="version-comparison__panel-actions">
                            <el-button 
                                class="version-comparison__panel-button"
                                @click="resetCurrentData"
                                aria-label="重置当前数据">
                                <el-icon><Refresh /></el-icon>
                                重置当前数据
                            </el-button>
                            <el-button 
                                class="version-comparison__panel-button"
                                type="danger"
                                @click="clearRecords"
                                aria-label="清空比对记录">
                                <el-icon><Delete /></el-icon>
                                清空比对记录
                            </el-button>
                        </div>
                    </section>
                    
                    <section class="version-comparison__panel-section version-comparison__stats">
                        <h3 class="version-comparison__stats-title">统计信息</h3>
                        <div class="version-comparison__stats-grid" role="group" aria-label="统计信息">
                            <div class="version-comparison__stats-item">
                                <span class="version-comparison__stats-label">总比对次数:</span>
                                <span class="version-comparison__stats-value">{{ statistics.total }}</span>
                            </div>
                            <div class="version-comparison__stats-item">
                                <span class="version-comparison__stats-label">版本正确:</span>
                                <span class="version-comparison__stats-value version-comparison__stats-value--success">{{ statistics.correct }}</span>
                            </div>
                            <div class="version-comparison__stats-item">
                                <span class="version-comparison__stats-label">版本错误:</span>
                                <span class="version-comparison__stats-value version-comparison__stats-value--error">{{ statistics.error }}</span>
                            </div>
                            <div class="version-comparison__stats-item">
                                <span class="version-comparison__stats-label">正确率:</span>
                                <span class="version-comparison__stats-value">{{ statistics.accuracy }}%</span>
                            </div>
                        </div>
                    </section>
                    
                    <section class="version-comparison__panel-section version-comparison__usage">
                        <h3 class="version-comparison__usage-title">使用说明</h3>
                        <ol class="version-comparison__usage-list">
                            <li class="version-comparison__usage-item">输入基准SN号查询基准版本（可选）</li>
                            <li class="version-comparison__usage-item">如固件状态非活跃，请到"所有固件"页面查询相关版本并手动填入基准值</li>
                            <li class="version-comparison__usage-item">确保比对基准值中至少批次号(工单号)字段有值</li>
                            <li class="version-comparison__usage-item">输入待比对SN号进行版本比对</li>
                            <li class="version-comparison__usage-item">批次号(工单号)比对逻辑：基准值为"无"时直接显示正确，为其他值时进行实际比对</li>
                            <li class="version-comparison__usage-item">查看比对结果和历史记录</li>
                        </ol>
                    </section>
                </aside>
            </section>
            
            <!-- 比对记录表格区域 -->
            <section class="version-comparison__records">
                <header class="version-comparison__records-header">
                    <div>
                        <h2 class="version-comparison__records-title">
                            <el-icon><Clock /></el-icon>
                            比对记录表格
                            <span class="version-comparison__records-count">{{ comparisonRecords.length }} 条记录</span>
                        </h2>
                        <p class="version-comparison__records-subtitle">记录所有版本比对的详细信息和结果</p>
                    </div>
                    <div class="version-comparison__records-actions">
                        <el-button 
                            type="primary" 
                            @click="exportRecords"
                            :disabled="comparisonRecords.length === 0"
                            aria-label="导出比对记录">
                            <el-icon><Download /></el-icon>
                            导出记录
                        </el-button>
                    </div>
                </header>
                
                <div v-if="comparisonRecords.length === 0" class="version-comparison__records-empty">
                    <div class="version-comparison__records-empty-icon" aria-hidden="true">
                        <el-icon><Clock /></el-icon>
                    </div>
                    <p class="version-comparison__records-empty-text">暂无比对记录</p>
                    <p class="version-comparison__records-empty-desc">完成版本比对后，记录将显示在这里</p>
                </div>
                
                <el-table 
                    v-else
                    :data="comparisonRecords" 
                    class="version-comparison__records-table"
                    stripe
                    border
                    size="small"
                    style="width: 100%"
                    aria-label="版本比对记录表格">
                    
                    <el-table-column prop="workOrder" label="批次号(工单号)" align="center" min-width="120"></el-table-column>
                    <el-table-column prop="batchNumber" label="批次号" align="center" min-width="100"></el-table-column>
                    <el-table-column prop="customerName" label="客户名称" align="center" min-width="120"></el-table-column>
                    <el-table-column prop="productModel" label="产品型号" align="center" min-width="120"></el-table-column>
                    <el-table-column prop="targetSN" label="SN号" align="center" min-width="100"></el-table-column>
                    <el-table-column prop="softwareVersion" label="软件版本" align="center" min-width="100"></el-table-column>
                    <el-table-column prop="buildTime" label="构建日期" align="center" min-width="100"></el-table-column>
                    <el-table-column prop="backplaneVersion" label="背板总线版本" align="center" min-width="130"></el-table-column>
                    <el-table-column prop="highSpeedIO" label="高速IO版本" align="center" min-width="130"></el-table-column>
                    <el-table-column prop="testStatus" label="测试状态" align="center" min-width="100"></el-table-column>
                    
                    <el-table-column label="比对结果" align="center" min-width="100">
                        <template #default="{ row }">
                            <el-tag 
                                :type="row.result === 'success' ? 'success' : 'danger'" 
                                size="small"
                                :aria-label="row.result === 'success' ? '正确' : '错误'">
                                {{ row.result === 'success' ? '正确' : '错误' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </section>
        </main>
        `
    };

    // 创建并挂载Vue应用
    const app = createApp(VersionComparisonApp);
    app.use(ElementPlus);

    // 注册Element Plus图标
    if (window.ElementPlusIconsVue) {
        for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
            app.component(key, component);
        }
    }

    // 挂载应用
    const mountApp = () => {
        try {
            app.mount('#version-comparison-app-container');
            Logger.log('Version Comparison Vue app mounted successfully');
            
            // 保存应用实例供清理使用
            window.currentFirmwareApp = app;
        } catch (error) {
            Logger.error('Failed to mount Version Comparison Vue app:', error);
        }
    };

    // Defer mounting until the DOM is fully loaded to prevent race conditions
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', mountApp);
    } else {
        mountApp();
    }
})(); 