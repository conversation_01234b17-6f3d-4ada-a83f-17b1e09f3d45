<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PLC生产车间检验流程</title>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
        }
        
        :root {
            /* shadcn/ui 风格颜色变量 */
            --background: #ffffff;
            --foreground: #020817;
            --card: #ffffff;
            --card-foreground: #020817;
            --popover: #ffffff;
            --popover-foreground: #020817;
            --primary: #0f172a;
            --primary-foreground: #f8fafc;
            --secondary: #f1f5f9;
            --secondary-foreground: #0f172a;
            --muted: #f1f5f9;
            --muted-foreground: #64748b;
            --accent: #f1f5f9;
            --accent-foreground: #0f172a;
            --destructive: #ef4444;
            --destructive-foreground: #f8fafc;
            --border: #e2e8f0;
            --input: #e2e8f0;
            --ring: #0f172a;
            --radius: 0.5rem;
            
            --success: #10b981;
            --success-foreground: #f8fafc;
            --info: #3b82f6;
            --info-foreground: #f8fafc;
            --warning: #f59e0b;
            --warning-foreground: #f8fafc;
        }
        
        body {
            background-color: var(--muted);
            color: var(--foreground);
            line-height: 1.5;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: var(--background);
            border-radius: var(--radius);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 24px;
        }
        
        /* 标题区域 */
        .header {
            padding: 28px;
            background-color: var(--secondary);
            border-radius: var(--radius);
            margin-bottom: 24px;
            border: 1px solid var(--border);
        }
        
        .header h1 {
            font-size: 24px;
            color: var(--primary);
            margin-bottom: 24px;
            font-weight: 600;
        }
        
        /* 工单信息区域 */
        .order-info {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .input-group {
            flex: 1;
            min-width: 250px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
            color: var(--muted-foreground);
        }
        
        .input-group label .required {
            color: var(--destructive);
            margin-left: 4px;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid var(--input);
            border-radius: calc(var(--radius) - 2px);
            font-size: 14px;
            transition: all 0.2s;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .input-group input:hover {
            border-color: var(--muted-foreground);
        }
        
        .input-group input:focus {
            outline: none;
            border-color: var(--ring);
            box-shadow: 0 0 0 3px rgba(15, 23, 42, 0.1);
        }
        
        .input-group.error input {
            border-color: var(--destructive);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
        
        .error-message {
            color: var(--destructive);
            font-size: 12px;
            margin-top: 6px;
            display: none;
        }
        
        .input-group.error .error-message {
            display: block;
        }
        
        /* 进度条 */
        .progress-container {
            margin-top: 24px;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--muted-foreground);
        }
        
        .progress-bar {
            height: 8px;
            background-color: var(--muted);
            border-radius: 100px;
            overflow: hidden;
        }
        
        .progress-inner {
            height: 100%;
            background-color: var(--info);
            border-radius: 100px;
            transition: width 0.3s ease;
        }
        
        /* 时间线 */
        .timeline {
            margin-top: 36px;
            position: relative;
        }
        
        .timeline-line {
            position: absolute;
            left: 15px;
            top: 30px;
            bottom: 0;
            width: 2px;
            background-color: var(--border);
        }
        
        /* 阶段卡片 */
        .stage {
            position: relative;
            padding-left: 50px;
            margin-bottom: 36px;
        }
        
        .stage-node {
            position: absolute;
            left: 0;
            top: 18px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--background);
            border: 2px solid var(--info);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
            color: var(--info);
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }
        
        .stage-node.completed {
            background-color: var(--success);
            border-color: var(--success);
            color: var(--success-foreground);
        }
        
        .stage-node.locked {
            background-color: var(--muted);
            border-color: var(--muted-foreground);
            color: var(--muted-foreground);
            opacity: 0.8;
        }
        
        .stage-card {
            border: 1px solid var(--border);
            border-radius: var(--radius);
            overflow: hidden;
            transition: all 0.3s;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .stage-header {
            padding: 16px 20px;
            background-color: var(--secondary);
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .stage-header:hover {
            background-color: var(--accent);
        }
        
        .stage-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary);
        }
        
        .stage-description {
            font-size: 14px;
            color: var(--muted-foreground);
            margin-top: 4px;
        }
        
        .stage-status {
            display: flex;
            align-items: center;
        }
        
        .stage-progress {
            margin-right: 12px;
            font-size: 14px;
            color: var(--muted-foreground);
            font-weight: 500;
        }
        
        .stage-toggle {
            color: var(--primary);
            font-size: 14px;
            display: flex;
            align-items: center;
            font-weight: 500;
        }
        
        .stage-toggle::after {
            content: "▼";
            margin-left: 6px;
            transition: transform 0.3s;
            font-size: 10px;
        }
        
        .stage-toggle.collapsed::after {
            transform: rotate(-90deg);
        }
        
        .stage-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .stage-content.expanded {
            max-height: 2000px;
        }
        
        /* 检验类型选项卡 */
        .inspection-tabs {
            display: flex;
            border-bottom: 1px solid var(--border);
        }
        
        .inspection-tab {
            flex: 1;
            text-align: center;
            padding: 14px 0;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
            font-size: 14px;
            position: relative;
        }
        
        .inspection-tab.active {
            color: var(--primary);
        }
        
        .inspection-tab.active::after {
            content: "";
            position: absolute;
            bottom: -1px;
            left: 25%;
            width: 50%;
            height: 2px;
            background-color: var(--primary);
            border-radius: 2px;
        }
        
        .inspection-tab:hover {
            background-color: var(--accent);
        }
        
        /* 操作人员信息区域 */
        .operator-info {
            background-color: var(--secondary);
            padding: 16px;
            margin: 16px;
            border-radius: calc(var(--radius) - 2px);
            border: 1px solid var(--border);
            transition: all 0.3s;
        }
        
        .operator-info.submitted {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.2);
        }
        
        .operator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .operator-label {
            font-weight: 500;
            color: var(--primary);
        }
        
        .submitted-tag {
            background-color: var(--success);
            color: var(--success-foreground);
            padding: 2px 8px;
            border-radius: var(--radius);
            font-size: 12px;
            font-weight: 500;
        }
        
        .operator-time {
            margin-top: 6px;
            font-size: 14px;
            color: var(--muted-foreground);
        }
        
        .operator-input {
            margin-top: 6px;
        }
        
        /* 检验项目列表 */
        .inspection-content {
            padding: 16px;
            display: none;
        }
        
        .inspection-content.active {
            display: block;
            animation: fadeIn 0.3s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .select-all {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--border);
        }
        
        .checkbox-container {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: calc(var(--radius) - 2px);
            transition: background-color 0.2s;
        }
        
        .checkbox-container:hover {
            background-color: var(--secondary);
        }
        
        .checkbox-container input {
            margin-right: 10px;
            margin-top: 3px;
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid var(--muted-foreground);
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            transition: all 0.2s;
        }
        
        .checkbox-container input:checked {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        
        .checkbox-container input:checked::after {
            content: "✓";
            color: var(--primary-foreground);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
        }
        
        .checkbox-container input:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .checkbox-container label {
            cursor: pointer;
            flex: 1;
            font-size: 14px;
            padding-top: 1px;
        }
        
        /* 附件区域 */
        .attachments {
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid var(--border);
        }
        
        .attachments-title {
            font-weight: 500;
            margin-bottom: 12px;
            color: var(--primary);
            font-size: 14px;
        }
        
        .attachment-item {
            display: flex;
            align-items: center;
            padding: 10px 12px;
            border: 1px solid var(--border);
            border-radius: calc(var(--radius) - 2px);
            margin-bottom: 8px;
            background-color: var(--background);
            transition: all 0.2s;
        }
        
        .attachment-item:hover {
            border-color: var(--muted-foreground);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }
        
        .attachment-icon {
            margin-right: 12px;
            color: var(--muted-foreground);
            font-size: 18px;
        }
        
        .attachment-info {
            flex: 1;
        }
        
        .attachment-name {
            font-size: 14px;
            color: var(--primary);
        }
        
        .attachment-size {
            font-size: 12px;
            color: var(--muted-foreground);
            margin-top: 2px;
        }
        
        .attachment-delete {
            color: var(--destructive);
            cursor: pointer;
            margin-left: 10px;
            font-size: 16px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }
        
        .attachment-delete:hover {
            background-color: rgba(239, 68, 68, 0.1);
        }
        
        .upload-btn {
            margin-top: 12px;
            padding: 8px 16px;
            background-color: var(--secondary);
            color: var(--primary);
            border: 1px solid var(--border);
            border-radius: calc(var(--radius) - 2px);
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            font-weight: 500;
        }
        
        .upload-btn:hover {
            background-color: var(--accent);
            border-color: var(--muted-foreground);
        }
        
        /* 底部操作区域 */
        .action-bar {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid var(--border);
        }
        
        .btn {
            padding: 10px 18px;
            border-radius: calc(var(--radius) - 2px);
            cursor: pointer;
            border: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-reset {
            background-color: var(--secondary);
            color: var(--secondary-foreground);
            border: 1px solid var(--border);
        }
        
        .btn-reset:hover {
            background-color: var(--accent);
            border-color: var(--muted-foreground);
        }
        
        .btn-submit {
            background-color: var(--primary);
            color: var(--primary-foreground);
            border: 1px solid var(--primary);
        }
        
        .btn-submit:hover {
            opacity: 0.9;
            box-shadow: 0 2px 6px rgba(15, 23, 42, 0.2);
        }
        
        .btn-disabled {
            background-color: var(--muted);
            color: var(--muted-foreground);
            cursor: not-allowed;
            border: 1px solid var(--border);
        }
        
        .btn-disabled:hover {
            background-color: var(--muted);
            box-shadow: none;
        }
        
        .completed-tag {
            background-color: var(--success);
            color: var(--success-foreground);
            padding: 8px 16px;
            border-radius: calc(var(--radius) - 2px);
            font-size: 14px;
            font-weight: 500;
        }
        
        /* 对话框 */
        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
            backdrop-filter: blur(2px);
        }
        
        .dialog-overlay.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .dialog {
            background-color: var(--background);
            border-radius: var(--radius);
            width: 400px;
            max-width: 90%;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
            transform: translateY(20px);
            transition: all 0.3s;
            border: 1px solid var(--border);
        }
        
        .dialog-overlay.visible .dialog {
            transform: translateY(0);
        }
        
        .dialog-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border);
        }
        
        .dialog-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary);
        }
        
        .dialog-body {
            padding: 20px;
        }
        
        .dialog-message {
            font-size: 14px;
            margin-bottom: 16px;
            color: var(--muted-foreground);
        }
        
        .dialog-footer {
            padding: 12px 20px 20px;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        /* 文件上传对话框 */
        .file-list {
            margin-bottom: 16px;
        }
        
        .file-upload-input {
            display: none;
        }
        
        /* 通知提示 */
        .notification {
            position: fixed;
            bottom: 24px;
            right: 24px;
            padding: 16px 20px;
            background-color: var(--background);
            border-radius: var(--radius);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: flex-start;
            transform: translateX(120%);
            transition: transform 0.3s, opacity 0.3s;
            z-index: 9998;
            border: 1px solid var(--border);
            max-width: 380px;
            opacity: 0;
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .notification-icon {
            margin-right: 12px;
            font-size: 20px;
        }
        
        .notification-icon.warning {
            color: var(--warning);
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: var(--primary);
        }
        
        .notification-message {
            font-size: 14px;
            color: var(--muted-foreground);
        }
        
        /* 完成消息 */
        .completion-message {
            background-color: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: var(--radius);
            padding: 40px 30px;
            text-align: center;
            margin-top: 36px;
            display: none;
        }
        
        .completion-icon {
            font-size: 48px;
            color: var(--success);
            margin-bottom: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            background-color: rgba(16, 185, 129, 0.1);
            border-radius: 50%;
        }
        
        .completion-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--primary);
        }
        
        .completion-info {
            font-size: 16px;
            color: var(--muted-foreground);
            margin-bottom: 20px;
        }
        
        .completion-details {
            display: inline-block;
            text-align: left;
            margin: 0 auto;
            background-color: var(--background);
            padding: 16px 24px;
            border-radius: calc(var(--radius) - 2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
        }
        
        .completion-details p {
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--muted-foreground);
        }
        
        .completion-details p span {
            font-weight: 500;
            color: var(--primary);
            margin-left: 4px;
        }
        
        /* 响应式布局 */
        @media (max-width: 768px) {
            .order-info {
                flex-direction: column;
            }
            
            .input-group {
                width: 100%;
            }
            
            .container {
                padding: 16px;
            }
            
            .header {
                padding: 20px;
            }
            
            .stage {
                padding-left: 40px;
            }
            
            .stage-node {
                width: 28px;
                height: 28px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 标题区域 -->
        <div class="header">
            <h1>PLC生产车间检验流程</h1>
            <div class="order-info">
                <div id="orderNoGroup" class="input-group">
                    <label for="orderNo">工单号 <span class="required">*</span></label>
                    <input type="text" id="orderNo" placeholder="请输入工单号">
                    <div class="error-message">工单号不能为空</div>
                </div>
                <div class="input-group">
                    <label for="serialNo">产品SN号</label>
                    <input type="text" id="serialNo" placeholder="请输入产品SN号">
                </div>
            </div>
            <div class="progress-container">
                <div class="progress-label">
                    <span>总体进度</span>
                    <span id="progressPercentage">0%</span>
                </div>
                <div class="progress-bar">
                    <div id="progressInner" class="progress-inner" style="width: 0%"></div>
                </div>
            </div>
        </div>
        
        <!-- 时间线 -->
        <div class="timeline">
            <div class="timeline-line"></div>
            
            <!-- 组装前阶段 -->
            <div id="preAssemblyStage" class="stage">
                <div class="stage-node">1</div>
                <div class="stage-card">
                    <div class="stage-header" onclick="toggleStage('preAssembly')">
                        <div>
                            <div class="stage-title">组装前阶段</div>
                            <div class="stage-description">完成组装前的检测后进入测试前阶段</div>
                        </div>
                        <div class="stage-status">
                            <div id="preAssemblyProgress" class="stage-progress">0%</div>
                            <div id="preAssemblyToggle" class="stage-toggle">展开</div>
                        </div>
                    </div>
                    <div id="preAssemblyContent" class="stage-content expanded">
                        <div class="inspection-tabs">
                            <div class="inspection-tab active" onclick="switchTab('preAssembly', 'production')">产线自检</div>
                            <div class="inspection-tab" onclick="switchTab('preAssembly', 'qc')">QC自检</div>
                        </div>
                        
                        <!-- 产线自检内容 -->
                        <div id="preAssembly-production" class="inspection-content active">
                            <div id="preAssembly-production-operator" class="operator-info">
                                <div class="operator-header">
                                    <span class="operator-label">操作人员: <span class="required">*</span></span>
                                </div>
                                <div class="operator-input">
                                    <input type="text" placeholder="请输入操作人员姓名">
                                </div>
                            </div>
                            
                            <div class="select-all">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="preAssembly-production-selectAll" onchange="toggleSelectAll('preAssembly', 'production')">
                                    <label for="preAssembly-production-selectAll">全选</label>
                                </div>
                            </div>
                            
                            <div id="preAssembly-production-items" class="inspection-items">
                                <!-- 检验项目将通过JS动态生成 -->
                            </div>
                            
                            <div class="attachments">
                                <div class="attachments-title">已上传文件</div>
                                <div id="preAssembly-production-files" class="attachment-list">
                                    <!-- 附件将通过JS动态生成 -->
                                </div>
                                <button class="upload-btn" onclick="showUploadDialog('preAssembly', 'production')">上传附件</button>
                            </div>
                            
                            <div class="action-bar">
                                <button class="btn btn-reset" onclick="resetInspection('preAssembly', 'production')">重置</button>
                                <button class="btn btn-submit" onclick="submitInspection('preAssembly', 'production')">提交产线自检</button>
                            </div>
                        </div>
                        
                        <!-- QC自检内容 -->
                        <div id="preAssembly-qc" class="inspection-content">
                            <div id="preAssembly-qc-operator" class="operator-info">
                                <div class="operator-header">
                                    <span class="operator-label">操作人员: <span class="required">*</span></span>
                                </div>
                                <div class="operator-input">
                                    <input type="text" placeholder="请输入操作人员姓名">
                                </div>
                            </div>
                            
                            <div class="select-all">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="preAssembly-qc-selectAll" onchange="toggleSelectAll('preAssembly', 'qc')">
                                    <label for="preAssembly-qc-selectAll">全选</label>
                                </div>
                            </div>
                            
                            <div id="preAssembly-qc-items" class="inspection-items">
                                <!-- 检验项目将通过JS动态生成 -->
                            </div>
                            
                            <div class="attachments">
                                <div class="attachments-title">已上传文件</div>
                                <div id="preAssembly-qc-files" class="attachment-list">
                                    <!-- 附件将通过JS动态生成 -->
                                </div>
                                <button class="upload-btn" onclick="showUploadDialog('preAssembly', 'qc')">上传附件</button>
                            </div>
                            
                            <div class="action-bar">
                                <button class="btn btn-reset" onclick="resetInspection('preAssembly', 'qc')">重置</button>
                                <button class="btn btn-submit" onclick="submitInspection('preAssembly', 'qc')">提交QC自检</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 测试前阶段 -->
            <div id="preTestingStage" class="stage">
                <div class="stage-node locked">2</div>
                <div class="stage-card">
                    <div class="stage-header" onclick="toggleStage('preTesting')">
                        <div>
                            <div class="stage-title">测试前阶段</div>
                            <div class="stage-description">完成测试前的检测后进入包装前阶段</div>
                        </div>
                        <div class="stage-status">
                            <div id="preTestingProgress" class="stage-progress">0%</div>
                            <div id="preTestingToggle" class="stage-toggle collapsed">展开</div>
                        </div>
                    </div>
                    <div id="preTestingContent" class="stage-content">
                        <div class="inspection-tabs">
                            <div class="inspection-tab active" onclick="switchTab('preTesting', 'production')">产线自检</div>
                            <div class="inspection-tab" onclick="switchTab('preTesting', 'qc')">QC自检</div>
                        </div>
                        
                        <!-- 产线自检内容 -->
                        <div id="preTesting-production" class="inspection-content active">
                            <div id="preTesting-production-operator" class="operator-info">
                                <div class="operator-header">
                                    <span class="operator-label">操作人员: <span class="required">*</span></span>
                                </div>
                                <div class="operator-input">
                                    <input type="text" placeholder="请输入操作人员姓名">
                                </div>
                            </div>
                            
                            <div class="select-all">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="preTesting-production-selectAll" onchange="toggleSelectAll('preTesting', 'production')">
                                    <label for="preTesting-production-selectAll">全选</label>
                                </div>
                            </div>
                            
                            <div id="preTesting-production-items" class="inspection-items">
                                <!-- 检验项目将通过JS动态生成 -->
                            </div>
                            
                            <div class="attachments">
                                <div class="attachments-title">已上传文件</div>
                                <div id="preTesting-production-files" class="attachment-list">
                                    <!-- 附件将通过JS动态生成 -->
                                </div>
                                <button class="upload-btn" onclick="showUploadDialog('preTesting', 'production')">上传附件</button>
                            </div>
                            
                            <div class="action-bar">
                                <button class="btn btn-reset" onclick="resetInspection('preTesting', 'production')">重置</button>
                                <button class="btn btn-submit" onclick="submitInspection('preTesting', 'production')">提交产线自检</button>
                            </div>
                        </div>
                        
                        <!-- QC自检内容 -->
                        <div id="preTesting-qc" class="inspection-content">
                            <div id="preTesting-qc-operator" class="operator-info">
                                <div class="operator-header">
                                    <span class="operator-label">操作人员: <span class="required">*</span></span>
                                </div>
                                <div class="operator-input">
                                    <input type="text" placeholder="请输入操作人员姓名">
                                </div>
                            </div>
                            
                            <div class="select-all">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="preTesting-qc-selectAll" onchange="toggleSelectAll('preTesting', 'qc')">
                                    <label for="preTesting-qc-selectAll">全选</label>
                                </div>
                            </div>
                            
                            <div id="preTesting-qc-items" class="inspection-items">
                                <!-- 检验项目将通过JS动态生成 -->
                            </div>
                            
                            <div class="attachments">
                                <div class="attachments-title">已上传文件</div>
                                <div id="preTesting-qc-files" class="attachment-list">
                                    <!-- 附件将通过JS动态生成 -->
                                </div>
                                <button class="upload-btn" onclick="showUploadDialog('preTesting', 'qc')">上传附件</button>
                            </div>
                            
                            <div class="action-bar">
                                <button class="btn btn-reset" onclick="resetInspection('preTesting', 'qc')">重置</button>
                                <button class="btn btn-submit" onclick="submitInspection('preTesting', 'qc')">提交QC自检</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 包装前阶段 -->
            <div id="prePackagingStage" class="stage">
                <div class="stage-node locked">3</div>
                <div class="stage-card">
                    <div class="stage-header" onclick="toggleStage('prePackaging')">
                        <div>
                            <div class="stage-title">包装前阶段</div>
                            <div class="stage-description">完成包装前的检测后整个检验流程结束</div>
                        </div>
                        <div class="stage-status">
                            <div id="prePackagingProgress" class="stage-progress">0%</div>
                            <div id="prePackagingToggle" class="stage-toggle collapsed">展开</div>
                        </div>
                    </div>
                    <div id="prePackagingContent" class="stage-content">
                        <div class="inspection-tabs">
                            <div class="inspection-tab active" onclick="switchTab('prePackaging', 'production')">产线自检</div>
                            <div class="inspection-tab" onclick="switchTab('prePackaging', 'qc')">QC自检</div>
                        </div>
                        
                        <!-- 产线自检内容 -->
                        <div id="prePackaging-production" class="inspection-content active">
                            <div id="prePackaging-production-operator" class="operator-info">
                                <div class="operator-header">
                                    <span class="operator-label">操作人员: <span class="required">*</span></span>
                                </div>
                                <div class="operator-input">
                                    <input type="text" placeholder="请输入操作人员姓名">
                                </div>
                            </div>
                            
                            <div class="select-all">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="prePackaging-production-selectAll" onchange="toggleSelectAll('prePackaging', 'production')">
                                    <label for="prePackaging-production-selectAll">全选</label>
                                </div>
                            </div>
                            
                            <div id="prePackaging-production-items" class="inspection-items">
                                <!-- 检验项目将通过JS动态生成 -->
                            </div>
                            
                            <div class="attachments">
                                <div class="attachments-title">已上传文件</div>
                                <div id="prePackaging-production-files" class="attachment-list">
                                    <!-- 附件将通过JS动态生成 -->
                                </div>
                                <button class="upload-btn" onclick="showUploadDialog('prePackaging', 'production')">上传附件</button>
                            </div>
                            
                            <div class="action-bar">
                                <button class="btn btn-reset" onclick="resetInspection('prePackaging', 'production')">重置</button>
                                <button class="btn btn-submit" onclick="submitInspection('prePackaging', 'production')">提交产线自检</button>
                            </div>
                        </div>
                        
                        <!-- QC自检内容 -->
                        <div id="prePackaging-qc" class="inspection-content">
                            <div id="prePackaging-qc-operator" class="operator-info">
                                <div class="operator-header">
                                    <span class="operator-label">操作人员: <span class="required">*</span></span>
                                </div>
                                <div class="operator-input">
                                    <input type="text" placeholder="请输入操作人员姓名">
                                </div>
                            </div>
                            
                            <div class="select-all">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="prePackaging-qc-selectAll" onchange="toggleSelectAll('prePackaging', 'qc')">
                                    <label for="prePackaging-qc-selectAll">全选</label>
                                </div>
                            </div>
                            
                            <div id="prePackaging-qc-items" class="inspection-items">
                                <!-- 检验项目将通过JS动态生成 -->
                            </div>
                            
                            <div class="attachments">
                                <div class="attachments-title">已上传文件</div>
                                <div id="prePackaging-qc-files" class="attachment-list">
                                    <!-- 附件将通过JS动态生成 -->
                                </div>
                                <button class="upload-btn" onclick="showUploadDialog('prePackaging', 'qc')">上传附件</button>
                            </div>
                            
                            <div class="action-bar">
                                <button class="btn btn-reset" onclick="resetInspection('prePackaging', 'qc')">重置</button>
                                <button class="btn btn-submit" onclick="submitInspection('prePackaging', 'qc')">提交QC自检</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 完成消息 -->
        <div id="completionMessage" class="completion-message">
            <div class="completion-icon">✓</div>
            <h2 class="completion-title">检验流程已完成</h2>
            <p class="completion-info">所有阶段的检测项目均已通过，产品可以交付。</p>
            <div class="completion-details">
                <p>工单号: <span id="completionOrderNo"></span></p>
                <p>SN号: <span id="completionSerialNo"></span></p>
            </div>
        </div>
    </div>
    
    <!-- 确认对话框 -->
    <div id="confirmDialog" class="dialog-overlay">
        <div class="dialog">
            <div class="dialog-header">
                <h3 class="dialog-title">确认提交检验结果</h3>
            </div>
            <div class="dialog-body">
                <p class="dialog-message">是否确认检查完成？一旦提交将不可修改。</p>
            </div>
            <div class="dialog-footer">
                <button class="btn btn-reset" onclick="closeDialog('confirmDialog')">取消</button>
                <button id="confirmSubmitBtn" class="btn btn-submit">确认提交</button>
            </div>
        </div>
    </div>
    
    <!-- 上传附件对话框 -->
    <div id="uploadDialog" class="dialog-overlay">
        <div class="dialog">
            <div class="dialog-header">
                <h3 class="dialog-title">上传检验附件</h3>
            </div>
            <div class="dialog-body">
                <p class="dialog-message" id="uploadDialogStage">为"组装前阶段产线自检"上传附件</p>
                
                <div class="file-list" id="uploadFileList">
                    <!-- 文件列表将通过JS动态生成 -->
                </div>
                
                <input type="file" id="fileUpload" class="file-upload-input">
                <button class="btn btn-reset" onclick="document.getElementById('fileUpload').click()">选择文件</button>
            </div>
            <div class="dialog-footer">
                <button class="btn btn-reset" onclick="closeDialog('uploadDialog')">关闭</button>
            </div>
        </div>
    </div>
    
    <!-- 通知提示 -->
    <div id="notification" class="notification">
        <div id="notificationIcon" class="notification-icon warning">⚠️</div>
        <div class="notification-content">
            <div id="notificationTitle" class="notification-title">提示</div>
            <div id="notificationMessage" class="notification-message">这是一条提示消息</div>
        </div>
    </div>
    
    <script>
        // 数据结构
        const stageData = {
            preAssembly: {
                title: "组装前阶段",
                description: "完成组装前的检测后进入测试前阶段",
                items: [
                    "打标质量是否符合要求",
                    "打标参数是否正确",
                    "覆膜型号是否正确",
                    "覆膜字符、色彩是否清晰",
                    "灯盖丝印型号是否正确，字符印刷是否清晰",
                    "灯盖丝印有无缺陷",
                    "上盖丝印是否正确，字符是否清晰",
                ]
            },
            preTesting: {
                title: "测试前阶段",
                description: "完成测试前的检测后进入包装前阶段",
                items: [
                    "打标质量是否符合要求",
                    "打标参数是否正确",
                    "覆膜型号是否正确",
                    "覆膜字符、色彩是否清晰",
                    "灯盖丝印型号是否正确，字符印刷是否清晰",
                ]
            },
            prePackaging: {
                title: "包装前阶段",
                description: "完成包装前的检测后整个检验流程结束",
                items: [
                    "灯盖丝印有无缺陷",
                    "上盖丝印是否正确，字符是否清晰",
                    "包装盒是否完整",
                    "标签是否正确",
                    "产品重量是否合格"
                ]
            }
        };
        
        // 检验结果状态
        const inspectionResults = {
            preAssembly: {
                production: [],
                qc: []
            },
            preTesting: {
                production: [],
                qc: []
            },
            prePackaging: {
                production: [],
                qc: []
            }
        };
        
        // 提交状态
        const inspectionSubmissions = {
            preAssembly: {
                production: {
                    operator: "",
                    submittedAt: "",
                    isSubmitted: false
                },
                qc: {
                    operator: "",
                    submittedAt: "",
                    isSubmitted: false
                }
            },
            preTesting: {
                production: {
                    operator: "",
                    submittedAt: "",
                    isSubmitted: false
                },
                qc: {
                    operator: "",
                    submittedAt: "",
                    isSubmitted: false
                }
            },
            prePackaging: {
                production: {
                    operator: "",
                    submittedAt: "",
                    isSubmitted: false
                },
                qc: {
                    operator: "",
                    submittedAt: "",
                    isSubmitted: false
                }
            }
        };
        
        // 附件
        const attachments = {
            preAssembly: {
                production: [],
                qc: []
            },
            preTesting: {
                production: [],
                qc: []
            },
            prePackaging: {
                production: [],
                qc: []
            }
        };
        
        // 当前确认操作的阶段和类型
        let currentConfirmStage = "";
        let currentConfirmType = "";
        
        // 当前上传附件的阶段和类型
        let currentUploadStage = "";
        let currentUploadType = "";
        
        // 初始化页面
        function initPage() {
            // 初始化检验项目
            for (const stage in stageData) {
                const items = stageData[stage].items;
                inspectionResults[stage].production = Array(items.length).fill(false);
                inspectionResults[stage].qc = Array(items.length).fill(false);
                
                renderInspectionItems(stage, 'production');
                renderInspectionItems(stage, 'qc');
            }
            
            // 工单号输入框事件
            document.getElementById('orderNo').addEventListener('focus', function() {
                document.getElementById('orderNoGroup').classList.remove('error');
            });
            
            // 文件上传事件
            document.getElementById('fileUpload').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    const newFile = {
                        name: file.name,
                        size: formatFileSize(file.size),
                        type: getFileType(file.name)
                    };
                    
                    attachments[currentUploadStage][currentUploadType].push(newFile);
                    renderAttachments(currentUploadStage, currentUploadType);
                    renderUploadFileList();
                    
                    // 清空文件输入
                    e.target.value = '';
                }
            });
            
            // 确认提交按钮事件
            document.getElementById('confirmSubmitBtn').addEventListener('click', function() {
                confirmSubmission();
            });
            
            // 更新进度
            updateProgress();
        }
        
        // 渲染检验项目
        function renderInspectionItems(stage, type) {
            const container = document.getElementById(`${stage}-${type}-items`);
            container.innerHTML = '';
            
            const items = stageData[stage].items;
            const results = inspectionResults[stage][type];
            const isSubmitted = inspectionSubmissions[stage][type].isSubmitted;
            
            items.forEach((item, index) => {
                const itemId = `${stage}-${type}-item-${index}`;
                const checked = results[index];
                const disabled = isSubmitted ? 'disabled' : '';
                
                const itemHtml = `
                    <div class="checkbox-container">
                        <input type="checkbox" id="${itemId}" ${checked ? 'checked' : ''} ${disabled} onchange="updateInspectionItem('${stage}', '${type}', ${index}, this.checked)">
                        <label for="${itemId}">${item}</label>
                    </div>
                `;
                
                container.innerHTML += itemHtml;
            });
            
            // 更新全选状态
            updateSelectAllStatus(stage, type);
        }
        
        // 更新检验项目
        function updateInspectionItem(stage, type, index, checked) {
            inspectionResults[stage][type][index] = checked;
            updateSelectAllStatus(stage, type);
            updateProgress();
        }
        
        // 更新全选状态
        function updateSelectAllStatus(stage, type) {
            const results = inspectionResults[stage][type];
            const allChecked = results.length > 0 && results.every(item => item);
            const selectAllCheckbox = document.getElementById(`${stage}-${type}-selectAll`);
            
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.disabled = inspectionSubmissions[stage][type].isSubmitted;
            }
        }
        
        // 切换全选
        function toggleSelectAll(stage, type) {
            const selectAllCheckbox = document.getElementById(`${stage}-${type}-selectAll`);
            const checked = selectAllCheckbox.checked;
            
            inspectionResults[stage][type] = inspectionResults[stage][type].map(() => checked);
            renderInspectionItems(stage, type);
            updateProgress();
        }
        
        // 切换检验类型选项卡
        function switchTab(stage, type) {
            // 更新选项卡状态
            const tabContainer = document.querySelector(`#${stage}Content .inspection-tabs`);
            const tabs = tabContainer.querySelectorAll('.inspection-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            tabContainer.querySelector(`.inspection-tab:nth-child(${type === 'production' ? 1 : 2})`).classList.add('active');
            
            // 更新内容
            const contentContainer = document.querySelector(`#${stage}Content`);
            const contents = contentContainer.querySelectorAll('.inspection-content');
            contents.forEach(content => content.classList.remove('active'));
            document.getElementById(`${stage}-${type}`).classList.add('active');
        }
        
        // 切换阶段展开/折叠
        function toggleStage(stage) {
            const content = document.getElementById(`${stage}Content`);
            const toggle = document.getElementById(`${stage}Toggle`);
            
            // 检查阶段是否被锁定
            const stageNode = document.querySelector(`#${stage}Stage .stage-node`);
            if (stageNode.classList.contains('locked')) {
                showNotification('warning', '阶段未解锁', '请先完成前一阶段的检验');
                return;
            }
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                toggle.classList.add('collapsed');
                toggle.textContent = '展开';
            } else {
                content.classList.add('expanded');
                toggle.classList.remove('collapsed');
                toggle.textContent = '展开';
            }
        }
        
        // 重置检验
        function resetInspection(stage, type) {
            if (inspectionSubmissions[stage][type].isSubmitted) {
                return;
            }
            
            inspectionResults[stage][type] = inspectionResults[stage][type].map(() => false);
            document.querySelector(`#${stage}-${type}-operator .operator-input input`).value = '';
            renderInspectionItems(stage, type);
            updateProgress();
        }
        
        // 提交检验
        function submitInspection(stage, type) {
            // 验证工单号
            const orderNo = document.getElementById('orderNo').value.trim();
            if (!orderNo) {
                document.getElementById('orderNoGroup').classList.add('error');
                showNotification('warning', '请填写工单号', '提交检验前，必须先填写工单号');
                return;
            }
            
            // 验证操作人员
            const operatorInput = document.querySelector(`#${stage}-${type}-operator .operator-input input`);
            const operator = operatorInput.value.trim();
            if (!operator) {
                showNotification('warning', '请填写操作人员', '提交检验前，必须填写操作人员');
                operatorInput.focus();
                return;
            }
            
            // 验证是否所有项目已选中
            const results = inspectionResults[stage][type];
            if (!results.every(item => item)) {
                showNotification('warning', '检验未完成', '请先完成所有检验项目');
                return;
            }
            
            // 显示确认对话框
            currentConfirmStage = stage;
            currentConfirmType = type;
            openDialog('confirmDialog');
        }
        
        // 确认提交
        function confirmSubmission() {
            const stage = currentConfirmStage;
            const type = currentConfirmType;
            
            const operatorInput = document.querySelector(`#${stage}-${type}-operator .operator-input input`);
            const operator = operatorInput.value.trim();
            
            // 更新提交状态
            inspectionSubmissions[stage][type] = {
                operator: operator,
                submittedAt: getCurrentTime(),
                isSubmitted: true
            };
            
            // 更新操作人员信息区域
            const operatorInfo = document.getElementById(`${stage}-${type}-operator`);
            operatorInfo.classList.add('submitted');
            operatorInfo.innerHTML = `
                <div class="operator-header">
                    <span class="operator-label">操作人员: ${operator}</span>
                    <span class="submitted-tag">已提交</span>
                </div>
                <div class="operator-time">提交时间: ${inspectionSubmissions[stage][type].submittedAt}</div>
            `;
            
            // 更新检验项目为禁用状态
            renderInspectionItems(stage, type);
            
            // 更新提交按钮
            const actionBar = document.querySelector(`#${stage}-${type} .action-bar`);
            actionBar.innerHTML = `
                <button class="btn btn-reset btn-disabled" disabled>重置</button>
                <span class="completed-tag">已完成</span>
            `;
            
            // 关闭确认对话框
            closeDialog('confirmDialog');
            
            // 检查阶段是否完成，并解锁下一阶段
            checkStageCompletion(stage);
            
            // 更新进度
            updateProgress();
        }
        
        // 检查阶段是否完成
        function checkStageCompletion(stage) {
            const productionSubmitted = inspectionSubmissions[stage].production.isSubmitted;
            const qcSubmitted = inspectionSubmissions[stage].qc.isSubmitted;
            
            if (productionSubmitted && qcSubmitted) {
                // 更新阶段状态为已完成
                const stageNode = document.querySelector(`#${stage}Stage .stage-node`);
                stageNode.classList.add('completed');
                stageNode.innerHTML = '✓';
                
                // 解锁下一阶段
                let nextStage = null;
                if (stage === 'preAssembly') {
                    nextStage = 'preTesting';
                } else if (stage === 'preTesting') {
                    nextStage = 'prePackaging';
                }
                
                if (nextStage) {
                    unlockStage(nextStage);
                }
                
                // 检查整个流程是否完成
                checkProcessCompletion();
            }
        }
        
        // 解锁阶段
        function unlockStage(stage) {
            const stageNode = document.querySelector(`#${stage}Stage .stage-node`);
            stageNode.classList.remove('locked');
            
            // 自动展开新解锁的阶段
            const content = document.getElementById(`${stage}Content`);
            const toggle = document.getElementById(`${stage}Toggle`);
            
            content.classList.add('expanded');
            toggle.classList.remove('collapsed');
            toggle.textContent = '展开';
        }
        
        // 检查整个流程是否完成
        function checkProcessCompletion() {
            const preAssemblyCompleted = inspectionSubmissions.preAssembly.production.isSubmitted && inspectionSubmissions.preAssembly.qc.isSubmitted;
            const preTestingCompleted = inspectionSubmissions.preTesting.production.isSubmitted && inspectionSubmissions.preTesting.qc.isSubmitted;
            const prePackagingCompleted = inspectionSubmissions.prePackaging.production.isSubmitted && inspectionSubmissions.prePackaging.qc.isSubmitted;
            
            if (preAssemblyCompleted && preTestingCompleted && prePackagingCompleted) {
                // 显示完成消息
                document.getElementById('completionOrderNo').textContent = document.getElementById('orderNo').value;
                document.getElementById('completionSerialNo').textContent = document.getElementById('serialNo').value || 'N/A';
                document.getElementById('completionMessage').style.display = 'block';
            }
        }
        
        // 更新进度
        function updateProgress() {
            // 计算总项目数
            let totalItems = 0;
            let checkedItems = 0;
            
            for (const stage in stageData) {
                const stageItems = stageData[stage].items.length;
                totalItems += stageItems * 2; // 产线自检和QC自检
                
                // 计算产线自检选中的项目数
                checkedItems += inspectionResults[stage].production.filter(item => item).length;
                
                // 计算QC自检选中的项目数
                checkedItems += inspectionResults[stage].qc.filter(item => item).length;
                
                // 更新阶段进度
                updateStageProgress(stage);
            }
            
            // 计算总体进度百分比
            const progressPercentage = totalItems > 0 ? Math.round((checkedItems / totalItems) * 100) : 0;
            
            // 更新进度条
            document.getElementById('progressPercentage').textContent = `${progressPercentage}%`;
            document.getElementById('progressInner').style.width = `${progressPercentage}%`;
        }
        
        // 更新阶段进度
        function updateStageProgress(stage) {
            const stageItems = stageData[stage].items.length;
            const totalItems = stageItems * 2; // 产线自检和QC自检
            
            // 计算已选中的项目数
            const productionChecked = inspectionResults[stage].production.filter(item => item).length;
            const qcChecked = inspectionResults[stage].qc.filter(item => item).length;
            const checkedItems = productionChecked + qcChecked;
            
            // 计算阶段进度百分比
            const progressPercentage = totalItems > 0 ? Math.round((checkedItems / totalItems) * 100) : 0;
            
            // 更新阶段进度显示
            document.getElementById(`${stage}Progress`).textContent = `${progressPercentage}%`;
        }
        
        // 显示上传附件对话框
        function showUploadDialog(stage, type) {
            currentUploadStage = stage;
            currentUploadType = type;
            
            // 更新对话框标题
            const stageTitle = stageData[stage].title;
            const typeTitle = type === 'production' ? '产线自检' : 'QC自检';
            document.getElementById('uploadDialogStage').textContent = `为"${stageTitle}${typeTitle}"上传附件`;
            
            renderUploadFileList();
            openDialog('uploadDialog');
        }
        
        // 渲染上传文件列表
        function renderUploadFileList() {
            const container = document.getElementById('uploadFileList');
            container.innerHTML = '';
            
            const files = attachments[currentUploadStage][currentUploadType];
            
            if (files.length === 0) {
                container.innerHTML = '<p>暂无已上传文件</p>';
                return;
            }
            
            files.forEach((file, index) => {
                const fileHtml = `
                    <div class="attachment-item">
                        <div class="attachment-icon">${getFileIcon(file.type)}</div>
                        <div class="attachment-info">
                            <div class="attachment-name">${file.name}</div>
                            <div class="attachment-size">${file.size}</div>
                        </div>
                        <div class="attachment-delete" onclick="deleteFile(${index})">×</div>
                    </div>
                `;
                
                container.innerHTML += fileHtml;
            });
            
            // 同步更新主界面的附件列表
            renderAttachments(currentUploadStage, currentUploadType);
        }
        
        // 渲染附件列表
        function renderAttachments(stage, type) {
            const container = document.getElementById(`${stage}-${type}-files`);
            container.innerHTML = '';
            
            const files = attachments[stage][type];
            
            if (files.length === 0) {
                container.innerHTML = '<p>暂无已上传文件</p>';
                return;
            }
            
            files.forEach((file, index) => {
                const fileHtml = `
                    <div class="attachment-item">
                        <div class="attachment-icon">${getFileIcon(file.type)}</div>
                        <div class="attachment-info">
                            <div class="attachment-name">${file.name}</div>
                            <div class="attachment-size">${file.size}</div>
                        </div>
                    </div>
                `;
                
                container.innerHTML += fileHtml;
            });
        }
        
        // 删除文件
        function deleteFile(index) {
            attachments[currentUploadStage][currentUploadType].splice(index, 1);
            renderUploadFileList();
        }
        
        // 获取文件类型
        function getFileType(filename) {
            const extension = filename.split('.').pop().toLowerCase();
            
            if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
                return 'image';
            } else if (extension === 'pdf') {
                return 'pdf';
            } else if (['doc', 'docx'].includes(extension)) {
                return 'word';
            } else if (['xls', 'xlsx'].includes(extension)) {
                return 'excel';
            } else if (['ppt', 'pptx'].includes(extension)) {
                return 'powerpoint';
            } else {
                return 'other';
            }
        }
        
        // 获取文件图标
        function getFileIcon(type) {
            switch (type) {
                case 'image': return '🖼️';
                case 'pdf': return '📄';
                case 'word': return '📝';
                case 'excel': return '📊';
                case 'powerpoint': return '📑';
                default: return '📎';
            }
        }
        
        // 格式化文件大小
        function formatFileSize(size) {
            if (size < 1024) {
                return size + ' B';
            } else if (size < 1024 * 1024) {
                return (size / 1024).toFixed(1) + ' KB';
            } else {
                return (size / (1024 * 1024)).toFixed(1) + ' MB';
            }
        }
        
        // 获取当前时间
        function getCurrentTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
        
        // 打开对话框
        function openDialog(dialogId) {
            const dialog = document.getElementById(dialogId);
            dialog.classList.add('visible');
        }
        
        // 关闭对话框
        function closeDialog(dialogId) {
            const dialog = document.getElementById(dialogId);
            dialog.classList.remove('visible');
        }
        
        // 显示通知
        function showNotification(type, title, message) {
            const notification = document.getElementById('notification');
            const notificationIcon = document.getElementById('notificationIcon');
            const notificationTitle = document.getElementById('notificationTitle');
            const notificationMessage = document.getElementById('notificationMessage');
            
            notificationIcon.className = `notification-icon ${type}`;
            notificationIcon.textContent = type === 'warning' ? '⚠️' : '✓';
            notificationTitle.textContent = title;
            notificationMessage.textContent = message;
            
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>