/* 
 * BEM布局类统一系统 - 基于CouplerVue.css标准
 * 命名空间: module-* 避免布局类污染
 * 版本: v1.0
 * 最后更新: 2024-12-19
 */

/* === 模块级主要布局容器类 === */
.module-main-container {
    min-height: 100vh;
    background: var(--module-bg-primary);
    position: relative;
    overflow: hidden;
}

.module-main-content {
    display: flex;
    height: calc(100vh - 89px);
}

.module-form-section {
    width: 50%;
    padding-left: 1.5rem;
    padding-right: 0.75rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    overflow-y: auto;
}

.module-test-section {
    width: 50%;
    border-left: 1px solid var(--module-toolbar-border);
    padding-left: 0.75rem;
    padding-right: 1.5rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    overflow-y: auto;
}

/* === 模块级工具栏布局类 === */
.module-toolbar-container {
    max-width: none;
    margin-left: 1.5rem;
    margin-right: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--module-toolbar-bg);
    border-color: var(--module-toolbar-border);
}

.module-toolbar-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.module-toolbar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.module-toolbar-status {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.module-toolbar-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* === 模块级卡片布局类 === */
.module-card {
    background: var(--module-card-bg);
    border: 1px solid var(--module-card-border);
    backdrop-filter: blur(16px);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.module-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--module-card-hover-shadow);
    border-color: var(--module-card-hover-border);
}

.module-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.module-card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.module-card-content {
    padding: 1.5rem;
}

/* === 模块级图标容器类 === */
.module-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.module-icon--blue {
    background: linear-gradient(135deg, #3b82f6, #4f46e5);
    box-shadow: var(--module-shadow-glow);
}

.module-icon--green {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.module-icon--purple {
    background: linear-gradient(135deg, #8b5cf6, #ec4899);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.module-icon--indigo {
    background: linear-gradient(135deg, #6366f1, #3b82f6);
    box-shadow: var(--module-shadow-glow);
}

/* === 模块级Flex布局通用类 === */
.module-flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.module-flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.module-flex-start {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.module-flex-column {
    display: flex;
    flex-direction: column;
}

/* === 模块级间距类 === */
.module-space-y {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.module-space-x {
    display: flex;
    gap: 0.75rem;
}

.module-space-sm {
    gap: 0.5rem;
}

.module-space-md {
    gap: 1rem;
}

.module-space-lg {
    gap: 1.5rem;
}

/* === 模块级表单布局类 === */
.module-form-content {
    padding: 1.5rem;
    padding-bottom: 1.5rem;
}

.module-form-collapsed {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.module-form-expanded {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.module-device-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* === 模块级网格系统 === */
.module-grid-2 {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
}

.module-grid-3 {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
}

.module-grid-4 {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 1rem;
}

.module-field-group {
    display: flex;
    flex-direction: column;
}

.module-field-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--module-text-secondary);
    margin-bottom: 0.25rem;
}

/* === 模块级测试进度卡片 === */
.module-progress-card {
    background: var(--module-card-bg);
    border: 1px solid var(--module-card-border);
    backdrop-filter: blur(16px);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
}

.module-progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.module-progress-tabs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.module-progress-stats {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 1rem;
    text-align: center;
}

.module-stat-card {
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid;
}

.module-stat-card--success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
    border-color: rgba(34, 197, 94, 0.2);
}

.module-stat-card--danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));
    border-color: rgba(239, 68, 68, 0.2);
}

.module-stat-card--neutral {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.1), rgba(148, 163, 184, 0.1));
    border-color: rgba(156, 163, 175, 0.2);
}

.module-stat-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.module-stat-label {
    font-size: 0.75rem;
    font-weight: 500;
}

/* === 模块级测试日志视图 === */
.module-log-layout {
    display: flex;
    gap: 1rem;
    height: 16rem;
}

.module-log-stats {
    width: 20%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.module-log-content {
    width: 80%;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.module-log-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
}

.module-log-indicators {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.module-log-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.module-log-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
}

.module-log-dot--success {
    background-color: #10b981;
}

.module-log-dot--error {
    background-color: #ef4444;
}

.module-log-dot--warning {
    background-color: #f59e0b;
}

.module-log-dot--info {
    background-color: #3b82f6;
}

.module-log-dot--system {
    background-color: #8b5cf6;
}

/* === 模块级测试项目卡片 === */
.module-test-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.module-test-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid var(--module-card-border);
    transition: all 0.3s ease;
}

.module-test-item:hover {
    border-color: var(--module-card-hover-border);
}

.module-test-item--active {
    background: var(--module-accent-blue-light);
    border-color: var(--module-card-hover-border);
    box-shadow: 0 8px 25px -8px var(--module-card-hover-shadow);
}

.module-test-item-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.module-test-item-icon {
    padding: 0.5rem;
    border-radius: 0.5rem;
}

.module-test-item-details {
    display: flex;
    flex-direction: column;
}

.module-test-item-name {
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--module-text-primary);
}

.module-test-item-meta {
    font-size: 0.75rem;
    color: var(--module-text-secondary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.module-test-item-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.module-test-item-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.module-test-item-buttons {
    display: flex;
    gap: 0.5rem;
}

/* === 模块级主题卡片样式 === */
.module-theme-card {
    background: var(--module-card-bg) !important;
    border-color: var(--module-card-border) !important;
    color: var(--module-text-primary);
}

.module-theme-label {
    color: var(--module-text-secondary) !important;
}

.module-theme-separator {
    border-color: var(--module-separator-color) !important;
}

.module-theme-text-primary {
    color: var(--module-text-primary) !important;
}

.module-theme-text-secondary {
    color: var(--module-text-secondary) !important;
}

.module-theme-text-tertiary {
    color: var(--module-text-tertiary) !important;
} 