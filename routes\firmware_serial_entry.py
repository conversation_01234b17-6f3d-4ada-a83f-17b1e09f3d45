# routes/firmware_serial_entry.py
"""
固件序列号录入模块
实现PCBA序列号和SN号的录入、查询、统计功能
遵循项目开发规范：PEP 8、SQLAlchemy ORM、DRY原则
"""

from flask import Blueprint, request, jsonify
from models.firmware import FirmwareSerialDetail, DownloadRecord
from database.db_manager import DatabaseManager
from utils.firmware_utils import create_response, handle_db_error
from sqlalchemy import and_, or_, desc, asc, func
from datetime import datetime
import logging
import hashlib
import re
from typing import Tuple, Optional
from jwt import decode as jwt_decode
from config import JWT_SECRET

# 创建蓝图
firmware_serial_entry_bp = Blueprint('firmware_serial_entry', __name__, 
                                    url_prefix='/api/firmware/serial-entry')
logger = logging.getLogger(__name__)
db_manager = DatabaseManager()


class SerialValidationError(Exception):
    """序列号验证异常类"""
    
    def __init__(self, message: str, serial_value: str = None, error_code: str = None):
        self.message = message
        self.serial_value = serial_value
        self.error_code = error_code
        super().__init__(self.message)


class SerialEntryService:
    """序列号录入服务类，封装业务逻辑，遵循DRY原则"""
    
    # 序列号类型常量
    SERIAL_TYPE_SN = 'SN'
    SERIAL_TYPE_PCBA = 'PCBA'
    
    # 工单类型前缀
    FG_ORDER_PREFIX = 'FG'
    
    @staticmethod
    def validate_serial_format(serial_value: str, serial_digits: int, serial_type: str) -> Tuple[bool, str]:
        """
        验证序列号格式
        
        Args:
            serial_value (str): 序列号值
            serial_digits (int): 要求位数
            serial_type (str): 序列号类型
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        if not serial_value or not serial_value.strip():
            return False, "序列号不能为空"
        serial_value = serial_value.strip()
        if len(serial_value) != serial_digits:
            return False, f"序列号长度必须为{serial_digits}位，当前为{len(serial_value)}位"
        # SN号和PCBA序列号都不做字符类型限制，只验证长度
        return True, ""
    
    @staticmethod
    def calculate_pcba_hash(pcba_serial: str) -> str:
        """
        计算PCBA序列号的SHA256哈希值
        
        Args:
            pcba_serial (str): PCBA序列号
            
        Returns:
            str: SHA256哈希值（64位十六进制字符串）
        """
        if not pcba_serial:
            raise ValueError("PCBA序列号不能为空")
        
        # 使用UTF-8编码计算SHA256哈希
        return hashlib.sha256(pcba_serial.encode('utf-8')).hexdigest()
    
    @staticmethod
    def determine_serial_type_by_work_order(work_order: str) -> str:
        """
        根据工单号确定序列号类型
        
        Args:
            work_order (str): 工单号
            
        Returns:
            str: 序列号类型 ('SN' 或 'PCBA')
        """
        if not work_order:
            raise ValueError("工单号不能为空")
        
        # FG开头的工单录入SN号，其他录入PCBA序列号
        return SerialEntryService.SERIAL_TYPE_SN if work_order.startswith(SerialEntryService.FG_ORDER_PREFIX) else SerialEntryService.SERIAL_TYPE_PCBA
    
    @staticmethod
    def check_sn_uniqueness(session, firmware_sn: str, exclude_id: int = None) -> bool:
        """
        检查SN号的全局唯一性
        
        Args:
            session: 数据库会话
            firmware_sn (str): SN号
            exclude_id (int): 排除的记录ID(用于更新时)
            
        Returns:
            bool: 是否唯一
        """
        query = session.query(FirmwareSerialDetail).filter(
            FirmwareSerialDetail.firmware_sn == firmware_sn,
            FirmwareSerialDetail.is_deleted == False
        )
        
        if exclude_id:
            query = query.filter(FirmwareSerialDetail.id != exclude_id)
        
        return query.first() is None
    
    @staticmethod
    def get_work_order_info(session, work_order: str) -> dict:
        """
        获取工单信息
        
        Args:
            session: 数据库会话
            work_order (str): 工单号
            
        Returns:
            dict: 工单信息
        """
        download_record = session.query(DownloadRecord).filter_by(
            work_order=work_order,
            is_deleted=False
        ).first()
        
        if not download_record:
            return None
        
        return download_record.to_dict()


@firmware_serial_entry_bp.route('/<path:work_order>/entry', methods=['POST'])
def create_serial_entry(work_order):
    """
    录入序列号
    
    POST /api/firmware/serial-entry/{work_order}/entry
    """
    try:
        # 获取当前用户信息
        token = request.cookies.get('token')
        try:
            user_data = jwt_decode(token, JWT_SECRET, algorithms=['HS256'])
            input_user = user_data.get('username', 'unknown')
        except Exception:
            input_user = 'unknown'
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return create_response(False, '请求数据不能为空', code=400)
        
        serial_type = data.get('serialType')  # 前端驼峰命名
        serial_value = data.get('serialValue', '').strip()
        serial_digits = data.get('serialDigits')
        
        # 基础参数验证
        if not all([serial_type, serial_value, serial_digits]):
            return create_response(False, '序列号类型、序列号值和位数要求不能为空', code=400)
        
        if serial_type not in ['SN', 'PCBA']:
            return create_response(False, '序列号类型必须为SN或PCBA', code=400)
        
        try:
            serial_digits = int(serial_digits)
        except ValueError:
            return create_response(False, '序列号位数必须为数字', code=400)
        
        # 验证序列号格式
        is_valid, error_msg = SerialEntryService.validate_serial_format(serial_value, serial_digits, serial_type)
        if not is_valid:
            return create_response(False, error_msg, code=400)
        
        with db_manager.get_session() as session:
            # 验证工单是否存在
            work_order_info = SerialEntryService.get_work_order_info(session, work_order)
            if not work_order_info:
                return create_response(False, f'工单号{work_order}不存在', code=404)
            
            # 新增：录入数量上限校验
            entry_count = session.query(FirmwareSerialDetail).filter_by(
                work_order=work_order,
                is_deleted=False
            ).count()
            target_count = work_order_info.get('burnCount', 0)
            if entry_count >= target_count:
                return create_response(False, f'已达生产数量上限({target_count})，不能再录入', code=400)
            
            # 根据序列号类型创建记录
            if serial_type == 'SN':
                # 查找是否已存在该SN号
                existing_sn = session.query(FirmwareSerialDetail).filter(
                    FirmwareSerialDetail.firmware_sn == serial_value,
                    FirmwareSerialDetail.is_deleted == False
                ).first()
                if existing_sn:
                    # 更新工单号和相关字段
                    existing_sn.work_order = work_order
                    existing_sn.serial_digits = serial_digits
                    existing_sn.input_user = input_user
                    existing_sn.input_time = datetime.now()
                    session.commit()
                    serial_record = existing_sn
                else:
                    # 创建SN记录
                    serial_record = FirmwareSerialDetail(
                        work_order=work_order,
                        serial_type='SN',
                        firmware_sn=serial_value,
                        firmware_pcba=None,
                        firmware_hash=None,
                        serial_digits=serial_digits,
                        input_user=input_user
                    )
                    session.add(serial_record)
                    session.commit()
            else:  # PCBA
                # 计算PCBA序列号的SHA256哈希值
                pcba_hash = SerialEntryService.calculate_pcba_hash(serial_value)
                
                # 创建PCBA记录
                serial_record = FirmwareSerialDetail(
                    work_order=work_order,
                    serial_type='PCBA',
                    firmware_sn=None,
                    firmware_pcba=serial_value,
                    firmware_hash=pcba_hash,
                    serial_digits=serial_digits,
                    input_user=input_user
                )
            
            session.add(serial_record)
            session.commit()
            
            # 获取录入进度
            entry_count = session.query(FirmwareSerialDetail).filter_by(
                work_order=work_order,
                is_deleted=False
            ).count()
            
            target_count = work_order_info.get('burnCount', 0)
            
            logger.info(f"序列号录入成功: 工单={work_order}, 类型={serial_type}, "
                       f"值={serial_value[:6]}..., 用户={input_user}")
            
            return create_response(True, '序列号录入成功', {
                'serialRecord': serial_record.to_dict(),
                'entryProgress': {
                    'enteredCount': entry_count,
                    'targetCount': target_count,
                    'remaining': max(0, target_count - entry_count),
                    'isCompleted': entry_count >= target_count
                }
            })
            
    except Exception as e:
        logger.error(f"录入序列号失败: {str(e)}")
        return handle_db_error(e)


@firmware_serial_entry_bp.route('/<path:work_order>/records', methods=['GET'])
def get_serial_records(work_order):
    """
    获取工单的序列号录入记录
    
    GET /api/firmware/serial-entry/{work_order}/records
    """
    try:
        # 获取查询参数
        page = max(1, int(request.args.get('page', 1)))
        per_page = min(1500, max(1, int(request.args.get('perPage', 20))))
        serial_type = request.args.get('serialType')  # 前端驼峰命名
        
        with db_manager.get_session() as session:
            # 验证工单是否存在
            work_order_info = SerialEntryService.get_work_order_info(session, work_order)
            if not work_order_info:
                return create_response(False, f'工单号{work_order}不存在', code=404)
            
            # 构建查询
            query = session.query(FirmwareSerialDetail).filter(
                FirmwareSerialDetail.work_order == work_order,
                FirmwareSerialDetail.is_deleted == False
            )
            
            # 按类型过滤
            if serial_type:
                query = query.filter(FirmwareSerialDetail.serial_type == serial_type)
            
            # 排序
            query = query.order_by(desc(FirmwareSerialDetail.input_time))
            
            # 分页
            total = query.count()
            offset = (page - 1) * per_page
            records = query.offset(offset).limit(per_page).all()
            
            # 转换为字典格式
            record_list = [record.to_dict() for record in records]
            
            return create_response(True, '查询成功', {
                'workOrderInfo': work_order_info,
                'records': record_list,
                'pagination': {
                    'total': total,
                    'page': page,
                    'perPage': per_page,
                    'totalPages': (total + per_page - 1) // per_page
                }
            })
            
    except Exception as e:
        logger.error(f"获取序列号记录失败: {str(e)}")
        return handle_db_error(e)


@firmware_serial_entry_bp.route('/<path:work_order>/progress', methods=['GET'])
def get_entry_progress(work_order):
    """
    获取工单录入进度
    
    GET /api/firmware/serial-entry/{work_order}/progress
    """
    try:
        with db_manager.get_session() as session:
            # 验证工单是否存在
            work_order_info = SerialEntryService.get_work_order_info(session, work_order)
            if not work_order_info:
                return create_response(False, f'工单号{work_order}不存在', code=404)
            
            # 统计录入数量
            sn_count = session.query(FirmwareSerialDetail).filter(
                FirmwareSerialDetail.work_order == work_order,
                FirmwareSerialDetail.serial_type == 'SN',
                FirmwareSerialDetail.is_deleted == False
            ).count()
            
            pcba_count = session.query(FirmwareSerialDetail).filter(
                FirmwareSerialDetail.work_order == work_order,
                FirmwareSerialDetail.serial_type == 'PCBA',
                FirmwareSerialDetail.is_deleted == False
            ).count()
            
            total_entered = sn_count + pcba_count
            target_count = work_order_info.get('burnCount', 0)
            
            return create_response(True, '查询成功', {
                'workOrderInfo': work_order_info,
                'progress': {
                    'snCount': sn_count,
                    'pcbaCount': pcba_count,
                    'totalEntered': total_entered,
                    'targetCount': target_count,
                    'remaining': max(0, target_count - total_entered),
                    'isCompleted': total_entered >= target_count,
                    'completionRate': round(total_entered / target_count * 100, 2) if target_count > 0 else 0
                }
            })
            
    except Exception as e:
        logger.error(f"获取录入进度失败: {str(e)}")
        return handle_db_error(e)


@firmware_serial_entry_bp.route('/<path:work_order>/validate-sn', methods=['POST'])
def validate_sn_uniqueness(work_order):
    """
    验证SN号唯一性
    
    POST /api/firmware/serial-entry/{work_order}/validate-sn
    """
    try:
        data = request.get_json()
        firmware_sn = data.get('firmwareSn', '').strip()
        
        if not firmware_sn:
            return create_response(False, 'SN号不能为空', code=400)
        
        with db_manager.get_session() as session:
            is_unique = SerialEntryService.check_sn_uniqueness(session, firmware_sn)
            
            return create_response(True, '验证完成', {
                'firmwareSn': firmware_sn,
                'isUnique': is_unique,
                'message': 'SN号可用' if is_unique else 'SN号已存在'
            })
            
    except Exception as e:
        logger.error(f"验证SN号唯一性失败: {str(e)}")
        return handle_db_error(e)


@firmware_serial_entry_bp.route('/statistics', methods=['GET'])
def get_entry_statistics():
    """
    获取序列号录入统计信息
    
    GET /api/firmware/serial-entry/statistics
    """
    try:
        with db_manager.get_session() as session:
            # 总录入量统计
            total_records = session.query(FirmwareSerialDetail).filter_by(
                is_deleted=False
            ).count()
            
            # 按类型统计
            type_stats = session.query(
                FirmwareSerialDetail.serial_type,
                func.count(FirmwareSerialDetail.id).label('count')
            ).filter_by(is_deleted=False).group_by(
                FirmwareSerialDetail.serial_type
            ).all()
            
            # 按录入人员统计
            user_stats = session.query(
                FirmwareSerialDetail.input_user,
                func.count(FirmwareSerialDetail.id).label('count')
            ).filter_by(is_deleted=False).group_by(
                FirmwareSerialDetail.input_user
            ).order_by(desc('count')).limit(10).all()
            
            # 今日录入统计
            today = datetime.now().date()
            today_count = session.query(FirmwareSerialDetail).filter(
                FirmwareSerialDetail.is_deleted == False,
                func.date(FirmwareSerialDetail.input_time) == today
            ).count()
            
            return create_response(True, '查询成功', {
                'totalRecords': total_records,
                'todayCount': today_count,
                'typeStatistics': [
                    {'serialType': type_name, 'count': count} 
                    for type_name, count in type_stats
                ],
                'userStatistics': [
                    {'inputUser': user_name, 'count': count} 
                    for user_name, count in user_stats
                ]
            })
            
    except Exception as e:
        logger.error(f"获取录入统计失败: {str(e)}")
        return handle_db_error(e)


@firmware_serial_entry_bp.route('/detail/<int:detail_id>', methods=['DELETE'])
def delete_serial_entry_detail(detail_id):
    """
    删除序列号明细记录（软删除）
    
    DELETE /api/firmware/serial-entry/detail/{detail_id}
    """
    try:
        with db_manager.get_session() as session:
            # 查找记录
            record = session.query(FirmwareSerialDetail).filter_by(
                id=detail_id,
                is_deleted=False
            ).first()
            
            if not record:
                return create_response(False, '记录不存在', code=404)
            
            # 直接删除（物理删除）
            work_order = record.work_order
            serial_type = record.serial_type
            session.delete(record)
            session.commit()
            
            logger.info(f"序列号明细删除成功: ID={detail_id}, 工单={work_order}, "
                       f"类型={serial_type}")
            
            return create_response(True, '删除成功')
            
    except Exception as e:
        logger.error(f"删除序列号明细失败: {str(e)}")
        return handle_db_error(e) 