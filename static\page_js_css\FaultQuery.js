(function() {
    // 检查是否已经存在全局变量
    if (typeof window.faultPageState === 'undefined') {
        window.faultPageState = {
            currentPage: 1,
            pageSize: 10,
            totalItems: 0,
            totalPages: 10,
            data: []
        };
    }

    if (typeof window.faultColumnState === 'undefined') {
        window.faultColumnState = {
            checkbox: true,
            orderNumber: true,
            productModel: true,
            productCode: true,
            workQty: true,
            faultRate: true,
            productStatus: true,
            maintenanceCount: true,
            reworkCount: true,
            testTime: true,
            tester: true
        };
    }

    function initFaultQueryPage() {
        const faultQueryContent = document.getElementById('fault-query-content');
        if (!faultQueryContent) {
            Logger.error('无法找到 fault-query-content 元素');
            return;
        }
        
        faultQueryContent.innerHTML = `
            <div class="container">
                <!-- 1. 查询区域 -->
                <div class="query-section">
                    <form class="query-form">
                        <div class="form-item">
                            <label class="form-label">工单号</label>
                            <input type="text" class="form-input" id="fault-order-number" placeholder="请输入工单号">
                        </div>
                        <div class="form-item">
                            <label class="form-label">SN号</label>
                            <input type="text" class="form-input" id="fault-sn" placeholder="请输入SN号">
                        </div>
                        <div class="form-item">
                            <label class="form-label">产品编码</label>
                            <input type="text" class="form-input" id="fault-product-code" placeholder="请输入产品编码">
                        </div>
                        <div class="form-item">
                            <label class="form-label">故障类型</label>
                            <select class="form-select" id="fault-type">
                                <option value="">全部</option>
                                <option value="基础功能">基础功能</option>
                                <option value="通信接口">通信接口</option>
                                <option value="IO接口">IO接口</option>
                                <option value="指示与控制">指示与控制</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-input" id="fault-start-date">
                        </div>
                        <div class="form-item">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-input" id="fault-end-date">
                        </div>
                    </form>
                    <div class="query-buttons">
                        <button class="btn btn-default" onclick="resetFaultForm()">重置</button>
                        <button class="btn btn-primary" onclick="searchFault()">查询</button>
                    </div>
                </div>

                <!-- 2. 工具栏区域 -->
                <div class="toolbar-section">
                    <div class="toolbar-left">
                        <button class="btn btn-export" onclick="exportFaultData()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-default" onclick="toggleFaultColumnSettings()">
                            <i class="fas fa-cog"></i> 列设置
                        </button>
                        <button class="btn btn-default" onclick="refreshFaultTable()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 3. 表格区域 -->
                <div class="table-section">
                    <table class="data-table">
                        <thead>
                            <tr class="table-header">
                                <th class="table-cell-fixed">
                                    <input type="checkbox" id="fault-select-all" onclick="toggleFaultSelectAll()">
                                </th>
                                <th>工单号</th>
                                <th>产品型号</th>
                                <th>产品编码</th>
                                <th>生产数量</th>
                                <th>故障率(%)</th>
                                <th>产品状态</th>
                                <th>维修次数</th>
                                <th>返工次数</th>
                                <th>测试时间</th>
                                <th>测试人员</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="fault-results-tbody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 4. 分页区域 -->
                <div class="pagination-section">
                    <div class="page-size">
                        <span>每页</span>
                        <select class="form-select" onchange="changeFaultPageSize(this.value)">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                        </select>
                        <span>条</span>
                    </div>
                    <div class="pagination-controls" id="fault-pagination-controls">
                        <button class="page-button" onclick="changeFaultPage(${faultPageState.currentPage - 1})" 
                                ${faultPageState.currentPage === 1 ? 'disabled' : ''}>
                            上一页
                        </button>
                        <button class="page-button active" onclick="changeFaultPage(1)">1</button>
                        <button class="page-button" onclick="changeFaultPage(2)">2</button>
                        <button class="page-button" onclick="changeFaultPage(3)">3</button>
                        <span>...</span>
                        <button class="page-button" onclick="changeFaultPage(${faultPageState.totalPages})">
                            ${faultPageState.totalPages || 1}
                        </button>
                        <button class="page-button" onclick="changeFaultPage(${faultPageState.currentPage + 1})"
                                ${faultPageState.currentPage === faultPageState.totalPages ? 'disabled' : ''}>
                            下一页
                        </button>
                        <div class="page-jump">
                            <span>跳至</span>
                            <input type="text" class="page-input" value="${faultPageState.currentPage}" 
                                   onchange="jumpToFaultPage(this.value)">
                            <span>页</span>
                        </div>
                        <span class="total-count">共 ${faultPageState.totalItems} 条</span>
                    </div>
                </div>

                <!-- 列设置弹窗 -->
                <div id="fault-column-settings-modal" class="modal fault-column-modal">
                    <div class="modal-content fault-column-modal__content">
                        <div class="modal-header">
                            <h3>列设置</h3>
                            <span class="close" onclick="closeFaultColumnSettings()">&times;</span>
                        </div>
                        <div class="modal-body">
                            <div class="column-list">
                                <!-- 全选项 -->
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="select-all-fault" onclick="toggleAllFaultColumns()">
                                        <span>全选</span>
                                    </label>
                                </div>
                                <div class="column-divider"></div>
                                <!-- 各列选项 -->
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-orderNumber" checked>
                                        <span>工单号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-productModel" checked>
                                        <span>产品型号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-productCode" checked>
                                        <span>产品编码</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-workQty" checked>
                                        <span>生产数量</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-faultRate" checked>
                                        <span>故障率</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-productStatus" checked>
                                        <span>产品状态</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-maintenanceCount" checked>
                                        <span>维修次数</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-reworkCount" checked>
                                        <span>返工次数</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-testTime" checked>
                                        <span>测试时间</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-fault-tester" checked>
                                        <span>测试人员</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" onclick="applyFaultColumnSettings()">确定</button>
                            <button class="btn btn-default" onclick="closeFaultColumnSettings()">取消</button>
                        </div>
                    </div>
                </div>

                <!-- 详情模态框 -->
                <div id="fault-detail-modal" class="modal">
                    <div class="fault-modal__content">
                        <div class="fault-modal__header">
                            <h2 class="fault-modal__title">故障详细信息</h2>
                            <span class="fault-modal__close">&times;</span>
                        </div>
                        <div class="fault-modal__body">
                            <!-- 基本信息卡片 -->
                            <div class="fault-modal__card">
                                <h3 class="fault-modal__subtitle">基本信息</h3>
                                <div class="fault-modal__grid">
                                    <div class="fault-modal__item">
                                        <label class="fault-modal__label">工单号</label>
                                        <span class="fault-modal__value" id="fault-detail-order-number">-</span>
                                    </div>
                                    <div class="fault-modal__item">
                                        <label class="fault-modal__label">产品型号</label>
                                        <span class="fault-modal__value" id="fault-detail-product-model">-</span>
                                    </div>
                                    <div class="fault-modal__item">
                                        <label class="fault-modal__label">产品编码</label>
                                        <span class="fault-modal__value" id="fault-detail-product-code">-</span>
                                    </div>
                                    <div class="fault-modal__item">
                                        <label class="fault-modal__label">产品SN</label>
                                        <span class="fault-modal__value" id="fault-detail-sn">-</span>
                                    </div>
                                    <div class="fault-modal__item">
                                        <label class="fault-modal__label">产品批次</label>
                                        <span class="fault-modal__value" id="fault-detail-batch">-</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 故障信息卡片 -->
                            <div class="fault-modal__card">
                                <h3 class="fault-modal__subtitle">故障信息</h3>
                                <div class="fault-modal__fault-list" id="fault-detail-list">
                                    <!-- 故障列表将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化事件监听
        initFaultEventListeners();
    }

    function initFaultEventListeners() {
        // 输入框回车事件
        const inputs = ['fault-order-number', 'fault-sn'];
        inputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        searchFault();
                    }
                });
            }
        });

        // 获取模态框元素
        const detailModal = document.getElementById('fault-detail-modal');
        if (detailModal) {
            // 移除旧的事件监听器
            const oldCloseBtn = detailModal.querySelector('.fault-modal__close');
            if (oldCloseBtn) {
                oldCloseBtn.onclick = null;
            }
            detailModal.onclick = null;

            // 添加新事件监听器
            const closeBtn = detailModal.querySelector('.fault-modal__close');
            if (closeBtn) {
                closeBtn.onclick = closeFaultDetailModal;
            }
            detailModal.onclick = (e) => {
                if (e.target === detailModal) {
                    closeFaultDetailModal();
                }
            };
        }

        // 移除可能存在的旧ESC事件监听器
        document.removeEventListener('keydown', handleFaultDetailEsc);

        // 添分页大小选器的事件监听
        const pageSizeSelect = document.querySelector('.page-size select');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                changeFaultPageSize(e.target.value);
            });
        }

        // 添加触摸事件监听器
        const touchElements = document.querySelectorAll('.scrollable');
        touchElements.forEach(element => {
            element.addEventListener('touchstart', handler, { passive: true });
            element.addEventListener('touchmove', handler, { passive: true });
        });

        // 添加表格中复选框的变化监听
        const tbody = document.getElementById('fault-results-tbody');
        if (tbody) {
            tbody.addEventListener('change', (e) => {
                if (e.target.type === 'checkbox') {
                    // 更新全选框状态
                    const selectAll = document.getElementById('fault-select-all');
                    const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
                    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                    selectAll.checked = allChecked;
                    
                    // 更新导出按钮文本
                    updateFaultExportButtonText();
                }
            });
        }
    }

    async function searchFault() {
        const orderNumber = document.getElementById('fault-order-number').value.trim();
        const sn = document.getElementById('fault-sn').value.trim();
        const productCode = document.getElementById('fault-product-code').value.trim();
        const faultType = document.getElementById('fault-type').value;
        const startDate = document.getElementById('fault-start-date').value;
        const endDate = document.getElementById('fault-end-date').value;
        
        if (!orderNumber && !sn && !productCode && !faultType && !startDate && !endDate) {
            showToast('请至少输入一个查询条件', 'warning');
            return;
        }

        if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
            showToast('开始日期不能大于结束日期', 'warning');
            return;
        }

        try {
            const params = new URLSearchParams();
            if (orderNumber) params.append('orderNumber', orderNumber);
            if (sn) params.append('sn', sn);
            if (productCode) params.append('productCode', productCode);
            if (faultType) params.append('faultType', faultType);
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);

            // 添加日志输出
            Logger.log('查询参数:', Object.fromEntries(params));
            showToast('正在查询...', 'info');

            const response = await fetch(`/api/fault/search?${params.toString()}`);
            const data = await response.json();
            
            // 添加日志输出
            Logger.log('查询结果:', data);

            if (data.success) {
                if (data.data && data.data.length > 0) {
                    faultPageState.currentPage = 1;
                    updateFaultResultsTable(data.data);
                    const tableSection = document.querySelector('.table-section');
                    if (tableSection) {
                        tableSection.style.display = 'block';
                    }
                    showToast(`查询成功，共找到 ${data.data.length} 条记录`, 'success');
                } else {
                    showToast('未找到匹配的记录', 'warning');
                    const tableSection = document.querySelector('.table-section');
                    if (tableSection) {
                        tableSection.style.display = 'none';
                    }
                    // 清空表格内容
                    const tbody = document.getElementById('fault-results-tbody');
                    if (tbody) {
                        tbody.innerHTML = '';
                    }
                }
            } else {
                showToast(data.message || '查询失败', 'error');
            }
        } catch (error) {
            Logger.error('查询失败:', error);
            showToast('查询失败，请稍后重试', 'error');
        }
    }

    function updateFaultResultsTable(data) {
        faultPageState.data = data;
        faultPageState.totalItems = data.length;
        faultPageState.totalPages = Math.ceil(data.length / faultPageState.pageSize);
        
        const startIndex = (faultPageState.currentPage - 1) * faultPageState.pageSize;
        const endIndex = startIndex + faultPageState.pageSize;
        const pageData = data.slice(startIndex, endIndex);
        
        const tbody = document.getElementById('fault-results-tbody');
        if (!tbody) return;

        // 计算每个工单的故障率
        const faultRates = {};
        data.forEach(item => {
            const orderNumber = item.orderNumber;
            if (!faultRates[orderNumber]) {
                faultRates[orderNumber] = {
                    workQty: item.workQty || 0,
                    faultCount: 0
                };
            }
            // 只统计产品状态为"新品"的故障数量
            if (item.productStatus === '新品') {
                faultRates[orderNumber].faultCount++;
            }
        });

        // 计算故障率
        Object.keys(faultRates).forEach(orderNumber => {
            const { workQty, faultCount } = faultRates[orderNumber];
            if (workQty > 0) {
                faultRates[orderNumber].rate = (faultCount / workQty * 100).toFixed(2);
            } else {
                faultRates[orderNumber].rate = '0.00';
            }
        });

        // 根据列设置更新表头
        const thead = document.querySelector('.table-header');
        if (thead) {
            thead.innerHTML = `
                ${faultColumnState.checkbox ? `
                    <th class="table-cell-fixed">
                        <input type="checkbox" id="fault-select-all" onclick="toggleFaultSelectAll()">
                    </th>
                ` : ''}
                ${faultColumnState.orderNumber ? `<th>工单号</th>` : ''}
                ${faultColumnState.productModel ? `<th>产品型号</th>` : ''}
                ${faultColumnState.productCode ? `<th>产品编码</th>` : ''}
                ${faultColumnState.workQty ? `<th>生产数量</th>` : ''}
                ${faultColumnState.faultRate ? `<th>故障率(%)</th>` : ''}
                ${faultColumnState.productStatus ? `<th>产品状态</th>` : ''}
                ${faultColumnState.maintenanceCount ? `<th>维修次数</th>` : ''}
                ${faultColumnState.reworkCount ? `<th>返工次数</th>` : ''}
                ${faultColumnState.testTime ? `<th>测试时间</th>` : ''}
                ${faultColumnState.tester ? `<th>测试人员</th>` : ''}
                <th class="text-center">操作</th>
            `;
        }

        // 根据列设置更新表格内容
        tbody.innerHTML = pageData.map(item => `
            <tr class="table-row">
                ${faultColumnState.checkbox ? `
                    <td class="text-center">
                        <input type="checkbox" class="row-checkbox">
                    </td>
                ` : ''}
                ${faultColumnState.orderNumber ? `<td class="text-code">${item.orderNumber || '-'}</td>` : ''}
                ${faultColumnState.productModel ? `<td class="text-code">${item.productModel || '-'}</td>` : ''}
                ${faultColumnState.productCode ? `<td class="text-code">${item.productCode || '-'}</td>` : ''}
                ${faultColumnState.workQty ? `<td class="text-number">${item.workQty !== null ? item.workQty : '-'}</td>` : ''}
                ${faultColumnState.faultRate ? `<td class="text-number">${faultRates[item.orderNumber]?.rate || '0.00'}%</td>` : ''}
                ${faultColumnState.productStatus ? `
                    <td class="text-status">
                        <span class="status-tag ${getStatusClass(item.productStatus)}">
                            ${item.productStatus || '未知'}
                        </span>
                    </td>
                ` : ''}
                ${faultColumnState.maintenanceCount ? `<td class="text-number">${item.maintenanceCount !== null ? item.maintenanceCount : '-'}</td>` : ''}
                ${faultColumnState.reworkCount ? `<td class="text-number">${item.reworkCount !== null ? item.reworkCount : '-'}</td>` : ''}
                ${faultColumnState.testTime ? `<td class="text-date">${formatDate(item.testTime) || '-'}</td>` : ''}
                ${faultColumnState.tester ? `<td class="text-name">${item.tester || '-'}</td>` : ''}
                <td class="text-center">
                    <button class="btn btn-detail" onclick='showFaultDetail(${JSON.stringify(item)})'>
                        <i class="fas fa-eye"></i> 详情
                    </button>
                </td>
            </tr>
        `).join('');

        updateFaultPagination();
        updateFaultExportButtonText();
    }

    function getFaultTypeClass(type) {
        switch (type) {
            case 'hardware': return 'fault-type-hardware';
            case 'software': return 'fault-type-software';
            default: return 'fault-type-other';
        }
    }

    function getFaultTypeText(type) {
        switch (type) {
            case 'hardware': return '硬件故障';
            case 'software': return '软件故障';
            default: return '其他故障';
        }
    }

    function getStatusClass(status) {
        switch (status) {
            case '新品':
                return 'status-new';
            case '修品':
                return 'status-repair';
            case '返工品':
                return 'status-rework';
            default:
                return 'status-unknown';
        }
    }

    function getStatusText(status) {
        return status || '未知';
    }

    // 添加独立的工具函数
    function formatDate(dateString) {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    function showToast(message, type = 'info') {
        // 创建容器（如果不存在）
        let toastContainer = document.querySelector('.fault-query__toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'fault-query__toast-container';
            document.body.appendChild(toastContainer);
        }
        
        // 创建新的 toast 元素
        const toast = document.createElement('div');
        toast.className = `fault-query__toast fault-query__toast--${type}`;
        
        // 设置图标
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="fas fa-check-circle"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-times-circle"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-circle"></i>';
                break;
            case 'info':
                icon = '<i class="fas fa-info-circle"></i>';
                break;
        }
        
        // 设置内容
        toast.innerHTML = `
            <div class="fault-query__toast-content">
                ${icon}
                <span>${message}</span>
            </div>
        `;
        
        // 添加到容器
        toastContainer.appendChild(toast);
        
        // 触发动画
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });
        
        // 设置自动移除
        setTimeout(() => {
            toast.classList.remove('show');
            toast.classList.add('hide');
            
            // 动画结束后移除元素
            setTimeout(() => {
                toast.remove();
                // 如果容器为空，也移除容器
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            }, 300);
        }, 3000);
    }

    function handler(event) {
        return true;
    }

    function updateFaultPagination() {
        const paginationControls = document.getElementById('fault-pagination-controls');
        if (!paginationControls) return;
        
        let html = `
            <button class="page-button" onclick="changeFaultPage(${faultPageState.currentPage - 1})" 
                    ${faultPageState.currentPage === 1 ? 'disabled' : ''}>
                上一页
            </button>
        `;
        
        for (let i = 1; i <= faultPageState.totalPages; i++) {
            if (
                i === 1 || 
                i === faultPageState.totalPages || 
                (i >= faultPageState.currentPage - 2 && i <= faultPageState.currentPage + 2)
            ) {
                html += `
                    <button class="page-button ${i === faultPageState.currentPage ? 'active' : ''}" 
                            onclick="changeFaultPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (
                i === faultPageState.currentPage - 3 || 
                i === faultPageState.currentPage + 3
            ) {
                html += `<span>...</span>`;
            }
        }
        
        html += `
            <button class="page-button" onclick="changeFaultPage(${faultPageState.currentPage + 1})" 
                    ${faultPageState.currentPage === faultPageState.totalPages ? 'disabled' : ''}>
                下一页
            </button>
            <div class="page-jump">
                <span>跳至</span>
                <input type="text" class="page-input" value="${faultPageState.currentPage}" 
                       onchange="jumpToFaultPage(this.value)">
                <span>页</span>
            </div>
            <span class="total-count">共 ${faultPageState.totalItems} 条</span>
        `;
        
        paginationControls.innerHTML = html;
    }

    function changeFaultPage(page) {
        if (page < 1 || page > faultPageState.totalPages || page === faultPageState.currentPage) {
            return;
        }
        
        faultPageState.currentPage = page;
        updateFaultResultsTable(faultPageState.data);
    }

    function resetFaultForm() {
        const form = document.querySelector('.query-form');
        if (form) {
            form.reset();
        }

        faultPageState.currentPage = 1;
        faultPageState.data = [];
        faultPageState.totalItems = 0;
        faultPageState.totalPages = 0;

        const tbody = document.getElementById('fault-results-tbody');
        if (tbody) {
            tbody.innerHTML = '';
        }

        const tableSection = document.querySelector('.table-section');
        if (tableSection) {
            tableSection.style.display = 'none';
        }

        updateFaultPagination();
        showToast('查询条件已重置', 'info');
    }

    function toggleFaultColumnSettings() {
        const modal = document.getElementById('fault-column-settings-modal');
        if (!modal) return;

        // 直接定义完整的列数组
        const columns = ['orderNumber', 'productModel', 'productCode', 'productStatus', 'maintenanceCount', 
                        'reworkCount', 'testTime', 'tester'];
                        
        const allChecked = columns.every(col => faultColumnState[col]);
        
        // 设置全选复选框状态
        const selectAll = document.getElementById('select-all-fault');
        if (selectAll) {
            selectAll.checked = allChecked;
            // 添加change事件监听
            selectAll.onchange = () => toggleAllFaultColumns();
        }
        
        // 设置各列复选框状态
        columns.forEach(col => {
            const checkbox = document.getElementById(`col-fault-${col}`);
            if (checkbox) {
                checkbox.checked = faultColumnState[col];
                // 添加change事件监听
                checkbox.onchange = () => updateFaultSelectAllState();
            }
        });

        modal.style.display = 'block';
    }

    function closeFaultColumnSettings() {
        const modal = document.getElementById('fault-column-settings-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    function applyFaultColumnSettings() {
        const columns = ['orderNumber', 'productModel', 'productCode', 'workQty', 'faultRate', 
                        'productStatus', 'maintenanceCount', 'reworkCount', 'testTime', 'tester'];
        
        // 更新列状态
        columns.forEach(col => {
            const checkbox = document.getElementById(`col-fault-${col}`);
            if (checkbox) {
                faultColumnState[col] = checkbox.checked;
            }
        });
        
        // 如果有数据，立即更新表格显示
        if (faultPageState.data.length > 0) {
            updateFaultResultsTable(faultPageState.data);
        }
        
        closeFaultColumnSettings();
        showToast('列设置已更新', 'success');
    }

    function toggleAllFaultColumns() {
        const selectAll = document.getElementById('select-all-fault');
        if (!selectAll) return;
        
        const isChecked = selectAll.checked;
        const columnCheckboxes = document.querySelectorAll('.column-list input[type="checkbox"]:not(#select-all-fault)');
        
        // 更新所有列的选中状态
        columnCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        
        // 更新columnState对象
        Object.keys(faultColumnState).forEach(key => {
            if (key !== 'checkbox') { // 保持checkbox列始终显示
                faultColumnState[key] = isChecked;
            }
        });
    }

    function updateFaultSelectAllState() {
        const selectAll = document.getElementById('select-all-fault');
        if (!selectAll) return;
        
        const columnCheckboxes = document.querySelectorAll('.column-list input[type="checkbox"]:not(#select-all-fault)');
        const allChecked = Array.from(columnCheckboxes).every(checkbox => checkbox.checked);
        selectAll.checked = allChecked;
    }

    // 添加一个获取选中行数据的辅助函数
    function getSelectedFaultData() {
        const tbody = document.getElementById('fault-results-tbody');
        if (!tbody) return [];

        const selectedRows = [];
        const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
        const allData = faultPageState.data;

        checkboxes.forEach((checkbox, index) => {
            if (checkbox.checked) {
                // 获取当前页面的起始索引
                const startIndex = (faultPageState.currentPage - 1) * faultPageState.pageSize;
                // 计算实际数据索引
                const dataIndex = startIndex + index;
                if (allData[dataIndex]) {
                    selectedRows.push(allData[dataIndex]);
                }
            }
        });

        return selectedRows;
    }

    // 修改 exportFaultData 函数
    async function exportFaultData() {
        // 获取选中的行数据
        const selectedRows = getSelectedFaultData();
        
        // 如果有中的行，就使用选中的数据；否则使用所有数据
        const dataToExport = selectedRows.length > 0 ? selectedRows : faultPageState.data;

        if (!dataToExport || dataToExport.length === 0) {
            showToast('没有可导出的数据', 'warning');
            return;
        }

        try {
            // 计算每个工单的故障率
            const faultRates = {};
            faultPageState.data.forEach(item => {
                const orderNumber = item.orderNumber;
                if (!faultRates[orderNumber]) {
                    faultRates[orderNumber] = {
                        workQty: item.workQty || 0,
                        faultCount: 0
                    };
                }
                // 只统计产品状态为"新品"的故障数量
                if (item.productStatus === '新品') {
                    faultRates[orderNumber].faultCount++;
                }
            });

            // 计算故障率
            Object.keys(faultRates).forEach(orderNumber => {
                const { workQty, faultCount } = faultRates[orderNumber];
                if (workQty > 0) {
                    faultRates[orderNumber].rate = (faultCount / workQty * 100).toFixed(2);
                } else {
                    faultRates[orderNumber].rate = '0.00';
                }
            });

            // 定义导出的表头和对应的数据字段
            const exportConfig = [
                { header: '工单号', field: 'orderNumber' },
                { header: '产品型号', field: 'productModel' },
                { header: '产品编码', field: 'productCode' },
                { header: '生产数量', field: 'workQty', format: 'number' },
                { header: '故障率(%)', field: 'orderNumber', format: 'faultRate' },  // 添加故障率列
                { header: '产品SN号', field: 'serialNumber' },
                { header: '产品批次号', field: 'productBatch' },
                { header: '产品状态', field: 'productStatus' },
                { header: '维修次数', field: 'maintenanceCount', format: 'number' },
                { header: '返工次数', field: 'reworkCount', format: 'number' },
                { header: '测试时间', field: 'testTime', format: 'date' },
                { header: '测试人员', field: 'tester' },
                { header: '基础功能故障', field: 'faultDetails', format: 'basicFault' },
                { header: '通信接口故障', field: 'faultDetails', format: 'commFault' },
                { header: 'IO接口故障', field: 'faultDetails', format: 'ioFault' },
                { header: '指示控制故障', field: 'faultDetails', format: 'controlFault' },
                { header: '其他故障', field: 'faultDetails', format: 'otherFault' }
            ];

            // 提取表头
            const headers = exportConfig.map(config => config.header);

            // 处理数据行
            const rows = dataToExport.map(item => {
                return exportConfig.map(config => {
                    let value = item[config.field];
                    
                    // 根据不同的格式处理数据
                    switch (config.format) {
                        case 'faultRate':
                            // 获取对应工单的故障率
                            value = faultRates[item.orderNumber]?.rate || '0.00';
                            break;
                        case 'date':
                            value = value ? formatDate(value) : '-';
                            break;
                        case 'number':
                            value = (value === 0 || value) ? value.toString() : '-';
                            break;
                        case 'basicFault':
                        case 'commFault':
                        case 'ioFault':
                        case 'controlFault':
                        case 'otherFault':
                            const faultDetails = item.faultDetails || {};
                            const faults = [];
                            
                            // 根据不同的故障类型收集故障信息
                            switch (config.format) {
                                case 'basicFault':
                                    if (faultDetails.burn) faults.push('烧录异常');
                                    if (faultDetails.power) faults.push('上电异常');
                                    break;
                                case 'commFault':
                                    if (faultDetails.backplane) faults.push('Backplane Bus通信');
                                    if (faultDetails.rs485_1) faults.push('RS485_1通信');
                                    if (faultDetails.rs485_2) faults.push('RS485_2通信');
                                    if (faultDetails.rs232) faults.push('RS232通信');
                                    if (faultDetails.canbus) faults.push('CANbus通信');
                                    if (faultDetails.ethercat) faults.push('EtherCAT通信');
                                    break;
                                case 'ioFault':
                                    if (faultDetails.bodyIO) faults.push('Body I/O输入输出');
                                    if (faultDetails.netPort) faults.push('网口');
                                    if (faultDetails.usbDrive) faults.push('U盘接口');
                                    if (faultDetails.sdSlot) faults.push('SD卡卡槽');
                                    if (faultDetails.debugPort) faults.push('调试串口');
                                    break;
                                case 'controlFault':
                                    if (faultDetails.ledTube) faults.push('Led数码管');
                                    if (faultDetails.ledBulb) faults.push('Led灯珠');
                                    if (faultDetails.dipSwitch) faults.push('拨码开关');
                                    if (faultDetails.resetBtn) faults.push('复位按钮');
                                    break;
                                case 'otherFault':
                                    if (faultDetails.other) faults.push(faultDetails.other);
                                    break;
                            }
                            value = faults.join('、') || '-';
                            break;
                        default:
                            // 对于普通字段，保留 0 值
                            value = (value === 0 || value) ? value : '-';
                    }
                    
                    return value;
                });
            });

            // 生成CSV内容
            const worksheet = [headers, ...rows];
            const csvContent = '\uFEFF' + worksheet.map(row => 
                row.map(cell => 
                    `"${(cell || '').toString().replace(/"/g, '""')}"`
                ).join(',')
            ).join('\n');

            // 创建并下载文件
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `故障品记录_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 根据是否是选中导出显示不同的提示
            const exportType = selectedRows.length > 0 ? '选中' : '全部';
            showToast(`已成功导出${exportType}数据`, 'success');
        } catch (error) {
            Logger.error('导出失败:', error);
            showToast('导出失败，请稍后重试', 'error');
        }
    }

    function refreshFaultTable() {
        if (!faultPageState.data || faultPageState.data.length === 0) {
            showToast('没有数据可刷新', 'warning');
            return;
        }
        searchFault();
    }

    function jumpToFaultPage(page) {
        const pageNum = parseInt(page);
        if (isNaN(pageNum)) return;
        changeFaultPage(Math.min(Math.max(1, pageNum), faultPageState.totalPages));
    }

    function changeFaultPageSize(size) {
        const newSize = parseInt(size);
        if (isNaN(newSize) || newSize === faultPageState.pageSize) return;
        
        faultPageState.pageSize = newSize;
        faultPageState.currentPage = 1;
        faultPageState.totalPages = Math.ceil(faultPageState.data.length / faultPageState.pageSize);
        
        updateFaultResultsTable(faultPageState.data);
    }

    // 添加重置模态框位置的函数
    function resetFaultDetailModalPosition() {
        const modalContent = document.querySelector('#fault-detail-modal .fault-modal__content');
        if (modalContent) {
            modalContent.style.transform = 'translate(-50%, -50%)';
            modalContent.style.top = '50%';
            modalContent.style.left = '50%';
        }
    }

    // 修改showFaultDetail函数
    function showFaultDetail(data) {
        try {
            // 更新基本信息
            const basicInfoCard = document.querySelector('.fault-modal__grid');
            if (basicInfoCard) {
                basicInfoCard.innerHTML = `
                    <div style="display: flex; flex-direction: column; gap: 16px; font-size: 14px;">
                        <div style="display: flex;">
                            <div style="width: 100px;">工单号：</div>
                            <div>${data.orderNumber || '-'}</div>
                        </div>
                        <div style="display: flex;">
                            <div style="width: 100px;">产品型号：</div>
                            <div>${data.productModel || '-'}</div>
                        </div>
                        <div style="display: flex;">
                            <div style="width: 100px;">产品编码：</div>
                            <div>${data.productCode || '-'}</div>
                        </div>
                        <div style="display: flex;">
                            <div style="width: 100px;">产品SN号：</div>
                            <div>${data.serialNumber || '-'}</div>
                        </div>
                        <div style="display: flex;">
                            <div style="width: 100px;">产品批次号：</div>
                            <div>${data.productBatch || '-'}</div>
                        </div>
                    </div>
                `;
            }

            // 更新故障信息列表
            const faultList = document.getElementById('fault-detail-list');
            if (faultList) {
                // 按类型分组的故障项
                const faultGroups = [
                    {
                        title: '基础功能',
                        items: [
                            { key: 'burn', label: '烧录异常', field: 'burn_err' },
                            { key: 'power', label: '上电异常', field: 'power_err' }
                        ]
                    },
                    {
                        title: '通信接口',
                        items: [
                            { key: 'backplane', label: 'Backplane Bus通信', field: 'backplane_err' },
                            { key: 'rs485_1', label: 'RS485_1通信', field: 'rs485_1_err' },
                            { key: 'rs485_2', label: 'RS485_2通信', field: 'rs485_2_err' },
                            { key: 'rs232', label: 'RS232通信', field: 'rs232_err' },
                            { key: 'canbus', label: 'CANbus通信', field: 'canbus_err' },
                            { key: 'ethercat', label: 'EtherCAT通信', field: 'ethercat_err' }
                        ]
                    },
                    {
                        title: 'IO接口',
                        items: [
                            { key: 'bodyIO', label: 'Body I/O输入输出', field: 'body_io_err' },
                            { key: 'netPort', label: '网口', field: 'net_port_err' },
                            { key: 'usbDrive', label: 'U盘口', field: 'usb_drive_err' },
                            { key: 'sdSlot', label: 'SD卡卡槽', field: 'sd_slot_err' },
                            { key: 'debugPort', label: '调试串口', field: 'debug_port_err' }
                        ]
                    },
                    {
                        title: '指示与控制',
                        items: [
                            { key: 'ledTube', label: 'Led数码管', field: 'led_tube_err' },
                            { key: 'ledBulb', label: 'Led灯珠', field: 'led_bulb_err' },
                            { key: 'dipSwitch', label: '拨码开关', field: 'dip_switch_err' },
                            { key: 'resetBtn', label: '复位按钮', field: 'reset_btn_err' }
                        ]
                    }
                ];

                const faultDetails = data.faultDetails || {};
                
                // 收集所有异常项
                let hasErrors = false;
                let html = '';
                
                faultGroups.forEach((group, groupIndex) => {
                    const errorItems = group.items.filter(item => faultDetails[item.key]);
                    if (errorItems.length > 0) {
                        hasErrors = true;
                        html += `
                            <div class="fault-modal__group" style="animation-delay: ${groupIndex * 0.1}s">
                                <h4 class="fault-modal__group-title">${group.title}</h4>
                                <div class="fault-modal__group-content">
                                    ${errorItems.map((item, itemIndex) => `
                                        <div class="fault-modal__fault-item fault-modal__fault-item--error" 
                                             style="animation-delay: ${(groupIndex * 0.1 + itemIndex * 0.05)}s">
                                            <span class="fault-modal__fault-label">${item.label}</span>
                                            <span class="fault-modal__fault-status">异常</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }
                });

                // 添加其他问题（如果有）
                if (faultDetails.other) {
                    hasErrors = true;
                    html += `
                        <div class="fault-modal__group" style="animation-delay: ${faultGroups.length * 0.1}s">
                            <h4 class="fault-modal__group-title">其他问题</h4>
                            <div class="fault-modal__group-content">
                                <div class="fault-modal__fault-item fault-modal__fault-item--error fault-modal__fault-item--other">
                                    <span class="fault-modal__fault-label">问题描述</span>
                                    <span class="fault-modal__fault-status">${faultDetails.other}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }

                // 如果没有任何故障，显示提示信息
                if (!hasErrors) {
                    html = `
                        <div class="fault-modal__no-error">
                            <span>未发现故障</span>
                        </div>
                    `;
                }

                faultList.innerHTML = html;
            }

            // 显示模态框前重置位置
            resetFaultDetailModalPosition();
            
            // 显示模态框
            const modal = document.getElementById('fault-detail-modal');
            if (modal) {
                modal.style.display = 'block';
                
                // 添加ESC键监听
                document.addEventListener('keydown', handleFaultDetailEsc);
                
                // 添加拖动功能
                const modalContent = modal.querySelector('.fault-modal__content');
                const modalHeader = modal.querySelector('.fault-modal__header');
                
                let isDragging = false;
                let currentX;
                let currentY;
                let initialX;
                let initialY;
                let xOffset = 0;
                let yOffset = 0;

                modalHeader.addEventListener('mousedown', dragStart);
                document.addEventListener('mousemove', drag);
                document.addEventListener('mouseup', dragEnd);

                function dragStart(e) {
                    if (e.target === modalHeader || e.target.parentElement === modalHeader) {
                        initialX = e.clientX - xOffset;
                        initialY = e.clientY - yOffset;
                        isDragging = true;
                        modalContent.classList.add('dragging');
                    }
                }

                function drag(e) {
                    if (isDragging) {
                        e.preventDefault();
                        
                        currentX = e.clientX - initialX;
                        currentY = e.clientY - initialY;

                        // 限制拖动范围
                        const modalRect = modalContent.getBoundingClientRect();
                        const viewportWidth = window.innerWidth;
                        const viewportHeight = window.innerHeight;

                        // 限制左右范围
                        if (currentX < -(viewportWidth - modalRect.width) / 2) {
                            currentX = -(viewportWidth - modalRect.width) / 2;
                        }
                        if (currentX > (viewportWidth - modalRect.width) / 2) {
                            currentX = (viewportWidth - modalRect.width) / 2;
                        }

                        // 限制上下范围
                        if (currentY < -(viewportHeight - modalRect.height) / 2) {
                            currentY = -(viewportHeight - modalRect.height) / 2;
                        }
                        if (currentY > (viewportHeight - modalRect.height) / 2) {
                            currentY = (viewportHeight - modalRect.height) / 2;
                        }

                        xOffset = currentX;
                        yOffset = currentY;

                        setTranslate(currentX, currentY, modalContent);
                    }
                }

                function dragEnd(e) {
                    initialX = currentX;
                    initialY = currentY;
                    isDragging = false;
                    modalContent.classList.remove('dragging');
                }

                function setTranslate(xPos, yPos, el) {
                    el.style.transform = `translate(${xPos}px, ${yPos}px)`;
                }

                // 定义cleanup函数
                const cleanup = () => {
                    modalHeader.removeEventListener('mousedown', dragStart);
                    document.removeEventListener('mousemove', drag);
                    document.removeEventListener('mouseup', dragEnd);
                    document.removeEventListener('keydown', handleFaultDetailEsc);
                };

                // 将cleanup函数存储在modal元素上
                modal._cleanup = cleanup;

                // 添加关闭按钮事件
                const closeBtn = modal.querySelector('.fault-modal__close');
                if (closeBtn) {
                    closeBtn.onclick = closeFaultDetailModal;
                }

                // 添加点击模态框外部关闭
                modal.onclick = (e) => {
                    if (e.target === modal) {
                        closeFaultDetailModal();
                    }
                };
            }
        } catch (error) {
            Logger.error('显示详情时出错:', error);
            showToast('显示详情失败，请稍后重试', 'error');
        }
    }

    // 修改 toggleFaultSelectAll 函数
    function toggleFaultSelectAll() {
        const selectAll = document.getElementById('fault-select-all');
        const checkboxes = document.querySelectorAll('#fault-results-tbody input[type="checkbox"]');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        // 更新导出按钮的状态提示
        updateFaultExportButtonText();
    }

    // 添加更新导出按钮文本的函数
    function updateFaultExportButtonText() {
        const selectedCount = getSelectedFaultData().length;
        const exportButton = document.querySelector('.btn-export');
        if (exportButton) {
            if (selectedCount > 0) {
                exportButton.innerHTML = `<i class="fas fa-download"></i> 导出选中(${selectedCount})`;
            } else {
                exportButton.innerHTML = `<i class="fas fa-download"></i> 导出数据`;
            }
        }
    }

    // 修改关闭模态框函数
    function closeFaultDetailModal() {
        const modal = document.getElementById('fault-detail-modal');
        if (modal) {
            // 获取并执行cleanup函数(如果存在)
            const cleanup = modal._cleanup;
            if (typeof cleanup === 'function') {
                cleanup();
            }
            
            // 移除ESC事件监听器
            document.removeEventListener('keydown', handleFaultDetailEsc);
            
            // 隐藏模态框
            modal.style.display = 'none';
            
            // 清除cleanup引用
            delete modal._cleanup;
        }
    }

    // ESC 按键处理函数
    function handleFaultDetailEsc(e) {
        if (e.key === 'Escape') {
            closeFaultDetailModal();
        }
    }

    // 将需要的函数暴露到全局作用域
    window.initFaultQueryPage = initFaultQueryPage;
    window.searchFault = searchFault;
    window.resetFaultForm = resetFaultForm;
    window.showFaultDetail = showFaultDetail;
    window.toggleFaultColumnSettings = toggleFaultColumnSettings;
    window.closeFaultColumnSettings = closeFaultColumnSettings;
    window.applyFaultColumnSettings = applyFaultColumnSettings;
    window.exportFaultData = exportFaultData;
    window.refreshFaultTable = refreshFaultTable;
    window.toggleFaultSelectAll = toggleFaultSelectAll;
    window.changeFaultPage = changeFaultPage;
    window.jumpToFaultPage = jumpToFaultPage;
    window.changeFaultPageSize = changeFaultPageSize;
    window.toggleAllFaultColumns = toggleAllFaultColumns;
    window.updateFaultSelectAllState = updateFaultSelectAllState;
    window.closeFaultDetailModal = closeFaultDetailModal;
    window.handleFaultDetailEsc = handleFaultDetailEsc;

})();