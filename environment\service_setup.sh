#!/bin/bash

# 加载配置
source "$(dirname "$0")/config.sh"

echo "开始配置服务..."
check_root

# 创建supervisor配置
echo "配置Supervisor..."
cat > /etc/supervisor/conf.d/${PROJECT_NAME}.conf << EOF
[program:${PROJECT_NAME}]
directory=${PROJECT_PATH}
command=${VENV_PATH}/bin/python3.12 -m gunicorn -c gunicorn_config.py app:app
user=${APP_USER}
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true

stdout_logfile=${LOG_PATH}/supervisor_stdout.log
stderr_logfile=${LOG_PATH}/supervisor_stderr.log
stdout_logfile_maxbytes=50MB
stderr_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile_backups=10

environment=
    FLASK_ENV="production",
    FLASK_APP="app.py",
    DATABASE_URL="mysql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

[supervisord]
logfile=${LOG_PATH}/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/tmp/supervisord.pid
nodaemon=false
minfds=1024
minprocs=200
EOF
check_status "Supervisor配置失败"

# 重新加载supervisor配置
echo "重新加载Supervisor配置..."
supervisorctl reread
supervisorctl update
check_status "Supervisor更新失败"

# 启动应用
echo "启动应用..."
supervisorctl start ${PROJECT_NAME}
check_status "应用启动失败"

# 添加服务验证函数
verify_service() {
    echo "验证服务状态..."
    local timeout=${SERVICE_WAIT_TIMEOUT}
    local count=0
    
    while [ $count -lt $timeout ]; do
        if curl -s "http://${GUNICORN_HOST}:${GUNICORN_PORT}/health" >/dev/null; then
            echo "服务已成功启动"
            return 0
        fi
        echo "等待服务启动... ($count/$timeout)"
        sleep 1
        count=$((count + 1))
    done
    
    echo "服务启动超时"
    return 1
}

verify_service

echo "服务配置完成！" 