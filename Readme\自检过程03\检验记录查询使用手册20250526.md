# 检验记录页面使用说明书

## 📋 概述

检验记录页面是PLC生产质量检验系统的核心查询模块，用于查询、查看和管理质量检验记录。支持按多种条件查询工单检验状态，查看详细的检验过程和结果，并提供数据导出功能。

---

## 🔍 页面功能概览

### 主要功能
- **多维度查询**: 支持按工单号、SN号查询
- **高级筛选**: 支持日期范围查询
- **数据展示**: 分页显示查询结果
- **排序功能**: 支持按多个字段排序
- **详情查看**: 查看完整的检验记录详情
- **数据导出**: 导出Excel格式的检验记录
- **批量操作**: 支持选择性导出数据

---

## 🎯 使用指南

### 1. 查询类型选择

#### 1.1 基础查询
页面顶部提供两种查询类型：

**🔘 工单号查询**
- 默认选中的查询方式
- 支持模糊匹配
- 输入框提示："请输入工单号"
- 适用场景：已知工单号，需要查看该工单的所有检验记录

**🔘 SN号查询**  
- 按产品序列号查询
- 支持模糊匹配
- 输入框提示："请输入SN号"
- 适用场景：已知具体产品SN号，需要追溯该产品的检验历程

#### 1.2 高级查询
点击右上角的 **🔽 高级查询** 按钮可展开日期筛选功能：

**日期范围筛选**
- **开始日期**: 设置查询的起始日期
- **结束日期**: 设置查询的截止日期
- **智能校验**: 自动确保结束日期不早于开始日期
- **组合查询**: 可与基础查询条件组合使用

### 2. 执行查询

#### 2.1 查询操作
- **🔍 查询按钮**: 执行搜索操作
- **🔄 重置按钮**: 清空所有查询条件和结果
- **快捷键**: 在输入框中按Enter键快速执行查询

#### 2.2 查询要求
- 至少需要提供一个查询条件：
  - 查询内容（工单号或SN号）
  - 或日期范围
- 系统会显示相应的提示信息

### 3. 查询结果展示

#### 3.1 结果表格
查询结果以表格形式展示，包含以下列：

| 列名 | 说明 | 操作 |
|------|------|------|
| ☐ | 全选/单选复选框 | 用于批量导出 |
| 序号 | 当前页面的记录序号 | - |
| 工单号 | 工单编号 | 🔽 可排序 |
| SN号 | 产品序列号 | 🔽 可排序 |
| 产品类型 | 产品型号/规格 | 🔽 可排序 |
| 工单状态 | 当前工单状态 | 🔽 可排序 |
| 创建时间 | 工单创建时间 | 🔽 可排序 |
| 操作 | 功能按钮 | 👁️ 查看详情 |

#### 3.2 工单状态说明

| 状态标识 | 显示文本 | 含义 | 颜色标识 |
|----------|----------|------|----------|
| `completed` | **已完成** | 所有9个阶段-角色组合都已完成final提交 | 🟢 绿色 |
| `processing` | **检验中** | 有部分检验完成，但未全部完成 | 🟠 橙色 |
| `in_progress` | **进行中** | 检验正在进行中 | 🟠 橙色 |
| `pending` | **待处理** | 尚未开始检验 | ⚪ 灰色 |

### 4. 排序功能

#### 4.1 排序操作
- 点击表头的排序图标 📊 可对该列进行排序
- **首次点击**: 升序排列 ⬆️
- **再次点击**: 降序排列 ⬇️
- **当前排序列**: 高亮显示，图标变为 ⬆️ 或 ⬇️

#### 4.2 支持排序的字段
- 工单号
- SN号  
- 产品类型
- 工单状态
- 创建时间

### 5. 分页功能

#### 5.1 分页控件
页面底部提供完整的分页控制：

**分页选项**
- **每页显示**: 10/20/50/100 条记录可选
- **记录统计**: 显示总记录数
- **页码导航**: 
  - ⏮️ 首页
  - ◀️ 上一页  
  - 📄 页码按钮
  - ▶️ 下一页
  - ⏭️ 末页

#### 5.2 分页说明
- 修改每页显示数量会自动重新查询
- 页码显示智能优化（最多显示5个页码）
- 大数据量时自动截断显示

### 6. 详情查看

#### 6.1 打开详情
点击操作列的 **👁️ 查看详情** 按钮，弹出详情模态框

#### 6.2 详情内容结构

**📋 基本信息区**
- 工单号
- SN号（根据查询方式显示）
- 产品类型

**🔍 检验阶段详情**
系统包含3个检验阶段，每个阶段有3种检验角色：

**检验阶段**：
- **🔧 组装前检验** (Assembly)
- **⚡ 测试前检验** (Test)  
- **📦 包装前检验** (Packaging)

**检验角色**：
- **🥇 首检** (First) - 第一次检验
- **🔍 自检** (Self) - 自我检验
- **👨‍🔬 IPQC** (In-Process Quality Control) - 过程质量控制

#### 6.3 详情显示逻辑

**按工单维度查看**
- 显示该工单下所有检验阶段的完成情况
- 显示最新完成各阶段检验的SN号
- 检验项目状态基于最终提交结果

**按SN维度查看**  
- 显示特定SN的检验历程
- 突出显示查询的SN号
- 检验项目状态基于该SN的具体检验结果

#### 6.4 检验项目展示
每个角色区域显示：
- **检验人员**: 执行检验的操作员
- **检验时间**: 完成检验的时间
- **完成SN号**: 完成该阶段检验的产品SN
- **检验项目列表**: 
  - 项目名称
  - ✅ 通过 / ❌ 未通过状态
  - 备注信息（如适用）

#### 6.5 附件管理
如果检验过程中上传了附件，会在相应角色区域显示：
- **📎 附件列表**: 显示文件名、大小、上传时间、上传人
- **文件预览**: 
  - 🖼️ 图片文件：弹窗预览
  - 📄 PDF文件：内嵌预览
  - 📁 其他文件：提供下载选项

#### 6.6 详情操作
- **📸 导出图片**: 将详情页面导出为PNG图片
- **❌ 关闭**: 关闭详情模态框

### 7. 数据导出功能

#### 7.1 导出按钮显示
当有查询结果时，会显示 **📤 导出数据** 按钮

#### 7.2 导出选项

**全部导出**
- 无选中记录时：导出所有查询结果
- 按钮显示：**📤 导出数据**

**选择性导出**
- 选中特定记录时：只导出选中的记录
- 按钮显示：**📤 导出选中(N)**（N为选中数量）

#### 7.3 批量选择操作
- **全选复选框**: 选择/取消选择当前页所有记录
- **单行复选框**: 选择/取消选择单个记录
- **智能状态**: 自动更新全选状态

#### 7.4 导出格式
- **文件格式**: Excel (.xlsx)
- **文件命名**: `质检记录_YYYYMMDDHHMMSS_已选N条.xlsx`
- **导出内容**: 
  - 工单ID
  - 工单号
  - SN号（首个）
  - 产品类型
  - 工单状态  
  - 创建时间
  - 更新时间

### 8. 图片导出功能

#### 8.1 详情页图片导出
在详情模态框中，点击 **📸 导出图片** 按钮：
- 生成整个详情页面的高清截图
- 自动移除导出按钮避免在图片中显示
- 文件格式：PNG
- 文件命名：`检验记录详情_工单号_时间戳.png`

#### 8.2 技术要求
- 需要浏览器支持html2canvas库
- 生成2倍分辨率确保清晰度
- 支持滚动内容完整捕获

---

## ⚠️ 注意事项

### 系统要求
- **浏览器兼容**: 现代浏览器（Chrome、Firefox、Safari、Edge）
- **JavaScript**: 必须启用JavaScript
- **网络连接**: 稳定的网络连接用于数据加载

### 使用建议
1. **查询优化**: 使用具体的查询条件可获得更精确的结果
2. **大数据量**: 处理大量数据时建议使用日期范围限制查询
3. **导出注意**: 大批量导出可能需要较长时间，请耐心等待
4. **详情查看**: 按需查看详情，避免频繁打开造成系统负担

### 故障排除
- **查询无结果**: 检查查询条件是否正确，尝试扩大查询范围
- **页面加载慢**: 网络问题或数据量过大，尝试缩小查询范围
- **导出失败**: 检查网络连接，重新尝试导出操作
- **详情显示异常**: 刷新页面重新尝试

---

## 📞 技术支持

如遇到技术问题或功能异常，请联系系统管理员或技术支持团队。

---

**文档版本**: v1.0  
**最后更新**: 2025年1月  
**适用系统**: PLC生产质量检验系统 



----总结说明---：


我来帮您创建"检验记录"页面的使用说明书。首先让我检查是否存在InspectionRecordues.md文档：

Ran tool

Ran tool
我看到目录中没有InspectionRecordues.md文件，我将创建这个文档并写入详细的使用说明书：


## ✅ "检验记录"页面使用说明书已创建完成！

我已经为您创建了一份详细的**检验记录页面使用说明书**，保存在`InspectionRecordues.md`文档中。

### 📚 文档特色

#### 🎯 **结构完整**
- **概述**: 明确页面定位和用途
- **功能概览**: 快速了解主要功能
- **详细使用指南**: 8个主要功能模块的详细说明
- **注意事项**: 系统要求、使用建议和故障排除

#### 🔍 **内容全面**
涵盖了所有重要功能：

1. **查询功能**
   - 工单号/SN号查询
   - 高级日期筛选
   - 组合查询条件

2. **数据展示**
   - 分页显示控制
   - 多字段排序功能
   - 工单状态标识说明

3. **详情查看**
   - 模态框详情展示
   - 工单/SN双维度查看
   - 检验阶段和角色说明
   - 附件管理和预览

4. **导出功能**
   - Excel格式数据导出
   - 批量选择操作
   - PNG图片导出

#### ✨ **用户友好**
- **图标标识**: 使用丰富的emoji图标增强可读性
- **表格说明**: 清晰的表格展示功能对比
- **操作步骤**: 详细的操作指导
- **故障排除**: 常见问题的解决方案

#### 📋 **特别说明了工单状态**
根据您之前的修复，详细说明了4种工单状态：
- `completed` → **已完成** (🟢 绿色)
- `processing` → **检验中** (🟠 橙色) ← 您刚才修改的
- `in_progress` → **进行中** (🟠 橙色)
- `pending` → **待处理** (⚪ 灰色)

### 📖 文档价值
这份使用说明书可以：
- **培训新用户**: 快速上手系统操作
- **技术支持**: 减少重复性问题咨询
- **系统文档**: 作为系统功能的完整记录
- **质量管理**: 确保操作的规范性和一致性

文档现在已经保存在您的项目根目录中，可以随时查阅和分享给团队成员使用！
