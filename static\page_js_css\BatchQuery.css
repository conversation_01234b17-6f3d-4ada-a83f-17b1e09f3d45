/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
}

.dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Inter', sans-serif;
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    line-height: 1.6;
}

.container {
    padding: 24px;
    width: 100%;
    height: calc(100vh - 100px);
    overflow: auto;
}

/* 查询区域 */
.query-section {
    background-color: hsl(var(--card));
    padding: 24px;
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    margin-bottom: 24px;
}

.query-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 28px;
}

.form-item {
    position: relative;
}

.form-label {
    font-size: 14px;
    color: hsl(var(--muted-foreground));
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
}

.form-input,
.form-select {
    width: 100%;
    padding: 10px 14px;
    background-color: transparent;
    border: 1px solid hsl(var(--input));
    border-radius: var(--radius);
    font-size: 14px;
    color: hsl(var(--foreground));
    transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--ring)/0.1);
}

.form-input:hover,
.form-select:hover {
    border-color: hsl(var(--ring));
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: var(--radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
}

.btn-primary:hover {
    opacity: 0.9;
}

.btn-default {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
}

.btn-default:hover {
    background-color: hsl(var(--accent));
}

/* 工具栏 */
.toolbar-section {
    background-color: hsl(var(--card));
    padding: 16px 24px;
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

/* 表格样式 */
.table-section {
    background-color: hsl(var(--card));
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table-header th {
    background-color: hsl(var(--muted));
    padding: 12px 16px;
    font-weight: 500;
    color: hsl(var(--muted-foreground));
    font-size: 14px;
    text-align: left;
    border-bottom: 1px solid hsl(var(--border));
}

.table-row td {
    padding: 12px 16px;
    font-size: 14px;
    color: hsl(var(--foreground));
    border-bottom: 1px solid hsl(var(--border));
}

.table-row:hover {
    background-color: hsl(var(--accent));
}

/* 状态标签 */
.status-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px 10px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
    min-width: 80px;
}

.status-active {
    background-color: hsl(142.1 76.2% 36.3% / 0.1);
    color: hsl(142.1 76.2% 36.3%);
}

.status-inactive {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
}

/* 分页控件 */
.pagination-section {
    background-color: hsl(var(--card));
    padding: 16px 24px;
    border-top: 1px solid hsl(var(--border));
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-button {
    padding: 6px 12px;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: transparent;
    color: hsl(var(--foreground));
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-button:hover:not(:disabled) {
    background-color: hsl(var(--accent));
}

.page-button.active {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border-color: transparent;
}

.page-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-input {
    width: 40px !important;
    min-width: 40px;
    text-align: center;
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
}

.page-input:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

.page-jump {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;
}

/* 确保在移动端也能正常显示 */
@media (max-width: 768px) {
    .page-input {
        width: 2.5rem;
        min-width: 2.5rem;
    }
    
    .page-jump {
        margin-left: 8px;
    }
}

/* 修改模态框容器样式，添加特定前缀 */
.batch-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

/* 修改模态框内容样式 */
.batch-modal__content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: hsl(var(--card));
    border-radius: var(--radius);
    width: 90%;
    max-width: 1200px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: auto;
    margin: 0;
    transition: none !important;
    animation: none !important;
    resize: both;
    min-width: 300px;
    min-height: 200px;
}

/* 添加拖动时的样式 */
.batch-modal__content.dragging {
    opacity: 0.95;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    cursor: move;
    user-select: none;
}

/* 调整header样式支持拖动 */
.batch-modal__header {
    cursor: move;
    user-select: none;
    padding: 16px 24px;
    background: #f7f7f7;
    border-radius: 8px 8px 0 0;
    position: sticky;
    top: 0;
    z-index: 2;
}

/* 添加缩放手柄 */
.batch-modal__content::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 16px;
    height: 16px;
    cursor: se-resize;
    background: linear-gradient(135deg, transparent 50%, #ddd 50%);
}

/* 修改模态框body样式 */
.batch-modal__body {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    max-height: calc(80vh - 120px);
}

/* 确保所有子元素不影响定位 */
.batch-modal *,
.batch-modal__content * {
    transition: none !important;
    animation: none !important;
    transform-style: flat !important;
}

/* 添加滚动条样式 */
.batch-modal__body::-webkit-scrollbar {
    width: 8px;
}

.batch-modal__body::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
}

.batch-modal__body::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.batch-modal__body::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* 模态框标题样式 */
.batch-modal__header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid hsl(var(--border));
}

.batch-modal__header h2 {
    font-size: 20px;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin: 0;
}

/* 信息卡片样式 */
.info-card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    padding: 20px;
    margin-bottom: 24px;
}

.info-card h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
    margin: 0 0 16px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #e6f4ff;
    position: relative;
}

.info-card h3::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #1890ff;
}

/* 信息网格布局 */
.info-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.info-row {
    display: grid;
    grid-template-columns: 1fr 1fr;  /* 每行两列 */
    gap: 24px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-item label {
    font-size: 14px;
    color: hsl(var(--muted-foreground));
    font-weight: 500;
    min-width: 100px;  /* 固定标签宽度 */
}

.info-item span {
    font-size: 14px;
    color: hsl(var(--foreground));
    flex: 1;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .info-row {
        grid-template-columns: 1fr;  /* 在小屏幕上改为单列 */
        gap: 12px;
    }
}

/* 绑定信息样式 */
.binding-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.board-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
}

.board-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: hsl(var(--accent));
    border-radius: var(--radius);
    width: 100%;
}

.board-item label {
    font-size: 14px;
    color: hsl(var(--muted-foreground));
    font-weight: 500;
    min-width: 80px;
    margin-right: 16px;
}

.board-item span {
    font-size: 14px;
    color: hsl(var(--foreground));
    flex: 1;
    word-break: break-all;
}

/* 时间信息样式 */
.time-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.time-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.time-item label {
    font-size: 14px;
    color: hsl(var(--muted-foreground));
    font-weight: 500;
}

.time-item span {
    font-size: 14px;
    color: hsl(var(--foreground));
}

/* 响应式整 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10vh auto;
        padding: 16px;
    }

    .info-grid,
    .board-list,
    .time-info {
        grid-template-columns: 1fr;
    }

    .board-item {
        padding: 10px 12px;
    }

    .board-item label {
        min-width: 70px;
        margin-right: 12px;
    }
}

/* Toast提示 */
.toast {
    border-radius: var(--radius);
    padding: 16px 24px;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    font-weight: 500;
    background-color: hsl(var(--background));
    border: 1px solid hsl(var(--border));
}

.toast-success {
    border-color: hsl(142.1 76.2% 36.3%);
    color: hsl(142.1 76.2% 36.3%);
}

.toast-error {
    border-color: hsl(var(--destructive));
    color: hsl(var(--destructive));
}

.toast-warning {
    border-color: hsl(48 96% 53%);
    color: hsl(48 96% 53%);
}

.toast-info {
    border-color: hsl(221.2 83.2% 53.3%);
    color: hsl(221.2 83.2% 53.3%);
}

/* 滚动条 */
.container::-webkit-scrollbar {
    width: 14px;
}

.container::-webkit-scrollbar-track {
    background-color: transparent;
}

.container::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted));
    border: 4px solid hsl(var(--background));
    border-radius: 9999px;
}

.container::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
}

/* 加载动画 */
@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid hsl(var(--muted));
    border-top-color: hsl(var(--primary));
    border-radius: 50%;
    animation: spin 0.6s linear infinite;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .container {
        padding: 16px;
    }
    
    .query-form {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 12px;
    }
    
    .query-section,
    .toolbar-section {
        padding: 16px;
    }
    
    .query-form {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .toolbar-section {
        flex-direction: column;
        gap: 16px;
    }
    
    .toolbar-left,
    .toolbar-right {
        width: 100%;
        justify-content: space-between;
    }
    
    .pagination-section {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .page-size,
    .pagination-controls {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 8px;
    }
    
    .query-section,
    .toolbar-section {
        padding: 12px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 13px;
    }
    
    .table-header th,
    .table-row td {
        padding: 12px;
        font-size: 13px;
    }
}

/* 分页控件样式优化 */
.page-size {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-size select,
.form-select {
    width: 4rem;  /* 确保足够宽度显示数字 */
    min-width: 4rem;
    padding: 6px 8px;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-size: 14px;
    text-align: center;
    box-sizing: border-box;
    cursor: pointer;
    appearance: none;  /* 移除默认的下拉箭头 */
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' viewBox='0 0 24 24'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    padding-right: 24px;  /* 为下拉箭头留出空间 */
}

.page-size select:focus,
.form-select:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* 确保在移动端也能正��显示 */
@media (max-width: 768px) {
    .page-size select,
    .form-select {
        width: 3.5rem;
        min-width: 3.5rem;
    }
}

/* 添加表头居中样式和列宽控制 */
.table-header th {
    text-align: center !important;  /* 确保表头文字居中 */
}

/* 宽列样式 */
.table-cell-wide {
    width: 180px;  /* 较宽的列 */
    min-width: 180px;
}

/* 标准列样式 */
.table-cell-normal {
    width: 120px;  /* 标准宽度的列 */
    min-width: 120px;
}

/* 操作列样式 */
.table-cell-action {
    width: 100px;  /* 操作列宽度 */
    min-width: 100px;
}

/* 添加表格居中样式 */
.table-cell-center {
    text-align: center !important;
    vertical-align: middle !important;
}

/* 状态标签容器样式 */
.status-cell {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* 确保状态标签本身也是居中的 */
.status-tag {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 绑定信息标题样式保持一致 */
.binding-info h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
    margin: 0 0 16px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #e6f4ff;
    position: relative;
}

.binding-info h3::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #1890ff;
}

/* 列设置模态框样式优化 */
.column-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.column-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: var(--radius);
    transition: background-color 0.2s;
}

.column-item:hover {
    background-color: hsl(var(--accent));
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    cursor: pointer;
    font-size: 14px;
    color: hsl(var(--foreground));
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
}

.column-divider {
    height: 1px;
    background-color: hsl(var(--border));
    margin: 4px 0;
}

/* 模态框底部按钮样式 */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid hsl(var(--border));
}

/* 在文件末尾添加响应式布局样式 */

/* 查询表单响应式布局 */
#batch-query-content .query-form {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
    margin-bottom: 28px;
    align-items: flex-end;
}

#batch-query-content .form-item {
    flex: 1;
    min-width: 180px;
}

/* 大屏幕 (1440px及以上) */
@media screen and (min-width: 1440px) {
    #batch-query-content .query-form {
        gap: 24px;
    }

    #batch-query-content .form-item {
        min-width: 200px;
    }
}

/* 中等屏幕 (1200px - 1439px) */
@media screen and (max-width: 1439px) {
    #batch-query-content .query-form {
        gap: 16px;
    }

    #batch-query-content .form-item {
        min-width: 160px;
    }
}

/* 小屏幕 (992px - 1199px) */
@media screen and (max-width: 1199px) {
    #batch-query-content .query-form {
        flex-wrap: wrap;
        gap: 12px;
    }

    #batch-query-content .form-item {
        flex: 1 1 calc(33.333% - 8px);
    }

    #batch-query-content .query-buttons {
        width: 100%;
        justify-content: flex-end;
        margin-top: 16px;
    }
}

/* 平板 (768px - 991px) */
@media screen and (max-width: 991px) {
    #batch-query-content .query-form {
        gap: 10px;
    }

    #batch-query-content .form-item {
        flex: 1 1 calc(50% - 5px);
    }

    #batch-query-content .form-label {
        font-size: 13px;
    }

    #batch-query-content .form-input,
    #batch-query-content .form-select {
        font-size: 13px;
        padding: 8px 12px;
    }

    #batch-query-content .toolbar-section {
        flex-direction: column;
        gap: 12px;
    }

    #batch-query-content .toolbar-left,
    #batch-query-content .toolbar-right {
        width: 100%;
        justify-content: space-between;
    }
}

/* 手机 (768px以下) */
@media screen and (max-width: 767px) {
    #batch-query-content .query-section {
        padding: 16px;
    }

    #batch-query-content .query-form {
        gap: 8px;
    }

    #batch-query-content .form-item {
        flex: 1 1 100%;
        min-width: 0;
    }

    #batch-query-content .query-buttons {
        flex-direction: row;
        gap: 8px;
    }

    #batch-query-content .btn {
        flex: 1;
        padding: 8px 12px;
        font-size: 13px;
    }

    #batch-query-content .pagination-section {
        flex-direction: column;
        gap: 16px;
        align-items: center;
    }

    #batch-query-content .pagination-controls {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    #batch-query-content .page-size {
        width: 100%;
        justify-content: center;
    }
}

/* 表格响应式处理 */
@media screen and (max-width: 1200px) {
    #batch-query-content .table-section {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    #batch-query-content .data-table {
        min-width: 900px;
    }
}

/* 模态框响应式调整 */
@media screen and (max-width: 991px) {
    .batch-modal__content {
        width: 95%;
        max-width: none;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .board-list {
        gap: 8px;
    }
}

/* 打印样式优化 */
@media print {
    #batch-query-content {
        padding: 0;
    }

    #batch-query-content .query-section,
    #batch-query-content .toolbar-section,
    #batch-query-content .pagination-section {
        display: none;
    }

    #batch-query-content .table-section {
        overflow: visible;
    }

    #batch-query-content .data-table {
        width: 100%;
        min-width: auto;
    }
}

/* 在文件末尾添加总条数样式 */
#batch-query-content .total-count {
    margin-left: 16px;
    color: hsl(var(--muted-foreground));
    font-size: 14px;
}

/* 调整响应式布局中的总条数显示 */
@media screen and (max-width: 767px) {
    #batch-query-content .total-count {
        margin-left: 8px;
    }
}

/* 在文件末尾添加 Toast 样式 */

/* Toast 容器样式 */
.batch-query__toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.batch-query__toast {
    background: white;
    padding: 12px 24px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.batch-query__toast.show {
    opacity: 1;
    transform: translateY(0);
}

/* Toast 内容样式 */
.batch-query__toast-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    line-height: 1.5;
}

/* Toast 图标样式 */
.batch-query__toast i {
    font-size: 16px;
}

/* Toast 类型样式 */
.batch-query__toast--success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
}

.batch-query__toast--success i {
    color: #52c41a;
}

.batch-query__toast--error {
    background-color: #fff1f0;
    border: 1px solid #ffccc7;
}

.batch-query__toast--error i {
    color: #ff4d4f;
}

.batch-query__toast--warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
}

.batch-query__toast--warning i {
    color: #faad14;
}

.batch-query__toast--info {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
}

.batch-query__toast--info i {
    color: #1890ff;
}

/* 动画效果 */
@keyframes batchToastSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes batchToastSlideOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

.batch-query__toast {
    animation: batchToastSlideIn 0.3s ease forwards;
}

.batch-query__toast.hide {
    animation: batchToastSlideOut 0.3s ease forwards;
} 