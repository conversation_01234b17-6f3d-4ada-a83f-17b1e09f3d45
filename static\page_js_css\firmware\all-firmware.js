// 所有固件页面Vue应用
(function() {
    'use strict';
    
    Logger.log('Loading All Firmware Vue App...');
    
    // 添加文件Hash计算工具函数（兼容不安全环境）
    const calculateFileHash = async (file) => {
        // 检查是否支持 crypto.subtle API
        if (!window.crypto || !window.crypto.subtle) {
            throw new Error('当前环境不支持加密API，请使用HTTPS或localhost访问');
        }
        
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async function(e) {
                try {
                    const arrayBuffer = e.target.result;
                    const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
                    const hashArray = Array.from(new Uint8Array(hashBuffer));
                    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                    resolve(hashHex);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file);
        });
    };
    
    // 检查环境是否支持Hash计算
    const isHashSupported = () => {
        return window.crypto && window.crypto.subtle;
    };
    
    // 检查共享分页功能是否已加载
    if (!window.FirmwarePagination) {
        Logger.error('FirmwarePagination not loaded. Please include firmware-common-paging.js first.');
        return;
    }
    
    // 检查依赖
    if (!window.Vue || !window.ElementPlus) {
        Logger.error('Vue or ElementPlus not loaded');
        return;
    }

    const { createApp, ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } = Vue;
    const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;
    
    const AllFirmwareApp = {
        setup() {
            // 响应式数据
            const searchQuery = ref('');
            const sortProp = ref('');
            const sortOrder = ref('');
            const firmwareList = ref([]);
            const loading = ref(false);
            
            // 对话框状态
            const uploadDialogVisible = ref(false);
            const downloadDialogVisible = ref(false);
            const updateDialogVisible = ref(false);
            const versionHistoryDialogVisible = ref(false);
            
            // 工单验证状态
            const workOrderValid = ref(false);
            
            // 表单数据
            const uploadForm = reactive({
                editId: null,
                serialNumber: '',
                developer: '',
                name: '',
                version: '',
                file: null,
                productsText: '',
                versionRequirements: '',
                description: '',
                buildTime: '',
                backplaneVersion: '',
                ioVersion: ''
            });
            
            const downloadForm = reactive({
                firmwareId: '',
                serialNumber: '',
                firmwareName: '',
                firmwareVersion: '',
                approveTime: '',
                products: [],
                versionRequirements: '',
                developer: '',
                workOrder: '',
                productCode: '',
                productModel: '',
                burnCount: '',
                softwareVersion: '',
                buildTime: '',
                backplaneVersion: '',
                highSpeedIOVersion: '',
                notes: '',
                // userName 字段已改为后端自动获取当前用户，无需前端验证
            });
            
            const updateForm = reactive({
                currentId: '',
                currentSerialNumber: '',
                currentName: '',
                currentVersion: '',
                currentDeveloper: '',
                currentApproveTime: '',
                currentDownloadCount: '',
                currentBuildTime: '',
                currentBackplaneVersion: '',
                currentIoVersion: '',
                newSerialNumber: '',
                newName: '',
                newVersion: '',
                newDeveloper: '',
                newProductsText: '',
                file: null,
                description: '',
                versionRequirements: '',
                buildTime: '',
                backplaneVersion: '',
                ioVersion: ''
            });
            
            const versionHistory = ref([]);
            const fileList = ref([]);
            const updateFileList = ref([]);
            
            // 表单验证规则
            const uploadRules = reactive({
                serialNumber: [{ required: true, message: '请输入ERP流水号', trigger: 'blur' }],
                developer: [{ required: true, message: '请输入研发者姓名', trigger: 'blur' }],
                name: [{ required: true, message: '请输入固件名称', trigger: 'blur' }],
                version: [{ required: true, message: '请输入软件版本', trigger: 'blur' }],
                description: [{ required: true, message: '请输入更新日志', trigger: 'blur' }],
                versionRequirements: [{ required: true, message: '请输入使用要求', trigger: 'blur' }],
                productsText: [{ required: true, message: '请输入适用产品', trigger: 'blur' }],
                buildTime: [{ required: true, message: '请输入构建日期', trigger: 'blur' }]
            });
            
            const downloadRules = reactive({
                workOrder: [{ required: true, message: '请输入工单号', trigger: 'blur' }],
                productCode: [{ required: true, message: '请输入产品编码', trigger: 'blur' }],
                productModel: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
                burnCount: [{ required: true, message: '请输入生产数量', trigger: 'blur' }],
                softwareVersion: [{ required: true, message: '请输入软件版本', trigger: 'blur' }],
                buildTime: [{ required: true, message: '请输入构建时间', trigger: 'blur' }],
                // 背板总线版本、高速IO版本不再必填
                // userName 字段已改为后端自动获取当前用户，无需前端验证
            });
            
            const updateRules = reactive({
                newSerialNumber: [{ required: true, message: '请输入新ERP流水号', trigger: 'blur' }],
                newName: [{ required: true, message: '请输入新固件名称', trigger: 'blur' }],
                newVersion: [{ required: true, message: '请输入新软件版本', trigger: 'blur' }],
                newDeveloper: [{ required: true, message: '请输入研发者姓名', trigger: 'blur' }],
                newProductsText: [{ required: true, message: '请输入适用产品', trigger: 'blur' }],
                description: [{ required: true, message: '请输入更新日志', trigger: 'blur' }],
                versionRequirements: [{ required: true, message: '请输入版本使用要求', trigger: 'blur' }],
                buildTime: [{ required: true, message: '请输入构建日期', trigger: 'blur' }]
            });
            
            // 表单引用
            const uploadFormRef = ref(null);
            const downloadFormRef = ref(null);
            const updateFormRef = ref(null);
            
            // 获取全局数据管理器
            const dataManager = window.FirmwareDataManager;
            
            // 加载数据 - 改为异步
            const loadData = async () => {
                loading.value = true;
                try {
                    const data = await dataManager.getActiveFirmware(true);
                    firmwareList.value = data || [];
                } catch (error) {
                    Logger.error('[All Firmware] 数据加载失败:', error);
                    ElMessage.error('数据加载失败: ' + error.message);
                    firmwareList.value = [];
                } finally {
                    loading.value = false;
                }
            };
            
            // 事件监听器
            let eventListeners = [];
            
            // 计算属性
            const filteredFirmwareList = computed(() => {
                let list = firmwareList.value.filter(item => 
                    item.status === 'active' || item.status === 'rejected'
                );
                
                if (searchQuery.value) {
                    const query = searchQuery.value.toLowerCase();
                    list = list.filter(item => 
                        item.serialNumber.toLowerCase().includes(query) ||
                        item.name.toLowerCase().includes(query) ||
                        item.developer.toLowerCase().includes(query) ||
                        (item.products && item.products.some(p => p.model.toLowerCase().includes(query)))
                    );
                }
                
                if (sortProp.value) {
                    list.sort((a, b) => {
                        let aVal = a[sortProp.value];
                        let bVal = b[sortProp.value];
                        if (sortProp.value === 'products') {
                            aVal = a.products ? a.products.map(p => p.model).join(', ') : '';
                            bVal = b.products ? b.products.map(p => p.model).join(', ') : '';
                        }
                        const result = aVal < bVal ? -1 : (aVal > bVal ? 1 : 0);
                        return sortOrder.value === 'ascending' ? result : -result;
                    });
                }
                
                return list;
            });
            
            // 使用共享分页功能（在filteredFirmwareList定义之后）
            const pagination = window.FirmwarePagination.usePagination({
                dataList: filteredFirmwareList,
                defaultPageSize: 10,
                pageSizeOptions: [10, 20, 50, 100]
            });
            
            // 当前页显示的数据（使用共享分页功能）
            const paginatedFirmwareList = pagination.paginatedData;
            
            // 全局数据变化监听器
            const handleGlobalDataChange = async () => {
                await loadData();
            };
            
            // 方法
            const handleSearch = () => {
                // 搜索已通过计算属性实现
                // 搜索时重置到第一页
                pagination.resetToFirstPage();
            };
            
            const handleSearchClear = () => {
                searchQuery.value = '';
                // 清空搜索时重置到第一页
                pagination.resetToFirstPage();
            };
            
            const handleSortChange = (column) => {
                sortProp.value = column.prop;
                sortOrder.value = column.order;
            };
            
            const getRowFontColor = (row) => {
                return '#333';
            };
            
            const showUploadDialog = () => {
                Object.assign(uploadForm, {
                    editId: null,
                    serialNumber: '',
                    developer: '',
                    name: '',
                    version: '',
                    file: null,
                    productsText: '',
                    versionRequirements: '',
                    description: '',
                    buildTime: '',
                    backplaneVersion: '',
                    ioVersion: ''
                });
                fileList.value = [];
                // 确保文件状态完全重置
                uploadForm.file = null;
                uploadDialogVisible.value = true;
            };
            
            const showEditDialog = (row) => {
                Object.assign(uploadForm, {
                    editId: row.serialNumber,
                    serialNumber: row.serialNumber,
                    developer: row.developer,
                    name: row.name,
                    version: row.version,
                    file: null,
                    productsText: row.products ? row.products.map(p => p.model).join(', ') : '',
                    versionRequirements: row.versionRequirements || '',
                    description: row.description || '',
                    buildTime: row.buildTime || '',
                    backplaneVersion: row.backplaneVersion || '',
                    ioVersion: row.ioVersion || ''
                });
                fileList.value = [];
                // 确保文件状态完全重置
                uploadForm.file = null;
                uploadDialogVisible.value = true;
            };
            
            const showUpdateDialog = (row) => {
                Object.assign(updateForm, {
                    currentId: row.serialNumber,
                    currentSerialNumber: row.serialNumber,
                    currentName: row.name,
                    currentVersion: row.version,
                    currentDeveloper: row.developer,
                    currentApproveTime: row.approveTime,
                    currentDownloadCount: row.downloadCount,
                    currentBuildTime: row.buildTime || '',
                    currentBackplaneVersion: row.backplaneVersion || '',
                    currentIoVersion: row.ioVersion || '',
                    newProductsText: row.products ? row.products.map(p => p.model).join(', ') : '',
                    newSerialNumber: '',
                    newName: '',
                    newVersion: '',
                    newDeveloper: '',
                    file: null,
                    description: '',
                    versionRequirements: '',
                    buildTime: '',
                    backplaneVersion: '',
                    ioVersion: ''
                });
                updateFileList.value = [];
                // 确保文件状态完全重置
                updateForm.file = null;
                updateDialogVisible.value = true;
            };
            
            // 记录使用（原下载功能改名）
            const recordUsage = (row) => {
                Object.assign(downloadForm, {
                    firmwareId: row.id,
                    serialNumber: row.serialNumber,
                    firmwareName: row.name,
                    firmwareVersion: row.version,
                    approveTime: row.approveTime,
                    products: row.products || [],
                    versionRequirements: row.versionRequirements || '暂无',
                    developer: row.developer,
                    workOrder: '',
                    productCode: '',
                    productModel: '',
                    burnCount: '',
                    // 自动填入固件的技术规格信息到版本信息字段
                    softwareVersion: row.version || '',
                    buildTime: row.buildTime || '',
                    backplaneVersion: row.backplaneVersion || '',
                    highSpeedIOVersion: row.ioVersion || '',
                    notes: '',
                });
                workOrderValid.value = false; // 重置工单验证状态
                downloadDialogVisible.value = true;
            };
            
            // 新增：工单号自动填充产品信息
            const fetchOrderInfo = async () => {
                const orderNo = downloadForm.workOrder && downloadForm.workOrder.trim();
                if (!orderNo) {
                    workOrderValid.value = false;
                    return;
                }
                try {
                    const resp = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNo)}`);
                    const data = await resp.json();
                    if (data.success && data.order) {
                        downloadForm.productCode = data.order.ord_productCode || '';
                        downloadForm.productModel = data.order.ord_productModel || '';
                        downloadForm.burnCount = data.order.ord_productionQuantity || '';
                        workOrderValid.value = true;
                    } else {
                        downloadForm.productCode = '';
                        downloadForm.productModel = '';
                        downloadForm.burnCount = '';
                        workOrderValid.value = false;
                        ElMessage.warning(data.message || '未找到工单信息');
                    }
                } catch (e) {
                    downloadForm.productCode = '';
                    downloadForm.productModel = '';
                    downloadForm.burnCount = '';
                    workOrderValid.value = false;
                    ElMessage.error('工单信息获取失败');
                }
            };
            
            // 直接下载固件文件（新功能，带Hash验证）
            const directDownloadFirmware = async (row) => {
                try {
                    loading.value = true;
                    
                    // 检查是否有文件
                    if (!row.fileCount || row.fileCount === 0) {
                        ElMessage.warning('该固件暂无文件可下载');
                        return;
                    }
                    
                    ElMessage.info('正在准备下载文件...');
                    
                    // 使用fetch下载文件以获取响应头
                    const downloadUrl = `/api/firmware/all/file/${row.serialNumber}`;
                    const response = await fetch(downloadUrl);
                    
                    if (!response.ok) {
                        throw new Error('下载失败');
                    }
                    
                    // 获取文件Hash和其他信息
                    const serverHash = response.headers.get('X-File-Hash');
                    const fileSize = response.headers.get('X-File-Size');
                    const serialNumber = response.headers.get('X-Serial-Number');
                    
                    // 获取文件blob
                    const blob = await response.blob();
                    
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    
                    // 获取文件名（使用多种方法确保成功获取）
                    let filename = null;
                    
                    // 方法1：尝试从自定义响应头获取原始文件名
                    const originalFilename = response.headers.get('X-Original-Filename');
                    if (originalFilename) {
                        filename = originalFilename;
                        Logger.log('从X-Original-Filename获取文件名:', filename);
                    } else {
                        // 方法2：从Content-Disposition头解析文件名
                        const contentDisposition = response.headers.get('content-disposition');
                        if (contentDisposition) {
                            Logger.log('Content-Disposition:', contentDisposition);
                            
                            // 支持多种文件名格式
                            const patterns = [
                                /filename\*=UTF-8''([^;]+)/,  // RFC 5987格式（支持中文等特殊字符）
                                /filename="([^"]+)"/,         // 标准格式
                                /filename=([^;]+)/            // 无引号格式
                            ];
                            
                            for (const pattern of patterns) {
                                const match = contentDisposition.match(pattern);
                                if (match) {
                                    filename = decodeURIComponent(match[1]);
                                    Logger.log('从Content-Disposition解析文件名:', filename);
                                    break;
                                }
                            }
                        }
                    }
                    
                    // 方法3：如果以上都失败，使用默认格式但尽量保持原始扩展名
                    if (!filename) {
                        Logger.log('无法获取原始文件名，使用默认格式');
                        
                        // 尝试从服务器响应推断文件类型
                        const contentType = response.headers.get('content-type');
                        let extension = '.bin'; // 默认扩展名
                        
                        if (contentType) {
                            Logger.log('Content-Type:', contentType);
                            // 根据content-type推断扩展名
                            if (contentType.includes('zip')) extension = '.zip';
                            else if (contentType.includes('rar')) extension = '.rar';
                            else if (contentType.includes('tar')) extension = '.tar';
                            else if (contentType.includes('7z')) extension = '.7z';
                            else if (contentType.includes('hex')) extension = '.hex';
                            else if (contentType.includes('bin')) extension = '.bin';
                            // 可以根据需要添加更多文件类型
                        }
                        
                        filename = `${row.serialNumber}_${row.version}${extension}`;
                        Logger.log('使用默认文件名:', filename);
                    }
                    
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    
                    ElMessage.success('文件下载完成');
                    
                    // 如果有服务器Hash，提示用户进行验证
                    if (serverHash) {
                        ElMessage({
                            type: 'info',
                            message: '文件下载完成，建议验证文件完整性',
                            duration: 5000
                        });
                        
                        // 可选：自动验证下载的文件
                        if (isHashSupported()) {
                            if (window.confirm('是否立即验证下载文件的完整性？')) {
                                try {
                                    ElMessage.info('正在计算下载文件的验证码...');
                                    const downloadedHash = await calculateFileHash(blob);
                                    
                                    if (downloadedHash.toLowerCase() === serverHash.toLowerCase()) {
                                        ElMessage.success('文件完整性验证通过，文件未损坏');
                                    } else {
                                        ElMessage.error('文件完整性验证失败，建议重新下载');
                                        Logger.error('Hash验证失败:', {
                                            server: serverHash,
                                            downloaded: downloadedHash
                                        });
                                    }
                                } catch (error) {
                                    Logger.error('Hash验证过程失败:', error);
                                    ElMessage.warning('无法验证文件完整性，请手动检查');
                                }
                            } else {
                                ElMessage({
                                    type: 'info',
                                    message: `文件Hash值: ${serverHash}`,
                                    duration: 10000
                                });
                            }
                        } else {
                            ElMessage({
                                type: 'warning',
                                message: `文件Hash值: ${serverHash}<br>当前环境不支持自动验证，请使用页面上的"验证文件完整性"按钮手动验证`,
                                duration: 15000,
                                dangerouslyUseHTMLString: true
                            });
                        }
                    }
                    
                } catch (error) {
                    Logger.error('直接下载固件失败:', error);
                    ElMessage.error('下载失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            };
            
            // 显示版本历史 - 改为异步
            const showVersionHistory = async (row) => {
                try {
                    loading.value = true;
                    const history = await dataManager.getVersionHistory(row.serialNumber);
                    versionHistory.value = history || [];
                    versionHistoryDialogVisible.value = true;
                } catch (error) {
                    Logger.error('获取版本历史失败:', error);
                    ElMessage.error('获取版本历史失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            };
            
            const exportToExcel = () => {
                if (window.FirmwareUtils) {
                    window.FirmwareUtils.exportToExcel(filteredFirmwareList.value, '所有固件列表');
                } else {
                    ElMessage.info('正在导出数据...');
                }
            };
            
            // 显示文件验证工具
            const showFileVerifier = () => {
                if (window.FileHashVerifier) {
                    window.FileHashVerifier.show();
                } else {
                    ElMessage.error('文件验证工具未加载');
                }
            };
            
            const handleFileChange = (file, fileList) => {
                // 处理文件删除的情况
                if (fileList.length === 0) {
                    uploadForm.file = null;
                    fileList.value = [];
                } else {
                    uploadForm.file = file.raw;
                    fileList.value = fileList.slice(-1);
                }
            };
            
            // 新增：明确处理文件删除
            const handleFileRemove = () => {
                uploadForm.file = null;
                fileList.value = [];
            };
            
            const handleUpdateFileChange = (file, fileList) => {
                // 处理文件删除的情况
                if (fileList.length === 0) {
                    updateForm.file = null;
                    updateFileList.value = [];
                } else {
                    updateForm.file = file.raw;
                    updateFileList.value = fileList.slice(-1);
                }
            };
            
            // 新增：明确处理更新对话框的文件删除
            const handleUpdateFileRemove = () => {
                updateForm.file = null;
                updateFileList.value = [];
            };
            
            // 提交上传 - 改为异步
            const submitUpload = async () => {
                const valid = await uploadFormRef.value?.validate();
                if (!valid) return;
                
                try {
                    loading.value = true;
                    
                    let clientHash = null;
                    
                    // 如果有文件，先计算客户端Hash
                    if (uploadForm.file) {
                        if (isHashSupported()) {
                            ElMessage.info('正在计算文件完整性验证码...');
                            try {
                                clientHash = await calculateFileHash(uploadForm.file);
                                Logger.log('客户端文件Hash:', clientHash);
                                ElMessage.success('文件完整性验证码计算完成');
                            } catch (error) {
                                Logger.warn('计算客户端Hash失败:', error);
                                ElMessage.warning('文件完整性验证码计算失败，将跳过验证');
                            }
                        } else {
                            Logger.warn('当前环境不支持Hash计算（需要HTTPS或localhost）');
                            ElMessage.warning('当前环境不支持文件完整性验证，建议使用HTTPS访问');
                        }
                    }
                    
                    const firmwareData = {
                        serialNumber: uploadForm.serialNumber.trim(),
                        name: uploadForm.name.trim(),
                        version: uploadForm.version.trim(),
                        developer: uploadForm.developer.trim(),
                        productsText: uploadForm.productsText.trim(),
                        versionRequirements: uploadForm.versionRequirements.trim(),
                        description: uploadForm.description.trim(),
                        file: uploadForm.file,
                        buildTime: uploadForm.buildTime.trim(),
                        backplaneVersion: uploadForm.backplaneVersion.trim(),
                        ioVersion: uploadForm.ioVersion.trim(),
                        clientHash: clientHash // 添加客户端计算的Hash
                    };
                    
                    if (uploadForm.editId) {
                        // 修改现有固件
                        await dataManager.updateFirmware(uploadForm.editId, firmwareData);
                        ElMessage.success('修改成功，重新进入审核流程');
                    } else {
                        // 添加新固件
                        await dataManager.addFirmware(firmwareData);
                        ElMessage.success('上传成功，文件完整性验证通过，已进入审核流程');
                    }
                    
                    uploadDialogVisible.value = false;
                    await loadData(); // 刷新数据
                } catch (error) {
                    Logger.error('提交失败:', error);
                    if (error.message && error.message.includes('Hash验证失败')) {
                        ElMessage.error('文件完整性验证失败，请重新上传文件');
                    } else {
                        ElMessage.error('提交失败: ' + error.message);
                    }
                } finally {
                    loading.value = false;
                }
            };
            
            // 提交更新 - 改为异步
            const submitUpdate = async () => {
                const valid = await updateFormRef.value?.validate();
                if (!valid) return;
                
                try {
                    loading.value = true;
                    
                    const newVersionData = {
                        serialNumber: updateForm.newSerialNumber.trim(),
                        name: updateForm.newName.trim(),
                        version: updateForm.newVersion.trim(),
                        developer: updateForm.newDeveloper.trim(),
                        productsText: updateForm.newProductsText.trim(),
                        versionRequirements: updateForm.versionRequirements.trim(),
                        description: updateForm.description.trim(),
                        file: updateForm.file,
                        buildTime: updateForm.buildTime.trim(),
                        backplaneVersion: updateForm.backplaneVersion.trim(),
                        ioVersion: updateForm.ioVersion.trim()
                    };
                    
                    await dataManager.createVersionUpdate(updateForm.currentId, newVersionData);
                    
                    ElMessage.success('版本更新提交成功，等待审核');
                    updateDialogVisible.value = false;
                    await loadData(); // 刷新数据
                } catch (error) {
                    Logger.error('版本更新失败:', error);
                    ElMessage.error('版本更新失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            };
            
            // 确认使用 - 改为异步（原确认下载功能修改）
            const confirmUsage = async () => {
                const valid = await downloadFormRef.value?.validate();
                if (!valid) return;
                
                // 检查工单是否有效
                if (!workOrderValid.value) {
                    ElMessage.error('请输入有效的工单号');
                    return;
                }
                
                try {
                    loading.value = true;
                    
                    const usageRecord = {
                        serialNumber: downloadForm.serialNumber.trim(),
                        workOrder: downloadForm.workOrder.trim(),
                        productCode: downloadForm.productCode.trim(),
                        productModel: downloadForm.productModel.trim(),
                        burnCount: downloadForm.burnCount,
                        softwareVersion: downloadForm.softwareVersion.trim(),
                        buildTime: downloadForm.buildTime.trim(),
                        backplaneVersion: downloadForm.backplaneVersion.trim(),
                        highSpeedIOVersion: downloadForm.highSpeedIOVersion.trim(),
                        notes: downloadForm.notes.trim()
                    };
                    
                    // 调用新的使用记录API（不下载文件）
                    await dataManager.addUsageRecord(usageRecord);
                    
                    ElMessage.success('使用记录保存成功');
                    downloadDialogVisible.value = false;
                    await loadData(); // 刷新数据以更新使用次数
                } catch (error) {
                    Logger.error('保存使用记录失败:', error);
                    ElMessage.error('保存使用记录失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            };
            
            // 删除固件
            const deleteFirmware = async (row) => {
                try {
                    // 安全检查：只能删除rejected状态的固件
                    if (row.status !== 'rejected') {
                        ElMessage.warning('只能删除审核退回的固件');
                        return;
                    }
                    
                    // 二次确认
                    const confirmed = await ElMessageBox.confirm(
                        `确定要删除固件 "${row.name}" (${row.serialNumber}) 吗？\n\n此操作将：\n• 删除固件基本信息\n• 删除固件文件\n• 保留使用记录和审核流水\n\n删除后无法恢复！`,
                        '删除固件确认',
                        {
                            confirmButtonText: '确定删除',
                            cancelButtonText: '取消',
                            type: 'warning',
                            dangerouslyUseHTMLString: false
                        }
                    );
                    
                    if (confirmed !== 'confirm') return;
                    
                    loading.value = true;
                    
                    await dataManager.deleteFirmware(row.serialNumber);
                    
                    ElMessage.success('固件删除成功');
                    await loadData(); // 刷新数据
                } catch (error) {
                    if (error.message && error.message.includes('cancel')) {
                        // 用户取消操作，不显示错误
                        return;
                    }
                    Logger.error('删除固件失败:', error);
                    ElMessage.error('删除固件失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            };
            
            // 生命周期
            onMounted(async () => {
                await loadData();
                
                // 监听数据更新事件
                const onDataUpdate = async () => {
                    await loadData();
                };
                
                eventListeners = [
                    { event: 'firmware-added', handler: onDataUpdate },
                    { event: 'firmware-updated', handler: onDataUpdate },
                    { event: 'firmware-approved', handler: onDataUpdate },
                    { event: 'firmware-rejected', handler: onDataUpdate },
                    { event: 'firmware-obsoleted', handler: onDataUpdate },
                    { event: 'version-updated', handler: onDataUpdate },
                    { event: 'firmware-downloaded', handler: onDataUpdate },
                    { event: 'usage-record-added', handler: onDataUpdate },
                    { event: 'firmware-used', handler: onDataUpdate },
                    { event: 'firmware-deleted', handler: onDataUpdate },
                    { event: 'global-data-refresh', handler: onDataUpdate }
                ];
                
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.on(event, handler);
                });
                
                // 添加全局window事件监听（用于跨页面通信）
                window.addEventListener('firmware-data-changed', handleGlobalDataChange);
                
                // 监听 downloadDialogVisible 打开时，绑定工单号输入框事件
                watch(
                    () => downloadDialogVisible.value,
                    (visible) => {
                        if (visible) {
                            nextTick(() => {
                                const input = document.querySelector('.el-form-item input[placeholder="请输入工单编号"]');
                                if (input) {
                                    input.addEventListener('blur', fetchOrderInfo);
                                    input.addEventListener('keydown', (e) => {
                                        if (e.key === 'Enter') fetchOrderInfo();
                                    });
                                }
                            });
                        }
                    }
                );
            });
            
            onUnmounted(() => {
                // 清理事件监听器
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.off(event, handler);
                });
                
                // 清理全局事件监听器
                window.removeEventListener('firmware-data-changed', handleGlobalDataChange);
            });
            
            return {
                // 数据
                searchQuery,
                firmwareList,
                filteredFirmwareList,
                paginatedFirmwareList,
                loading,
                versionHistory,
                fileList,
                updateFileList,
                workOrderValid,
                
                // 分页数据（使用共享分页功能）
                ...pagination,
                
                // 对话框
                uploadDialogVisible,
                downloadDialogVisible,
                updateDialogVisible,
                versionHistoryDialogVisible,
                
                // 表单
                uploadForm,
                downloadForm,
                updateForm,
                uploadFormRef,
                downloadFormRef,
                updateFormRef,
                uploadRules,
                downloadRules,
                updateRules,
                
                // 方法
                handleSearch,
                handleSearchClear,
                handleSortChange,
                getRowFontColor,
                showUploadDialog,
                showEditDialog,
                showUpdateDialog,
                recordUsage,
                showVersionHistory,
                exportToExcel,
                showFileVerifier,
                handleFileChange,
                handleFileRemove,
                handleUpdateFileChange,
                handleUpdateFileRemove,
                submitUpload,
                submitUpdate,
                confirmUsage,
                directDownloadFirmware,
                deleteFirmware,
                
                // 工具函数
                statusMap: window.FirmwareUtils?.statusMap || {
                    'active': '已生效',
                    'pending': '待审核',
                    'rejected': '审核退回',
                    'obsolete': '已作废'
                },
                formatDate: window.FirmwareUtils?.formatDate || ((date) => date)
            };
        },
        
        template: `
        <div class="firmware-container all-firmware firmware-page">
            <div class="firmware-page__search-bar all-firmware__search-bar">
                <el-input
                    class="firmware-page__search-input all-firmware__search-input"
                    placeholder="请输入ERP流水号、固件名称或适用产品进行搜索"
                    v-model="searchQuery"
                    clearable
                    @clear="handleSearchClear"
                    @keyup.enter="handleSearch">
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
                                    <div class="firmware-page__action-buttons all-firmware__action-buttons">
                        <el-button type="primary" @click="showUploadDialog" class="firmware-page__action-button firmware-page__action-button--primary">上传新固件</el-button>
                        <el-button type="success" @click="exportToExcel" class="firmware-page__action-button firmware-page__action-button--success">导出数据</el-button>
                        <el-button type="info" @click="showFileVerifier" class="firmware-page__action-button firmware-page__action-button--info">验证文件完整性</el-button>
                    </div>
                <span class="firmware-page__tip all-firmware__tip">
                    （单击软件版本，可查看版本历史）
                </span>
            </div>
            
            <div class="firmware-page__table-container">
                <el-table 
                    :data="paginatedFirmwareList" 
                    class="all-firmware__table"
                    style="width: 100%"
                    border
                    size="small"
                    v-loading="loading"
                    @sort-change="handleSortChange">
                    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                    <el-table-column prop="serialNumber" label="ERP流水号" width="150" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="name" label="固件名称" min-width="220" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="version" label="软件版本" width="170" sortable="custom" show-overflow-tooltip>
                        <template #default="{ row }">
                            <el-link 
                                type="primary"
                                @click="showVersionHistory(row)"
                                class="all-firmware__version-link">
                                {{ row.version }}
                            </el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="100" sortable="custom">
                        <template #default="{ row }">
                            <span :class="'all-firmware__status--' + row.status">
                                {{ statusMap[row.status] }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="products" label="适用产品" width="180" sortable="custom" show-overflow-tooltip>
                        <template #default="{ row }">
                            {{ row.products.map(p => p.model).join(', ') }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="versionRequirements" label="版本使用要求" width="200" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="description" label="变更内容" width="200" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="developer" label="研发者" width="120" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="downloadCount" label="下载次数" width="100" sortable="custom"></el-table-column>
                    <el-table-column prop="usageCount" label="使用次数" width="100" sortable="custom"></el-table-column>
                    <el-table-column prop="approveTime" label="生效时间" width="180" sortable="custom">
                        <template #default="{ row }">
                            {{ formatDate(row.approveTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="approverName" label="审核者" width="120" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="uploadTime" label="上传时间" width="180" sortable="custom">
                        <template #default="{ row }">
                            {{ formatDate(row.uploadTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="uploader" label="上传者" width="120" sortable="custom" show-overflow-tooltip></el-table-column>
                    
                    <!-- 新增：下载固件列 -->
                    <el-table-column label="下载固件" width="120" align="center">
                        <template #default="{ row }">
                            <el-button 
                                v-if="row.status === 'active'"
                                class="all-firmware__action-button"
                                size="small" 
                                type="success" 
                                @click="directDownloadFirmware(row)">
                                下载固件
                            </el-button>
                            <span v-else class="all-firmware__no-download">-</span>
                        </template>
                    </el-table-column>
                    
                    <!-- 新增：删除固件列 -->
                    <el-table-column label="删除固件" width="120" align="center">
                        <template #default="{ row }">
                            <el-button 
                                v-if="row.status === 'rejected'"
                                class="all-firmware__action-button"
                                size="small" 
                                type="danger" 
                                @click="deleteFirmware(row)">
                                删除固件
                            </el-button>
                            <span v-else class="all-firmware__no-delete">-</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="操作" width="180" fixed="right">
                        <template #default="{ row }">
                            <!-- 修改：下载按钮改为使用按钮 -->
                            <el-button 
                                v-if="row.status === 'active'"
                                class="all-firmware__action-button"
                                size="small" 
                                type="primary" 
                                @click="recordUsage(row)">使用</el-button>
                            <el-button 
                                v-if="row.status === 'rejected'" 
                                class="all-firmware__action-button"
                                size="small" 
                                type="info" 
                                @click="showEditDialog(row)">修改</el-button>
                            <el-button 
                                v-if="row.status === 'active'"
                                class="all-firmware__action-button"
                                size="small" 
                                type="warning" 
                                @click="showUpdateDialog(row)">更新版本</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页组件（使用共享模板） -->
                <div class="table-footer">
                    <div class="table-length">
                        <div class="page-size-selector">
                            每页
                            <select @change="handleSizeChange($event.target.value)">
                                <option 
                                    v-for="size in pageSizes" 
                                    :key="size" 
                                    :value="size" 
                                    :selected="size === pageSize">
                                    {{ size }}
                                </option>
                            </select>
                            条
                        </div>
                        <div class="total-count">
                            共计 <span>{{ totalCount }}</span> 条
                        </div>
                    </div>
                    <div class="table-pagination">
                        <button 
                            class="btn btn-icon" 
                            @click="goToFirstPage" 
                            :disabled="currentPage === 1">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button 
                            class="btn btn-icon" 
                            @click="goToPrevPage" 
                            :disabled="currentPage === 1">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <div class="pagination-pages">
                            <span v-if="startPage > 1" class="pagination-ellipsis">...</span>
                            <button 
                                v-for="page in visiblePages" 
                                :key="page"
                                class="btn btn-page" 
                                :class="{ active: page === currentPage }"
                                @click="goToPage(page)">
                                {{ page }}
                            </button>
                            <span v-if="endPage < totalPages" class="pagination-ellipsis">...</span>
                        </div>
                        <button 
                            class="btn btn-icon" 
                            @click="goToNextPage" 
                            :disabled="currentPage === totalPages">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button 
                            class="btn btn-icon" 
                            @click="goToLastPage" 
                            :disabled="currentPage === totalPages">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 上传/编辑对话框 -->
            <el-dialog 
                :title="uploadForm.editId ? '修改固件信息' : '上传新固件'" 
                v-model="uploadDialogVisible" 
                width="50%"
                class="firmware-modal__dialog firmware-modal__upload-dialog">
                <el-form 
                    :model="uploadForm" 
                    :rules="uploadRules" 
                    ref="uploadFormRef" 
                    label-width="120px"
                    class="firmware-modal__form">
                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">基本信息</span>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="ERP流水号" prop="serialNumber">
                                <el-input v-model="uploadForm.serialNumber" placeholder="请输入ERP流水号"></el-input>
                            </el-form-item>
                            <el-form-item label="研发者" prop="developer">
                                <el-input v-model="uploadForm.developer" placeholder="请输入研发者姓名"></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="固件名称" prop="name">
                                <el-input v-model="uploadForm.name" placeholder="请输入固件名称"></el-input>
                            </el-form-item>
                            <el-form-item label="软件版本" prop="version">
                                <el-input v-model="uploadForm.version" placeholder="请输入软件版本"></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="构建日期" prop="buildTime">
                                <el-input v-model="uploadForm.buildTime" placeholder="请输入构建日期"></el-input>
                            </el-form-item>
                            <el-form-item label="背板总线版本">
                                <el-input v-model="uploadForm.backplaneVersion" placeholder="IO、耦合器可不填"></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="高速IO版本">
                                <el-input v-model="uploadForm.ioVersion" placeholder="IO、耦合器可不填"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">详细信息</span>
                        </div>
                        <el-form-item label="适用产品" prop="productsText">
                            <el-input 
                                type="textarea" 
                                v-model="uploadForm.productsText" 
                                placeholder="请输入适用产品，多个产品用逗号分隔"
                                :rows="3">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="变更内容" prop="description">
                            <el-input 
                                type="textarea" 
                                v-model="uploadForm.description" 
                                placeholder="请输入变更内容信息"
                                :rows="3">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="版本使用要求" prop="versionRequirements">
                            <el-input 
                                type="textarea" 
                                v-model="uploadForm.versionRequirements" 
                                :rows="3" 
                                placeholder="库存处理：不涉及/不处理/返工&#10;在制处理：不涉及/不处理/返工&#10;已售处理：不涉及/不召回/召回返工">
                            </el-input>
                        </el-form-item>
                    </div>

                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">固件文件</span>
                        </div>
                        <el-form-item label="固件文件">
                            <el-upload
                                action=""
                                :on-change="handleFileChange"
                                :on-remove="handleFileRemove"
                                :auto-upload="false"
                                :file-list="fileList"
                                class="firmware-modal__upload">
                                <div class="firmware-modal__upload-area">
                                    <el-button type="primary" class="firmware-modal__upload-button">
                                        选择文件
                                    </el-button>
                                    <span class="firmware-modal__upload-tip">（注：只能上传单个文件，若多个文件，打包压缩后上传）</span>
                                </div>
                                <template #tip>
                                    <div class="firmware-modal__upload-tip">请上传固件文件，大小不超过500MB</div>
                                </template>
                            </el-upload>
                        </el-form-item>
                    </div>
                </el-form>
                <template #footer>
                    <div class="firmware-modal__footer">
                        <el-button @click="uploadDialogVisible = false" class="firmware-modal__button-cancel">取消</el-button>
                        <el-button type="primary" @click="submitUpload" class="firmware-modal__button-submit">
                            {{ uploadForm.editId ? '确认修改' : '提交审核' }}
                        </el-button>
                    </div>
                </template>
            </el-dialog>

            <!-- 更新固件对话框 -->
            <el-dialog title="更新固件版本" v-model="updateDialogVisible" width="50%" class="firmware-modal__dialog firmware-modal__update-dialog">
                <el-form 
                    :model="updateForm" 
                    :rules="updateRules" 
                    ref="updateFormRef" 
                    label-width="120px"
                    class="firmware-modal__form">
                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">原固件信息</span>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="原ERP流水号">
                                <el-input v-model="updateForm.currentSerialNumber" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="原固件名称">
                                <el-input v-model="updateForm.currentName" disabled></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="原软件版本">
                                <el-input v-model="updateForm.currentVersion" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="原构建日期">
                                <el-input v-model="updateForm.currentBuildTime" disabled></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="原背板总线版本">
                                <el-input v-model="updateForm.currentBackplaneVersion" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="原高速IO版本">
                                <el-input v-model="updateForm.currentIoVersion" disabled></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="原研发者">
                                <el-input v-model="updateForm.currentDeveloper" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="下载次数">
                                <el-input v-model="updateForm.currentDownloadCount" disabled></el-input>
                            </el-form-item>
                        </div>
                        <el-form-item label="生效时间">
                            <el-input v-model="updateForm.currentApproveTime" disabled></el-input>
                        </el-form-item>
                    </div>
                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">新版本信息</span>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="新ERP流水号" prop="newSerialNumber">
                                <el-input v-model="updateForm.newSerialNumber" placeholder="请输入新ERP流水号"></el-input>
                            </el-form-item>
                            <el-form-item label="新研发者" prop="newDeveloper">
                                <el-input v-model="updateForm.newDeveloper" placeholder="请输入研发者姓名"></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="新固件名称" prop="newName">
                                <el-input v-model="updateForm.newName" placeholder="请输入新固件名称"></el-input>
                            </el-form-item>
                            <el-form-item label="新软件版本" prop="newVersion">
                                <el-input v-model="updateForm.newVersion" placeholder="请输入新软件版本"></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="构建日期" prop="buildTime">
                                <el-input v-model="updateForm.buildTime" placeholder="请输入构建日期"></el-input>
                            </el-form-item>
                            <el-form-item label="背板总线版本">
                                <el-input v-model="updateForm.backplaneVersion" placeholder="IO、耦合器可不填"></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="高速IO版本">
                                <el-input v-model="updateForm.ioVersion" placeholder="IO、耦合器可不填"></el-input>
                            </el-form-item>
                        </div>
                        <el-form-item label="适用产品" prop="newProductsText">
                            <el-input 
                                type="textarea" 
                                v-model="updateForm.newProductsText" 
                                placeholder="请输入适用产品，多个产品用逗号隔开"
                                :rows="3">
                            </el-input>
                        </el-form-item>
                    </div>
                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">详细信息</span>
                        </div>
                        <el-form-item label="版本使用要求" prop="versionRequirements">
                            <el-input 
                                type="textarea" 
                                v-model="updateForm.versionRequirements" 
                                :rows="3" 
                                placeholder="库存处理：不涉及/不处理/返工&#10;在制处理：不涉及/不处理/返工&#10;已售处理：不涉及/不召回/召回返工">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="更新日志" prop="description">
                            <el-input 
                                type="textarea" 
                                v-model="updateForm.description" 
                                placeholder="请输入更新日志信息"
                                :rows="3">
                            </el-input>
                        </el-form-item>
                    </div>

                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">固件文件</span>
                        </div>
                        <el-form-item label="固件文件">
                            <el-upload
                                action=""
                                :on-change="handleUpdateFileChange"
                                :on-remove="handleUpdateFileRemove"
                                :auto-upload="false"
                                :file-list="updateFileList"
                                class="firmware-modal__upload">
                                <div class="firmware-modal__upload-area">
                                    <el-button type="primary" class="firmware-modal__upload-button">
                                        选择文件
                                    </el-button>
                                    <span class="firmware-modal__upload-tip">（注：只能上传单个文件，若多个文件，打包压缩后上传）</span>
                                </div>
                                <template #tip>
                                    <div class="firmware-modal__upload-tip">请上传固件文件，大小不超过500MB</div>
                                </template>
                            </el-upload>
                        </el-form-item>
                    </div>
                </el-form>
                <template #footer>
                    <div class="firmware-modal__footer">
                        <el-button @click="updateDialogVisible = false" class="firmware-modal__button-cancel">取消</el-button>
                        <el-button type="primary" @click="submitUpdate" class="firmware-modal__button-submit">提交更新</el-button>
                    </div>
                </template>
            </el-dialog>

            <!-- 记录使用对话框（原下载固件对话框） -->
            <el-dialog title="记录使用信息" v-model="downloadDialogVisible" width="50%" class="firmware-modal__dialog firmware-modal__usage-dialog">
                <el-form 
                    :model="downloadForm" 
                    :rules="downloadRules" 
                    ref="downloadFormRef" 
                    label-width="120px"
                    class="firmware-modal__form">
                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">固件信息</span>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="ERP流水号">
                                <el-input v-model="downloadForm.serialNumber" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="研发者">
                                <el-input v-model="downloadForm.developer" disabled></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="固件名称">
                                <el-input v-model="downloadForm.firmwareName" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="软件版本">
                                <el-input v-model="downloadForm.firmwareVersion" disabled></el-input>
                            </el-form-item>
                        </div>
                        <el-form-item label="生效时间">
                            <el-input v-model="downloadForm.approveTime" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="适用产品">
                            <el-input :model-value="downloadForm.products.map(p => p.model).join(', ')" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="版本使用要求">
                            <el-input v-model="downloadForm.versionRequirements" disabled></el-input>
                        </el-form-item>
                    </div>
                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">使用信息</span>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="工单号" prop="workOrder">
                                <el-input v-model="downloadForm.workOrder" placeholder="请输入工单编号"></el-input>
                            </el-form-item>
                            <el-form-item label="产品编码" prop="productCode">
                                <el-input v-model="downloadForm.productCode" placeholder="请输入产品编码" disabled></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="产品型号" prop="productModel">
                                <el-input v-model="downloadForm.productModel" placeholder="请输入产品型号" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="生产数量" prop="burnCount">
                                <el-input v-model="downloadForm.burnCount" placeholder="请输入生产数量" type="number" disabled></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    
                    <div class="firmware-modal__section">
                        <div class="firmware-modal__section-header">
                            <span class="firmware-modal__section-title">版本信息</span>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="软件版本" prop="softwareVersion">
                                <el-input v-model="downloadForm.softwareVersion" placeholder="请输入软件版本"></el-input>
                            </el-form-item>
                            <el-form-item label="构建日期" prop="buildTime">
                                <el-input v-model="downloadForm.buildTime" placeholder="请输入构建日期"></el-input>
                            </el-form-item>
                        </div>
                        <div class="firmware-modal__row">
                            <el-form-item label="背板总线版本" prop="backplaneVersion">
                                <el-input v-model="downloadForm.backplaneVersion" placeholder="IO、耦合器不必填写"></el-input>
                            </el-form-item>
                            <el-form-item label="高速IO版本" prop="highSpeedIOVersion">
                                <el-input v-model="downloadForm.highSpeedIOVersion" placeholder="IO、耦合器不必填写"></el-input>
                            </el-form-item>
                        </div>
                        <el-form-item label="备注" prop="notes">
                            <el-input type="textarea" v-model="downloadForm.notes" :rows="3" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </div>
                </el-form>
                <template #footer>
                    <div class="firmware-modal__footer">
                        <el-button @click="downloadDialogVisible = false" class="firmware-modal__button-cancel">取消</el-button>
                        <el-button type="primary" @click="confirmUsage" class="firmware-modal__button-submit">确认使用</el-button>
                    </div>
                </template>
            </el-dialog>

            <!-- 版本历史对话框 -->
            <el-dialog title="版本历史" v-model="versionHistoryDialogVisible" width="60%" class="firmware-modal firmware-history-modal">
                <div class="firmware-history">
                    <div class="firmware-history__timeline" v-if="versionHistory.length > 0">
                        <el-timeline>
                            <el-timeline-item 
                                v-for="(history, index) in versionHistory" 
                                :key="history.id"
                                :timestamp="formatDate(history.uploadTime)"
                                placement="top"
                                :color="history.status === 'active' ? '#67C23A' : '#F56C6C'">
                                
                                <div class="firmware-history__item" :style="{'--item-index': index}">
                                    <div class="firmware-history__header">
                                        <div class="firmware-history__timestamp">
                                            <i class="el-icon-time"></i>
                                        </div>
                                        <div class="firmware-history__status" :class="['firmware-history__status--' + history.status]">
                                            {{ statusMap[history.status] }}
                                        </div>
                                    </div>
                                    
                                    <div class="firmware-history__content">
                                        <div class="firmware-history__left">
                                            <div class="firmware-history__info-item">
                                                <span class="firmware-history__info-label">ERP流水号：</span>
                                                <span class="firmware-history__info-value">{{ history.serialNumber }}</span>
                                            </div>
                                            <div class="firmware-history__info-item">
                                                <span class="firmware-history__info-label">固件名称：</span>
                                                <span class="firmware-history__info-value">{{ history.name }}</span>
                                            </div>
                                            <div class="firmware-history__info-item">
                                                <span class="firmware-history__info-label">软件版本：</span>
                                                <span class="firmware-history__info-value">{{ history.version }}</span>
                                            </div>
                                            <div class="firmware-history__info-item">
                                                <span class="firmware-history__info-label">构建日期：</span>
                                                <span class="firmware-history__info-value">{{ history.buildTime || '暂无' }}</span>
                                            </div>
                                            <div class="firmware-history__info-item">
                                                <span class="firmware-history__info-label">背板总线版本：</span>
                                                <span class="firmware-history__info-value">{{ history.backplaneVersion || '暂无' }}</span>
                                            </div>
                                            <div class="firmware-history__info-item">
                                                <span class="firmware-history__info-label">高速IO版本：</span>
                                                <span class="firmware-history__info-value">{{ history.ioVersion || '暂无' }}</span>
                                            </div>
                                            <div class="firmware-history__info-item">
                                                <span class="firmware-history__info-label">研发者：</span>
                                                <span class="firmware-history__info-value">{{ history.developer }}</span>
                                            </div>
                                            <div class="firmware-history__info-item">
                                                <span class="firmware-history__info-label">生效时间：</span>
                                                <span class="firmware-history__info-value">{{ formatDate(history.approveTime) || '尚未生效' }}</span>
                                            </div>
                                            <div class="firmware-history__info-item" v-if="history.status === 'obsolete'">
                                                <span class="firmware-history__info-label">作废时间：</span>
                                                <span class="firmware-history__info-value">{{ formatDate(history.obsoleteTime) }}</span>
                                            </div>
                                            <div class="firmware-history__info-item" v-if="history.status === 'obsolete'">
                                                <span class="firmware-history__info-label">生效天数：</span>
                                                <span class="firmware-history__info-value">{{ history.effectiveDays !== null ? history.effectiveDays + ' 天' : '-' }}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="firmware-history__right">
                                                                        <div class="firmware-history__section">
                                <div class="firmware-history__section-title">适用产品</div>
                                <div class="firmware-history__section-content">
                                    <div class="firmware-history__product-list" v-if="history.products && history.products.length">
                                        <el-tag 
                                            v-for="product in history.products" 
                                            :key="product.code"
                                            class="firmware-history__product-tag">
                                            {{ product.model }}
                                        </el-tag>
                                    </div>
                                    <span v-else class="firmware-history__empty-text">暂无</span>
                                </div>
                            </div>
                                            
                                            <div class="firmware-history__divider"></div>
                                            
                                            <div class="firmware-history__section">
                                                <div class="firmware-history__section-title">版本使用要求</div>
                                                <div class="firmware-history__section-content">
                                                    {{ history.versionRequirements || '暂无' }}
                                                </div>
                                            </div>
                                            
                                            <div class="firmware-history__divider"></div>
                                            
                                            <div class="firmware-history__section">
                                                <div class="firmware-history__section-title">变更内容</div>
                                                <div class="firmware-history__section-content">
                                                    {{ history.description || '暂无' }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-timeline-item>
                        </el-timeline>
                    </div>
                    <div v-else class="firmware-history__empty">
                        <div class="firmware-history__empty-icon">
                            <i class="el-icon-document"></i>
                        </div>
                        <div class="firmware-history__empty-text">暂无版本历史</div>
                    </div>
                </div>
                <template #footer>
                    <div class="firmware-history__footer">
                        <el-button @click="versionHistoryDialogVisible = false" class="firmware-modal__button-cancel">关闭</el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
        `
    };

    // 创建并挂载Vue应用
    const app = createApp(AllFirmwareApp);
    app.use(ElementPlus);

    // 注册Element Plus图标
    if (window.ElementPlusIconsVue) {
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
    }

    // 挂载应用
    const mountApp = () => {
        try {
            app.mount('#all-firmware-app-container');
            Logger.log('All Firmware Vue app mounted successfully');
            
            // 保存应用实例供清理使用
            window.currentFirmwareApp = app;
        } catch (error) {
            Logger.error('Failed to mount All Firmware Vue app:', error);
        }
    };

    // Defer mounting until the DOM is fully loaded to prevent race conditions
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', mountApp);
    } else {
        mountApp();
    }
})();