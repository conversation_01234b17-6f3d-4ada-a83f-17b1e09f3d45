/* SN打印记录页面样式 */
:root {
    --primary-color: #409eff;
    --success-color: #67c23a;
    --warning-color: #e6a23c;
    --danger-color: #f56c6c;
    --info-color: #909399;
    --primary-text: #303133;
    --regular-text: #606266;
    --secondary-text: #909399;
    --placeholder: #c0c4cc;
    --border-color: #dcdfe6;
    --border-light: #e4e7ed;
    --background: #f5f7fa;
    --card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    --radius: 0.25rem;
    --spacing-base: clamp(1rem, 2vw, 1.5rem);
    --font-size-base: 1rem;
    --font-size-lg: clamp(1.1rem, 1vw + 0.5rem, 1.25rem);
    --font-size-xl: clamp(1.25rem, 1.5vw + 0.5rem, 1.5rem);
}

/* 基础容器样式 */
.sn-print-record {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: var(--spacing-base);
    font-family: 'Noto Sans SC', 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 标题样式 */
.sn-print-record__title {
    color: var(--primary-text);
    font-size: var(--font-size-xl);
    font-weight: 500;
    margin-bottom: 1.5rem;
    padding-bottom: 0.625rem;
    position: relative;
    border-bottom: 1px solid var(--border-light);
}

/* 水平布局容器样式 */
.sn-print-record__horizontal-layout {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-base);
    margin-bottom: var(--spacing-base);
}

/* 卡片容器样式 */
.sn-print-record__card {
    background: white;
    border-radius: var(--radius);
    box-shadow: var(--card-shadow);
    margin-bottom: var(--spacing-base);
    overflow: hidden;
    transition: box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.sn-print-record__card:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

/* 左侧卡片和右侧卡片样式 */
.sn-print-record__card--left,
.sn-print-record__card--right {
    flex: 1;
    min-width: 300px;
    margin-bottom: 0;
}

/* 卡片头部样式 */
.sn-print-record__card-header {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 1rem var(--spacing-base);
    border-bottom: 1px solid var(--border-light);
}

.sn-print-record__card-header i {
    margin-right: 0.75rem;
    color: var(--primary-color);
    font-size: 1.25rem;
}

.sn-print-record__card-header h2 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--primary-text);
    flex: 1;
}

.sn-print-record__count {
    color: var(--primary-color);
    font-weight: 500;
    margin-left: auto;
}

/* 表单样式 */
.sn-print-record__form {
    padding: var(--spacing-base);
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 220px;  /* 确保两个卡片高度一致 */
}

.sn-print-record__row {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-base);
    margin-bottom: var(--spacing-base);
}

.sn-print-record__row:last-child {
    margin-bottom: 0;
}

.sn-print-record__form-group {
    flex: 1;
    min-width: 10rem;
}

/* 标签样式 */
.sn-print-record__label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: var(--font-size-base);
    color: var(--primary-text);
    font-weight: 500;
}

/* 输入框样式 */
.sn-print-record__input {
    width: 100%;
    height: 2.5rem;
    padding: 0 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: var(--font-size-base);
    color: var(--primary-text);
    background-color: white;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.sn-print-record__input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.sn-print-record__input:hover {
    border-color: #c0c4cc;
}

.sn-print-record__input::placeholder {
    color: var(--placeholder);
}

/* 高亮输入框 */
.sn-print-record__input--highlight {
    background-color: white;
    border-color: var(--primary-color);
    font-weight: 500;
    color: var(--primary-color);
    font-size: 1.1rem;
    letter-spacing: 0.5px;
    text-align: center;
    height: 2.75rem;
}

/* **新增：特殊格式订货号的样式** */
.sn-print-record__input--special-format {
    background-color: #fff7e6;
    border-color: var(--warning-color);
    color: var(--warning-color);
    font-weight: 600;
}

.sn-print-record__input--special-format::placeholder {
    color: var(--warning-color);
    opacity: 0.8;
}

/* 特殊格式提示样式 */
.sn-print-record__special-format-hint {
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--warning-color);
    font-weight: 500;
    background-color: #fff7e6;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    border-left: 3px solid var(--warning-color);
}

/* 只读输入框样式 */
.sn-print-record__input[readonly] {
    background-color: white;
    cursor: text;
}

/* 输入提示 */
.sn-print-record__input-hint {
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--secondary-text);
}

/* 操作按钮区域样式 */
.sn-print-record__actions-container {
    background: white;
    border-radius: var(--radius);
    box-shadow: var(--card-shadow);
    margin-bottom: var(--spacing-base);
    padding: 1rem var(--spacing-base);
    transition: box-shadow 0.3s;
}

.sn-print-record__actions-container:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

/* 操作按钮和首件SN的水平布局 */
.sn-print-record__actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

/* 首件SN行样式 - 使标签和输入框在同一水平线上 */
.sn-print-record__first-sn-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
}

.sn-print-record__first-sn-row .sn-print-record__label {
    margin-bottom: 0;
    white-space: nowrap;
    min-width: 5rem;
    color: var(--primary-color);
    font-weight: 600;
}

.sn-print-record__first-sn-row .sn-print-record__input {
    flex: 1;
}

/* 首件SN包装器样式 */
.sn-print-record__first-sn-wrapper {
    flex: 1;
    min-width: 300px;
    background-color: #ecf5ff;
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    border-left: 4px solid var(--primary-color);
    display: flex;
    align-items: center;
}

/* 按钮包装器样式 */
.sn-print-record__buttons-wrapper {
    flex: 2;
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* 按钮基础样式 */
.sn-print-record__btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25;
    height: 2.75rem; /* 与首件SN输入框高度一致 */
    border-radius: var(--radius);
    border: none;
    cursor: pointer;
    transition: all 0.3s;
    color: white;
    white-space: nowrap;
}

.sn-print-record__btn i {
    font-size: 1rem;
}

/* 主要按钮 */
.sn-print-record__btn--primary {
    background-color: var(--primary-color);
}

.sn-print-record__btn--primary:hover {
    background-color: #66b1ff;
}

/* 次要按钮 */
.sn-print-record__btn--secondary {
    background-color: white;
    color: var(--regular-text);
    border: 1px solid var(--border-color);
}

.sn-print-record__btn--secondary:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: #ecf5ff;
}

/* 成功按钮 */
.sn-print-record__btn--success {
    background-color: var(--success-color);
}

.sn-print-record__btn--success:hover {
    background-color: #85ce61;
}

/* 信息按钮 */
.sn-print-record__btn--info {
    background-color: #1976D2;
}

.sn-print-record__btn--info:hover {
    background-color: #2196F3;
}

/* 表格容器 */
.sn-print-record__table-container {
    width: 100%;
    overflow-y: auto;
    height: 370px; /* 固定高度，与扫描验证区域保持一致 */
    padding: 0 var(--spacing-base);
}

/* 表格样式 */
.sn-print-record__table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    table-layout: fixed;
}

.sn-print-record__table th,
.sn-print-record__table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.sn-print-record__table th {
    background-color: #f8f9fa;
    color: var(--primary-text);
    font-weight: 500;
    white-space: nowrap;
}

.sn-print-record__table tr:nth-child(even) {
    background-color: #f9fafb;
}

.sn-print-record__table tr:hover {
    background-color: #f0f7ff;
}

/* 移除空白占位区样式，不再需要 */
.sn-print-record__spacer {
    display: none;
}

/* 响应式设计 */
/* 小屏幕 */
@media screen and (max-width: 480px) {
    .sn-print-record__row {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .sn-print-record__form-group {
        min-width: 100%;
    }
    
    .sn-print-record__actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .sn-print-record__first-sn-wrapper,
    .sn-print-record__buttons-wrapper {
        width: 100%;
    }
    
    .sn-print-record__buttons-wrapper {
        margin-top: 1rem;
    }
    
    .sn-print-record__btn {
        width: calc(50% - 0.375rem); /* 每行两个按钮，考虑间距 */
        flex: 0 0 auto;
    }
    
    .sn-print-record__buttons-wrapper {
        justify-content: space-between;
    }
}

/* 中屏幕 */
@media screen and (min-width: 481px) and (max-width: 768px) {
    .sn-print-record__actions {
        flex-wrap: wrap;
        justify-content: space-between;
    }
    
    .sn-print-record__first-sn-wrapper,
    .sn-print-record__buttons-wrapper {
        width: 100%;
    }
    
    .sn-print-record__buttons-wrapper {
        margin-top: 1rem;
    }
    
    .sn-print-record__btn {
        flex: 1 0 calc(50% - 0.375rem);
    }
}

/* 响应式设计 - 水平布局在小屏幕上变成垂直布局 */
@media screen and (max-width: 768px) {
    .sn-print-record__horizontal-layout {
        flex-direction: column;
    }
    
    .sn-print-record__card--left,
    .sn-print-record__card--right {
        width: 100%;
        margin-bottom: var(--spacing-base);
    }
    
    .sn-print-record__card--right {
        margin-bottom: 0;
    }
    
    .sn-print-record__table th:first-child,
    .sn-print-record__table td:first-child {
        width: 25%;
    }
    
    .sn-print-record__table th:last-child,
    .sn-print-record__table td:last-child {
        width: 75%;
    }
}

/* 添加双栏布局样式 */
.sn-print-record__twin-layout {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-base);
    margin-bottom: var(--spacing-base);
}

.sn-print-record__card--list,
.sn-print-record__card--verify {
    flex: 1;
    min-width: 380px;
}

/* 扫描验证区域样式 */
.sn-print-record__verify-container {
    padding: var(--spacing-base);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: 370px; /* 与表格容器保持一致的高度 */
}

.sn-print-record__scan-input-wrapper {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.sn-print-record__scan-input-group {
    display: flex;
    gap: 0.5rem;
}

.sn-print-record__input--scan {
    flex: 1;
    height: 2.75rem;
    font-size: 1.1rem;
    border: 2px solid var(--primary-color);
    padding: 0 1rem;
}

.sn-print-record__scan-status {
    font-size: 0.875rem;
    padding: 0.5rem;
    border-radius: var(--radius);
    background-color: #f8f9fa;
    border-left: 3px solid var(--info-color);
}

.sn-print-record__scan-status.success {
    background-color: #f0f9eb;
    border-left-color: var(--success-color);
    color: var(--success-color);
}

.sn-print-record__scan-status.error {
    background-color: #fef0f0;
    border-left-color: var(--danger-color);
    color: var(--danger-color);
}

.sn-print-record__progress-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.sn-print-record__progress-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--primary-text);
}

.sn-print-record__progress-bar-container {
    height: 1.25rem;
    background-color: #e9ecef;
    border-radius: var(--radius);
    overflow: hidden;
}

.sn-print-record__progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    font-size: 0.75rem;
    line-height: 1.25rem;
    transition: width 0.3s ease;
}

.sn-print-record__recent-scans {
    border: 1px solid var(--border-light);
    border-radius: var(--radius);
    max-height: 140px; /* 限制高度，只显示约5条记录 */
    overflow-y: auto;
    flex: 1; /* 让它占据剩余空间 */
}

.sn-print-record__recent-scans h3 {
    margin: 0;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--primary-text);
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-light);
}

.sn-print-record__recent-list {
    padding: 0.5rem;
}

.sn-print-record__scan-item {
    padding: 0.5rem;
    border-radius: var(--radius);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sn-print-record__scan-item:last-child {
    margin-bottom: 0;
}

.sn-print-record__scan-item.success {
    background-color: #f0f9eb;
}

.sn-print-record__scan-item.error {
    background-color: #fef0f0;
}

.sn-print-record__scan-item i {
    font-size: 1rem;
}

.sn-print-record__scan-item i.success {
    color: var(--success-color);
}

.sn-print-record__scan-item i.error {
    color: var(--danger-color);
}

.sn-print-record__scan-item-sn {
    font-weight: 500;
    flex: 1;
}

.sn-print-record__scan-item-time {
    color: var(--secondary-text);
    font-size: 0.75rem;
}

.sn-print-record__empty-message {
    padding: 1rem;
    text-align: center;
    color: var(--secondary-text);
    font-style: italic;
}

.sn-print-record__verify-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

/* **新增：语音按钮的特殊样式** */
.sn-print-record__verify-actions button[id="toggle-voice-btn"] {
    margin-right: auto; /* 将语音按钮推到左侧 */
}

/* 小屏幕下的响应式调整 */
@media screen and (max-width: 480px) {
    .sn-print-record__verify-actions {
        justify-content: center;
    }
    
    .sn-print-record__verify-actions button[id="toggle-voice-btn"] {
        margin-right: 0;
        order: -1; /* 确保语音按钮在最前面 */
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

.sn-print-record__status {
    color: var(--primary-color);
    font-weight: 500;
    margin-left: auto;
}

/* 表格中状态列的样式 */
.sn-verified {
    color: var(--success-color);
    font-weight: 500;
}

.sn-verified-warning {
    color: var(--warning-color);
}

.sn-pending {
    color: var(--secondary-text);
    font-style: italic;
}

/* 响应式设计调整 */
@media screen and (max-width: 768px) {
    .sn-print-record__twin-layout {
        flex-direction: column;
    }
    
    .sn-print-record__card--list,
    .sn-print-record__card--verify {
        width: 100%;
    }
} 