// 固件管理模块通用分页功能
// 提供Vue组合函数方式的分页实现

/**
 * 创建分页功能的Vue组合函数
 * @param {Object} options 配置选项
 * @param {Array} options.dataList - 原始数据列表（响应式）
 * @param {number} options.defaultPageSize - 默认每页条数
 * @param {Array} options.pageSizeOptions - 每页条数选项
 * @returns {Object} 分页相关的响应式数据和方法
 */
function usePagination(options = {}) {
    const { 
        dataList,
        defaultPageSize = 10,
        pageSizeOptions = [10, 20, 50, 100]
    } = options;

    if (!window.Vue) {
        Logger.error('Vue is required for pagination functionality');
        return {};
    }

    const { ref, computed } = window.Vue;

    // 分页响应式数据
    const currentPage = ref(1);
    const pageSize = ref(defaultPageSize);
    const pageSizes = ref(pageSizeOptions);

    // 计算属性
    const totalCount = computed(() => {
        return dataList.value ? dataList.value.length : 0;
    });

    const totalPages = computed(() => {
        return Math.ceil(totalCount.value / pageSize.value);
    });

    const startPage = computed(() => {
        return Math.max(1, currentPage.value - 2);
    });

    const endPage = computed(() => {
        const start = startPage.value;
        return Math.min(totalPages.value, start + 4);
    });

    const visiblePages = computed(() => {
        const pages = [];
        for (let i = startPage.value; i <= endPage.value; i++) {
            pages.push(i);
        }
        return pages;
    });

    // 当前页显示的数据
    const paginatedData = computed(() => {
        if (!dataList.value) return [];
        const start = (currentPage.value - 1) * pageSize.value;
        const end = start + pageSize.value;
        return dataList.value.slice(start, end);
    });

    // 分页方法
    const goToPage = (page) => {
        if (page >= 1 && page <= totalPages.value) {
            currentPage.value = page;
            Logger.log('页码变更:', page);
        }
    };

    const goToFirstPage = () => {
        goToPage(1);
    };

    const goToPrevPage = () => {
        if (currentPage.value > 1) {
            goToPage(currentPage.value - 1);
        }
    };

    const goToNextPage = () => {
        if (currentPage.value < totalPages.value) {
            goToPage(currentPage.value + 1);
        }
    };

    const goToLastPage = () => {
        goToPage(totalPages.value);
    };

    const handleSizeChange = (size) => {
        pageSize.value = parseInt(size);
        currentPage.value = 1; // 切换每页条数时回到第一页
        Logger.log('每页条数变更:', size);
    };

    // 重置到第一页（搜索等操作时使用）
    const resetToFirstPage = () => {
        currentPage.value = 1;
    };

    return {
        // 响应式数据
        currentPage,
        pageSize,
        pageSizes,
        totalCount,
        totalPages,
        startPage,
        endPage,
        visiblePages,
        paginatedData,

        // 方法
        goToPage,
        goToFirstPage,
        goToPrevPage,
        goToNextPage,
        goToLastPage,
        handleSizeChange,
        resetToFirstPage
    };
}

/**
 * 获取分页组件的HTML模板
 * @returns {string} 分页组件的HTML模板字符串
 */
function getPaginationTemplate() {
    return `
        <div class="table-footer">
            <div class="table-length">
                <div class="page-size-selector">
                    每页
                    <select @change="handleSizeChange($event.target.value)">
                        <option 
                            v-for="size in pageSizes" 
                            :key="size" 
                            :value="size" 
                            :selected="size === pageSize">
                            {{ size }}
                        </option>
                    </select>
                    条
                </div>
                <div class="total-count">
                    共计 <span>{{ totalCount }}</span> 条
                </div>
            </div>
            <div class="table-pagination">
                <button 
                    class="btn btn-icon" 
                    @click="goToFirstPage" 
                    :disabled="currentPage === 1">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button 
                    class="btn btn-icon" 
                    @click="goToPrevPage" 
                    :disabled="currentPage === 1">
                    <i class="fas fa-angle-left"></i>
                </button>
                <div class="pagination-pages">
                    <span v-if="startPage > 1" class="pagination-ellipsis">...</span>
                    <button 
                        v-for="page in visiblePages" 
                        :key="page"
                        class="btn btn-page" 
                        :class="{ active: page === currentPage }"
                        @click="goToPage(page)">
                        {{ page }}
                    </button>
                    <span v-if="endPage < totalPages" class="pagination-ellipsis">...</span>
                </div>
                <button 
                    class="btn btn-icon" 
                    @click="goToNextPage" 
                    :disabled="currentPage === totalPages">
                    <i class="fas fa-angle-right"></i>
                </button>
                <button 
                    class="btn btn-icon" 
                    @click="goToLastPage" 
                    :disabled="currentPage === totalPages">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        </div>
    `;
}

// 导出到全局，供其他文件使用
window.FirmwarePagination = {
    usePagination,
    getPaginationTemplate
};

Logger.log('Firmware Common Paging loaded successfully'); 