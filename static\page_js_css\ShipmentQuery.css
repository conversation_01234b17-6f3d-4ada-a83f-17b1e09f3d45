/* 出库记录查询样式 */
:root {
    /* 字体大小 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    
    /* 颜色 */
    --color-primary: #1976D2;
    --color-primary-light: #2196F3;
    --color-primary-dark: #1565C0;
    --color-text-primary: #1a1f36;
    --color-text-secondary: #606266;
    --color-text-tertiary: #909399;
    --color-border: #dcdfe6;
    --color-background: #f5f7fa;
    --color-white: #ffffff;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    
    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 基础容器样式 */
.shipment-query {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: var(--spacing-xl);
    background-color: var(--color-white);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    color: var(--color-text-primary);
}

/* 标题样式 */
.shipment-query__title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    position: relative;
    letter-spacing: 0.5px;
}

.shipment-query__title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, 
        rgba(26, 31, 54, 0.8) 0%,
        rgba(26, 31, 54, 0.6) 50%,
        rgba(26, 31, 54, 0.2) 100%
    );
}

.shipment-query__subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

/* 查询头部布局 */
.shipment-query__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

/* 查询类型区域 */
.shipment-query__type {
    flex: 1;
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

/* 查询类型选项 */
.shipment-query__type-options {
    display: flex;
    gap: var(--spacing-xl);
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
}

.shipment-query__type-option {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.shipment-query__type-option:hover {
    color: var(--color-primary);
}

.shipment-query__type-radio {
    margin-right: var(--spacing-sm);
    cursor: pointer;
}

/* 查询类型文本字体大小 */  
.shipment-query__type-text {
    white-space: nowrap;
    font-size: var(--font-size-base);
}

/* 高级筛选按钮 */
.shipment-query__filter {
    margin-left: 20px;
    display: flex;
    align-items: center;
}

.shipment-query__filter-btn {
    padding: 8px 16px;
    background-color: var(--color-white);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    height: 32px;
    display: flex;
    align-items: center;
}

.shipment-query__filter-btn:hover {
    background-color: var(--color-background);
    border-color: var(--color-primary-light);
    color: var(--color-primary);
}

.shipment-query__filter-text {
    font-size: 14px;
    margin-right: 8px;
}

.shipment-query__filter-icon {
    font-size: 12px;
    transition: transform 0.3s;
}

.shipment-query__filter-btn.active .shipment-query__filter-icon {
    transform: rotate(180deg);
}

/* 高级筛选展开区域 */
.shipment-query__advanced-filter {
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.shipment-query__date-range {
    display: flex;
    gap: 30px;
    align-items: center;
}

.shipment-query__date-field {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
    background: var(--color-white);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
}
/* 高级筛选日期标签 */
.shipment-query__date-label {
    color: var(--color-text-secondary);
    font-weight: 500;
    white-space: nowrap;
    min-width: 70px;
    font-size: var(--font-size-sm);
}

.shipment-query__date-input {
    flex: 1;
    height: 36px;
    padding: 0 12px;
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    background-color: #fff;
    transition: all 0.3s ease;
}

.shipment-query__date-input:hover {
    border-color: var(--color-primary-light);
}

.shipment-query__date-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* 查询表单样式 */
.shipment-query__form {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-end;
}

/* 表单组样式 */
.shipment-query__form-group {
    flex: 1;
    min-width: 200px;
    position: relative;
}

/* 按钮容器 */
.shipment-query__buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 标签样式 */
.shipment-query__label {
    display: block;
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
    font-weight: 500;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

/* 输入框基础样式 */
.shipment-query__input {
    width: 100%;
    height: 40px;
    padding: 0 var(--spacing-md);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    line-height: 2.5rem;
    color: var(--color-text-primary);
    background-color: #fff;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.shipment-query__input:hover {
    border-color: var(--color-primary-light);
}

.shipment-query__input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.shipment-query__input::placeholder {
    color: var(--color-text-secondary);
}

/* 按钮样式 */
.shipment-query__btn {
    height: 40px;
    padding: 0 var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid transparent;
}

.shipment-query__btn--search {
    background-color: var(--color-primary);
    color: var(--color-white);
}

.shipment-query__btn--search:hover {
    background-color: var(--color-primary-dark);
}

.shipment-query__btn--reset {
    background-color: var(--color-white);
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border);
}

.shipment-query__btn--reset:hover {
    background-color: var(--color-background);
    border-color: var(--color-text-secondary);
}

.shipment-query__btn i {
    margin-right: 5px;
}

/* 结果表格样式 */
.shipment-query__results {
    margin-top: 30px;
}

.shipment-query__table-container {
    position: relative;
    overflow-x: auto !important;
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
}

.shipment-query__table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
    table-layout: fixed;
    min-width: 1500px;
}

.shipment-query__table th {
    background-color: var(--color-background);
    color: var(--color-text-primary);
    font-weight: 500;
    text-align: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    white-space: nowrap;
}

.shipment-query__table td {
    padding: var(--spacing-md) var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
    color: var(--color-text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.shipment-query__table tr:hover {
    background-color: rgba(33, 150, 243, 0.05);
}

.shipment-query__table tr:last-child td {
    border-bottom: none;
}

/* 分页样式 */
.shipment-query__pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: var(--spacing-md) 0;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.shipment-query__pagination-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.shipment-query__page-size {
    width: 70px;
    height: 32px;
    padding: 0 var(--spacing-sm);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    color: var(--color-text-secondary);
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.shipment-query__page-size:hover {
    border-color: var(--color-primary-light);
}

.shipment-query__total {
    margin-left: 8px;
}

.shipment-query__pagination-controls {
    display: flex;
    align-items: center;
    gap: 4px;
}

.shipment-query__pagination-btn {
    min-width: 32px;
    height: 32px;
    padding: 0 4px;
    border: 1px solid var(--color-border);
    background-color: #fff;
    color: var(--color-text-secondary);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: var(--font-size-sm);
}

.shipment-query__pagination-btn:hover:not(.disabled) {
    color: var(--color-primary);
    border-color: var(--color-primary-light);
    background-color: rgba(33, 150, 243, 0.1);
}

.shipment-query__pagination-btn.disabled {
    color: var(--color-text-tertiary);
    cursor: not-allowed;
    background-color: var(--color-background);
}

.shipment-query__pagination-btn.active {
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

/* 无数据提示 */
.shipment-query__no-data {
    text-align: center;
    padding: 40px 0;
    color: var(--color-text-tertiary);
    font-size: var(--font-size-sm);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .shipment-query {
        padding: var(--spacing-md);
    }
    
    .shipment-query__header {
        flex-direction: column;
    }
    
    .shipment-query__filter {
        margin-left: 0;
        margin-top: var(--spacing-md);
        width: 100%;
    }
    
    .shipment-query__filter-btn {
        width: 100%;
        justify-content: center;
    }
    
    .shipment-query__date-range {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .shipment-query__date-field {
        width: 100%;
    }
    
    .shipment-query__form {
        flex-direction: column;
    }
    
    .shipment-query__form-group {
        width: 100%;
    }
    
    .shipment-query__type-options {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }
    
    .shipment-query__type-option {
        margin-right: var(--spacing-md);
    }
    
    .shipment-query__pagination {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .shipment-query__pagination-info {
        width: 100%;
        justify-content: flex-start;
    }
    
    .shipment-query__pagination-controls {
        width: 100%;
        justify-content: center;
    }
}

/* 表格排序样式 */
.shipment-query__table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-right: var(--spacing-xl);
}

.shipment-query__table th.sortable:hover {
    background-color: rgba(64, 158, 255, 0.1);
}

.shipment-query__table th.sortable i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-text-tertiary);
    transition: all 0.3s ease;
}

.shipment-query__table th.sortable:hover i {
    color: var(--color-primary);
}

.shipment-query__table th.sortable i.fa-sort-up,
.shipment-query__table th.sortable i.fa-sort-down {
    color: var(--color-primary);
}

/* 表格加载指示器样式 */
.table-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--color-border);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 添加复选框样式 */
.shipment-query__checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
    border: 1px solid var(--color-border);
    border-radius: 3px;
    transition: all 0.2s ease;
    position: relative;
    vertical-align: middle;
}

.shipment-query__checkbox:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.shipment-query__checkbox:hover {
    border-color: var(--color-primary-light);
}

/* 调整表格第一列宽度 */
.shipment-query__table th:first-child,
.shipment-query__table td:first-child {
    width: 5%;
    min-width: 40px;
    text-align: center;
}

/* 调整序号列宽度 */
.shipment-query__table th:nth-child(2),
.shipment-query__table td:nth-child(2) {
    width: 7%;
    min-width: 50px;
    text-align: center;
}

/* 调整产品SN号列宽度 */
.shipment-query__table th:nth-child(3),
.shipment-query__table td:nth-child(3) {
    width: 15%;
    min-width: 150px;
}

/* 调整箱单号列宽度 */
.shipment-query__table th:nth-child(4),
.shipment-query__table td:nth-child(4) {
    width: 10%;
    min-width: 150px;
}

/* 调整出库单号列宽度 */
.shipment-query__table th:nth-child(5),
.shipment-query__table td:nth-child(5) {
    width: 23%;
    min-width: 140px;
}

/* 调整客户名称列宽度 */
.shipment-query__table th:nth-child(6),
.shipment-query__table td:nth-child(6) {
    width: 28%;
    min-width: 140px;
}

/* 调整出库时间列宽度 */
.shipment-query__table th:nth-child(7),
.shipment-query__table td:nth-child(7) {
    width: 9%;
    min-width: 80px;
    text-align: center;
}

/* 调整操作人列宽度 */
.shipment-query__table th:nth-child(8),
.shipment-query__table td:nth-child(8) {
    width: 10%;
    min-width: 80px;
    text-align: center;
}

/* 修改无数据提示的colspan */
.shipment-query__no-data,
.shipment-query__error {
    text-align: center;
    padding: 40px 0;
    color: var(--color-text-tertiary);
    font-size: var(--font-size-sm);
}

/* 导出按钮样式 */
.shipment-query__btn--export {
    background-color: #67c23a;
    color: var(--color-white);
}

.shipment-query__btn--export:hover {
    background-color: #5daf34;
}

/* 移除旧的导出容器 */
.shipment-query__export-container {
    display: none;
}
