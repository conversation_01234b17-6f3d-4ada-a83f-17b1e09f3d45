// 固件管理API客户端
class FirmwareAPIClient {
    constructor() {
        this.baseURL = '/api/firmware';
        this.cache = {
            allFirmware: null,
            downloadRecords: null,
            lastUpdate: null
        };
        this.eventBus = new EventTarget();
    }

    // HTTP请求工具方法
    async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        };

        const mergedOptions = { ...defaultOptions, ...options };
        
        try {
            Logger.log(`[FirmwareAPI] 发起请求: ${mergedOptions.method} ${url}`);
            const response = await fetch(url, mergedOptions);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            Logger.log(`[FirmwareAPI] 请求成功: ${url}`, data);
            return data;
        } catch (error) {
            Logger.error(`[FirmwareAPI] 请求失败: ${url}`, error);
            throw error;
        }
    }

    // GET请求
    async get(endpoint, params = {}) {
        const url = new URL(this.baseURL + endpoint, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                url.searchParams.append(key, params[key]);
            }
        });
        return await this.request(url.toString());
    }

    // POST请求
    async post(endpoint, data = {}) {
        return await this.request(this.baseURL + endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT请求
    async put(endpoint, data = {}) {
        return await this.request(this.baseURL + endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE请求
    async delete(endpoint) {
        return await this.request(this.baseURL + endpoint, {
            method: 'DELETE'
        });
    }

    // 文件上传请求
    async uploadFile(endpoint, formData) {
        return await this.request(this.baseURL + endpoint, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
    }
}

// 全局固件数据管理器（API版本）
class FirmwareDataManager {
    constructor() {
        this.api = new FirmwareAPIClient();
        this.eventBus = new EventTarget();
        this.cache = {
            allFirmware: [],
            downloadRecords: [],
            lastUpdate: null
        };
    }

    // ===== 获取数据方法 =====

    // 获取所有固件列表
    async getAllFirmware(forceRefresh = false) {
        if (!forceRefresh && this.cache.allFirmware.length > 0) {
            return [...this.cache.allFirmware];
        }

        try {
            // 修复：使用大per_page参数获取所有数据，避免分页限制
            const response = await this.api.get('/all/list', {
                per_page: 2000,  // 使用足够大的值获取所有记录
                page: 1
            });
            if (response.success) {
                this.cache.allFirmware = response.data.items || [];
                this.cache.lastUpdate = new Date();
                Logger.log(`[FirmwareDataManager] 获取到 ${this.cache.allFirmware.length} 条固件记录，总计 ${response.data.total} 条`);
                return [...this.cache.allFirmware];
            } else {
                throw new Error(response.message || '获取固件列表失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 获取所有固件失败:', error);
            throw error;
        }
    }

    // 获取活跃固件（已生效和审核退回）
    async getActiveFirmware(forceRefresh = false) {
        const allFirmware = await this.getAllFirmware(forceRefresh);
        return allFirmware.filter(f => f.status === 'active' || f.status === 'rejected');
    }

    // 获取待审核固件
    async getPendingFirmware() {
        try {
            // 修复：使用大per_page参数获取所有数据，避免分页限制
            const response = await this.api.get('/pending/list', {
                per_page: 2000,  // 使用足够大的值获取所有记录
                page: 1
            });
            if (response.success) {
                const items = response.data.items || [];
                Logger.log(`[FirmwareDataManager] 获取到 ${items.length} 条待审核固件记录，总计 ${response.data.total} 条`);
                return items;
            } else {
                throw new Error(response.message || '获取待审核固件失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 获取待审核固件失败:', error);
            throw error;
        }
    }

    // 获取作废固件
    async getObsoleteFirmware() {
        try {
            // 修复：使用大per_page参数获取所有数据，避免分页限制
            const response = await this.api.get('/obsolete/list', {
                per_page: 2000,  // 使用足够大的值获取所有记录
                page: 1
            });
            if (response.success) {
                const items = response.data.items || [];
                Logger.log(`[FirmwareDataManager] 获取到 ${items.length} 条作废固件记录，总计 ${response.data.total} 条`);
                // 映射字段名称以匹配前端期望的格式
                return items.map(item => ({
                    ...item,
                    effectiveDays: item.activeDays || 0,  // 后端返回activeDays，前端期望effectiveDays
                    obsoleteBy: item.obsoleteOperator || '未知',  // 后端返回obsoleteOperator，前端期望obsoleteBy
                    usageCount: item.usageCount || 0,
                    downloadCount: item.downloadCount || 0
                }));
            } else {
                throw new Error(response.message || '获取作废固件失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 获取作废固件失败:', error);
            throw error;
        }
    }

    // 获取下载记录
    async getDownloadRecords(params = {}) {
        try {
            // 修复：合并分页参数，确保获取所有数据
            const mergedParams = {
                per_page:5000,  // 使用足够大的值获取所有记录
                page: 1,
                ...params  // 保留用户传入的其他参数（如时间范围等）
            };
            const response = await this.api.get('/usage/list', mergedParams);
            if (response.success) {
                // 支持不同的响应格式，优先使用 items，然后是 data
                const records = response.data.items || response.data || [];
                Logger.log(`[FirmwareDataManager] 获取到 ${records.length} 条使用记录，总计 ${response.data.total} 条`);
                this.cache.downloadRecords = records;
                return [...this.cache.downloadRecords];
            } else {
                throw new Error(response.message || '获取使用记录失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 获取下载记录失败:', error);
            throw error;
        }
    }

    // 获取固件详情
    async getFirmwareDetail(serialNumber) {
        try {
            const response = await this.api.get(`/all/${serialNumber}`);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || '获取固件详情失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 获取固件详情失败:', error);
            throw error;
        }
    }

    // 获取版本历史
    async getVersionHistory(serialNumber) {
        try {
            const response = await this.api.get(`/all/${serialNumber}/history`);
            if (response.success) {
                return response.data.history || [];
            } else {
                throw new Error(response.message || '获取版本历史失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 获取版本历史失败:', error);
            throw error;
        }
    }

    // ===== 操作方法 =====

    // 添加新固件
    async addFirmware(firmwareData) {
        try {
            const formData = new FormData();
            
            // 添加基本字段
            Object.keys(firmwareData).forEach(key => {
                if (key !== 'file' && firmwareData[key] !== undefined) {
                    formData.append(key, firmwareData[key]);
                }
            });
            
            // 添加文件
            if (firmwareData.file) {
                formData.append('file', firmwareData.file);
            }

            const response = await this.api.uploadFile('/all/upload', formData);
            if (response.success) {
                this.invalidateCache();
                this.emit('firmware-added', response.data);
                return response.data;
            } else {
                throw new Error(response.message || '上传固件失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 添加固件失败:', error);
            throw error;
        }
    }

    // 更新固件
    async updateFirmware(serialNumber, updates) {
        try {
            const formData = new FormData();
            
            Object.keys(updates).forEach(key => {
                if (key !== 'file' && updates[key] !== undefined) {
                    formData.append(key, updates[key]);
                }
            });
            
            if (updates.file) {
                formData.append('file', updates.file);
            }

            const response = await this.api.uploadFile(`/all/${serialNumber}/update`, formData);
            if (response.success) {
                this.invalidateCache();
                this.emit('firmware-updated', response.data);
                return response.data;
            } else {
                throw new Error(response.message || '更新固件失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 更新固件失败:', error);
            throw error;
        }
    }

    // 审核通过
    async approveFirmware(serialNumber, approver) {
        try {
            const response = await this.api.post(`/pending/${serialNumber}/approve`, {
                approver: approver
            });
            if (response.success) {
                this.invalidateCache();
                this.emit('firmware-approved', response.data);
                return response.data;
            } else {
                throw new Error(response.message || '审核通过失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 审核通过失败:', error);
            throw error;
        }
    }

    // 审核拒绝
    async rejectFirmware(serialNumber, reason, approver) {
        try {
            const response = await this.api.post(`/pending/${serialNumber}/reject`, {
                reason: reason,
                approver: approver
            });
            if (response.success) {
                this.invalidateCache();
                this.emit('firmware-rejected', response.data);
                return response.data;
            } else {
                throw new Error(response.message || '审核拒绝失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 审核拒绝失败:', error);
            throw error;
        }
    }

    // 创建版本更新
    async createVersionUpdate(serialNumber, newVersionData) {
        try {
            const formData = new FormData();
            
            Object.keys(newVersionData).forEach(key => {
                if (key !== 'file' && newVersionData[key] !== undefined) {
                    formData.append(key, newVersionData[key]);
                }
            });
            
            if (newVersionData.file) {
                formData.append('file', newVersionData.file);
            }

            const response = await this.api.uploadFile(`/all/${serialNumber}/version-update`, formData);
            if (response.success) {
                this.invalidateCache();
                this.emit('version-updated', response.data);
                // 触发作废事件，因为父版本在创建新版本时被立即作废
                this.emit('firmware-obsoleted', { parentSerialNumber: serialNumber, reason: '因版本升级而立即作废' });
                return response.data;
            } else {
                throw new Error(response.message || '版本更新失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 版本更新失败:', error);
            throw error;
        }
    }

    // 下载固件并记录
    async addDownloadRecord(recordData) {
        try {
            const response = await this.api.post(`/all/${recordData.serialNumber}/download`, recordData);
            if (response.success) {
                this.invalidateCache();
                this.emit('firmware-downloaded', response.data);
                this.emit('download-record-added', response.data);
                
                // 自动下载文件
                if (response.data.downloadUrl) {
                    const link = document.createElement('a');
                    link.href = response.data.downloadUrl;
                    link.download = response.data.fileName || 'firmware.bin';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
                
                return response.data;
            } else {
                throw new Error(response.message || '下载固件失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 下载固件失败:', error);
            throw error;
        }
    }

    // 添加使用记录（新方法 - 不下载文件）
    async addUsageRecord(recordData) {
        try {
            const response = await this.api.post(`/all/${recordData.serialNumber}/download`, recordData);
            if (response.success) {
                this.invalidateCache();
                this.emit('usage-record-added', response.data);
                this.emit('firmware-used', response.data);
                return response.data;
            } else {
                throw new Error(response.message || '保存使用记录失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 保存使用记录失败:', error);
            throw error;
        }
    }

    // 删除固件（软删除 + 物理文件清理）
    async deleteFirmware(serialNumber) {
        try {
            const response = await this.api.delete(`/all/${serialNumber}/delete`);
            if (response.success) {
                this.invalidateCache();
                this.emit('firmware-deleted', response.data);
                return response.data;
            } else {
                throw new Error(response.message || '删除固件失败');
            }
        } catch (error) {
            Logger.error('[FirmwareDataManager] 删除固件失败:', error);
            throw error;
        }
    }

    // ===== 缓存管理 =====
    invalidateCache() {
        this.cache.allFirmware = [];
        this.cache.downloadRecords = [];
        this.cache.lastUpdate = null;
        
        // 发射全局数据刷新事件，通知所有页面
        this.emit('global-data-refresh', { timestamp: Date.now() });
        
        // 同时发射到window对象，确保跨组件通信
        if (window.dispatchEvent) {
            window.dispatchEvent(new CustomEvent('firmware-data-changed', { 
                detail: { timestamp: Date.now() } 
            }));
        }
    }

    // ===== 事件系统 =====
    emit(eventName, data) {
        Logger.log(`[FirmwareDataManager] 发射事件: ${eventName}`, data);
        this.eventBus.dispatchEvent(new CustomEvent(eventName, { detail: data }));
    }

    on(eventName, callback) {
        this.eventBus.addEventListener(eventName, (e) => callback(e.detail));
    }

    off(eventName, callback) {
        this.eventBus.removeEventListener(eventName, callback);
    }

    // ===== 兼容性方法（同步接口） =====
    // 为了兼容现有的Vue组件，提供一些同步方法（返回缓存数据）
    getAllFirmwareSync() {
        return [...this.cache.allFirmware];
    }

    getActiveFirmwareSync() {
        return this.cache.allFirmware.filter(f => f.status === 'active' || f.status === 'rejected');
    }

    getDownloadRecordsSync() {
        return [...this.cache.downloadRecords];
    }
}

// 创建全局实例
window.FirmwareDataManager = window.FirmwareDataManager || new FirmwareDataManager();

Logger.log('[FirmwareDataManager] API版本数据管理器已初始化'); 