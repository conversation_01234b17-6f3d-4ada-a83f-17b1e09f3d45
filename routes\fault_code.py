from flask import Blueprint, jsonify, request

fault_code_bp = Blueprint('fault_code', __name__)

@fault_code_bp.route('/search', methods=['GET'])
def search_fault_code():
    code = request.args.get('code')
    # 处理故障码查询逻辑
    return jsonify({
        'status': 'success',
        'data': {
            'code': code,
            'description': '系统通信故障',
            'solution': '检查网络连接'
        }
    })
