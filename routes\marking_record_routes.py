import logging
from flask import Blueprint, jsonify, request, send_file
from sqlalchemy import asc, desc, and_, or_
from datetime import datetime
import pandas as pd
import io
import json

from database.db_manager import DatabaseManager
from models.laser_standard import LaserStandard

# 设置日志记录器
logger = logging.getLogger(__name__)

marking_record_bp = Blueprint('marking_record_api', __name__, url_prefix='/api/marking-records')

# 排序字段映射：前端字段名 -> SQLAlchemy模型列
# 这些键名对应前端 'MarkingRecord.js' 中 sortBy 函数的参数以及请求中的 'sort_field'
SORT_FIELD_MAP = {
    'work_order_no': LaserStandard.laser_order_no,
    'product_code': LaserStandard.laser_product_code,
    'product_model': LaserStandard.laser_product_model,
    'serial_number': LaserStandard.laser_sn,
    'sn_status': LaserStandard.laser_sn_status,
    'production_quantity': LaserStandard.laser_quantity, # 注意模型字段为 laser_quantity
    'operator': LaserStandard.laser_operator,
    'operation_time': LaserStandard.laser_operation_time,
}

# 默认排序字段和顺序 (如果前端未提供或提供无效值)
DEFAULT_SORT_COLUMN = LaserStandard.laser_operation_time
DEFAULT_SORT_ORDER_STR = 'desc'

@marking_record_bp.route('/search', methods=['GET'])
def search_marking_records():
    """
    查询打标记录。
    支持按工单号、SN号、日期范围查询，并支持排序和分页。
    SN状态定义：laser_sn_status=1 表示 "正常", laser_sn_status=2 表示 "作废"。
    """
    try:
        # 获取并校验查询参数
        query_type = request.args.get('type', 'orderNo')  # 'orderNo' (工单号) 或 'serialNumber' (SN号)
        query_value = request.args.get('value', '').strip() # 查询的文本值
        
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('pageSize', 10, type=int)
        
        # 前端发送的排序字段名，默认为 'operation_time' (对应 LaserStandard.laser_operation_time)
        sort_field_key = request.args.get('sort_field', 'operation_time') 
        sort_order_str = request.args.get('sort_order', DEFAULT_SORT_ORDER_STR).lower() # 'asc' 或 'desc'
        
        start_date_str = request.args.get('startDate') # 格式: YYYY-MM-DD
        end_date_str = request.args.get('endDate')     # 格式: YYYY-MM-DD
        query_voided = request.args.get('query_voided', 'false').lower() == 'true' # 新增：查询作废记录参数

        # 基本参数校验和修正
        if page <= 0:
            logger.warning(f"接收到无效的页码: {page}，将使用默认值 1。")
            page = 1
        if page_size <= 0: # 限制最小页面大小，例如为1，或保持默认10
            logger.warning(f"接收到无效的页面大小: {page_size}，将使用默认值 10。")
            page_size = 10 # 或者可以设定一个允许的最小/最大值
        
        if sort_order_str not in ['asc', 'desc']:
            logger.warning(f"接收到无效的排序顺序: '{sort_order_str}'，将使用默认值 '{DEFAULT_SORT_ORDER_STR}'。")
            sort_order_str = DEFAULT_SORT_ORDER_STR

        db = DatabaseManager()
        with db.get_session() as session:
            query = session.query(LaserStandard)
            
            active_filters = [] # 用于存储所有有效的筛选条件

            # 文本查询条件 (工单号或SN号)
            if query_value: # 仅当 query_value 不为空时应用此筛选
                if query_type == 'orderNo':
                    active_filters.append(LaserStandard.laser_order_no.ilike(f"%{query_value}%"))
                elif query_type == 'serialNumber':
                    active_filters.append(LaserStandard.laser_sn.ilike(f"%{query_value}%"))
                else:
                    # 如果 query_type 无效，但 query_value 存在，记录警告并忽略此条件
                    logger.warning(f"未知的查询类型 '{query_type}'，但查询值 '{query_value}' 存在。该文本查询条件将被忽略。")
            
            # 日期范围筛选条件
            if start_date_str:
                try:
                    start_datetime = datetime.strptime(start_date_str, '%Y-%m-%d')
                    active_filters.append(LaserStandard.laser_operation_time >= start_datetime)
                except ValueError:
                    logger.warning(f"无效的开始日期格式: '{start_date_str}'。该日期条件将被忽略。")
            
            if end_date_str:
                try:
                    # 结束日期应包含当天所有时间，所以设置为当天的 23:59:59.999999
                    end_datetime = datetime.strptime(end_date_str, '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=999999)
                    active_filters.append(LaserStandard.laser_operation_time <= end_datetime)
                except ValueError:
                    logger.warning(f"无效的结束日期格式: '{end_date_str}'。该日期条件将被忽略。")
            
            # 应用所有有效的筛选条件
            if active_filters:
                query = query.filter(and_(*active_filters))
            
            # 新增：如果要求只查询作废记录，则添加此特定条件
            if query_voided:
                query = query.filter(LaserStandard.laser_sn_status == 2)

            # 获取应用筛选条件后的总记录数 (用于分页)
            total_count = query.count()

            # 排序逻辑
            sort_column = SORT_FIELD_MAP.get(sort_field_key)
            if not sort_column: # 如果前端提供的 sort_field_key 无效或不在映射中
                logger.warning(f"提供的排序字段 '{sort_field_key}' 无效或未在映射中找到，将使用默认排序字段。")
                sort_column = DEFAULT_SORT_COLUMN # 使用默认排序列
            
            if sort_order_str == 'desc':
                query = query.order_by(desc(sort_column))
            else: # 'asc'
                query = query.order_by(asc(sort_column))

            # 分页获取当页数据
            records_on_page = query.offset((page - 1) * page_size).limit(page_size).all()

            # 格式化结果以匹配前端期望的字段名
            formatted_records = []
            for record in records_on_page:
                formatted_records.append({
                    'work_order_no': record.laser_order_no,
                    'product_code': record.laser_product_code,
                    'product_model': record.laser_product_model,
                    'serial_number': record.laser_sn,
                    'sn_status': record.laser_sn_status, # 返回原始状态码 (1或2), 前端JS负责转换
                    'production_quantity': record.laser_quantity,
                    'operator': record.laser_operator,
                    'operation_time': record.laser_operation_time.strftime('%Y-%m-%d %H:%M:%S') if record.laser_operation_time else None
                })
            
            logger.info(f"打标记录查询成功：类型='{query_type}', 值='{query_value}', 日期范围=('{start_date_str}'至'{end_date_str}'), 页码={page}, 页面大小={page_size}, 仅查作废={query_voided}。本次返回 {len(formatted_records)} 条记录, 符合条件的总记录数为 {total_count} 条。")
            
            return jsonify({
                'success': True,
                'records': formatted_records,
                'totalCount': total_count
            })

    except Exception as e:
        logger.error(f"查询打标记录时发生严重错误: {e}", exc_info=True) # exc_info=True 会记录完整的堆栈跟踪信息
        return jsonify({'success': False, 'message': f'查询打标记录时发生服务器内部错误，请联系管理员。错误详情: {str(e)}'}), 500

@marking_record_bp.route('/void', methods=['PUT'])
def void_marking_record():
    """
    作废指定的SN打标记录。
    作废操作会将原记录的SN号前加上 "zf" 并将状态更新为 2 (作废)。
    这实际上是通过删除原记录并创建一条新的作废记录来完成的，
    以允许原始SN号可以被重新使用。
    """
    try:
        data = request.get_json()
        if not data:
            logger.warning("作废SN请求未提供JSON数据。")
            return jsonify({'success': False, 'message': '请求数据不能为空'}), 400

        work_order_no = data.get('work_order_no')
        original_sn = data.get('serial_number')

        if not work_order_no or not original_sn:
            logger.warning(f"作废SN请求缺少必要参数：工单号='{work_order_no}', SN='{original_sn}'")
            return jsonify({'success': False, 'message': '缺少参数：工单号 (work_order_no) 和 SN号 (serial_number) 不能为空'}), 400

        logger.info(f"收到作废SN请求：工单号='{work_order_no}', SN='{original_sn}'")

        db = DatabaseManager()
        with db.get_session() as session:
            # 1. 查询原始记录
            original_record = session.query(LaserStandard).filter_by(
                laser_order_no=work_order_no,
                laser_sn=original_sn
            ).first()

            if not original_record:
                logger.warning(f"未找到要作废的记录：工单号='{work_order_no}', SN='{original_sn}'")
                return jsonify({'success': False, 'message': '未找到指定的打标记录'}), 404

            # 2. 验证记录状态
            if original_record.laser_sn_status == 2:
                logger.info(f"记录已处于作废状态：工单号='{work_order_no}', SN='{original_sn}'")
                return jsonify({'success': False, 'message': '该SN记录已作废，无需重复操作'}), 400
            
            if original_record.laser_sn_status != 1:
                logger.warning(f"记录状态异常，无法作废：工单号='{work_order_no}', SN='{original_sn}', 当前状态='{original_record.laser_sn_status}'")
                return jsonify({'success': False, 'message': f'记录状态异常(非正常状态)，无法作废。当前状态: {original_record.laser_sn_status}'}), 400

            # 3. 执行作废操作
            #    生成唯一的作废SN号
            base_void_sn_prefix = "zf"
            voided_sn = f"{base_void_sn_prefix}-{original_sn}"
            counter = 0

            # 循环查找不重复的作废SN号
            # 首先检查不带数字后缀的，例如 zf-SN123
            # 如果存在，则尝试 zf1-SN123, zf2-SN123 ...
            check_sn_to_use = voided_sn # 初始检查不带数字序号的
            if counter == 0: # 第一次尝试，不带数字
                check_sn_to_use = f"{base_void_sn_prefix}-{original_sn}"
            else:
                check_sn_to_use = f"{base_void_sn_prefix}{counter}-{original_sn}"

            existing_voided_check = session.query(LaserStandard).filter_by(
                laser_order_no=work_order_no,
                laser_sn=check_sn_to_use
            ).first()

            while existing_voided_check:
                counter += 1
                check_sn_to_use = f"{base_void_sn_prefix}{counter}-{original_sn}"
                existing_voided_check = session.query(LaserStandard).filter_by(
                    laser_order_no=work_order_no,
                    laser_sn=check_sn_to_use
                ).first()
            
            final_voided_sn = check_sn_to_use
            logger.info(f"为工单 '{work_order_no}' 下的原始SN '{original_sn}' 生成的最终作废SN为: '{final_voided_sn}'")

            #   b. 创建新的作废记录对象
            new_voided_record = LaserStandard(
                laser_order_no=original_record.laser_order_no,
                laser_sn=final_voided_sn, # 使用最终确定的唯一作废SN
                laser_product_model=original_record.laser_product_model,
                laser_sn_status=2,  # 更新状态为作废
                laser_product_code=original_record.laser_product_code,
                laser_quantity=original_record.laser_quantity,
                laser_operator=original_record.laser_operator,
                laser_operation_time=original_record.laser_operation_time # 保留原始操作时间
            )
            
            #   c. 删除原记录
            session.delete(original_record)
            
            #   d. 添加新的作废记录
            session.add(new_voided_record)
            
            #   e. 提交事务
            session.commit()

            logger.info(f"SN记录成功作废：原工单号='{work_order_no}', 原SN='{original_sn}', 新SN='{final_voided_sn}'")
            return jsonify({'success': True, 'message': f'SN号 {original_sn} 已成功作废。'})

    except Exception as e:
        session.rollback() # 发生异常时回滚
        logger.error(f"作废SN记录时发生严重错误: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'作废SN记录时发生服务器内部错误。错误详情: {str(e)}'}), 500 

@marking_record_bp.route('/export', methods=['GET'])
def export_marking_records():
    """
    导出打标记录为Excel文件。
    支持导出所有查询结果或选定的记录。
    """
    try:
        # 获取查询参数
        query_type = request.args.get('type', 'orderNo')
        query_value = request.args.get('value', '').strip()
        start_date_str = request.args.get('startDate')
        end_date_str = request.args.get('endDate')
        query_voided = request.args.get('query_voided', 'false').lower() == 'true'
        sort_field_key = request.args.get('sort_field', 'operation_time')
        sort_order_str = request.args.get('sort_order', DEFAULT_SORT_ORDER_STR).lower()
        
        # 获取选中的记录ID列表（如果有）
        selected_records_str = request.args.get('selected_records')
        selected_records = json.loads(selected_records_str) if selected_records_str else None

        db = DatabaseManager()
        with db.get_session() as session:
            query = session.query(LaserStandard)
            active_filters = []

            # 如果有选中的记录，优先处理选中的记录
            if selected_records:
                # 构建工单号和SN号的组合条件
                selected_conditions = []
                for record_id in selected_records:
                    # 假设record_id的格式为 "工单号_SN号"
                    try:
                        order_no, sn = record_id.split('_', 1)
                        selected_conditions.append(
                            and_(
                                LaserStandard.laser_order_no == order_no,
                                LaserStandard.laser_sn == sn
                            )
                        )
                    except ValueError:
                        logger.warning(f"无效的记录ID格式: {record_id}")
                        continue
                
                if selected_conditions:
                    active_filters.append(or_(*selected_conditions))
            else:
                # 如果没有选中的记录，使用查询条件
                if query_value:
                    if query_type == 'orderNo':
                        active_filters.append(LaserStandard.laser_order_no.ilike(f"%{query_value}%"))
                    elif query_type == 'serialNumber':
                        active_filters.append(LaserStandard.laser_sn.ilike(f"%{query_value}%"))

                # 处理日期范围
                if start_date_str:
                    try:
                        start_datetime = datetime.strptime(start_date_str, '%Y-%m-%d')
                        active_filters.append(LaserStandard.laser_operation_time >= start_datetime)
                    except ValueError:
                        logger.warning(f"导出时收到无效的开始日期格式: '{start_date_str}'")

                if end_date_str:
                    try:
                        end_datetime = datetime.strptime(end_date_str, '%Y-%m-%d').replace(
                            hour=23, minute=59, second=59, microsecond=999999
                        )
                        active_filters.append(LaserStandard.laser_operation_time <= end_datetime)
                    except ValueError:
                        logger.warning(f"导出时收到无效的结束日期格式: '{end_date_str}'")

                if query_voided:
                    active_filters.append(LaserStandard.laser_sn_status == 2)

            # 应用筛选条件
            if active_filters:
                query = query.filter(and_(*active_filters))

            # 应用排序
            sort_column = SORT_FIELD_MAP.get(sort_field_key, DEFAULT_SORT_COLUMN)
            query = query.order_by(desc(sort_column) if sort_order_str == 'desc' else asc(sort_column))

            # 执行查询
            records = query.all()

            # 转换为DataFrame
            data = []
            for record in records:
                data.append({
                    '工单号': record.laser_order_no,
                    '产品编码': record.laser_product_code,
                    '产品型号': record.laser_product_model,
                    'SN号': record.laser_sn,
                    'SN状态': '作废' if record.laser_sn_status == 2 else '正常',
                    '生产数量': record.laser_quantity,
                    '操作人员': record.laser_operator,
                    '操作时间': record.laser_operation_time.strftime('%Y-%m-%d %H:%M:%S') if record.laser_operation_time else None
                })

            if not data:
                return jsonify({'success': False, 'message': '没有找到符合条件的记录可供导出'}), 404

            df = pd.DataFrame(data)

            # 创建Excel文件
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='xlsxwriter', mode='xlsx') as writer:
                df.to_excel(writer, sheet_name='打标记录', index=False)
                
                # 获取工作表对象
                worksheet = writer.sheets['打标记录']
                
                # 设置列宽
                for idx, col in enumerate(df.columns):
                    max_length = max(
                        df[col].astype(str).apply(len).max(),
                        len(str(col))
                    )
                    worksheet.set_column(idx, idx, max_length + 2)

            output.seek(0)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d%H%M')
            filename = f'打标记录_{timestamp}.xlsx'
            
            logger.info(f"成功导出打标记录：文件名='{filename}', 记录数={len(data)}")
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )

    except Exception as e:
        logger.error(f"导出打标记录时发生错误: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'导出失败，服务器内部错误: {str(e)}'
        }), 500 