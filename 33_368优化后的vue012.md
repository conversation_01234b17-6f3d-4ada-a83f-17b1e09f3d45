1、管理自动部署脚本environment
2、管理自动备份脚本及日常运维Operations
3、优化产品测试查询页面
最终极版，完全可用




2025-01-03 10:30
1、优化了BarcodeBinding.js的逐一验证，取消批量验证，如有问题可回退到上一版本的BarcodeBinding.js

2、优化了OrderManagement.js的表格样式，确保表格内容不会被截断

3、优化了Coupler.js、CPUController.js、IOModule.js的PCBA检查逻辑，确保在不需要PCBA检查时，可以继续测试

4、优化了BarcodeBinding.js的验证逻辑，确保在验证外壳SN时，同时验证订货号

5、优化了BarcodeBinding.js的验证逻辑，确保在验证外壳SN时，同时验证订货号

6、优化了BarcodeBinding.js的验证逻辑，确保在验证外壳SN时，同时验证订货号

2025-03-21:
1、优化工单管理，调整io模块、耦合器、CPU控制器、维修返工测试的工单管理删除对比时间、对比结果、对比人员字段
2、让工单管理的状态显示的更正确
3、工单管理增加手动完成标志
4、手动完成会显示不一样的图标
5、增加各页面基本信息使用trim()函数，确保字段值不会出现空格



20250423上传服务器

augement di二班可用

可上传附件
175可上传附件到当前的模态框
176更改模态框样式更现代化

178实现了想要的预览功能

180_修改添加SN号，每个工单下的SN号都需要检查
181 有详细提示能够提交


修复了很多角色变量问题，可用版本.

三大阶段都可以正常提交了
在提交检验的时候可以获取到当前的用户，不用手动输入。

再次优化命名

修复附件上传，最终版



20250508最新：
重新优化质检过程表创建.sql的表结构以便于增加返工工单的处理

返工工单功能实现修改总结
我已经完成了返工工单功能的实现，以下是我所做的主要修改：

前端修改
SelfInspection.js:
添加了"返工工单"复选框到工单创建表单中
修改了createWorkOrder函数，根据是否为返工工单调用不同的API
添加了showReworkWorkOrderBadge函数，用于显示返工工单标识
更新了processWorkOrderData函数，使其能够正确处理返工工单信息
ScanSN.js:
修改了processScanedSN函数，增加了对返工产品的处理逻辑
增强了confirmAddReworkProduct函数，显示更多信息并使用专门的API
更新了addProductToWorkOrder函数，支持返工产品的添加
SelfInspection.css:
添加了返工标识的样式
后端修改
quality_inspection.py:
添加了/rework-work-order端点，用于创建返工工单
添加了/work-order/<int:work_order_id>/rework-product端点，用于添加返工产品
添加了/work-order/<int:work_order_id>/info端点，用于获取工单基本信息
modelquality_inspection.py:
更新了Product模型，添加了original_product_id和original_work_order_id字段
表创建.sql:
更新了SQL模式，添加了新字段和索引

20250509:
修复表结构，采用最新combination.md的方案修改
我已经完成了对 SelfInspection_addsn01.md文档的更新，根据最新的功能结构和数据库设计进行了全面修改。以下是主要的更新内容：

主要更新内容
数据库结构更新：
移除了原来的inspection_records、inspection_submissions和stage_completions表
添加了新的product_inspection_status表，用于记录产品在各阶段各角色的检验状态
更新了表结构描述和字段说明
数据库关系图更新：
简化了数据库关系图，反映新的表结构和关系
更新了关系说明，移除了不再存在的表的关系描述
数据库模型更新：
更新了SQLAlchemy模型定义，使用新的ProductInspectionStatus模型
更新了模型之间的关系定义
添加了新的约束和注释
API实现更新：
更新了提交质检记录的API实现，使用新的数据结构
修改了API逻辑，适应新的数据模型
总结部分更新：
添加了数据库优化的说明
更新了质检记录关联的描述，反映新的数据结构
强调了数据模型简化带来的性能和维护性提升
这些更新全面反映了系统的最新功能结构和数据库设计，特别是数据库优化带来的变化。文档现在准确描述了系统如何使用新的product_inspection_status表来记录产品检验状态，而不是之前的多表结构。同时，文档保持了对SN号管理和返工功能的详细描述，这些是系统的核心功能。

文档的结构和风格保持一致，确保了易读性和连贯性。这个更新后的文档将为开发团队和未来的维护人员提供清晰的系统概览和实现细节。


20250509晚上：
完美就解决了多人协同无法提交的问题

20250510：
完美解决多人协同问题，重构优化了quality_inspection.py代码，名为优化01.py

20250512：
更新组件样式：返工工单改为下拉框、刷新按钮添加背景色
更改录入完SN后提交检验结果的SweetAlert2弹窗按钮样式美化.
修改产品状态显示样式，增加工单数量

输入工单号之后获取产品数量、判断SN长度等

20250513：
输入工单自动获取工单状态、是否返工，并自动选择
修复复选框全局变量污染问题
移除每次添加产品SN号需要确认的弹窗
修复福建上传不显示操作人员问题

20250514：
增加打印记录后端、增加查询信息的UI

增加打标查询记录
添加打标记录查询作废功能、导出按钮

20250515：
完成打标记录导出功能；
优化SN打印记录首件SN号生成规则，自动根据周期和编码获取最新一个SN号

修复quality_inspection.py，所有预定义的阶段（assembly, test, packaging）和所有预定义的角色（first, self, ipqc）的每一个组合（共 3x3 = 9 个组合），都至少有一条 ProductInspectionStatus 记录的 submission_type 是 'final'，最后的状态变更为completed




20250516：
修复附件不可以协同查看的问题
因为当前工单要变成“completed”的条件是这9个的每一条的SN都必须完成检测，如果某一条有一个SN没有检测就一直是processing

20250518:
可查看质检测试
增加导出功能
添加图片导出功能(根据当前的实际内容进行渲染、比如只打开测试前阶段就只渲染这个基=阶段的图片内容)

20250519:
修改ScanSN.js 209 返工工单可以随意添加SN号
215修改了quality_inspection.py:
//“工单状态变为 completed 的条件是：该工单下至少一个SN号，在每一个阶段（组装前、测试前、包装前），对于每一个角色（首检、自检、IPQC），都必须有一条对应的 ProductInspectionStatus 记录，并且这条记录的 submission_type 必须是 'final'”。那么最终状态就是“completed”，并不需要所有SN。//

216:修改了quality_inspection_work_orders表结构添加了三个新字段：assembly_stage_completed, test_stage_completed, packaging_stage_completed

217：修改了assembly_stage_completed, test_stage_completed, packaging_stage_completed 为1，则status为completed

setupProductStatusRefresh:每间隔10秒自动刷新"阶段SN号的检验状态";
setupAutoRefresh:没间隔60秒自动刷新"工单检验状态"

218：优化质检查询页面表格、优化SN打印记录查询页面表格
219:修改了模块的名称


20250520：
增加测试前阶段检测该工单有没有完成外观检验、相比于。

特殊：33-221有两种方法。第一种是测试前阶段的修改了io_module.py添加后端API，再修改三个测试页面 js； 但是组装前、包装前是直接在work_order.py里面添加quality_order = session.query(QualityInspectionWorkOrder).filter_by获取quality_inspection_work_orders 里面的三个阶段的状态是否为1、最小修改扫码绑定、条码比对页面的js.

33_222：统一在work_order.py添加后端获取三个阶段的完整状态，修改各自页面的js实现质检阶段判定和对应组装、测试、包装的联动

33_233：
    修改"出货条码录入、允许同一个SN号多次录入，修改了ShipmentBarcode.js、shipment.py、shipment_barcode.py； gemini2.5pro修复了页面刷新报取空值的错误。
    

33_234：
    修改产品管理页面的字段排序，实现双击“产品编码”跳转到“产品编辑”页面；双击”加工单号“跳转到”工单编辑“页面。成品测试查询的实现方法值得学习，最小修改

33_225：
修改了质检附件上传的存储路径。
修改系统名称，改为”生产管理系统“，修改了login.html、index.html

33_227:
修复SCANSN文件返工工单重复录取报错问题
修改只要某个阶段的所有三个角色都有最终提交记录（可以是不同的产品），该阶段就算完成。

33_228：修改SN打印记录输入的SN号格式支持特殊自定义的比如四代的#’；添加语音播报，可以开启关闭语音

33_229:修改工单每个阶段完成的逻辑判断，修改检验报表查询显示的工单状态判断

33_230：修改组装前扫码绑定的判断逻辑，改为在绑定外壳的时候判断，而不是直接在绑定电路板的时候，只是CPU绑定

33_231：修改组装前扫码绑定的判断逻辑，改为在绑定外壳的时候判断，而不是直接在绑定电路板的时候，增加IO绑定

33_232：添加固件管理菜单

33_233：固件版本采用Vue + Element混合架构与实现
33_234：在js\utils增加FirmwareDataManager.js实现四个页面的数据互通
33_235:删除不必要的功能

33_242:完整可用版本固件后端修改，但是有瑕疵需要进一步更改。
33_243：增加删除按钮，比如版本A不要了，只有再次“更新版本”，提交审核的时候选择“拒绝”，回退到“所有固件”页面的内容会显示删除按钮，删除后不影响使用记录和作废斑斑的记录
33_244:作废版本的附件记录表firmware_file`的is_deleted字段会变为1，已经软删除
33_246:o4迷你实现固件管理用户名自动获取；snoet4实现使用用户名的自动获取
33_247：修复第一次进入固件管理页面显示空白需要刷新浏览器的问题
33_248:使用模态框添加工单号自动获取相关信息把构建日期改为vchar类型
33_249:添加工单号下产品明细的保存，根据工单类型决定需要录入保存什么信息
33_250:增加使用记录页面录入的后端记录
33_251：把SN的默认值改为空值可以改为唯一的、可以正常录入保存
33_252：实现分页功能公用组件的方式
33_253：调整表格样式防止文本换行并增加溢出提示、优化列宽、减小行距
33_256：调整所有页面的模态框aung样式、完美可用
33_257：调整所有页面的模态框aung样式、调整按钮、搜索框样式、完美的一版
33_258：调整所有页面的模态框aung样式、调整按钮、搜索框样式、完美的一版、修复待审核、作废版本页面的适用产品等内容过多导致显示不全的问题

33_259:优化部分显示
33_260:优化使用记录的功能
33_265:固件上传增加到500M、添加前端后端的HASH256校验
33_269:实现版本比对的完整UI
33_270：实现版本比对的后端功能
33_272：增加批次号比对，"批次号比对逻辑：基准值为'无'时直接显示正确，为其他值时进行实际比对"
33_273：把批次号比对改为工单号比对、因为工单号是唯一的、如果工单号没问题、那么对应的批次号也没问题
33_274:增加客户名称、比对可以先输入预设的基本版本信息

33_275:修改出货记录的功能增加客户验证自动开启验证SN的开关、增加SN的版本验证是不是最新的、增加客户验证
33_276：增加耦合器、IO模块自动获取软件版本、构建日期参数
33_277：增加CPU自动获取软件版本、构建日期参数

33_278：增加CPU自动获取软件版本、构建日期参数，解决测试页面提交后不会聚焦到SN输入框aung的情况

33_280：优化固件表的字段类型长度

33_283:相比280 、281、修改了BarcodeBinding.js的内容增加PCAB序列号长度验证等.如果扫码绑定有问题可以直接复制  33_280的BarcodeBinding.js

33_284:增加只有首检完成了最终提交、其余角色才能够提交。相比280 、281、修改了SelfInspection.js的内容每个阶段都必须按照"首检→自检→IPQC"的顺序进行.如果自检过程有问题可以直接复制  33_280的SelfInspection.js

33_285:固件管理最终相对完整的版本

33_286:修改固件上传路径、修改firmware_utils.py、config.py

33_288:如果不是最新的版本也能在检基准处查询到，并且语音播报版本信息

ALTER TABLE firmware ADD COLUMN build_time VARCHAR(100) COMMENT '构建时间';
ALTER TABLE firmware ADD COLUMN backplane_version VARCHAR(50) COMMENT '背板总线版本';
ALTER TABLE firmware ADD COLUMN io_version VARCHAR(50) COMMENT '高速IO版本';

33_289:修复CPU查询页面获取版本信息的逻辑，改为直接用工单号获取，修改版本比对错误自动清除SN号
33_290:修复一些已知问题
33_291:修改固件管理使用录入可以自由选择是PCBA还是sn
33_294:测试页面都用vue替代了，主要修改了scripts文件，解决页面刷新2次的问题
33_296:cpu测试后端独立、完全orm,增加faultentry_table的数据模型在test_result.py
33_298：耦合器、IO模块采用orm，三个测试页面的后端VUE重构完成，已经达到与CPU_控制器Vue版本同等级的优化后端

vue测试重构：
33_299优化后的vue01.md：CPU测试页面基本完整重构了
🛠️ 技术栈配置
保持本地版本：
Vue 3 (本地UMD版本)
Element Plus (本地版本)
CDN引入：
Tailwind CSS (CDN)
Lucide Icons (CDN)

33_300优化后的vue02.md：CPU测试页面基本完整重构了.添加日志记录、优化只读样式、调整待测文字图标位置
33_301优化后的vue03.md：CPU测试页面基本完整重构了.添加日志记录、优化只读样式、调整待测文字图标位置、隐藏滚动条、调整基本信息表单

33_302优化后的vue03.md：重构CPUControllerVue页面的样式把内联样式独立出来、采用bem命名，但是全局作用没有更改有问题。
33_303优化后的vue04.md：样式隔离是组件化开发的基础！即使使用BEM，也必须确保作用域完全隔离！ok

33_305优化后的vue06.md：三个测试页面基本全部完成样式统一的重构、待修复测试提交逻辑
33_314优化后的vue09.md:优化深色主题下拉框、整体样式UI等可用版本好的

33_315：修复测试提交逻辑、默认提交为全部通过、手动选择不通过根据实际保留
33_317：修复测试提交逻辑、默认提交为全部通过、手动选择不通过根据实际保留、优化测试页面错误提示样式
33_319：耦合器V添加自动提交的功能
33_320：耦合器V、IO模块V 测试页面添加自动提交开关，开启之后按照提交的检验逻辑，只有复合要求的才会自动执行提交
33_321:修复控制器测试的日志样式
33_322:修改CPU测试提交逻辑，点击提交的时候执行全选通过再提交数据”点击"提交测试" → 自动全通过 → 验证表单 → 提交数据“。已备份、
33_325：测试页面添加订货号判断、自检过程添加订货号判断、添加跳过各阶段检查的逻辑

33_326：修改固件上传显示问题，隐藏旧的测试页面
33_327:调整文档位置、相关文档都放到readme文件夹里20250627
33_328:把tailwindcss和lucide图标改为本地引用
33_330:添加Logger.js 统一管理页面的网页控制台输入信息，除了VUE的测试/固件管理页面未修改
33_331:添加Logger.js 统一管理页面的网页控制台输入信息，除了自检过程页面还没修改
33_332:添加Logger.js 统一管理页面的网页控制台输入信息，修改了所有页面
33_333:锁定已经测试的了，不能手动更改结果，但是全部通过的还是会被更改结果
33_334:锁定已经测试的了，不能手动更改结果，点击提交测试的时候自动跳过自己获取M区结果的测试项
33_335：添加耦合器的产品配置
33_336:根据产品配置动态加载耦合器页面的测试项目
33_337:根据产品配置动态加载耦合器页面的测试项目、后端也修改了根据动态加载的数据提交测试项目
33_338:根据产品配置动态加载耦合器、CPU控制器页面的测试项目、后端也修改了根据动态加载的数据提交测试项目
33_339:CPU_控制器 添加产品测试项目动态加载，根据加载的内容动态提交
33_341:CPU_控制器 添加产品测试项目动态加载，根据加载的内容动态提交，移除'前端的RS485_2通信“，保留“RS485_1通信”，只是把前端的name"RS485_1通信"改为“RS485_通信”，用的还是RS485_1
33_342:RS485_通信测试项目的多M区组合判断功能实现，完全按照您的需求精确实施.
33_345:增加成品测试CPU的日志记录、只有自动测试的通过M区判断的才会存储
33_346：修改测试日志前端显示的样式、采用工业风格的样式、完美可用
33_351：修改成品测试日志查询样式、导出图片的时候自动添加日志导出的渲染
33_352：添加了背板通信的双重判断、不单检测M5的值、还需要根据key=modulemessage判断是否有slave为1的值、有说明网页上读取到IO模块了
33_353：修改user_local_proxy.py采用tk和图标状态栏方式显示更直观看到服务有没有启动
33_354：加入视觉检测的逻辑
33_357：视觉检测逻辑正确、但是需要修复显示通过失败的时间顺序
33_358：启用视觉检测的时候、第一次会把视觉检测的项目的结果设置为待测、40秒之后在自动获取M去的值。best版本
33_359：完全独立的视觉检测和自动测试、不相互影响best01
33_360：不管是视觉检测还是自动测试都存储M区取值的日志或者手动完成的标记存储到数据库
33_361：完美的日志存储及成品测试查询图片内容的导出best
33_363:修复201产品测试项目不对及不能实时获取M区值的问题best09__ 20250723
33_364：修复视觉检测按钮背景色及addlog日志
33_365:CPu测试的M区M1-M5必须不为0才能提交
33_366：修改检验记录查询支持根据不同阶段、角色查询当前录入的SN明细、如果是部分提交的显示部分提交（通过）
33_367：质检记录添加导出SN和同一阶段同一角色同一个SN号重复提交的提示
33_368：同一阶段同一角色同一SN号重复提交作出组织不让提交、修改quality_inspection 309-318 添加修改角色即可"# 如果想对除了首检之外的所有角色进行检查，比如：if inspector_role != 'first':  # 对除首检外的所有角色进行此检查
last


最新在用
最新在用
最新在用