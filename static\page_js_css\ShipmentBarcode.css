/* 使用CSS变量定义主题颜色和尺寸 */
:root {
    --font-size-base: 1rem;
    --font-size-lg: clamp(1.1rem, 1vw + 0.5rem, 1.25rem);
    --font-size-xl: clamp(1.25rem, 1.5vw + 1rem, 1.5rem);
    --spacing-base: clamp(1rem, 2vw, 1.5rem);
    --card-padding: clamp(1rem, 2vw, 1.5rem);
    --border-radius: 0.5rem;
}

/* 基础容器样式 */
.shipment-barcode {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: var(--spacing-base);
    background-color: hsl(var(--background));
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 标题样式 */
.shipment-barcode__title {
    color: #1a1f36;
    font-size: var(--font-size-xl);
    font-weight: 500;
    margin-bottom: var(--spacing-base);
    padding-bottom: 0.625rem;
    position: relative;
}

.shipment-barcode__title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, 
        rgba(26, 31, 54, 0.8) 0%,
        rgba(26, 31, 54, 0.6) 50%,
        rgba(26, 31, 54, 0.2) 100%
    );
}

.shipment-barcode__subtitle {
    font-size: 14px;
    color: #697386;
    margin-bottom: 32px;
    line-height: 1.6;
}

/* 表单卡片样式 */
.shipment-barcode__form {
    display: grid;
    grid-template-columns: 19% 22% 22% 20% 12%;
    gap: var(--spacing-base);
    background: hsl(var(--card));
    border-radius: var(--border-radius);
    padding: var(--card-padding);
    margin-bottom: var(--spacing-base);
    border: 1px solid hsl(var(--border));
}

/* 表单组样式 */
.shipment-barcode__form-group {
    margin-bottom: clamp(1rem, 1.5vw, 1.25rem);
    position: relative;
}

/* 标签样式 */
.shipment-barcode__label {
    display: block;
    color: hsl(var(--foreground));
    font-size: var(--font-size-base);
    font-weight: 500;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.shipment-barcode__form-group.required .shipment-barcode__label {
    color: #1a1f36;
}

.shipment-barcode__form-group.required.empty .shipment-barcode__label {
    color: #1a1f36;
}

.shipment-barcode__form-group.required .shipment-barcode__label:after {
    content: '*';
    color: #f56c6c;
    margin-left: 4px;
}

/* 输入框基础样式 */
.shipment-barcode__input,
.shipment-barcode__datetime,
.shipment-barcode__sn-input {
    width: 100%;
    height: 2.5rem;
    padding: 0 0.75rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    line-height: 2.5rem;
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.shipment-barcode__input:hover,
.shipment-barcode__datetime:hover {
    border-color: #409eff;
}

.shipment-barcode__input:focus,
.shipment-barcode__datetime:focus {
    outline: none;
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64,158,255,.1);
}

.shipment-barcode__input::placeholder {
    color: #c0c4cc;
}

.shipment-barcode__input[readonly] {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #909399;
    cursor: not-allowed;
}

.shipment-barcode__auto-mode {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.shipment-barcode__switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-right: 12px;
}

.shipment-barcode__switch-input {
    opacity: 0;
    width: 0;
    height: 0;
}

.shipment-barcode__switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.shipment-barcode__switch-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.shipment-barcode__switch-input:checked + .shipment-barcode__switch-slider {
    background-color: #1976D2;
}

.shipment-barcode__switch-input:checked + .shipment-barcode__switch-slider:before {
    transform: translateX(26px);
}

.shipment-barcode__mode-text {
    font-size: 14px;
    color: #1a1f36;
    font-weight: 500;
}

.shipment-barcode__mode-status {
    color: #1976D2;
    font-size: 14px;
    font-weight: 500;
}

.shipment-barcode__input-container {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.shipment-barcode__input-wrapper {
    flex: 1;
}

.shipment-barcode__btn--add,
.shipment-barcode__btn--submit {
    padding: 8px 20px;
    font-size: 14px;
    border-radius: 4px;
    border: 1px solid transparent;
    font-weight: 500;
    cursor: pointer;
    transition: all .3s;
    line-height: 1.5;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.shipment-barcode__btn--add {
    background-color: #409eff;
    color: white;
}

.shipment-barcode__btn--add:hover {
    background-color: #66b1ff;
}

.shipment-barcode__btn--submit {
    background-color: #67c23a;
    color: white;
}

.shipment-barcode__btn--submit:hover {
    background-color: #85ce61;
}

.shipment-barcode__input-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
}

/* 自动模式特定样式 */
.shipment-barcode__scan-title {
    font-size: 14px;
    color: #1a1f36;
    margin-bottom: 16px;
    font-weight: 500;
}

.shipment-barcode__scan-hint {
    font-size: 13px;
    color: #697386;
    margin-top: 8px;
    line-height: 1.5;
}

.shipment-barcode__auto-input {
    display: none;
}

.shipment-barcode__auto-input.active {
    display: block;
}

.shipment-barcode__auto-input .shipment-barcode__input-wrapper {
    border-color: #3b82f6;
}

.shipment-barcode__manual-title {
    font-size: 14px;
    color: #1a1f36;
    margin-bottom: 16px;
    font-weight: 500;
}

.shipment-barcode__manual-input {
    display: block;
}

.shipment-barcode__manual-input.hidden {
    display: none;
}

.shipment-barcode__manual-input .shipment-barcode__input-wrapper {
    border: 1px solid #dcdfe6;
}

/* SN输入区域样式 */
.shipment-barcode__sn-input-section {
    display: grid;
    gap: var(--spacing-base);
    margin-bottom: var(--spacing-base);
}

.shipment-barcode__sn-card {
    background: hsl(var(--card));
    border-radius: var(--border-radius);
    padding: var(--card-padding);
    border: 1px solid hsl(var(--border));
}

.shipment-barcode__list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 24px 0 16px;
}

.shipment-barcode__list-title {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 500;
}

.shipment-barcode__sn-list {
    background-color: #fff;
    border-radius: 8px;
    padding: 32px;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #697386;
    font-size: 14px;
    border: 1px dashed #e5e7eb;
}

.shipment-barcode__sn-list.has-items {
    display: block;
    padding: 0;
}

/* 表格样式 */
.shipment-barcode__table-container {
    width: 100%;
    overflow-x: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 24px;
}

.shipment-barcode__table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    table-layout: fixed;
}

/* 强化所有表格单元格居中 */
.shipment-barcode__table th,
.shipment-barcode__table td {
    padding: 0.75rem 1rem;
    text-align: center !important; /* 添加!important确保优先级 */
    border-bottom: 1px solid #e5e7eb;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 强化表头居中样式 */
.shipment-barcode__table th {
    background-color: #f3f4f6;
    color: #1f2937;
    font-weight: 600;
    white-space: nowrap;
    text-align: center !important;
}

/* 确保产品SN号列内容居中 */
.shipment-barcode__table td:nth-child(2) {
    text-align: center !important;
}

/* 确保录入时间列内容居中 */
.shipment-barcode__table td:nth-child(3) {
    text-align: center !important;
}

/* 调整列宽 - 增加版本状态和批次状态列后重新分配 */
/* 序号列 */
.shipment-barcode__table th:nth-child(1),
.shipment-barcode__table td:nth-child(1) {
    width: 6%;
}

/* 产品SN号列 */
.shipment-barcode__table th:nth-child(2),
.shipment-barcode__table td:nth-child(2) {
    width: 25%;
}

/* 版本状态列 */
.shipment-barcode__table th:nth-child(3),
.shipment-barcode__table td:nth-child(3) {
    width: 12%;
}

/* 批次状态列 */
.shipment-barcode__table th:nth-child(4),
.shipment-barcode__table td:nth-child(4) {
    width: 10%;
}

/* 录入时间列 */
.shipment-barcode__table th:nth-child(5),
.shipment-barcode__table td:nth-child(5) {
    width: 18%;
}

/* 储存状态列 */
.shipment-barcode__table th:nth-child(6),
.shipment-barcode__table td:nth-child(6) {
    width: 12%;
}

/* 操作列 */
.shipment-barcode__table th:nth-child(7),
.shipment-barcode__table td:nth-child(7) {
    width: 17%;
}

.shipment-barcode__table tr:nth-child(even) {
    background-color: #f9fafb;
}

.shipment-barcode__table tr:hover {
    background-color: #e0f2fe;
}

/* 只读输入框样式 */
.shipment-barcode__datetime[readonly] {
    background-color: #f5f7fa;
    cursor: not-allowed;
    border-color: #dcdfe6;
    color: #909399;
}

.shipment-barcode__btn--delete {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    background-color: #f56c6c;
    color: white;
    border: none;
    cursor: pointer;
    transition: all .3s;
}

.shipment-barcode__btn--delete:hover {
    background-color: #f78989;
}

/* 响应式媒体查询 */
@media screen and (max-width: 768px) {
    .shipment-barcode__form {
        grid-template-columns: 1fr;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .shipment-barcode {
        background-color: hsl(var(--background-dark));
    }
    
    .shipment-barcode__input,
    .shipment-barcode__datetime,
    .shipment-barcode__sn-input {
        background-color: hsl(var(--background-dark));
        color: hsl(var(--foreground-dark));
        border-color: hsl(var(--border-dark));
    }
}

/* 自动模式激活/隐藏状态 */
.shipment-barcode__auto-input {
    display: none;
}

.shipment-barcode__auto-input.active {
    display: block;
}

.shipment-barcode__manual-input.hidden {
    display: none;
}

/* 空列表提示样式 */
.shipment-barcode__sn-list:not(.has-items) {
    color: #666;
    text-align: center;
    padding: 32px;
    background: #f5f5f5;
    border-radius: 4px;
}

/* 错误状态样式 */
.shipment-barcode__form-group.error .shipment-barcode__input,
.shipment-barcode__form-group.error .shipment-barcode__datetime {
    border-color: #f56c6c;
}

.shipment-barcode__form-group.error .shipment-barcode__input:focus,
.shipment-barcode__form-group.error .shipment-barcode__datetime:focus {
    box-shadow: 0 0 0 2px rgba(245,108,108,.2);
}

/* 错误提示信息 */
.error-message {
    position: absolute;
    font-size: 12px;
    color: #f56c6c;
    line-height: 1;
    padding-top: 4px;
    left: 0;
}

/* 必填项未填写时的输入框样式 */
.shipment-barcode__form-group.required.empty .shipment-barcode__input,
.shipment-barcode__form-group.required.empty .shipment-barcode__datetime {
    border-color: #dcdfe6;
    background-color: #fff;
}

/* 必填项未填写时的输入框hover效果 */
.shipment-barcode__form-group.required.empty .shipment-barcode__input:hover,
.shipment-barcode__form-group.required.empty .shipment-barcode__datetime:hover {
    border: 2px solid #f56c6c;
    padding: 0 11px;
}

/* 必填项未填写时的输入框focus效果 */
.shipment-barcode__form-group.required.empty .shipment-barcode__input:focus,
.shipment-barcode__form-group.required.empty .shipment-barcode__datetime:focus {
    border: 2px solid #f56c6c;
    padding: 0 11px;
    box-shadow: 0 0 0 2px rgba(245,108,108,.1);
}

/* 输入框有值时的样式 */
.shipment-barcode__input.input-has-value,
.shipment-barcode__datetime.input-has-value {
    border: 1px solid #dcdfe6;
    background-color: #f0f9eb;
    padding: 0 12px;
}

/* 输入框有值时的hover效果 */
.shipment-barcode__input.input-has-value:hover,
.shipment-barcode__datetime.input-has-value:hover {
    border: 1px solid #c0c4cc;
}

/* 输入框有值时的focus效果 */
.shipment-barcode__input.input-has-value:focus,
.shipment-barcode__datetime.input-has-value:focus {
    border: 1px solid #409eff;
    box-shadow: 0 0 0 2px rgba(64,158,255,.1);
}

/* 新添加的SN号行样式 */
.shipment-barcode__table tr.new-item {
    background-color: #f0f9eb;
    animation: highlight-new-item 2s ease-in-out;
}

@keyframes highlight-new-item {
    0% {
        background-color: #67c23a;
    }
    100% {
        background-color: #f0f9eb;
    }
}

/* 储存状态样式 */
.storage-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #67c23a;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

/* 版本状态样式 */
.version-status-normal {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #67c23a;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

.version-status-error {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #f56c6c;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

/* 批次状态样式 */
.batch-status-normal {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #67c23a;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

.batch-status-error {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #f56c6c;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

/* 表格基础样式 */
.table-section {
    overflow-x: auto;
}

.shipment-barcode__table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.shipment-barcode__table th,
.shipment-barcode__table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.shipment-barcode__table th {
    background-color: #f3f4f6;
    color: #1f2937;
    font-weight: 600;
    white-space: nowrap;
}

.shipment-barcode__table tr:nth-child(even) {
    background-color: #f9fafb;
}

.shipment-barcode__table tr:hover {
    background-color: #e0f2fe;
}

/* 分页样式 */
.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
}

.table-length {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    color: #4b5563;
    font-size: 0.875rem;
}

.page-size-selector {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.page-size-selector select {
    margin: 0 0.25rem;
    padding: 0.25rem 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: #ffffff;
}

.table-pagination {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-page {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
    color: #374151;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-page:hover:not(:disabled) {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.btn-page.active {
    background-color: #1e40af;
    color: #ffffff;
    border-color: #1e40af;
}

.btn-page:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-ellipsis {
    padding: 0 0.5rem;
    color: #6b7280;
}

/* 按钮图标样式 */
.btn-icon {
    padding: 0.375rem;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
    color: #374151;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-icon:hover:not(:disabled) {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.btn-icon:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 按钮组样式 */
.shipment-barcode__button-group {
    display: none;
}

/* 调整列表操作按钮组样式 */
.shipment-barcode__list-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* 调整完成录入按钮样式，使其与其他按钮保持一致的外观 */
.shipment-barcode__btn--submit {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #1e40af;  /* 使用深蓝色区分功能 */
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
    margin: 0;  /* 移除所有外边距 */
}

.shipment-barcode__btn--submit:hover {
    background-color: #2563eb;
}

/* 导出按钮样式 */
.shipment-barcode__btn--export {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #67c23a;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.shipment-barcode__btn--export:hover {
    background-color: #85ce61;
}

.shipment-barcode__btn--export i {
    font-size: 16px;
}

/* 导入按钮样式 */
.shipment-barcode__btn--import {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.shipment-barcode__btn--import:hover {
    background-color: #66b1ff;
}

.shipment-barcode__btn--import i {
    font-size: 16px;
}

/* 提示图标样式 */
.shipment-barcode__mode-tip {
    position: relative;
    margin-left: 8px;
    display: inline-block;
}

.shipment-barcode__mode-tip i {
    color: #909399;
    font-size: 16px;
    cursor: help;
    transition: color 0.3s;
}

.shipment-barcode__mode-tip:hover i {
    color: #409eff;
}

/* 提示内容样式 */
.shipment-barcode__mode-tip-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 8px;
    padding: 16px;
    width: 280px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    font-size: 14px;
    color: #606266;
    border: 1px solid #e4e7ed;
}

.shipment-barcode__mode-tip:hover .shipment-barcode__mode-tip-content {
    display: block;
}

/* 提示框标题 */
.shipment-barcode__mode-tip-content h4 {
    margin: 0 0 12px;
    color: #303133;
    font-size: 15px;
    font-weight: 500;
}

/* 提示框列表样式 */
.shipment-barcode__mode-tip-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.shipment-barcode__mode-tip-content li {
    margin: 4px 0;
    line-height: 1.5;
}

.shipment-barcode__mode-tip-content p {
    margin: 8px 0 4px;
}

.shipment-barcode__mode-tip-content strong {
    color: #303133;
}

/* 添加小箭头 */
.shipment-barcode__mode-tip-content::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    width: 12px;
    height: 12px;
    background: white;
    border-left: 1px solid #e4e7ed;
    border-top: 1px solid #e4e7ed;
}

.shipment-barcode__customer-check {
    display: flex;
    align-items: center;
    margin-bottom: 20px; /* 或根据需要调整间距 */
    /* 可以复用 .shipment-barcode__auto-mode 的部分样式 */
}

.shipment-barcode__check-text {
    font-size: 14px;
    color: #1a1f36;
    font-weight: 500;
     /* 可以复用 .shipment-barcode__mode-text 的样式 */
}

/* 确保开关样式一致 */
.shipment-barcode__customer-check .shipment-barcode__switch {
    /* 确保样式与 auto-mode-toggle 一致 */
     position: relative;
     display: inline-block;
     width: 50px;
     height: 24px;
     margin-right: 12px;
}
.shipment-barcode__customer-check .shipment-barcode__switch-input {
    opacity: 0;
    width: 0;
    height: 0;
}
.shipment-barcode__customer-check .shipment-barcode__switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}
.shipment-barcode__customer-check .shipment-barcode__switch-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}
.shipment-barcode__customer-check .shipment-barcode__switch-input:checked + .shipment-barcode__switch-slider {
    background-color: #1976D2; /* 选中时的颜色 */
}
.shipment-barcode__customer-check .shipment-barcode__switch-input:checked + .shipment-barcode__switch-slider:before {
    transform: translateX(26px);
} 