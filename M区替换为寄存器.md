# M区替换为状态寄存器规则说明文档

## 概述

本文档规定了在`addTestLog`日志记录中，将"M区"相关术语替换为"状态寄存器"术语的标准规则和命名规范。此替换仅影响日志显示内容，不改变系统核心功能和数据处理逻辑。

## 替换原则

### 1. 替换范围
- **仅限于**: `addTestLog`函数调用中的日志消息内容
- **不包括**: 变量名、函数名、API接口、数据处理逻辑、配置文件等

### 2. 替换目标
- 隐藏内部实现细节中的"M区"概念
- 使用更通用、专业的"状态寄存器"术语
- 保持日志的可读性和专业性

## 日志类别标识替换规则

### 基础替换模式
```
M_AREA_* → STATUS_REG_*
```

### 具体替换对照表

| 原始标识 | 替换后标识 | 用途说明 |
|---------|-----------|----------|
| `M_AREA_CONFIG` | `STATUS_REG_CONFIG` | 状态寄存器配置信息 |
| `M_AREA_LOG` | `STATUS_REG_LOG` | 状态寄存器测试日志 |
| `M_AREA_REALTIME` | `STATUS_REG_REALTIME` | 实时状态寄存器数据获取 |
| `M_AREA_FALLBACK` | `STATUS_REG_FALLBACK` | 状态寄存器数据回退处理 |
| `M_AREA_COMBINED` | `STATUS_REG_COMBINED` | 组合状态寄存器判断 |
| `M_AREA_SINGLE` | `STATUS_REG_SINGLE` | 单一状态寄存器判断 |
| `M_AREA_VALIDATION` | `STATUS_REG_VALIDATION` | 状态寄存器验证 |
| `M_AREA_LOCK` | `STATUS_REG_LOCK` | 状态寄存器项目锁定 |
| `M_AREA_ERROR` | `STATUS_REG_ERROR` | 状态寄存器错误处理 |
| `M_AREA_SUMMARY` | `STATUS_REG_SUMMARY` | 状态寄存器测试总结 |
| `M_AREA_REFRESH` | `STATUS_REG_REFRESH` | 状态寄存器数据刷新 |
| `M_AREA_INIT` | `STATUS_REG_INIT` | 状态寄存器初始化 |

### 特殊场景替换

| 原始标识 | 替换后标识 | 说明 |
|---------|-----------|------|
| `BACKPLANE_M_AREA` | `BACKPLANE_STATUS_REG` | 背板通信状态寄存器检查 |

## 日志消息内容替换规则

### 1. 基础术语替换

| 原始术语 | 替换术语 | 示例 |
|---------|---------|------|
| M区 | 状态寄存器 | "M区数据" → "状态寄存器数据" |
| M区控制 | 状态寄存器控制 | "M区控制项目" → "状态寄存器控制项目" |
| M区值 | 寄存器值 | "M区值获取" → "寄存器值获取" |
| M区检查 | 状态寄存器检查 | "M区检查通过" → "状态寄存器检查通过" |
| M区验证 | 状态寄存器验证 | "M区验证失败" → "状态寄存器验证失败" |
| M区统计 | 状态寄存器统计 | "M区统计失败" → "状态寄存器统计失败" |
| M区判断 | 状态寄存器判断 | "不做M区判断" → "不做状态寄存器判断" |

### 2. 寄存器编号替换

| 原始格式 | 替换格式 | 示例 |
|---------|---------|------|
| M0, M1, M2... | REG0, REG1, REG2... | "M0=1" → "REG0=1" |
| M${index} | REG${index} | "M${detail.mIndex}" → "REG${detail.mIndex}" |
| M${i}+M${j} | REG${i}+REG${j} | "M0+M1" → "REG0+REG1" |

### 3. 组合表达式替换

```javascript
// 原始格式
`M${mIndices.join('+M')}=${values.join('+')}`

// 替换后格式  
`REG${mIndices.join('+REG')}=${values.join('+')}`

// 示例
"M0+M1=1+1" → "REG0+REG1=1+1"
```

## 实际应用示例

### 示例1: 基础日志替换
```javascript
// 原始代码
addTestLog('info', 'M_AREA_REALTIME', '正在实时获取M区数据...');

// 替换后代码
addTestLog('info', 'STATUS_REG_REALTIME', '正在实时获取状态寄存器数据...');
```

### 示例2: 复杂消息替换
```javascript
// 原始代码
addTestLog('success', 'M_AREA_COMBINED', 
          `${testName}: M0+M1=1+1 (AND模式)`, 
          `组合判断通过: 所有M区值都为1`);

// 替换后代码
addTestLog('success', 'STATUS_REG_COMBINED', 
          `${testName}: REG0+REG1=1+1 (AND模式)`, 
          `组合判断通过: 所有状态寄存器值都为1`);
```

### 示例3: 动态内容替换
```javascript
// 原始代码
addTestLog('info', 'M_AREA_CONFIG', mAreaInfo);

// 替换后代码
addTestLog('info', 'STATUS_REG_CONFIG', 
          mAreaInfo.replace(/M区/g, '状态寄存器').replace(/M(\d+)/g, 'REG$1'));
```

## 特殊处理规则

### 1. 正则表达式替换
```javascript
// M区文本替换
text.replace(/M区/g, '状态寄存器')

// M+数字格式替换
text.replace(/M(\d+)/g, 'REG$1')
```

### 2. 数组处理
```javascript
// 原始格式
mAreaTestResults.map(r => `${r.test}(${r.mValue})=${r.result}`)

// 替换后格式
mAreaTestResults.map(r => `${r.test}(${r.mValue.replace(/M(\d+)/g, 'REG$1')})=${r.result}`)
```

### 3. 注释内容
```javascript
// 原始注释
// 第六步：检测拨码开关 (M11)

// 替换后注释
// 第六步：检测拨码开关 (REG11)
```

## 不变更内容

以下内容**不进行替换**，保持原有命名：

### 1. 变量和函数名
- `mAreaData`
- `parseMAreaData()`
- `mAreaValues`
- `mAreaControl`
- `mAreaTestCompleted`

### 2. 配置管理器调用
- `configManager.getMAreaControlInfo()`
- `configManager.validateCombinedMAreaTest()`
- `configManager.getAutoTestMAreaControlInfo()`

### 3. API接口和数据结构
- `/api/cpu-controller-vue/update-m-area-log`
- `m_area_log` 字段名
- 数据库字段名

### 4. 内部逻辑标识
- `mIndex`、`mIndices`
- `mAreaMapping`
- 配置文件中的字段名

## 质量保证

### 1. 一致性检查
- 同类型日志使用相同的替换规则
- 寄存器编号格式统一为 `REG{数字}`
- 术语替换保持上下文一致

### 2. 完整性验证
- 确保所有 `addTestLog` 中的M区相关内容都已替换
- 验证替换后的日志消息语义正确
- 检查是否有遗漏的M区术语

### 3. 功能验证
- 替换后系统功能正常运行
- 日志记录功能不受影响
- 测试逻辑保持不变

## 维护指南

### 1. 新增日志规范
在添加新的 `addTestLog` 时，如涉及M区相关内容，应直接使用"状态寄存器"术语：

```javascript
// 推荐写法
addTestLog('info', 'STATUS_REG_CHECK', '状态寄存器检查开始', `检查REG${index}状态`);

// 避免写法
addTestLog('info', 'M_AREA_CHECK', 'M区检查开始', `检查M${index}状态`);
```

### 2. 代码审查要点
- 检查新增的 `addTestLog` 是否遵循替换规则
- 确认日志类别标识使用 `STATUS_REG_*` 格式
- 验证寄存器编号使用 `REG{数字}` 格式

### 3. 文档更新
当系统功能发生变化时，及时更新本规则文档，确保替换规则的时效性和准确性。

---

**版本**: 1.0  
**创建日期**: 2025-01-21  
**适用范围**: CPUControllerVue.js 及相关测试系统  
**维护者**: 开发团队





文档结构
1. 概述和替换原则
明确了替换范围（仅限addTestLog）
说明了替换目标和原则
2. 日志类别标识替换规则
提供了完整的替换对照表
包含了所有已实施的替换（如M_AREA_* → STATUS_REG_*）
3. 日志消息内容替换规则
基础术语替换（M区 → 状态寄存器）
寄存器编号替换（M0 → REG0）
组合表达式替换规则
4. 实际应用示例
提供了3个不同复杂度的替换示例
包含动态内容替换的处理方法
5. 特殊处理规则
正则表达式替换方法
数组处理规则
注释内容替换
6. 不变更内容清单
明确列出了不需要替换的内容
包括变量名、函数名、API接口等
7. 质量保证和维护指南
一致性检查要点
新增日志的规范要求
代码审查指导
文档特点
规则明确: 每个替换规则都有具体的对照表和示例
实用性强: 提供了实际代码示例和正则表达式
完整性好: 涵盖了所有已实施的替换场景
可维护: 包含了未来维护和扩展的指导原则
这份文档可以作为团队的标准参考，确保在后续开发中保持一致的替换规则和命名规范。





修改总结
我已经成功将所有addTestLog中的M区相关信息替换为"状态寄存器"术语：

1. 日志类别标识替换
M_AREA_CONFIG → STATUS_REG_CONFIG
M_AREA_LOG → STATUS_REG_LOG
M_AREA_REALTIME → STATUS_REG_REALTIME
M_AREA_FALLBACK → STATUS_REG_FALLBACK
BACKPLANE_M_AREA → BACKPLANE_STATUS_REG
M_AREA_COMBINED → STATUS_REG_COMBINED
M_AREA_SINGLE → STATUS_REG_SINGLE
M_AREA_VALIDATION → STATUS_REG_VALIDATION
M_AREA_LOCK → STATUS_REG_LOCK
M_AREA_ERROR → STATUS_REG_ERROR
M_AREA_SUMMARY → STATUS_REG_SUMMARY
M_AREA_REFRESH → STATUS_REG_REFRESH
M_AREA_INIT → STATUS_REG_INIT
2. 日志消息内容替换
"M区" → "状态寄存器"
"M区数据" → "状态寄存器数据"
"M区控制" → "状态寄存器控制"
"M区值" → "状态寄存器值"
"M区检查" → "状态寄存器检查"
"M区验证" → "状态寄存器验证"
"M区统计" → "状态寄存器统计"
"M区智能测试" → "状态寄存器智能测试"
3. 寄存器编号替换
"M0、M1、M11" → "REG0、REG1、REG11"
"M
𝑑
𝑒
𝑡
𝑎
𝑖
𝑙
.
𝑚
𝐼
𝑛
𝑑
𝑒
𝑥
"
→
"
𝑅
𝐸
𝐺
detail.mIndex"→"REG{detail.mIndex}"
"M
𝑚
𝐼
𝑛
𝑑
𝑖
𝑐
𝑒
𝑠
.
𝑗
𝑜
𝑖
𝑛
(
′
+
𝑀
′
)
"
→
"
𝑅
𝐸
𝐺
mIndices.join( 
′
 +M 
′
 )"→"REG{mIndices.join('+REG')}"
4. 特殊场景替换
自动测试模式说明中的"M区项目" → "状态寄存器项目"
视觉检测中的"M区测试" → "状态寄存器测试"
锁定状态说明中的"M区结果" → "状态寄存器结果"
注释中的"(M11)" → "(REG11)"
保持不变的部分
按照要求，我只修改了addTestLog相关的打印信息，以下功能保持不变：

变量名（如mAreaData、parseMAreaData等）
函数名和API接口
数据处理逻辑
配置管理器的调用
其他非日志相关的代码
这样的修改确保了在测试日志中完全隐藏了"M区"这个术语，使用更通用的"状态寄存器"概念，同时不影响系统的核心功能。