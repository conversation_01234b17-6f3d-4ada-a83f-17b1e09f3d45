/* 基础样式 */
#product-management-page .container {
    padding: 1.5rem;
    max-width: 100%;
    background-color: #f3f4f6;
    min-height: calc(100vh - var(--header-height));
}

/* 卡片基础样式 */
#product-management-page .card {
    background: #ffffff;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    margin-bottom: 1rem;
    padding: 1.25rem;
}

/* 页面标题 */
#product-management-page .page-header {
    margin-bottom: 1.5rem;
}

#product-management-page .page-header h1 {
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
}

/* 搜索区域 */
#product-management-page .search-section {
    margin-bottom: 1rem;
}

#product-management-page .search-form {
    display: flex;
    align-items: center;
}

#product-management-page .search-input-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
}

#product-management-page .search-input-wrapper {
    position: relative;
    flex: 1;
}

#product-management-page .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #4b5563;
    pointer-events: none; /* 确保图标不会干扰输入 */
}

#product-management-page .search-form input {
    width: 100%;
    padding: 0.625rem 0.75rem 0.625rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s;
}

#product-management-page .search-form input:focus {
    outline: none;
    border-color: #1e40af;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

#product-management-page .search-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* 查询按钮 */
#product-management-page .btn-search {
    background-color: #1e40af;
    color: #ffffff;
}

#product-management-page .btn-search:hover {
    background-color: #1e3a8a;
}

/* 重置按钮 */
#product-management-page .btn-reset {
    background-color: #dddbdb;
    color: #4b5563;
    border: 1px solid #d1d5db;
}

#product-management-page .btn-reset:hover {
    background-color: #f3f4f6;
}

/* 成功按钮（导出Excel） */
#product-management-page .btn-success {
    background-color: #059669;
    color: #ffffff;
}

#product-management-page .btn-success:hover {
    background-color: #047857;
}

/* 按钮组 */
#product-management-page .button-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* 按钮基础样式 */
#product-management-page .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
    border: none;
    gap: 0.5rem;
}

#product-management-page .btn i {
    font-size: 1rem;
}

/* 主要按钮 */
#product-management-page .btn-primary {
    background-color: #1e40af;
    color: #ffffff;
}

#product-management-page .btn-primary:hover {
    background-color: #1e3a8a;
}

/* 次要按钮 */
#product-management-page .btn-search {
    background-color: #e0f2fe;
    color: #1e40af;
}

#product-management-page .btn-search:hover {
    background-color: #bae6fd;
}

/* 危险按钮 */
#product-management-page .btn-danger {
    background-color: #dc2626;
    color: #ffffff;
}

#product-management-page .btn-danger:hover {
    background-color: #b91c1c;
}

/* 轮廓按钮 */
#product-management-page .btn-outline {
    background-color: transparent;
    border: 1px solid #4b5563;
    color: #4b5563;
}

#product-management-page .btn-outline:hover {
    background-color: #f3f4f6;
}

/* 表格容器样式 */
#product-management-page .table-section {
    position: relative;
    overflow-x: auto !important;  /* 启用水平滚动 */
    overflow-y: hidden;          /* 禁用垂直滚动 */
    background: #ffffff;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

/* 表格基础样式 */
#product-management-page .data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    table-layout: fixed;     /* 使用固定表格布局 */
    min-width: 1500px;       /* 设置最小宽度确保列宽 */
}

/* 调整单元格高度，使其更紧凑 */
#product-management-page .data-table th,
#product-management-page .data-table td {
    padding: 0.5rem 1rem;    /* 减小上下padding */
    height: 40px;            /* 固定行高 */
    line-height: 1.5;        /* 确保文本垂直居中 */
    box-sizing: border-box;  /* 确保padding不会增加总高度 */
}

/* 列宽控制 */
#product-management-page .data-table th:nth-child(1),
#product-management-page .data-table td:nth-child(1) {
    width: 2%;  /* 复选框列 */
    min-width: 10px;
}

#product-management-page .data-table th:nth-child(2),
#product-management-page .data-table td:nth-child(2) {
    width: 11%;  /* 产品编码列 */
    min-width: 120px;
}

#product-management-page .data-table th:nth-child(3),
#product-management-page .data-table td:nth-child(3) {
    width: 20%;  /* 产品型号列 */
    min-width: 150px;
}

#product-management-page .data-table th:nth-child(4),
#product-management-page .data-table td:nth-child(4) {
    width: 9%;  /* 产品类型列 */
    min-width: 120px;
}

#product-management-page .data-table th:nth-child(5),
#product-management-page .data-table td:nth-child(5) {
    width: 8%;  /* 品牌列 */
    min-width: 120px;
}

#product-management-page .data-table th:nth-child(6),
#product-management-page .data-table td:nth-child(6) {
    width: 8%;  /* 订货号列 */
    min-width: 100px;
}

#product-management-page .data-table th:nth-child(7),
#product-management-page .data-table td:nth-child(7) {
    width: 6%;   /* SN长度列 */
    min-width: 60px;
}

#product-management-page .data-table th:nth-child(8),
#product-management-page .data-table td:nth-child(8) {
    width: 8%;  /* 创建人员列 */
    min-width: 80px;
}

#product-management-page .data-table th:nth-child(9),
#product-management-page .data-table td:nth-child(9) {
    width: 12%;  /* 创建时间列 */
    min-width: 100px;
}

#product-management-page .data-table th:nth-child(10),
#product-management-page .data-table td:nth-child(10) {
    width: 6%;   /* 备注列 */
    min-width: 70px;
}

#product-management-page .data-table th:nth-child(11),
#product-management-page .data-table td:nth-child(11) {
    width: 8%;   /* 操作列 */
    min-width: 50px;
}

/* 单元格内容处理 */
#product-management-page .data-table td {
    white-space: nowrap;        /* 防止文本换行 */
    overflow: hidden;           /* 隐藏溢出内容 */
    text-overflow: ellipsis;    /* 显示省略号 */
    border-bottom: 1px solid #e5e7eb;
    color: #4b5563;
}

/* 表头样式 */
#product-management-page .data-table th {
    text-align: left;
    font-weight: 600;
    color: #1f2937;
    background-color: #f3f4f6;
    border-bottom: 1px solid #e5e7eb;
    white-space: nowrap;
}

/* hover效果 */
#product-management-page .data-table tr:hover {
    background-color: #f9fafb;
}

/* tooltip效果 */
#product-management-page .data-table td {
    position: relative;
}

#product-management-page .data-table td:hover::after {
    content: attr(title);
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 1000;
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: normal;
    max-height: 200px;      /* 限制tooltip最大高度 */
    overflow-y: auto;       /* 如果内容过长允许tooltip滚动 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-size: 12px;
}

/* 复选框样式 */
#product-management-page .data-table input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    margin: 0;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

#product-management-page .data-table th:first-child,
#product-management-page .data-table td:first-child {
    text-align: center;
    width: 40px;
}

/* 排序图标 */
#product-management-page .fa-sort,
#product-management-page .fa-sort-up,
#product-management-page .fa-sort-down {
    margin-left: 0.5rem;
    color: #4b5563;
}

/* 模态框样式 */
.product-management-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 1000;
}

.product-management-modal .modal-content {
    position: relative;
    margin: 10vh auto 0;
    background: #ffffff;
    border-radius: 0.5rem;
    width: 90%;
    max-width: 800px;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.product-management-modal .modal-header {
    margin: -1px -1px 0 -1px;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, #60a5fa, #93c5fd);
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    border-bottom: none;
    position: relative;
}

.product-management-modal .modal-header h2 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 500;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.05);
}

.product-management-modal .modal-header .close {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 400;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.2s;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    margin: -0.5rem -0.75rem -0.5rem 0;
}

.product-management-modal .modal-header .close:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.product-management-modal .modal-body {
    padding: 1.5rem;
}

.product-management-modal .modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 表单样式 */
.product-management-modal .two-column-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-management-modal .form-row {
    display: flex;
    gap: 1rem;
    width: 100%;
}

.product-management-modal .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.product-management-modal .form-row.full-width {
    flex-direction: column;
}

.product-management-modal .form-group {
    margin-bottom: 1rem;
}

.product-management-modal .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #4b5563;
    font-weight: 500;
}

.product-management-modal .form-group input,
.product-management-modal .form-group textarea {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    transition: all 0.2s;
}

.product-management-modal .form-group input:focus,
.product-management-modal .form-group textarea:focus {
    outline: none;
    border-color: #1e40af;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.product-management-modal .required {
    color: #dc2626;
    margin-left: 0.25rem;
}

/* 禁用输入框样式 */
.product-management-modal .form-group input:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .product-management-modal .form-row {
        flex-direction: column;
    }
    
    .product-management-modal .form-row .form-group {
        width: 100%;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.product-management-modal[style*="display: block"] {
    animation: fadeIn 0.3s ease-out;
}

/* 分页样式 */
#product-management-page .pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
}

#product-management-page .pagination-info {
    color: #4b5563;
    font-size: 0.875rem;
}

#product-management-page .pagination-controls {
    display: flex;
    gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #product-management-page .search-form {
        flex-direction: column;
    }
    
    #product-management-page .button-group {
        flex-wrap: wrap;
    }
    
    #product-management-page .btn {
        width: 100%;
    }
    
    #product-management-page .pagination-section {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
}

/* 可访问性增强 */
#product-management-page .btn:focus,
#product-management-page input:focus,
#product-management-page textarea:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

/* 确保足够的文字大小 */
body {
    font-size: 16px;
    line-height: 1.5;
}

/* 两列表单布局 */
#product-management-page .two-column-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

#product-management-page .form-row {
    display: flex;
    gap: 1rem;
    width: 100%;
}

#product-management-page .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

#product-management-page .form-row.full-width {
    flex-direction: column;
}

/* 禁用输入框样式 */
#product-management-page .form-group input:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
}

/* 调整模态框宽度 */
#product-management-page .modal-content {
    max-width: 800px;  /* 增加模态框宽度 */
}

/* 响应式处理 */
@media (max-width: 640px) {
    #product-management-page .form-row {
        flex-direction: column;
    }
    
    #product-management-page .form-row .form-group {
        width: 100%;
    }
    
    #product-management-page .search-input-group {
        flex-direction: column;
    }
    
    #product-management-page .search-buttons {
        width: 100%;
    }
    
    #product-management-page .search-buttons .btn {
        flex: 1;
    }
}

/* 表格头部和底部样式 */
#product-management-page .table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
}

#product-management-page .table-length {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    color: #4b5563;
    font-size: 0.875rem;
}

#product-management-page .page-size-selector {
    display: flex;
    align-items: center;
    white-space: nowrap;
}
#product-management-page .btn-page.active {
    background-color: #1e40af;
    color: #ffffff;
    border-color: #1e40af;
}


#product-management-page .page-size-selector select {
    margin: 0 0.25rem;
    padding: 0.25rem 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
}

#product-management-page .total-count {
    white-space: nowrap;
    color: #4b5563;
}

#product-management-page .table-pagination {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    #product-management-page .table-footer {
        flex-direction: column;
        gap: 1rem;
    }
    
    #product-management-page .table-length {
        width: 100%;
        justify-content: space-between;
    }
    
    #product-management-page .table-pagination {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* 导入Excel按钮 */
#product-management-page .btn-info {
    background-color: #3b82f6;
    color: #ffffff;
}

#product-management-page .btn-info:hover {
    background-color: #2563eb;
}

/* 编辑按钮样式 */
#product-management-page .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

#product-management-page .btn-edit {
    background-color: #3b82f6;
    color: #ffffff;
}

#product-management-page .btn-edit:hover {
    background-color: #2563eb;
}

/* 可双击单元格样式 */
#product-management-page .data-table td.dblclick-edit {
    cursor: pointer;
    transition: background-color 0.2s;
}

#product-management-page .data-table td.dblclick-edit:hover {
    background-color: #a4c3f1;
} 