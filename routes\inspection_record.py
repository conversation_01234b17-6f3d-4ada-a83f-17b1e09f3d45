#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PLC生产质量检验系统 - 检验记录查询API模块
"""

from flask import Blueprint, request, jsonify, current_app, url_for
from sqlalchemy import func, or_, and_, desc, asc
from datetime import datetime
import logging
from io import BytesIO
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment
import json
import openpyxl.utils

from database.db_manager import DatabaseManager
from models.modelquality_inspection import (
    QualityInspectionWorkOrder, 
    Product, 
    ProductType, 
    InspectionItem,
    ProductInspectionStatus, 
    InspectionAttachment
)

# 创建蓝图
inspection_record_bp = Blueprint('inspection_record', __name__, url_prefix='/api/inspection-records')

# 获取logger
logger = logging.getLogger(__name__)

@inspection_record_bp.route('/search', methods=['GET'])
def search_inspection_records():
    """
    检验记录查询接口
    支持按工单号、SN号、日期范围查询，并返回分页结果
    """
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            # 提取查询参数
            query_type = request.args.get('type', 'orderNo')  # 默认按工单号查询
            query_value = request.args.get('value', '').strip()
            page = request.args.get('page', 1, type=int)
            page_size = request.args.get('pageSize', 10, type=int)
            
            sort_field_request = request.args.get('sort_field', 'created_at')
            sort_order = request.args.get('sort_order', 'desc').lower()
            
            # 日期范围过滤
            start_date_str = request.args.get('startDate')
            end_date_str = request.args.get('endDate')
            
            # 新增：阶段和检验角色过滤
            stage_filter = request.args.get('stage', '').strip()
            inspector_role_filter = request.args.get('inspector_role', '').strip()

            # 构建基础查询
            # 如果有stage或inspector_role过滤条件，需要以Product为主表查询
            if stage_filter or inspector_role_filter:
                # 以Product为主表，连接相关表
                query = session.query(
                    Product,
                    QualityInspectionWorkOrder,
                    ProductType.type_name.label('product_type_name')
                ).join(
                    QualityInspectionWorkOrder,
                    Product.work_order_id == QualityInspectionWorkOrder.id
                ).join(
                    ProductType,
                    QualityInspectionWorkOrder.product_type_id == ProductType.id
                ).join(
                    ProductInspectionStatus,
                    Product.id == ProductInspectionStatus.product_id
                )
                
                # 应用阶段过滤
                if stage_filter:
                    query = query.filter(ProductInspectionStatus.stage == stage_filter)
                
                # 应用检验角色过滤
                if inspector_role_filter:
                    query = query.filter(ProductInspectionStatus.inspector_role == inspector_role_filter)
                
                # 使用distinct避免重复记录
                query = query.distinct()
                
            else:
                # 原有逻辑：以工单为主表查询
                query = session.query(
                    QualityInspectionWorkOrder,
                    ProductType.type_name.label('product_type_name')
                ).join(
                    ProductType, 
                    QualityInspectionWorkOrder.product_type_id == ProductType.id
                )

            # 根据查询类型和值构建过滤条件
            if query_value:
                if query_type == 'orderNo':
                    # 按工单号查询 (模糊匹配)
                    query = query.filter(QualityInspectionWorkOrder.work_order_no.ilike(f"%{query_value}%"))
                elif query_type == 'serialNumber':
                    # 按SN号查询 (模糊匹配)
                    if stage_filter or inspector_role_filter:
                        # 如果是Product为主表的查询，直接过滤Product表
                        query = query.filter(Product.serial_no.ilike(f"%{query_value}%"))
                    else:
                        # 原有逻辑：以工单为主表，需要连接Product表
                        query = query.join(
                            Product, 
                            QualityInspectionWorkOrder.id == Product.work_order_id
                        ).filter(
                            Product.serial_no.ilike(f"%{query_value}%")
                        )
            
            # 日期范围过滤
            if start_date_str:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                    query = query.filter(func.date(QualityInspectionWorkOrder.created_at) >= start_date)
                except ValueError:
                    logger.warning(f"无效的开始日期格式: {start_date_str}")
            
            if end_date_str:
                try:
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                    query = query.filter(func.date(QualityInspectionWorkOrder.created_at) <= end_date)
                except ValueError:
                    logger.warning(f"无效的结束日期格式: {end_date_str}")
            
            # 应用排序
            # 根据查询类型选择合适的排序字段映射
            if stage_filter or inspector_role_filter:
                # Product为主表的查询，排序字段映射
                sort_field_map = {
                    'order_no': QualityInspectionWorkOrder.work_order_no,
                    'serial_number': Product.serial_no,  # 新增SN号排序
                    'product_type': ProductType.type_name,
                    'status': QualityInspectionWorkOrder.status,
                    'created_at': QualityInspectionWorkOrder.created_at
                }
            else:
                # 工单为主表的查询，原有排序字段映射
                sort_field_map = {
                    'order_no': QualityInspectionWorkOrder.work_order_no,
                    'product_type': ProductType.type_name,
                    'status': QualityInspectionWorkOrder.status,
                    'created_at': QualityInspectionWorkOrder.created_at
                }
            
            sort_column = sort_field_map.get(sort_field_request, QualityInspectionWorkOrder.created_at)
            if sort_order == 'asc':
                query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(sort_column))
            
            # 获取总记录数
            total_count = query.count()
            
            # 应用分页
            query = query.offset((page - 1) * page_size).limit(page_size)
            
            # 执行查询并获取结果
            results = query.all()
            
            # 构建响应数据
            records_data = []
            
            if stage_filter or inspector_role_filter:
                # 当有stage/inspector_role筛选时，结果结构是：(Product, QualityInspectionWorkOrder, product_type_name)
                for product, wo, pt_name in results:
                    records_data.append({
                        'id': wo.id,  # 保持工单ID作为主键，但用于详情查询时会使用product_id
                        'order_no': wo.work_order_no,
                        'serial_number': product.serial_no,
                        'product_type': pt_name,
                        'status': wo.status,
                        'created_at': wo.created_at.strftime('%Y-%m-%d %H:%M:%S') if wo.created_at else '-',
                        'detail_query_type': 'serial_number',  # 有筛选条件时，详情应该按SN维度查询
                        'product_id_for_detail': product.id
                    })
            else:
                # 原有逻辑：结果结构是：(QualityInspectionWorkOrder, product_type_name)
                for wo, pt_name in results:
                    # 查询当前工单关联的SN信息
                    sn_info = None
                    product_id_for_detail = None
                    detail_query_type = 'work_order'  # 默认按工单查询详情
                    
                    if query_type == 'serialNumber' and query_value:
                        # 如果是按SN查询，优先获取查询的那个SN
                        sn_info = session.query(Product).filter(
                            Product.work_order_id == wo.id,
                            Product.serial_no.ilike(f"%{query_value}%")
                        ).first()
                        
                        if sn_info:
                            detail_query_type = 'serial_number'
                            product_id_for_detail = sn_info.id
                    
                    # 如果不是按SN查询，或者未找到匹配的SN，则获取工单下的第一个SN
                    if not sn_info:
                        sn_info = session.query(Product).filter(
                            Product.work_order_id == wo.id
                        ).order_by(Product.id).first()
                        
                        if sn_info and query_type == 'serialNumber':
                            # 如果是按SN查询但前面未找到精确匹配，这里找到了工单的其他SN，也可以提供SN维度详情
                            detail_query_type = 'serial_number'
                            product_id_for_detail = sn_info.id
                    
                    # SN显示值
                    sn_display = sn_info.serial_no if sn_info else '-'
                    
                    # 构建记录数据
                    records_data.append({
                        'id': wo.id,
                        'order_no': wo.work_order_no,
                        'serial_number': sn_display,
                        'product_type': pt_name,
                        'status': wo.status,
                        'created_at': wo.created_at.strftime('%Y-%m-%d %H:%M:%S') if wo.created_at else '-',
                        'detail_query_type': detail_query_type,
                        'product_id_for_detail': product_id_for_detail
                    })
            
            # 计算总页数
            total_pages = (total_count + page_size - 1) // page_size if page_size > 0 else 0
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'records': records_data,
                'totalCount': total_count,
                'currentPage': page,
                'pageSize': page_size,
                'totalPages': total_pages
            })
            
    except Exception as e:
        logger.error(f"查询检验记录时发生错误: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'查询检验记录失败: {str(e)}'
        }), 500


@inspection_record_bp.route('/<int:record_id>/detail', methods=['GET'])
def get_inspection_record_detail(record_id):
    """
    获取检验记录详情
    支持按工单维度或SN维度查询详情
    """
    try:
        # 获取查询类型参数
        detail_type = request.args.get('detail_type', 'work_order')
        product_id = request.args.get('product_id', None, type=int)
        
        # 处理参数错误的情况
        if detail_type not in ['work_order', 'serial_number']:
            return jsonify({
                'success': False,
                'message': '无效的详情查询类型'
            }), 400
        
        if detail_type == 'serial_number' and not product_id:
            return jsonify({
                'success': False,
                'message': '按SN查询详情时必须提供product_id参数'
            }), 400
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 获取工单信息
            work_order = session.query(QualityInspectionWorkOrder).get(record_id)
            if not work_order:
                return jsonify({
                    'success': False,
                    'message': f'未找到ID为{record_id}的工单'
                }), 404
            
            # 获取产品类型信息
            product_type = session.query(ProductType).get(work_order.product_type_id)
            if not product_type:
                return jsonify({
                    'success': False,
                    'message': f'未找到工单关联的产品类型'
                }), 404
            
            # 初始化基本信息
            basic_info = {
                'order_no': work_order.work_order_no,
                'product_model': product_type.type_name,
                'created_at': work_order.created_at.strftime('%Y-%m-%d %H:%M:%S') if work_order.created_at else '-',
                'updated_at': work_order.updated_at.strftime('%Y-%m-%d %H:%M:%S') if work_order.updated_at else '-',
                'status': work_order.status,
                'is_rework': work_order.is_rework
            }
            
            # 如果是按SN查询，需要验证SN并添加到基本信息中
            if detail_type == 'serial_number':
                product = session.query(Product).get(product_id)
                if not product or product.work_order_id != record_id:
                    return jsonify({
                        'success': False,
                        'message': '指定的SN不属于该工单'
                    }), 400
                
                basic_info['serial_no_queried'] = product.serial_no
            
            # 获取检验阶段项目定义
            inspection_items = session.query(InspectionItem).filter(
                InspectionItem.product_type_id == work_order.product_type_id
            ).order_by(
                InspectionItem.stage,
                InspectionItem.display_order
            ).all()
            
            # 将检验项目按阶段分组
            items_by_stage = {}
            for item in inspection_items:
                if item.stage not in items_by_stage:
                    items_by_stage[item.stage] = []
                
                items_by_stage[item.stage].append({
                    'id': item.id,
                    'name': item.item_name,
                    'is_required': item.is_required
                })
            
            # 预定义的阶段和角色
            stages = ['assembly', 'test', 'packaging']
            roles = ['first', 'self', 'ipqc']
            
            # 初始化阶段数据
            stages_data = {}
            for stage in stages:
                stages_data[stage] = {}
                for role in roles:
                    stages_data[stage][role] = {
                        'is_completed': False,
                        'inspector': '-',
                        'inspection_time': '-',
                        'items': []
                    }
                    
                    # 当前阶段的标准检验项目
                    current_stage_items = items_by_stage.get(stage, [])
                    
                    # 按工单维度查询
                    if detail_type == 'work_order':
                        # 查询该工单在当前阶段和角色下最新的最终提交记录
                        final_submission = session.query(ProductInspectionStatus, Product.serial_no).join(
                            Product, ProductInspectionStatus.product_id == Product.id
                        ).filter(
                            ProductInspectionStatus.work_order_id == record_id,
                            ProductInspectionStatus.stage == stage,
                            ProductInspectionStatus.inspector_role == role,
                            ProductInspectionStatus.submission_type == 'final'
                        ).order_by(
                            desc(ProductInspectionStatus.inspection_time)
                        ).first()
                        
                        if final_submission:
                            status, sn = final_submission
                            stages_data[stage][role]['is_completed'] = True
                            stages_data[stage][role]['inspector'] = status.inspector
                            stages_data[stage][role]['inspection_time'] = status.inspection_time.strftime('%Y-%m-%d %H:%M:%S')
                            stages_data[stage][role]['completed_by_sn'] = sn
                            # 工单维度下，最终提交意味着所有检验项目都通过
                            stages_data[stage][role]['items'] = [
                                {'name': item['name'], 'passed': True}
                                for item in current_stage_items
                            ]
                        else:
                            # 查找是否有部分提交
                            partial_submission = session.query(ProductInspectionStatus, Product.serial_no).join(
                                Product, ProductInspectionStatus.product_id == Product.id
                            ).filter(
                                ProductInspectionStatus.work_order_id == record_id,
                                ProductInspectionStatus.stage == stage,
                                ProductInspectionStatus.inspector_role == role,
                                ProductInspectionStatus.submission_type == 'partial'
                            ).order_by(
                                desc(ProductInspectionStatus.inspection_time)
                            ).first()
                            
                            if partial_submission:
                                status, sn = partial_submission
                                stages_data[stage][role]['inspector'] = status.inspector
                                stages_data[stage][role]['inspection_time'] = status.inspection_time.strftime('%Y-%m-%d %H:%M:%S')
                                stages_data[stage][role]['completed_by_sn'] = f"(部分提交 by {sn})"
                            
                            # 无论是否有部分提交，项目都显示为未通过
                            stages_data[stage][role]['items'] = [
                                {'name': item['name'], 'passed': False}
                                for item in current_stage_items
                            ]
                    
                    # 按SN维度查询
                    else:
                        # 查询特定SN在当前阶段和角色下的检验状态
                        sn_status = session.query(ProductInspectionStatus).filter(
                            ProductInspectionStatus.product_id == product_id,
                            ProductInspectionStatus.stage == stage,
                            ProductInspectionStatus.inspector_role == role
                        ).first()
                        
                        if sn_status:
                            stages_data[stage][role]['inspector'] = sn_status.inspector
                            stages_data[stage][role]['inspection_time'] = sn_status.inspection_time.strftime('%Y-%m-%d %H:%M:%S')
                            
                            if sn_status.submission_type == 'final':
                                stages_data[stage][role]['is_completed'] = True
                                # SN维度下, 最终提交意味着所有项目通过
                                stages_data[stage][role]['items'] = [
                                    {'name': item['name'], 'passed': True}
                                    for item in current_stage_items
                                ]
                            else:  # partial
                                # 部分提交意味着该SN号已经通过了所有检验项目，只是整个阶段还没最终完成
                                stages_data[stage][role]['items'] = [
                                    {'name': item['name'], 'passed': True, 'note': '(部分提交)'}
                                    for item in current_stage_items
                                ]
                        else:
                            # 该SN在此阶段和角色下无检验记录
                            stages_data[stage][role]['items'] = [
                                {'name': item['name'], 'passed': False, 'note': '(未检验)'}
                                for item in current_stage_items
                            ]
            
            # 获取附件信息
            attachments = session.query(InspectionAttachment).filter(
                InspectionAttachment.work_order_id == record_id
            ).all()
            
            # 将附件按阶段和角色分组
            attachments_data = {}
            for attachment in attachments:
                if attachment.stage not in attachments_data:
                    attachments_data[attachment.stage] = {}
                
                if attachment.inspector_role not in attachments_data[attachment.stage]:
                    attachments_data[attachment.stage][attachment.inspector_role] = []
                
                # 生成附件URL
                attachment_url = url_for(
                    'quality_inspection.get_attachment_file',
                    attachment_id=attachment.id,
                    _external=True
                )
                
                attachments_data[attachment.stage][attachment.inspector_role].append({
                    'id': attachment.id,
                    'name': attachment.file_name,
                    'url': attachment_url,
                    'upload_time': attachment.upload_time.strftime('%Y-%m-%d %H:%M:%S') if attachment.upload_time else '-',
                    'file_type': attachment.file_type,
                    'file_size': attachment.file_size,
                    'uploaded_by': attachment.uploaded_by
                })
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'record': {
                    'basic_info': basic_info,
                    'stages': stages_data,
                    'attachments': attachments_data
                }
            })
            
    except Exception as e:
        logger.error(f"获取检验记录详情时发生错误: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取检验记录详情失败: {str(e)}'
        }), 500


@inspection_record_bp.route('/export', methods=['GET'])
def export_inspection_records():
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            # 提取查询参数 (与 search_inspection_records 类似)
            query_type = request.args.get('type', 'orderNo')
            query_value = request.args.get('value', '').strip()
            sort_field_request = request.args.get('sort_field', 'created_at')
            sort_order = request.args.get('sort_order', 'desc').lower()
            start_date_str = request.args.get('startDate')
            end_date_str = request.args.get('endDate')
            selected_records_str = request.args.get('selected_records') # JSON string of IDs
            
            # 新增：阶段和检验角色过滤参数
            stage_filter = request.args.get('stage', '').strip()
            inspector_role_filter = request.args.get('inspector_role', '').strip()

            # 构建基础查询
            # 如果有stage或inspector_role过滤条件，需要以Product为主表查询
            if stage_filter or inspector_role_filter:
                # 以Product为主表，连接相关表
                query = session.query(
                    Product,
                    QualityInspectionWorkOrder,
                    ProductType.type_name.label('product_type_name')
                ).join(
                    QualityInspectionWorkOrder,
                    Product.work_order_id == QualityInspectionWorkOrder.id
                ).join(
                    ProductType,
                    QualityInspectionWorkOrder.product_type_id == ProductType.id
                ).join(
                    ProductInspectionStatus,
                    Product.id == ProductInspectionStatus.product_id
                )
                
                # 应用阶段过滤
                if stage_filter:
                    query = query.filter(ProductInspectionStatus.stage == stage_filter)
                
                # 应用检验角色过滤
                if inspector_role_filter:
                    query = query.filter(ProductInspectionStatus.inspector_role == inspector_role_filter)
                
                # 使用distinct避免重复记录
                query = query.distinct()
                
            else:
                # 原有逻辑：以工单为主表查询
                query = session.query(
                    QualityInspectionWorkOrder,
                    ProductType.type_name.label('product_type_name')
                ).join(
                    ProductType,
                    QualityInspectionWorkOrder.product_type_id == ProductType.id
                )

            # 根据查询类型和值构建过滤条件
            if query_value:
                if query_type == 'orderNo':
                    # 按工单号查询 (模糊匹配)
                    query = query.filter(QualityInspectionWorkOrder.work_order_no.ilike(f"%{query_value}%"))
                elif query_type == 'serialNumber':
                    # 按SN号查询 (模糊匹配)
                    if stage_filter or inspector_role_filter:
                        # 如果是Product为主表的查询，直接过滤Product表
                        query = query.filter(Product.serial_no.ilike(f"%{query_value}%"))
                    else:
                        # 原有逻辑：以工单为主表，需要连接Product表
                        query = query.join(
                            Product, 
                            QualityInspectionWorkOrder.id == Product.work_order_id
                        ).filter(
                            Product.serial_no.ilike(f"%{query_value}%")
                        )

            # 日期范围过滤 (与 search_inspection_records 类似)
            if start_date_str:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                    query = query.filter(func.date(QualityInspectionWorkOrder.created_at) >= start_date)
                except ValueError:
                    logger.warning(f"导出时无效的开始日期格式: {start_date_str}")
            
            if end_date_str:
                try:
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                    query = query.filter(func.date(QualityInspectionWorkOrder.created_at) <= end_date)
                except ValueError:
                    logger.warning(f"导出时无效的结束日期格式: {end_date_str}")

            # 如果有选中的记录ID，则按ID筛选
            if selected_records_str:
                try:
                    selected_ids = json.loads(selected_records_str)
                    if selected_ids and isinstance(selected_ids, list):
                        # 确保ID是整数
                        selected_ids = [int(id_str) for id_str in selected_ids if str(id_str).isdigit()]
                        query = query.filter(QualityInspectionWorkOrder.id.in_(selected_ids))
                except json.JSONDecodeError:
                    logger.warning(f"解析选中记录ID失败: {selected_records_str}")
                except ValueError:
                    logger.warning(f"选中记录ID包含非整数值: {selected_records_str}")


            # 应用排序
            # 根据查询类型选择合适的排序字段映射
            if stage_filter or inspector_role_filter:
                # Product为主表的查询，排序字段映射
                sort_field_map = {
                    'order_no': QualityInspectionWorkOrder.work_order_no,
                    'serial_number': Product.serial_no,  # 新增SN号排序
                    'product_type': ProductType.type_name,
                    'status': QualityInspectionWorkOrder.status,
                    'created_at': QualityInspectionWorkOrder.created_at
                }
            else:
                # 工单为主表的查询，原有排序字段映射
                sort_field_map = {
                    'order_no': QualityInspectionWorkOrder.work_order_no,
                    'product_type': ProductType.type_name,
                    'status': QualityInspectionWorkOrder.status,
                    'created_at': QualityInspectionWorkOrder.created_at
                }
            
            sort_column = sort_field_map.get(sort_field_request, QualityInspectionWorkOrder.created_at)
            if sort_order == 'asc':
                query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(sort_column))
            
            # 执行查询获取所有匹配的记录 (不分页)
            results = query.all()
            
            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "检验记录"

            # 定义表头
            headers = [
                "工单ID", "工单号", "SN号 (首个)", "产品类型", "工单状态", "创建时间", "更新时间",
                # 您可以根据需要添加更多来自详情的列，例如各阶段的检验员、时间、结果等
                # 这需要更复杂的查询和数据处理逻辑
            ]
            ws.append(headers)

            # 设置表头样式
            header_font = Font(bold=True)
            for cell in ws[1]:
                cell.font = header_font
                cell.alignment = Alignment(horizontal="center", vertical="center")

            # 填充数据
            if stage_filter or inspector_role_filter:
                # 当有stage/inspector_role筛选时，结果结构是：(Product, QualityInspectionWorkOrder, product_type_name)
                for product, wo, pt_name in results:
                    row_data = [
                        wo.id,
                        wo.work_order_no,
                        product.serial_no,  # 直接使用筛选出的SN
                        pt_name,
                        wo.status,
                        wo.created_at.strftime('%Y-%m-%d %H:%M:%S') if wo.created_at else '-',
                        wo.updated_at.strftime('%Y-%m-%d %H:%M:%S') if wo.updated_at else '-',
                    ]
                    ws.append(row_data)
            else:
                # 原有逻辑：结果结构是：(QualityInspectionWorkOrder, product_type_name)
                for wo, pt_name in results:
                    # 获取工单的第一个SN号作为示例，实际导出可能需要更复杂的SN逻辑
                    first_product = session.query(Product.serial_no).filter(Product.work_order_id == wo.id).order_by(Product.id).first()
                    sn_display = first_product.serial_no if first_product else '-'

                    row_data = [
                        wo.id,
                        wo.work_order_no,
                        sn_display,
                        pt_name,
                        wo.status,
                        wo.created_at.strftime('%Y-%m-%d %H:%M:%S') if wo.created_at else '-',
                        wo.updated_at.strftime('%Y-%m-%d %H:%M:%S') if wo.updated_at else '-',
                    ]
                    ws.append(row_data)

            # 调整列宽 (示例)
            for col_idx, column_cells in enumerate(ws.columns):
                max_length = 0
                column = openpyxl.utils.get_column_letter(col_idx + 1)
                for cell in column_cells:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column].width = adjusted_width


            # 将工作簿保存到内存中的字节流
            excel_stream = BytesIO()
            wb.save(excel_stream)
            excel_stream.seek(0) # 重置流的指针到开始位置

            # 创建响应
            response = current_app.response_class(
                excel_stream.read(),
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                headers={
                    'Content-Disposition': 'attachment; filename="inspection_records_export.xlsx"',
                    "Access-Control-Expose-Headers": "Content-Disposition" # 允许前端JS访问此头
                }
            )
            return response

    except Exception as e:
        logger.error(f"导出检验记录时发生错误: {str(e)}", exc_info=True)
        # 对于API错误，返回JSON可能比HTML错误页面更好
        return jsonify({
            'success': False,
            'message': f'导出检验记录失败: {str(e)}'
        }), 500


def modify_check_work_order_completion():
    """
    提供修改过的工单完成状态检查函数代码
    注意：此函数仅作为参考，实际的修复已在quality_inspection.py中完成
    """
    # 以下是修改过的check_work_order_completion函数的代码
    def check_work_order_completion(session, work_order_id):
        """
        检查工单是否完成
        工单完成的定义：所有9个阶段-角色组合（3阶段 × 3角色）都至少有一个产品(SN)完成'final'提交
        不同阶段-角色组合的SN号可以不同，但每个组合都必须至少有一个SN完成final提交
        """
        work_order = session.query(QualityInspectionWorkOrder).get(work_order_id)
        if not work_order:
            logger.warning(f"工单 {work_order_id} 不存在，无法检查完成状态")
            return
            
        # 如果工单已是completed状态，无需再检查
        if work_order.status == 'completed':
            logger.info(f"工单 {work_order_id} 已标记为完成状态")
            return
            
        # 预定义的阶段和角色
        defined_stages = ['assembly', 'test', 'packaging']
        defined_roles = ['first', 'self', 'ipqc']
        
        # 查询所有最终提交记录，获取每个阶段-角色组合的完成情况
        final_submissions = session.query(
            ProductInspectionStatus.stage,
            ProductInspectionStatus.inspector_role,
            ProductInspectionStatus.product_id
        ).filter(
            ProductInspectionStatus.work_order_id == work_order_id,
            ProductInspectionStatus.submission_type == 'final'
        ).all()
        
        # 统计每个阶段-角色组合是否有final提交记录
        completed_combinations = set()
        for submission in final_submissions:
            stage = submission.stage
            role = submission.inspector_role
            # 将阶段-角色组合添加到已完成集合
            completed_combinations.add((stage, role))
        
        # 生成所有必需的阶段-角色组合
        all_required_combinations = set()
        for stage in defined_stages:
            for role in defined_roles:
                all_required_combinations.add((stage, role))
        
        # 检查所有必需的组合是否都已完成
        all_combinations_completed = all_required_combinations.issubset(completed_combinations)
        
        # 记录未完成的组合（用于调试）
        missing_combinations = all_required_combinations - completed_combinations
        if missing_combinations:
            logger.info(f"工单 {work_order_id} 未完成的阶段-角色组合: {missing_combinations}")
                
        # 如果所有组合都满足条件，标记工单为已完成
        if all_combinations_completed:
            if work_order.status != 'completed':
                work_order.status = 'completed'
                logger.info(f"工单 {work_order_id} 标记为已完成，所有9个阶段-角色组合都已完成") 
        else:
            logger.info(f"工单 {work_order_id} 未完成: 已完成 {len(completed_combinations)}/9 个组合") 