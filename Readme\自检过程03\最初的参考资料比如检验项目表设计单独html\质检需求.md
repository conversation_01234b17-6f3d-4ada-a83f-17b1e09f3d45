

我有两种产品：五代PLC和四代PLC，每种产品都有特定的检验项目。无论五代还是四代，每个工单的检验流程分为三大阶段：**组装前**、**测试前**和**包装前**。每个阶段的检验项目需由三类职责人员分别检测：**首检**、**自检**和**IPQC**。

#### 产品及检验项目概览
- **五代PLC** 和 **四代PLC** 的检验项目分别如下，每个阶段包含若干具体检查项。
- 检验项目在不同阶段和产品间部分重叠，但也有差异，需灵活支持。

#### 五代PLC检验项目
**组装前**  
1. 激光打标外壳型号/打标型号参数正确，符合工单要求。  
2. 激光打标面所有二维码能正常扫码，识别出的内容正确。  
3. 激光打标SN编码正确，不与历史记录重复。  
4. 激光打标SN字符内容与SN二维码内容比对一致。  
5. 外壳打标质量合格，丝印字符清晰，壳体无残缺/污渍。  
6. 覆膜型号、尺寸正确，无残缺/污渍。  
7. 覆膜色彩无差异，字符清晰可辨识。  
8. 灯盖丝印型号、尺寸正确，无残缺/污渍。  
9. 灯盖丝印色彩/透明度无差异，字符清晰可辨识。  
10. PCBA电路板型号与BOM一致。  
11. 接地片焊接牢固，无锈迹、氧化或污损。  
12. PCBA金手指干净无破损、无氧化、无污渍。  
13. 壳体卡扣全部正常卡住，无断裂脱落。  

**测试前**  
1. 数码管/LED能正常点亮。  
2. 外壳SN与PCBA模组正确绑定。  
3. 覆膜型号正确，粘贴可靠，无污损。  
4. 覆膜色彩无差异，字符清晰可辨识。  
5. 灯盖丝印型号正确，安装可靠，无污损。  
6. 灯盖丝印色彩/透明度无差异，字符清晰可辨识。  
7. 外壳锁扣全部正常卡住，无断裂脱落。  
8. 接地片无污损，正常露出，无卡住阻塞。  
9. 金手指连接器弹片无污损、变形、氧化。  
10. 产品整体外观无指纹/污渍，无划痕，无破损。  
11. 待测试产品标识正确，摆放/周转可靠，不易掉落。  

**包装前**  
1. 防尘贴/防尘塞/电池仓（电池）等正确粘贴/安装。  
2. 配件包内配件型号、数量正确。  
3. 成品灯盖/丝印/覆膜型号正确，安装粘贴合格。  
4. 金手指连接器弹片无污损、变形、氧化。  
5. 成品整体外观无指纹/污渍，表面无明显划痕。  
6. 蓝色锁扣正常卡住，蓝色滑扣下滑锁住。  
7. 壳体卡扣全部正常卡住，无断裂脱落。  
8. 铭牌标签内容参数正确，打印清晰，粘贴位置正确。  
9. 本工单待包装全部成品、配件检查合格。  
10. 待包装产品标识正确，摆放/周转可靠，不易掉落。  

#### 四代PLC检验项目
**组装前**  
1. 铭牌标签内容参数正确，打印清晰，粘贴位置正确。  
2. 铭牌标签所有二维码能正常扫码，识别出的内容正确。  
3. 铭牌标签SN编码正确，不与历史记录重复。  
4. 铭牌标签SN字符内容与SN二维码内容比对一致。  
5. 壳体丝印字符清晰，无残缺/污渍，无明显色差。  
6. 覆膜型号、尺寸正确，无残缺/污渍。  
7. 覆膜色彩无差异，字符清晰可辨识。  
8. 灯盖丝印型号、尺寸正确，无残缺/污渍。  
9. 灯盖丝印色彩无差异，字符清晰可辨识。  
10. PCBA电路板型号与BOM一致。  
11. 接地片焊接牢固，无锈迹、氧化或污损。  
12. 壳体卡扣全部正常卡住，无断裂脱落。  

**测试前**  
1. 数码管/LED能正常点亮。  
2. 外壳SN与PCBA模组正确绑定。  
3. 覆膜型号正确，粘贴可靠，无污损。  
4. 覆膜色彩无差异，字符清晰可辨识。  
5. 灯盖丝印型号正确，安装可靠，无污损。  
6. 灯盖丝印色彩无差异，字符清晰可辨识。  
7. 外壳锁扣全部正常卡住，无断裂脱落。  
8. 接地片无污损，正常露出，无卡住阻塞。  
9. 模块扩展接口能正常插拔，无堵塞。  
10. 产品整体外观无指纹/污渍，无划痕，无破损。  
11. 待测试产品标识正确，摆放/周转可靠，不易掉落。  

**包装前**  
1. 配件包内配件型号、数量正确。  
2. 产品灯盖/丝印/覆膜型号正确，安装粘贴合格。  
3. 模块扩展接口能正常插拔，无堵塞。  
4. 接地片无锈迹，无污损。  
5. 成品整体外观无指纹/污渍，表面无明显划痕。  
6. 橙色锁扣正常卡住，橙色滑扣在卡槽内，无脱落。  
7. 壳体卡扣全部正常卡住，无断裂脱落。  
8. 铭牌标签内容参数正确，打印清晰，粘贴位置正确。  
9. 本工单待包装全部成品、配件检查合格。  
10. 待包装产品标识正确，摆放/周转可靠，不易掉落。  

#### 需求要点
1. **检验流程**：每个工单的检验分为“组装前”、“测试前”、“包装前”三个阶段。
2. **职责分工**：每个阶段的每个检验项目需由“首检”、“自检”和“IPQC”三类人员分别检查。
3. **记录要求**：需记录每个工单在各阶段的检验结果，包括检查人员、时间和是否合格。



# PLC生产质量检验系统的业务逻辑详细分析

## 系统概述

当前的PLC生产质量检验系统是一个用于管理PLC产品生产过程中质量检验工作的应用。系统支持对不同类型的PLC产品（五代PLC和四代PLC）在生产的三个关键阶段（组装前、测试前、包装前）进行质量检验，并由三种不同角色（首检、自检、IPQC）共同完成检验工作。

## 核心业务功能

### 1. 工单管理

* **工单创建与查询**：
  * 用户可以输入工单号系统会在保存数据时自动创建
  * 已有工单会显示历史检验记录和附件

* **工单状态跟踪**：
  * 工单状态包括：待检验(pending)、检验中(processing)、已完成(completed)
  * 系统会根据检验完成情况自动更新工单状态

### 2. 检验项目管理

* **产品类型关联**：
  * 不同产品类型（五代PLC/四代PLC）有各自特定的检验项目
  * 通过下拉选择产品类型，系统动态加载对应检验项目

* **阶段性检验**：
  * 检验分为三个阶段：组装前、测试前、包装前
  * 系统按阶段组织显示检验项目，并支持阶段性展开

* **检验角色分工**：
  * 系统支持三种检验角色：首检、自检、IPQC
  * 每个角色都需要对同一阶段的检验项目进行检验

### 3. 检验数据记录

* **检验项目勾选**：
  * 用户可以选择/取消选择检验项目
  * 支持"全选"和"取消全选"操作

* **检验结果保存**：
  * 用户选择检验角色，勾选已检查通过的项目
  * 提交后，系统将结果保存到数据库中
  * 系统会记录检验人员和检验时间

* **附件上传管理**：
  * 在每个阶段的每个角色下，可以上传相关附件（图片或PDF）
  * 支持文件预览和删除
  * 附件与特定工单、阶段、检验角色关联

### 4. 自动化流程控制

* **阶段性展开**：
  * 默认只显示组装前阶段
  * 当组装前阶段完成后，自动展开测试前阶段
  * 当测试前阶段完成后，自动展开包装前阶段

* **检验完整性验证**：
  * 提交前验证必要信息（工单号、产品类型）
  * 检查是否至少完成了一个阶段的检验项目

## 数据流程

1. **初始化加载**：
   * 页面加载时， 通过API动态获取产品类型和检验项目并选择默认产品类型
   * 获取该产品类型的检验项目清单并按阶段分组展示

2. **检验数据提交流程**：
   * 选择检验角色→勾选已检查项目→点击"保存数据"
   * 前端构建数据对象→调用API保存检验记录→后端更新数据库
   * 保存成功后，根据情况自动展开下一阶段

4. **附件上传流程**：



## 技术实现特点

1. **动态加载与渲染**：
   * 通过API动态获取产品类型和检验项目
   * 根据数据动态渲染页面内容

2. **模块化设计**：
   * 功能按照不同模块划分（产品类型管理、检验项管理、附件管理等）
   * 各模块通过API交互，保持逻辑清晰


3. **用户体验优化**：
   * 提供文件预览功能
   * 状态反馈（加载中、成功、失败）
   * 自动展开下一阶段，引导用户完成整个流程

4. **防错设计**：
   * 输入验证和提交前数据检查
   * 清晰的错误提示
   * 操作确认（如保存前的检查）

## 业务规则与约束

1. **检验顺序**：检验流程遵循组装→测试→包装的顺序

2. **角色职责**：每个阶段需要三种角色（首检、自检、IPQC）共同完成检验

3. **项目关联**：检验项目与产品类型强关联，不同产品有不同检验要求

4. **数据完整性**：工单信息、检验记录和附件之间通过外键维护完整性关系

5. **唯一性约束**：同一工单的同一检验项目在同一角色下只能有一条检验记录

## 总结

PLC生产质量检验系统是一个专门为PLC产品生产质量管理设计的应用，通过严格的检验流程管理、角色分工和数据记录，确保产品在生产过程中的质量控制。系统采用现代化的Web技术实现，提供了灵活的配置能力、良好的用户体验和完整的数据管理功能，有效支持了生产线上的质量检验工作。
