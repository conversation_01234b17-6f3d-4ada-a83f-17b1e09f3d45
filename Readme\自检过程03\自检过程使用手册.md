# 生产工序检验页面使用手册

## 1. 引言

本手册旨在帮助用户理解和使用生产工序检验页面。该页面用于记录和管理产品在生产过程中（组装前、测试前、包装前）的各项检验结果，包括首检、自检和IPQC检验。

## 2. 页面概览

打开生产工序检验页面后，您将看到以下主要区域：

*   **顶部信息区**:
    *   **工单号**: 输入或扫描生产工单的编号。
    *   **产品型号**: 根据工单号自动填充。
    *   **产品类型**: 选择当前工单对应的产品类型。
    *   **返工工单**: 指示当前工单是否为返工单。
    *   **总体进度**: 显示当前工单所有检验项目的整体完成百分比。
    *   **工单检验状态**: (输入工单号后显示) 一个概览表格，展示各阶段各角色（首检、自检、IPQC）的提交状态、操作员及提交时间。

*   **时间线检验区**:
    *   以时间线的形式展示三个主要的检验阶段：**组装前阶段**、**测试前阶段**、**包装前阶段**。
    *   每个阶段卡片会显示该阶段的检验进度，并可以展开/折叠。

## 3. 功能与操作步骤

### 3.1. 开始检验流程

1.  **输入工单信息**:
    *   **工单号**:
        *   在"工单号"输入框中手动输入完整的工单编号。
        *   系统会自动尝试从工单管理系统加载工单信息。如果工单存在，"产品型号"等信息会自动填充。
        *   如果工单在质检系统中已存在记录，则会加载历史检验数据。
        *   如果工单在质检系统中不存在，系统会根据工单号前缀（如 "FG" 开头判断为返工）或下方"返工工单"下拉框的选择，准备创建新的质检记录。
    *   **产品型号**: 通常根据工单号自动从工单管理系统带出，无需手动输入。
    *   **产品类型**:
        *   点击下拉框，选择当前工单对应的正确产品类型（例如："五代PLC"）。这将决定后续加载哪些检验项目。
        *   系统通常会默认选择一个产品类型（如"五代PLC"），请根据实际情况确认或修改。
    *   **返工工单**:
        *   如果工单号以特定前缀（如"FG"）开头，系统可能会自动识别为返工工单并设置此选项为"是"。
        *   您也可以手动选择"是"或"否"。此选项会影响工单创建和后续某些逻辑的处理。
        *   选择为"是"后，工单信息旁边会显示"返工工单"的醒目提示。
    *   **刷新工单状态**: 点击工单号输入框旁边的"刷新"按钮，可以重新加载当前工单的最新检验状态和数据。

2.  **确认操作员信息**:
    *   在每个检验阶段的每个角色（首检、自检、IPQC）下方，会显示"操作人员"信息。
    *   系统会自动尝试获取当前登录用户的用户名并填充到此输入框。
    *   **在提交前，此信息是只读的。** 提交后，这里会显示实际提交人和提交时间。

### 3.2. 执行检验操作

1.  **选择检验阶段与角色**:
    *   页面中间以时间线展示了"组装前阶段"、"测试前阶段"、"包装前阶段"。
    *   初始状态下，通常只有第一个阶段（组装前阶段）是激活的，后续阶段会根据前一阶段的完成情况自动解锁（节点变为绿色对勾，卡片边框变色）。
    *   点击阶段卡片的头部（标题区域）可以展开或折叠该阶段的详细内容。已锁定的阶段无法展开。
    *   展开某个阶段后，您会看到三个选项卡："首检"、"自检"、"IPQC"。点击相应的选项卡，切换到该角色的检验项目列表。

2.  **勾选检验项目**:
    *   在选定的阶段和角色下，会列出所有需要检验的项目。
    *   对于每个项目，如果检验通过或确认为合格，请勾选项目前的复选框。
    *   **全选**: 点击检验项目列表上方的"全选"复选框，可以一次性勾选或取消勾选当前列表中的所有项目。
    *   **重置**: 在提交之前，如果需要清空当前角色下的所有勾选记录，可以点击下方的"重置"按钮。

### 3.3. 扫描产品SN号 (提交前关键步骤)

在您对检验项目进行了勾选，并准备提交时，系统需要知道这些检验结果是针对哪些具体产品的。

1.  **触发SN扫描**:
    *   当您点击"提交首检"、"提交自检"或"提交IPQC"按钮时，如果系统检测到您尚未选择任何产品SN号，会自动弹出"请扫描产品SN号"的界面。

2.  **扫描/输入SN号**:
    *   在弹出的SN扫描界面中，将光标定位在"扫描或输入SN号"的输入框。
    *   使用扫描枪扫描产品上的SN条码，或者手动输入SN号后按回车键（或点击"确认"按钮）。
    *   系统会验证SN号的有效性（例如，是否属于当前工单，对于返工工单的特殊处理等）。
        *   **普通工单**: 如果SN号是全新的，系统会自动将其添加到当前工单的产品列表中。如果SN号已存在于当前工单，则直接选中。
        *   **返工工单**: 通常要求SN号是已存在于某个工单的。如果SN号存在于其他工单，系统会将其作为返工产品添加到当前返工工单。如果SN号是全新的，系统也可能允许添加为新的返工产品。
    *   每成功扫描/确认一个SN号，它会显示在下方的"已扫描产品"列表中。返工产品会有"返工"标识。

3.  **管理已扫描列表**:
    *   列表中会显示已扫描产品的SN号和数量。
    *   点击SN号旁边的"×"按钮，可以从列表中移除该产品。
    *   点击"清空"按钮，并在确认提示后，可以移除列表中的所有产品。

4.  **开始检验**:
    *   当您扫描完所有需要本次提交检验的产品后，点击"开始检验"按钮。
    *   系统会将这些选中的产品SN号记录下来，并返回到之前的检验项目勾选界面。
    *   **注意**: 对于"自检"角色，系统可能会提示您当前工单还有未扫描的产品，并询问是否继续。自检通常需要检验工单下的所有产品。对于"首检"和"IPQC"，通常允许抽检。

### 3.4. 提交检验结果

当检验项目已勾选，并且相关的产品SN也已通过扫描界面选定后，您可以提交检验结果。

1.  **点击提交按钮**: 点击对应角色下方的"提交首检"、"提交自检"或"提交IPQC"按钮。

2.  **选择提交方式**: 系统会弹出对话框，让您选择提交方式：
    *   **部分提交 (仅提交已扫描的产品)**:
        *   此选项会将当前勾选的检验项目结果，应用到您本次通过SN扫描界面选择的那些产品上。
        *   提交后，您可以继续扫描其他产品，并再次进行提交（可以是部分提交或最终提交）。
        *   此选项适用于一个批次的产品分多次检验提交的场景。
    *   **最终提交 (完成产品检验)**:
        *   此选项表示当前阶段、当前角色的检验工作已全部完成。
        *   系统会将当前勾选的检验项目结果，同样应用到您本次通过SN扫描界面选择的那些产品上。
        *   **重要**: 一旦选择最终提交，当前阶段的当前角色将被标记为"已完成"，之后不能再为这个角色添加新的产品检验记录或修改提交状态。所有未在本次或之前部分提交中包含的产品，将不会被记录为已完成此阶段此角色的检验。
        *   最终提交前，系统通常会有二次确认提示，请仔细阅读。

3.  **提交后**:
    *   系统会将数据保存到后端。
    *   界面会更新：
        *   对应角色的操作员信息区域会显示提交人、提交时间，并标记为"已提交"。
        *   检验项目变为只读（不可勾选/取消）。
        *   "重置"和"提交"按钮会被禁用或替换为"已完成"状态显示。
        *   总体进度和阶段进度会更新。
        *   如果一个阶段的所有角色（首检、自检、IPQC）都已完成"最终提交"，该阶段在时间线上会标记为完成（例如，节点变为绿色对勾），并且下一个检验阶段会自动解锁。
        *   工单检验状态概览表格会更新。

### 3.5. 管理附件

您可以为每个检验阶段的每个角色上传相关的证明文件或图片。

1.  **打开上传对话框**:
    *   在每个角色的操作区域，点击"上传附件"按钮（在旧版界面中，可能先点击"提交"按钮旁的"上传附件"按钮，然后选择文件）。
    *   系统会弹出"上传检验附件"对话框。

2.  **选择文件**:
    *   在对话框中，点击"选择文件"按钮，从您的电脑中选择需要上传的文件。
    *   支持的文件类型通常包括：图片 (JPG, PNG, GIF, BMP, WEBP) 和 PDF 文档。
    *   文件大小通常限制在 10MB 以内。
    *   选择文件后，文件会出现在"待上传文件"列表中。

3.  **管理待上传文件**:
    *   您可以多次选择文件，将多个文件加入待上传列表。
    *   点击文件名可以尝试预览（图片和PDF）。
    *   点击文件旁的"×"可以从待上传列表中移除。

4.  **上传文件**:
    *   确认待上传列表中的文件无误后，点击对话框右下角的"完成"按钮。
    *   系统会提示您确认上传，因为上传后的附件可能不可删除（尤其是在检验已提交后）。
    *   确认后，文件将被上传到服务器，并与当前的工单、阶段、角色关联。

5.  **查看和管理已上传文件**:
    *   上传成功后，文件会显示在对应角色检验项目下方的"已上传文件"区域，也会显示在"上传检验附件"对话框的"已上传文件"列表中。
    *   您可以点击文件名预览已上传的文件。
    *   **删除附件**:
        *   如果某个角色的检验**尚未提交**，并且附件本身没有被特殊标记为不可删除，那么在"上传检验附件"对话框的"已上传文件"列表中，可删除的附件旁会显示"×"按钮。点击并确认后即可删除。
        *   如果角色的检验**已经提交**，或者附件被系统标记为不可删除，则无法删除该附件。

### 3.6. 监控进度与状态

*   **总体进度条**: 页面顶部的进度条显示整个工单所有检验（所有阶段、所有角色、所有项目）的完成百分比。
*   **阶段进度**: 每个阶段卡片上会显示该阶段（包含其下的首检、自检、IPQC所有项目）的完成百分比。
*   **工单检验状态概览**: 页面顶部（输入工单号后）的表格会清晰展示每个阶段下，首检、自检、IPQC分别由谁在什么时间提交完成，或者显示为未完成。
*   **产品实时检验状态**: 在每个阶段的每个角色选项卡内部，会有一个区域（通常在操作员信息下方）动态显示当前工单下所有产品的检验状态列表。此列表会定时自动刷新，内容包括：
    *   产品总数、已完成数、未完成数。
    *   每个产品的SN号。
    *   该产品是否已完成当前阶段当前角色的检验，以及检验员是谁。
    *   如果当前角色已"最终提交"，此处会提示此阶段检验已完成最终提交。如果只是"部分提交"，会提示已部分提交，可继续扫描添加。

## 4. 特殊说明

*   **返工工单**: 返工工单的处理流程与普通工单基本一致，但在SN号验证、产品添加等方面可能有特殊逻辑（例如，通常关联已存在的SN号）。界面上会有明确的"返工工单"标识。
*   **通知与提示**: 系统会在关键操作后（如加载成功/失败、提交成功/失败、上传成功/失败等）通过弹窗给出反馈信息。请注意阅读这些提示。
*   **自动刷新**:
    *   产品实时检验状态列表会每隔较短时间（如10秒）自动刷新。
    *   整个工单的检验数据也会每隔较长时间（如60秒）在后台静默刷新，以确保您看到的是最新状态。

## 5. 总结

生产工序检验页面提供了一个全面的解决方案，用于记录、跟踪和管理生产过程中的质量检验数据。通过理解并遵循本手册中的操作步骤，用户可以高效、准确地完成各项检验任务。如有疑问，请联系系统管理员。 