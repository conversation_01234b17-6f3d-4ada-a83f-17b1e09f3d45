<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品固件版本管理</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- html2canvas -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .search-bar {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .search-input {
            flex: 1;
            max-width: 50%;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        .status-active { color: #67C23A; }
        .status-pending { color: #E6A23C; }
        .status-rejected { color: #F56C6C; }
        .status-obsolete { color: #F56C6C; }
        .product-tag {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .el-table {
            margin-top: 15px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        .el-tabs__content {
            padding: 15px;
            background-color: #fff;
            border-radius: 4px;
        }
        .developer-row {
            display: flex;
            gap: 20px;
        }
        .version-col {
            flex: 0 0 20%;
        }
        .time-col {
            flex: 0 0 45%;
        }
        .download-col {
            flex: 0 0 10%;
        }
        .developer-row .el-form-item { flex: 1; }
        .file-upload-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .upload-tip {
            color: #999;
            font-size: 12px;
            line-height: 1;
            margin-left: 10px;
            white-space: nowrap;
        }
        .version-history-dialog .el-dialog__body {
            max-height: 600px;
            overflow-y: auto;
            padding: 0 20px;
        }
        .version-timeline {
            padding: 10px;
        }
        .version-timeline-item__content {
            line-height: 1;
        }
        .export-image-button {
            position: absolute;
            top: 15px;
            right: 20px;
            z-index: 10;
        }
        .version-timeline-item__content .el-tag {
            margin-bottom: 4px;
        }
        .version-timeline-item__content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 10px;
            border: 1px solid #ebeef5;
            background-color: #f9f9f9;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .left-section, .right-section {
            flex: 1;
            min-width: 250px;
            padding: 5px;
            line-height: 1;
        }
        .right-section {
            margin-left: 20px;
            border-left: 1px solid #eee;
            padding-left: 20px;
        }
        .right-section .products,
        .right-section .description {
            margin-bottom: 10px;
        }
        .divider {
            width: 100%;
            height: 1px;
            background-color: #ebeef5;
            margin: 10px 0;
        }
        .version-history-dialog .status-label-large {
            font-size: 15px !important;
            font-weight: bold;
            padding: 0px 20px;
        }
        .section-title {
            text-align: center;
            margin: 5px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="header">
            <h3>产品固件版管理</h3>
            <el-dropdown @command="handleCommand">
                <span class="el-dropdown-link">
                    {{ currentUser.name }}<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </span>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
        
        <el-tabs v-model="activeTab">
            <!-- 所有固件页 -->
            <el-tab-pane label="所有固件" name="all">
                <div class="search-bar">
                    <el-input
                        class="search-input"
                        placeholder="请输入ERP流水号、固件名称或适用产品进行搜索"
                        v-model="searchQuery"
                        clearable
                        @clear="handleSearchClear"
                        @keyup.enter="handleSearch">
                        <template #append>
                            <el-button icon="Search" @click="handleSearch"></el-button>
                        </template>
                    </el-input>
                    <div class="action-buttons">
                        <el-button type="primary" @click="showUploadDialog">上传新固件</el-button>
                        <el-button type="success" @click="exportToExcel('all')">导出数据</el-button>
                        <span style="color: #5555ff; font-size: 12px; margin-left: 15px; line-height: 32px;">
                            （在本页双击版本号，可查看版本历史）
                        </span>
                    </div>
                </div>
                
                <el-table 
                    :data="filteredFirmwareList" 
                    style="width: 100%"
                    border
                    @sort-change="handleSortChange">
                    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                    <el-table-column prop="serialNumber" label="ERP流水号" width="150" sortable="custom"></el-table-column>
                    <el-table-column prop="name" label="固件名称" width="150" sortable="custom"></el-table-column>
                    <el-table-column prop="version" label="版本号" width="120" sortable="custom">
                        <template #default="scope">
                            <span 
                                @dblclick="showVersionHistory(scope.row)"
                                :style="{ color: getRowFontColor(scope.row) }"
                                style="cursor: pointer;">
                                {{ scope.row.version }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="100" sortable="custom">
                        <template #default="scope">
                            <span :class="'status-' + scope.row.status">
                                {{ statusMap[scope.row.status] }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="products" label="适用产品" width="180" sortable="custom">
                        <template #default="scope">
                            {{ scope.row.products.map(p => p.model).join(', ') }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="versionRequirements" label="版本使用要求" width="200"></el-table-column>
                    <el-table-column prop="description" label="变更内容" width="200"></el-table-column>
                    <el-table-column prop="developer" label="研发者" width="120" sortable="custom"></el-table-column>
                    <el-table-column prop="downloadCount" label="下载次数" width="100" sortable="custom"></el-table-column>
                    <el-table-column prop="usageCount" label="使用次数" width="100" sortable="custom"></el-table-column>
                    <el-table-column prop="approveTime" label="生效时间" width="180" sortable="custom"></el-table-column>
                    <el-table-column prop="approver" label="审核者" width="120" sortable="custom"></el-table-column>
                    <el-table-column prop="uploadTime" label="上传时间" width="180" sortable="custom"></el-table-column>
                    <el-table-column prop="uploader" label="上传者" width="120" sortable="custom"></el-table-column>
                    <el-table-column label="操作" width="180" fixed="right">
                        <template #default="scope">
                            <el-button size="small" type="primary" @click="downloadFirmware(scope.row)">下载</el-button>
                            <el-button 
                                v-if="scope.row.status === 'rejected'" 
                                size="small" 
                                type="info" 
                                @click="showEditDialog(scope.row)">修改</el-button>
                            <el-button 
                                v-else 
                                size="small" 
                                type="warning" 
                                @click="showUpdateDialog(scope.row)">更新版本</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            
            <!-- 待审核页 -->
            <el-tab-pane label="待审核" name="pending" v-if="currentUser.role === 'admin'">
                <el-table :data="pendingFirmwareList" style="width: 100%" border>
                    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                    <el-table-column prop="source" label="类型" width="100"></el-table-column>
                    <el-table-column prop="oldSerialNumber" label="旧ERP流水号" width="150">
                        <template #default="scope">
                            {{ scope.row.oldSerialNumber || '无' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="serialNumber" label="ERP流水号" width="150"></el-table-column>
                    <el-table-column prop="name" label="固件名称" width="150"></el-table-column>
                    <el-table-column prop="version" label="版本号" width="120">
                        <template #default="scope">
                            <span @dblclick="showPendingVersionDetails(scope.row)" style="cursor: pointer; color: #f4a300;">
                                {{ scope.row.version }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="100">
                        <template #default="scope">
                            <span :class="'status-' + scope.row.status">
                                {{ statusMap[scope.row.status] }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="products" label="适用产品" width="180">
                        <template #default="scope">
                            {{ scope.row.products?.length > 0 
                                ? scope.row.products.map(p => p.model).join(', ') 
                                : '暂无' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="versionRequirements" label="版本使用要求" width="200"></el-table-column>
                    <el-table-column prop="description" label="变更内容" width="200"></el-table-column>
                    <el-table-column prop="developer" label="研发者" width="120"></el-table-column>
                    <el-table-column prop="uploadTime" label="上传时间" width="180"></el-table-column>
                    <el-table-column prop="uploader" label="上传者" width="120"></el-table-column>
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="scope">
                            <el-button size="small" type="danger" @click="rejectFirmware(scope.row)">拒绝</el-button>
                            <el-button size="small" type="success" @click="approveFirmware(scope.row)">通过</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            
            <!-- 使用记录页 -->
            <el-tab-pane label="使用记录" name="downloads">
                <div class="search-bar">
                    <el-input
                        class="search-input"
                        placeholder="请输入工单号、产品编码/型号、备注或研发者进行搜索"
                        v-model="downloadSearchQuery"
                        clearable
                        @clear="handleDownloadSearchClear"
                        @keyup.enter="handleDownloadSearch">
                        <template #append>
                            <el-button icon="Search" @click="handleDownloadSearch"></el-button>
                        </template>
                    </el-input>
                    <div class="action-buttons">
                        <el-button type="success" @click="exportToExcel('downloads')">导出数据</el-button>
                        <span style="color: #5555ff; font-size: 12px; margin-left: 15px; line-height: 32px;">
                            （在本页双击工单号，查看下载版本详情）
                        </span>
                    </div>
                </div>
                <el-table :data="filteredDownloadRecords" style="width: 100%" border>
                    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                    <el-table-column prop="serialNumber" label="ERP流水号" width="150"></el-table-column>
                    <el-table-column prop="workOrder" label="工单号" width="150">
                        <template #default="scope">
                            <span 
                                @dblclick="showDownloadDetail(scope.row)"
                                style="cursor: pointer; color: #5555ff;">
                                {{ scope.row.workOrder }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="productCode" label="产品编码" width="150"></el-table-column>
                    <el-table-column prop="productModel" label="产品型号" width="150"></el-table-column>
                    <el-table-column prop="productionCount" label="生产数量" width="100"></el-table-column>
                    <el-table-column prop="softwareVersion" label="软件版本" width="120"></el-table-column>
                    <el-table-column prop="buildTime" label="构建时间" width="180"></el-table-column>
                    <el-table-column prop="backplaneVersion" label="背板总线版本" width="150"></el-table-column>
                    <el-table-column prop="highSpeedIOVersion" label="高速IO版本" width="150"></el-table-column>
                    <el-table-column prop="usageTime" label="使用时间" width="180"></el-table-column>
                    <el-table-column prop="usageUser" label="使用人" width="120"></el-table-column>
                    <el-table-column prop="notes" label="备注"></el-table-column>
                </el-table>
            </el-tab-pane>
            
            <!-- 作废版本页 -->
            <el-tab-pane label="作废版本" name="obsolete">
                <div class="search-bar">
                    <el-input
                        class="search-input"
                        placeholder="请输入ERP流水号、固件名称或适用产品进行搜索"
                        v-model="obsoleteSearchQuery"
                        clearable
                        @clear="handleObsoleteSearchClear"
                        @keyup.enter="handleObsoleteSearch">
                        <template #append>
                            <el-button icon="Search" @click="handleObsoleteSearch"></el-button>
                        </template>
                    </el-input>
                    <div class="action-buttons">
                        <el-button type="success" @click="exportToExcel('obsolete')">导出数据</el-button>
                    </div>
                </div>
                <el-table 
                    :data="filteredObsoleteFirmwareList" 
                    style="width: 100%"
                    border>
                    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                    <el-table-column prop="serialNumber" label="ERP流水号" width="150"></el-table-column>
                    <el-table-column prop="name" label="固件名称" width="150"></el-table-column>
                    <el-table-column prop="version" label="版本号" width="120"></el-table-column>
                    <el-table-column prop="status" label="状态" width="100">
                        <template #default="scope">
                            <span :class="'status-' + scope.row.status">
                                {{ statusMap[scope.row.status] }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="products" label="适用产品" width="180">
                        <template #default="scope">
                            {{ scope.row.products.map(p => p.model).join(', ') }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="versionRequirements" label="版本使用要求" width="200"></el-table-column>
                    <el-table-column prop="description" label="变更内容" width="200"></el-table-column>
                    <el-table-column prop="developer" label="研发者" width="120"></el-table-column>
                    <el-table-column prop="usageCount" label="使用次数" width="100"></el-table-column>
                    <el-table-column prop="approveTime" label="生效时间" width="180"></el-table-column>
                    <el-table-column prop="approver" label="审核者" width="120"></el-table-column>
                    <el-table-column prop="uploadTime" label="上传时间" width="180"></el-table-column>
                    <el-table-column prop="uploader" label="上传者" width="120"></el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>

        <!-- 下载记录详情弹窗 -->
        <el-dialog
            title="工单固件下载详情"
            v-model="downloadDetailDialogVisible"
            width="60%"
            class="version-history-dialog">
            <el-button 
                class="export-image-button" 
                type="primary"
                @click="exportDownloadDetailAsImage">
                导出图片
            </el-button>
            
            <div class="version-timeline-item__content" ref="downloadDetailContent">
                <div class="left-section">
                    <el-tag type="success" size="large" class="status-label-large">已下载</el-tag>
                    <div class="section-title">工单信息</div>
                    <div class="divider"></div>
                    <p><strong>工单号：</strong>{{ downloadDetails.workOrder }}</p>
                    <p><strong>产品编码：</strong>{{ downloadDetails.productCode }}</p>
                    <p><strong>产品型号：</strong>{{ downloadDetails.productModel }}</p>
                    <p><strong>烧录数量：</strong>{{ downloadDetails.burnCount }}</p>
                    <p><strong>备注：</strong>{{ downloadDetails.notes }}</p>
                    <p><strong>下载时间：</strong>{{ downloadDetails.downloadTime }}</p>
                    <p><strong>下载人：</strong>{{ downloadDetails.downloader }}</p>
                </div>
                <div class="right-section">
                    <div class="section-title">固件信息</div>
                    <div class="divider"></div>
                    <p><strong>ERP流水号：</strong>{{ downloadDetails.serialNumber }}</p>
                    <p><strong>固件名称：</strong>{{ downloadDetails.firmwareName }}</p>
                    <p><strong>版本号：</strong>{{ downloadDetails.firmwareVersion }}</p>
                    <p><strong>适用产品：</strong>
                        <span v-if="downloadDetails.products && downloadDetails.products.length">
                            {{ downloadDetails.products.map(p => p.model).join(' / ') }}
                        </span>
                        <span v-else>暂无</span>
                    </p>
                    <p><strong>生效时间：</strong>{{ downloadDetails.approveTime || '-' }}</p>
                    <p><strong>版本使用要求：</strong>{{ downloadDetails.versionRequirements }}</p>
                    <p><strong>研发者：</strong>{{ downloadDetails.developer }}</p>
                </div>
            </div>
        </el-dialog>

        <!-- 上传新固件对话框 -->
        <el-dialog 
            :title="uploadForm.editId ? '修改固件信息' : '上传新固件'" 
            v-model="uploadDialogVisible" 
            width="50%">
            <el-form 
                :model="uploadForm" 
                :rules="uploadRules" 
                ref="uploadFormRef" 
                label-width="120px">
                <div class="developer-row">
                    <el-form-item label="ERP流水号" prop="serialNumber">
                        <el-input v-model="uploadForm.serialNumber" placeholder="请输入ERP流水号"></el-input>
                    </el-form-item>
                    <el-form-item label="研发者" prop="developer">
                        <el-input v-model="uploadForm.developer" placeholder="请输入研发者姓名"></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="固件名称" prop="name">
                    <el-input v-model="uploadForm.name" placeholder="请输入固件名称"></el-input>
                </el-form-item>
                <el-form-item label="版本号" prop="version">
                    <el-input v-model="uploadForm.version" placeholder="请输入版本号"></el-input>
                </el-form-item>
                <el-form-item label="适用产品" prop="productsText">
                    <el-input 
                        type="textarea" 
                        v-model="uploadForm.productsText" 
                        placeholder="请输入适用产品，多个产品用逗号分隔"
                        :rows="3">
                    </el-input>
                </el-form-item>
                <el-form-item label="变更内容" prop="description">
                    <el-input 
                        type="textarea" 
                        v-model="uploadForm.description" 
                        placeholder="请输入变更内容信息"
                        :rows="3">
                    </el-input>
                </el-form-item>
                <el-form-item label="版本使用要求" prop="versionRequirements">
                    <el-input 
                        type="textarea" 
                        v-model="uploadForm.versionRequirements" 
                        :rows="3" 
                        placeholder="库存处理：不涉及/不处理/返工&#10;在制处理：不涉及/不处理/返工&#10;已售处理：不涉及/不召回/召回返工">
                    </el-input>
                </el-form-item>
                <el-form-item label="固件文件" prop="file">
                    <el-upload
                        class="upload-demo"
                        action=""
                        :on-change="handleFileChange"
                        :auto-upload="false"
                        :file-list="fileList">
                        <div class="file-upload-container">
                            <el-button size="small" type="primary">选择文件</el-button>
                            <span class="upload-tip">（注：只能上传单个文件，若多个文件，打包压缩后上传）</span>
                        </div>
                        <template #tip>
                            <div class="el-upload__tip">请上传固件文件，大小不超过50MB</div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="uploadDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitUpload">
                        {{ uploadForm.editId ? '确认修改' : '提交审核' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 下载固件对话框 -->
        <el-dialog title="下载固件" v-model="downloadDialogVisible" width="50%">
            <el-form 
                :model="downloadForm" 
                :rules="downloadRules" 
                ref="downloadFormRef" 
                label-width="120px">
                <div class="developer-row">
                    <el-form-item label="ERP流水号">
                        <el-input v-model="downloadForm.serialNumber" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="研发者">
                        <el-input v-model="downloadForm.developer" disabled></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="固件名称">
                    <el-input v-model="downloadForm.firmwareName" disabled></el-input>
                </el-form-item>
                <div class="developer-row">
                    <el-form-item label="版本号" label-width="120px">
                        <el-input v-model="downloadForm.firmwareVersion" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="生效时间" label-width="120px">
                        <el-input v-model="downloadForm.approveTime" disabled></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="适用产品">
                    <el-input :model-value="downloadForm.products.map(p => p.model).join(', ')" disabled></el-input>
                </el-form-item>
                <el-form-item label="版本使用要求">
                    <el-input v-model="downloadForm.versionRequirements" disabled></el-input>
                </el-form-item>
                <el-form-item label="工单号" prop="workOrder">
                    <el-input v-model="downloadForm.workOrder" placeholder="请输入工单编号"></el-input>
                </el-form-item>
                <el-form-item label="产品编码" prop="productCode">
                    <el-input v-model="downloadForm.productCode" placeholder="请输入产品编码"></el-input>
                </el-form-item>
                <el-form-item label="产品型号" prop="productModel">
                    <el-input v-model="downloadForm.productModel" placeholder="请输入产品型号"></el-input>
                </el-form-item>
                <el-form-item label="生产数量" prop="burnCount">
                    <el-input v-model="downloadForm.burnCount" placeholder="请输入生产数量" type="number"></el-input>
                </el-form-item>
                <el-form-item label="软件版本" prop="softwareVersion">
                    <el-input v-model="downloadForm.softwareVersion" placeholder="请输入软件版本"></el-input>
                </el-form-item>
                <el-form-item label="构建时间" prop="buildTime">
                    <el-date-picker
                        v-model="downloadForm.buildTime"
                        type="datetime"
                        placeholder="请选择构建时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="背板总线版本" prop="backplaneVersion">
                    <el-input v-model="downloadForm.backplaneVersion" placeholder="请输入背板总线版本"></el-input>
                </el-form-item>
                <el-form-item label="高速IO版本" prop="highSpeedIOVersion">
                    <el-input v-model="downloadForm.highSpeedIOVersion" placeholder="请输入高速IO版本"></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="notes">
                    <el-input type="textarea" v-model="downloadForm.notes" :rows="3" placeholder="请输入备注信息"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="downloadDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmDownload">确认下载</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 更新固件对话框 -->
        <el-dialog title="更新固件版本" v-model="updateDialogVisible" width="50%">
            <el-form 
                :model="updateForm" 
                :rules="updateRules" 
                ref="updateFormRef" 
                label-width="120px">
                <div class="developer-row">
                    <el-form-item label="原ERP流水号">
                        <el-input v-model="updateForm.currentSerialNumber" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="原研发者">
                        <el-input v-model="updateForm.currentDeveloper" disabled></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="原固件名称">
                    <el-input v-model="updateForm.currentName" disabled></el-input>
                </el-form-item>
                <div class="developer-row">
                    <el-form-item label="原版本号" label-width="120px" class="version-col">
                        <el-input v-model="updateForm.currentVersion" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="生效时间" label-width="75px" class="time-col">
                        <el-input v-model="updateForm.currentApproveTime" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="下载次数" label-width="75px" class="download-col">
                        <el-input v-model="updateForm.currentDownloadCount" disabled></el-input>
                    </el-form-item>
                </div>
                <div class="developer-row">
                    <el-form-item label="新ERP流水号" prop="newSerialNumber">
                        <el-input v-model="updateForm.newSerialNumber" placeholder="请输入新ERP流水号"></el-input>
                    </el-form-item>
                    <el-form-item label="新研发者" prop="newDeveloper">
                        <el-input v-model="updateForm.newDeveloper" placeholder="请输入研发者姓名"></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="新固件名称" prop="newName">
                    <el-input v-model="updateForm.newName" placeholder="请输入新固件名称"></el-input>
                </el-form-item>
                <el-form-item label="新版本号" prop="newVersion">
                    <el-input v-model="updateForm.newVersion" placeholder="请输入新版本号"></el-input>
                </el-form-item>
                <el-form-item label="适用产品" prop="newProductsText">
                    <el-input 
                        type="textarea" 
                        v-model="updateForm.newProductsText" 
                        placeholder="请输入适用产品，多个产品用逗号隔开"
                        :rows="3">
                    </el-input>
                </el-form-item>
                <el-form-item label="版本使用要求" prop="versionRequirements">
                    <el-input 
                        type="textarea" 
                        v-model="updateForm.versionRequirements" 
                        :rows="3" 
                        placeholder="库存处理：不涉及/不处理/返工&#10;在制处理：不涉及/不处理/返工&#10;已售处理：不涉及/不召回/召回返工">
                    </el-input>
                </el-form-item>
                <el-form-item label="更新日志" prop="description">
                    <el-input 
                        type="textarea" 
                        v-model="updateForm.description" 
                        placeholder="请输入更新日志信息"
                        :rows="3">
                    </el-input>
                </el-form-item>
                <el-form-item label="固件文件" prop="file">
                    <el-upload
                        class="upload-demo"
                        action=""
                        :on-change="handleUpdateFileChange"
                        :auto-upload="false"
                        :file-list="updateFileList">
                        <el-button size="small" type="primary">选择文件</el-button>
                        <span class="upload-tip">（注：只能上传单个文件，若多个文件，打包压缩后上传）</span>
                        <template #tip>
                            <div class="el-upload__tip">请上传新版本固件文件</div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="updateDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitUpdate">提交更新</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 待审核详情对话框 -->
        <el-dialog 
            title="待审核版本详情"
            v-model="pendingVersionDialogVisible" 
            width="50%" 
            class="version-history-dialog">
            
            <el-button 
                class="export-image-button" 
                type="primary" 
                @click="exportPendingVersionAsImage">
                导出图片
            </el-button>
            
            <div class="version-timeline-item__content" ref="pendingVersionContent">
                <div class="left-section">
                    <el-tag
                        type="warning" 
                        size="large"
                        class="status-label-large">
                        待审核
                    </el-tag>
                    
                    <p><strong>ERP流水号：</strong>{{ pendingDetails.serialNumber }}</p>
                    <p><strong>固件名称：</strong>{{ pendingDetails.name }}</p>
                    <p><strong>版本号：</strong>{{ pendingDetails.version }}</p>
                    <p><strong>研发者：</strong>{{ pendingDetails.developer }}</p>
                    <p><strong>上传时间：</strong>{{ pendingDetails.uploadTime }}</p>
                    <p><strong>上传者：</strong>{{ pendingDetails.uploader }}</p>
                </div>

                <div class="right-section">
                    <div class="products">
                        <strong>适用产品：</strong>
                        <span v-if="pendingDetails.products && pendingDetails.products.length">
                            {{ pendingDetails.products.map(p => p.model).join(' / ') }}
                        </span>
                        <span v-else>暂无</span>
                    </div>
                    <div class="divider"></div>

                    <div class="requirements">
                        <strong>版本使用要求：</strong>
                        <span v-if="pendingDetails.versionRequirements">
                            {{ pendingDetails.versionRequirements }}
                        </span>
                        <span v-else>暂无</span>
                    </div>
                    <div class="divider"></div>

                    <div class="description">
                        <strong>变更内容：</strong>
                        <span v-if="pendingDetails.description">
                            {{ pendingDetails.description }}
                        </span>
                        <span v-else>暂无</span>
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 版本历史对话框 -->
        <el-dialog title="版本历史" v-model="versionHistoryDialogVisible" width="50%" class="version-history-dialog">
            <el-button 
                class="export-image-button" 
                type="primary" 
                @click="exportVersionHistoryAsImage">
                导出图片
            </el-button>
            
            <el-timeline class="version-timeline">
                <el-timeline-item 
                    v-for="version in versionHistory" 
                    :key="version.id"
                    :timestamp="version.uploadTime"
                    placement="top"
                    :color="statusColorMap[version.status]">
                    
                    <div class="version-timeline-item__content">
                        <div class="left-section">
                            <el-tag
                                :type="statusTagType(version.status)" 
                                size="large"
                                :class="{ 'status-label-large': ['active', 'obsolete'].includes(version.status) }">
                                {{ statusMap[version.status] }}
                            </el-tag>
                            <p><strong>ERP流水号：</strong>{{ version.serialNumber }}</p>
                            <p><strong>固件名称：</strong>{{ version.name }}</p>
                            <p><strong>版本号：</strong>{{ version.version }}</p>
                            <p><strong>研发者：</strong>{{ version.developer }}</p>
                            <p><strong>生效时间：</strong>{{ formatDate(version.approveTime) }}</p>
                            <p v-if="version.status === 'obsolete'">
                                <strong>作废时间：</strong>{{ formatDate(version.obsoleteTime) }}
                            </p>
                            <p v-if="version.status === 'obsolete'">
                                <strong>生效天数：</strong>{{ calculateDays(version.approveTime, version.obsoleteTime) }} 天
                            </p>
                        </div>
                    
                        <div class="right-section">
                            <div class="products">
                                <strong>适用产品：</strong>
                                <span v-if="version.products && version.products.length > 0">
                                    {{ version.products.map(p => p.model).join(', ') }}
                                </span>
                                <span v-else>暂无</span>
                            </div>
                            <div class="divider"></div>
                            <div class="requirements">
                                <strong>版本使用要求：</strong>
                                <span v-if="version.versionRequirements">{{ version.versionRequirements }}</span>
                                <span v-else>暂无</span>
                            </div>
                            <div class="divider"></div>
                            <div class="description">
                                <strong>变更内容：</strong>
                                <span v-if="version.description">{{ version.description }}</span>
                                <span v-else>暂无</span>
                            </div>
                        </div>
                    </div>
                </el-timeline-item>
            </el-timeline>
        </el-dialog>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <!-- axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <script>
        const { createApp, ref, reactive, computed, nextTick } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;
        
        const app = createApp({
            setup() {
                // 响应式数据
                const currentUser = reactive({
                    id: '001',
                    name: '管理员',
                    role: 'admin'
                });
                
                const activeTab = ref('all');
                const searchQuery = ref('');
                const downloadSearchQuery = ref('');
                const obsoleteSearchQuery = ref('');
                const sortProp = ref('');
                const sortOrder = ref('');
                
                // 对话框状态
                const uploadDialogVisible = ref(false);
                const downloadDialogVisible = ref(false);
                const updateDialogVisible = ref(false);
                const versionHistoryDialogVisible = ref(false);
                const downloadDetailDialogVisible = ref(false);
                const pendingVersionDialogVisible = ref(false);
                
                // 表单数据
                const uploadForm = reactive({
                    editId: null,
                    serialNumber: '',
                    developer: '',
                    name: '',
                    version: '',
                    file: null,
                    productsText: '',
                    versionRequirements: '',
                    description: ''
                });
                
                const downloadForm = reactive({
                    firmwareId: '',
                    serialNumber: '',
                    firmwareName: '',
                    firmwareVersion: '',
                    approveTime: '',
                    products: [],
                    versionRequirements: '',
                    developer: '',
                    workOrder: '',
                    productCode: '',
                    productModel: '',
                    burnCount: '',
                    softwareVersion: '',
                    buildTime: '',
                    backplaneVersion: '',
                    highSpeedIOVersion: '',
                    notes: ''
                });
                
                const updateForm = reactive({
                    currentId: '',
                    currentSerialNumber: '',
                    currentName: '',
                    currentVersion: '',
                    currentDeveloper: '',
                    currentApproveTime: '',
                    currentDownloadCount: '',
                    newSerialNumber: '',
                    newName: '',
                    newVersion: '',
                    newDeveloper: '',
                    newProductsText: '',
                    file: null,
                    description: '',
                    versionRequirements: '',
                    editId: null
                });
                
                const downloadDetails = reactive({
                    workOrder: '-',
                    productCode: '-',
                    productModel: '-',
                    burnCount: 0,
                    products: [],
                    versionRequirements: '-',
                    serialNumber: '-',
                    firmwareName: '-',
                    firmwareVersion: '-',
                    approveTime: '-',
                    developer: '-',
                    downloadTime: '-',
                    downloader: '-',
                    notes: '-'
                });
                
                const pendingDetails = reactive({
                    serialNumber: '',
                    name: '',
                    version: '',
                    developer: '',
                    uploadTime: '',
                    uploader: '',
                    products: [],
                    versionRequirements: '',
                    description: ''
                });
                
                const versionHistory = ref([]);
                const fileList = ref([]);
                const updateFileList = ref([]);
                
                // 表单验证规则
                const uploadRules = reactive({
                    serialNumber: [{ required: true, message: '请输入ERP流水号', trigger: 'blur' }],
                    developer: [{ required: true, message: '请输入研发者姓名', trigger: 'blur' }],
                    name: [{ required: true, message: '请输入固件名称', trigger: 'blur' }],
                    version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
                    file: [{ required: true, message: '请选择固件文件', trigger: 'change' }],
                    description: [{ required: true, message: '请输入更新日志', trigger: 'blur' }],
                    versionRequirements: [{ required: true, message: '请输入使用要求', trigger: 'blur' }],
                    productsText: [{ required: true, message: '请输入适用产品', trigger: 'blur' }]
                });
                
                const downloadRules = reactive({
                    workOrder: [{ required: true, message: '请输入工单号', trigger: 'blur' }],
                    productCode: [{ required: true, message: '请输入产品编码', trigger: 'blur' }],
                    productModel: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
                    burnCount: [{ required: true, message: '请输入生产数量', trigger: 'blur' }],
                    softwareVersion: [{ required: true, message: '请输入软件版本', trigger: 'blur' }],
                    buildTime: [{ required: true, message: '请选择构建时间', trigger: 'change' }],
                    backplaneVersion: [{ required: true, message: '请输入背板总线版本', trigger: 'blur' }],
                    highSpeedIOVersion: [{ required: true, message: '请输入高速IO版本', trigger: 'blur' }]
                });
                
                const updateRules = reactive({
                    newSerialNumber: [{ required: true, message: '请输入新ERP流水号', trigger: 'blur' }],
                    newName: [{ required: true, message: '请输入新固件名称', trigger: 'blur' }],
                    newVersion: [{ required: true, message: '请输入新版本号', trigger: 'blur' }],
                    newDeveloper: [{ required: true, message: '请输入研发者姓名', trigger: 'blur' }],
                    newProductsText: [{ required: true, message: '请输入适用产品', trigger: 'blur' }],
                    description: [{ required: true, message: '请输入更新日志', trigger: 'blur' }],
                    versionRequirements: [{ required: true, message: '请输入版本使用要求', trigger: 'blur' }],
                    file: [{ required: true, message: '请选择固件文件', trigger: 'change' }]
                });
                
                const statusMap = reactive({
                    'active': '已生效',
                    'pending': '待审核',
                    'rejected': '审核退回',
                    'obsolete': '已作废'
                });
                
                const statusColorMap = reactive({
                    'active': '#67C23A',
                    'pending': '#E6A23C',
                    'obsolete': '#F56C6C'
                });
                
                // 示例数据
                const firmwareList = reactive([
                    {
                        id: '001',
                        serialNumber: 'FW202305001',
                        oldSerialNumber: '无',
                        name: '主控板固件',
                        version: 'V1.2.0',
                        developer: '张三',
                        products: [
                            { code: 'P1001', model: '智能设备A' },
                            { code: 'P1002', model: '智能设备B' }
                        ],
                        versionRequirements: '需硬件版本≥V3.0',
                        description: '主控板基础功能固件',
                        uploadTime: '2023-05-10 14:30',
                        uploader: '张三',
                        approver: '李四',
                        approveTime: '2023-05-10 15:30',
                        status: 'active',
                        downloadCount: 15,
                        usageCount: 25,
                        obsoleteTime: '-'
                    },
                    {
                        id: '002',
                        serialNumber: 'FW202305002',
                        oldSerialNumber: '无',
                        name: '通信模块固件',
                        version: 'V2.1.0',
                        developer: '李四',
                        products: [
                            { code: 'P2001', model: '通信设备X' }
                        ],
                        versionRequirements: '需硬件版本≥V3.0',
                        description: '通信模块优化版本',
                        uploadTime: '2023-05-12 09:15',
                        uploader: '李四',
                        approver: '王五',
                        approveTime: '2023-05-12 10:30',
                        status: 'active',
                        downloadCount: 8,
                        usageCount: 12,
                        obsoleteTime: '-'
                    },
                    {
                        id: '003',
                        serialNumber: 'FW202305003',
                        oldSerialNumber: '无',
                        name: '传感器固件',
                        version: 'V1.0.1',
                        developer: '王五',
                        products: [
                            { code: 'P3001', model: '传感器设备A' }
                        ],
                        description: '传感器校准版本',
                        versionRequirements: '需硬件版本≥V3.0',
                        uploadTime: '2023-05-15 11:20',
                        uploader: '王五',
                        status: 'pending',
                        downloadCount: 0,
                        usageCount: 0,
                        source: '新发布',
                        obsoleteTime: '-'
                    }
                ]);
                
                const downloadRecords = reactive([
                    {
                        serialNumber: 'FW202305001',
                        workOrder: 'WO20230510001',
                        productCode: 'P1001',
                        productModel: '智能设备A',
                        productionCount: 100,
                        softwareVersion: 'SW_V1.2.0',
                        buildTime: '2023-05-10 08:30',
                        backplaneVersion: 'BP_V2.1',
                        highSpeedIOVersion: 'HSIO_V1.5',
                        usageTime: '2023-05-11 10:20',
                        usageUser: '王五',
                        notes: '生产线升级使用'
                    },
                    {
                        serialNumber: 'FW202305001',
                        workOrder: 'WO20230510002',
                        productCode: 'P1002',
                        productModel: '智能设备B',
                        productionCount: 50,
                        softwareVersion: 'SW_V1.2.0',
                        buildTime: '2023-05-10 08:30',
                        backplaneVersion: 'BP_V2.1',
                        highSpeedIOVersion: 'HSIO_V1.5',
                        usageTime: '2023-05-11 14:30',
                        usageUser: '赵六',
                        notes: '设备维修更换'
                    }
                ]);
                
                // 获取表单引用
                const uploadFormRef = ref(null);
                const downloadFormRef = ref(null);
                const updateFormRef = ref(null);
                const downloadDetailContent = ref(null);
                const pendingVersionContent = ref(null);
                
                // 计算属性
                const pendingFirmwareList = computed(() => {
                    return firmwareList.filter(item => item.status === 'pending');
                });
                
                const activeFirmwareList = computed(() => {
                    return firmwareList.filter(item => item.status === 'active' || item.status === 'rejected');
                });
                
                const filteredFirmwareList = computed(() => {
                    let list = activeFirmwareList.value;
                    if (searchQuery.value) {
                        const query = searchQuery.value.toLowerCase();
                        list = list.filter(item => 
                            item.serialNumber.toLowerCase().includes(query) ||
                            item.name.toLowerCase().includes(query) ||
                            item.developer.toLowerCase().includes(query) ||
                            item.products.some(p => p.model.toLowerCase().includes(query))
                        );
                    }
                    if (sortProp.value) {
                        list.sort((a, b) => {
                            let aVal = a[sortProp.value];
                            let bVal = b[sortProp.value];
                            if (sortProp.value === 'products') {
                                aVal = a.products.map(p => p.model).join(', ');
                                bVal = b.products.map(p => p.model).join(', ');
                            }
                            if (aVal < bVal) {
                                return sortOrder.value === 'ascending' ? -1 : 1;
                            }
                            if (aVal > bVal) {
                                return sortOrder.value === 'ascending' ? 1 : -1;
                            }
                            return 0;
                        });
                    }
                    return list;
                });
                
                const filteredObsoleteFirmwareList = computed(() => {
                    let list = firmwareList.filter(item => item.status === 'obsolete');
                    if (obsoleteSearchQuery.value) {
                        const query = obsoleteSearchQuery.value.toLowerCase();
                        list = list.filter(item => 
                            item.serialNumber.toLowerCase().includes(query) ||
                            item.name.toLowerCase().includes(query) ||
                            item.developer.toLowerCase().includes(query) ||
                            item.products.some(p => p.model.toLowerCase().includes(query))
                        );
                    }
                    return list;
                });
                
                const filteredDownloadRecords = computed(() => {
                    let list = downloadRecords;
                    if (downloadSearchQuery.value) {
                        const query = downloadSearchQuery.value.toLowerCase();
                        list = list.filter(item => 
                            item.workOrder.toLowerCase().includes(query) ||
                            item.productCode.toLowerCase().includes(query) ||
                            item.productModel.toLowerCase().includes(query) ||
                            (item.notes && item.notes.toLowerCase().includes(query))
                        );
                    }
                    return list;
                });
                
                // 方法
                const handleCommand = (command) => {
                    if (command === 'logout') {
                        ElMessage.success('退出登录成功');
                    }
                };
                
                const handleSearch = () => {};
                const handleSearchClear = () => {
                    searchQuery.value = '';
                };
                
                const handleDownloadSearch = () => {};
                const handleDownloadSearchClear = () => {
                    downloadSearchQuery.value = '';
                };
                
                const handleObsoleteSearch = () => {};
                const handleObsoleteSearchClear = () => {
                    obsoleteSearchQuery.value = '';
                };
                
                const handleSortChange = (column) => {
                    sortProp.value = column.prop;
                    sortOrder.value = column.order;
                };
                
                const showUploadDialog = () => {
                    Object.assign(uploadForm, {
                        editId: null,
                        serialNumber: '',
                        developer: '',
                        name: '',
                        version: '',
                        file: null,
                        productsText: '',
                        versionRequirements: '',
                        description: ''
                    });
                    fileList.value = [];
                    uploadDialogVisible.value = true;
                };
                
                const showEditDialog = (row) => {
                    Object.assign(uploadForm, {
                        editId: row.id,
                        serialNumber: row.serialNumber,
                        developer: row.developer,
                        name: row.name,
                        version: row.version,
                        file: null,
                        productsText: row.products.map(p => p.model).join(', '),
                        versionRequirements: row.versionRequirements || '',
                        description: row.description || ''
                    });
                    fileList.value = [];
                    uploadDialogVisible.value = true;
                };
                
                const showUpdateDialog = (row) => {
                    if (updateForm.editId) {
                        // 编辑模式逻辑
                        updateDialogVisible.value = true;
                        Object.assign(updateForm, {
                            currentId: row.id,
                            currentSerialNumber: row.serialNumber,
                            currentName: row.name,
                            currentVersion: row.version,
                            currentDeveloper: row.developer,
                            currentApproveTime: row.approveTime,
                            currentDownloadCount: row.downloadCount,
                            newDeveloper: row.developer,
                            newProductsText: row.products.map(p => p.model).join(', ')
                        });
                        nextTick(() => {
                            updateFormRef.value?.resetFields();
                            updateFileList.value = [];
                        });
                        updateForm.editId = null;
                    } else {
                        // 新增模式初始化逻辑
                        Object.assign(updateForm, {
                            currentId: row.id,
                            currentSerialNumber: row.serialNumber,
                            currentName: row.name,
                            currentVersion: row.version,
                            currentDeveloper: row.developer,
                            currentApproveTime: row.approveTime,
                            currentDownloadCount: row.downloadCount,
                            newProductsText: row.products
                                ? row.products.map(p => p.model).join(', ')
                                : '',
                            newSerialNumber: '',
                            newName: '',
                            newVersion: '',
                            newDeveloper: '',
                            file: null,
                            description: '',
                            versionRequirements: ''
                        });
                    }
                    updateFileList.value = [];
                    updateDialogVisible.value = true;
                };
                
                const downloadFirmware = (row) => {
                    downloadDialogVisible.value = true;
                    Object.assign(downloadForm, {
                        firmwareId: row.id,
                        serialNumber: row.serialNumber,
                        firmwareName: row.name,
                        firmwareVersion: row.version,
                        approveTime: row.approveTime,
                        products: row.products,
                        versionRequirements: row.versionRequirements || '暂无',
                        developer: row.developer,
                        workOrder: '',
                        productCode: '',
                        productModel: '',
                        burnCount: '',
                        softwareVersion: '',
                        buildTime: '',
                        backplaneVersion: '',
                        highSpeedIOVersion: '',
                        notes: ''
                    });
                    
                    nextTick(() => {
                        downloadFormRef.value?.resetFields();
                        downloadForm.products = row.products;
                    });
                };
                
                const showVersionHistory = (row) => {
                    const versionChain = [row];
                    
                    const findOldVersions = (current) => {
                        if (current.oldSerialNumber && current.oldSerialNumber !== '无') {
                            const oldVersion = firmwareList.find(
                                f => f.serialNumber === current.oldSerialNumber
                            );
                            if (oldVersion) {
                                versionChain.push(oldVersion);
                                findOldVersions(oldVersion);
                            }
                        }
                    };
                    
                    findOldVersions(row);
                    
                    versionHistory.value = versionChain
                        .filter(v => v.status !== 'pending')
                        .sort((a, b) => 
                            new Date(b.uploadTime) - new Date(a.uploadTime)
                        );
                    
                    versionHistoryDialogVisible.value = true;
                };
                
                const showDownloadDetail = (row) => {
                    Object.assign(downloadDetails, {
                        workOrder: row.workOrder || '-',
                        productCode: row.productCode || '-',
                        productModel: row.productModel || '-',
                        burnCount: row.burnCount || 0,
                        products: row.products || [],
                        versionRequirements: row.versionRequirements || '-',
                        serialNumber: row.serialNumber || '-',
                        firmwareName: row.firmwareName || '-',
                        firmwareVersion: row.firmwareVersion || '-',
                        approveTime: row.approveTime || '-',
                        developer: row.developer || '-',
                        downloadTime: row.downloadTime || '-',
                        downloader: row.downloader || '-',
                        notes: row.notes || '-'
                    });
                    downloadDetailDialogVisible.value = true;
                };
                
                const showPendingVersionDetails = (row) => {
                    Object.assign(pendingDetails, {
                        serialNumber: row.serialNumber,
                        name: row.name,
                        version: row.version,
                        developer: row.developer,
                        uploadTime: row.uploadTime,
                        uploader: row.uploader,
                        products: row.products || [],
                        versionRequirements: row.versionRequirements || '',
                        description: row.description || ''
                    });
                    pendingVersionDialogVisible.value = true;
                };
                
                const getRowFontColor = (row) => {
                    const hasHistory = firmwareList.some(f => 
                        (f.serialNumber === row.serialNumber || 
                         f.products.some(p => row.products.map(rp => rp.model).includes(p.model))) &&
                        f.status === 'obsolete'
                    );
                    return hasHistory ? '#409EFF' : '#333';
                };
                
                const handleFileChange = (file, fileList) => {
                    uploadForm.file = file.raw;
                    fileList.value = fileList.slice(-1);
                };
                
                const handleUpdateFileChange = (file, fileList) => {
                    updateForm.file = file.raw;
                    updateFileList.value = fileList.slice(-1);
                };
                
                const submitUpload = () => {
                    uploadFormRef.value?.validate(valid => {
                        if (valid) {
                            if (uploadForm.editId) {
                                // 编辑模式
                                const index = firmwareList.findIndex(f => f.id === uploadForm.editId);
                                if (index !== -1) {
                                    Object.assign(firmwareList[index], {
                                        serialNumber: uploadForm.serialNumber,
                                        name: uploadForm.name,
                                        version: uploadForm.version,
                                        developer: uploadForm.developer,
                                        products: uploadForm.productsText.split(',').map(p => ({ 
                                            code: 'P' + Math.floor(Math.random() * 10000), 
                                            model: p.trim() 
                                        })),
                                        description: uploadForm.description,
                                        versionRequirements: uploadForm.versionRequirements,
                                        status: 'pending',
                                        uploadTime: new Date().toLocaleString(),
                                        uploader: currentUser.name
                                    });
                                    ElMessage.success('修改已提交，重新进入审核流程');
                                }
                            } else {
                                // 新增模式
                                firmwareList.push({
                                    id: '00' + (firmwareList.length + 1),
                                    serialNumber: uploadForm.serialNumber,
                                    name: uploadForm.name,
                                    version: uploadForm.version,
                                    developer: uploadForm.developer,
                                    products: uploadForm.productsText.split(',').map(p => ({ 
                                        code: 'P' + Math.floor(Math.random() * 10000), 
                                        model: p.trim() 
                                    })),
                                    description: uploadForm.description,
                                    versionRequirements: uploadForm.versionRequirements,
                                    uploadTime: new Date().toLocaleString(),
                                    uploader: currentUser.name,
                                    status: 'pending',
                                    downloadCount: 0,
                                    usageCount: 0,
                                    obsoleteTime: '-',
                                    source: '新发布',
                                    oldSerialNumber: '无'
                                });
                                ElMessage.success('新固件已提交审核');
                            }
                            uploadDialogVisible.value = false;
                        }
                    });
                };
                
                const submitUpdate = () => {
                    updateFormRef.value?.validate(valid => {
                        if (valid) {
                            if (updateForm.editId) {
                                // 编辑模式 - 更新现有记录
                                const index = firmwareList.findIndex(f => f.id === updateForm.editId);
                                if (index !== -1) {
                                    Object.assign(firmwareList[index], {
                                        serialNumber: updateForm.newSerialNumber,
                                        name: updateForm.newName,
                                        version: updateForm.newVersion,
                                        developer: updateForm.newDeveloper,
                                        products: updateForm.newProductsText.split(',').map(p => ({
                                            code: 'P' + Math.floor(Math.random() * 10000),
                                            model: p.trim()
                                        })),
                                        description: updateForm.description,
                                        versionRequirements: updateForm.versionRequirements,
                                        uploadTime: new Date().toLocaleString(),
                                        status: 'pending',
                                        source: '升级'
                                    });
                                }
                            } else {
                                // 新增模式 - 从"所有固件"页点击"更新版本"时的逻辑
                                // 1. 将当前行状态改为已作废
                                const obsoleteIndex = firmwareList.findIndex(f => f.id === updateForm.currentId);
                                if (obsoleteIndex !== -1) {
                                    Object.assign(firmwareList[obsoleteIndex], {
                                        status: 'obsolete',
                                        obsoleteTime: new Date().toLocaleString()
                                    });
                                }
                                
                                // 2. 添加新版本记录
                                firmwareList.push({
                                    id: '00' + (firmwareList.length + 1),
                                    serialNumber: updateForm.newSerialNumber,
                                    name: updateForm.newName,
                                    version: updateForm.newVersion,
                                    developer: updateForm.newDeveloper,
                                    products: updateForm.newProductsText.split(',').map(p => ({
                                        code: 'P' + Math.floor(Math.random() * 10000),
                                        model: p.trim()
                                    })),
                                    description: updateForm.description,
                                    versionRequirements: updateForm.versionRequirements,
                                    uploadTime: new Date().toLocaleString(),
                                    uploader: currentUser.name,
                                    status: 'pending',
                                    downloadCount: 0,
                                    usageCount: 0,
                                    obsoleteTime: '-',
                                    oldSerialNumber: updateForm.currentSerialNumber,
                                    source: '升级'
                                });
                            }
                            
                            updateDialogVisible.value = false;
                            ElMessage.success('版本更新提交成功，等待审核');
                        }
                    });
                };
                
                const confirmDownload = () => {
                    downloadFormRef.value?.validate((valid) => {
                        if (valid) {
                            ElMessage.success('开始下载固件');
                            downloadDialogVisible.value = false;
                            
                            const products = downloadForm.products || [];
                            downloadRecords.unshift({
                                serialNumber: downloadForm.serialNumber,
                                workOrder: downloadForm.workOrder,
                                productCode: downloadForm.productCode,
                                productModel: downloadForm.productModel,
                                productionCount: downloadForm.burnCount,
                                softwareVersion: downloadForm.softwareVersion,
                                buildTime: downloadForm.buildTime,
                                backplaneVersion: downloadForm.backplaneVersion,
                                highSpeedIOVersion: downloadForm.highSpeedIOVersion,
                                usageTime: new Date().toLocaleString(),
                                usageUser: currentUser.name,
                                notes: downloadForm.notes
                            });
                            
                            const firmware = firmwareList.find(f => f.id === downloadForm.firmwareId);
                            if (firmware) {
                                firmware.downloadCount += 1;
                            }
                        }
                    });
                };
                
                const approveFirmware = (row) => {
                    ElMessageBox.confirm('确定要审核通过此固件吗?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        row.status = 'active';
                        row.approver = currentUser.name;
                        row.approveTime = new Date().toLocaleString();
                        ElMessage.success('审核通过');
                    }).catch(() => {});
                };
                
                const rejectFirmware = (row) => {
                    ElMessageBox.confirm('确定要拒绝此固件吗? 拒绝后该记录将回退到"所有固件"页面，状态显示为"审核退回"', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        row.status = 'rejected';
                        row.approver = currentUser.name;
                        row.rejectTime = new Date().toLocaleString();
                        ElMessage.success('已拒绝该固件，记录已回退到"所有固件"页面');
                    }).catch(() => {});
                };
                
                const exportToExcel = (type) => {
                    ElMessage.info(`正在导出${type}数据...`);
                    // 导出功能的完整实现可以根据需要添加
                };
                
                const exportDownloadDetailAsImage = () => {
                    const element = downloadDetailContent.value;
                    if (element && window.html2canvas) {
                        html2canvas(element).then(canvas => {
                            const link = document.createElement('a');
                            link.href = canvas.toDataURL('image/png');
                            link.download = `工单_${downloadDetails.workOrder}_详情.png`;
                            link.click();
                        });
                    } else {
                        ElMessage.error('html2canvas未加载或元素未找到');
                    }
                };
                
                const exportPendingVersionAsImage = () => {
                    const element = pendingVersionContent.value;
                    if (element && window.html2canvas) {
                        html2canvas(element).then(canvas => {
                            const link = document.createElement('a');
                            link.href = canvas.toDataURL('image/png');
                            link.download = `待审核版本_${pendingDetails.version}.png`;
                            link.click();
                        });
                    } else {
                        ElMessage.error('html2canvas未加载或元素未找到');
                    }
                };
                
                const exportVersionHistoryAsImage = () => {
                    const timelineEl = document.querySelector('.version-timeline');
                    if (timelineEl && window.html2canvas) {
                        html2canvas(timelineEl).then(canvas => {
                            const link = document.createElement('a');
                            link.href = canvas.toDataURL('image/png');
                            link.download = `版本历史_${new Date().toLocaleDateString()}.png`;
                            link.click();
                            ElMessage.success('版本历史已导出为图片');
                        });
                    } else {
                        ElMessage.error('html2canvas未加载或元素未找到');
                    }
                };
                
                // 新增辅助方法
                const statusTagType = (status) => {
                    switch(status) {
                        case 'active': return 'success';
                        case 'pending': return 'warning';
                        case 'obsolete': return 'danger';
                        default: return 'info';
                    }
                };
                
                const formatDate = (dateString) => {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    if (isNaN(date.getTime())) return '';
                
                    const year = date.getFullYear();
                    const month = date.getMonth() + 1;
                    const day = date.getDate();
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    const seconds = date.getSeconds().toString().padStart(2, '0');
                
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                };
                
                const calculateDays = (approveTime, obsoleteTime) => {
                    if (!approveTime || !obsoleteTime) return 0;
                
                    const start = new Date(approveTime);
                    const end = new Date(obsoleteTime);
                
                    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                        return 0;
                    }
                
                    const diffTime = Math.abs(end - start);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return diffDays;
                };
                
                return {
                    // 响应式数据
                    currentUser,
                    activeTab,
                    searchQuery,
                    downloadSearchQuery,
                    obsoleteSearchQuery,
                    sortProp,
                    sortOrder,
                    statusMap,
                    statusColorMap,
                    firmwareList,
                    downloadRecords,
                    
                    // 对话框状态
                    uploadDialogVisible,
                    downloadDialogVisible,
                    updateDialogVisible,
                    versionHistoryDialogVisible,
                    downloadDetailDialogVisible,
                    pendingVersionDialogVisible,
                    
                    // 表单数据
                    uploadForm,
                    downloadForm,
                    updateForm,
                    downloadDetails,
                    pendingDetails,
                    versionHistory,
                    fileList,
                    updateFileList,
                    
                    // 表单验证规则
                    uploadRules,
                    downloadRules,
                    updateRules,
                    
                    // 表单引用
                    uploadFormRef,
                    downloadFormRef,
                    updateFormRef,
                    downloadDetailContent,
                    pendingVersionContent,
                    
                    // 计算属性
                    pendingFirmwareList,
                    activeFirmwareList,
                    filteredFirmwareList,
                    filteredObsoleteFirmwareList,
                    filteredDownloadRecords,
                    
                    // 方法
                    handleCommand,
                    handleSearch,
                    handleSearchClear,
                    handleDownloadSearch,
                    handleDownloadSearchClear,
                    handleObsoleteSearch,
                    handleObsoleteSearchClear,
                    handleSortChange,
                    showUploadDialog,
                    showEditDialog,
                    showUpdateDialog,
                    downloadFirmware,
                    showVersionHistory,
                    showDownloadDetail,
                    showPendingVersionDetails,
                    getRowFontColor,
                    handleFileChange,
                    handleUpdateFileChange,
                    submitUpload,
                    submitUpdate,
                    confirmDownload,
                    approveFirmware,
                    rejectFirmware,
                    exportToExcel,
                    exportDownloadDetailAsImage,
                    exportPendingVersionAsImage,
                    exportVersionHistoryAsImage,
                    statusTagType,
                    formatDate,
                    calculateDays
                };
            }
        });
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html> 