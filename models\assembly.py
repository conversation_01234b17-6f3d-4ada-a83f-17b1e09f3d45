from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, func
from sqlalchemy.orm import relationship
from database.db_manager import Base
import hashlib

class BoardAssembly(Base):
    __tablename__ = 'board_assemblies'
    
    id = Column(Integer, primary_key=True)
    assembly_id = Column(String(64), unique=True, nullable=False)
    board_type = Column(Integer, nullable=False)  # 1=4板, 2=3板, 3=单板, 4=双板, 5=2板
    board_tester_name = Column(String(50))
    board_product_model = Column(String(50))
    board_processing_order = Column(String(50))
    board_product_quantity = Column(Integer)
    board_a_sn = Column(String(50))
    board_b_sn = Column(String(50))
    board_c_sn = Column(String(50))
    board_d_sn = Column(String(50))
    board_procode = Column(String(50))
    created_at = Column(DateTime, default=func.now())
    
    # 建立与CompleteProduct的一对一关系
    complete_product = relationship("CompleteProduct", back_populates="board_assembly", uselist=False)
    
    @classmethod
    def calculate_assembly_id(cls, board_sns):
        """计算板子的组合校验和"""
        sorted_sns = sorted(filter(None, board_sns))
        combined = '_'.join(sorted_sns)
        return hashlib.sha256(combined.encode()).hexdigest()
    
    def to_dict(self):
        return {
            'assembly_id': self.assembly_id,
            'board_type': self.board_type,
            'board_tester_name': self.board_tester_name,
            'board_product_model': self.board_product_model,
            'board_processing_order': self.board_processing_order,
            'board_product_quantity': self.board_product_quantity,
            'board_a_sn': self.board_a_sn,
            'board_b_sn': self.board_b_sn,
            'board_c_sn': self.board_c_sn,
            'board_d_sn': self.board_d_sn,
            'board_procode': self.board_procode,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

class CompleteProduct(Base):
    __tablename__ = 'complete_products'
    
    id = Column(Integer, primary_key=True)
    assembly_id = Column(String(64), ForeignKey('board_assemblies.assembly_id'), unique=True)
    product_sn = Column(String(50), unique=True)
    complete_tester_name = Column(String(50))
    complete_processing_order = Column(String(50))
    complete_product_quantity = Column(Integer)
    complete_product_model = Column(String(50))
    complete_product_remark = Column(String(200))
    assembled_at = Column(DateTime, default=func.now())
    complete_procode = Column(String(50))
    
    # 建立与BoardAssembly的一对一关系
    board_assembly = relationship("BoardAssembly", back_populates="complete_product")
    
    def to_dict(self):
        return {
            'product_sn': self.product_sn,
            'complete_tester_name': self.complete_tester_name,
            'complete_processing_order': self.complete_processing_order,
            'complete_product_quantity': self.complete_product_quantity,
            'complete_product_model': self.complete_product_model,
            'complete_product_remark': self.complete_product_remark,
            'assembled_at': self.assembled_at.strftime('%Y-%m-%d %H:%M:%S') if self.assembled_at else None,
            'complete_procode': self.complete_procode
        } 