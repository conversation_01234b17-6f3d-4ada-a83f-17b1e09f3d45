import mysql.connector

# 数据库连接信息
db_config = {
    'host': '************',
    'port': 3309,
    'user': 'root',
    'password': 'kmlc@3302133',
    'database': 'kmlc_plc'
}

table_name = 'orders'

try:
    # 建立数据库连接
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()

    # 获取表结构信息
    # 使用 DESCRIBE 语句或者 SHOW COLUMNS FROM 语句
    # DESCRIBE table_name;
    # SHOW COLUMNS FROM table_name;
    cursor.execute(f"DESCRIBE {table_name}")

    print(f"表 '{table_name}' 的结构信息：")
    print("------------------------------------")
    print(f"{'字段名 (Field)':<30} {'类型 (Type)':<25} {'允许为空 (Null)':<15} {'键 (Key)':<10} {'默认值 (Default)':<20} {'额外信息 (Extra)':<20}")
    print("------------------------------------")

    for column in cursor:
        field = column[0] if column[0] is not None else ''
        col_type = column[1] if column[1] is not None else ''
        null_allowed = column[2] if column[2] is not None else ''
        key = column[3] if column[3] is not None else ''
        default_value = str(column[4]) if column[4] is not None else '' # 转换为字符串以防出现非字符串类型
        extra = column[5] if column[5] is not None else ''

        print(f"{field:<30} {col_type:<25} {null_allowed:<15} {key:<10} {default_value:<20} {extra:<20}")

except mysql.connector.Error as err:
    print(f"数据库错误：{err}")

finally:
    # 关闭数据库连接
    if 'conn' in locals() and conn.is_connected():
        cursor.close()
        conn.close()
        print("------------------------------------")
        print("数据库连接已关闭。")