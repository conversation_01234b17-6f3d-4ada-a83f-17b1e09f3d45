from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from database.db_manager import Base
from datetime import datetime

class Order(Base):
    __tablename__ = 'orders'
    
    order_id = Column(Integer, primary_key=True, autoincrement=True)
    box_serial_number = Column(String(100), nullable=False)
    shipping_number = Column(String(100), nullable=False)
    customer_name = Column(String(100), nullable=False, index=True)
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    created_user = Column(String(30), nullable=False)
    
    # 建立与SNRecord的关系
    sn_records = relationship("SNRecord", back_populates="order", cascade="all, delete-orphan")

class SNRecord(Base):
    __tablename__ = 'sn_records'
    
    record_id = Column(Integer, primary_key=True, autoincrement=True)
    order_id = Column(Integer, ForeignKey('orders.order_id', ondelete='CASCADE'), nullable=False)
    sn_number = Column(String(50), nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    created_user = Column(String(30), nullable=False)
    
    # 建立与Order的关系
    order = relationship("Order", back_populates="sn_records") 