// 将变量声明移动到 window 对象上
if (typeof window.orderManagementVars === 'undefined') {
    window.orderManagementVars = {
        editingOrderId: null
    };
}

// 工单管理页面的状态管理
window.orderManagementState = {
    isActive: false,
    currentPage: 1,
    pageSize: 10,
    pageSizeOptions: [10, 20, 50, 100],
    orders: [],
    totalCount: 0,
    sortField: 'ord_createTime',
    sortOrder: 'desc',
    searchKeyword: '',
    totalPages: 0
};

// 初始化工单管理页面
function initOrderManagement() {
    window.orderManagementState.isActive = true;
    const orderManagementContent = document.getElementById('order-management-content');
    
    if (!orderManagementContent) {
        Logger.error('无法找到 order-management-content 元素');
        return;
    }

    orderManagementContent.innerHTML = `
        <div id="order-management-page" class="container">
            <header class="page-header">
                <h1>工单信息管理</h1>
            </header>

            <section class="search-section card">
                <div class="search-form">
                    <div class="search-input-group">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="orderSearch" placeholder="请输入加工单号搜索">
                        </div>
                        <div class="search-buttons">
                            <button class="btn btn-search" onclick="searchOrders()">
                                <i class="fas fa-search"></i> 查询
                            </button>
                            <button class="btn btn-reset" onclick="resetSearch()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <section class="operation-section card">
                <div class="button-group">
                    <button class="btn btn-primary" onclick="openAddOrderModal()">
                        <i class="fas fa-plus"></i> 添加工单
                    </button>
                    <button class="btn btn-danger" onclick="deleteSelectedOrders()">
                        <i class="fas fa-trash"></i> 删除工单
                    </button>
                    <button class="btn btn-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> 导出Excel
                    </button>
                    <span style="margin-left: 12px; color: #1890ff; font-size: 13px;">（双击工单号进入工单编辑状态）</span>
                </div>
            </section>

            <section class="table-section card">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="order-management-select-all" onchange="toggleOrderManagementSelectAll()">
                            </th>
                            <th onclick="sortBy('ord_processOrderNo')" class="sortable">
                                加工单号 <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortBy('ord_probatch')" class="sortable">
                                产品批次 <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortBy('ord_productionQuantity')" class="sortable">
                                生产数量 <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortBy('ord_productCode')" class="sortable">
                                产品编码 <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortBy('ord_productType')" class="sortable">
                                产品类型 <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortBy('ord_productModel')" class="sortable">
                                产品型号 <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortBy('production_status')" class="sortable">
                                状态 <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortBy('ord_creator')" class="sortable">
                                创建人员 <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortBy('ord_createTime')" class="sortable">
                                创建时间 <i class="fas fa-sort"></i>
                            </th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <!-- 数据将通过 JavaScript 动态插入 -->
                    </tbody>
                </table>
                <div class="table-footer">
                    <div class="table-length">
                        <div class="page-size-selector">
                            每页
                            <select id="pageSizeSelect" onchange="changePageSize()">
                                ${window.orderManagementState.pageSizeOptions.map(size => 
                                    `<option value="${size}" ${size === window.orderManagementState.pageSize ? 'selected' : ''}>
                                        ${size}
                                    </option>`
                                ).join('')}
                            </select>
                            条
                        </div>
                        <div class="total-count">
                            共计 <span id="totalRecords">0</span> 条
                        </div>
                    </div>
                    <div class="table-pagination">
                        <button class="btn btn-icon" onclick="goToFirstPage()" id="firstPageBtn">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="btn btn-icon" onclick="goToPrevPage()" id="prevPageBtn">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <div class="pagination-pages" id="paginationPages">
                            <!-- 页码将通过 JavaScript 动态生成 -->
                        </div>
                        <button class="btn btn-icon" onclick="goToNextPage()" id="nextPageBtn">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="btn btn-icon" onclick="goToLastPage()" id="lastPageBtn">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 新增/编辑工单的模态框 -->
            <div class="order-management-modal" id="orderModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 id="modalTitle">添加工单</h2>
                        <span class="close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="orderForm" class="two-column-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="ord_creator">创建人员 <span class="required">*</span></label>
                                    <input type="text" id="ord_creator" required>
                                </div>
                                <div class="form-group">
                                    <label for="ord_createTime">创建时间</label>
                                    <input type="text" id="ord_createTime" disabled value="${new Date().toLocaleString()}">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="ord_processOrderNo">加工单号 <span class="required">*</span></label>
                                    <input type="text" id="ord_processOrderNo" required>
                                </div>
                                <div class="form-group">
                                    <label for="ord_productionQuantity">生产数量 <span class="required">*</span></label>
                                    <input type="number" id="ord_productionQuantity" required min="1">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="ord_productCode">产品编码 <span class="required">*</span></label>
                                    <input type="text" id="ord_productCode" required onchange="handleProductCodeChange()">
                                </div>
                                <div class="form-group">
                                    <label for="ord_productType">产品类型 <span class="required">*</span></label>
                                    <input type="text" id="ord_productType" required readonly>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="ord_productModel">产品型号 <span class="required">*</span></label>
                                    <input type="text" id="ord_productModel" required readonly>
                                </div>
                                <div class="form-group">
                                    <label for="ord_batchNumber">产品批次</label>
                                    <input type="text" id="ord_batchNumber" readonly>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="ord_customerName">客户名称</label>
                                    <input type="text" id="ord_customerName">
                                </div>
                                <div class="form-group">
                                    <label for="orderproduct_type_id">产品属性</label>
                                    <select id="orderproduct_type_id">
                                        <option value="2" selected>五代plc</option>
                                        <option value="1">四代plc</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-row"> 
                                <div class="form-group full-width">
                                    <label for="ord_remark">备注</label>
                                    <textarea id="ord_remark" rows="1"></textarea>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group full-width">
                                    <div class="pcba-toggle">
                                        <div class="pcba-toggle__header">
                                            <div class="pcba-toggle__switch-wrapper">
                                                <input type="checkbox" 
                                                       id="ord_disable_pcba_check" 
                                                       class="pcba-toggle__input">
                                                <span class="pcba-toggle__slider"></span>
                                            </div>
                                            <label for="ord_disable_pcba_check" class="pcba-toggle__label">
                                                解除PCBA/外壳SN绑定检查
                                            </label>
                                            <div class="pcba-toggle__help">
                                                <i class="fas fa-question-circle"></i>
                                                <div class="pcba-toggle__help-content">
                                                    <h3>使用说明</h3>
                                                    <div class="help-section">
                                                        <h4>1. 默认状态</h4>
                                                        <ul>
                                                            <li><strong>关闭</strong>：需要按照正常工序进行操作。</li>
                                                        </ul>
                                                    </div>
                                                    <div class="help-section">
                                                        <h4>2. 点击开启</h4>
                                                        <p><strong>功能</strong>：取消SN号与产品PCBA的关联。</p>
                                                        <p><strong>适用场景</strong>（以下场景须开启）：</p>
                                                        <div class="scenario">
                                                            <h5>1) 借测还回</h5>
                                                            <ul>
                                                                <li><strong>适用情况</strong>：未绑定pcba的产品，添加工单时必须开启。</li>
                                                                <li>
                                                                    <strong>工单命名规则</strong>：
                                                                    <ul>
                                                                        <li>格式：<code>JC+年月日-00001/FC+产品类型</code></li>
                                                                        <li>尾号含义：</li>
                                                                        <li class="indent">001：CPU</li>
                                                                        <li class="indent">002：耦合器</li>
                                                                        <li class="indent">003：IO模块</li>
                                                                    </ul>
                                                                </li>
                                                                <li><strong>示例</strong>：CPU的完整工单为 <code>JC20250228-0001/FC001</code></li>
                                                            </ul>
                                                        </div>
                                                        <div class="scenario">
                                                            <h5>2) 老产品改装或未绑定过PCBA的产品</h5>
                                                            <ul>
                                                                <li><strong>工单填写</strong>：根据实际情况填写。</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="pcba-toggle__status" id="pcba-check-status"></div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeOrderModal()">取消</button>
                        <button class="btn btn-primary" onclick="saveOrder()">保存</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 初始化事件监听和数据
    initEventListeners();
    loadOrders();
}

// 初始化事件监听器
function initEventListeners() {
    // 搜索框防抖
    const searchInputs = document.querySelectorAll('.search-form input, .search-form select');
    searchInputs.forEach(input => {
        input.addEventListener('input', debounce(searchOrders, 500));
    });

    // 模态框关闭按钮
    const closeBtn = document.querySelector('#orderModal .close');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeOrderModal);
    }
}

// 加载工单列表
async function loadOrders() {
    if (!window.orderManagementState.isActive) return;
    
    try {
        const { currentPage, pageSize, searchKeyword, sortField, sortOrder } = window.orderManagementState;
        const queryParams = new URLSearchParams({
            page: currentPage,
            pageSize: pageSize,
            searchKeyword: searchKeyword || '',
            ...(sortField && sortField !== 'production_status' && { sortField, sortOrder })
        });

        const response = await fetch(`/api/work-order/orders?${queryParams}`);
        const data = await response.json();

        if (data.success) {
            window.orderManagementState.orders = data.orders;
            window.orderManagementState.totalCount = data.total;
            window.orderManagementState.totalPages = Math.ceil(data.total / pageSize);

            // 如果当前是按状态排序，在前端进行排序
            if (sortField === 'production_status') {
                window.orderManagementState.orders.sort((a, b) => {
                    const statusA = a.production_status?.status || '未知';
                    const statusB = b.production_status?.status || '未知';
                    const compareResult = statusA.localeCompare(statusB);
                    return sortOrder === 'asc' ? compareResult : -compareResult;
                });
            }

            renderOrdersTable();
            updatePagination();
        } else {
            Swal.fire({
                title: '错误',
                text: data.message || '加载工单列表失败',
                icon: 'error',
                confirmButtonText: '确定'
            });
        }
    } catch (error) {
        Logger.error('Error loading orders:', error);
        Swal.fire({
            title: '错误',
            text: '加载工单列表失败',
            icon: 'error',
            confirmButtonText: '确定'
        });
    }
}

// 渲染工单表格
function renderOrdersTable() {
    const tbody = document.getElementById('ordersTableBody');
    if (!tbody) return;

    const orders = window.orderManagementState.orders || [];

    tbody.innerHTML = orders.map(order => `
        <tr>
            <td>
                <input type="checkbox" class="order-checkbox" value="${order.id}">
            </td>
            <td class="dblclick-edit" 
                ondblclick="openEditOrderModal(${order.id})"
                title="${escapeHtml(order.ord_processOrderNo)}">${escapeHtml(order.ord_processOrderNo)}</td>
            <td title="${escapeHtml(order.ord_probatch)}">${escapeHtml(order.ord_probatch)}</td>
            <td title="${escapeHtml(order.ord_productionQuantity)}">${escapeHtml(order.ord_productionQuantity)}</td>
            <td title="${escapeHtml(order.ord_productCode)}">${escapeHtml(order.ord_productCode)}</td>
            <td title="${escapeHtml(order.ord_productType)}">${escapeHtml(order.ord_productType)}</td>
            <td title="${escapeHtml(order.ord_productModel)}">${escapeHtml(order.ord_productModel)}</td>
            <td class="status-cell" title="${getStatusText(order.production_status?.status)}">
                ${renderStatusCell(order)}
            </td>
            <td title="${escapeHtml(order.ord_creator)}">${escapeHtml(order.ord_creator)}</td>
            <td title="${escapeHtml(order.ord_createTime)}">${escapeHtml(order.ord_createTime)}</td>
            <td title="${escapeHtml(order.ord_remark)}">${escapeHtml(order.ord_remark || '')}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-edit" onclick="openEditOrderModal(${order.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    ${order.production_status?.status === '生产中' ? `
                    <button class="btn btn-sm btn-success" onclick="markOrderAsCompleted(${order.id})">
                        <i class="fas fa-check"></i> 手动完成
                    </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// 渲染状态单元格，优化手动完成的标识
function renderStatusCell(order) {
    const status = order.production_status?.status || '未知';
    
    // 判断是否是手动标记完成
    // 如果完成标记为true，但实际完成数量不足，则是手动标记
    const isManuallyCompleted = order.ord_completed_flag === true && 
                               (order.production_status?.packaged < order.production_status?.target);
    
    if (status === '已完成') {
        // 添加不同图标区分自动和手动完成
        let completionIcon = '';
        if (isManuallyCompleted) {
            completionIcon = '<i class="fas fa-user-check manual-mark" title="手动标记完成"></i>';
        } else {
            completionIcon = '<i class="fas fa-check-circle auto-mark" title="自动完成"></i>';
        }
        
        return `
            <span class="status-badge completed">
                已完成 ${completionIcon}
            </span>
        `;
    } else if (status === '生产中' || status === 'in_progress') {
        return `
            <span class="status-badge in-progress" 
                  onclick="showProductionDetails(${order.id})">
                生产中
            </span>
        `;
    }
    return `<span class="status-badge">${status}</span>`;
}

// 显示生产详情模态框
function showProductionDetails(orderId) {
    fetch(`/api/work-order/orders/${orderId}/status`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const status = data.status;
                
                // 检查是否手动标记完成
                const isManuallyCompleted = status.manually_completed === true;
                
                let completionInfo = '';
                if (status.status === '已完成') {
                    completionInfo = `
                        <div class="detail-item completion-type">
                            <span class="detail-label">完成方式：</span>
                            <span class="detail-value ${isManuallyCompleted ? 'manual' : 'auto'}">
                                ${isManuallyCompleted ? '手动标记' : '自动完成'}
                            </span>
                        </div>
                    `;
                }
                
                Swal.fire({
                    title: '生产进度详情',
                    html: `
                        <div class="production-details">
                            <div class="detail-item">
                                <span class="detail-label">已组装：</span>
                                <span class="detail-value">${status.assembled}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">已测试：</span>
                                <span class="detail-value">${status.tested}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">已包装：</span>
                                <span class="detail-value">${status.packaged}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">目标数量：</span>
                                <span class="detail-value">${status.target}</span>
                            </div>
                            ${completionInfo}
                        </div>
                    `,
                    confirmButtonText: '关闭'
                });
            }
        })
        .catch(error => {
            Logger.error('Error fetching production details:', error);
            Swal.fire({
                title: '错误',
                text: '获取生产详情失败',
                icon: 'error'
            });
        });
}

// 获取状态文本
function getStatusText(status) {
    if (!status) return '未知';
    switch(status) {
        case 'in_progress':
            return '生产中';
        case 'completed':
            return '已完成';
        default:
            return status;
    }
}

// 其他必要的辅助函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function formatDate(date) {
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).replace(/\//g, '-');
}

// 清理函数
window.cleanupOrderManagement = function() {
    window.orderManagementState.isActive = false;
    // 清理其他可能的事件监听器和状态
};

// 添加其他必要的函数
function escapeHtml(str) {
    if (str === null || str === undefined) return '';
    // 确保转换为字符串
    str = String(str);
    return str
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// 分页相关函数
function updatePagination() {
    const { currentPage, totalCount, pageSize } = window.orderManagementState;
    const totalPages = Math.ceil(totalCount / pageSize);
    window.orderManagementState.totalPages = totalPages;

    // 添加安全检查
    const totalRecordsElement = document.getElementById('totalRecords');
    const paginationPagesElement = document.getElementById('paginationPages');
    
    // 如果元素不存在，说明可能在页面切换中，直接返回
    if (!totalRecordsElement || !paginationPagesElement) {
        return;
    }

    // 更新总条数
    totalRecordsElement.textContent = totalCount;

    // 生成页码
    let pagesHtml = '';
    
    // 确定显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);
    
    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }

    // 添加页码按钮
    if (startPage > 1) {
        pagesHtml += `<span class="pagination-ellipsis">...</span>`;
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pagesHtml += `
            <button class="btn btn-page ${i === currentPage ? 'active' : ''}" 
                    onclick="goToPage(${i})">
                ${i}
            </button>
        `;
    }
    
    if (endPage < totalPages) {
        pagesHtml += `<span class="pagination-ellipsis">...</span>`;
    }

    paginationPagesElement.innerHTML = pagesHtml;

    // 更新导航按钮状态 - 添加安全检查
    const buttons = {
        firstPageBtn: document.getElementById('firstPageBtn'),
        prevPageBtn: document.getElementById('prevPageBtn'),
        nextPageBtn: document.getElementById('nextPageBtn'),
        lastPageBtn: document.getElementById('lastPageBtn')
    };

    // 只有在所有按钮都存在时才更新状态
    if (Object.values(buttons).every(btn => btn)) {
        buttons.firstPageBtn.disabled = currentPage === 1;
        buttons.prevPageBtn.disabled = currentPage === 1;
        buttons.nextPageBtn.disabled = currentPage === totalPages;
        buttons.lastPageBtn.disabled = currentPage === totalPages;
    }
}

// 页码导航函数
function goToPage(page) {
    window.orderManagementState.currentPage = page;
    loadOrders();
}

function goToFirstPage() {
    goToPage(1);
}

function goToPrevPage() {
    const { currentPage } = window.orderManagementState;
    if (currentPage > 1) {
        goToPage(currentPage - 1);
    }
}

function goToNextPage() {
    const { currentPage, totalPages } = window.orderManagementState;
    if (currentPage < totalPages) {
        goToPage(currentPage + 1);
    }
}

function goToLastPage() {
    goToPage(window.orderManagementState.totalPages);
}

function changePageSize() {
    const newSize = parseInt(document.getElementById('pageSizeSelect').value);
    window.orderManagementState.pageSize = newSize;
    window.orderManagementState.currentPage = 1;
    loadOrders();
}

// 修改开关事件处理函数
function initPcbaToggle() {
    const toggle = document.getElementById('ord_disable_pcba_check');
    const statusElement = document.getElementById('pcba-check-status');
    const switchWrapper = document.querySelector('.pcba-toggle__switch-wrapper');
    
    // 先移除之前可能存在的事件监听器
    const newSwitchWrapper = switchWrapper.cloneNode(true);
    switchWrapper.parentNode.replaceChild(newSwitchWrapper, switchWrapper);
    
    // 重新获取新的元素
    const newToggle = document.getElementById('ord_disable_pcba_check');
    
    function updateStatus(checked) {
        if (checked) {
            statusElement.textContent = '该工单测试的时候不需要验证SN号是否绑定PCBA';
            statusElement.classList.add('active');
        } else {
            statusElement.textContent = '';
            statusElement.classList.remove('active');
        }
    }
    
    // 添加点击事件到开关包装器
    newSwitchWrapper.addEventListener('click', function(e) {
        e.preventDefault(); // 防止事件冒泡
        newToggle.checked = !newToggle.checked; // 切换复选框状态
        updateStatus(newToggle.checked); // 更新状态显示
        
        // 触发 change 事件
        const event = new Event('change');
        newToggle.dispatchEvent(event);
    });
    
    // 保持原有的change事件监听
    newToggle.addEventListener('change', function() {
        updateStatus(this.checked);
    });
    
    // 初始状态
    updateStatus(newToggle.checked);
}

// 修改打开添加工单模态框函数
async function openAddOrderModal() {
    try {
        // 重置编辑状态
        window.orderManagementVars.editingOrderId = null;
        
        // 获取当前用户信息
        const response = await fetch('/api/work-order/current-user');
        const data = await response.json();
        
        const modal = document.querySelector('.order-management-modal');
        const form = document.getElementById('orderForm');
        form.reset();
        
        // 重置模态框标题
        const modalTitle = modal.querySelector('.modal-header h2');
        modalTitle.textContent = '添加工单';
        
        // 如果成功获取到用户信息，自动填入创建人员
        if (data.success) {
            document.getElementById('ord_creator').value = data.username;
            // 可选：禁用创建人员输入框，防止修改
            document.getElementById('ord_creator').setAttribute('readonly', true);
        }
        
        // 重置所有只读属性
        document.getElementById('ord_processOrderNo').removeAttribute('readonly');
        document.getElementById('ord_productCode').removeAttribute('readonly'); // 添加工单时产品编码可编辑
        if (document.getElementById('ord_probatch')) {
            document.getElementById('ord_probatch').removeAttribute('readonly');
        }
        
        // 设置创建时间为当前时间
        const now = new Date();
        document.getElementById('ord_createTime').value = formatDate(now);
        
        // 初始化PCBA检查开关
        initPcbaToggle();
        
        modal.style.display = 'flex';
    } catch (error) {
        Logger.error('Error getting current user:', error);
        await Swal.fire({
            title: '错误',
            text: '获取用户信息失败',
            icon: 'error',
            confirmButtonText: '确定'
        });
    }
}

// 关闭模态框
function closeOrderModal() {
    const modal = document.querySelector('.order-management-modal');
    modal.style.display = 'none';
}

// 搜索工单
async function searchOrders() {
    const searchKeyword = document.getElementById('orderSearch').value;
    window.orderManagementState.searchKeyword = searchKeyword;
    window.orderManagementState.currentPage = 1;
    await loadOrders();
}

// 重置搜索
function resetSearch() {
    document.getElementById('orderSearch').value = '';
    window.orderManagementState.searchKeyword = '';
    window.orderManagementState.currentPage = 1;
    loadOrders();
}

// 修改保存工单函数
async function saveOrder() {
    const form = document.getElementById('orderForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // 获取表单数据
    const formData = {
        ord_creator: document.getElementById('ord_creator').value.trim(),
        ord_createTime: document.getElementById('ord_createTime').value.trim(),
        ord_processOrderNo: document.getElementById('ord_processOrderNo').value.trim(),
        ord_productCode: document.getElementById('ord_productCode').value.trim(),
        ord_productType: document.getElementById('ord_productType').value.trim(),
        ord_productModel: document.getElementById('ord_productModel').value.trim(),
        ord_customerName: document.getElementById('ord_customerName').value.trim(),
        orderproduct_type_id: parseInt(document.getElementById('orderproduct_type_id').value, 10),
        ord_productionQuantity: parseInt(document.getElementById('ord_productionQuantity').value || 0),
        ord_remark: document.getElementById('ord_remark').value.trim(),
        // 恢复 snlenth 字段的收集
        ord_snlenth: window.currentProductSnLength || null,
        // PCBA检查控制字段
        ord_requires_pcba_check: !document.getElementById('ord_disable_pcba_check').checked
    };

    // 确定请求URL和方法
    const url = window.orderManagementVars.editingOrderId ? 
        `/api/work-order/orders/${window.orderManagementVars.editingOrderId}` : 
        '/api/work-order/orders';
    const method = window.orderManagementVars.editingOrderId ? 'PUT' : 'POST';

    try {
        // 保存当前的排序状态
        const currentSortField = window.orderManagementState.sortField;
        const currentSortOrder = window.orderManagementState.sortOrder;
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const data = await response.json();

        if (data.success) {
            await Swal.fire({
                title: '成功',
                text: window.orderManagementVars.editingOrderId ? '工单更新成功' : '工单创建成功',
                icon: 'success',
                confirmButtonText: '确定'
            });
            closeOrderModal();
            
            // 重新加载数据，但保持排序状态
            await loadOrders();
            
            // 如果排序状态被重置，手动恢复
            if (window.orderManagementState.sortField !== currentSortField || 
                window.orderManagementState.sortOrder !== currentSortOrder) {
                window.orderManagementState.sortField = currentSortField;
                window.orderManagementState.sortOrder = currentSortOrder;
                updateSortIcons(currentSortField);
                await loadOrders();
            }
            
            // 重置编辑状态
            window.orderManagementVars.editingOrderId = null;
        } else {
            await Swal.fire({
                title: '错误',
                text: data.message || (window.orderManagementVars.editingOrderId ? '工单更新失败' : '工单创建失败'),
                icon: 'error',
                confirmButtonText: '确定'
            });
        }
    } catch (error) {
        Logger.error('Error saving order:', error);
        await Swal.fire({
            title: '错误',
            text: '保存工单失败',
            icon: 'error',
            confirmButtonText: '确定'
        });
    }
}

// 删除选中的工单
async function deleteSelectedOrders() {
    const selectedIds = Array.from(document.querySelectorAll('.order-checkbox:checked'))
        .map(checkbox => parseInt(checkbox.value));

    if (selectedIds.length === 0) {
        await Swal.fire({
            title: '警告',
            text: '请选择要删除的工单',
            icon: 'warning',
            confirmButtonText: '确定'
        });
        return;
    }

    const result = await Swal.fire({
        title: '确认删除',
        text: '确定要删除选中的工单吗？',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    });

    if (!result.isConfirmed) return;

    try {
        const response = await fetch('/api/work-order/orders/batch', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ orderIds: selectedIds })
        });

        const data = await response.json();

        if (data.success) {
            await Swal.fire({
                title: '成功',
                text: '工单删除成功',
                icon: 'success',
                confirmButtonText: '确定'
            });
            loadOrders();
        } else {
            await Swal.fire({
                title: '错误',
                text: data.message || '工单删除失败',
                icon: 'error',
                confirmButtonText: '确定'
            });
        }
    } catch (error) {
        Logger.error('Error deleting orders:', error);
        await Swal.fire({
            title: '错误',
            text: '删除工单失败',
            icon: 'error',
            confirmButtonText: '确定'
        });
    }
}

// 已由 toggleOrderManagementSelectAll 取代

// 导出到Excel
async function exportToExcel() {
    try {
        const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
        const selectedIds = Array.from(selectedCheckboxes)
            .map(checkbox => checkbox.value)
            .filter(id => id); // 过滤掉空值
        
        const url = '/api/work-order/orders/export' + 
            (selectedIds.length ? `?ids=${selectedIds.join(',')}` : '');
            
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || '导出失败');
        }
        
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = downloadUrl;
        const timestamp = formatDate(new Date());
        a.download = selectedIds.length ? 
            `已选择${selectedIds.length}个工单_${timestamp}.xlsx` : 
            `全部工单_${timestamp}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(downloadUrl);
        document.body.removeChild(a);
        
        await Swal.fire({
            title: '成功',
            text: '导出成功',
            icon: 'success',
            timer: 1500
        });
    } catch (error) {
        Logger.error('Error exporting to Excel:', error);
        await Swal.fire({
            title: '错误',
            text: error.message || '导出失败，请稍后重试',
            icon: 'error'
        });
    }
}

// 格式化日期函数
function formatDate(date) {
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).replace(/\//g, '-');
}

// 排序功能
function sortBy(field) {
    if (window.orderManagementState.sortField === field) {
        window.orderManagementState.sortOrder = 
            window.orderManagementState.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
        window.orderManagementState.sortField = field;
        window.orderManagementState.sortOrder = 'asc';
    }
    
    updateSortIcons(field);
    
    // 如果是状态字段，在前端处理排序
    if (field === 'production_status') {
        const orders = [...window.orderManagementState.orders];
        orders.sort((a, b) => {
            const statusA = a.production_status?.status || '未知';
            const statusB = b.production_status?.status || '未知';
            const compareResult = statusA.localeCompare(statusB);
            return window.orderManagementState.sortOrder === 'asc' 
                ? compareResult 
                : -compareResult;
        });
        window.orderManagementState.orders = orders;
        renderOrdersTable();
    } else {
        // 对于其他字段，发送请求到后端进行排序
        loadOrders();
    }
}

// 更新排序图标
function updateSortIcons(activeField) {
    const headers = document.querySelectorAll('th[onclick]');
    headers.forEach(header => {
        const icon = header.querySelector('i');
        if (icon) {
            const field = header.getAttribute('onclick').match(/'([^']+)'/)[1];
            if (field === activeField) {
                icon.className = `fas fa-sort-${window.orderManagementState.sortOrder === 'asc' ? 'up' : 'down'}`;
            } else {
                icon.className = 'fas fa-sort';
            }
        }
    });
}

// 添加产品编码变更处理函数
async function handleProductCodeChange() {
    const productCodeInput = document.getElementById('ord_productCode');
    const productTypeInput = document.getElementById('ord_productType');
    const productModelInput = document.getElementById('ord_productModel');
    const productCode = productCodeInput.value.trim();
    
    if (!productCode) {
        productTypeInput.value = '';
        productModelInput.value = '';
        window.currentProductSnLength = null;
        return;
    }
    
    try {
        const response = await fetch(`/api/product-by-code?code=${encodeURIComponent(productCode)}`);
        const data = await response.json();
        
        if (data.success && data.product) {
            productTypeInput.value = data.product.productType || '';
            productModelInput.value = data.product.productName || '';
            // 确保正确获取并存储 snLength
            window.currentProductSnLength = data.product.snLength;
        } else {
            productTypeInput.value = '';
            productModelInput.value = '';
            window.currentProductSnLength = null;
            await Swal.fire({
                title: '警告',
                text: '未找到对应的产品信息',
                icon: 'warning',
                confirmButtonText: '确定'
            });
        }
    } catch (error) {
        Logger.error('Error fetching product details:', error);
        productTypeInput.value = '';
        productModelInput.value = '';
        window.currentProductSnLength = null;
    }
}

// 打开编辑工单模态框
async function openEditOrderModal(orderId) {
    window.orderManagementVars.editingOrderId = orderId;
    
    try {
        const response = await fetch(`/api/work-order/orders/${orderId}`);
        const data = await response.json();
        
        if (data.success) {
            const order = data.order;
            const modal = document.querySelector('.order-management-modal');
            const modalTitle = modal.querySelector('.modal-header h2');
            modalTitle.textContent = '编辑工单';
            
            // 填充表单
            document.getElementById('ord_creator').value = order.ord_creator;
            document.getElementById('ord_creator').setAttribute('readonly', true);
            
            // 处理日期格式
            let createTime = order.ord_createTime;
            if (createTime && !createTime.includes('/')) {
                createTime = createTime.replace(/-/g, '/');
            }
            document.getElementById('ord_createTime').value = createTime;
            
            document.getElementById('ord_processOrderNo').value = order.ord_processOrderNo;
            
            // 设置产品编码为只读（仅在编辑时）
            document.getElementById('ord_productCode').value = order.ord_productCode;
            document.getElementById('ord_productCode').setAttribute('readonly', true); // 编辑时产品编码不允许修改
            
            document.getElementById('ord_productType').value = order.ord_productType;
            document.getElementById('ord_productModel').value = order.ord_productModel;
            document.getElementById('ord_customerName').value = order.ord_customerName || '';
            document.getElementById('orderproduct_type_id').value = order.orderproduct_type_id || 2;
            document.getElementById('ord_productionQuantity').value = order.ord_productionQuantity;
            
            if (document.getElementById('ord_probatch')) {
                document.getElementById('ord_probatch').value = order.ord_probatch;
                document.getElementById('ord_probatch').setAttribute('readonly', true);
            }
            
            document.getElementById('ord_remark').value = order.ord_remark || '';
            
            // 设置PCBA检查开关状态
            document.getElementById('ord_disable_pcba_check').checked = !order.ord_requires_pcba_check;
            initPcbaToggle();
            
            // 显示模态框
            modal.style.display = 'flex';
        } else {
            await Swal.fire({
                title: '错误',
                text: data.message || '获取工单信息失败',
                icon: 'error',
                confirmButtonText: '确定'
            });
        }
    } catch (error) {
        Logger.error('Error fetching order details:', error);
        await Swal.fire({
            title: '错误',
            text: '获取工单信息失败',
            icon: 'error',
            confirmButtonText: '确定'
        });
    }
}

// 添加状态辅助函数
function getStatusClass(status) {
    if (!status) return '';
    switch(status) {
        case 'in_progress':
        case '生产中':
            return 'in-progress';
        case 'completed':
        case '已完成':
            return 'completed';
        default:
            return '';
    }
}

// 添加标记完成的函数
async function markOrderAsCompleted(orderId) {
    const result = await Swal.fire({
        title: '确认操作',
        text: '确定要将此工单标记为完成状态吗？此操作不可逆。',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    });

    if (!result.isConfirmed) return;

    try {
        const response = await fetch(`/api/work-order/orders/${orderId}/complete`, {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            await Swal.fire({
                title: '成功',
                text: '工单已标记为完成状态',
                icon: 'success',
                confirmButtonText: '确定'
            });
            loadOrders(); // 重新加载工单列表
        } else {
            await Swal.fire({
                title: '错误',
                text: data.message || '操作失败',
                icon: 'error',
                confirmButtonText: '确定'
            });
        }
    } catch (error) {
        Logger.error('Error marking order as completed:', error);
        await Swal.fire({
            title: '错误',
            text: '操作失败，请检查网络连接',
            icon: 'error',
            confirmButtonText: '确定'
        });
    }
}

function toggleOrderManagementSelectAll() {
    const selectAllCheckbox = document.getElementById('order-management-select-all');
    const checkboxes = document.querySelectorAll('.order-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
} 