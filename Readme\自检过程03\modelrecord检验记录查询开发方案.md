# PLC生产质量检验系统 - 检验记录查询功能开发文档

## 1. 功能概述

本功能模块旨在为用户提供一个全面、高效的检验记录查询界面。用户能够通过工单号、产品SN号或日期范围来检索检验记录。查询结果以列表形式展示工单的基本信息。用户可以进一步查看选定工单的详细检验情况，包括各检验阶段（组装前、测试前、包装前）下各个角色（首检、自检、IPQC）的完成状态、执行SN、检验员、检验时间、具体的检验项目（基于完成该阶段角色的SN的最终提交结果）以及相关附件。

当直接通过SN号查询时，详情将展示该特定SN在工单流程中的具体检验情况。

## 1.1 核心需求说明与澄清

在具体的查询与详情展示实现中，遵循以下核心原则：

1.  **列表展示 (工单号/日期查询)**：
    *   查询结果以工单为基本单位进行展示。
    *   列表中的SN列，显示该工单关联的**首个SN号**。

2.  **"详情"模态框 (从工单号/日期查询结果进入时)**：
    *   模态框主要展示该**工单的整体完成情况和检验记录，不细化到每一个SN的独立完整流程**。
    *   关注点在于该工单在各个检验阶段（组装前、测试前、包装前）及各个检验角色（首检、自检、IPQC）是否**至少有一个SN完成了最终的检验提交**。

3.  **附件关联**：
    *   附件是**按工单级别上传的**，并与检验阶段 (`stage`) 和检验角色 (`inspector_role`) 相关联。
    *   附件**不直接与单个SN**进行关联。详情模态框中展示的附件是基于工单、阶段和角色的。

4.  **工单完成状态的定义**：
    *   一个工单被视为"完成检验"其所有预定义的检验阶段，在每个阶段的每个预定义检验角色下，都**至少有一个SN的 `ProductInspectionStatus` 记录的 `submission_type` 为 `'final'`**。
    *   允许不同阶段、不同角色的最终检验记录来自该工单下的不同SN。

5.  **通过SN查询时的"详情"模态框**：
    *   当用户通过输入特定SN号进行查询，并查看其详情时，模态框应明确展示这个**特定SN**的检验记录。
    *   如果该特定SN在某个"阶段-角色"组合下没有检验记录（即该组合的检验是由同工单下的其他SN完成的），则在该特定SN的详情视图中，对应的"阶段-角色"应显示为"未检验此SN"或类似状态。

## 2. 技术实现方案

### 2.1 前端技术栈
- Bootstrap 5：提供响应式UI组件和样式。
- 原生JavaScript：处理数据交互和DOM操作。
- Bootstrap Icons：提供文件类型图标等。
- SweetAlert2：用于消息提示和加载指示。

### 2.2 后端技术栈
- Python 3.x
- Flask (Web框架)
- SQLAlchemy (ORM)
- MySQL (数据库)

### 2.3 数据交互
- 前后端通过 JSON 格式进行异步数据交互 (Fetch API)。
- API接口遵循 RESTful 设计原则。

## 3. 用户界面组件 - 列表视图

### 3.1 查询入口
- **工单号输入框**: 允许用户输入工单号进行精确或模糊查询。
- **SN号输入框**: 允许用户输入产品SN号进行查询。
- **日期选择**:
    - 开始日期输入框
    - 结束日期输入框
    (用于筛选此日期范围内创建的工单)
- **查询按钮**: 触发查询操作。
- **重置按钮**: 清空所有查询条件和结果。
- **高级查询切换按钮**: 显示/隐藏日期选择等高级筛选条件。

### 3.2 查询结果列表
- 以表格形式展示查询结果，支持分页和排序。
- **列定义**:
    - **序号**: 行号。
    - **工单号**: `QualityInspectionWorkOrder.work_order_no`。
    - **SN号**:
        - 若按工单号或日期查询：显示该工单下的首个SN号 (或SN总数，如"共X个")。
        - 若按SN号查询：明确显示查询的SN号。
    - **产品类型**: `ProductType.type_name`。
    - **工单状态**: `QualityInspectionWorkOrder.status` (例如：pending, processing, completed)。
    - **创建时间**: `QualityInspectionWorkOrder.created_at`。
    - **操作**:
        - **查看详情按钮**: 点击后弹出"检验记录详情"模态框。

## 4. "检验记录详情"模态框

模态框用于展示选定记录的详细检验信息。其内容根据查询来源（工单列表或SN直接查询）动态调整。

**详细的UI布局、卡片设计、颜色区分、检验项目展示方式及附件预览需求，请参见 `model.md` 文件中的"查询结果模态框"及其后续相关章节的描述。**

核心展示逻辑：
- **工单维度详情** (从按工单号或日期查询的列表进入):
    - **基本信息**: 工单号、产品型号、创建/更新时间、状态、是否返工。
    - **各检验阶段与角色**:
        - 清晰展示每个"阶段-角色"组合是由哪个SN完成的最终提交。
        - 显示该最终提交的检验员和检验时间。
        - 基于完成该组合的SN的最终提交结果，列出所有检验项目，并标记为全部通过。
    - **附件**: 展示与该工单、阶段、角色关联的附件。
- **SN维度详情** (从按SN号查询的列表进入):
    - **基本信息**: 工单号、被查询的SN号、产品型号、工单创建/更新时间、工单状态。
    - **各检验阶段与角色**:
        - 展示被查询SN在每个"阶段-角色"组合的检验状态 (是否完成最终提交、部分提交或未检验)。
        - 显示检验员和检验时间 (如果已检验)。
        - 如果该SN在此"阶段-角色"完成最终提交，则所有项目标记为通过。
        - 如果是部分提交或未检验，则所有项目标记为未通过或相应提示。
    - **附件**: 展示与该SN所属工单、阶段、角色关联的附件。


## 5. 后端API设计

所有相关API均在 `quality_inspection_bp` 蓝图下，URL前缀为 `/api/quality-inspection`。

### 5.1 `check_work_order_completion(session, work_order_id)` 函数 (内部逻辑调整)
- **目的**: 在检验记录提交后，检查对应工单是否达到完成状态。
- **核心逻辑 (已调整)**:
    - 工单完成的定义：所有预定义的阶段（assembly, test, packaging）和所有预定义的角色（first, self, ipqc）的每一个组合（共 3x3 = 9 个组合），都**至少有一条** `ProductInspectionStatus` 记录的 `submission_type` 是 `'final'`。
    - 如果满足此条件，且工单当前状态不是 'completed'，则更新 `QualityInspectionWorkOrder.status` 为 'completed'。
- 此函数由 `POST /api/quality-inspection/records` 接口在保存检验记录后调用。

### 5.2 `GET /inspection-records/search` (检验记录列表查询)
- **请求参数**:
    - `type` (string, optional, default: 'orderNo'): 查询类型。可选值：'orderNo', 'serialNumber'。
    - `value` (string, optional): 查询类型对应的值。
    - `page` (int, optional, default: 1): 当前页码。
    - `pageSize` (int, optional, default: 10): 每页记录数。
    - `sort_field` (string, optional, default: 'created_at'): 排序字段。支持 'order_no', 'product_type', 'status', 'created_at'。 (按SN排序在工单列表视图中意义不大，可忽略或按工单号处理)。
    - `sort_order` (string, optional, default: 'desc'): 排序顺序。可选值：'asc', 'desc'。
    - `startDate` (string, optional, format: 'YYYY-MM-DD'): 开始日期。
    - `endDate` (string, optional, format: 'YYYY-MM-DD'): 结束日期。
- **响应体 (JSON)**:
    ```json
    {
        "success": true,
        "records": [
            {
                "id": 123, // QualityInspectionWorkOrder.id
                "order_no": "WO2023001",
                "serial_number": "SN00001", // 或 "共5个"
                "product_type": "PLC模块V1",
                "status": "completed",
                "created_at": "2023-10-26 10:00:00",
                "detail_query_type": "work_order", // 'work_order' 或 'serial_number'
                "product_id_for_detail": null // 或 对应的 Product.id
            }
            // ... more records
        ],
        "totalCount": 100,
        "currentPage": 1,
        "pageSize": 10,
        "totalPages": 10
    }
    ```
- **主要查询逻辑**:
    - 基于 `QualityInspectionWorkOrder` 表，关联 `ProductType`。
    - 根据 `type` 和 `value` 构建过滤条件：
        - `orderNo`: 模糊匹配 `QualityInspectionWorkOrder.work_order_no`。
        - `serialNumber`: 查询 `Product.serial_no` 所在的 `QualityInspectionWorkOrder.id`，再关联查询。
    - 根据 `startDate`, `endDate` 过滤 `QualityInspectionWorkOrder.created_at`。
    - 应用排序和分页。
    - 为每条工单记录获取其首个SN号（或SN总数）用于列表展示。
    - 根据查询类型，填充 `detail_query_type` 和 `product_id_for_detail` 以便前端调用详情接口。

### 5.3 `GET /inspection-records/<int:record_id>/detail` (检验记录详情)
- **URL参数**:
    - `<int:record_id>`: 此处固定为 `QualityInspectionWorkOrder.id`。
- **Query参数**:
    - `detail_type` (string, required): 'work_order' 或 'serial_number'。
    - `product_id` (int, optional): 当 `detail_type` 为 'serial_number' 时，此为 `Product.id`。
- **响应体 (JSON)**:
    ```json
    // 详细结构请参照上一轮讨论中提供的Python代码实现中的JSON结构
    {
        "success": true,
        "record": {
            "basic_info": {
                "order_no": "WO2023001",
                "product_model": "PLC模块V1",
                // ... 其他工单基本信息
                "serial_no_queried": "SN00001" // (仅当 detail_type='serial_number')
            },
            "stages": { // Keyed by stage_name ('assembly', 'test', 'packaging')
                "assembly": { // Keyed by role_name ('first', 'self', 'ipqc')
                    "first": {
                        "is_completed": true, // boolean
                        "inspector": "张三",
                        "inspection_time": "2023-10-26 11:00:00",
                        "completed_by_sn": "SN00001", // (仅当 detail_type='work_order')
                        "items": [
                            {"name": "外观检查", "passed": true},
                            {"name": "接口检查", "passed": true}
                            // ... (若为SN详情且部分提交，passed可能为false并有note)
                        ]
                    }
                    // ... other roles
                }
                // ... other stages
            },
            "attachments": { // Keyed by stage_name, then role_name
                "assembly": {
                    "first": [
                        {
                            "id": 1, "name": "外观图.jpg", "url": "/api/...", 
                            "upload_time": "...", "file_type": ".jpg"
                        }
                    ]
                }
            }
        }
    }
    ```
- **主要查询逻辑**:
    - **若 `detail_type` == 'work_order'**:
        - 获取工单基本信息。
        - 对每个预定义的"阶段-角色"组合：
            - 查询 `ProductInspectionStatus` 中与该工单、阶段、角色匹配的、`submission_type` 为 `'final'` 的最新一条记录。
            - 获取完成该记录的 `Product.serial_no`，检验员，检验时间。
            - 获取该产品类型在该阶段的所有标准 `InspectionItem`。因是工单维度的最终提交，所有项目视为 `passed: true`。
            - 若无 `final` 记录，则查找 `partial` 记录填充检验员和时间，但项目仍视为 `passed: false`（或相应提示）。
        - 获取与该工单关联的所有 `InspectionAttachment`，按阶段和角色组织。
    - **若 `detail_type` == 'serial_number'**:
        - 获取 `Product` 信息及关联的 `QualityInspectionWorkOrder` 基本信息。
        - 对每个预定义的"阶段-角色"组合：
            - 查询 `ProductInspectionStatus` 中与该 `product_id`、阶段、角色匹配的唯一记录。
            - 获取检验员、检验时间。
            - 获取该产品类型在该阶段的所有标准 `InspectionItem`。
            - 如果该SN的 `submission_type` 为 `'final'`，所有项目视为 `passed: true`。
            - 如果为 `'partial'`，所有项目视为 `passed: false` 并可加备注。
            - 如果无记录，所有项目视为 `passed: false` 并提示未检验。
        - 获取与该SN所属工单关联的所有 `InspectionAttachment`。

## 6. 交互流程简述
1.  用户在"检验记录查询"页面设置查询条件，点击"查询"。
2.  前端调用 `GET /api/inspection-records/search` API。
3.  后端返回查询结果列表，前端渲染表格和分页。
4.  用户点击某条记录的"查看详情"按钮。
5.  前端根据该行记录的 `detail_query_type` 和 `id` (工单ID), `product_id_for_detail` (产品ID，如果需要) 构造参数，调用 `GET /api/inspection-records/<id>/detail` API。
6.  后端返回详细数据，前端渲染详情模态框。
7.  用户在模态框中查看信息，可进行打印（打印视图由前端处理，可参考`model.md`）。

## 7. 打印视图
- 遵循 `model.md` 中关于打印视图的设计要求，确保打印布局清晰，包含必要信息（如检验人员签名区域和日期），并隐藏不必要的UI元素。

## 8. 遵循的开发规范
- 所有前后端代码开发均需严格遵守项目 `ruler.md` 文档中定义的规范，包括但不限于命名约定、SQLAlchemy ORM的使用、代码风格、注释标准等。
- 后端逻辑，特别是数据库查询和业务规则处理，应尽可能封装在服务层或相应的工具函数中，保持路由处理函数的简洁性。 