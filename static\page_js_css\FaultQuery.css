/* 完全独立的故障品查询样式 */

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #333;
    background: #f0f2f5;
    line-height: 1.5;
}

/* 容器样式 */
.container {
    margin: 0 !important;
    max-width: none !important;
    padding: 16px;
    width: 100% !important;
    height: calc(100vh - 100px);
    overflow: auto;
}

/* 查询区域样式 */
.query-section {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
    width: 100%;
}

.query-form {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
    margin-bottom: 24px;
    align-items: flex-end;
}

.form-item {
    flex: 1;
    min-width: 180px;
    margin: 0;
}

.form-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.form-input,
.form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    color: #1f2937;
    transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 按钮样式 */
.query-buttons {
    display: flex;
    justify-content: flex-end;  /* 右对齐 */
    gap: 12px;
    margin-top: 24px;
    padding-left: 0;
}

.btn {
    height: 32px;
    padding: 0 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.2s;
    cursor: pointer;
}

.btn-primary {
    background: #1890ff;
    color: white;
    border: none;
}

.btn-default {
    background: white;
    border: 1px solid #d9d9d9;
    color: #666;
}

/* 工具栏样式 */
.toolbar-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 表格样式 */
.table-section {
    background: #fff;
    border-radius: 4px;
    overflow: auto;
    width: 100%;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.table-header th {
    background: #fafafa;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    border-bottom: 1px solid #f0f0f0;
}

.table-row td {
    padding: 12px 16px;
    font-size: 14px;
    border-bottom: 1px solid #f0f0f0;
}

/* 故障品特有的状态标签样式 */
.status-error {
    background-color: #fff1f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
    padding: 2px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}

.status-pending {
    background-color: #fffbe6;
    color: #faad14;
    border: 1px solid #ffe58f;
    padding: 2px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}

.status-processed {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
    padding: 2px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}

/* 分页样式 */
.pagination-section {
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-top: 1px solid #f0f0f0;
}

.page-size {
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.page-size select {
    width: 60px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-button {
    min-width: 32px;
    height: 32px;
    padding: 0 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    white-space: nowrap;
}

.page-button.active {
    background: #1890ff;
    color: white;
    border-color: #1890ff;
}

.page-jump {
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.page-input {
    width: 40px;
    text-align: center;
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
}

/* 导出按钮样式 */
.btn-export {
    background-color: #52c41a;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-export:hover {
    background-color: #389e0d;
}

/* 修改模态框容器样式，保持原有动画效果 */
.fault-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: hidden;
    animation: modalFadeIn 0.2s ease-out;
}

.fault-modal__content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 1200px;
    max-height: 80vh;
    overflow: auto;
    margin: 0;
    display: flex;
    flex-direction: column;
    resize: both;
    min-width: 300px;
    min-height: 200px;
    z-index: 1001;
    animation: contentFadeIn 0.3s ease-out;
}

/* 恢复原有的动画效果 */
@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes contentFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -48%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* 保持原有的故障组动画 */
.fault-modal__group {
    margin-bottom: 20px;
    opacity: 0;
    animation: groupFadeIn 0.5s ease-out forwards;
}

/* 保持原有的故障项动画 */
.fault-modal__fault-item {
    opacity: 0;
    animation: itemFadeIn 0.3s ease-out forwards;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

/* 添加拖动时的样式，但不影响原有动画 */
.fault-modal__content.dragging {
    opacity: 0.95;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    cursor: move;
    user-select: none;
    animation: none;
}

/* 调整header样式，保持原有外观 */
.fault-modal__header {
    cursor: move;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #f7f7f7;
    border-radius: 8px 8px 0 0;
    position: sticky;
    top: 0;
    z-index: 2;
    margin: 0;
}

.fault-modal__title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.fault-modal__title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #1890ff;
}

.fault-modal__close {
    font-size: 24px;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    line-height: 1;
    transition: all 0.2s ease;
    border-radius: 4px;
    user-select: none;
    background: transparent;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.fault-modal__close:hover {
    color: #666;
    background-color: #f3f4f6;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 调整内容区域的padding */
.fault-modal__body {
    padding: 24px;
    padding-top: 0;
}

/* 添加缩放手柄，但确保不影响内容显示 */
.fault-modal__content::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 16px;
    height: 16px;
    cursor: se-resize;
    background: linear-gradient(135deg, transparent 50%, #ddd 50%);
    pointer-events: none;
}

/* 保持原有的故障信息卡片样式不变 */
.fault-modal__card {
    background: #f9fafb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
}

/* 保持原有的动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式布局 */
@media (max-width: 768px) {
    .query-form {
        grid-template-columns: 1fr;
    }
    
    .toolbar-section {
        flex-direction: column;
        gap: 16px;
    }
    
    .pagination-section {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
}

/* 添加无故障时的提示样式 */
.fault-modal__no-error {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 24px;
    background: #f9fafb;
    border-radius: 6px;
    color: #6b7280;
    font-size: 14px;
}

/* 优化故障项显示样式 */
.fault-modal__fault-item--error {
    background: #fff1f0;
    border-color: #ffccc7;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 调整故障列表布局 */
.fault-modal__fault-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    animation: fadeIn 0.3s ease-in-out;
}

/* 故障组样式 */
.fault-modal__group {
    margin-bottom: 20px;
    opacity: 0;
    animation: groupFadeIn 0.5s ease-out forwards;
}

.fault-modal__group-title {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 12px;
    padding-left: 8px;
    border-left: 3px solid #1890ff;
}

.fault-modal__group-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fault-modal__fault-item {
    opacity: 0;
    animation: itemFadeIn 0.3s ease-out forwards;
}

.fault-modal__fault-item--other {
    background: #fff7e6;
    border-color: #ffd591;
}

.fault-modal__fault-item--other .fault-modal__fault-status {
    color: #fa8c16;
}

@keyframes groupFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes itemFadeIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 优化现有样式 */
.fault-modal__fault-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.fault-modal__no-error {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 32px;
    background: #f9fafb;
    border-radius: 8px;
    color: #6b7280;
    font-size: 14px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 添加主题变量 */
#fault-query-content {
    /* 主题变量 - 借鉴自BatchQuery的设计系统 */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;

    /* 添加字体相关变量 */
    font-family: -apple-system, BlinkMacSystemFont, 'Inter', sans-serif;
}

/* 保持原有样式，修改颜色和字体大小 */
#fault-query-content .query-section {
    background-color: hsl(var(--card));
    padding: 24px;
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    margin-bottom: 24px;
}

#fault-query-content .query-form {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
    margin-bottom: 24px;
    align-items: flex-end;
}

#fault-query-content .form-item {
    flex: 1;
    min-width: 180px;
    margin: 0;
}

#fault-query-content .form-label {
    font-size: 14px;
    color: hsl(var(--muted-foreground));
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
}

#fault-query-content .form-input,
#fault-query-content .form-select {
    width: 100%;
    padding: 10px 14px;
    background-color: transparent;
    border: 1px solid hsl(var(--input));
    border-radius: var(--radius);
    font-size: 14px;
    color: hsl(var(--foreground));
    transition: all 0.2s ease;
}

#fault-query-content .form-input:hover,
#fault-query-content .form-select:hover {
    border-color: hsl(var(--ring));
}

#fault-query-content .form-input:focus,
#fault-query-content .form-select:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--ring)/0.1);
}

#fault-query-content .btn {
    font-size: 14px;
    font-weight: 500;
}

#fault-query-content .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
}

#fault-query-content .btn-default {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
}

#fault-query-content .table-header th {
    background-color: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
    border-color: hsl(var(--border));
    font-size: 14px;
    font-weight: 500;
    padding: 12px 16px;
}

#fault-query-content .table-row td {
    border-color: hsl(var(--border));
    color: hsl(var(--foreground));
    font-size: 14px;
    padding: 12px 16px;
}

#fault-query-content .page-button {
    padding: 6px 12px;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--card));  /* 添加默认背景色 */
    color: hsl(var(--foreground));
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

#fault-query-content .page-button:hover:not(:disabled) {
    background-color: hsl(var(--accent));  /* 添加悬停背景色 */
}

#fault-query-content .page-button.active {
    background-color: hsl(var(--primary));  /* 当前页码的背景色 */
    color: hsl(var(--primary-foreground));
    border-color: transparent;
}

#fault-query-content .page-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#fault-query-content .page-input {
    font-size: 14px;
}

/* 其他原有样式保持不变... */ 

/* 保持故障详细信息标题的原有样式 */
.fault-modal__title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;  /* 恢复原来的颜色 */
    margin: 0;
}

/* 优卡片标题样式 */
.fault-modal__subtitle {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;  /* 使用蓝色 */
    margin: 0 0 16px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #e6f4ff;  /* 使用浅蓝色作为基础下划线 */
    position: relative;
}

/* 添加强调下划线 */
.fault-modal__subtitle::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #1890ff;  /* 使用深蓝色短下划线作为强调 */
}

/* 优化关闭按钮样式，增加视觉层次感 */
.fault-modal__close {
    font-size: 24px;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    line-height: 1;
    transition: all 0.2s ease;
    border-radius: 4px;
    user-select: none;
    background: transparent;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.fault-modal__close:hover {
    color: #666;
    background-color: #f3f4f6;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 调整内容区域的padding和第一个卡片的margin */
.fault-modal__body {
    padding: 24px;
    padding-top: 0;
}

/* 调整第一个卡片的margin，增加与标题的间距 */
.fault-modal__body .fault-modal__card:first-child {
    margin-top: 32px;  /* 增加与标题间距 */
}

/* 优化卡片标题样式 */
.fault-modal__subtitle {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
    margin: 0 0 16px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #e6f4ff;
    position: relative;
}

/* 添加强调下划线 */
.fault-modal__subtitle::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #1890ff;
}

/* 提高page-input的选择器优先级 */
#fault-query-content .page-input,
.pagination-section .page-input {
    width: 40px !important;
    text-align: center;
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
}

/* 日期输入框宽度调整 */
#fault-query-content .form-item input[type="date"] {
    width: 100%;
    min-width: 140px;
}

/* 下拉框宽度调整 */
#fault-query-content .form-item select {
    width: 100%;
    min-width: 120px;
}

/* 工单号和SN号输入框宽度调整 */
#fault-query-content #fault-order-number,
#fault-query-content #fault-sn {
    width: 100%;
    min-width: 160px;
}

/* 查询按钮区域调整 */
#fault-query-content .query-buttons {
    display: flex;
    justify-content: flex-end;  /* 恢复原来的右对齐 */
    gap: 12px;
    margin-top: 24px;  /* 恢复原来的上边距 */
    padding-left: 0;  /* 移除之前添加的左内边距 */
}

/* 响应式布局调整 */
@media (max-width: 1200px) {
    #fault-query-content .query-form {
        flex-wrap: wrap;
    }
    
    #fault-query-content .form-item {
        flex: 1 1 auto;
    }
    
    #fault-query-content .query-buttons {
        width: 100%;  /* 在换行时占满宽度 */
        justify-content: flex-end;  /* 保持右对齐 */
        margin-top: 16px;
    }
}

/* 确保输入框样式一致性 */
#fault-query-content .form-input,
#fault-query-content .form-select {
    height: 32px;
    padding: 4px 8px;
    box-sizing: border-box;
}

/* 调整故障类型下拉框的样式 */
#fault-query-content #fault-type {
    text-align: left;  /* 文本左对齐 */
    text-align-last: left;  /* 确保下拉选项也是左对齐 */
    padding-left: 8px;  /* 添加左侧内边距 */
    appearance: none;  /* 移除默认的下拉箭头 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' viewBox='0 0 24 24'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    padding-right: 24px;  /* 为下拉箭头留出空间 */
}

/* 优化响应式布局 */
/* 大屏幕 (1440px及以上) */
@media screen and (min-width: 1440px) {
    #fault-query-content .query-form {
        gap: 24px;  /* 在大屏幕上增加间距 */
    }

    #fault-query-content .form-item {
        min-width: 200px;  /* 增加最小宽度 */
    }
}

/* 中等屏幕 (1200px - 1439px) */
@media screen and (max-width: 1439px) {
    #fault-query-content .query-form {
        gap: 16px;
    }

    #fault-query-content .form-item {
        min-width: 180px;
    }
}

/* 小屏幕 (992px - 1199px) */
@media screen and (max-width: 1199px) {
    #fault-query-content .query-form {
        flex-wrap: wrap;  /* 允许换行 */
        gap: 12px;
    }

    #fault-query-content .form-item {
        flex: 1 1 calc(33.333% - 8px);  /* 三列布局 */
        min-width: 160px;
    }

    #fault-query-content .query-buttons {
        width: 100%;
        justify-content: flex-end;
        margin-top: 16px;
    }
}

/* 平板 (768px - 991px) */
@media screen and (max-width: 991px) {
    #fault-query-content .query-form {
        gap: 10px;
    }

    #fault-query-content .form-item {
        flex: 1 1 calc(50% - 5px);  /* 两列布局 */
    }

    #fault-query-content .form-label {
        font-size: 13px;
    }

    #fault-query-content .form-input,
    #fault-query-content .form-select {
        font-size: 13px;
    }
}

/* 手机 (768px以下) */
@media screen and (max-width: 767px) {
    #fault-query-content .query-section {
        padding: 16px;
    }

    #fault-query-content .query-form {
        gap: 8px;
    }

    #fault-query-content .form-item {
        flex: 1 1 100%;  /* 单列布局 */
        min-width: 0;
    }

    #fault-query-content .query-buttons {
        flex-direction: row;
        gap: 8px;
    }

    #fault-query-content .btn {
        flex: 1;  /* 按钮等宽 */
        padding: 0 12px;
        font-size: 13px;
    }
}

/* 超小屏幕 (480px以下) */
@media screen and (max-width: 479px) {
    #fault-query-content .query-section {
        padding: 12px;
    }

    #fault-query-content .form-item {
        margin-bottom: 4px;
    }

    #fault-query-content .query-buttons {
        margin-top: 12px;
    }

    #fault-query-content .btn {
        padding: 0 8px;
        height: 28px;
        font-size: 12px;
    }
}

/* 优化表格在小屏幕上的显示 */
@media screen and (max-width: 1200px) {
    #fault-query-content .table-section {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;  /* 提供更好的移动端滚动体验 */
    }

    #fault-query-content .data-table {
        min-width: 900px;  /* 确保表格内容不会被压缩 */
    }
}

/* 优化分页控件在小屏幕上的显示 */
@media screen and (max-width: 767px) {
    #fault-query-content .pagination-section {
        flex-direction: column;
        gap: 16px;
        align-items: center;
    }

    #fault-query-content .pagination-controls {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    #fault-query-content .page-size {
        width: 100%;
        justify-content: center;
    }
}

/* 在文件末尾添加总条数样式 */
#fault-query-content .total-count {
    margin-left: 16px;
    color: #666;
    font-size: 14px;
}

/* 调整响应式布局中的总条数显示 */
@media screen and (max-width: 767px) {
    #fault-query-content .total-count {
        margin-left: 8px;
    }
}

/* 在文件末尾添加 Toast 样式 */

/* Toast 容器样式 */
.fault-query__toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.fault-query__toast {
    background: white;
    padding: 12px 24px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.fault-query__toast.show {
    opacity: 1;
    transform: translateY(0);
}

/* Toast 内容样式 */
.fault-query__toast-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    line-height: 1.5;
}

/* Toast 图标样式 */
.fault-query__toast i {
    font-size: 16px;
}

/* Toast 类型样式 */
.fault-query__toast--success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
}

.fault-query__toast--success i {
    color: #52c41a;
}

.fault-query__toast--error {
    background-color: #fff1f0;
    border: 1px solid #ffccc7;
}

.fault-query__toast--error i {
    color: #ff4d4f;
}

.fault-query__toast--warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
}

.fault-query__toast--warning i {
    color: #faad14;
}

.fault-query__toast--info {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
}

.fault-query__toast--info i {
    color: #1890ff;
}

/* 动画效果 */
@keyframes faultToastSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes faultToastSlideOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

.fault-query__toast {
    animation: faultToastSlideIn 0.3s ease forwards;
}

.fault-query__toast.hide {
    animation: faultToastSlideOut 0.3s ease forwards;
}

/* 列设置模态框样式调整 */
.fault-column-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.fault-column-modal__content {
    position: relative;
    top: 5%; /* 从顶部 10% 开始显示 */
    margin: 0 auto;
    width: 90%;
    max-width: 600px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 80vh; /* 最大高度为视窗高度的 80% */
    overflow-y: auto;
}

/* 优化滚动条样式 */
.fault-column-modal__content::-webkit-scrollbar {
    width: 6px;
}

.fault-column-modal__content::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
}

.fault-column-modal__content::-webkit-scrollbar-track {
    background-color: #f5f5f5;
}

/* 确保模态框内容布局合理 */
.fault-column-modal .modal-body {
    max-height: calc(80vh - 120px); /* 减去头部和底部的高度 */
    overflow-y: auto;
    padding: 16px 24px;
}

/* 响应式调整 */
@media screen and (max-height: 768px) {
    .fault-column-modal__content {
        top: 5%;
        max-height: 90vh;
    }
}





