# routes/firmware_all.py
from flask import Blueprint, request, jsonify, current_app, send_file
from models.firmware import Firmware, FirmwareFile, DownloadRecord, ApprovalFlow
from database.db_manager import DatabaseManager
from utils.firmware_utils import FirmwareUtils, create_response, handle_db_error
from sqlalchemy import and_, or_, desc, asc
from datetime import datetime
import logging
import os
from jwt import decode as jwt_decode
from config import JWT_SECRET

logger = logging.getLogger(__name__)
firmware_all_bp = Blueprint('firmware_all', __name__, url_prefix='/api/firmware/all')

# 创建数据库管理器实例
db_manager = DatabaseManager()

@firmware_all_bp.route('/list', methods=['GET'])
def get_firmware_list():
    """获取所有固件列表（分页）"""
    try:
        # 参数获取和验证
        page = max(1, int(request.args.get('page', 1)))
        per_page = min(2000, max(1, int(request.args.get('per_page', 20))))  # 增加最大限制到2000
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status', '').strip()
        sort_by = request.args.get('sort_by', 'create_time')
        sort_order = request.args.get('sort_order', 'desc')

        # 添加获取所有数据的特殊处理
        get_all = request.args.get('get_all', '').lower() == 'true'
        if get_all:
            per_page = 10000  # 设置一个非常大的值来获取所有数据
            logger.info("请求获取所有固件数据，不进行分页限制")
        
        with db_manager.get_session() as session:
            # 构建查询
            query = session.query(Firmware).filter(
                Firmware.is_deleted == False,
                or_(Firmware.status == 'active', Firmware.status == 'rejected')
            )
            
            # 搜索过滤
            if search:
                search_filter = or_(
                    Firmware.serial_number.ilike(f'%{search}%'),
                    Firmware.name.ilike(f'%{search}%'),
                    Firmware.developer.ilike(f'%{search}%'),
                    Firmware.version.ilike(f'%{search}%')
                )
                query = query.filter(search_filter)
            
            # 状态过滤
            if status_filter:
                query = query.filter(Firmware.status == status_filter)
            
            # 排序
            sort_field = getattr(Firmware, sort_by, Firmware.create_time)
            if sort_order.lower() == 'asc':
                query = query.order_by(asc(sort_field))
            else:
                query = query.order_by(desc(sort_field))
            
            # 分页
            total = query.count()
            offset = (page - 1) * per_page
            items = query.offset(offset).limit(per_page).all()
            
            # 转换为字典
            result_items = []
            for item in items:
                item_dict = item.to_dict()
                # 添加额外信息
                file_count = session.query(FirmwareFile).filter_by(
                    serial_number=item.serial_number,
                    is_deleted=False
                ).count()
                item_dict['fileCount'] = file_count
                result_items.append(item_dict)
            
            return create_response(True, '查询成功', {
                'items': result_items,
                'total': total,
                'page': page,
                'per_page': per_page,
                'total_pages': (total + per_page - 1) // per_page
            })
            
    except Exception as e:
        logger.error(f"获取固件列表失败: {str(e)}")
        return handle_db_error(e)

@firmware_all_bp.route('/upload', methods=['POST'])
def upload_firmware():
    """上传固件文件"""
    try:
        # 从token中获取当前用户
        token = request.cookies.get('token')
        try:
            user_data = jwt_decode(token, JWT_SECRET, algorithms=['HS256'])
            uploader_name = user_data.get('username', 'unknown')
        except:
            uploader_name = 'unknown'

        # 获取表单数据
        data = request.form.to_dict()
        
        # 验证必填字段
        required_fields = ['serialNumber', 'name', 'version', 'developer', 'description']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}',
                    'code': 400
                }), 400
        
        logger.info(f"开始上传固件，ERP流水号: {data['serialNumber']}")
        
        # 检查ERP流水号是否已存在
        with db_manager.get_session() as session:
            existing = session.query(Firmware).filter(
                Firmware.serial_number == data['serialNumber'],
                Firmware.is_deleted == False
            ).first()
            
            if existing:
                return jsonify({
                    'success': False,
                    'message': f'ERP流水号 {data["serialNumber"]} 已存在',
                    'code': 409
                }), 409
        
        # 获取上传的文件和客户端Hash
        file = request.files.get('file')
        client_hash = data.get('clientHash')
        
        # 初始化文件相关变量
        file_path = None
        file_hash = None
        file_size = 0
        
        # 如果有文件上传，则处理文件保存
        if file and file.filename:
            logger.info(f"检测到文件上传: {file.filename}")
            if client_hash:
                logger.info(f"客户端Hash: {client_hash}")
            
            try:
                # 保存文件
                file_result = FirmwareUtils.save_uploaded_file(file, data['serialNumber'])
                if isinstance(file_result, tuple) and len(file_result) == 3:
                    file_path, file_hash, file_size = file_result
                    logger.info(f"文件保存成功: path={file_path}, hash={file_hash}, size={file_size}")
                    
                    # Hash值完整性验证（只有当客户端提供了有效Hash时才验证）
                    if client_hash and client_hash != 'null' and file_hash:
                        if client_hash.lower() != file_hash.lower():
                            logger.error(f"Hash验证失败: 客户端={client_hash}, 服务器={file_hash}")
                            # 删除已保存的文件
                            if os.path.exists(file_path):
                                os.remove(file_path)
                            return jsonify({
                                'success': False,
                                'message': 'Hash验证失败，文件在传输过程中可能已损坏',
                                'code': 400
                            }), 400
                        else:
                            logger.info("Hash验证通过：客户端与服务器Hash一致")
                    elif not client_hash or client_hash == 'null':
                        logger.info("跳过Hash验证：客户端环境不支持Hash计算")
                    else:
                        logger.warning(f"Hash验证异常：client_hash={client_hash}, file_hash={file_hash}")
                    
                else:
                    logger.error(f"文件保存返回格式错误: {file_result}")
                    return jsonify({
                        'success': False,
                        'message': '文件保存失败，返回格式错误',
                        'code': 500
                    }), 500
            except Exception as e:
                logger.error(f"文件保存失败: {str(e)}")
                return jsonify({
                    'success': False,
                    'message': f'文件保存失败: {str(e)}',
                    'code': 500
                }), 500
        else:
            logger.info("没有检测到文件上传，创建空文件记录")
        
        with db_manager.get_session() as session:
            # 处理适用产品信息
            products = []
            products_text = data.get('productsText', '')
            if products_text:
                product_models = [p.strip() for p in products_text.split(',') if p.strip()]
                for i, model in enumerate(product_models):
                    products.append({
                        'code': f"AUTO_{data['serialNumber'][-6:]}_{i+1:02d}",
                        'model': model
                    })
            
            # 创建固件记录
            firmware = Firmware(
                serial_number=data['serialNumber'],
                name=data['name'],
                version=data['version'],
                developer=data['developer'],
                description=data.get('description', ''),
                version_requirements=data.get('versionRequirements', ''),
                products=products,  # 存储为JSON
                status='pending',
                uploader=uploader_name,
                # 新增技术规格字段
                build_time=data.get('buildTime', ''),
                backplane_version=data.get('backplaneVersion', ''),
                io_version=data.get('ioVersion', '')
            )
            session.add(firmware)
            session.flush()  # 获取ID
            
            # 只有当有文件时才创建文件记录
            if file_path:
                firmware_file = FirmwareFile(
                    serial_number=data['serialNumber'],
                    file_path=file_path,
                    original_filename=file.filename,
                    file_hash=file_hash,
                    file_size=file_size
                )
                session.add(firmware_file)
            
            session.commit()
            logger.info(f"固件 {data['serialNumber']} 上传成功")
        
        return jsonify({
            'success': True,
            'message': '固件上传成功，已进入审核流程',
            'code': 200,
            'data': {
                'serialNumber': data['serialNumber'],
                'name': data['name'],
                'version': data['version'],
                'hasFile': file_path is not None
            }
        })
        
    except Exception as e:
        logger.error(f"上传固件失败: {str(e)}")
        
        # 特殊处理文件大小超限错误
        if "文件大小超限" in str(e):
            return jsonify({
                'success': False,
                'message': f'文件大小超限，当前最大支持500MB',
                'code': 413
            }), 413
        elif "413" in str(e) or "Request Entity Too Large" in str(e):
            return jsonify({
                'success': False,
                'message': '文件过大，当前最大支持500MB，请压缩文件后重试',
                'code': 413
            }), 413
        
        return handle_db_error(e)

@firmware_all_bp.route('/<serial_number>/update', methods=['POST'])
def update_firmware(serial_number):
    """更新固件信息（仅限审核退回的固件）"""
    try:
        data = request.form.to_dict()
        file = request.files.get('file')
        token = request.cookies.get('token')
        try:
            user_data = jwt_decode(token, JWT_SECRET, algorithms=['HS256'])
            operator_name = user_data.get('username', 'unknown')
        except:
            operator_name = 'unknown'

        with db_manager.get_session() as session:
            # 查找固件
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '固件不存在', code=404)
            
            if firmware.status != 'rejected':
                return create_response(False, '只能修改审核退回的固件', code=400)
            
            # 更新基本信息
            firmware.name = data.get('name', firmware.name)
            firmware.version = data.get('version', firmware.version)
            firmware.developer = data.get('developer', firmware.developer)
            firmware.description = data.get('description', firmware.description)
            firmware.version_requirements = data.get('versionRequirements', firmware.version_requirements)
            # 更新技术规格字段
            firmware.build_time = data.get('buildTime', firmware.build_time)
            firmware.backplane_version = data.get('backplaneVersion', firmware.backplane_version)
            firmware.io_version = data.get('ioVersion', firmware.io_version)
            
            # 更新适用产品
            if data.get('productsText'):
                products = []
                product_models = [p.strip() for p in data['productsText'].split(',') if p.strip()]
                for i, model in enumerate(product_models):
                    products.append({
                        'code': f"P{serial_number[-3:]}_{i+1:02d}",
                        'model': model
                    })
                firmware.products = products
            
            # 如果有新文件，更新文件
            if file:
                is_valid, message = FirmwareUtils.validate_file(file)
                if not is_valid:
                    return create_response(False, message, code=400)
                
                # 保存新文件
                file_path, file_hash, file_size = FirmwareUtils.save_uploaded_file(file, serial_number)
                
                # 查找现有文件记录
                firmware_file = session.query(FirmwareFile).filter_by(
                    serial_number=serial_number,
                    is_deleted=False
                ).first()
                
                if firmware_file:
                    # 删除旧文件
                    if os.path.exists(firmware_file.file_path):
                        os.remove(firmware_file.file_path)
                    
                    # 更新文件记录
                    firmware_file.file_path = file_path
                    firmware_file.original_filename = file.filename
                    firmware_file.file_hash = file_hash
                    firmware_file.file_size = file_size
                    firmware_file.upload_time = datetime.now()
                else:
                    # 如果没有现有文件记录，创建新的文件记录
                    firmware_file = FirmwareFile(
                        serial_number=serial_number,
                        file_path=file_path,
                        original_filename=file.filename,
                        file_hash=file_hash,
                        file_size=file_size
                    )
                    session.add(firmware_file)
            
            # 重新提交审核
            firmware.status = 'pending'
            firmware.update_time = datetime.now()
            
            # 添加审核流水记录
            approval_flow = ApprovalFlow(
                serial_number=serial_number,
                action='submit',
                operator_name=operator_name,
                notes='修改后重新提交审核'
            )
            session.add(approval_flow)
            
            session.commit()
            
            logger.info(f"固件修改成功: {serial_number}")
            return create_response(True, '固件修改成功，已重新提交审核')
            
    except Exception as e:
        logger.error(f"更新固件失败: {str(e)}")
        return handle_db_error(e)

@firmware_all_bp.route('/<serial_number>/version-update', methods=['POST'])
def create_version_update(serial_number):
    """创建版本更新"""
    try:
        data = request.form.to_dict()
        file = request.files.get('file')
        token = request.cookies.get('token')
        try:
            user_data = jwt_decode(token, JWT_SECRET, algorithms=['HS256'])
            uploader_name = user_data.get('username', 'unknown')
        except:
            uploader_name = 'unknown'

        # 数据验证
        is_valid, message = FirmwareUtils.validate_firmware_data(data)
        if not is_valid:
            return create_response(False, message, code=400)

        # 移除强制文件要求，文件变为可选
        # if not file:
        #     return create_response(False, '请选择固件文件', code=400)

        # 如果有文件才进行文件验证
        if file and file.filename:
            is_valid, message = FirmwareUtils.validate_file(file)
            if not is_valid:
                return create_response(False, message, code=400)

        with db_manager.get_session() as session:
            # 检查父版本是否存在（使用URL中的serial_number作为父版本）
            parent_firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                is_deleted=False
            ).first()

            if not parent_firmware:
                return create_response(False, '父版本不存在', code=404)

            # 检查新流水号是否已存在
            new_serial_number = data['serialNumber']
            existing = session.query(Firmware).filter_by(
                serial_number=new_serial_number,
                is_deleted=False
            ).first()

            if existing:
                return create_response(False, '新固件流水号已存在', code=400)

            # 立即作废父版本（新逻辑：创建新版本时立即作废父版本）
            if parent_firmware.status == 'active':
                parent_firmware.status = 'obsolete'
                parent_firmware.obsolete_time = datetime.now()
                parent_firmware.update_time = datetime.now()
                
                # 同时将父版本的文件记录标记为删除（保持数据一致性）
                parent_files = session.query(FirmwareFile).filter_by(
                    serial_number=serial_number,
                    is_deleted=False
                ).all()
                
                for parent_file in parent_files:
                    parent_file.is_deleted = True
                
                # 记录父版本作废流水
                parent_approval = ApprovalFlow(
                    serial_number=serial_number,
                    action='obsolete',
                    operator_name=uploader_name,
                    notes=f'因版本升级({new_serial_number})而立即作废，文件记录同步标记删除'
                )
                session.add(parent_approval)
                logger.info(f"父版本 {serial_number} 因新版本创建而立即作废，同时标记 {len(parent_files)} 个文件记录为删除")

            # 初始化文件相关变量
            file_path = None
            file_hash = None
            file_size = 0

            # 如果有文件上传，才保存文件
            if file and file.filename:
                logger.info(f"准备调用save_uploaded_file, file: {file.filename}, serial: {data['serialNumber']}")
                file_path, file_hash, file_size = FirmwareUtils.save_uploaded_file(
                    file, new_serial_number
                )
                logger.info(f"save_uploaded_file返回值: file_path={file_path}, file_hash={file_hash}, file_size={file_size}")
                logger.info(f"file_path类型: {type(file_path)}, file_hash类型: {type(file_hash)}, file_size类型: {type(file_size)}")
            else:
                logger.info("没有检测到文件上传，创建空文件记录")

            # 解析适用产品
            products = []
            if data.get('productsText'):
                product_models = [p.strip() for p in data['productsText'].split(',') if p.strip()]
                for i, model in enumerate(product_models):
                    products.append({
                        'code': f"P{new_serial_number[-3:]}_{i+1:02d}",
                        'model': model
                    })

            # 创建新版本固件记录
            new_firmware = Firmware(
                serial_number=new_serial_number,
                name=data['name'],
                version=data['version'],
                developer=data['developer'],
                uploader=uploader_name,
                products=products,
                version_requirements=data.get('versionRequirements', ''),
                description=data['description'],
                status='pending',
                source='升级',
                parent_sn=serial_number,  # 使用URL中的serial_number作为父版本
                # 新增技术规格字段
                build_time=data.get('buildTime', ''),
                backplane_version=data.get('backplaneVersion', ''),
                io_version=data.get('ioVersion', '')
            )
            session.add(new_firmware)

            # 只有当有文件时才创建文件记录
            if file_path:
                firmware_file = FirmwareFile(
                    serial_number=new_serial_number,
                    file_path=file_path,
                    original_filename=file.filename,
                    file_hash=file_hash,
                    file_size=file_size
                )
                session.add(firmware_file)

            # 创建审核流水记录
            approval_flow = ApprovalFlow(
                serial_number=new_serial_number,
                action='submit',
                operator_name=uploader_name,
                notes=f'基于{serial_number}创建版本更新'
            )
            session.add(approval_flow)

            session.commit()

            logger.info(f"版本更新创建成功: {new_serial_number}")
            return create_response(True, '版本更新提交成功，已提交审核')

    except Exception as e:
        logger.error(f"创建版本更新失败: {str(e)}")
        return handle_db_error(e)

@firmware_all_bp.route('/<serial_number>/download', methods=['POST'])
def download_firmware(serial_number):
    """记录使用信息（修改后：不下载文件，只保存使用记录）"""
    try:
        data = request.get_json()
        token = request.cookies.get('token')
        try:
            user_data = jwt_decode(token, JWT_SECRET, algorithms=['HS256'])
            user_name = user_data.get('username', 'unknown')
        except:
            user_name = 'unknown'
        
        with db_manager.get_session() as session:
            # 查找固件
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                status='active',
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '固件不存在或未生效', code=404)
            
            # 处理构建时间：直接存储用户输入的文本
            build_time_input = data.get('buildTime', '')

            # 创建使用记录（不检查文件是否存在）
            download_record = DownloadRecord(
                serial_number=serial_number,
                work_order=data.get('workOrder', ''),
                product_code=data.get('productCode', ''),
                product_model=data.get('productModel', ''),
                burn_count=int(data.get('burnCount', 0)),
                software_version=data.get('softwareVersion', ''),
                build_time=build_time_input,  # 直接存储用户输入的文本
                backplane_version=data.get('backplaneVersion', ''),
                io_version=data.get('highSpeedIOVersion', ''),
                user_name=user_name,
                notes=data.get('notes', '')
            )
            session.add(download_record)
            
            # 只增加使用计数，不增加下载计数
            firmware.usage_count += int(data.get('burnCount', 0))
            firmware.update_time = datetime.now()
            
            session.commit()
            
            # 只返回成功响应，不返回下载链接
            return create_response(True, '使用记录保存成功', {
                'recordId': download_record.id,
                'serialNumber': serial_number,
                'usageCount': firmware.usage_count
            })
            
    except Exception as e:
        logger.error(f"保存使用记录失败: {str(e)}")
        return handle_db_error(e)

@firmware_all_bp.route('/file/<serial_number>', methods=['GET'])
def get_firmware_file(serial_number):
    """获取固件文件（直接下载，增加下载计数）"""
    try:
        with db_manager.get_session() as session:
            # 查找固件
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                status='active',
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '固件不存在或未生效', code=404)
            
            # 查找文件
            firmware_file = session.query(FirmwareFile).filter_by(
                serial_number=serial_number,
                is_deleted=False
            ).first()
            
            if not firmware_file or not os.path.exists(firmware_file.file_path):
                return create_response(False, '固件文件不存在', code=404)
            
            # 增加下载计数
            firmware.download_count += 1
            firmware.update_time = datetime.now()
            session.commit()
            
            # 创建响应，包含文件Hash信息
            response = send_file(
                firmware_file.file_path,
                as_attachment=True,
                download_name=firmware_file.original_filename
            )
            
            # 在响应头中添加文件Hash和其他信息，供前端验证
            response.headers['X-File-Hash'] = firmware_file.file_hash
            response.headers['X-File-Size'] = str(firmware_file.file_size)
            response.headers['X-Serial-Number'] = serial_number
            response.headers['X-Original-Filename'] = firmware_file.original_filename
            
            # 确保Content-Disposition头正确设置（防止某些情况下Flask未正确设置）
            # 使用更兼容的格式，支持中文文件名
            import urllib.parse
            encoded_filename = urllib.parse.quote(firmware_file.original_filename.encode('utf-8'))
            response.headers['Content-Disposition'] = f'attachment; filename="{firmware_file.original_filename}"; filename*=UTF-8\'\'{encoded_filename}'
            
            return response
            
    except Exception as e:
        logger.error(f"获取固件文件失败: {str(e)}")
        return handle_db_error(e)

@firmware_all_bp.route('/<serial_number>/history', methods=['GET'])
def get_version_history(serial_number):
    """获取固件版本历史"""
    try:
        with db_manager.get_session() as session:
            # 查找当前固件
            current_firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                is_deleted=False
            ).first()
            
            if not current_firmware:
                return create_response(False, '固件不存在', code=404)
            
            # 构建版本链
            version_chain = []
            
            # 向前追溯（父版本）
            current = current_firmware
            while current:
                current_dict = current.to_dict()
                if current.approve_time and current.obsolete_time:
                    current_dict['effectiveDays'] = (current.obsolete_time - current.approve_time).days
                else:
                    current_dict['effectiveDays'] = None
                version_chain.append(current_dict)
                if current.parent_sn:
                    current = session.query(Firmware).filter_by(
                        serial_number=current.parent_sn,
                        is_deleted=False
                    ).first()
                else:
                    break
            
            # 向后追溯（子版本）
            children = session.query(Firmware).filter_by(
                parent_sn=serial_number,
                is_deleted=False
            ).all()
            
            for child in children:
                child_dict = child.to_dict()
                if child.approve_time and child.obsolete_time:
                    child_dict['effectiveDays'] = (child.obsolete_time - child.approve_time).days
                else:
                    child_dict['effectiveDays'] = None
                version_chain.append(child_dict)
            
            # 按时间排序
            version_chain.sort(key=lambda x: x['createTime'], reverse=True)
            
            return create_response(True, '查询成功', {'history': version_chain})
            
    except Exception as e:
        logger.error(f"获取版本历史失败: {str(e)}")
        return handle_db_error(e)

@firmware_all_bp.route('/<serial_number>', methods=['GET'])
def get_firmware_detail(serial_number):
    """获取固件详细信息"""
    try:
        with db_manager.get_session() as session:
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '固件不存在', code=404)
            
            # 获取详细信息
            result = firmware.to_dict()
            
            # 获取文件信息
            files = session.query(FirmwareFile).filter_by(
                serial_number=serial_number,
                is_deleted=False
            ).all()
            result['files'] = [f.to_dict() for f in files]
            
            # 获取审核流水
            approval_flows = session.query(ApprovalFlow).filter_by(
                serial_number=serial_number
            ).order_by(desc(ApprovalFlow.create_time)).all()
            result['approvalFlows'] = [a.to_dict() for a in approval_flows]
            
            return create_response(True, '查询成功', result)
            
    except Exception as e:
        logger.error(f"获取固件详情失败: {str(e)}")
        return handle_db_error(e)

@firmware_all_bp.route('/export', methods=['GET'])
def export_firmware_list():
    """导出固件列表"""
    try:
        # 获取所有生效和退回的固件
        firmware_list = Firmware.query.filter(
            Firmware.is_deleted == False,
            Firmware.status.in_(['active', 'rejected'])
        ).order_by(Firmware.create_time.desc()).all()
        
        # 转换为Excel格式
        export_data = []
        for firmware in firmware_list:
            data = firmware.to_dict()
            export_data.append({
                'ERP流水号': data['serialNumber'],
                '固件名称': data['name'],
                '软件版本': data['version'],
                '状态': data['status'],
                '来源类型': data['source'],
                '适用产品': ', '.join([p.get('model', '') for p in data['products']]),
                '研发者': data['developer'],
                '上传者': data['uploader'],
                '审核者': data['approverName'] or '',
                '下载次数': data['downloadCount'],
                '使用次数': data['usageCount'],
                '创建时间': data['createTime'],
                '生效时间': data['approveTime'] or '',
                '变更内容': data['description']
            })
        
        # 导出Excel
        file_path = FirmwareUtils.export_to_excel(export_data, '所有固件列表')
        
        if file_path:
            return jsonify({
                'code': 200,
                'message': '导出成功',
                'data': {'file_path': file_path}
            })
        else:
            return jsonify({'code': 500, 'message': '导出失败'}), 500
        
    except Exception as e:
        logger.error(f"导出固件列表失败: {str(e)}")
        return jsonify({'code': 500, 'message': '导出失败'}), 500

@firmware_all_bp.route('/<serial_number>/delete', methods=['DELETE'])
def delete_firmware(serial_number):
    """删除固件（软删除 + 物理文件清理）"""
    try:
        token = request.cookies.get('token')
        try:
            user_data = jwt_decode(token, JWT_SECRET, algorithms=['HS256'])
            operator_name = user_data.get('username', 'unknown')
        except:
            operator_name = 'unknown'
        with db_manager.get_session() as session:
            # 查找固件
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '固件不存在', code=404)
            
            # 安全检查：只能删除rejected状态的固件
            if firmware.status != 'rejected':
                return create_response(False, '只能删除审核退回的固件', code=400)
            
            # 软删除固件记录
            firmware.is_deleted = True
            firmware.update_time = datetime.now()
            
            # 软删除对应的文件记录
            firmware_files = session.query(FirmwareFile).filter_by(
                serial_number=serial_number,
                is_deleted=False
            ).all()
            
            file_paths = []
            for file_record in firmware_files:
                file_record.is_deleted = True
                file_paths.append(file_record.file_path)
            
            # 记录删除操作到审核流水
            approval_flow = ApprovalFlow(
                serial_number=serial_number,
                action='obsolete',  # 使用obsolete作为删除操作的标记
                operator_name=operator_name,
                notes='固件已被删除（软删除）'
            )
            session.add(approval_flow)
            
            session.commit()
            
            # 删除物理文件（在数据库提交后进行，即使失败也不影响数据库操作）
            deleted_files = []
            failed_files = []
            for file_path in file_paths:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        deleted_files.append(file_path)
                        logger.info(f"物理文件删除成功: {file_path}")
                except Exception as e:
                    failed_files.append(file_path)
                    logger.warning(f"物理文件删除失败: {file_path}, 错误: {str(e)}")
            
            result_message = f'固件删除成功'
            if failed_files:
                result_message += f'，但有{len(failed_files)}个文件删除失败'
            
            logger.info(f"固件删除完成: {serial_number}, 删除文件: {len(deleted_files)}个")
            return create_response(True, result_message, {
                'serialNumber': serial_number,
                'deletedFiles': len(deleted_files),
                'failedFiles': len(failed_files)
            })
            
    except Exception as e:
        logger.error(f"删除固件失败: {str(e)}")
        return handle_db_error(e) 