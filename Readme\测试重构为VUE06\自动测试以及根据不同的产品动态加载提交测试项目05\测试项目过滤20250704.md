# 成品测试查询 — 测试项目过滤逻辑说明（2025-07-04）

> 适用文件：`static/page_js_css/ProductTestQuery.js`

## 1. 过滤目的

成品测试查询页面需要根据不同产品类型（CPU 控制器 / 耦合器 / IO 模块）在**明细模态框**中只展示与之相关且有效的测试项目，避免无意义或无数据的项占据界面与导出结果。

## 2. 数据来源

1. 当用户双击 SN 号或点击"详情"按钮时，前端调用
   ```js
   showProductTestDetail(serialNumber)
   ```
2. 该函数通过 `fetch('/api/product-test-query/detail/:sn')` 获取后端返回的 JSON：
   ```json
   {
     "success": true,
     "data": {
       "productType": "CPU控制器", // 或 "耦合器" / "IO模块"
       "testResults": { "backplane": 1, "netPort": 0, ... },
       ... 其他字段
     }
   }
   ```
3. 请求成功后，结果被传入 `updateModalContent(result)` 用于渲染模态框。

## 3. 过滤核心代码

```js
const testResults = result.data.testResults || {};
let testItemsToShow = Object.entries(testResults);

if (result.productType === 'IO模块') {
  // ① 去掉与 IO 模块无关的 netPort、ledTube
  // ② 隐藏值为 0 的测试项
  testItemsToShow = testItemsToShow.filter(([key, value]) =>
    key !== 'netPort' &&
    key !== 'ledTube' &&
    value !== 0
  );
} else if (result.productType === 'CPU控制器') {
  // CPU 仅隐藏值为 0 的测试项
  testItemsToShow = testItemsToShow.filter(([, value]) => value !== 0);
} else if (result.productType === '耦合器') {
  // 耦合器同样仅隐藏值为 0 的测试项
  testItemsToShow = testItemsToShow.filter(([, value]) => value !== 0);
}
```

## 4. 渲染流程

1. 经过过滤的 `testItemsToShow` 会被 `map` 成网格 HTML：
   ```html
   <div class="product-test-query__test-item">
     <label class="product-test-query__test-label">项目名</label>
     <span class="product-test-query__test-value--pass|fail">通过/不通过</span>
   </div>
   ```
2. 项目名通过 `formatTestItemName(key)` 转换为中文。
3. 值等于 `1/true` → "通过"，否则为 "不通过"。

## 5. 导出 CSV 时的同步过滤

在 `exportProductTestData()` 中同样按照 `productType` 动态构造 `testResultsConfig`：

* IO 模块：事先移除了 `netPort`、`ledTube`，再导出。
* CPU 控制器 / 耦合器：使用全量配置，但导出函数内部仍会读取真实值；因为前面对 **testResults** 已经在详情数据中按值为 0 过滤，导出时不会出现隐藏项。

这样保证"界面显示"与"CSV 导出"保持一致。

## 6. 扩展与维护

1. **新增测试项**：
   - 在后端 `testResults` 返回中加入键值。
   - 前端 `formatTestItemName()` 添加中文映射。
   - 若仅适用于特定产品类型，则在上述过滤逻辑中排除不相关产品。
2. **新增产品类型**：
   - 在 switch / if 分支中添加新的过滤规则。
   - 更新设备信息渲染与导出配置。

## 7. 结论

通过简单的数组 `filter` 逻辑，即实现了基于产品类型和测试结果的双重过滤：

| 产品类型 | 去除与类型无关的项 | 隐藏值为 0 的项 |
|-----------|--------------------|------------------|
| CPU 控制器 | 无                | ✔️ |
| 耦合器     | 无                | ✔️ |
| IO 模块    | `netPort`, `ledTube` | ✔️ |

这样既保证明细信息的精准性，也避免了用户因无效数据产生疑惑。 