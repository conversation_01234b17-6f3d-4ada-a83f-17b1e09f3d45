/* 基础样式 */
.container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 1.5rem;
    background-color: hsl(var(--background));
    box-sizing: border-box;
}

/* 可以删除大部分样式，只保留一些特殊的样式覆盖或补充 */
.text-center {
    text-align: center;
}

.text-primary {
    color: #3b82f6;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.d-none {
    display: none;
}

/* 警告提示样式 */
.alert {
    border-radius: var(--radius);
    padding: 1rem;
    margin-bottom: 1rem;
    animation: fadeIn 0.2s ease-out;
}

.alert-success {
    background-color: hsl(142.1 76.2% 36.3% / 0.1);
    border: 1px solid hsl(142.1 76.2% 36.3% / 0.2);
    color: hsl(142.1 76.2% 36.3%);
}

.alert-danger {
    background-color: hsl(var(--destructive) / 0.1);
    border: 1px solid hsl(var(--destructive) / 0.2);
    color: hsl(var(--destructive));
}

.alert .btn-close {
    background: none;
    border: none;
    color: currentColor;
    padding: 0.25rem;
    cursor: pointer;
}

/* 扫码绑定系统样式 */
.scan-container {
    margin-bottom: 20px;
}

.scan-group {
    display: none;
    margin: 20px 0;
}

.scan-item {
    margin: 10px 0;
}

/* 修改 scan-button 样式 */
.scan-button {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.scan-button:hover {
    opacity: 0.9;
}

.scan-button:disabled {
    background-color: hsl(var(--muted)) !important;
    color: hsl(var(--muted-foreground));
    cursor: not-allowed;
}

.scan-button.danger {
    background-color: hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
}

.scan-button.search {
    background-color: hsl(var(--primary));
}

.section {
    width: 100%;
    max-width: 100%;
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    padding: 1.5rem 1.5rem 1.5rem 2rem;
    margin: 1.5rem 0;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    box-sizing: border-box;
}

.bound-info {
    border: 1px solid hsl(var(--border));
    background-color: hsl(var(--card));
    border-radius: var(--radius);
    padding: 1rem;
    margin: 0.75rem 0;
    transition: all 0.2s;
}

.bound-info:hover {
    border-color: hsl(var(--ring));
    background-color: hsl(var(--accent));
}

.flex-container {
    display: flex;
    gap: 20px;
}

.flex-item {
    flex: 1;
}

.board-group {
    margin: 10px 0;
    padding: 10px;
    border: 1px solid #ddd;
    background-color: #fff;
    border-radius: 4px;
}

.section h3 {
    position: relative;
    color: hsl(var(--foreground));
    font-size: 20px;
    font-weight: 600;
    padding: 16px 0;
    margin-bottom: 24px;
    border: none;
    display: flex;
    align-items: center;
}

.section h3::before {
    content: '';
    position: absolute;
    left: -1.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(to bottom, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    border-radius: 2px;
    transition: height 0.3s ease;
}

.section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: rgba(0, 0, 0, 0.7);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.section h3:hover::before {
    height: 32px;
}

.status {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: hsl(var(--accent));
    border-radius: var(--radius);
    line-height: 1.8;
}

.status > div {
    margin-bottom: 0.25rem;
}

.status > div:last-child {
    margin-bottom: 0;
}

.status .success {
    color: #4CAF50;
    font-weight: 500;
}

.status .pending {
    color: #FFA500;
    font-weight: 500;
}

.success {
    color: #4CAF50;
}

.pending {
    color: #FFA500;
}

.search-group {
    display: flex;
    align-items: center;
    gap: 10px;
    height: 40px; /* 固定高度 */
}

.search-group input {
    flex: 1;
    height: 100%;
    padding: 0 10px;
}

.search-group .scan-button {
    height: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap; /* 防止文字换行 */
}

.action-buttons {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

.select-all {
    margin: 10px 0;
    padding: 5px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .flex-container {
        flex-direction: column;
    }
    
    .flex-item {
        width: 100%;
    }
}

/* 添加根变量，与 FaultEntry.css 保持一致 */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

/* 添加动画效果 */
@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(5px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* 添加板子选择列表的样式 */
.board-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* 板子选项样式 */
.board-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: background-color 0.2s;
}

.board-item:hover {
    background-color: hsl(var(--accent));
}

/* 复选框容器样式 */
.barcode-binding-page .checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 复选框样式 - 使用浏览器默认样式 */
.barcode-binding-page input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
    cursor: pointer;
}

/* 标签样式 */
.barcode-binding-page .board-item label {
    cursor: pointer;
    font-size: 0.95rem;
    color: hsl(var(--foreground));
    user-select: none;
}

/* 全选样式 */
.barcode-binding-page .select-all {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    margin-bottom: 1rem;
    background-color: hsl(var(--accent));
    border-radius: var(--radius);
}

/* 标签页导航样式 */
.tabs-nav {
    display: flex;
    border-bottom: 1px solid hsl(var(--border));
    margin-bottom: 1rem;
}

.tab-button {
    padding: 0.75rem 1.5rem;
    border: none;
    background: none;
    cursor: pointer;
    font-weight: 500;
    font-size: 16px;
    color: hsl(var(--muted-foreground));
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.tab-button:hover {
    color: hsl(var(--foreground));
}

.tab-button.active {
    color: hsl(var(--foreground));
    border-bottom-color: hsl(var(--primary));
    font-weight: 600;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

/* 修改表单网格布局，使用grid-template-areas实现特定布局 */
.barcode-binding-page .basic-info-card .form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    width: 100%;
    box-sizing: border-box;
}

/* 每个字段占据一列,按照正确顺序排列 */
.barcode-binding-page .basic-info-card .form-group[data-field="tester"] {
    grid-column: 1 / 2;
}

.barcode-binding-page .basic-info-card .form-group[data-field="orderNumber"] {
    grid-column: 2 / 3;
}

.barcode-binding-page .basic-info-card .form-group[data-field="productionQuantity"] {
    grid-column: 3 / 4;
}

.barcode-binding-page .basic-info-card .form-group[data-field="productCode"] {
    grid-column: 1 / 2;
}

.barcode-binding-page .basic-info-card .form-group[data-field="productModel"] {
    grid-column: 2 / 3;
}

.barcode-binding-page .basic-info-card .form-group[data-field="memo"] {
    grid-column: 3 / 4;
}

/* 表单组基础样式 */
.barcode-binding-page .basic-info-card .form-group {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
}

/* 标签样式 */
.barcode-binding-page .basic-info-card .form-group label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

/* 必填字段的标签样式 */
.barcode-binding-page .basic-info-card .form-group[data-field="orderNumber"] label,
.barcode-binding-page .basic-info-card .form-group[data-field="productionQuantity"] label,
.barcode-binding-page .basic-info-card .form-group[data-field="productCode"] label,
.barcode-binding-page .basic-info-card .form-group[data-field="productModel"] label {
    position: relative;
}

.barcode-binding-page .basic-info-card .form-group[data-field="orderNumber"] label::after,
.barcode-binding-page .basic-info-card .form-group[data-field="productionQuantity"] label::after,
.barcode-binding-page .basic-info-card .form-group[data-field="productCode"] label::after,
.barcode-binding-page .basic-info-card .form-group[data-field="productModel"] label::after {
    content: '*';
    color: hsl(var(--destructive));
    margin-left: 4px;
}

/* 输入框基础样式 */
.barcode-binding-page .basic-info-card input[type="text"],
.barcode-binding-page .basic-info-card input[type="number"],
.barcode-binding-page .basic-info-card select {
    height: 2.5rem;
    padding: 0.5rem 0.75rem;
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
    border: 2px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 输入框悬浮状态 */
.barcode-binding-page .basic-info-card input[type="text"]:hover,
.barcode-binding-page .basic-info-card input[type="number"]:hover,
.barcode-binding-page .basic-info-card select:hover {
    border-color: hsl(var(--border-hover, var(--border)));
    background-color: hsl(var(--background-hover, var(--background)));
}

/* 输入框焦点状态 */
.barcode-binding-page .basic-info-card input[type="text"]:focus,
.barcode-binding-page .basic-info-card input[type="number"]:focus,
.barcode-binding-page .basic-info-card select:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* 只读输入框样式 */
.barcode-binding-page .basic-info-card input[readonly] {
    background-color: hsl(var(--muted));
    border-color: hsl(var(--border));
    cursor: not-allowed;
}

/* 错误状态样式 */
.barcode-binding-page .basic-info-card input.is-invalid {
    border-color: hsl(var(--destructive));
}

.barcode-binding-page .basic-info-card input.is-invalid:focus {
    box-shadow: 0 0 0 2px hsl(var(--destructive) / 0.2);
}

/* 响应式布局调整 */
@media (max-width: 1400px) {
    .barcode-binding-page .basic-info-card .form-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .barcode-binding-page .basic-info-card .form-group {
        grid-column: auto !important;
    }
}

@media (max-width: 768px) {
    .barcode-binding-page .basic-info-card .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .barcode-binding-page .basic-info-card .form-group {
        grid-column: auto !important;
    }
}

@media (max-width: 480px) {
    .barcode-binding-page .basic-info-card .form-grid {
        grid-template-columns: 1fr;
    }
}

/* 添加查询区域样式 */
.query-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
}

.query-input-group {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.query-input-group input {
    flex: 1;
}

.query-result {
    margin-top: 1rem;
    padding: 1rem;
    background-color: hsl(var(--accent));
    border-radius: var(--radius);
}

.result-content {
    display: grid;
    gap: 0.75rem;
    margin-top: 0.75rem;
}

.result-item {
    display: flex;
    gap: 1rem;
}

.result-item .label {
    font-weight: 500;
    min-width: 100px;
}

/* 查询按钮样式 */
.query-input-group .scan-button {
    white-space: nowrap;
    padding: 0 1.5rem;
}

/* 外壳绑定区域样式 */
.shell-binding-section {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid hsl(var(--border));
}

.binding-input-group {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.binding-input-group input {
    flex: 1;
}

.binding-input-group .scan-button {
    white-space: nowrap;
    padding: 0 1.5rem;
}

/* 绑定信息显示样式 */
.bound-boards {
    margin-bottom: 1rem;
}

.shell-info {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid hsl(var(--border));
}

.shell-info .label {
    font-weight: 500;
    margin-right: 1rem;
}

/* 状态信息区域样式 */
.status-info {
    margin: 1rem 0;
    padding: 1rem;
    background-color: hsl(var(--accent));
    border-radius: var(--radius);
}

.status-info > div {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.status-info > div:last-child {
    margin-bottom: 0;
}

.order-info, .assembly-time, .shell-time {
    color: hsl(var(--foreground));
    font-size: 0.95rem;
}

.status {
    font-weight: 500;
}

.status .success {
    color: #4CAF50;
}

.status .pending {
    color: #FFA500;
}

/* 添加自动绑定开关样式 */
.auto-bind-toggle {
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.toggle-label {
    font-size: 0.95rem;
    color: hsl(var(--foreground));
}

/* 标题样式 */
h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
}

h3 {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 16px;
}

h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

/* 表单文样式 */
.form-group label {
    font-size: 14px;
    font-weight: 400;
    color: var(--text-secondary);
}

/* 输入框文字 */
input[type="text"],
input[type="number"],
input[type="datetime-local"],
select {
    font-size: 14px;
}

/* 按钮文字 */
.scan-button {
    font-size: 14px;
    font-weight: 500;
}

/* 状态文字 */
.status {
    font-size: 14px;
}

.status .success,
.status .pending {
    font-weight: 500;
}

/* 查询结果文字 */
.result-item {
    font-size: 14px;
}

.result-item .label {
    font-weight: 500;
}

/* 辅助文字 */
.toggle-label,
.select-all label {
    font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    h2 {
        font-size: 22px;
    }
    
    h3 {
        font-size: 18px;
    }
    
    h4 {
        font-size: 15px;
    }
}

/* 统一所有扫码输入框样式 */
.scan-item input,
.query-input-group input,
.binding-input-group input,
#rebindSN,
#shellSN {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid hsl(var(--border));
    border-radius: var(--radius);
    font-size: 14px;
    transition: all 0.2s ease;
    position: relative;
    padding-right: 2.5rem; /* 为√预留空间 */
}

.scan-item input:hover,
.query-input-group input:hover,
.binding-input-group input:hover,
#rebindSN:hover,
#shellSN:hover {
    border-color: hsl(var(--ring) / 0.5);
}

.scan-item input:focus,
.query-input-group input:focus,
.binding-input-group input:focus,
#rebindSN:focus,
#shellSN:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.1);
}

/* 统一输入框容器样式 */
.scan-item,
.query-input-group,
.binding-input-group,
.search-group {
    position: relative;
    margin: 10px 0;
}

/* 修改完成标记样式，只针对扫码输入框 */
.scan-item.completed::after {
    content: '✓';
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #4CAF50;
    font-size: 1.2rem;
    font-weight: bold;
    opacity: 0;
    animation: checkmark 0.3s ease forwards;
}

/* 修改完成状态样式，只针对扫码输入框 */
.scan-item.completed input {
    border-color: #4CAF50;
    background-color: hsl(var(--background));
}

/* 调整按钮位置 */
.query-input-group .scan-button,
.binding-input-group .scan-button,
.search-group .scan-button {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
}

/* √标记动画 */
@keyframes checkmark {
    from {
        opacity: 0;
        transform: translateY(-50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
}

/* 修改查询区域和外壳绑定区域的标签字体大小 */
.barcode-binding-page .query-section label,
.barcode-binding-page .shell-binding-section label {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
    display: block;
}

/* 保外壳SN标签的样式一致 */
.barcode-binding-page .shell-info .label {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

/* 修改基本信息区域的标签和输入框字体大小 */
.barcode-binding-page .basic-info-card .form-group label {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.barcode-binding-page .basic-info-card input[type="text"],
.barcode-binding-page .basic-info-card input[type="number"],
.barcode-binding-page .basic-info-card input[type="datetime-local"],
.barcode-binding-page .basic-info-card select {
    font-size: 16px;
    padding: 0.75rem 1rem;
    width: 100%;
}

/* 基础变量定义 */
:root {
    --font-h2: clamp(1.375rem, 1.25rem + 0.625vw, 1.5rem);
    --font-h3: clamp(1.25rem, 1.125rem + 0.625vw, 1.375rem);
    --font-h4: clamp(1rem, 0.875rem + 0.625vw, 1.125rem);
    --font-base: clamp(0.875rem, 0.8125rem + 0.3125vw, 1rem);
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --card-padding: clamp(1rem, 0.875rem + 0.625vw, 1.5rem);
}

/* 基本信息卡片样式 */
.barcode-binding-page .basic-info-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        inset 0 2px 4px rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

/* 表单网格布局 */
.barcode-binding-page .basic-info-card .form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    width: 100%;
    box-sizing: border-box;
}

/* 表单组样式 */
.barcode-binding-page .basic-info-card .form-group {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
}

/* 明确指定每个字段的位置 */
.barcode-binding-page .basic-info-card .form-group[data-field="tester"] { grid-column: 1; }
.barcode-binding-page .basic-info-card .form-group[data-field="orderNumber"] { grid-column: 2; }
.barcode-binding-page .basic-info-card .form-group[data-field="productionQuantity"] { grid-column: 3; }
.barcode-binding-page .basic-info-card .form-group[data-field="productCode"] { grid-column: 1; }
.barcode-binding-page .basic-info-card .form-group[data-field="productModel"] { grid-column: 2; }
.barcode-binding-page .basic-info-card .form-group[data-field="memo"] { grid-column: 3; }

/* 响应式布局调整 */
@media (max-width: 1400px) {
    .barcode-binding-page .basic-info-card .form-grid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
    .barcode-binding-page .basic-info-card .form-group {
        grid-column: auto !important;
    }
}

@media (max-width: 768px) {
    .barcode-binding-page .basic-info-card .form-grid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    .barcode-binding-page .basic-info-card .form-group {
        grid-column: auto !important;
    }
}

@media (max-width: 480px) {
    .barcode-binding-page .basic-info-card .form-grid {
        grid-template-columns: minmax(0, 1fr);
    }
}

/* 添加主标签页样式 */
.main-tabs-nav {
    display: flex;
    border-bottom: 2px solid hsl(var(--border));
    margin-bottom: 1.5rem;
    background: hsl(var(--background));
    position: sticky;
    top: 0;
    z-index: 10;
}

.main-tab-button {
    padding: 1rem 2rem;
    border: none;
    background: none;
    cursor: pointer;
    font-weight: 500;
    font-size: 1.1rem;
    color: hsl(var(--muted-foreground));
    border-bottom: 3px solid transparent;
    transition: all 0.2s;
    margin-bottom: -2px;
}

.main-tab-button:hover {
    color: hsl(var(--foreground));
    background-color: hsl(var(--accent));
}

.main-tab-button.active {
    color: hsl(var(--foreground));
    border-bottom-color: hsl(var(--primary));
    font-weight: 600;
    background-color: hsl(var(--accent));
}

.main-tab-content {
    display: none;
}

.main-tab-content.active {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

/* 修改 section 样式以适应新布局 */
.section {
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
}

/* 修改flex-container相关样式 */
.flex-container {
    display: block;
    gap: 0;
}

.flex-item {
    flex: none;
    width: 100%;
}

/* 确保内容区域占满屏幕 */
.scan-container {
    margin: -1.5rem;
    padding: 1.5rem;
    background-color: hsl(var(--background));
}

/* 调整标签页内容的边距 */
.main-tab-content .section {
    padding: 2rem;
}

/* 添加水平布局样式 */
.horizontal-layout {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

.horizontal-layout .form-group {
    display: flex;
    flex-direction: column;
    margin: 0;
}

.horizontal-layout .half-width {
    flex: 1;
}

.horizontal-layout .form-group label {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.horizontal-layout .form-group input {
    height: 2.5rem;
    padding: 0.75rem 1rem;
    border: 2px solid hsl(var(--border));
    border-radius: var(--radius);
    font-size: 16px;
    transition: all 0.2s ease;
}

.horizontal-layout .form-group input:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .horizontal-layout {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .horizontal-layout .half-width {
        flex: none;
        width: 100%;
    }
}
