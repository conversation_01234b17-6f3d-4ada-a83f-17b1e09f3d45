#!/bin/bash

# KMLC PLC 测试系统管理脚本
# 用途：提供系统管理、监控和维护的常用功能
# 作者：[作者名]
# 版本：1.0.0

# 加载配置
PROJECT_NAME="kmlc_plc"
PROJECT_PATH="/var/www/${PROJECT_NAME}"
LOG_PATH="${PROJECT_PATH}/logs"
DB_NAME="kmlc_plc"
DB_USER="root"
DB_PASSWORD="kmlc@3302133"
DB_HOST="*************"
DB_PORT="3309"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查root权限
check_root() {
    if [ "$(id -u)" != "0" ]; then
        print_message "$RED" "错误: 此脚本需要root权限运行"
        exit 1
    fi
}

# 显示菜单
show_menu() {
    clear
    echo "=================================="
    echo "   KMLC PLC 测试系统管理工具"
    echo "=================================="
    echo "1. 查看系统信息"
    echo "2. 查看服务状态"
    echo "3. 管理服务"
    echo "4. 查看日志"
    echo "5. 数据库管理"
    echo "6. 系统维护"
    echo "7. 性能监控"
    echo "8. 备份管理"
    echo "0. 退出"
    echo "=================================="
}

# 添加系统信息查看函数
check_system_info() {
    echo "=================================="
    echo "        系统信息"
    echo "=================================="
    
    # 操作系统信息
    echo -e "\n[操作系统信息]"
    echo "------------------------"
    lsb_release -a 2>/dev/null || cat /etc/os-release
    
    # CPU信息
    echo -e "\n[CPU信息]"
    echo "------------------------"
    echo "CPU型号:"
    cat /proc/cpuinfo | grep "model name" | head -n 1 | cut -d ":" -f2
    echo "CPU核心数:"
    nproc
    echo "CPU使用率:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4}' | awk '{print $1"%"}'
    
    # 内存信息
    echo -e "\n[内存信息]"
    echo "------------------------"
    free -h | grep -v +
    
    # Python版本
    echo -e "\n[Python版本]"
    echo "------------------------"
    echo "系统Python版本:"
    python3 --version
    echo "虚拟环境Python版本:"
    if [ -f "${VENV_PATH}/bin/python" ]; then
        ${VENV_PATH}/bin/python --version
    else
        echo "虚拟环境未激活或不存在"
    fi
    
    # MySQL版本
    echo -e "\n[MySQL版本]"
    echo "------------------------"
    mysql --version
    
    # 磁盘使用情况
    echo -e "\n[磁盘使用情况]"
    echo "------------------------"
    df -h | grep -v "tmpfs" | grep -v "udev"
    
    # 系统运行时间
    echo -e "\n[系统运行时间]"
    echo "------------------------"
    uptime
    
    # 服务状态
    echo -e "\n[关键服务状态]"
    echo "------------------------"
    echo "MySQL状态: $(systemctl is-active mysql)"
    echo "Supervisor状态: $(systemctl is-active supervisor)"
    
    # 网络信息
    echo -e "\n[网络信息]"
    echo "------------------------"
    echo "监听端口:"
    netstat -tulpn | grep -E ":(${SERVER_PORT}|${DB_PORT})" | awk '{print $4}'
    
    # 应用版本信息
    echo -e "\n[应用信息]"
    echo "------------------------"
    if [ -f "${PROJECT_PATH}/VERSION" ]; then
        echo "应用版本: $(cat ${PROJECT_PATH}/VERSION)"
    else
        echo "应用版本文件不存在"
    fi
    
    # Python包信息
    echo -e "\n[主要Python包版本]"
    echo "------------------------"
    if [ -f "${VENV_PATH}/bin/pip" ]; then
        ${VENV_PATH}/bin/pip freeze | grep -E "flask|sqlalchemy|gunicorn|gevent"
    else
        echo "虚拟环境未激活或不存在"
    fi
}

# 查看服务状态
check_service_status() {
    echo "正在检查服务状态..."
    echo "----------------------------"
    echo "1. Supervisor 状态:"
    supervisorctl status kmlc_plc
    echo "----------------------------"
    echo "2. MySQL 状态:"
    systemctl status mysql --no-pager
    echo "----------------------------"
    echo "3. 进程状态:"
    ps aux | grep -E "gunicorn|mysql" | grep -v grep
    echo "----------------------------"
    echo "4. 端口监听状态:"
    netstat -tulpn | grep -E "3308|3309"
}

# 服务管理菜单
manage_services() {
    while true; do
        echo "1. 启动所有服务"
        echo "2. 停止所有服务"
        echo "3. 重启所有服务"
        echo "4. 重启应用服务"
        echo "5. 重启数据库"
        echo "0. 返回主菜单"
        
        read -p "请选择操作 [0-5]: " choice
        case $choice in
            1)
                systemctl start mysql
                supervisorctl start kmlc_plc
                print_message "$GREEN" "所有服务已启动"
                ;;
            2)
                supervisorctl stop kmlc_plc
                systemctl stop mysql
                print_message "$GREEN" "所有服务已停止"
                ;;
            3)
                supervisorctl stop kmlc_plc
                systemctl restart mysql
                supervisorctl start kmlc_plc
                print_message "$GREEN" "所有服务已重启"
                ;;
            4)
                supervisorctl restart kmlc_plc
                print_message "$GREEN" "应用服务已重启"
                ;;
            5)
                systemctl restart mysql
                print_message "$GREEN" "数据库已重启"
                ;;
            0)
                return
                ;;
            *)
                print_message "$RED" "无效选择"
                ;;
        esac
    done
}

# 查看日志
view_logs() {
    while true; do
        echo "1. 查看应用日志"
        echo "2. 查看Supervisor错误日志"
        echo "3. 查看MySQL错误日志"
        echo "4. 搜索错误日志"
        echo "0. 返回主菜单"
        
        read -p "请选择操作 [0-4]: " choice
        case $choice in
            1)
                tail -f "${PROJECT_PATH}/app.log"
                ;;
            2)
                tail -f "${LOG_PATH}/supervisor_stderr.log"
                ;;
            3)
                tail -f /var/log/mysql/error.log
                ;;
            4)
                read -p "请输入搜索关键词: " keyword
                echo "搜索应用日志..."
                grep "$keyword" "${PROJECT_PATH}/app.log"
                echo "搜索Supervisor日志..."
                grep "$keyword" "${LOG_PATH}/supervisor_stderr.log"
                ;;
            0)
                return
                ;;
            *)
                print_message "$RED" "无效选择"
                ;;
        esac
    done
}

# 数据库管理
manage_database() {
    while true; do
        echo "1. 备份数据库"
        echo "2. 检查数据库状态"
        echo "3. 优化数据库"
        echo "0. 返回主菜单"
        
        read -p "请选择操作 [0-3]: " choice
        case $choice in
            1)
                # 首先确保备份目录存在
                backup_path="${PROJECT_PATH}/backups"
                if [ ! -d "$backup_path" ]; then
                    mkdir -p "$backup_path"
                    chown -R www-data:www-data "$backup_path"
                    chmod 755 "$backup_path"
                fi

                backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
                backup_full_path="${backup_path}/${backup_file}"

                # 执行备份
                if mysqldump -u "$DB_USER" -p"$DB_PASSWORD" -h "$DB_HOST" -P "$DB_PORT" "$DB_NAME" > "$backup_full_path"; then
                    chown www-data:www-data "$backup_full_path"
                    chmod 644 "$backup_full_path"
                    print_message "$GREEN" "数据库已成功备份到: $backup_full_path"
                else
                    print_message "$RED" "数据库备份失败"
                fi
                ;;
            2)
                if mysqlcheck -u "$DB_USER" -p"$DB_PASSWORD" -h "$DB_HOST" -P "$DB_PORT" "$DB_NAME"; then
                    print_message "$GREEN" "数据库状态检查完成"
                else
                    print_message "$RED" "数据库状态检查失败"
                fi
                ;;
            3)
                if mysqlcheck -u "$DB_USER" -p"$DB_PASSWORD" -h "$DB_HOST" -P "$DB_PORT" --optimize "$DB_NAME"; then
                    print_message "$GREEN" "数据库优化完成"
                else
                    print_message "$RED" "数据库优化失败"
                fi
                ;;
            0)
                return
                ;;
            *)
                print_message "$RED" "无效选择"
                ;;
        esac

        echo
        read -p "按回车键继续..."
    done
}

# 系统维护
system_maintenance() {
    while true; do
        echo "1. 清理日志文件"
        echo "2. 清理临时文件"
        echo "3. 检查磁盘空间"
        echo "4. 检查系统负载"
        echo "0. 返回主菜单"
        
        read -p "请选择操作 [0-4]: " choice
        case $choice in
            1)
                find "$LOG_PATH" -name "*.log.*" -mtime +30 -delete
                print_message "$GREEN" "已清理30天前的日志文件"
                ;;
            2)
                find "${PROJECT_PATH}" -name "*.pyc" -delete
                find "${PROJECT_PATH}" -name "__pycache__" -type d -exec rm -rf {} +
                print_message "$GREEN" "已清理临时文件"
                ;;
            3)
                df -h
                ;;
            4)
                top -n 1 -b
                ;;
            0)
                return
                ;;
            *)
                print_message "$RED" "无效选择"
                ;;
        esac
    done
}

# 性能监控
monitor_performance() {
    while true; do
        echo "1. 查看CPU使用率"
        echo "2. 查看内存使用"
        echo "3. 查看网络连接"
        echo "4. 查看进程数量"
        echo "0. 返回主菜单"
        
        read -p "请选择操作 [0-4]: " choice
        case $choice in
            1)
                top -n 1 -b | head -n 12
                ;;
            2)
                free -h
                ;;
            3)
                netstat -an | grep -E "3308|3309" | wc -l
                ;;
            4)
                echo "Gunicorn进程数："
                ps aux | grep gunicorn | grep -v grep | wc -l
                ;;
            0)
                return
                ;;
            *)
                print_message "$RED" "无效选择"
                ;;
        esac
    done
}

# 备份管理
manage_backups() {
    while true; do
        echo "1. 创建完整备份"
        echo "2. 列出现有备份"
        echo "3. 清理旧备份"
        echo "0. 返回主菜单"
        
        read -p "请选择操作 [0-3]: " choice
        case $choice in
            1)
                backup_dir="${PROJECT_PATH}/backups/full_backup_$(date +%Y%m%d_%H%M%S)"
                mkdir -p "$backup_dir"
                cp -r "${PROJECT_PATH}"/* "$backup_dir/"
                mysqldump -u "$DB_USER" -p"$DB_PASSWORD" -h "$DB_HOST" -P "$DB_PORT" "$DB_NAME" > "$backup_dir/database.sql"
                print_message "$GREEN" "完整备份已创建: $backup_dir"
                ;;
            2)
                ls -lh "${PROJECT_PATH}/backups/"
                ;;
            3)
                find "${PROJECT_PATH}/backups/" -mtime +30 -delete
                print_message "$GREEN" "已清理30天前的备份"
                ;;
            0)
                return
                ;;
            *)
                print_message "$RED" "无效选择"
                ;;
        esac
    done
}

# 主程序
main() {
    check_root
    
    while true; do
        show_menu
        read -p "请选择操作 [0-8]: " choice
        case $choice in
            1) check_system_info ;;
            2) check_service_status ;;
            3) manage_services ;;
            4) view_logs ;;
            5) manage_database ;;
            6) system_maintenance ;;
            7) monitor_performance ;;
            8) manage_backups ;;
            0) 
                print_message "$GREEN" "感谢使用，再见！"
                exit 0 
                ;;
            *)
                print_message "$RED" "无效选择"
                ;;
        esac
        
        echo
        read -p "按回车键继续..."
    done
}

# 启动主程序
main 