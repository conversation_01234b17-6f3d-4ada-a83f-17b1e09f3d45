# routes/firmware_obsolete.py - 简化版本
from flask import Blueprint, request, jsonify
from models.firmware import Firmware, ApprovalFlow
from database.db_manager import DatabaseManager
from utils.firmware_utils import create_response, handle_db_error
from sqlalchemy import and_, or_, desc, asc, func
from datetime import datetime, timedelta
import logging

obsolete_bp = Blueprint('firmware_obsolete', __name__, url_prefix='/api/firmware/obsolete')
logger = logging.getLogger(__name__)
db_manager = DatabaseManager()

@obsolete_bp.route('/list', methods=['GET'])
def get_obsolete_list():
    """获取作废固件列表（分页）"""
    try:
        # 参数获取和验证
        page = max(1, int(request.args.get('page', 1)))
        per_page = min(2000, max(1, int(request.args.get('per_page', 20))))  # 增加最大限制到2000
        search = request.args.get('search', '').strip()
        reason = request.args.get('reason', '').strip()  # 作废原因过滤
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()
        sort_by = request.args.get('sort_by', 'obsolete_time')
        sort_order = request.args.get('sort_order', 'desc')

        # 添加获取所有数据的特殊处理
        get_all = request.args.get('get_all', '').lower() == 'true'
        if get_all:
            per_page = 10000  # 设置一个非常大的值来获取所有数据
            logger.info("请求获取所有作废固件数据，不进行分页限制")
        
        with db_manager.get_session() as session:
            # 构建查询
            query = session.query(Firmware).filter(
                Firmware.is_deleted == False,
                Firmware.status == 'obsolete'
            )
            
            # 搜索过滤
            if search:
                search_filter = or_(
                    Firmware.serial_number.ilike(f'%{search}%'),
                    Firmware.name.ilike(f'%{search}%'),
                    Firmware.version.ilike(f'%{search}%'),
                    Firmware.developer.ilike(f'%{search}%'),
                    Firmware.uploader.ilike(f'%{search}%')
                )
                query = query.filter(search_filter)
            
            # 作废日期范围过滤
            if start_date:
                try:
                    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(Firmware.obsolete_time >= start_dt)
                except ValueError:
                    return create_response(False, '开始日期格式错误', code=400)
            
            if end_date:
                try:
                    end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                    query = query.filter(Firmware.obsolete_time < end_dt)
                except ValueError:
                    return create_response(False, '结束日期格式错误', code=400)
            
            # 排序
            sort_field = getattr(Firmware, sort_by, Firmware.obsolete_time)
            if sort_order.lower() == 'asc':
                query = query.order_by(asc(sort_field))
            else:
                query = query.order_by(desc(sort_field))
            
            # 分页
            total = query.count()
            offset = (page - 1) * per_page
            items = query.offset(offset).limit(per_page).all()
            
            # 转换为字典格式并添加额外信息
            result_items = []
            for item in items:
                item_dict = item.to_dict()
                
                # 计算生效天数
                if item.approve_time and item.obsolete_time:
                    active_days = (item.obsolete_time - item.approve_time).days
                    item_dict['activeDays'] = max(0, active_days)
                else:
                    item_dict['activeDays'] = 0
                
                # 获取作废原因（从审核流水中获取）
                obsolete_flow = session.query(ApprovalFlow).filter_by(
                    serial_number=item.serial_number,
                    action='obsolete'
                ).order_by(desc(ApprovalFlow.create_time)).first()
                
                item_dict['obsoleteReason'] = obsolete_flow.notes if obsolete_flow else '未知'
                item_dict['obsoleteOperator'] = obsolete_flow.operator_name if obsolete_flow else '未知'
                
                # 获取子版本信息
                children = session.query(Firmware).filter_by(
                    parent_sn=item.serial_number,
                    is_deleted=False
                ).all()
                item_dict['hasChildren'] = len(children) > 0
                item_dict['childrenCount'] = len(children)
                
                result_items.append(item_dict)
            
            return create_response(True, '查询成功', {
                'items': result_items,
                'total': total,
                'page': page,
                'per_page': per_page,
                'total_pages': (total + per_page - 1) // per_page
            })
            
    except Exception as e:
        logger.error(f"获取作废固件列表失败: {str(e)}")
        return handle_db_error(e)

@obsolete_bp.route('/obsolete', methods=['POST'])
def make_firmware_obsolete():
    """手动作废固件"""
    try:
        data = request.get_json()
        serial_number = data.get('serialNumber')
        operator_name = data.get('operatorName', 'unknown')
        reason = data.get('reason', '')
        
        if not serial_number:
            return create_response(False, '流水号不能为空', code=400)
        
        if not reason:
            return create_response(False, '作废理由不能为空', code=400)
        
        with db_manager.get_session() as session:
            # 查找活跃状态的固件
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                status='active',
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '固件不存在或状态不允许作废', code=404)
            
            # 更新固件状态
            firmware.status = 'obsolete'
            firmware.obsolete_time = datetime.now()
            firmware.update_time = datetime.now()
            
            # 创建审核流水记录
            approval_flow = ApprovalFlow(
                serial_number=serial_number,
                action='obsolete',
                operator_name=operator_name,
                notes=reason
            )
            session.add(approval_flow)
            
            session.commit()
            
            logger.info(f"固件手动作废: {serial_number} by {operator_name}, 理由: {reason}")
            return create_response(True, '固件作废成功')
            
    except Exception as e:
        logger.error(f"作废固件失败: {str(e)}")
        return handle_db_error(e)

@obsolete_bp.route('/batch-obsolete', methods=['POST'])
def batch_obsolete_firmware():
    """批量作废固件"""
    try:
        data = request.get_json()
        serial_numbers = data.get('serialNumbers', [])
        operator_name = data.get('operatorName', 'unknown')
        reason = data.get('reason', '')
        
        if not serial_numbers:
            return create_response(False, '请选择要作废的固件', code=400)
        
        if not reason:
            return create_response(False, '作废理由不能为空', code=400)
        
        success_count = 0
        failed_items = []
        
        with db_manager.get_session() as session:
            for serial_number in serial_numbers:
                try:
                    # 查找活跃状态的固件
                    firmware = session.query(Firmware).filter_by(
                        serial_number=serial_number,
                        status='active',
                        is_deleted=False
                    ).first()
                    
                    if not firmware:
                        failed_items.append({'serialNumber': serial_number, 'reason': '固件不存在或状态不允许作废'})
                        continue
                    
                    # 更新固件状态
                    firmware.status = 'obsolete'
                    firmware.obsolete_time = datetime.now()
                    firmware.update_time = datetime.now()
                    
                    # 创建审核流水记录
                    approval_flow = ApprovalFlow(
                        serial_number=serial_number,
                        action='obsolete',
                        operator_name=operator_name,
                        notes=reason
                    )
                    session.add(approval_flow)
                    
                    success_count += 1
                    
                except Exception as e:
                    failed_items.append({'serialNumber': serial_number, 'reason': str(e)})
                    continue
            
            session.commit()
            
            result_message = f'批量作废完成，成功: {success_count}个'
            if failed_items:
                result_message += f'，失败: {len(failed_items)}个'
            
            logger.info(f"批量作废完成: 成功{success_count}个，失败{len(failed_items)}个")
            return create_response(True, result_message, {
                'successCount': success_count,
                'failedItems': failed_items
            })
            
    except Exception as e:
        logger.error(f"批量作废失败: {str(e)}")
        return handle_db_error(e)

@obsolete_bp.route('/detail/<serial_number>', methods=['GET'])
def get_obsolete_detail(serial_number):
    """获取作废固件详细信息"""
    try:
        with db_manager.get_session() as session:
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                status='obsolete',
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '作废固件不存在', code=404)
            
            # 获取基本信息
            result = firmware.to_dict()
            
            # 计算生效天数
            if firmware.approve_time and firmware.obsolete_time:
                active_days = (firmware.obsolete_time - firmware.approve_time).days
                result['activeDays'] = max(0, active_days)
            else:
                result['activeDays'] = 0
            
            # 获取审核流水
            approval_flows = session.query(ApprovalFlow).filter_by(
                serial_number=serial_number
            ).order_by(desc(ApprovalFlow.create_time)).all()
            result['approvalFlows'] = [a.to_dict() for a in approval_flows]
            
            # 获取作废信息
            obsolete_flow = session.query(ApprovalFlow).filter_by(
                serial_number=serial_number,
                action='obsolete'
            ).order_by(desc(ApprovalFlow.create_time)).first()
            
            if obsolete_flow:
                result['obsoleteInfo'] = {
                    'reason': obsolete_flow.notes,
                    'operator': obsolete_flow.operator_name,
                    'time': obsolete_flow.create_time.strftime('%Y-%m-%d %H:%M:%S')
                }
            
            # 获取父版本信息
            if firmware.parent_sn:
                parent_firmware = session.query(Firmware).filter_by(
                    serial_number=firmware.parent_sn,
                    is_deleted=False
                ).first()
                if parent_firmware:
                    result['parentInfo'] = parent_firmware.to_dict()
            
            # 获取子版本信息
            children = session.query(Firmware).filter_by(
                parent_sn=serial_number,
                is_deleted=False
            ).all()
            result['children'] = [child.to_dict() for child in children]
            
            return create_response(True, '查询成功', result)
            
    except Exception as e:
        logger.error(f"获取作废固件详情失败: {str(e)}")
        return handle_db_error(e)

@obsolete_bp.route('/stats', methods=['GET'])
def get_obsolete_stats():
    """获取作废固件统计信息"""
    try:
        # 时间范围参数
        days = int(request.args.get('days', 30))  # 默认最近30天
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()
        
        with db_manager.get_session() as session:
            # 构建时间过滤条件
            query_filter = [
                Firmware.is_deleted == False,
                Firmware.status == 'obsolete'
            ]
            
            if start_date and end_date:
                # 使用指定日期范围
                try:
                    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                    end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                    query_filter.extend([
                        Firmware.obsolete_time >= start_dt,
                        Firmware.obsolete_time < end_dt
                    ])
                    date_range = f"{start_date} 至 {end_date}"
                except ValueError:
                    return create_response(False, '日期格式错误', code=400)
            else:
                # 使用最近N天
                start_dt = datetime.now() - timedelta(days=days)
                query_filter.append(Firmware.obsolete_time >= start_dt)
                date_range = f"最近{days}天"
            
            # 基础统计
            base_query = session.query(Firmware).filter(and_(*query_filter))
            
            total_obsolete = base_query.count()
            
            # 按作废原因统计
            reason_stats = {}
            obsolete_flows = session.query(ApprovalFlow).filter_by(
                action='obsolete'
            ).all()
            
            for flow in obsolete_flows:
                # 检查对应的固件是否在时间范围内
                firmware = session.query(Firmware).filter_by(
                    serial_number=flow.serial_number,
                    status='obsolete',
                    is_deleted=False
                ).first()
                
                if firmware and firmware.obsolete_time:
                    # 检查是否在时间范围内
                    in_range = True
                    if start_date and end_date:
                        try:
                            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                            end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                            in_range = start_dt <= firmware.obsolete_time < end_dt
                        except:
                            pass
                    elif days:
                        start_dt = datetime.now() - timedelta(days=days)
                        in_range = firmware.obsolete_time >= start_dt
                    
                    if in_range:
                        reason = flow.notes or '未知原因'
                        reason_stats[reason] = reason_stats.get(reason, 0) + 1
            
            # 按开发者统计
            developer_stats = session.query(
                Firmware.developer,
                func.count(Firmware.serial_number).label('count')
            ).filter(and_(*query_filter)).group_by(
                Firmware.developer
            ).order_by(desc('count')).limit(10).all()
            
            developer_details = [
                {
                    'developer': stat.developer,
                    'obsoleteCount': stat.count
                }
                for stat in developer_stats
            ]
            
            # 按来源类型统计
            source_stats = session.query(
                Firmware.source,
                func.count(Firmware.serial_number).label('count')
            ).filter(and_(*query_filter)).group_by(
                Firmware.source
            ).all()
            
            source_details = {stat.source: stat.count for stat in source_stats}
            
            # 平均生效天数统计
            active_days_list = []
            for firmware in base_query.all():
                if firmware.approve_time and firmware.obsolete_time:
                    days = (firmware.obsolete_time - firmware.approve_time).days
                    active_days_list.append(max(0, days))
            
            avg_active_days = sum(active_days_list) / len(active_days_list) if active_days_list else 0
            
            return create_response(True, '查询成功', {
                'dateRange': date_range,
                'summary': {
                    'totalObsolete': total_obsolete,
                    'averageActiveDays': round(avg_active_days, 1)
                },
                'reasonStats': reason_stats,
                'developerStats': developer_details,
                'sourceStats': source_details
            })
            
    except Exception as e:
        logger.error(f"获取作废固件统计失败: {str(e)}")
        return handle_db_error(e)

@obsolete_bp.route('/export', methods=['GET'])
def export_obsolete_list():
    """导出作废固件列表"""
    try:
        # 获取查询参数
        search = request.args.get('search', '').strip()
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()
        
        with db_manager.get_session() as session:
            # 构建查询
            query = session.query(Firmware).filter(
                Firmware.is_deleted == False,
                Firmware.status == 'obsolete'
            )
            
            # 应用过滤条件
            if search:
                search_filter = or_(
                    Firmware.serial_number.ilike(f'%{search}%'),
                    Firmware.name.ilike(f'%{search}%'),
                    Firmware.version.ilike(f'%{search}%'),
                    Firmware.developer.ilike(f'%{search}%')
                )
                query = query.filter(search_filter)
            
            if start_date:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(Firmware.obsolete_time >= start_dt)
            
            if end_date:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                query = query.filter(Firmware.obsolete_time < end_dt)
            
            # 获取所有记录
            firmwares = query.order_by(desc(Firmware.obsolete_time)).all()
            
            # 准备导出数据
            export_data = []
            for firmware in firmwares:
                # 获取作废原因
                obsolete_flow = session.query(ApprovalFlow).filter_by(
                    serial_number=firmware.serial_number,
                    action='obsolete'
                ).order_by(desc(ApprovalFlow.create_time)).first()
                
                # 计算生效天数
                active_days = 0
                if firmware.approve_time and firmware.obsolete_time:
                    active_days = (firmware.obsolete_time - firmware.approve_time).days
                
                row_data = {
                    'ERP流水号': firmware.serial_number,
                    '固件名称': firmware.name,
                    '版本号': firmware.version,
                    '来源类型': firmware.source,
                    '适用产品': ', '.join([p.get('model', '') for p in firmware.products or []]),
                    '研发者': firmware.developer,
                    '上传者': firmware.uploader,
                    '生效时间': firmware.approve_time.strftime('%Y-%m-%d %H:%M:%S') if firmware.approve_time else '',
                    '作废时间': firmware.obsolete_time.strftime('%Y-%m-%d %H:%M:%S') if firmware.obsolete_time else '',
                    '生效天数': max(0, active_days),
                    '作废原因': obsolete_flow.notes if obsolete_flow else '未知',
                    '作废操作人': obsolete_flow.operator_name if obsolete_flow else '未知',
                    '下载次数': firmware.download_count,
                    '使用次数': firmware.usage_count,
                    '版本使用要求': firmware.version_requirements or '',
                    '变更内容': firmware.description or ''
                }
                export_data.append(row_data)
            
            # 这里可以调用导出工具生成Excel文件
            # file_path = FirmwareUtils.export_to_excel(export_data, '作废固件列表')
            
            return create_response(True, f'共导出 {len(export_data)} 条记录', {
                'recordCount': len(export_data),
                'data': export_data[:100]  # 仅返回前100条预览
            })
            
    except Exception as e:
        logger.error(f"导出作废固件列表失败: {str(e)}")
        return handle_db_error(e) 