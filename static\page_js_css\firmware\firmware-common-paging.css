/* 固件管理模块通用分页样式 */
/* 适用于所有固件管理页面的分页组件 */

/* ===== 分页容器样式 ===== */
.firmware-container .table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    background: white;
}

.firmware-container .table-length {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    color: #4b5563;
    font-size: 0.875rem;
}

.firmware-container .page-size-selector {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.firmware-container .page-size-selector select {
    margin: 0 0.25rem;
    padding: 0.25rem 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
    cursor: pointer;
}

.firmware-container .page-size-selector select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.firmware-container .total-count {
    color: #4b5563;
    font-size: 0.875rem;
}

.firmware-container .total-count span {
    font-weight: 600;
    color: #1f2937;
}

/* ===== 分页导航样式 ===== */
.firmware-container .table-pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.firmware-container .pagination-pages {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* ===== 分页按钮样式 ===== */
.firmware-container .btn-icon {
    padding: 0.375rem 0.5rem;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
    color: #374151;
    font-size: 0.875rem;
    line-height: 1.25rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.firmware-container .btn-icon:hover:not(:disabled) {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.firmware-container .btn-icon:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    opacity: 0.6;
}

.firmware-container .btn-page {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
    color: #374151;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.firmware-container .btn-page:hover:not(:disabled) {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.firmware-container .btn-page:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    opacity: 0.6;
}

.firmware-container .pagination-ellipsis {
    padding: 0.375rem 0.5rem;
    color: #4b5563;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

/* ===== 不同页面的主题色适配 ===== */

/* 作废固件页面 - 红色主题 */
.obsolete-firmware .btn-page.active {
    background-color: #f56c6c;
    color: #ffffff;
    border-color: #f56c6c;
}

/* 待审核固件页面 - 橙色主题 */
.pending-firmware .btn-page.active {
    background-color: #e6a23c;
    color: #ffffff;
    border-color: #e6a23c;
}

/* 所有固件页面 - 蓝色主题 */
.all-firmware .btn-page.active {
    background-color: #409eff;
    color: #ffffff;
    border-color: #409eff;
}

/* 使用记录页面 - 绿色主题 */
.usage-record .btn-page.active {
    background-color: #67c23a;
    color: #ffffff;
    border-color: #67c23a;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
    .firmware-container .table-footer {
        flex-direction: column;
        gap: 1rem;
    }
    
    .firmware-container .table-length {
        width: 100%;
        justify-content: space-between;
    }
    
    .firmware-container .table-pagination {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .firmware-container .table-footer {
        padding: 15px 10px;
    }
    
    .firmware-container .table-pagination {
        transform: scale(0.9);
    }
    
    .firmware-container .table-length {
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
    }
    
    .firmware-container .page-size-selector,
    .firmware-container .total-count {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .firmware-container .table-footer {
        padding: 10px 5px;
    }
    
    .firmware-container .table-pagination {
        transform: scale(0.8);
    }
    
    .firmware-container .btn-icon,
    .firmware-container .btn-page {
        min-width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
}

/* ===== 动画效果 ===== */
.firmware-container .btn-icon,
.firmware-container .btn-page {
    transition: all 0.3s ease;
}

.firmware-container .btn-page.active {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== 可访问性优化 ===== */
.firmware-container .btn-icon:focus,
.firmware-container .btn-page:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.firmware-container .page-size-selector select:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* ===== 加载状态 ===== */
.firmware-container .table-footer.loading {
    opacity: 0.6;
    pointer-events: none;
}

.firmware-container .table-footer.loading * {
    cursor: wait !important;
} 