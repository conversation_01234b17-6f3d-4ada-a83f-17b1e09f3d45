SN打印记录页面开发需求
概述
在左侧菜单栏"扫码绑定"下方，新增"SN打印记录"页面，采用单页面应用架构，JavaScript 和 CSS 独立存放，遵循项目整体设计风格，确保代码模块化、可维护性和响应式设计。
技术要求
1. CSS 规范
● BEM 命名：使用 BEM（Block-Element-Modifier）命名规范，避免样式污染。
● 响应式单位：使用 rem 单位代替固定像素值。
● 字体大小：使用 clamp() 函数设置响应式字体大小，确保跨设备兼容性。
● 媒体查询断点：与项目其他页面保持一致的断点（如 480px、768px、1200px 等）。
2. JavaScript 规范
● 全局变量控制：避免全局变量污染，使用 IIFE（立即执行函数表达式）或模块化封装。
● 事件绑定：确保事件监听器高效且无内存泄漏。
● 功能模块化：逻辑分块（如日期处理、周期计算、序列号生成等），便于维护。
3. HTML 结构
● 语义化：使用语义化标签（如 <section>、<article>、<label> 等），提高可访问性。
● 结构一致性：遵循现有页面 HTML 结构模式，确保视觉和代码风格统一。
页面内容
1. 基本信息
● 字段：
  ○ 操作人员
  ○ 加工单号
  ○ 产品编码
  ○ 产品型号
  ○ 生产数量
● UI 要求：
  ○ 使用图标和分隔线区分标题。
  ○ 表单布局清晰，字段对齐。
2. SN 配置
● 字段：
  ○ 订货号
  ○ 产品 SN 号位数
  ○ 日期（格式：YYMMDD，如 "250429"）
  ○ 周期（自动计算，基于 ISO 8601 标准）
  ○ 首件 SN（高亮显示）
● 功能逻辑：
  ○ 日期：默认填充当前日期（格式：YYMMDD）。
  ○ 周期：
    ■ 根据输入日期计算当前年份第几周（如 "250429" 对应第 17 周）。
    ■ 使用 ISO 8601 标准计算，确保准确性。
    ■ 默认自动填充，允许手动调整。
    ■ 提供周期验证提示（如日期无效时显示错误信息）。
  ○ 首件 SN：高亮输入框，醒目区分。
3. 序列号列表
● 内容：
  ○ 动态生成序列号表格。
  ○ 显示序列号总数。
● 显示逻辑：
  ○ 仅在生成序列号时显示表格。
  ○ 表格包含序号和序列号两列。
响应式设计
● 小屏幕（<480px）：
  ○ 表单字段垂直排列。
  ○ 简化图标和间距。
● 中屏幕（480px-768px）：
  ○ 表单字段可水平排列（视空间）。
  ○ 保持清晰的标题分隔。
● 大屏幕（>768px）：
  ○ 完整布局，字段水平对齐。
  ○ 表格自适应宽度，优化显示。
其他要求
● 性能优化：
  ○ 最小化 DOM 操作。
  ○ 延迟加载非必要资源。
● 一致性：
  ○ 可以参考现有页面的设计组件，比如ShipmentBarcode页面。
  ○ 保持统一的配色方案和字体。
  ○ 只用完成前端页面的开发，暂时不应处理后端
  ○ 也买你要现代化好看
  

# SN打印记录功能实现文档

## 一、功能概述

SN打印记录页面是一个用于生成、验证和管理产品序列号(SN)的单页面应用。主要功能包括：

1. **工单信息获取**：根据加工单号自动获取产品信息
2. **SN配置生成**：基于订货号、日期、周期自动生成和预览SN号
3. **特殊格式支持**：支持包含#分隔符的订货号自定义格式
4. **批量SN生成**：根据配置生成批量SN号列表
5. **扫描验证**：通过扫描枪或手动输入验证SN号
6. **语音提示**：智能语音播报验证结果，提升操作效率
7. **实时保存**：验证成功后立即保存到数据库
8. **验证进度追踪**：显示验证状态、进度和最近扫描记录

系统设计遵循"验证即保存"的模式，增强了数据安全性和用户体验。

## 二、后端API详细说明

后端API基于Flask框架实现，位于`routes/sn_print_record_controller.py`文件中。主要提供以下端点：

### 1. 获取当前用户

```
GET /api/sn-print-record/get-current-user
```

**功能**：获取当前登录用户的用户名
**响应**：
- 成功：`{"success": true, "username": "用户名"}`
- 失败：`{"success": false, "message": "无法获取用户信息，请重新登录"}`

### 2. 获取工单详情

```
GET /api/sn-print-record/get-work-order-details?processOrder={加工单号}
```

**功能**：根据加工单号查询工单信息
**参数**：
- `processOrder`：加工单号
**响应**：
- 成功：
```json
{
    "success": true,
    "order": {
        "ord_productModel": "产品型号",
        "ord_productCode": "产品编码",
        "ord_productionQuantity": 数量,
        "ord_snlenth": "SN长度",
        "ord_product_type_id": "产品类型ID"
    }
}
```
- 失败：`{"success": false, "message": "错误信息"}`

### 3. 保存单个验证成功的SN记录

```
POST /api/sn-print-record/save-single-verified-sn
```

**功能**：保存单个已验证成功的SN记录到数据库
**请求体**：
```json
{
    "processOrder": "加工单号",
    "productModel": "产品型号",
    "productCode": "产品编码",
    "quantity": 数量,
    "operator": "操作人员",
    "sn": "序列号"
}
```
**响应**：
- 成功：`{"success": true, "message": "SN xxx 已成功保存到数据库"}`
- 失败：`{"success": false, "message": "错误信息"}`

### 4. 检查SN是否存在

```
GET /api/sn-print-record/check-sn-exists?sn={序列号}
```

**功能**：检查指定SN是否已存在于数据库中
**参数**：
- `sn`：要检查的序列号
**响应**：
- 成功：`{"success": true, "exists": true|false}`
- 失败：`{"success": false, "message": "错误信息"}`

### 5. 获取下一可用流水号

`GET /api/sn-print-record/get-next-sequence`

**功能**: 根据产品编码、年份(YY)、周期(WW)和订货号，获取下一个可用的流水号，用于SN的连续生成。
**参数**:
- `productCode`: 产品编码
- `year`: 年份后两位 (例如 "25")
- `week`: 周期 (例如 "17")
- `orderNumber`: 订货号
**响应**:
- 成功: `{"success": true, "nextSequence": <下一个流水号>, "snPrefix": "<生成的SN前缀>"}`
- 失败: `{"success": false, "message": "错误信息"}`

## 三、数据库设计

SN记录存储在`laser_standard`表中，表结构如下：

```sql
CREATE TABLE laser_standard (
    laser_order_no VARCHAR(50) NOT NULL COMMENT '加工单号',
    laser_sn VARCHAR(100) NOT NULL COMMENT 'SN号',
    laser_product_model VARCHAR(50) NOT NULL COMMENT '产品型号',
    laser_sn_status TINYINT(1) DEFAULT 0 COMMENT 'SN状态，0或1',
    laser_product_code VARCHAR(50) NOT NULL COMMENT '产品编码',
    laser_quantity INT NOT NULL COMMENT '生产数量',
    laser_operator VARCHAR(50) NOT NULL COMMENT '操作人员',
    laser_operation_time DATETIME NOT NULL COMMENT '操作时间',
    INDEX idx_laser_sn (laser_sn),
    PRIMARY KEY (laser_order_no, laser_sn)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='激光达标表';
```

数据库模型在`models/laser_standard.py`中定义，使用SQLAlchemy ORM实现。

## 四、前端功能实现

前端实现遵循模块化设计原则，主要代码位于`static/page_js_css/SNPrintRecord.js`和`static/page_js_css/SNPrintRecord.css`文件中。

### 1. 页面初始化与自动填充

**主要实现**：`initSNPrintRecordPage`函数
- 初始化页面结构、事件监听器
- 自动获取并填充当前日期
- 调用API获取当前用户信息填充到操作人员字段
- 实现页面内容的动态生成

### 2. 工单信息联动

**主要实现**：`fetchWorkOrderDetails`函数
- 根据输入的加工单号实时获取工单详情
- 自动填充产品型号、产品编码、生产数量和SN长度
- 防抖处理避免频繁API调用
- 成功获取工单信息（特别是产品编码和订货号）后，会触发调用新的后端API `/api/sn-print-record/get-next-sequence` 来获取当前产品编码和周期下的起始流水号。

### 3. SN号生成逻辑（支持多种格式）

**主要实现**：`updateFirstSN`, `generateSNList`, `fetchNextSequence`, `validateManualSN` 函数，及 `window.snPrintRecordState` 状态管理

#### 3.1 常规格式（自动生成）
- **智能流水号生成**:
  - 当工单信息（产品编码、订货号）获取完成，或日期/周期发生变化时，前端会向后端 `/api/sn-print-record/get-next-sequence` 发起请求。
  - 该API会根据当前产品编码、年份(YY)、周期(WW)和订货号，查询数据库中已存在的SN记录，计算并返回下一个可用的流水号。
  - **流水号累加规则**:
    - 对于同一产品编码，在相同的年份和周期内，流水号会跨工单连续累加。
    - 例如：工单A（产品123，订货号XYZ，周期25年17周）生成10个SN，其SN号如 `XYZ25170001` 到 `XYZ25170010`。后续工单B（相同产品123，相同订货号XYZ，周期25年17周）的SN流水号将从 `XYZ25170011` 开始。
    - 当进入新的周期（例如25年18周）或年份变化时，即使产品编码和订货号相同，流水号也会从该产品在新周期下的第一个可用流水号（通常是1，除非已有记录）重新开始。
- **格式规范**：`订货号 + 年份(YY) + 周期(WW) + 流水号(NNNN)`
- **示例**：`ABC123251700001` （ABC123为订货号，25为2025年，17为第17周，0001为流水号）

#### 3.2 特殊格式（手动定义）
- **触发条件**：当订货号包含`#`分隔符时自动识别为特殊格式
- **格式自由度**：支持任意自定义格式，只要包含`#`分隔符即可
- **生成规则**：
  - 系统检测到订货号中的`#`后，跳过自动生成逻辑
  - 用户需手动输入完整的首件SN格式
  - 系统从最后一个`#`位置提取流水号部分进行递增
- **示例**：
  ```
  订货号: 10010826#
  手动输入首件SN: 10010826#2522#0001
  生成数量: 4
  结果: 10010826#2522#0001, 10010826#2522#0002, 10010826#2522#0003, 10010826#2522#0004
  ```
- **界面提示**：
  - 特殊格式订货号会显示橙色高亮样式
  - 提供格式示例和操作提示
  - 首件SN输入框显示特殊样式区分

#### 3.3 智能验证
- **长度验证**：检查SN长度与配置的SN长度是否一致
- **格式验证**：验证SN是否以正确的订货号开头
- **周期验证**：对常规格式进行周期匹配检查，提供用户确认选项
- **特殊格式验证**：验证`#`分隔符的存在和流水号部分的数字格式

### 4. 周期计算

**主要实现**：`calculateWeekNumber`和`getISOWeek`函数
- 根据日期自动计算ISO 8601标准周数
- 对无效日期进行验证和错误提示
- 支持手动调整周期值

### 5. 扫描验证与实时保存

**主要实现**：`verifySN`函数
- 支持扫描枪或手动输入验证SN
- 验证成功即时保存到数据库
- 提供即时视觉反馈
- 跟踪验证进度和已验证SN列表
- 记录最近扫描历史

### 6. 智能语音提示系统

**主要实现**：`speakText`, `toggleVoice`, `updateVoiceButtonStatus` 函数

#### 6.1 语音反馈功能
- **验证成功**：播报"条码正确"
- **验证失败**：播报"条码错误"（包括不在列表中、重复扫描、保存失败等情况）
- **语音特性**：
  - 使用中文语音合成（zh-CN）
  - 优化语速（1.2倍速）和音量（0.8）
  - 自动停止重叠播放
  - 静默错误处理，不影响主要功能

#### 6.2 语音控制功能
- **一键切换**：提供语音开启/关闭按钮
- **状态持久化**：使用localStorage保存用户偏好
- **视觉反馈**：
  - 开启状态：蓝色按钮 + 音量图标
  - 关闭状态：灰色按钮 + 静音图标
- **用户体验**：
  - 默认开启语音提示
  - 开启时播放确认音"语音提示已开启"
  - 按钮位于验证区域左侧，方便操作

#### 6.3 兼容性处理
- 自动检测浏览器语音合成API支持
- 不支持的浏览器静默跳过，不影响正常功能
- 错误情况下优雅降级

### 7. 优化的重置功能

**主要实现**：`resetForm`函数
- 重置时保留操作人员、日期、周期和流水号长度。
- 清空工单相关信息、生成的SN列表，以及前端缓存的与SN流水号生成相关的状态（`window.snPrintRecordState`中的 `currentProductCode`, `currentOrderNumber`, `currentYear`, `currentWeek` 和 `nextSequence`），确保下次生成时能正确获取新的起始流水号。
- 清理特殊格式相关的样式和提示信息
- 用户友好的确认弹窗和成功提示。

## 五、主要优化点

### 1. 验证即保存机制

- 每次SN验证成功后立即保存到数据库，而非批量保存
- 减少数据丢失风险，提高数据安全性
- 分散服务器负载，避免一次提交大量记录造成的服务器压力

### 2. 智能表单重置

- 保留经常使用但不常变化的字段（操作人员、日期、周期、流水号长度）
- 提高连续处理多个工单时的效率
- 减少重复操作，改善用户体验

### 3. 多格式SN支持

- **常规格式**：标准的订货号+年份+周期+流水号组合，适用于大多数生产场景
- **特殊格式**：支持包含`#`分隔符的自定义格式，满足特殊生产需求
- **智能识别**：系统自动检测订货号格式，无需手动切换模式
- **格式验证**：针对不同格式提供相应的验证规则

### 4. 语音辅助操作

- **提升效率**：操作员无需看屏幕即可知晓验证结果
- **减少错误**：清晰的语音反馈降低操作失误
- **用户控制**：提供一键开关，适应不同工作环境
- **无缝集成**：语音功能不影响现有操作流程

### 5. 代码优化与精简

- 删除冗余的批量保存API，保持代码简洁
- 使用单一职责原则设计API端点
- 应用防抖技术减少不必要的API调用

### 6. 状态管理与反馈

- 清晰的视觉反馈指示操作状态
- 实时更新验证进度和状态
- 记录最近的扫描历史，方便用户查看

### 7. 智能流水号管理

- 实现基于产品编码和周期的流水号自动累加，确保在同一周期内同一产品的SN号连续。
- 不同周期的SN号自动从该产品在新周期下的第一个可用流水号开始（通常是1，除非已有记录），符合生产追溯的实际需求。
- 通过前后端配合，动态获取下一个可用流水号，提高了SN生成的准确性和智能化程度。

## 六、响应式设计与用户体验

### 1. 布局适配
- **大屏幕**：双栏布局，左侧SN列表，右侧验证区域
- **中屏幕**：保持双栏布局，适当调整间距
- **小屏幕**：垂直堆叠布局，优化按钮排列

### 2. 交互优化
- **键盘支持**：回车键快速操作，提升扫描效率
- **视觉反馈**：状态颜色区分（成功绿色、错误红色、警告橙色）
- **进度指示**：实时显示验证进度条和计数
- **操作提示**：清晰的提示信息和格式示例

### 3. 性能优化
- **防抖处理**：避免频繁API调用
- **状态缓存**：减少重复计算和网络请求
- **错误处理**：优雅的错误恢复机制

## 七、使用场景与工作流程

### 1. 标准生产场景
```
1. 输入加工单号 → 自动获取工单信息
2. 系统自动计算周期和流水号 → 生成首件SN预览
3. 确认信息无误 → 点击生成序列号
4. 使用扫描枪验证SN → 听到"条码正确"语音确认
5. 完成全部验证 → 系统自动保存所有记录
6. 重置表单 → 处理下一个工单
```

### 2. 特殊格式生产场景
```
1. 输入包含#的订货号 → 系统识别为特殊格式
2. 手动输入首件SN（如：10010826#2522#0001）
3. 系统生成对应格式的序列号列表
4. 按标准流程完成扫描验证
```

### 3. 连续工单处理
```
1. 完成当前工单验证
2. 点击重置表单（保留操作员、日期等信息）
3. 输入新的加工单号
4. 重复生成和验证流程
```

## 八、总结

SN打印记录页面基于现代Web技术实现，提供了高效的产品SN生成、验证和管理功能。通过"验证即保存"的设计模式，增强了数据安全性和系统稳定性。智能表单重置功能提高了操作效率，特别适合需要连续处理多个工单的场景。

**主要特色功能**：
- 🎯 **双格式支持**：兼容标准格式和特殊自定义格式
- 🔊 **语音提示**：提升操作效率和准确性
- 🧠 **智能流水号**：跨工单连续递增，符合生产追溯需求
- 💾 **实时保存**：验证即保存，确保数据安全
- 🎨 **现代界面**：响应式设计，适配各种设备

整体设计遵循项目的编码规范和UI风格，保持了视觉和功能上的一致性，为生产环境提供了专业、高效、易用的SN管理解决方案。  