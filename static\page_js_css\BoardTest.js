function initBoardTestPage() {
    const boardTestContent = document.getElementById('board-test-content');
    if (!boardTestContent) {
        Logger.error('无法找到 board-test-content 元素');
        return;
    }
    
    boardTestContent.innerHTML = `
        <section class="container">
            <h3>CPU功能检测</h3>
            <header class="header">
                <select id="ip-select">
                    <option value="*************">KMLC:*************</option>
                    <option value="*************">KMLC:*************</option>
                    <option value="*************">KMLC:*************</option>
                </select>
                <button class="load-btn">程序加载</button>
                <button class="start-btn">开始测试</button>
                <button class="finish-btn">测试完成</button>
            </header>
            <div class="content">
                <div class="left-column">
                    ${createTestItems()}
                </div>
                <div class="right-column">
                    <div class="device-info">
                        <h3>设备信息核对</h3>
                        <textarea id="device-info-text" rows="10"></textarea>
                    </div>
                    <div class="test-results">
                        <h3>测试结果</h3>
                        <textarea id="test-results-text" rows="10"></textarea>
                    </div>
                </div>
            </div>
        </section>
    `;

    // 添加事件监听器
    addEventListeners();
}

function createTestItems() {
    const testItems = [
        { id: 'rs4851', label: '01 RS4851通信检测' },
        { id: 'rs4852', label: '02 RS4852通信检测' },
        { id: 'rs232', label: '03 RS232通信检测' },
        { id: 'profinet', label: '04 PROFINET通信检测' },
        { id: 'can', label: '05 Can通信检测' },
        { id: 'ecat', label: '06 ECAT通信检测' },
        { id: 'backplane', label: '07 背板通信检测' },
        { id: 'io', label: '08 IO输入输出检测' },
        { id: 'usb', label: '09 U盘接口检测' },
        { id: 'switch', label: '10 拨码开关检测', hasResult: true },
        { id: 'fmk', label: '11 FMK检测', hasResult: true }
    ];

    return testItems.map(item => `
        <div class="test-item">
            <label for="${item.id}">${item.label}</label>
            <input type="text" id="${item.id}" ${item.hasResult ? 'class="result-input"' : ''}>
            ${item.hasResult ? `
                <div class="result">
                    <input type="checkbox" id="pass${item.id}" class="pass-checkbox">
                    <label for="pass${item.id}" class="pass">Pass</label>
                    <input type="checkbox" id="ng${item.id}" class="ng-checkbox">
                    <label for="ng${item.id}" class="ng">NG</label>
                </div>
            ` : ''}
        </div>
    `).join('');
}

function addEventListeners() {
    const ipSelect = document.getElementById('ip-select');
    const loadBtn = document.querySelector('.load-btn');
    const startBtn = document.querySelector('.start-btn');
    const finishBtn = document.querySelector('.finish-btn');

    ipSelect.addEventListener('change', () => {
        Logger.log('选择的 IP 地址:', ipSelect.value);
        // 在这里添加 IP 地址变更的逻辑
    });

    loadBtn.addEventListener('click', () => {
        Logger.log('程序加载');
        // 在这里添加程序加载的逻辑
    });

    startBtn.addEventListener('click', () => {
        Logger.log('开始测试');
        // 在这里添加开始测试的逻辑
    });

    finishBtn.addEventListener('click', () => {
        Logger.log('测试完成');
        // 在这里添加测试完成的逻辑
    });

    // 添加结果复选框的事件监听器
    document.querySelectorAll('.pass-checkbox, .ng-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const testId = this.id.replace('pass', '').replace('ng', '');
            const inputField = document.getElementById(testId);
            const passCheckbox = document.getElementById(`pass${testId}`);
            const ngCheckbox = document.getElementById(`ng${testId}`);

            if (this.checked) {
                if (this.classList.contains('pass-checkbox')) {
                    inputField.classList.add('pass');
                    inputField.classList.remove('ng');
                    ngCheckbox.checked = false;
                } else {
                    inputField.classList.add('ng');
                    inputField.classList.remove('pass');
                    passCheckbox.checked = false;
                }
            } else {
                inputField.classList.remove('pass', 'ng');
            }
        });
    });
}
