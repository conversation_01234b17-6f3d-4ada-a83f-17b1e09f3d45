/**
 * 产品类型测试项目配置
 * 定义了每种CPU产品类型需要执行的测试项目集合
 */
const CPU_PRODUCT_TYPE_CONFIGS = {
    // All配置
    'all_config': {
        name: 'All',
        description: '当前配置：所有测试项目',
        enabledTestCount: 14,
        // 调整顺序：将 12(拨码开关) 提前至 Led灯珠 之后，U盘接口 之前
        enabledTests: [0, 1, 2, 3, 4, 5, 6, 7, 12, 8, 9, 10, 11, 13],
        testMapping: [
            { index: 0, testName: 'RS485_通信', category: '通信' },
            { index: 1, testName: 'RS232通信', category: '通信' },
            { index: 2, testName: 'CANbus通信', category: '通信' },
            { index: 3, testName: 'EtherCAT通信', category: '通信' },
            { index: 4, testName: 'Backplane Bus通信', category: '通信' },
            { index: 5, testName: 'Body I/O输入输出', category: '硬件' },
            { index: 6, testName: 'Led数码管', category: '硬件' },
            { index: 7, testName: 'Led灯珠', category: '硬件' },
            { index: 8, testName: 'U盘接口', category: '接口' },
            { index: 9, testName: 'SD卡', category: '接口' },
            { index: 10, testName: '调试串口', category: '接口' },
            { index: 11, testName: '网口', category: '接口' },
            { index: 12, testName: '拨码开关', category: '硬件' },
            { index: 13, testName: '复位按钮', category: '硬件' }
        ],
        productModels: 'EC310、EC311、EC312、EC321、EC411、EC412、MC501、MC511、MC512、MC521、MC522、MC611、MC612、MC622   tip：老产品除了冗余型都用此配置'
    },
    
    // 高速IO配置
    'high_speed_io': {
        name: '小型机、MC701',
        description: '当前配置：所有测试项目',
        enabledTestCount: 14,
        // 调整顺序同上
        enabledTests: [0, 1, 2, 3, 4, 5, 6, 7, 12, 8, 9, 10, 11, 13],
        testMapping: [
            { index: 0, testName: 'RS485_通信', category: '通信' },
            { index: 1, testName: 'RS232通信', category: '通信' },
            { index: 2, testName: 'CANbus通信', category: '通信' },
            { index: 3, testName: 'EtherCAT通信', category: '通信' },
            { index: 4, testName: 'Backplane Bus通信', category: '通信' },
            { index: 5, testName: 'Body I/O输入输出', category: '硬件' },
            { index: 6, testName: 'Led数码管', category: '硬件' },
            { index: 7, testName: 'Led灯珠', category: '硬件' },
            { index: 8, testName: 'U盘接口', category: '接口' },
            { index: 9, testName: 'SD卡', category: '接口' },
            { index: 10, testName: '调试串口', category: '接口' },
            { index: 11, testName: '网口', category: '接口' },
            { index: 12, testName: '拨码开关', category: '硬件' },
            { index: 13, testName: '复位按钮', category: '硬件' }
        ],
        productModels: 'MC701、EC201、EC202、EC203、EC205、EC206'
    },
    
    // 冗余型配置
    'redundant_type': {
        name: '冗余型',
        description: '当前配置：Backplane Bus通信+Led数码管+网口+拨码开关+复位按钮+SD卡+调试串口',
        enabledTestCount: 7,
        enabledTests: [4, 6, 11, 12, 13, 9, 10],
        testMapping: [
            { index: 4, testName: 'Backplane Bus通信', category: '通信' },
            { index: 6, testName: 'Led数码管', category: '硬件' },
            { index: 11, testName: '网口', category: '接口' },
            { index: 12, testName: '拨码开关', category: '硬件' },
            { index: 13, testName: '复位按钮', category: '硬件' },
            { index: 9, testName: 'SD卡', category: '接口' },
            { index: 10, testName: '调试串口', category: '接口' }
        ],
        productModels: 'EC431、EC451、EC400-0200N、EC401-0200N'
    },
    
    // 201无输入输出配置
    'type_201_no_io': {
        name: '201无输入输出',
        description: '当前配置：RS485_通信+CANbus通信+Led数码管+网口+复位按钮+SD卡',
        enabledTestCount: 6,
        enabledTests: [0, 2, 6, 11, 13, 9],
        testMapping: [
            { index: 0, testName: 'RS485_通信', category: '通信' },
            { index: 2, testName: 'CANbus通信', category: '通信' },
            { index: 6, testName: 'Led数码管', category: '硬件' },
            { index: 11, testName: '网口', category: '接口' },
            { index: 13, testName: '复位按钮', category: '硬件' },
            { index: 9, testName: 'SD卡', category: '接口' }
        ],
        productModels: 'EC201S-CPU1A410N 无本体IO'
    },
    
    // 201有输入输出配置
    'type_201_with_io': {
        name: '201有输入输出',
        description: '当前配置：RS485_通信+CANbus通信+Backplane Bus通信+Led数码管+网口+复位按钮+Body I/O输入输出+SD卡',
        enabledTestCount: 8,
        enabledTests: [0, 2, 4, 6, 11, 13, 5, 9],
        testMapping: [
            { index: 0, testName: 'RS485_通信', category: '通信' },
            { index: 2, testName: 'CANbus通信', category: '通信' },
            { index: 4, testName: 'Backplane Bus通信', category: '通信' },
            { index: 6, testName: 'Led数码管', category: '硬件' },
            { index: 11, testName: '网口', category: '接口' },
            { index: 13, testName: '复位按钮', category: '硬件' },
            { index: 5, testName: 'Body I/O输入输出', category: '硬件' },
            { index: 9, testName: 'SD卡', category: '接口' }
        ],
        productModels: 'EC201S-CPU1A410G0808D'
    },
    
    // 201五通信配置（移除RS485_2通信和CANbus通信，保留RS485_通信）
    'type_201_five_comm': {
        name: '201五通信',
        description: '当前配置：RS485_通信+RS232通信+Backplane Bus通信+Led数码管+网口+复位按钮+Body I/O输入输出+SD卡',
        enabledTestCount: 8,
        enabledTests: [0, 1, 4, 6, 11, 13, 5, 9],
        testMapping: [
            { index: 0, testName: 'RS485_通信', category: '通信' },
            { index: 1, testName: 'RS232通信', category: '通信' },
            { index: 4, testName: 'Backplane Bus通信', category: '通信' },
            { index: 6, testName: 'Led数码管', category: '硬件' },
            { index: 11, testName: '网口', category: '接口' },
            { index: 13, testName: '复位按钮', category: '硬件' },
            { index: 5, testName: 'Body I/O输入输出', category: '硬件' },
            { index: 9, testName: 'SD卡', category: '接口' }
        ],
        productModels: 'EC201S-CPU1A500G0808D'
    }
};

// 导出为全局变量
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CPU_PRODUCT_TYPE_CONFIGS };
} else {
    window.CPU_PRODUCT_TYPE_CONFIGS = CPU_PRODUCT_TYPE_CONFIGS;
}

// 配置加载完成日志
if (typeof Logger !== 'undefined') {
    Logger.info('[ProductTypeConfigs] CPU产品类型配置已加载:', Object.keys(CPU_PRODUCT_TYPE_CONFIGS).length + '个配置');
}
