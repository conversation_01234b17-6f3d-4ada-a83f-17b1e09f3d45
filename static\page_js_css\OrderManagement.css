/* 基础样式 - 与产品管理保持一致 */
#order-management-page .container {
    padding: 1.5rem;
    max-width: 100%;
    background-color: #f3f4f6;
    min-height: calc(100vh - var(--header-height));
}

/* 卡片基础样式 */
#order-management-page .card {
    background: #ffffff;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    margin-bottom: 1rem;
    padding: 1.25rem;
}

/* 页面标题 */
#order-management-page .page-header {
    margin-bottom: 1.5rem;
}

#order-management-page .page-header h1 {
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
}

/* 搜索区域 */
#order-management-page .search-section {
    margin-bottom: 1rem;
}

#order-management-page .search-form {
    display: flex;
    align-items: center;
}

#order-management-page .search-input-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
}

#order-management-page .search-input-wrapper {
    position: relative;
    flex: 1;
}

#order-management-page .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #4b5563;
    pointer-events: none;
}

#order-management-page .search-form input {
    width: 100%;
    padding: 0.625rem 0.75rem 0.625rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s;
}

#order-management-page .search-form input:focus {
    outline: none;
    border-color: #1e40af;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

#order-management-page .search-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* 按钮组样式 */
#order-management-page .operation-section {
    margin-bottom: 1rem;
}

#order-management-page .button-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* 按钮基础样式 */
#order-management-page .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
    border: none;
    gap: 0.5rem;
}

/* 按钮变体 */
#order-management-page .btn-primary {
    background-color: #1e40af;
    color: #ffffff;
}

#order-management-page .btn-primary:hover {
    background-color: #1e3a8a;
}

#order-management-page .btn-danger {
    background-color: #dc2626;
    color: #ffffff;
}

#order-management-page .btn-danger:hover {
    background-color: #b91c1c;
}

#order-management-page .btn-success {
    background-color: #059669;
    color: #ffffff;
}

#order-management-page .btn-success:hover {
    background-color: #047857;
}

#order-management-page .btn-info {
    background-color: #3b82f6;
    color: #ffffff;
}

#order-management-page .btn-info:hover {
    background-color: #2563eb;
}

/* 查询按钮 */
#order-management-page .btn-search {
    background-color: #e0f2fe;
    color: #1e40af;
}

#order-management-page .btn-search:hover {
    background-color: #bae6fd;
}

/* 重置按钮 */
#order-management-page .btn-reset {
    background-color: #dddbdb;
    color: #4b5563;
    border: 1px solid #d1d5db;
}

#order-management-page .btn-reset:hover {
    background-color: #f3f4f6;
}

/* 表格容器样式 */
#order-management-page .table-section {
    position: relative;
    overflow-x: auto !important;  /* 保持水平滚动 */
    overflow-y: hidden;          /* 禁用垂直滚动 */
    background: #ffffff;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

/* 表格基础样式 */
#order-management-page .data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    table-layout: fixed;
    min-width: 1800px;
}

/* 调整单元格高度，使其更紧凑 */
#order-management-page .data-table th,
#order-management-page .data-table td {
    padding: 0.5rem 1rem;    /* 减小上下padding */
    height: 40px;            /* 固定行高 */
    line-height: 1.5;        /* 确保文本垂直居中 */
    box-sizing: border-box;  /* 确保padding不会增加总高度 */
}

/* 列宽控制 */
#order-management-page .data-table th:nth-child(1),
#order-management-page .data-table td:nth-child(1) {
    width: 3%;  /* 复选框列 */
    min-width: 10px;
}

#order-management-page .data-table th:nth-child(2),
#order-management-page .data-table td:nth-child(2) {
    width: 10%;  /* 加工单号列 */
    min-width: 120px;
}

#order-management-page .data-table th:nth-child(3),
#order-management-page .data-table td:nth-child(3) {
    width: 5%;  /* 产品批次列 */
    min-width: 100px;
}

#order-management-page .data-table th:nth-child(4),
#order-management-page .data-table td:nth-child(4) {
    width: 5%;   /* 生产数量列 */
    min-width: 80px;
}

#order-management-page .data-table th:nth-child(5),
#order-management-page .data-table td:nth-child(5) {
    width: 6%;  /* 产品编码列 */
    min-width: 120px;
}

#order-management-page .data-table th:nth-child(6),
#order-management-page .data-table td:nth-child(6) {
    width: 5%;  /* 产品类型列 */
    min-width: 100px;
}

#order-management-page .data-table th:nth-child(7),
#order-management-page .data-table td:nth-child(7) {
    width: 11%;  /* 产品型号列 */
    min-width: 100px;
}

#order-management-page .data-table th:nth-child(8),
#order-management-page .data-table td:nth-child(8) {
    width: 4.3%;   /* 状态列 */
    min-width: 101px;
}

#order-management-page .data-table th:nth-child(9),
#order-management-page .data-table td:nth-child(9) {
    width: 5%;   /* 创建人员列 */
    min-width: 80px;
}

#order-management-page .data-table th:nth-child(10),
#order-management-page .data-table td:nth-child(10) {
    width: 7%;  /* 创建时间列 */
    min-width: 100px;
}

#order-management-page .data-table th:nth-child(11),
#order-management-page .data-table td:nth-child(11) {
    width: 5%;   /* 备注列 */
    min-width: 50px;
}

#order-management-page .data-table th:nth-child(12),
#order-management-page .data-table td:nth-child(12) {
    width: 8%;   /* 操作列 */
    min-width: 50px;
}

/* 单元格内容处理 */
#order-management-page .data-table td {
    white-space: nowrap;        /* 防止文本换行 */
    overflow: hidden;           /* 隐藏溢出内容 */
    text-overflow: ellipsis;    /* 显示省略号 */
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    color: #4b5563;
}

/* 表头样式 */
#order-management-page .data-table th {
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #1f2937;
    background-color: #f3f4f6;
    border-bottom: 1px solid #e5e7eb;
    white-space: nowrap;
}

/* hover效果 */
#order-management-page .data-table tr:hover {
    background-color: #f9fafb;
}

/* 可选：添加tooltip效果，显示完整内容 */
#order-management-page .data-table td {
    position: relative;
}

#order-management-page .data-table td:hover::after {
    content: attr(title);
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 1000;
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: normal;
    max-height: 200px;      /* 限制tooltip最大高度 */
    overflow-y: auto;       /* 如果内容过长允许tooltip滚动 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-size: 12px;
}

/* 分页样式 */
#order-management-page .table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
}

#order-management-page .table-length {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    color: #4b5563;
    font-size: 0.875rem;
}

#order-management-page .page-size-selector {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

#order-management-page .page-size-selector select {
    margin: 0 0.25rem;
    padding: 0.25rem 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
}

#order-management-page .table-pagination {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 添加分页按钮样式 */
#order-management-page .btn-page {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
    color: #374151;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
}

#order-management-page .btn-page:hover:not(:disabled) {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

#order-management-page .btn-page.active {
    background-color: #1e40af;
    color: #ffffff;
    border-color: #1e40af;
}

#order-management-page .btn-page:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
}

#order-management-page .pagination-ellipsis {
    padding: 0.375rem 0.5rem;
    color: #4b5563;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #order-management-page .search-form {
        flex-direction: column;
    }
    
    #order-management-page .search-input-group {
        flex-direction: column;
    }
    
    #order-management-page .button-group {
        flex-wrap: wrap;
    }
    
    #order-management-page .btn {
        width: 100%;
    }
    
    #order-management-page .table-footer {
        flex-direction: column;
        gap: 1rem;
    }
    
    #order-management-page .table-length {
        width: 100%;
        justify-content: space-between;
    }
    
    #order-management-page .table-pagination {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }
} 

/* 模态框样式 */
.order-management-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.order-management-modal .modal-content {
    background: #ffffff;
    border-radius: 0.5rem;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.order-management-modal .modal-header {
    margin: -1px -1px 0 -1px;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, #60a5fa, #93c5fd);
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    border-bottom: none;
    position: relative;
}

.order-management-modal .modal-header h2 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 500;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.05);
}

.order-management-modal .modal-header .close {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 400;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.2s;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    margin: -0.5rem -0.75rem -0.5rem 0;
}

.order-management-modal .modal-header .close:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.order-management-modal .modal-body {
    padding: 1.5rem;
}

.order-management-modal .modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 表单样式 */
.order-management-modal .two-column-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.order-management-modal .form-row {
    display: flex;
    gap: 1rem;
    width: 100%;
}

.order-management-modal .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.order-management-modal .form-row.full-width {
    flex-direction: column;
}

.order-management-modal .form-group {
    margin-bottom: 1rem;
}

.order-management-modal .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #4b5563;
    font-weight: 500;
}

.order-management-modal .form-group input,
.order-management-modal .form-group textarea {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    transition: all 0.2s;
}

.order-management-modal .form-group input:focus,
.order-management-modal .form-group textarea:focus {
    outline: none;
    border-color: #1e40af;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.order-management-modal .required {
    color: #dc2626;
    margin-left: 0.25rem;
}

/* 禁用输入框样式 */
.order-management-modal .form-group input:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .order-management-modal .form-row {
        flex-direction: column;
    }
    
    .order-management-modal .form-row .form-group {
        width: 100%;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.order-management-modal[style*="display: block"] {
    animation: fadeIn 0.3s ease-out;
} 

/* 表格排序样式 */
#order-management-page .sortable {
    cursor: pointer;
    user-select: none;
}

#order-management-page .sortable i {
    margin-left: 0.5rem;
    color: #9ca3af;
    font-size: 0.875rem;
}

#order-management-page .sortable:hover i {
    color: #4b5563;
}

#order-management-page .sortable i.fa-sort-up,
#order-management-page .sortable i.fa-sort-down {
    color: #1e40af;
} 

/* 修改状态列样式 */
#order-management-page .data-table th.status-cell {
    font-size: inherit;  /* 继承其他表头的字体大小 */
    font-weight: 600;    /* 与其他表头一致的字重 */
    color: #1f2937;      /* 与其他表头一致的颜色 */
    background-color: #f3f4f6;  /* 与其他表头一致的背景色 */
    cursor: pointer;
    user-select: none;
    white-space: nowrap;
    padding: 0.75rem 1rem;
}

#order-management-page .data-table th.status-cell i {
    margin-left: 0.5rem;
    color: #9ca3af;
    font-size: 0.875rem;
}

#order-management-page .data-table th.status-cell:hover i {
    color: #4b5563;
}

#order-management-page .data-table th.status-cell i.fa-sort-up,
#order-management-page .data-table th.status-cell i.fa-sort-down {
    color: #1e40af;
}

/* 保持状态标签的样式不变 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
}

.status-badge.completed {
    background-color: #e6f7ed;
    color: #52c41a;
}

.status-badge.in-progress {
    background-color: #e6f7ff;
    color: #1890ff;
    cursor: pointer;
}

.status-badge i {
    margin-left: 5px;
}

.status-badge i.manual-mark {
    color: #fa8c16; /* 橙色，表示手动标记 */
}

.status-badge i.auto-mark {
    color: #52c41a; /* 绿色，表示自动完成 */
}

/* 生产详情模态框样式 */
.production-details {
    margin: 1rem 0;
    text-align: left;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: #4b5563;
    font-weight: 500;
}

.detail-value {
    color: #1f2937;
    font-weight: 600;
} 

/* 调整加工单号列宽度 */
#order-management-page .data-table th:nth-child(4),
#order-management-page .data-table td:nth-child(4) {
    min-width: 180px;  /* 设置最小宽度 */
    max-width: 250px;  /* 设置最大宽度 */
} 

/* 编辑按钮样式 */
#order-management-page .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

#order-management-page .btn-edit {
    background-color: #3b82f6;
    color: #ffffff;
}

#order-management-page .btn-edit:hover {
    background-color: #2563eb;
} 

/* PCBA绑定检查开关 - BEM命名 */
.pcba-toggle {
    margin: 1rem 0;
}

.pcba-toggle__header {
    display: flex;
    align-items: center;
    gap: 12px; /* 添加开关和文字之间的间距 */
    margin-bottom: 0.5rem;
}

/* 修改开关样式以确保可点击 */
.pcba-toggle__switch-wrapper {
    position: relative;
    width: 44px;
    height: 24px;
    order: -1;
    flex-shrink: 0;
    cursor: pointer; /* 添加指针样式 */
    z-index: 1; /* 确保开关在最上层 */
}

/* 调整标签样式 */
.pcba-toggle__label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    user-select: none; /* 防止文字被选中 */
    cursor: default; /* 移除指针样式，因为现在只能通过开关切换 */
}

/* 其他样式保持不变 */
.pcba-toggle__input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

/* 确保滑块可以正确响应点击 */
.pcba-toggle__slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e5e7eb;
    transition: .4s;
    border-radius: 24px;
    pointer-events: none; /* 防止滑块干扰点击事件 */
}

.pcba-toggle__slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 状态样式优化 */
.pcba-toggle__status {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    margin-left: 56px; /* 与开关对齐 */
    color: #6b7280;
    min-height: 1rem;
    transition: .3s;
}

/* 其他交互效果保持不变 */
.pcba-toggle__input:checked + .pcba-toggle__slider {
    background-color: #2563eb;
}

.pcba-toggle__input:focus + .pcba-toggle__slider {
    box-shadow: 0 0 1px #2563eb;
}

.pcba-toggle__input:checked + .pcba-toggle__slider:before {
    transform: translateX(20px);
}

.pcba-toggle__status.active {
    color: #2563eb;
    font-weight: 500;
}

.pcba-toggle__switch-wrapper:hover .pcba-toggle__slider {
    background-color: #d1d5db;
}

.pcba-toggle__switch-wrapper:hover .pcba-toggle__slider:before {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.pcba-toggle__input:checked + .pcba-toggle__slider:hover {
    background-color: #1d4ed8;
} 

/* PCBA开关帮助提示样式 */
.pcba-toggle__help {
    position: relative;
    margin-left: 8px;
    display: flex;
    align-items: center;
}

.pcba-toggle__help i {
    color: #6b7280;
    font-size: 16px;
    cursor: help;
    transition: color 0.2s;
}

.pcba-toggle__help:hover i {
    color: #2563eb;
}

/* 修改帮助提示内容的位置 */
.pcba-toggle__help-content {
    display: none;
    position: absolute;
    left: 24px;
    top: -350px; /* 进一步上移 */
    width: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 16px;
    z-index: 1000;
    font-size: 0.875rem;
    color: #374151;
    max-height: 400px; /* 适当减小最大高度 */
    overflow-y: auto;
}

/* 相应调整箭头位置 */
.pcba-toggle__help-content::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 250px; /* 调整箭头位置，与top值对应 */
    width: 12px;
    height: 12px;
    background: white;
    transform: rotate(45deg);
    box-shadow: -2px 2px 2px rgba(0, 0, 0, 0.05);
}

/* 添加过渡动画，使显示更平滑 */
.pcba-toggle__help-content {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.pcba-toggle__help:hover .pcba-toggle__help-content {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* 帮助内容样式 */
.pcba-toggle__help-content h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 8px;
}

.pcba-toggle__help-content h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    margin: 12px 0 8px;
}

.pcba-toggle__help-content h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
    margin: 8px 0 4px;
}

.help-section {
    margin-bottom: 16px;
}

.help-section:last-child {
    margin-bottom: 0;
}

.help-section ul {
    margin: 0;
    padding-left: 20px;
}

.help-section li {
    margin: 4px 0;
}

.help-section code {
    background: #f3f4f6;
    padding: 2px 4px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.875rem;
}

.scenario {
    margin: 8px 0;
    padding-left: 16px;
}

.indent {
    padding-left: 16px;
}

/* 优化滚动条样式 */
.pcba-toggle__help-content::-webkit-scrollbar {
    width: 6px;
}

.pcba-toggle__help-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.pcba-toggle__help-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.pcba-toggle__help-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.action-buttons {
    display: flex;
    gap: 5px;
    align-items: center;
}

.action-buttons .btn {
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
}

.action-buttons .btn i {
    margin-right: 4px;
}

/* 可双击单元格样式 */
#order-management-page .data-table td.dblclick-edit {
    cursor: pointer;
    transition: background-color 0.2s;
}

#order-management-page .data-table td.dblclick-edit:hover {
    background-color: #a4c3f1;
} 