# 售后查询功能 (After Sales Query)

## 1. 概述

本功能模块旨在提供一个售后服务支持接口，允许用户根据 **产品编码 (Product Code)**、**软件版本 (Software Version)** 或 **PCBA 板号的部分内容** (通过"PCBA外协工单号"输入框输入) 查询相关产品的信息。查询结果将整合产品的基本测试信息和出货记录，以判断产品的出库状态、客户名称和出库时间，并显示关联的 **板A序列号**。

主要功能点：

*   根据产品编码、软件版本 **或** PCBA 板号的部分内容查询产品。
*   整合 `CPUTest` 和 `CouplerTest` 表中的测试记录。
*   关联 `SNRecord` 和 `Order` 表中的出货记录。
*   **新增**: 关联 `BoardAssembly` 和 `CompleteProduct` 表以支持通过板号查询，并获取 `board_a_sn` 用于显示。
*   判断产品是否已出库，并显示客户名称和出库时间。
*   提供分页查询。
*   **新增**: 提供列排序功能 (支持 SN号、产品编码、产品型号、软件版本、是否出库、客户名称、出库时间)。
*   **新增**: 实现固定列宽布局，处理内容溢出。
*   支持将查询结果（全部或选中）导出为 Excel 文件。

## 2. 后端实现

### 2.1. 文件位置

*   蓝图和路由定义：`routes/after_sales_query.py`
*   相关数据模型：
    *   `models/test_result.py` (CPUTest, CouplerTest)
    *   `models/shipment.py` (Order, SNRecord)
    *   **新增**: `models/assembly.py` (BoardAssembly, CompleteProduct)
*   数据库连接管理：`database/db_manager.py`
*   蓝图注册：`app.py`

### 2.2. 核心逻辑流程

后端查询逻辑采用多阶段处理，以整合不同查询条件并获取所需信息：

1.  **获取满足任一条件的最终产品 SN 列表 (`get_sn_list_by_combined_criteria`)**:
    *   接收 `productCode`、`softwareVersion` 和 `pcba_sn_part` (来自前端的 `pcbaOrderNumber` 输入) 作为参数。
    *   初始化一个空集合 `sn_set` 用于存储所有匹配的最终产品 SN。
    *   **产品编码/软件版本查询**: 如果提供了 `productCode` 或 `softwareVersion`，则使用 `LIKE '%...%'` 查询 `CPUTest` 和 `CouplerTest` 表，将匹配记录的 `pro_sn` 添加到 `sn_set`。使用 `UNION` 和 `DISTINCT` 确保 SN 唯一。
    *   **PCBA 板号部分匹配查询**: 如果提供了 `pcba_sn_part`:
        *   对输入值 `pcba_sn_part` 进行安全转义，防止 SQL 注入和通配符冲突。
        *   使用 `LIKE '%<escaped_part>%'` 在 `BoardAssembly` 表的 `board_a_sn`, `board_b_sn`, `board_c_sn`, `board_d_sn` 四个字段中进行 OR 查询，查找包含输入内容的记录。为防止性能问题，此查询限制了返回的 `assembly_id` 数量（例如 1000）。
        *   如果找到匹配的 `assembly_id`，则进一步查询 `CompleteProduct` 表，获取这些 `assembly_id` 对应的最终产品 `product_sn`，并将其添加到 `sn_set`。
    *   函数返回包含所有找到的唯一产品 SN 的列表 (`list(sn_set)`)。

2.  **获取产品基础详情 (`get_product_details_for_sns`)**:
    *   接收上一步得到的最终 `sn_list`。
    *   根据 `sn_list`，查询 `CPUTest` 和 `CouplerTest` 表 (使用 `WHERE pro_sn IN (...)`)，获取每个 SN 对应的 `productCode`, `productModel`, `softwareVersion`, `productType` 等信息。
    *   返回一个以产品 SN 为键、包含基础详情的字典 (`products_dict`)。

3.  **获取出货信息 (`get_shipment_info`)**:
    *   接收最终的 `sn_list`。
    *   查询 `SNRecord` 表并 `JOIN` `Order` 表，使用 `WHERE SNRecord.sn_number IN (...)` 过滤。
    *   返回一个以 SN 为键、包含 `customerName` 和 `outboundTime` 的字典 (`shipment_dict`)。

4.  **获取用于显示的组装信息 (`get_assembly_details`)**:
    *   接收最终的 `sn_list`。
    *   查询 `CompleteProduct` 表并 `JOIN` `BoardAssembly` 表，使用 `WHERE CompleteProduct.product_sn IN (...)` 过滤。
    *   主要目的是获取每个产品 SN 对应的 `board_a_sn`。
    *   返回一个以产品 SN 为键、`board_a_sn` 为值的字典 (`assembly_dict`)。

5.  **组合最终数据 (`combine_product_shipment_data`)**:
    *   接收 `products_dict`, `shipment_dict`, `assembly_dict`。
    *   遍历 `products_dict` 中的每个产品信息。
    *   使用产品 SN 在 `shipment_dict` 中查找出货状态、客户名称和出库时间。
    *   **关键变更**: 使用产品 SN 在 `assembly_dict` 中查找对应的 `board_a_sn`。将找到的 `board_a_sn` (如果找不到则为 '无') 赋值给结果记录中的 `pcbaOrderNumber` 字段。
    *   为每条记录生成一个临时的 `id` (供前端使用)。
    *   将组合后的完整记录添加到最终的结果列表 `result_data` 中。

6.  **新增: 后端排序**: 
    *   在获取到组合后的 `result_data` 后，检查请求中是否包含 `sortField` 和 `sortOrder` 参数。
    *   如果包含排序参数，则使用自定义的比较函数 (`safe_compare`，可以处理不同数据类型和 None 值) 对 `result_data` 列表在内存中进行排序。

7.  **处理分页和响应 (`search_after_sales` 路由)**:
    *   获取**排序后**的 `result_data`，计算总记录数 `total`。
    *   根据请求参数中的 `page` 和 `pageSize` 对 `result_data` 进行切片，获取当前页的数据 `paged_data`。
    *   将 `paged_data` 和 `total` 封装成 JSON 格式返回给前端。

### 2.3. 关键组件

*   **Flask Blueprint**: `after_sales_query_bp`。
*   **路由**:
    *   `/search` (GET): 处理查询请求。
    *   `/export` (GET): 处理导出请求。
*   **辅助函数**:
    *   **新增**: `get_sn_list_by_combined_criteria()`: 根据所有条件获取最终 SN 列表。
    *   **新增**: `get_product_details_for_sns()`: 根据 SN 列表获取产品基础信息。
    *   `get_shipment_info()`: 获取出货信息。
    *   `get_assembly_details()`: 获取用于显示的 `board_a_sn`。
    *   `combine_product_shipment_data()`: 组合所有信息。
    *   **新增**: `safe_compare()`: 用于后端排序时安全比较不同类型的值。

### 2.4. 数据库交互

*   使用 SQLAlchemy ORM。
*   查询涉及 `cpu_table`, `coupler_table`, `sn_records`, `orders`, **`board_assemblies`**, **`complete_products`** 表。
*   查询条件使用了 `like`, `in_` 等操作符。
    *   对产品编码、软件版本使用 `LIKE '%...%'`。
    *   **新增**: 对 `board_a/b/c/d_sn` 使用 `LIKE '%...%'` 进行部分匹配查询。
*   通过 `union` 合并不同来源的 SN。
*   通过 `join` 关联出货单、SN 记录、板子组合和完整产品信息。

### 2.5. 性能考虑

*   分阶段查询有助于逻辑分离。
*   先获取唯一的 SN 列表，再查询详情，避免对详情表进行重复查询。
*   **重要**: 针对 PCBA 板号的查询 (`board_a/b/c/d_sn`) 使用了 `LIKE '%...%'`，这在 `board_assemblies` 表数据量很大时可能导致性能下降，因为它通常无法有效利用标准 B-Tree 索引。已添加查询限制 (`limit`) 来缓解潜在的性能问题。如果性能成为瓶颈，未来可能需要考虑数据库层面的优化（如全文索引）。
*   **新增**: 后端排序是在获取所有相关数据并组合后，在内存中对结果列表进行的。如果查询结果集非常大，排序操作可能会消耗较多内存和 CPU 时间。
*   确保相关查询字段（如 `pro_code`, `sw_version`, `couplersw_version`, `pro_sn`, `sn_number`, `product_sn`, `assembly_id`, 以及 `board_a/b/c/d_sn`）在数据库层面已建立合适的索引。

## 3. API 端点文档

### 3.1. 查询售后记录

*   **URL**: `/api/after-sales-query/search`
*   **Method**: `GET`
*   **Query Parameters**:
    *   `productCode` (string, optional): 产品编码 (模糊查询)。
    *   `softwareVersion` (string, optional): 软件版本 (模糊查询)。
    *   `pcbaOrderNumber` (string, optional): **用于输入 PCBA 板号的部分内容进行模糊查询**。
    *   `page` (integer, optional, default=1): 请求的页码。
    *   `pageSize` (integer, optional, default=10): 每页显示的记录数。
    *   **新增**: `sortField` (string, optional, default='outboundTime'): 用于排序的字段名 (对应返回 JSON 数据中的键名)。
    *   **新增**: `sortOrder` (string, optional, default='desc'): 排序顺序 ('asc' 或 'desc')。
*   **Success Response (200 OK)**:
    *   **Content**:
        ```json
        {
          "success": true,
          "data": [
            {
              "id": 12345, // 临时ID，用于前端
              "serialNumber": "SN20230001",
              "productCode": "PC-CPU-5000",
              "productModel": "CPU-5000",
              "softwareVersion": "V1.2.3",
              "pcbaOrderNumber": "2410181132JG007+070505010034+GXX24110400753", // 显示关联的 board_a_sn
              "isOutOfStock": true,
              "customerName": "上海工业自动化有限公司",
              "outboundTime": "2023-10-26 10:30:00"
            },
            // ... more records
          ],
          "total": 55 // 总记录数
        }
        ```
*   **Error Responses**:
    *   `400 Bad Request`: 如果未提供任何查询参数 (`productCode`, `softwareVersion` 或 `pcbaOrderNumber`)。
        ```json
        {
          "success": false,
          "message": "请至少输入产品编码、软件版本或PCBA板号作为查询条件"
        }
        ```
    *   `500 Internal Server Error`: 如果后端处理出错。
        ```json
        {
          "success": false,
          "message": "查询失败: <错误详情>"
        }
        ```

### 3.2. 导出售后记录

*   **URL**: `/api/after-sales-query/export`
*   **Method**: `GET`
*   **Query Parameters**:
    *   `productCode` (string, optional): 同上。
    *   `softwareVersion` (string, optional): 同上。
    *   `pcbaOrderNumber` (string, optional): 同上，**用于 PCBA 板号部分内容查询**。
    *   `selectedIds` (string, optional): 一个包含选中记录 `id` 的 JSON 字符串数组 (e.g., `"[12345, 67890]"`). 如果提供此参数，则只导出选中的记录；否则导出所有查询结果。
*   **Success Response (200 OK)**:
    *   **Content**: 直接返回 Excel 文件 (`.xlsx`) 下载。文件名格式：`售后查询记录_<时间戳>.xlsx`。
    *   **Excel 内容**: "PCBA外协工单号" 列已重命名为 "**板A_SN(原PCBA工单号列)**"，并包含对应产品关联的 `board_a_sn` 数据。
*   **Error Responses**:
    *   `400 Bad Request`: 如果未提供任何查询参数。
    *   `404 Not Found`: 如果根据查询条件未找到任何记录。
        ```json
        {
          "success": false,
          "message": "没有找到匹配的记录，无法导出"
        }
        ```
    *   `500 Internal Server Error`: 如果导出过程中发生错误（如缺少 `xlsxwriter` 库或文件写入失败）。
        ```json
        {
          "success": false,
          "message": "导出失败: <错误详情>"
        }
        ```

## 4. 前端集成

*   前端交互逻辑主要在 `static/page_js_css/AfterSalesQuery.js` 文件中实现。
*   用户现在可以通过页面上的 "PCBA外协工单号" 输入框输入板号的部分内容进行查询。
*   `loadAfterSalesData` 函数负责收集所有三个查询条件（产品编码、软件版本、PCBA板号部分内容）**以及当前的排序状态 (`sortField`, `sortOrder`)** 并调用后端的 `/search` API 获取数据。
    *   页面初始加载时（无查询条件），该函数会跳过 API 调用，直接显示 "请输入查询条件" 的提示，并渲染默认的分页控件（显示10页）。
    *   该函数在无查询条件加载时，会确保分页状态的 `totalPages` 被设置为 10。
*   表格中原 "PCBA外协工单号" 列现在显示的是后端返回的 `board_a_sn` 值 (通过 `pcbaOrderNumber` 键传递)。
*   `exportAfterSalesData` 函数同样会收集所有三个查询条件并传递给后端的 `/export` API。
*   前端表格的渲染已移除"操作"列和相关按钮。
*   **新增: 固定列宽与溢出处理**: 
    *   在 `static/page_js_css/AfterSalesQuery.css` 中，为 `.data-table` 添加了 `table-layout: fixed;` 和 `min-width`。
    *   使用 `:nth-child()` 选择器为每个表格列 (`<th>`, `<td>`) 设置了固定的 `width` 和 `min-width`。
    *   为表格单元格 `<td>` 添加了 `white-space: nowrap;`, `overflow: hidden;`, `text-overflow: ellipsis;` 样式，以处理内容溢出。
    *   在 `AfterSalesQuery.js` 的 `renderAfterSalesData` 函数中，为每个 `<td>` 元素添加了 `title` 属性，以便在鼠标悬停时显示完整的单元格内容。
*   **新增: 排序功能**: 
    *   在 `AfterSalesQuery.js` 的全局状态 `window.afterSalesPageState` 中添加了 `sortField` (默认为 'outboundTime') 和 `sortOrder` (默认为 'desc') 来管理排序状态。
    *   在 `initAfterSalesQueryPage` 函数中，为可排序的表头 `<th>` 添加了 `class="sortable"` 和 `onclick="sortByAfterSales('字段名')"` 事件处理器，并添加了排序图标 `<i class="fas fa-sort"></i>`。
    *   实现了 `sortByAfterSales(field)` 函数：点击表头时触发，用于更新 `sortField` 和 `sortOrder` 状态，切换排序方向，并调用 `loadAfterSalesData` 重新加载数据。
    *   实现了 `updateSortIconsAfterSales()` 函数：根据当前的 `sortField` 和 `sortOrder` 状态，更新表头中排序图标的显示 (例如，`fa-sort`, `fa-sort-up`, `fa-sort-down`)。
    *   修改了 `loadAfterSalesData` 和 `silentAfterSalesSearch` 函数，将 `sortField` 和 `sortOrder` 作为查询参数附加到 `/api/after-sales-query/search` 的请求 URL 中。
    *   在 `static/page_js_css/AfterSalesQuery.css` 中添加了 `.sortable` 类的样式 (设置鼠标指针为 `pointer` 等) 以及排序图标 (`i.fas`) 在不同状态下的样式。
*   **分页功能优化**: (逻辑不变，但现在分页请求会带上排序参数)
    *   `updatePagination` 函数 ... (说明不变)
    *   `changeAfterSalesPage` 函数现在调用 `silentAfterSalesSearch` 函数进行页码切换 ... (说明不变)
    *   `resetAfterSalesForm` 函数已更新，在重置表单时会正确重置分页**和排序**状态 (排序恢复为默认值)。
    *   页码跳转输入框 ... (说明不变)

## 5. 依赖

*   后端导出功能依赖 `xlsxwriter` 库。请确保该库已添加到项目的 `requirements.txt` 文件中并已安装。

    ```
    pip install xlsxwriter
    ```

## 6. 注意事项

*   **PCBA 板号查询**: 用户在 "PCBA外协工单号" 输入框输入的是板号的**部分内容**，后端使用 `LIKE '%...%'` 在 `board_a/b/c/d_sn` 四个字段中进行模糊匹配查找。
*   **显示内容**: 查询结果表格和导出 Excel 文件中，原 "PCBA外协工单号" 列（Excel 中已重命名为 "板A_SN(原PCBA工单号列)"）现在显示的是与该产品关联的 **`board_a_sn`** 的值。如果找不到关联的 `board_a_sn`，则显示 '无'。
*   **性能**: 对 PCBA 板号的模糊查询可能影响性能，尤其是在数据量大的情况下。
*   **临时 ID**: 后端为每条记录生成了一个基于 SN 号 hash 的临时 `id`，仅用于前端实现行选择和导出选中功能，并非数据库主键。
*   **后端 API 行为变更**：当 `/api/after-sales-query/search` 接口在没有任何查询参数（`productCode`, `softwareVersion`, `pcbaOrderNumber` 都为空）的情况下被调用时，现在会返回 `success: true` 以及空的 `data` 列表和 `total: 0`，而不是之前的 400 错误。这主要是为了配合前端初始化的显示逻辑。
