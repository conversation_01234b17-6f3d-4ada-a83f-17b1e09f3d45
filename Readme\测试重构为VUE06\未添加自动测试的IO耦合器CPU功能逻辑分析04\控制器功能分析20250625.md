# CPU控制器测试系统功能逻辑分析

## 系统概述

CPU控制器测试系统是一个基于Web的产品测试管理平台，专门用于CPU控制器模块的质量检测和数据记录。系统采用前后端分离架构，通过本地代理服务器与设备通信，实现设备信息查询、程序加载、版本验证等功能。相比其他测试系统，CPU控制器系统具有更复杂的设备交互和更多的测试项目。

## 1. 工单验证逻辑

### 1.1 工单号输入触发机制和防抖处理

CPU控制器系统采用防抖机制处理工单号输入，通过500ms延迟避免频繁的API调用：

```javascript
// 使用防抖包装查询函数，设置500ms延迟
const debouncedQuery = debounce(queryOrderInfo, 500);

orderNumberInput.addEventListener('input', function() {
    const orderNumber = this.value.trim();
    debouncedQuery(orderNumber);
});
```

**防抖机制特点：**
- 延迟时间：500ms，平衡响应速度和性能
- 触发条件：用户在工单号输入框中输入内容
- 自动取消：新输入会取消之前的延迟调用

### 1.2 工单状态检查的具体验证规则

系统对工单进行严格的多层验证：

**存在性验证：**
- 工单号必须在系统数据库中存在
- 调用API `/api/work-order/by-number` 进行查询

**阶段完成验证：**
```javascript
if (!data.order.test_stage_completed) {
    // 清空并移除绿色背景
    const fieldsToReset = ['productionQuantity', 'productCode', 'productModel', 'batchNumber', 'snByteCount'];
    fieldsToReset.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.value = '';
            element.classList.remove('input-has-value');
        }
    });
    await Swal.fire({
        title: '提示',
        text: '该工单未完成测试前阶段外观检验',
        icon: 'warning',
        confirmButtonText: '确定'
    });
}
```

**PCBA检查配置：**
- 根据工单配置设置PCBA绑定检查标志（`ord_requires_pcba_check`）
- 控制后续SN号检测流程的执行策略

### 1.3 工单信息查询的API调用流程

```
用户输入工单号 → 防抖处理(500ms) → API调用(/api/work-order/by-number)
    ↓
状态验证(test_stage_completed) → 自动填充字段 → 设置PCBA检查标志
    ↓
版本信息获取(/api/cpu-controller/get-version-info) → 填充指定版本字段
    ↓
验证失败 → 清空字段 → 用户提示
```

### 1.4 验证失败时的错误处理和用户提示

**字段清空策略：**
- 清空相关字段：生产数量、产品编码、产品型号、批次号、SN字节数
- 清空版本信息：指定版本、指定日期、指定背板、指定高速IO
- 移除视觉状态：清除输入框的绿色背景样式

**用户提示机制：**
- 使用Swal.fire显示具体错误信息
- 错误类型包括：工单不存在、测试前阶段未完成、网络连接失败
- 提供明确的操作指导和解决建议

**状态重置：**
```javascript
// 重置PCBA检查标志为默认值
window.cpuControllerState.requiresPcbaCheck = true;
```

### 1.5 自动填充和版本信息获取

验证成功后，系统会：
1. **自动填充基本字段**：生产数量、产品编码、产品型号、批次号、SN字节数
2. **调用版本信息API**：根据工单号获取对应的版本信息
3. **填充版本字段**：软件版本、构建日期、背板版本、高速IO版本
4. **视觉反馈**：为已填充字段添加绿色背景样式

## 2. 检测状态逻辑

### 2.1 SN号输入后的检测流程

CPU控制器系统在产品SN号输入框失焦时触发检测流程：

```javascript
productSNInput.addEventListener('blur', async function() {
    const sn = this.value.trim();
    if (!sn) return;

    // 如果当前工单不需要PCBA检查，则跳过检查
    if (!window.cpuControllerState.requiresPcbaCheck) {
        console.log('当前工单无需PCBA绑定检查，允许继续测试');
        return;
    }
    // 执行PCBA绑定检查...
});
```

**检测流程特点：**
- 触发时机：失焦事件（blur），避免输入过程中的干扰
- 前置条件：SN号不为空
- 条件检查：根据工单配置决定是否需要PCBA验证

### 2.2 PCBA绑定状态的检查机制

**检查策略分类：**

*需要PCBA检查的情况：*
- 调用API `/api/cpu-controller/check-sn` 验证SN号绑定状态
- 验证产品是否已完成PCBA绑定流程
- 未绑定时阻止继续测试流程

*无需PCBA检查的情况：*
- 跳过PCBA绑定验证
- 直接允许继续测试流程
- 在控制台输出跳过检查的日志信息

### 2.3 产品状态对检测流程的影响

CPU控制器系统支持三种产品状态：
- **新品(new)**：全新产品的标准测试流程
- **维修(used)**：维修后产品的复测流程  
- **返工(refurbished)**：返工产品的重新测试流程

**状态影响：**
- 产品状态主要影响数据提交时的状态映射和计数逻辑
- 不同状态在检测流程中的处理方式相同
- 状态信息用于后续的维修返工次数统计

### 2.4 检测失败时的处理逻辑

当PCBA绑定检查失败时：

```javascript
if (!data.exists) {
    await SweetAlert.warning('该SN号未绑定PCBA！');
    this.value = ''; // 清空输入框
    this.focus();    // 重新获取焦点
    return;
}
```

**处理步骤：**
1. **用户提示**：显示"该SN号未绑定PCBA！"警告信息
2. **输入清空**：清空SN号输入框内容
3. **焦点重置**：自动将焦点重新设置到SN号输入框
4. **流程中断**：阻止后续的测试提交流程

## 3. 版本比较逻辑

### 3.1 自动版本信息获取的触发条件

CPU控制器系统的版本信息获取有特定的触发机制：

**主要触发点：**
- **工单验证成功**：当工单验证通过后自动调用版本信息获取
- **基于工单号**：使用工单号而非SN号获取版本信息
- **一次性获取**：获取所有相关版本信息（软件版本、构建日期、背板版本、高速IO版本）

### 3.2 版本信息的数据来源和获取方式

**API调用：**
```javascript
async function fetchVersionInfoByWorkOrder(workOrder) {
    const response = await fetch(`/api/cpu-controller/get-version-info?work_order=${encodeURIComponent(workOrder)}`);
    const data = await response.json();
    
    if (data.success && data.data) {
        // 自动填充版本信息到指定字段
        if (specifiedVersionInput && data.data.software_version) {
            specifiedVersionInput.value = data.data.software_version;
        }
        // ... 其他版本字段填充
    }
}
```

**数据字段映射：**
- `software_version` → 指定版本字段
- `build_time` → 指定日期字段  
- `backplane_version` → 指定背板字段
- `io_version` → 指定高速IO字段（特殊处理："-"前加空格）

### 3.3 版本比较规则和验证机制

CPU控制器系统采用设备查询时的实时比较验证：

**比较时机：**
- 在设备信息查询时进行版本比较
- 通过"查询"按钮触发设备信息获取和版本验证

**比较规则：**
```javascript
// 检查软件版本
const specifiedVersion = document.getElementById('specifiedVersion').value.trim();
if (specifiedVersion && specifiedVersion !== deviceData.data.softwareversion) {
    errors.push(`软件版本错误 预期: ${specifiedVersion} 实际: ${deviceData.data.softwareversion}`);
}

// 检查高速IO版本（特殊处理）
const normalizedDeviceHighSpeed = (deviceData.data.higspeedIOversion === '-') ? ' -' : deviceData.data.higspeedIOversion;
const normalizedSpecifiedHighSpeed = (specifiedHighSpeed === '-') ? ' -' : specifiedHighSpeed;
if (normalizedSpecifiedHighSpeed && normalizedSpecifiedHighSpeed !== normalizedDeviceHighSpeed) {
    errors.push(`高速IO版本错误 预期: ${normalizedSpecifiedHighSpeed} 实际: ${normalizedDeviceHighSpeed}`);
}
```

**验证项目：**
- 软件版本精确匹配
- 构建日期精确匹配
- 背板版本精确匹配
- 高速IO版本匹配（特殊处理"-"值）
- ODM信息匹配

### 3.4 版本不一致时的错误处理

**错误收集机制：**
- 收集所有版本不一致的错误信息
- 使用数组存储多个错误项
- 统一显示所有错误信息

**错误显示：**
```javascript
if (errors.length > 0) {
    await Swal.fire({
        title: '设备检查结果',
        html: errors.map(error => `• ${error}`).join('<br>'),
        icon: 'error',
        confirmButtonText: '确定'
    });
} else {
    await SweetAlert.success('设备信息一致');
}
```

**错误类型：**
- 版本不一致：显示预期值和实际值的对比
- 设备异常：如序列号为"-"、背板版本为"V0.0.0.0"
- 数据异常：如M区数据非零需要清除

## 4. 测试提交逻辑

### 4.1 提交前的数据验证步骤

CPU控制器系统采用严格的多层验证机制：

**第一层：必填字段验证**
```javascript
const requiredFields = {
    'tester': '测试人员',
    'orderNumber': '加工单号',
    'productionQuantity': '生产数',
    'productCode': '产品编码',
    'productModel': '产品型号',
    'productStatus': '产品状态',
    'productSN': '产品SN号',
    'snByteCount': '产品SN号字节数',
    'serialNumber': '出厂序号'
};
```

**第二层：特殊字段验证**
- 出厂序号不能为"-"的特殊检查
- SN号长度与预设字节数的精确匹配验证
- 字节数输入的有效性验证（非NaN）

**第三层：业务逻辑验证**
- 产品状态映射验证
- 维修返工次数计算验证

### 4.2 表单数据的收集和格式转换

**基本信息收集：**
```javascript
const formData = {
    // 基本信息
    tester: document.getElementById('tester').value,
    test_time: document.getElementById('testTime').value,
    work_order: document.getElementById('orderNumber').value.trim(),
    // ... 其他基本字段
    
    // 设备信息
    device_name: document.getElementById('deviceName').value || 'N/A',
    serial: document.getElementById('serialNumber').value || 'N/A',
    sw_version: document.getElementById('softwareVersion').value || 'N/A',
    back_ver: document.getElementById('backplaneVersion').value || 'N/A',
    high_speed_io_version: document.getElementById('highSpeedIOVersion').value || 'N/A',
    'build_date': document.getElementById('buildDate').value || 'N/A'
};
```

**产品状态转换：**
```javascript
const productStatusMap = {
    'new': 1,      // 新品
    'used': 2,     // 维修
    'refurbished': 3  // 返工
};
formData.pro_status = productStatusMap[productStatus];

// 设置维修和返工次数
formData.maintenance = formData.pro_status === 2 ? 1 : 0;
formData.rework = formData.pro_status === 3 ? 1 : 0;
```

### 4.3 测试结果的默认状态和用户选择处理

**CPU控制器特有的15个测试项目：**
1. RS485_1通信、RS485_2通信、RS232通信
2. CANbus通信、EtherCAT通信
3. Backplane Bus通信、Body I/O输入输出
4. Led数码管、Led灯珠
5. U盘接口、SD卡槽、调试串口
6. 网口、拨码开关、复位按钮

**默认状态设计：**
```javascript
<select id="test-result-${index}" data-code="${item.code}">
    <option value="通过" selected>通过</option>
    <option value="不通过">不通过</option>
</select>
```

**测试结果转换：**
```javascript
const testResults = {
    rs485_1: formData.rs485_1_result === '通过' ? 1 : 2,
    rs485_2: formData.rs485_2_result === '通过' ? 1 : 2,
    // ... 其他测试项目
    reset_btn: formData.reset_btn_result === '通过' ? 1 : 2
};

// 确定整体测试状态
const allTestsPassed = Object.values(testResults).every(result => result === 1);
formData.test_status = allTestsPassed ? 'pass' : 'ng';
```

### 4.4 提交成功后的状态管理和界面重置

**字段保持策略：**
```javascript
const savedValues = {
    // 基本信息卡片需要保留的字段
    tester: document.getElementById('tester').value,
    orderNumber: document.getElementById('orderNumber').value,
    // ... 其他基本信息字段
    
    // 设备信息卡片需要保留的字段
    specifiedVersion: document.getElementById('specifiedVersion').value,
    specifiedTime: document.getElementById('specifiedTime').value,
    specifiedBackplane: document.getElementById('specifiedBackplane').value,
    specifiedHighSpeed: document.getElementById('specifiedHighSpeed').value,
    odmInfo: document.getElementById('odmInfo').value,
    ipAddress: document.getElementById('ipAddress').value
};
```

**选择性清空：**
```javascript
// 清除设备信息字段的值和样式
const clearDeviceInfoFields = [
    'deviceManufacturer', 'deviceName', 'serialNumber',
    'softwareVersion', 'buildDate', 'backplaneVersion',
    'highSpeedIOVersion', 'macAddress', 'mAreaData'
];
clearDeviceInfoFields.forEach(fieldId => {
    const element = document.getElementById(fieldId);
    if (element) {
        element.value = ''; // 清空值
        element.classList.remove('input-has-value'); // 移除绿色背景
    }
});
```

**UI状态控制：**
```javascript
// 暂时禁用所有提交按钮，直到用户输入新的SN
const submitButton = document.querySelector('.submit-button');
const quickSubmitButton = document.getElementById('quickSubmit');

if (submitButton) submitButton.disabled = true;
if (quickSubmitButton) quickSubmitButton.disabled = true;

// 添加一次性事件监听器，当用户输入时重新启用按钮
productSNInput.addEventListener('input', function onFirstInput() {
    if (submitButton) submitButton.disabled = false;
    if (quickSubmitButton) quickSubmitButton.disabled = false;
    
    // 移除监听器，避免重复执行
    productSNInput.removeEventListener('input', onFirstInput);
});
```

## 功能模块间的逻辑关系和数据流转

### 模块间依赖关系
```
工单验证 → 版本信息获取 → 设备检测 → 测试提交
    ↓         ↓           ↓         ↓
前置条件   版本预设     PCBA验证   数据完整
    ↓         ↓           ↓         ↓
自动填充   设备比较     状态检查   智能重置
```

### 数据流转路径
1. **工单驱动流程**：工单号输入 → 工单验证 → 字段自动填充 → 版本信息获取
2. **设备交互流程**：IP地址选择 → 设备信息查询 → 版本比较验证 → 错误检查
3. **检测验证流程**：SN号输入 → PCBA绑定检查 → 测试准备完成
4. **提交处理流程**：数据收集 → 多层验证 → 格式转换 → API提交 → 状态重置

### 特有功能特点
- **设备通信**：通过本地代理服务器与设备进行TCP通信
- **实时验证**：设备查询时进行版本信息的实时比较
- **复杂测试项目**：15个不同类型的测试项目
- **设备操作**：支持设备重启、程序加载、恢复出厂设置等操作
- **网络信息获取**：支持多网口MAC地址和内存数据的获取

这个系统通过复杂的设备交互和严格的验证机制，确保了CPU控制器测试流程的准确性和可靠性。
