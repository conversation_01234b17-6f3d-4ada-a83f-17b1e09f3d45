# 打标记录查询 (SN Marking Record Query)

## 1. 页面功能概述

"打标记录查询"页面用于查询和管理激光打标的SN（序列号）记录。用户可以通过多种条件筛选记录，对记录进行排序和分页查看，并能对正常的SN记录执行作废操作。

主要功能包括：

*   **记录查询**:
    *   按工单号查询。
    *   按SN号查询。
    *   按操作时间范围（开始日期、结束日期）查询。
    *   支持上述条件的组合查询。
*   **结果展示**:
    *   以表格形式清晰展示查询结果，包含序号、加工单号、产品编码、产品型号、SN号、SN号状态、生产数量、操作人员、操作时间及操作列。
    *   SN号状态定义：
        *   `1` (正常)：记录有效。
        *   `2` (作废)：记录已作废，SN号前缀会添加 "zf"。
*   **排序功能**:
    *   支持对加工单号、产品编码、产品型号、SN号、SN号状态、生产数量、操作人员、操作时间等列进行升序或降序排序。
*   **分页浏览**:
    *   提供分页控件，方便用户浏览大量数据。
    *   用户可自定义每页显示的记录条数（如10, 20, 50, 100条）。
    *   显示符合查询条件的总记录数。
*   **记录作废**:
    *   对于状态为"正常"的SN记录，操作列提供"作废"按钮。
    *   点击作废按钮后，系统会弹出确认提示。
    *   确认作废后，该记录的SN号会被修改，同时其SN状态更新为 `2` (作废)。作废后的SN号将遵循特定规则以确保唯一性：
        *   **首次作废**：原始SN（例如 `SN123`）会被修改为 `zf-SN123`。
        *   **多次作废同一原始SN**：如果原始SN（如 `SN123`）在同一工单下被恢复并再次使用后需要再次作废，系统会自动在 `zf` 和原始SN之间添加一个递增的数字序号，以确保新生成的作废SN的唯一性。例如，第二次作废会生成 `zf1-SN123`，第三次会生成 `zf2-SN123`，以此类推。
    *   此机制确保了作废记录的留存，并且所有作废历史均可追溯，同时使得原始SN号（如 `SN123`）可以被重新用于新的打标记录。
    *   已作废的记录在操作列会显示"已作废"文本。
*   **用户体验**:
    *   提供查询条件重置功能。
    *   在数据加载和操作过程中显示加载指示。
    *   通过弹窗（SweetAlert2）提供清晰的操作反馈，如查询成功、查询失败、作废成功、作废失败等。

## 2. 后端API接口

### 2.1 查询打标记录

*   **HTTP方法**: `GET`
*   **Endpoint**: `/api/marking-records/search`
*   **功能描述**: 根据提供的查询参数获取打标记录列表，支持排序和分页。
*   **请求参数 (Query Parameters)**:
    *   `type` (string, 可选): 查询类型。
        *   `orderNo`: 按工单号查询 (默认)。
        *   `serialNumber`: 按SN号查询。
    *   `value` (string, 可选): 根据 `type` 指定的查询类型的具体查询值。
    *   `startDate` (string, 可选): 查询范围的开始日期，格式 `YYYY-MM-DD`。
    *   `endDate` (string, 可选): 查询范围的结束日期，格式 `YYYY-MM-DD`。
    *   `page` (integer, 可选): 请求的页码，默认为 `1`。
    *   `pageSize` (integer, 可选): 每页显示的记录数，默认为 `10`。
    *   `sort_field` (string, 可选): 用于排序的字段名。可用值包括：
        *   `work_order_no` (加工单号)
        *   `product_code` (产品编码)
        *   `product_model` (产品型号)
        *   `serial_number` (SN号)
        *   `sn_status` (SN号状态)
        *   `production_quantity` (生产数量)
        *   `operator` (操作人员)
        *   `operation_time` (操作时间 - 默认排序字段)
    *   `sort_order` (string, 可选): 排序顺序。
        *   `asc`: 升序。
        *   `desc`: 降序 (默认)。
*   **成功响应 (200 OK)**:
    ```json
    {
        "success": true,
        "records": [
            {
                "work_order_no": "订单号示例",
                "product_code": "产品编码示例",
                "product_model": "产品型号示例",
                "serial_number": "SN号示例",
                "sn_status": 1, // 1: 正常, 2: 作废
                "production_quantity": 100,
                "operator": "操作员张三",
                "operation_time": "2023-10-26 14:30:00"
            }
            // ...更多记录
        ],
        "totalCount": 125 // 符合条件的总记录数
    }
    ```
*   **失败响应 (4xx/5xx)**:
    ```json
    {
        "success": false,
        "message": "错误描述信息"
    }
    ```

### 2.2 作废SN打标记录

*   **HTTP方法**: `PUT`
*   **Endpoint**: `/api/marking-records/void`
*   **功能描述**: 将指定的正常SN打标记录作废。作废操作会修改SN号（添加"zf"前缀）并更新状态。
*   **请求体 (Request Body - JSON)**:
    ```json
    {
        "work_order_no": "需要作废记录的工单号",
        "serial_number": "需要作废记录的原始SN号"
    }
    ```
*   **成功响应 (200 OK)**:
    ```json
    {
        "success": true,
        "message": "SN号 [原始SN号] 已成功作废。"
    }
    ```
*   **失败响应 (4xx/5xx)**:
    *   `400 Bad Request`: 请求参数错误、记录已作废或状态异常无法作废。
    *   `404 Not Found`: 未找到指定的记录。
    *   `500 Internal Server Error`: 服务器内部错误。
    ```json
    {
        "success": false,
        "message": "具体的错误描述，例如：'未找到指定的打标记录' 或 '该SN记录已作废，无需重复操作'"
    }
    ```

## 3. 前端实现简述

前端页面 (`MarkingRecord.js` 和 `MarkingRecord.css`) 负责：

*   构建用户交互界面，包括查询表单、结果表格、分页控件等。
*   处理用户输入和事件（如点击查询、选择日期、切换分页、点击排序、点击作废）。
*   调用后端API获取数据和执行操作。
*   动态渲染查询结果和分页信息。
*   使用 `SweetAlert2` 库提供用户友好的提示和确认对话框。
*   根据SN状态 (`sn_status`) 决定是否显示"作废"按钮，并处理其点击逻辑。
*   作废成功后刷新表格数据。 