#!/bin/bash

# 项目基础配置
export PROJECT_NAME="kmlc_plc"
export PROJECT_PATH="/var/www/${PROJECT_NAME}"
export PYTHON_VERSION="3.12.8"
export PYTHON_PATH="/usr/bin/python3.12"
export PYTHON_BIN="${PYTHON_PATH}"
export VENV_PATH="${PROJECT_PATH}/venv"

# 服务器配置
export SERVER_IP="*************"
export SERVER_PORT="3308"

# 数据库配置
export DB_NAME="kmlc_plc"
export DB_USER="root"
export DB_PASSWORD="kmlc@3302133"
export DB_HOST="*************"
export DB_PORT="3309"
export DB_CHARSET="utf8mb4"
export DB_COLLATE="utf8mb4_general_ci"

# Gunicorn配置
export GUNICORN_HOST="*************"
export GUNICORN_PORT="3308"
export GUNICORN_WORKERS=$(($(nproc) * 2 + 1))
export GUNICORN_THREADS="2"

# 项目目录结构
export STATIC_PATH="${PROJECT_PATH}/static"
export TEMPLATES_PATH="${PROJECT_PATH}/templates"
export MODELS_PATH="${PROJECT_PATH}/models"
export ROUTES_PATH="${PROJECT_PATH}/routes"
export SERVICES_PATH="${PROJECT_PATH}/services"
export DATABASE_PATH="${PROJECT_PATH}/database"

# 日志配置
export LOG_PATH="${PROJECT_PATH}/logs"
export ACCESS_LOG="${LOG_PATH}/access.log"
export ERROR_LOG="${LOG_PATH}/error.log"

# 用户配置
export APP_USER="www-data"
export APP_GROUP="www-data"

# 备份配置
export BACKUP_PATH="${PROJECT_PATH}/backups"
export BACKUP_RETENTION_DAYS=7
export BACKUP_MAX_COUNT=5  # 保留的最大备份数量
export BACKUP_COMPRESS=true  # 是否压缩备份

# 日志配置补充
export LOG_ROTATE_SIZE="100M"
export LOG_ROTATE_COUNT=10
export LOG_LEVEL="info"

# 超时配置
export MYSQL_WAIT_TIMEOUT=30  # MySQL启动等待时间
export SERVICE_WAIT_TIMEOUT=30  # 服务启动等待时间

# 检查是否为root用户
check_root() {
    if [ "$(id -u)" != "0" ]; then
        echo "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查上一个命令是否成功
check_status() {
    if [ $? -ne 0 ]; then
        echo "错误: $1"
        exit 1
    fi
}

# 创建目录函数
create_directory() {
    mkdir -p "$1"
    check_status "创建目录 $1 失败"
    chown "${APP_USER}:${APP_GROUP}" "$1"
    chmod 755 "$1"
}

# 验证函数
validate_config() {
    local required_vars=(
        "PROJECT_PATH" "PYTHON_BIN" "DB_PASSWORD"
        "GUNICORN_HOST" "GUNICORN_PORT"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo "错误: 必需的配置变量 $var 未设置"
            exit 1
        fi
    done
}

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${LOG_PATH}/deploy.log"
} 