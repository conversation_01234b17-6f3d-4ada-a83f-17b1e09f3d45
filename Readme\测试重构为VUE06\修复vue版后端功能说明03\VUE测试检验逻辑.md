# VUE测试检验逻辑说明文档

## 概述

本文档详细说明IOModuleVue.js (IO模块Vue版本测试系统)的完整校验逻辑、数据流向和实现机制。该系统采用多层级验证架构，确保测试数据的准确性和完整性。

## 1. 工单号验证逻辑

### 1.1 触发机制
- **触发器**: `watch(() => formData.orderNumber)`监听器
- **防抖处理**: `debouncedQueryOrderInfo(500ms)`，避免频繁API调用
- **输入事件**: 用户在"加工单号"输入框中输入时自动触发

### 1.2 验证流程
```javascript
// 数据流向：用户输入 → 防抖 → API调用 → 数据验证 → UI更新
const queryOrderInfo = async (orderNumber) => {
    // Step 1: 空值处理
    if (!orderNumber) {
        // 清空相关字段，重置PCBA检查标志
        clearRelatedFields();
        return;
    }

    // Step 2: API调用
    const response = await fetch(`/api/work-order/by-number?orderNumber=${orderNumber}`);
    const data = await response.json();
    
    // Step 3: 存在性验证
    if (data.success && data.order) {
        // Step 4: 测试前阶段验证
        if (!data.order.test_stage_completed) {
            // 工单存在但未完成外观检验
            showWarning('该工单未完成测试前阶段外观检验');
            clearRelatedFields();
            return;
        }
        
        // Step 5: 自动填充字段
        autoFillOrderFields(data.order);
        
        // Step 6: 设置PCBA检查标志
        requiresPcbaCheck.value = data.order.ord_requires_pcba_check !== false;
        
        // Step 7: UI状态更新
        if (!basicInfoCollapsed.value) {
            basicInfoCollapsed.value = true; // 自动折叠基本信息
        }
    }
}
```

### 1.3 自动填充字段
- `ord_productionQuantity` → `formData.productionQuantity`
- `ord_productCode` → `formData.productCode` 
- `ord_productModel` → `formData.productModel`
- `ord_probatch` → `formData.batchNumber`
- `ord_snlenth` → `formData.snByteCount`

## 2. SN号多重验证体系

### 2.1 验证链条架构
```
用户输入SN → 订货号验证 → PCBA绑定验证 → 版本信息获取 → 长度最终验证
```

### 2.2 订货号匹配验证 (新增功能)
```javascript
const validateSnOrderNumber = async (sn, productCode) => {
    // 前置条件检查
    if (!productCode) {
        showError('产品编码缺失，无法验证SN');
        return false;
    }
    
    // API验证调用
    const response = await fetch(`/api/products/validate-sn-order?sn=${sn}&productCode=${productCode}`);
    const result = await response.json();
    
    if (!result.success) {
        showError(result.message || 'SN与工单订货号不匹配！');
        return false;
    }
    
    return true;
}
```

### 2.3 PCBA绑定验证
```javascript
const checkSN = async (sn) => {
    // Step 1: 订货号验证 (优先级最高)
    const isOrderNumberValid = await validateSnOrderNumber(sn, formData.productCode);
    if (!isOrderNumberValid) {
        formData.productSN = ''; // 清空输入框
        focusToField('sn');     // 重新聚焦
        return; // 中断后续操作
    }
    
    // Step 2: PCBA检查条件判断
    if (!requiresPcbaCheck.value) {
        // 跳过PCBA检查，直接获取版本信息
        await autoFetchVersionInfo(sn);
        return;
    }
    
    // Step 3: PCBA绑定API验证
    const response = await fetch(`/api/io-modulevue/check-sn?sn=${sn}`);
    const data = await response.json();
    
    if (data.success && data.exists) {
        // PCBA绑定正常，获取版本信息
        await autoFetchVersionInfo(sn);
    } else {
        // PCBA未绑定处理
        showWarning('该SN号未绑定PCBA！');
        formData.productSN = '';
        focusToField('sn');
    }
}
```

### 2.4 SN长度验证 (提交时最终验证)
```javascript
// 在submitForm函数中执行
const snByteCount = parseInt(formData.snByteCount);
const productSN = String(formData.productSN || '');

if (productSN.length !== snByteCount) {
    showError(`SN号长度不符合要求：当前${productSN.length}字符，要求${snByteCount}字符`);
    focusToField('productSN');
    return;
}
```

## 3. 版本信息获取与比对

### 3.1 自动版本信息获取
```javascript
const autoFetchVersionInfo = async (productSN) => {
    const productStatus = formData.productStatus;
    
    // 支持的产品状态
    if (!['new', 'used', 'refurbished'].includes(productStatus)) {
        return; // 跳过不支持的状态
    }

    // API调用获取版本信息
    const response = await fetch(`/api/io-modulevue/get-version-info?product_sn=${productSN}&product_status=${productStatus}`);
    const data = await response.json();
    
    if (data.success && data.data) {
        // 自动填充版本信息
        formData.autoVersion = data.data.software_version;
        formData.autoDate = data.data.build_time;
        
        // 触发自动提交检查
        if (autoSubmitEnabled.value) {
            setTimeout(() => triggerAutoSubmitIfEnabled(productSN), 500);
        }
    }
}
```

### 3.2 版本一致性比对
```javascript
const isVersionConsistent = computed(() => {
    // 无自动获取版本时认为一致
    if (!formData.autoVersion && !formData.autoDate) return true;
    
    // 版本比对
    const versionMatch = !formData.autoVersion || !formData.ioVersion || 
                        formData.autoVersion === formData.ioVersion;
    const dateMatch = !formData.autoDate || !formData.ioBuildDate || 
                     formData.autoDate === formData.ioBuildDate;
    
    return versionMatch && dateMatch;
});
```

### 3.3 提交时版本验证
```javascript
// 详细的版本不一致错误提示
if (!isVersionConsistent.value) {
    if (formData.autoVersion !== formData.ioVersion) {
        showError(`IO软件版本不匹配：自动获取"${formData.autoVersion}"与手动输入"${formData.ioVersion}"不一致`);
        focusToField('ioVersion');
        return;
    }
    
    if (formData.autoDate !== formData.ioBuildDate) {
        showError(`IO构建日期不匹配：自动获取"${formData.autoDate}"与手动输入"${formData.ioBuildDate}"不一致`);
        focusToField('ioBuildDate');
        return;
    }
}
```

## 4. 提交验证规则

### 4.1 表单验证层级
```javascript
const submitForm = async () => {
    // 第一层：Element Plus表单验证
    try {
        await formRef.value.validate();
    } catch (validationErrors) {
        handleValidationErrors(validationErrors);
        return;
    }
    
    // 第二层：版本一致性验证
    if (!isVersionConsistent.value) {
        // 详细版本错误处理...
        return;
    }
    
    // 第三层：SN长度验证
    if (productSN.length !== snByteCount) {
        // SN长度错误处理...
        return;
    }
    
    // 第四层：产品状态验证
    if (!submitData.pro_status) {
        showWarning('请选择产品状态！');
        return;
    }
    
    // 提交数据到后端
    await submitToBackend(submitData);
}
```

### 4.2 测试结果处理策略
```javascript
// 默认通过策略：只有明确标记为"不通过"的才算失败
const submitData = {
    backplane: testItems.value[0].result === '不通过' ? 2 : 1,
    body_io: testItems.value[1].result === '不通过' ? 2 : 1,
    led_bulb: testItems.value[2].result === '不通过' ? 2 : 1
};
```

### 4.3 产品状态映射
```javascript
const productStatusMap = {
    'new': 1,        // 新品
    'used': 2,       // 维修 (maintenance = 1)
    'refurbished': 3 // 返工 (rework = 1)
};
```

## 5. 自动提交规则

### 5.1 自动提交触发条件
```javascript
const triggerAutoSubmitIfEnabled = async (sn) => {
    // 条件1：自动提交开关启用
    if (!autoSubmitEnabled.value) return;
    
    // 条件2：执行预校验
    const validation = performAutoValidation();
    if (!validation.allValid) {
        // 校验失败，转为手动模式
        showWarning(`自动提交失败：${validation.errors[0]}。请手动检查并提交。`);
        return;
    }
    
    // 条件3：执行自动提交流程
    try {
        setAllPass();              // 自动设置全通过
        await delay(600);          // 等待UI更新
        await submitForm();        // 自动提交表单
    } catch (error) {
        showError('自动提交失败，请手动操作');
    }
}
```

### 5.2 自动校验规则
```javascript
const performAutoValidation = () => {
    const errors = [];
    
    // 校验1：SN有效性
    if (!productSN) errors.push('产品SN号为空');
    if (isNaN(snByteCount)) errors.push('SN号字节数无效');
    if (productSN.length !== snByteCount) errors.push('SN号长度不符合要求');
    
    // 校验2：版本一致性
    if (!isVersionConsistent.value) errors.push('版本信息不一致');
    
    // 校验3：必填字段
    if (!formData.tester || !formData.orderNumber || !formData.productCode ||
        !formData.productModel || !formData.ioVersion || !formData.ioBuildDate) {
        errors.push('必填字段未完整填写');
    }
    
    return { allValid: errors.length === 0, errors };
}
```

## 6. 自动聚焦与事件触发

### 6.1 智能聚焦系统
```javascript
const focusToField = (fieldType) => {
    nextTick(() => {
        let input;
        switch (fieldType) {
            case 'sn':
            case 'productSN':
                // 支持展开和折叠状态的SN输入框
                const snSelectors = [
                    'input[placeholder*="产品SN号"]',
                    'input[placeholder*="请输入产品SN号"]',
                    '.io-module__form-collapsed input[placeholder*="SN"]',
                    '.io-module__form-expanded input[placeholder*="SN"]'
                ];
                for (const selector of snSelectors) {
                    input = document.querySelector(selector);
                    if (input && input.offsetParent !== null) break;
                }
                break;
            // 其他字段处理...
        }
        
        if (input) {
            input.focus();
            input.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    });
}
```

### 6.2 事件触发机制
```javascript
// SN输入事件绑定
@keyup.enter.prevent="checkSN(formData.productSN)"  // 回车触发
@blur="checkSN(formData.productSN)"                // 失焦触发

// 工单号监听
watch(() => formData.orderNumber, (newVal) => {
    if (newVal) debouncedQueryOrderInfo(newVal);
});

// 产品状态监听
watch(() => formData.productStatus, () => {
    // 清空自动版本信息
    formData.autoVersion = '';
    formData.autoDate = '';
    
    // 重新获取版本信息
    if (['new', 'used', 'refurbished'].includes(formData.productStatus) && formData.productSN) {
        autoFetchVersionInfo(formData.productSN);
    }
});
```

### 6.3 错误处理时的自动聚焦
```javascript
const handleValidationErrors = (errors) => {
    // 获取第一个错误字段
    const firstErrorField = Object.keys(errors)[0];
    const fieldName = fieldNameMap[firstErrorField];
    
    // 显示具体错误信息
    showError(`${fieldName}：${errors[firstErrorField][0].message}`);
    
    // 自动聚焦到错误字段
    focusToField(firstErrorField);
}
```

## 7. 数据流向总结

### 7.1 完整数据流向图
```
用户操作输入
    ↓
工单号验证 → 存在性检查 → 阶段完成验证 → 自动填充基础信息
    ↓
SN号输入 → 订货号验证 → PCBA绑定验证 → 版本信息获取
    ↓
版本比对 → 一致性检查 → 手动修正 → 最终验证
    ↓
测试执行 → 结果录入 → 表单验证 → 数据提交
    ↓
成功处理 → 表单重置 → 聚焦准备 → 下一轮测试
```

### 7.2 错误处理流程
```
验证失败 → 错误分类 → 具体提示 → 自动聚焦 → 用户修正 → 重新验证
```

## 8. 潜在优化建议

### 8.1 已实现的优化
✅ 防抖机制减少API调用频率
✅ 详细的错误提示和自动聚焦
✅ 版本信息自动获取和比对
✅ 多层级验证确保数据准确性
✅ 自动提交功能提升效率
✅ 智能表单重置保留必要信息

### 8.2 可能的增强点
- API调用超时处理机制
- 离线状态检测和缓存
- 更细粒度的权限控制
- 批量SN验证功能
- 历史记录和回退功能

## 总结

IOModuleVue.js实现了一套完整的多层级验证体系，从工单验证到SN检查，从版本比对到最终提交，每个环节都有详细的错误处理和用户引导。通过智能聚焦、自动提交等功能，大大提升了测试人员的工作效率，同时保证了数据的准确性和完整性。 