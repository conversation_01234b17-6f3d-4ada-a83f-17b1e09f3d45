/* 使用BEM命名规范重构CSS */
.self-inspection {
    /* 现代化配色方案 */
    --si-background: #ffffff;
    --si-foreground: #1e293b;
    --si-card: #ffffff;
    --si-card-foreground: #1e293b;
    --si-popover: #ffffff;
    --si-popover-foreground: #020817;
    --si-primary: #3b82f6;
    --si-primary-foreground: #ffffff;
    --si-secondary: #f8fafc;
    --si-secondary-foreground: #1e293b;
    --si-muted: #f1f5f9;
    --si-muted-foreground: #64748b;
    --si-accent: #f1f5f9;
    --si-accent-foreground: #1e293b;
    --si-destructive: #ef4444;
    --si-destructive-foreground: #ffffff;
    --si-border: #e2e8f0;
    --si-input: #e2e8f0;
    --si-ring: #3b82f6;
    --si-radius: 0.5rem;

    --si-success: #10b981;
    --si-success-foreground: #ffffff;
    --si-info: #0ea5e9;
    --si-info-foreground: #ffffff;
    --si-warning: #f59e0b;
    --si-warning-foreground: #ffffff;

    /* 组件样式 */
    max-width: 95%;
    margin: 2rem auto;
    background-color: var(--si-background);
    border-radius: calc(var(--si-radius) + 4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    padding: 32px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 标题区域 */
.self-inspection__header {
    padding: 24px;
    background-color: var(--si-secondary);
    border-radius: var(--si-radius);
    margin-bottom: 32px;
    border: none;
}

.self-inspection__title {
    font-size: 24px;
    color: var(--si-foreground);
    margin-bottom: 20px;
    font-weight: 600;
    letter-spacing: -0.5px;
}

/* 工单信息区域 */
.self-inspection__order-info {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}

.self-inspection__input-group {
    flex: 1;
    min-width: 150px;
}

/* 新增：为输入组分配不同的flex比例 */
#orderNoGroup {
    flex: 2.5;
}

#serialNoGroup {
    flex: 2.5;
}

#productTypeGroup {
    flex: 1;
}

#reworkGroup {
    flex: 0.8;
}

#reworkGroup .self-inspection__checkbox-container {
    margin-bottom: 0;
    margin-top: 0 !important;
    padding: 0;
    display: flex;
    align-items: center;
}

#reworkGroup .self-inspection__checkbox {
    transform: scale(1.5);
    margin-right: 10px;
}

#reworkGroup .self-inspection__checkbox-label {
    font-size: 14px;
}

/* 跳过检验组样式 */
#skipInspectionGroup {
    flex: 1.2;
}

/* 开关样式 */
.self-inspection__switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.self-inspection__switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.self-inspection__slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: 0.3s;
    border-radius: 24px;
}

.self-inspection__slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .self-inspection__slider {
    background-color: var(--si-destructive);
}

input:checked + .self-inspection__slider:before {
    transform: translateX(26px);
}

/* 确认跳过按钮样式 */
.self-inspection__btn--danger {
    background-color: var(--si-destructive);
    color: var(--si-destructive-foreground);
    border: 1px solid var(--si-destructive);
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.self-inspection__btn--danger:hover {
    background-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

/* 帮助文本样式 */
.self-inspection__help-text {
    font-size: 12px;
    color: #64748b;
    margin-top: 5px;
    line-height: 1.4;
}

/* 返工标识样式 */
.rework-badge {
    display: inline-block;
    background-color: var(--si-warning);
    color: var(--si-warning-foreground);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 10px;
    vertical-align: middle;
    font-weight: 500;
}

.self-inspection__input-group--error .self-inspection__input {
    border-color: var(--si-destructive);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.self-inspection__label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
    color: #334155;
}

.self-inspection__required {
    color: var(--si-destructive);
    margin-left: 4px;
}

.self-inspection__input {
    width: 100%;
    padding: 10px 14px;
    border: 1px solid var(--si-border);
    border-radius: var(--si-radius);
    font-size: 14px;
    transition: all 0.2s;
    background-color: var(--si-background);
}

.self-inspection__input:focus {
    outline: none;
    border-color: var(--si-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.self-inspection__error-message {
    color: var(--si-destructive);
    font-size: 12px;
    margin-top: 6px;
    display: none;
}

.self-inspection__input-group--error .self-inspection__error-message {
    display: block;
}

/* 进度条 */
.self-inspection__progress {
    margin-top: 20px;
}

.self-inspection__progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    color: #475569;
    font-weight: 500;
}

.self-inspection__progress-bar {
    height: 10px;
    background-color: var(--si-muted);
    border-radius: 100px;
    overflow: hidden;
}

.self-inspection__progress-inner {
    height: 100%;
    background: var(--si-primary);
    border-radius: 100px;
    transition: width 0.5s ease;
}

/* 工单状态概览样式 */
.self-inspection__status-overview {
    margin: 20px 0;
    background-color: var(--si-secondary);
    border-radius: var(--si-radius);
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.self-inspection__status-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--si-foreground);
    margin-bottom: 12px;
}

.self-inspection__status-table {
    display: table;
    width: 100%;
    border-collapse: collapse;
}

.self-inspection__status-header {
    display: table-row;
    background-color: var(--si-muted);
    font-weight: 600;
}

.self-inspection__status-row {
    display: table-row;
    border-bottom: 1px solid var(--si-border);
}

.self-inspection__status-cell {
    display: table-cell;
    padding: 10px 12px;
    vertical-align: middle;
    border: 1px solid var(--si-border);
}

.self-inspection__status-completed {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--si-success);
}

.self-inspection__status-pending {
    color: var(--si-muted-foreground);
}

.self-inspection__status-icon {
    font-size: 18px;
    font-weight: bold;
    margin-right: 8px;
    display: inline-block;
}

.self-inspection__status-info {
    display: inline-block;
    vertical-align: middle;
}

.self-inspection__status-time {
    font-size: 12px;
    color: var(--si-muted-foreground);
    margin-top: 2px;
}

.self-inspection__refresh-button {
    margin-left: 10px;
    padding: 8px 12px;
    background-color: #DBEAFE; /* Light blue background */
    border: 1px solid #BFDBFE; /* Slightly darker blue border */
    border-radius: var(--si-radius);
    color: var(--si-foreground); /* Keep existing text color or adjust if needed */
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
}

.self-inspection__refresh-button:hover {
    background-color: #BFDBFE; /* Darker blue on hover */
    border-color: #93C5FD; /* Even darker border for hover or same as background */
}

.self-inspection__refresh-button i {
    margin-right: 8px;
    font-size: 16px;
}

/* 工单状态概览样式 */
.self-inspection__status-overview {
    margin: 20px 0;
    background-color: var(--si-secondary);
    border-radius: var(--si-radius);
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.self-inspection__status-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--si-foreground);
    margin-bottom: 12px;
}

.self-inspection__status-table {
    display: table;
    width: 100%;
    border-collapse: collapse;
}

.self-inspection__status-header {
    display: table-row;
    background-color: var(--si-muted);
    font-weight: 600;
}

.self-inspection__status-row {
    display: table-row;
    border-bottom: 1px solid var(--si-border);
}

.self-inspection__status-cell {
    display: table-cell;
    padding: 10px 12px;
    vertical-align: middle;
    border: 1px solid var(--si-border);
}

.self-inspection__status-completed {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--si-success);
}

.self-inspection__status-pending {
    color: var(--si-muted-foreground);
}

.self-inspection__status-icon {
    font-size: 18px;
    font-weight: bold;
    margin-right: 8px;
    display: inline-block;
}

.self-inspection__status-info {
    display: inline-block;
    vertical-align: middle;
}

.self-inspection__status-time {
    font-size: 12px;
    color: var(--si-muted-foreground);
    margin-top: 2px;
}

/* 时间线 */
.self-inspection__timeline {
    margin-top: 40px;
    position: relative;
}

.self-inspection__timeline-line {
    position: absolute;
    left: 15px;
    top: 32px;
    bottom: 0;
    width: 2px;
    background-color: var(--si-border);
}

/* 阶段卡片 */
.self-inspection__stage {
    position: relative;
    padding-left: 50px;
    margin-bottom: 32px;
}

.self-inspection__stage-node {
    position: absolute;
    left: 0;
    top: 18px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--si-background);
    border: 2px solid var(--si-border);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    color: var(--si-muted-foreground);
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
}

.self-inspection__stage-node--completed {
    background-color: var(--si-success);
    border-color: var(--si-success);
    color: var(--si-success-foreground);
}

.self-inspection__stage-node--locked {
    background-color: var(--si-muted);
    border-color: var(--si-border);
    color: var(--si-muted-foreground);
    opacity: 0.7;
}

.self-inspection__stage:not(:has(.self-inspection__stage-node--locked)):not(:has(.self-inspection__stage-card--completed)) .self-inspection__stage-node {
    border-color: var(--si-primary);
    color: var(--si-primary);
}

.self-inspection__stage-card {
    border: 1px solid var(--si-border);
    border-radius: var(--si-radius);
    overflow: hidden;
    transition: all 0.3s;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
    background-color: var(--si-card);
}

.self-inspection__stage-header {
    padding: 16px 24px;
    background-color: var(--si-background);
    border-bottom: 1px solid var(--si-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.self-inspection__stage-card--completed .self-inspection__stage-header {
    background-color: var(--si-success);
    border-bottom-color: rgba(255, 255, 255, 0.2);
    cursor: default;
}

.self-inspection__stage-header:hover {
    background-color: var(--si-muted);
}

.self-inspection__stage-card--completed .self-inspection__stage-header:hover {
    background-color: var(--si-success);
}

.self-inspection__stage-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--si-foreground);
}

.self-inspection__stage-card--completed .self-inspection__stage-title {
    color: var(--si-success-foreground);
}

.self-inspection__stage-description {
    font-size: 13px;
    color: var(--si-muted-foreground);
    margin-top: 4px;
}

.self-inspection__stage-card--completed .self-inspection__stage-description {
    color: rgba(255, 255, 255, 0.85);
}

.self-inspection__stage-status {
    display: flex;
    align-items: center;
}

.self-inspection__stage-progress {
    margin-right: 12px;
    font-size: 14px;
    color: #3b82f6;
    font-weight: 500;
}

.self-inspection__stage-card--completed .self-inspection__stage-progress {
    color: var(--si-success-foreground);
}

.self-inspection__stage-toggle {
    color: #334155;
    font-size: 14px;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.self-inspection__stage-card--completed .self-inspection__stage-toggle {
    color: var(--si-success-foreground);
    opacity: 0.8;
}

.self-inspection__stage-toggle::after {
    content: "▼";
    margin-left: 6px;
    transition: transform 0.3s;
    font-size: 10px;
    opacity: 0.7;
}

.self-inspection__stage-toggle--collapsed::after {
    transform: rotate(-90deg);
}

.self-inspection__stage-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.self-inspection__stage-content--expanded {
    max-height: 2000px;
}

/* 检验类型选项卡 */
.self-inspection__inspection-tabs {
    display: flex;
    border-bottom: 1px solid var(--si-border);
    background-color: var(--si-secondary);
    border-radius: 0;
    overflow: hidden;
    margin: 0;
    border-left: none;
    border-right: none;
    border-top: 1px solid var(--si-border);
}

.self-inspection__inspection-tab {
    flex: 1;
    text-align: center;
    padding: 12px 0;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    color: var(--si-muted-foreground);
    background-color: transparent;
    border-bottom: 3px solid transparent;
    margin-bottom: -1px;
}

.self-inspection__inspection-tab--active {
    color: var(--si-primary);
    background-color: transparent;
    border-bottom-color: var(--si-primary);
    font-weight: 600;
}

.self-inspection__inspection-tab:hover:not(.self-inspection__inspection-tab--active) {
    background-color: transparent;
    color: var(--si-primary);
    border-bottom-color: rgba(59, 130, 246, 0.3);
}

/* 操作人员信息区域 */
.self-inspection__operator-info {
    padding: 16px;
    margin: 16px;
    border-radius: var(--si-radius);
    border: 1px solid var(--si-border);
    background-color: var(--si-secondary);
    transition: all 0.3s;
}

.self-inspection__operator-info--submitted {
    background-color: rgba(16, 185, 129, 0.08);
    border-color: rgba(16, 185, 129, 0.3);
}

.self-inspection__operator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.self-inspection__operator-label {
    font-weight: 500;
    color: #334155;
}

.self-inspection__submitted-tag {
    background-color: var(--si-success);
    color: var(--si-success-foreground);
    padding: 3px 10px;
    border-radius: 100px;
    font-size: 12px;
    font-weight: 500;
}

.self-inspection__operator-time {
    margin-top: 6px;
    font-size: 14px;
    color: #64748b;
}

.self-inspection__operator-input {
    margin-top: 6px;
}

/* 检验项目列表 */
.self-inspection__inspection-content {
    padding: 16px;
    display: none;
    margin: 0;
}

.self-inspection__inspection-content--active {
    display: block;
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 复选框容器 */
.self-inspection__checkbox-container {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    padding: 10px 12px;
    border-radius: var(--si-radius);
    transition: background-color 0.2s;
    border: 1px solid transparent;
}

.self-inspection__checkbox-container:hover {
    background-color: var(--si-muted);
}

/* 使用原生复选框样式 */
.self-inspection__checkbox {
    margin-right: 10px;
    margin-top: 3px;
    /* 使用原生复选框 */
    -webkit-appearance: checkbox;
    appearance: checkbox;
    width: auto;
    height: auto;
    cursor: pointer;
}

.self-inspection__checkbox:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.self-inspection__checkbox-label {
    cursor: pointer;
    flex: 1;
    font-size: 14px;
    padding-top: 1px;
    color: #334155;
}

/* 全选区域 */
.self-inspection__select-all {
    margin-bottom: 16px;
    padding: 0;
    border-bottom: 1px solid var(--si-border);
    margin-left: 0;
    margin-right: 0;
    padding-bottom: 16px;
}

.self-inspection__select-all .self-inspection__checkbox-container {
    background-color: transparent;
    border: none;
    padding-left: 0;
}

.self-inspection__select-all .self-inspection__checkbox-container:hover {
    background-color: var(--si-muted);
}

.self-inspection__select-all .self-inspection__checkbox-label {
    font-weight: 500;
    color: #0f172a;
}

/* 附件区域 */
.self-inspection__attachments {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--si-border);
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
}

.self-inspection__attachments-title {
    font-weight: 600;
    margin-bottom: 16px;
    color: #334155;
    font-size: 14px;
}

.self-inspection__attachment-item {
    display: flex;
    align-items: center;
    padding: 10px 14px;
    border: 1px solid var(--si-border);
    border-radius: var(--si-radius);
    margin-bottom: 8px;
    background-color: var(--si-background);
    transition: all 0.2s;
}

.self-inspection__attachment-item:hover {
    border-color: var(--si-muted-foreground);
    box-shadow: none;
    background-color: var(--si-muted);
}

.self-inspection__attachment-icon {
    margin-right: 14px;
    color: #64748b;
    font-size: 20px;
}

.self-inspection__attachment-info {
    flex: 1;
}

.self-inspection__attachment-name {
    font-size: 14px;
    color: #334155;
    font-weight: 500;
}

.self-inspection__attachment-size {
    font-size: 12px;
    color: #64748b;
    margin-top: 2px;
}

.self-inspection__attachment-delete {
    color: var(--si-destructive);
    cursor: pointer;
    margin-left: 10px;
    font-size: 16px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.self-inspection__attachment-delete:hover {
    background-color: rgba(239, 68, 68, 0.1);
}

.self-inspection__upload-btn {
    margin-top: 16px;
    padding: 8px 16px;
    background-color: var(--si-background);
    color: var(--si-primary);
    border: 1px solid var(--si-primary);
    border-radius: var(--si-radius);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
}

.self-inspection__upload-btn:hover {
    background-color: rgba(59, 130, 246, 0.05);
    border-color: var(--si-primary);
}

.self-inspection__upload-btn::before {
    content: "📎";
    margin-right: 8px;
    font-size: 16px;
}

/* 底部操作区域 */
.self-inspection__action-bar {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--si-border);
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
}

.self-inspection__btn {
    padding: 9px 18px;
    border-radius: var(--si-radius);
    cursor: pointer;
    border: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.self-inspection__btn--reset {
    background-color: var(--si-secondary); /* Original light greyish background */
    color: var(--si-foreground);       /* Original dark text */
    border: 1px solid var(--si-border);    /* Original light border */
}

.self-inspection__btn--reset:hover {
    background-color: var(--si-muted); /* Original hover light grey */
    border-color: var(--si-muted-foreground); /* Original hover darker grey border */
    color: var(--si-foreground); /* Original hover text color */
}

.self-inspection__btn--submit {
    background-color: var(--si-primary);
    color: var(--si-primary-foreground);
    border: 1px solid var(--si-primary);
}

.self-inspection__btn--submit:hover {
    background-color: #2563eb;
    border-color: #2563eb;
    box-shadow: 0 1px 3px rgba(37, 99, 235, 0.2);
}

.self-inspection__btn--disabled {
    background-color: #f1f5f9;
    color: #94a3b8;
    cursor: not-allowed;
    border: 1px solid #e2e8f0;
}

.self-inspection__btn--disabled:hover {
    background-color: #f1f5f9;
    box-shadow: none;
}

.self-inspection__completed-tag {
    background-color: #10b981;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 100px;
    font-size: 14px;
    font-weight: 500;
}

/* 对话框 */
.self-inspection__dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(15, 23, 42, 0.5);
    backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
}

.self-inspection__dialog-overlay--visible {
    opacity: 1;
    visibility: visible;
}

.self-inspection__dialog {
    background-color: #ffffff;
    border-radius: var(--si-radius);
    width: 420px;
    max-width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(20px);
    transition: all 0.3s;
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.self-inspection__dialog-overlay--visible .self-inspection__dialog {
    transform: translateY(0);
}

.self-inspection__dialog-header {
    background-color: #ffffff;
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
}

.self-inspection__dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: #0f172a;
}

.self-inspection__dialog-body {
    background-color: #ffffff;
    padding: 24px;
}

.self-inspection__dialog-message {
    font-size: 15px;
    margin-bottom: 16px;
    color: #475569;
    line-height: 1.5;
}

.self-inspection__dialog-footer {
    background-color: #f8fafc;
    padding: 16px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #e2e8f0;
}

/* 文件上传对话框 */
.self-inspection__file-list {
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.self-inspection__file-upload-input {
    display: none;
}

/* 通知提示 */
.self-inspection__notification {
    position: fixed;
    bottom: 24px;
    right: 24px;
    padding: 16px 20px;
    background-color: #ffffff;
    border-radius: var(--si-radius);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: flex-start;
    transform: translateX(120%);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s;
    z-index: 9998;
    border: 1px solid #e2e8f0;
    max-width: 380px;
    opacity: 0;
}

.self-inspection__notification--show {
    transform: translateX(0);
    opacity: 1;
}

.self-inspection__notification-icon {
    margin-right: 14px;
    font-size: 22px;
}

.self-inspection__notification-icon--warning {
    color: #f59e0b;
}

.self-inspection__notification-content {
    flex: 1;
}

.self-inspection__notification-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: #0f172a;
}

.self-inspection__notification-message {
    font-size: 14px;
    color: #64748b;
    line-height: 1.5;
}

/* 完成消息 */
.self-inspection__completion-message {
    background: linear-gradient(to right bottom, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.1));
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: var(--si-radius);
    padding: 40px 30px;
    text-align: center;
    margin-top: 36px;
    display: none;
}

.self-inspection__completion-icon {
    font-size: 48px;
    color: #10b981;
    margin-bottom: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 90px;
    background-color: rgba(16, 185, 129, 0.1);
    border-radius: 50%;
    box-shadow: 0 0 0 8px rgba(16, 185, 129, 0.05);
}

.self-inspection__completion-title {
    font-size: 26px;
    font-weight: 700;
    margin-bottom: 16px;
    color: #0f172a;
}

.self-inspection__completion-info {
    font-size: 16px;
    color: #475569;
    margin-bottom: 24px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.self-inspection__completion-details {
    display: inline-block;
    text-align: left;
    margin: 0 auto;
    background-color: #ffffff;
    padding: 20px 28px;
    border-radius: calc(var(--si-radius) - 2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.self-inspection__completion-details p {
    margin-bottom: 10px;
    font-size: 15px;
    color: #475569;
}

.self-inspection__completion-details p span {
    font-weight: 600;
    color: #0f172a;
    margin-left: 4px;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .self-inspection {
        padding: 16px;
        max-width: 100%;
        margin: 1rem auto;
    }
    .self-inspection__order-info {
        flex-direction: column;
    }

    .self-inspection__input-group {
        width: 100%;
    }

    .self-inspection__header {
        padding: 16px;
    }

    .self-inspection__stage {
        padding-left: 40px;
    }

    .self-inspection__stage-node {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .self-inspection__dialog {
        width: 95%;
    }
}

/* SweetAlert2 自定义样式 */
.self-inspection-swal-container {
    z-index: 10000;
}

.self-inspection-swal-popup {
    border-radius: calc(var(--si-radius) + 2px);
    padding: 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.self-inspection-swal-title {
    color: #0f172a;
    font-weight: 600;
    font-size: 20px;
    padding: 20px 24px 0;
}

.self-inspection-swal-html-container {
    color: #475569;
    font-size: 15px;
    padding: 16px 24px;
}

.swal2-html-container-custom {
    color: #475569;
    font-size: 15px;
    margin-bottom: 16px;
    line-height: 1.5;
}

.self-inspection-swal-actions {
    padding: 16px 24px;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.self-inspection-swal-confirm-button {
    background-color: #3b82f6 !important;
    color: #ffffff !important;
    border-radius: var(--si-radius) !important;
    font-weight: 500 !important;
    padding: 9px 18px !important;
    box-shadow: none !important;
}

.self-inspection-swal-confirm-button:hover {
    background-color: #2563eb !important;
    box-shadow: 0 1px 3px rgba(37, 99, 235, 0.2) !important;
}

.self-inspection-swal-cancel-button {
    background-color: #f1f5f9 !important;
    color: #475569 !important;
    border-radius: var(--si-radius) !important;
    font-weight: 500 !important;
    padding: 9px 18px !important;
    box-shadow: none !important;
    border: 1px solid #e2e8f0 !important;
}

.self-inspection-swal-cancel-button:hover {
    background-color: #e2e8f0 !important;
    color: #334155 !important;
}

.self-inspection-swal-icon {
    border: none !important;
    margin-top: 30px !important;
}

/* 自定义文件上传样式 */
#sweetalert-file-list {
    max-height: 250px;
    overflow-y: auto;
    margin-bottom: 16px;
    border: 1px solid #e2e8f0;
    border-radius: calc(var(--si-radius) - 2px);
    padding: 8px;
    background-color: #f8fafc;
}

#sweetalert-file-list p {
    text-align: center;
    padding: 16px;
    color: #64748b;
}

#sweetalert-file-upload {
    display: none;
}

/* 产品状态显示 */
.self-inspection__product-status {
    margin: 16px;
    padding: 16px;
    border: 1px solid var(--si-border);
    border-radius: var(--si-radius);
    background-color: var(--si-secondary);
}

.self-inspection__product-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.self-inspection__product-status-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--si-foreground);
    margin: 0;
}

.self-inspection__product-status-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 8px 12px;
    font-size: 14px;
    color: var(--si-muted-foreground);
}

/* 工单数量样式 */
.self-inspection__workorder-count {
    display: flex;
    align-items: center;
    margin: 0 0 12px 0;
    padding: 6px 10px;
    background-color: rgba(59, 130, 246, 0.05);
    border-left: 3px solid #3b82f6;
    color: #3b82f6;
    font-weight: 500;
    border-radius: 4px;
}

.self-inspection__workorder-count i {
    margin-right: 8px;
    font-size: 14px;
}

.self-inspection__product-status-list {
    max-height: 90px; /* 减小最大高度 */
    overflow-y: auto;
    border: 1px solid var(--si-border);
    border-radius: calc(var(--si-radius) - 2px);
    background-color: var(--si-background);
}

.self-inspection__product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--si-border);
}

.self-inspection__product-item:last-child {
    border-bottom: none;
}

.self-inspection__product-item--completed {
    background-color: rgba(16, 185, 129, 0.05);
}

.self-inspection__product-sn {
    font-weight: 500;
    color: var(--si-foreground);
}

.self-inspection__product-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.self-inspection__status-tag {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
}

.self-inspection__status-tag--completed {
    background-color: rgba(16, 185, 129, 0.1);
    color: rgb(16, 185, 129);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.self-inspection__status-tag--pending {
    background-color: rgba(107, 114, 128, 0.1);
    color: rgb(107, 114, 128);
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.self-inspection__inspector {
    margin-left: 8px;
    font-size: 12px;
    color: #666;
    display: inline-block;
}

.self-inspection__final-submitted-notice {
    margin-top: 10px;
    padding: 6px 10px;
    background-color: rgba(245, 158, 11, 0.1);
    border-left: 3px solid var(--si-warning);
    color: var(--si-warning);
    font-size: 13px;
    border-radius: calc(var(--si-radius) - 2px);
    display: flex;
    align-items: center;
}

.self-inspection__final-submitted-notice::before {
    content: "!";
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    background-color: var(--si-warning);
    color: white;
    border-radius: 50%;
    margin-right: 8px;
    font-weight: bold;
    font-size: 12px;
}

.self-inspection__partial-submitted-notice {
    margin-top: 10px;
    padding: 6px 10px;
    background-color: rgba(3, 169, 244, 0.05);
    border-left: 3px solid #03A9F4;
    color: #0277BD;
    font-size: 13px;
    border-radius: calc(var(--si-radius) - 2px);
    display: flex;
    align-items: center;
}

.self-inspection__partial-submitted-notice::before {
    content: "i";
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    background-color: #03A9F4;
    color: white;
    border-radius: 50%;
    margin-right: 8px;
    font-weight: bold;
    font-size: 12px;
    font-style: italic;
}

/* 返工标识样式 */
.rework-badge {
    display: inline-block;
    margin-left: 8px;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    background-color: #ff6b6b;
    border-radius: 3px;
}

/* 扫描产品项样式 */
.scanned-product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}