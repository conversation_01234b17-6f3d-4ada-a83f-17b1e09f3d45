from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
import logging
from urllib.parse import quote_plus
import pymysql
import time
from typing import Optional

logger = logging.getLogger(__name__)

Base = declarative_base()

class DatabaseManager:
    _instance = None
    MAX_RETRIES = 3
    RETRY_DELAY = 2  # 重试延迟秒数
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.db_params = {
            'host': '*************',  # 确认这个IP是否正确
            'port': 3309,
            'user': 'root',
            'password': 'kmlc@3302133',
            'database': 'kmlc_plc',
            'charset': 'utf8mb4',
            'connect_timeout': 10  # 增加连接超时时间
        }
        
        self._initialize_connection()
        self._initialized = True
    
    def _initialize_connection(self):
        """初始化数据库连接"""
        retries = 0
        last_exception = None
        
        while retries < self.MAX_RETRIES:
            try:
                # 测试PyMySQL连接
                conn = self._try_connect()
                if conn:
                    conn.close()
                    logger.info("PyMySQL direct connection successful!")
                    break
            except Exception as e:
                last_exception = e
                retries += 1
                logger.warning(f"Connection attempt {retries} failed: {str(e)}")
                if retries < self.MAX_RETRIES:
                    time.sleep(self.RETRY_DELAY)
                continue
        
        if retries == self.MAX_RETRIES:
            logger.error(f"Failed to connect after {self.MAX_RETRIES} attempts")
            raise last_exception
        
        # 构建 SQLAlchemy URL
        password = quote_plus(self.db_params['password'])
        self.DATABASE_URL = f"mysql+pymysql://{self.db_params['user']}:{password}@{self.db_params['host']}:{self.db_params['port']}/{self.db_params['database']}"
        
        self.engine = create_engine(
            self.DATABASE_URL,
            poolclass=QueuePool,
            pool_size=30,
            max_overflow=30,
            pool_timeout=60,
            pool_recycle=1800,
            pool_pre_ping=True,
            echo=False
        )
        
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
    
    def _try_connect(self) -> Optional[pymysql.Connection]:
        """尝试建立数据库连接"""
        try:
            conn = pymysql.connect(**self.db_params)
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
            return conn
        except Exception as e:
            logger.error(f"PyMySQL connection failed: {str(e)}")
            raise
    
    @contextmanager
    def get_session(self):
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            logger.error(f"Database error: {str(e)}", exc_info=True)
            session.rollback()
            raise
        finally:
            session.close() 