class Particle {
    constructor(canvas, x, y) {
        this.canvas = canvas;
        this.x = x || Math.random() * canvas.width;
        this.y = y || Math.random() * canvas.height;
        this.speed = {
            x: Math.random() * 2 - 1,
            y: Math.random() * 2 - 1
        };
        this.radius = Math.random() * 2 + 1;
        this.originalRadius = this.radius;
        this.mouseRepelRadius = 100;
    }

    draw(ctx) {
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.fillStyle = '#00a8ff';
        ctx.fill();
        ctx.closePath();
    }

    update(mouse) {
        // 基础移动
        this.x += this.speed.x;
        this.y += this.speed.y;

        // 边界检查
        if (this.x < 0 || this.x > this.canvas.width) this.speed.x *= -1;
        if (this.y < 0 || this.y > this.canvas.height) this.speed.y *= -1;

        // 鼠标交互
        if (mouse.x !== undefined && mouse.y !== undefined) {
            const dx = mouse.x - this.x;
            const dy = mouse.y - this.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < this.mouseRepelRadius) {
                const angle = Math.atan2(dy, dx);
                const force = (this.mouseRepelRadius - distance) / this.mouseRepelRadius;
                
                this.x -= Math.cos(angle) * force * 5;
                this.y -= Math.sin(angle) * force * 5;
                
                // 粒子靠近鼠标时变大
                this.radius = this.originalRadius * (1 + force);
            } else {
                // 恢复原始大小
                this.radius = this.originalRadius;
            }
        }
    }
}

class ParticleSystem {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.particles = [];
        this.mouse = { x: undefined, y: undefined };
        this.particleCount = 100;
        this.connectionDistance = 150;
        
        this.init();
    }

    init() {
        // 设置画布
        this.canvas.style.position = 'fixed';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';
        this.canvas.style.width = '100%';
        this.canvas.style.height = '100%';
        this.canvas.style.zIndex = '0';
        this.canvas.style.pointerEvents = 'none';
        document.querySelector('.background').appendChild(this.canvas);

        // 设置画布尺寸
        this.resize();
        window.addEventListener('resize', () => this.resize());

        // 鼠标事件
        document.addEventListener('mousemove', (e) => {
            this.mouse.x = e.clientX;
            this.mouse.y = e.clientY;
        });

        document.addEventListener('mouseleave', () => {
            this.mouse.x = undefined;
            this.mouse.y = undefined;
        });

        // 创建粒子
        for (let i = 0; i < this.particleCount; i++) {
            this.particles.push(new Particle(this.canvas));
        }

        // 开始动画
        this.animate();
    }

    resize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    drawConnections() {
        for (let i = 0; i < this.particles.length; i++) {
            for (let j = i + 1; j < this.particles.length; j++) {
                const dx = this.particles[i].x - this.particles[j].x;
                const dy = this.particles[i].y - this.particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < this.connectionDistance) {
                    const opacity = 1 - (distance / this.connectionDistance);
                    this.ctx.beginPath();
                    this.ctx.strokeStyle = `rgba(0, 168, 255, ${opacity * 0.2})`;
                    this.ctx.lineWidth = 1;
                    this.ctx.moveTo(this.particles[i].x, this.particles[i].y);
                    this.ctx.lineTo(this.particles[j].x, this.particles[j].y);
                    this.ctx.stroke();
                    this.ctx.closePath();
                }
            }
        }
    }

    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 更新和绘制所有粒子
        this.particles.forEach(particle => {
            particle.update(this.mouse);
            particle.draw(this.ctx);
        });

        // 绘制连接线
        this.drawConnections();

        requestAnimationFrame(() => this.animate());
    }
}

// 启动粒子系统
window.addEventListener('DOMContentLoaded', () => {
    new ParticleSystem();
}); 