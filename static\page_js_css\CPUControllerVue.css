/* CPU控制器模块Vue版本样式 - 现代化主题系统 */

/* CPU控制器专用CSS变量主题系统 - 避免全局污染 */
.cpu-controller__main {
    /* === 新增：优化字体系统变量 === */
    --cpu-font-size-2xl: 1.5rem;      /* 24px - 系统主标题 */
    --cpu-font-size-xl: 1.25rem;      /* 20px - 功能标题 */
    --cpu-font-size-lg: 1.125rem;     /* 18px - 子标题 */
    --cpu-font-size-base: 1rem;       /* 16px - 中间层级 */
    --cpu-font-size-sm: 0.875rem;     /* 14px - 正文内容 */
    --cpu-font-size-xs: 0.8125rem;    /* 13px - 辅助信息和操作按钮 */
    --cpu-font-size-2xs: 0.75rem;     /* 12px - 最小文字 */
    
    /* === 新增：增强科技感配色变量 === */
    --cpu-accent-cyan: #00f5ff;       /* 电子青 */
    --cpu-accent-green: #00ff88;      /* 电子绿 */
    --cpu-accent-purple: #8b5cf6;     /* 科技紫 */
    --cpu-status-pending: #a78bfa;    /* 等待状态 */
    --cpu-status-processing: #fbbf24; /* 处理状态 */
    
    /* === 新增：增强视觉层次变量 === */
    --cpu-shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
    --cpu-shadow-intense: 0 8px 32px rgba(59, 130, 246, 0.4);
    --cpu-border-glow: rgba(59, 130, 246, 0.6);
    
    /* 浅色主题变量 */
    --cpu-bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);
    --cpu-bg-overlay: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(139, 92, 246, 0.05) 50%, rgba(236, 72, 153, 0.05) 100%);
    --cpu-card-bg: rgba(255, 255, 255, 0.8);
    --cpu-card-border: rgba(37, 99, 235, 0.35);
    --cpu-card-hover-border: rgba(37, 99, 235, 0.6);
    --cpu-card-hover-shadow: rgba(37, 99, 235, 0.3);
    --cpu-text-primary: #1f2937;
    --cpu-text-secondary: #6b7280;
    --cpu-text-tertiary: #9ca3af;
    --cpu-toolbar-bg: rgba(255, 255, 255, 0.9);
    --cpu-toolbar-border: rgba(37, 99, 235, 0.2);
    --cpu-separator-color: rgba(37, 99, 235, 0.2);
    --cpu-accent-blue: #2563eb;
    --cpu-accent-blue-light: rgba(37, 99, 235, 0.1);
}

[data-theme="dark"] .cpu-controller__main {
    /* === 深色主题下的增强配色 === */
    --cpu-accent-cyan: #00d4ff;       /* 深色主题电子青 */
    --cpu-accent-green: #00f5a0;      /* 深色主题电子绿 */
    --cpu-shadow-glow: 0 0 20px rgba(0, 212, 255, 0.4);
    --cpu-shadow-intense: 0 8px 32px rgba(0, 212, 255, 0.5);
    --cpu-border-glow: rgba(0, 212, 255, 0.7);
    
    /* 深色主题变量 */
    --cpu-bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
    --cpu-bg-overlay: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 50%, rgba(236, 72, 153, 0.1) 100%);
    --cpu-card-bg: rgba(30, 41, 59, 0.8);
    --cpu-card-border: rgba(59, 130, 246, 0.4);
    --cpu-card-hover-border: rgba(59, 130, 246, 0.4);
    --cpu-card-hover-shadow: rgba(59, 130, 246, 0.3);
    --cpu-text-primary: #ffffff;
    --cpu-text-secondary: #d1d5db;
    --cpu-text-tertiary: #9ca3af;
    --cpu-toolbar-bg: rgba(30, 41, 59, 0.9);
    --cpu-toolbar-border: rgba(59, 130, 246, 0.3);
    --cpu-separator-color: rgba(59, 130, 246, 0.2);
    --cpu-accent-blue: #3b82f6;
    --cpu-accent-blue-light: rgba(59, 130, 246, 0.1);
}

/* ===== BEM架构重构：内联样式提取 ===== */

/* 1. 工具栏背景样式 - 替代内联样式 */
.cpu-controller__toolbar {
    background: var(--cpu-toolbar-bg);
    border-color: var(--cpu-toolbar-border);
}

.cpu-controller__toolbar--dark {
    background: rgba(30, 41, 59, 0.9);
    border-color: var(--cpu-toolbar-border);
}

.cpu-controller__toolbar--light {
    background: rgba(255, 255, 255, 0.9);
    border-color: var(--cpu-toolbar-border);
}

/* 2. 测试区域边框样式 - 替代内联样式 */
.cpu-controller__test-section {
    border-color: var(--cpu-toolbar-border);
}

/* 3. 进度条动态宽度 - 替代内联样式 */
.cpu-controller__progress-bar {
    width: var(--progress-width, 0%);
    transition: width 0.3s ease;
}

/* ===== BEM架构重构：Tailwind类提取 ===== */

/* 1. 主要布局容器类 */
.cpu-controller__main {
    min-height: 100vh;
    background: var(--cpu-bg-primary);
    position: relative;
    overflow: hidden;
}

.cpu-controller__main-content {
    display: flex;
    height: calc(100vh - 89px);
}

.cpu-controller__form-section {
    width: 50%;
    padding-left: 1.5rem;
    padding-right: 0.75rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    overflow-y: auto;
}

.cpu-controller__test-section {
    width: 50%;
    border-left: 1px solid var(--cpu-toolbar-border);
    padding-left: 0.75rem;
    padding-right: 1.5rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    overflow-y: auto;
}

/* 2. 工具栏布局类 */
.cpu-controller__toolbar-container {
    max-width: none;
    margin-left: 1.5rem;
    margin-right: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cpu-controller__toolbar-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.cpu-controller__toolbar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.cpu-controller__toolbar-status {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.cpu-controller__toolbar-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* 3. 卡片布局类 */
.cpu-controller__card {
    background: var(--cpu-card-bg);
    border: 1px solid var(--cpu-card-border);
    backdrop-filter: blur(16px);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.cpu-controller__card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--cpu-card-hover-shadow);
    border-color: var(--cpu-card-hover-border);
}

.cpu-controller__card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.cpu-controller__card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cpu-controller__card-content {
    padding: 1.5rem;
}

/* ===== 新增：优化字体类系统 ===== */

/* 主标题优化 - 系统标题使用 */
.cpu-controller__title-main {
    font-size: var(--cpu-font-size-2xl);
    font-weight: 700;
    line-height: 1.2;
}

/* 功能标题优化 - 卡片标题使用 */
.cpu-controller__title-section {
    font-size: var(--cpu-font-size-xl);
    font-weight: 600;
    line-height: 1.3;
}

/* 子标题优化 - 副标题使用 */
.cpu-controller__title-sub {
    font-size: var(--cpu-font-size-lg);
    font-weight: 500;
    line-height: 1.4;
}

/* 操作按钮文字优化 */
.cpu-controller__text-button {
    font-size: var(--cpu-font-size-xs);
    font-weight: 500;
    line-height: 1.2;
}

/* 日志文字优化 */
.cpu-controller__text-log {
    font-size: var(--cpu-font-size-xs);
    font-family: 'SF Mono', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
    line-height: 1.4;
}

/* 状态指示文字优化 */
.cpu-controller__text-status {
    font-size: var(--cpu-font-size-xs);
    font-weight: 500;
    line-height: 1.3;
}

/* 4. 图标容器类 */
.cpu-controller__icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cpu-controller__icon--blue {
    background: linear-gradient(135deg, #3b82f6, #4f46e5);
    box-shadow: var(--cpu-shadow-glow);
}

.cpu-controller__icon--green {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.cpu-controller__icon--purple {
    background: linear-gradient(135deg, #8b5cf6, #ec4899);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.cpu-controller__icon--indigo {
    background: linear-gradient(135deg, #6366f1, #3b82f6);
    box-shadow: var(--cpu-shadow-glow);
}

/* 新增：橙色图标背景，用于"产品类型配置"图标 */
.cpu-controller__icon--orange {
    background: linear-gradient(135deg, #f97316, #f43f5e);
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.3);
}

/* 5. Flex布局通用类 */
.cpu-controller__flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.cpu-controller__flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cpu-controller__flex-start {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* 6. 间距类 */
.cpu-controller__space-y {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cpu-controller__space-x {
    display: flex;
    gap: 0.75rem;
}

/* 7. 表单布局类 */
.cpu-controller__form-content {
    padding: 1.5rem;
    padding-bottom: 1.5rem;
}

.cpu-controller__form-collapsed {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cpu-controller__form-expanded {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 8. 网格系统 */
.cpu-controller__grid-2 {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
}

.cpu-controller__grid-3 {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
}

.cpu-controller__grid-4 {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 1rem;
}

.cpu-controller__field-group {
    display: flex;
    flex-direction: column;
}

.cpu-controller__field-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--cpu-text-secondary);
    margin-bottom: 0.25rem;
}

/* 9. 设备信息卡片 */
.cpu-controller__device-card {
    background: var(--cpu-card-bg);
    border: 1px solid var(--cpu-card-border);
    backdrop-filter: blur(16px);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.cpu-controller__device-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--cpu-card-hover-shadow);
    border-color: var(--cpu-card-hover-border);
}

.cpu-controller__device-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.cpu-controller__device-actions {
    display: flex;
    gap: 0.5rem;
}

.cpu-controller__device-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 10. 测试进度卡片 */
.cpu-controller__progress-card {
    background: var(--cpu-card-bg);
    border: 1px solid var(--cpu-card-border);
    backdrop-filter: blur(16px);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.cpu-controller__progress-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--cpu-card-hover-shadow);
    border-color: var(--cpu-card-hover-border);
}

.cpu-controller__progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.cpu-controller__progress-tabs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cpu-controller__progress-stats {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
    text-align: center;
}

.cpu-controller__stat-card {
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid;
}

.cpu-controller__stat-card--success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
    border-color: rgba(34, 197, 94, 0.2);
}

.cpu-controller__stat-card--danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));
    border-color: rgba(239, 68, 68, 0.2);
}

.cpu-controller__stat-card--neutral {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.1), rgba(148, 163, 184, 0.1));
    border-color: rgba(156, 163, 175, 0.2);
}

.cpu-controller__stat-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.cpu-controller__stat-label {
    font-size: 0.75rem;
    font-weight: 500;
}

/* 11. 测试日志视图 */
.cpu-controller__log-layout {
    display: flex;
    gap: 1rem;
    height: 16rem;
}

.cpu-controller__log-stats {
    width: 20%;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.cpu-controller__compact-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 0.5rem;
    font-size: var(--cpu-font-size-sm);
    font-weight: 500;
    border: 1px solid;
    transition: all 0.3s ease;
}

.cpu-controller__compact-stat:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cpu-controller__compact-stat--success {
    background-color: rgba(16, 185, 129, 0.05);
    border-color: rgba(16, 185, 129, 0.2);
    color: #047857;
}

.cpu-controller__compact-stat--danger {
    background-color: rgba(239, 68, 68, 0.05);
    border-color: rgba(239, 68, 68, 0.2);
    color: #991b1b;
}

.cpu-controller__compact-stat--neutral {
    background-color: rgba(107, 114, 128, 0.05);
    border-color: rgba(107, 114, 128, 0.2);
    color: #374151;
}

[data-theme="dark"] .cpu-controller__compact-stat--success {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: #6ee7b7;
}

[data-theme="dark"] .cpu-controller__compact-stat--danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #fca5a5;
}

[data-theme="dark"] .cpu-controller__compact-stat--neutral {
    background-color: rgba(107, 114, 128, 0.1);
    border-color: rgba(107, 114, 128, 0.3);
    color: #d1d5db;
}

.cpu-controller__log-content {
    width: 80%;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.cpu-controller__log-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
}

.cpu-controller__log-indicators {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.cpu-controller__log-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.cpu-controller__log-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
}

.cpu-controller__log-dot--success {
    background-color: #10b981;
}

.cpu-controller__log-dot--error {
    background-color: #ef4444;
}

.cpu-controller__log-dot--warning {
    background-color: #f59e0b;
}

.cpu-controller__log-dot--info {
    background-color: #3b82f6;
}

.cpu-controller__log-dot--system {
    background-color: #8b5cf6;
}

/* 12. 测试项目卡片 */
.cpu-controller__test-card {
    background: var(--cpu-card-bg);
    border: 1px solid var(--cpu-card-border);
    backdrop-filter: blur(16px);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.cpu-controller__test-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--cpu-card-hover-shadow);
    border-color: var(--cpu-card-hover-border);
}

.cpu-controller__test-actions {
    display: flex;
    gap: 0.5rem;
}

.cpu-controller__test-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.cpu-controller__test-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid var(--cpu-card-border);
    transition: all 0.3s ease;
}

.cpu-controller__test-item:hover {
    border-color: var(--cpu-card-hover-border);
}

.cpu-controller__test-item--active {
    background: var(--cpu-accent-blue-light);
    border-color: var(--cpu-card-hover-border);
    box-shadow: 0 8px 25px -8px var(--cpu-card-hover-shadow);
}

.cpu-controller__test-item-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.cpu-controller__test-item-icon {
    padding: 0.5rem;
    border-radius: 0.5rem;
}

.cpu-controller__test-item-details {
    display: flex;
    flex-direction: column;
}

.cpu-controller__test-item-name {
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--cpu-text-primary);
}

.cpu-controller__test-item-meta {
    font-size: 0.75rem;
    color: var(--cpu-text-secondary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cpu-controller__test-item-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.cpu-controller__test-item-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cpu-controller__test-item-buttons {
    display: flex;
    gap: 0.5rem;
}

/* ===== 原有样式保持不变 ===== */

/* CPU控制器专用过渡动画 - 性能优化，避免通配符 */
.cpu-controller__card,
.cpu-controller__toolbar,
.cpu-controller__test-item,
.cpu-controller__stat-card,
.cpu-controller__icon {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Element Plus 组件过渡动画限定 */
.cpu-controller__main .el-button,
.cpu-controller__main .el-input__wrapper,
.cpu-controller__main .el-tag {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease !important;
}

/* 自定义样式 */
.glass-effect {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.gradient-bg {
    background: var(--cpu-bg-primary);
    position: relative;
    overflow: hidden;
    height: 100vh;
}

.gradient-bg::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--cpu-bg-overlay);
    pointer-events: none;
}

/* 卡片悬停动画 - 增强优先级，确保与CouplerVue一致的舒适切换效果 */
.card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.card-hover::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent) !important;
    transition: left 0.5s ease !important;
    pointer-events: none !important;
}

.card-hover:hover {
    transform: translateY(-4px) scale(1.02) !important;
    box-shadow: 0 25px 50px -12px var(--cpu-card-hover-shadow) !important;
    border-color: var(--cpu-card-hover-border) !important;
}

.card-hover:hover::before {
    left: 100% !important;
}

.animate-pulse-dot {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 状态指示器 */
.status-indicator {
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.status-connected::after {
    background: #10b981;
    opacity: 0.75;
}

.status-disconnected::after {
    background: #ef4444;
    opacity: 0.75;
}

/* 卡片折叠动画 - 增强优先级 */
.collapse-enter-active,
.collapse-leave-active {
    transition: all 0.5s ease !important;
    overflow: hidden !important;
}

.collapse-enter-from,
.collapse-leave-to {
    max-height: 0 !important;
    opacity: 0 !important;
}

.collapse-enter-to,
.collapse-leave-from {
    max-height: 1000px !important;
    opacity: 1 !important;
}

/* 主题卡片样式 */
.theme-card {
    background: var(--cpu-card-bg) !important;
    border-color: var(--cpu-card-border) !important;
    color: var(--cpu-text-primary);
}

.theme-label {
    color: var(--cpu-text-secondary) !important;
}

.theme-separator {
    border-color: var(--cpu-separator-color) !important;
}

.theme-text-primary {
    color: var(--cpu-text-primary) !important;
}

.theme-text-secondary {
    color: var(--cpu-text-secondary) !important;
}

.theme-text-tertiary {
    color: var(--cpu-text-tertiary) !important;
}

/* CPU控制器专用Element Plus 输入框和选择框主题适配 - 限定作用域 */
.cpu-controller__main .el-input__wrapper,
.cpu-controller__main .el-select .el-input__wrapper {
    background-color: var(--cpu-card-bg) !important;
    border-color: var(--cpu-card-border) !important;
    box-shadow: 0 0 0 1px var(--cpu-card-border) inset !important;
}

.cpu-controller__main .el-input__inner {
    color: var(--cpu-text-primary) !important;
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

.cpu-controller__main .el-input__inner::placeholder {
    color: var(--cpu-text-tertiary) !important;
}

.cpu-controller__main .el-textarea__inner {
    background-color: var(--cpu-card-bg) !important;
    color: var(--cpu-text-primary) !important;
    outline: none !important;
    border: none !important;
    box-shadow: 0 0 0 1px var(--cpu-card-border) inset !important;
    border-radius: 4px !important;
}

/* CPU控制器浅色主题特殊适配 */
.cpu-controller__main .el-input__wrapper,
.cpu-controller__main .el-select .el-input__wrapper,
.cpu-controller__main .el-textarea__inner {
    background-color: rgba(248, 250, 252, 0.8) !important;
}

/* CPU控制器深色主题特殊适配 - 使用更高优先级 */
[data-theme="dark"] .cpu-controller__main .el-input__wrapper,
[data-theme="dark"] .cpu-controller__main .el-select .el-input__wrapper,
[data-theme="dark"] .cpu-controller__main .el-textarea__inner {
    background-color: rgba(51, 65, 85, 0.8) !important;
    border-color: rgba(71, 85, 105, 0.6) !important;
    box-shadow: 0 0 0 1px rgba(71, 85, 105, 0.6) inset !important;
}

/* CPU控制器深色主题下只读输入框专门优化 */

/* 只读输入框外层容器样式 */
[data-theme="dark"] .cpu-controller__main .el-input[readonly] .el-input__wrapper,
[data-theme="dark"] .cpu-controller__main .el-input.is-disabled .el-input__wrapper,
[data-theme="dark"] .cpu-controller__main .bg-gray-50 .el-input__wrapper {
    background-color: rgba(15, 23, 42, 0.95) !important;
    border-color: rgba(30, 41, 59, 0.8) !important;
    color: rgba(148, 163, 184, 0.9) !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

/* 只读输入框内部文本样式 */
[data-theme="dark"] .cpu-controller__main .el-input[readonly] .el-input__inner,
[data-theme="dark"] .cpu-controller__main .el-input.is-disabled .el-input__inner,
[data-theme="dark"] .cpu-controller__main .bg-gray-50 .el-input__inner {
    background-color: transparent !important;
    color: rgba(148, 163, 184, 0.9) !important;
    cursor: default !important;
}

/* 只读输入框占位符文本 */
[data-theme="dark"] .cpu-controller__main .el-input[readonly] .el-input__inner::placeholder,
[data-theme="dark"] .cpu-controller__main .el-input.is-disabled .el-input__inner::placeholder,
[data-theme="dark"] .cpu-controller__main .bg-gray-50 .el-input__inner::placeholder {
    color: rgba(148, 163, 184, 0.6) !important;
}

/* 只读文本区域优化 */
[data-theme="dark"] .cpu-controller__main .el-textarea[readonly] .el-textarea__inner,
[data-theme="dark"] .cpu-controller__main .el-textarea.is-disabled .el-textarea__inner,
[data-theme="dark"] .cpu-controller__main .bg-gray-50 .el-textarea__inner {
    background-color: rgba(15, 23, 42, 0.95) !important;
    border-color: rgba(30, 41, 59, 0.8) !important;
    color: rgba(148, 163, 184, 0.9) !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2) !important;
    cursor: default !important;
}

/* 只读输入框悬停状态 */
[data-theme="dark"] .cpu-controller__main .el-input[readonly]:hover .el-input__wrapper,
[data-theme="dark"] .cpu-controller__main .el-input.is-disabled:hover .el-input__wrapper,
[data-theme="dark"] .cpu-controller__main .bg-gray-50:hover .el-input__wrapper {
    border-color: rgba(30, 41, 59, 0.9) !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.25) !important;
}

/* 强制覆盖Tailwind的bg-gray-50类在深色主题下的显示 */
[data-theme="dark"] .cpu-controller__main .bg-gray-50 {
    background-color: rgba(15, 23, 42, 0.95) !important;
}

/* CPU控制器深色主题下拉选择框输入框强制覆盖 */
[data-theme="dark"] .cpu-controller__main .el-select .el-input.is-focus .el-input__wrapper,
[data-theme="dark"] .cpu-controller__main .el-select .el-input__wrapper:hover,
[data-theme="dark"] .cpu-controller__main .el-select .el-input__wrapper {
    background-color: rgba(51, 65, 85, 0.9) !important;
    border-color: rgba(59, 130, 246, 0.4) !important;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3) inset !important;
}

/* CPU控制器聚焦状态的输入框移除内部边框 */
.cpu-controller__main .el-input__inner:focus,
.cpu-controller__main .el-input__inner:focus-visible,
.cpu-controller__main .el-input__inner:active,
.cpu-controller__main .el-textarea__inner:focus,
.cpu-controller__main .el-textarea__inner:focus-visible,
.cpu-controller__main .el-textarea__inner:active {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* CPU控制器输入框外层容器的聚焦样式 */
.cpu-controller__main .el-input__wrapper:focus,
.cpu-controller__main .el-input__wrapper.is-focus,
.cpu-controller__main .el-textarea__inner:focus,
.cpu-controller__main .el-select .el-input__wrapper:focus,
.cpu-controller__main .el-select .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px var(--cpu-accent-blue) !important;
    border-color: var(--cpu-accent-blue) !important;
    outline: none !important;
}

/* ===== 增强按钮交互反馈 ===== */
.cpu-controller__main .el-button {
    background: var(--cpu-card-bg) !important;
    border-color: var(--cpu-card-border) !important;
    color: var(--cpu-text-primary) !important;
    position: relative !important;
    overflow: hidden !important;
    transform-style: preserve-3d !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.cpu-controller__main .el-button:hover {
    background: var(--cpu-accent-blue-light) !important;
    border-color: var(--cpu-card-hover-border) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 8px 25px -8px var(--cpu-card-hover-shadow) !important;
}

.cpu-controller__main .el-button:active {
    transform: translateY(1px) scale(0.98) !important;
    transition: all 0.1s ease !important;
}

/* 主要操作按钮特殊效果 */
.cpu-controller__main .el-button--primary {
    background: linear-gradient(135deg, var(--cpu-accent-blue), var(--cpu-accent-purple)) !important;
    border: none !important;
    color: white !important;
    box-shadow: var(--cpu-shadow-glow) !important;
}

.cpu-controller__main .el-button--primary:hover {
    background: linear-gradient(135deg, var(--cpu-accent-cyan), var(--cpu-accent-blue)) !important;
    box-shadow: var(--cpu-shadow-intense) !important;
}

.cpu-controller__main .el-button--success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3) !important;
}

.cpu-controller__main .el-button--success:hover {
    background: linear-gradient(135deg, var(--cpu-accent-green), #10b981) !important;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4) !important;
}

/* 特殊样式：全通过按钮绿色背景 */
.cpu-controller__main .el-button.set-all-pass-btn {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3) !important;
}

.cpu-controller__main .el-button.set-all-pass-btn:hover {
    background: linear-gradient(135deg, var(--cpu-accent-green), #10b981) !important;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4) !important;
}

/* 主题切换按钮特殊样式 */
.theme-toggle-btn {
    width: 40px !important;
    height: 40px !important;
    border-radius: 12px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: var(--cpu-accent-blue-light) !important;
    border: 2px solid var(--cpu-card-border) !important;
    color: var(--cpu-accent-blue) !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.theme-toggle-btn:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 8px 25px -8px var(--cpu-card-hover-shadow) !important;
    border-color: var(--cpu-card-hover-border) !important;
}

.theme-toggle-btn .lucide {
    transition: all 0.3s ease !important;
}

/* CPU控制器测试项目背景主题适配 */
.cpu-controller__main .test-item-bg {
    background: var(--cpu-card-bg) !important;
    border-color: var(--cpu-card-border) !important;
}

.cpu-controller__main .test-item-bg:hover {
    border-color: var(--cpu-card-hover-border) !important;
}

.cpu-controller__main .test-item-bg.active {
    background: var(--cpu-accent-blue-light) !important;
    border-color: var(--cpu-card-hover-border) !important;
}

/* CPU控制器浅色主题测试项目特殊适配 */
.cpu-controller__main .test-item-bg {
    background: rgba(255, 255, 255, 0.6) !important;
    border-color: rgba(37, 99, 235, 0.2) !important;
}

.cpu-controller__main .test-item-bg:hover {
    border-color: rgba(37, 99, 235, 0.4) !important;
}

.cpu-controller__main .test-item-bg.active {
    background: rgba(37, 99, 235, 0.1) !important;
    border-color: rgba(37, 99, 235, 0.4) !important;
}

/* CPU控制器深色主题测试项目特殊适配 */
[data-theme="dark"] .cpu-controller__main .test-item-bg {
    background: rgba(51, 65, 85, 0.6) !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
}

[data-theme="dark"] .cpu-controller__main .test-item-bg:hover {
    border-color: rgba(59, 130, 246, 0.5) !important;
}

[data-theme="dark"] .cpu-controller__main .test-item-bg.active {
    background: rgba(59, 130, 246, 0.2) !important;
    border-color: rgba(59, 130, 246, 0.5) !important;
}

/* CPU控制器Dialog主题适配 - 限定作用域 */
.cpu-controller__main + .el-dialog {
    background: var(--cpu-card-bg) !important;
    border: 1px solid var(--cpu-card-border) !important;
    backdrop-filter: blur(12px) !important;
}

.cpu-controller__main + .el-dialog .el-dialog__header {
    border-bottom: 1px solid var(--cpu-card-border) !important;
}

.cpu-controller__main + .el-dialog .el-dialog__title {
    color: var(--cpu-text-primary) !important;
}

.cpu-controller__main + .el-dialog .el-dialog__body {
    color: var(--cpu-text-secondary) !important;
}

/* CPU控制器浅色主题Dialog特殊适配 */
.cpu-controller__main + .el-dialog {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(37, 99, 235, 0.2) !important;
}

/* CPU控制器深色主题Dialog特殊适配 */
[data-theme="dark"] .cpu-controller__main + .el-dialog {
    background: rgba(15, 23, 42, 0.98) !important;
    border: 1px solid rgba(59, 130, 246, 0.4) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .cpu-controller__main + .el-dialog .el-dialog__header {
    border-bottom-color: rgba(59, 130, 246, 0.3) !important;
}

/* CPU控制器Select下拉框主题适配 - 限定作用域 */
.cpu-controller__main .el-select-dropdown {
    background: var(--cpu-card-bg) !important;
    border: 1px solid var(--cpu-card-border) !important;
    backdrop-filter: blur(12px) !important;
}

.cpu-controller__main .el-select-dropdown__item {
    color: var(--cpu-text-primary) !important;
}

.cpu-controller__main .el-select-dropdown__item:hover {
    background: var(--cpu-accent-blue-light) !important;
}

/* CPU控制器浅色主题下拉框特殊适配 */
.cpu-controller__main .el-select-dropdown {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(37, 99, 235, 0.2) !important;
}

.cpu-controller__main .el-select-dropdown__item:hover {
    background: rgba(37, 99, 235, 0.1) !important;
}

/* CPU控制器深色主题下拉框特殊适配 */
[data-theme="dark"] .cpu-controller__main .el-select-dropdown {
    background: rgba(15, 23, 42, 0.98) !important;
    border: 1px solid rgba(59, 130, 246, 0.4) !important;
    backdrop-filter: blur(12px) !important;
}

[data-theme="dark"] .cpu-controller__main .el-select-dropdown__item {
    color: var(--cpu-text-secondary) !important;
}

[data-theme="dark"] .cpu-controller__main .el-select-dropdown__item:hover {
    background-color: rgba(59, 130, 246, 0.1) !important;
}

[data-theme="dark"] .cpu-controller__main .el-select-dropdown__item.is-selected {
    background-color: rgba(37, 99, 235, 0.15) !important;
    color: #ffffff !important;
}

/* CPU控制器Element Plus 标签组件深色主题适配 */
[data-theme="dark"] .cpu-controller__main .el-tag {
    border-color: var(--cpu-card-border) !important;
}

/* CPU控制器Element Plus 标签布局完全重写 - 图标在左侧文字在右侧 */
.cpu-controller__main .el-tag {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex-direction: row !important;
    gap: 6px !important;
    padding: 2px 8px !important;
    white-space: nowrap !important;
    height: auto !important;
}

/* CPU控制器移除所有标签内元素的默认边距和尺寸 */
.cpu-controller__main .el-tag *,
.cpu-controller__main .el-tag > *,
.cpu-controller__main .el-tag i,
.cpu-controller__main .el-tag span,
.cpu-controller__main .el-tag svg {
    margin: 0 !important;
    padding: 0 !important;
}

/* CPU控制器图标样式 - 确保在最左侧 */
.cpu-controller__main .el-tag i[data-lucide],
.cpu-controller__main .el-tag .lucide,
.cpu-controller__main .el-tag svg,
.cpu-controller__main .el-tag .el-icon {
    width: 12px !important;
    height: 12px !important;
    flex-shrink: 0 !important;
    display: inline-block !important;
}

/* CPU控制器文字样式 - 在图标右侧 */
.cpu-controller__main .el-tag span {
    font-size: 12px !important;
    line-height: 1.2 !important;
    flex-shrink: 0 !important;
    display: inline-block !important;
}

/* CPU控制器强制覆盖Tailwind CSS的边距类 */
.cpu-controller__main .el-tag .mr-1,
.cpu-controller__main .el-tag .mr-2,
.cpu-controller__main .el-tag .mr-3,
.cpu-controller__main .el-tag .ml-1,
.cpu-controller__main .el-tag .ml-2,
.cpu-controller__main .el-tag .ml-3 {
    margin: 0 !important;
}

/* CPU控制器强制覆盖Tailwind CSS的尺寸类 */
.cpu-controller__main .el-tag .w-3,
.cpu-controller__main .el-tag .w-4,
.cpu-controller__main .el-tag .w-5,
.cpu-controller__main .el-tag .h-3,
.cpu-controller__main .el-tag .h-4,
.cpu-controller__main .el-tag .h-5 {
    width: 12px !important;
    height: 12px !important;
}

/* CPU控制器深色主题下待测状态标签样式 */
[data-theme="dark"] .cpu-controller__main .el-tag--info {
    background: rgba(51, 65, 85, 0.8) !important;
    border-color: rgba(71, 85, 105, 0.6) !important;
    color: var(--cpu-text-secondary) !important;
}

/* CPU控制器深色主题下成功状态标签样式 */
[data-theme="dark"] .cpu-controller__main .el-tag--success {
    background: rgba(5, 46, 22, 0.8) !important;
    border-color: rgba(22, 101, 52, 0.6) !important;
    color: #86efac !important;
}

/* CPU控制器深色主题下失败状态标签样式 */
[data-theme="dark"] .cpu-controller__main .el-tag--danger {
    background: rgba(69, 10, 10, 0.8) !important;
    border-color: rgba(127, 29, 29, 0.6) !important;
    color: #fca5a5 !important;
}

/* CPU控制器深色主题下警告状态标签样式 */
[data-theme="dark"] .cpu-controller__main .el-tag--warning {
    background: rgba(69, 26, 3, 0.8) !important;
    border-color: rgba(146, 64, 14, 0.6) !important;
    color: #fcd34d !important;
}

/* 新增：深色主题下待测统计卡片文字颜色优化 */
[data-theme="dark"] .cpu-controller__main .cpu-controller__stat-card--neutral .cpu-controller__stat-value,
[data-theme="dark"] .cpu-controller__main .cpu-controller__stat-card--neutral .cpu-controller__stat-label {
    color: var(--cpu-text-secondary) !important;
}

/* ===== CPU控制器专用进度条样式 - 完全样式隔离 ===== */
/* ===== CPU控制器专用进度条样式 - 完全独立，避免样式冲突 ===== */
.cpu-controller__main .custom-progress {
    height: 16px;
    border-radius: 8px;
    background: linear-gradient(90deg, 
        rgba(59, 130, 246, 0.1), 
        rgba(139, 92, 246, 0.1)
    );
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cpu-controller__main .custom-progress-bar {
    height: 100%;
    border-radius: 8px;
    background: linear-gradient(90deg, 
        var(--cpu-accent-cyan), 
        #3b82f6, 
        var(--cpu-accent-purple)
    );
    position: relative;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.cpu-controller__main .custom-progress-bar::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 2.5s ease-in-out infinite;
    border-radius: 8px;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 页面内容容器滚动条隐藏 */
.main-content-area {
    overflow: hidden;
}

/* 左右分栏滚动条样式优化 */
.form-section,
.test-section {
    /* Webkit内核浏览器滚动条隐藏 */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.form-section::-webkit-scrollbar,
.test-section::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
}

/* 确保内容可滚动但不显示滚动条 */
.form-section:hover::-webkit-scrollbar,
.test-section:hover::-webkit-scrollbar {
    display: none;
}

/* 顶部工具栏容器对齐优化 */
.toolbar-container {
    max-width: calc(100% - 48px);
    margin: 0 auto;
}

/* 测试日志样式 */
.log-container {
    background: #111827;
    color: #f3f4f6;
    font-family: 'Courier New', monospace;
}

.log-success { color: #34d399; }
.log-error { color: #f87171; }
.log-warning { color: #fbbf24; }
.log-info { color: #60a5fa; }
.log-system { color: #a78bfa; }

/* 加载状态优化 */
.el-loading-mask {
    background: rgba(99, 102, 241, 0.1);
    backdrop-filter: blur(10px);
}

.el-loading-spinner .el-loading-text {
    color: #6366f1;
    font-weight: 600;
}

/* 响应式布局 */
@media (max-width: 1400px) {
    .cpu-controller__grid-4 {
        grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
}

@media (max-width: 1200px) {
    .cpu-controller__grid-4,
    .cpu-controller__grid-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
    
    /* 工具栏移动端优化 */
    .cpu-controller__toolbar-container {
        margin-left: 1rem;
        margin-right: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .cpu-controller__toolbar-left {
        justify-content: center;
    }
    
    .cpu-controller__toolbar-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    /* 主要布局调整 */
    .cpu-controller__main-content {
        flex-direction: column !important;
        height: auto !important;
    }
    
    .cpu-controller__form-section,
    .cpu-controller__test-section {
        width: 100% !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
    
    .cpu-controller__test-section {
        border-left: none !important;
        border-top: 1px solid var(--cpu-toolbar-border) !important;
        padding-top: 1rem !important;
    }
    
    /* 网格系统移动端适配 */
    .cpu-controller__grid-4,
    .cpu-controller__grid-3,
    .cpu-controller__grid-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
        gap: 0.75rem !important;
    }
    
    /* 工具栏移动端完全重构 */
    .cpu-controller__toolbar-container {
        margin-left: 0.5rem;
        margin-right: 0.5rem;
        padding: 1rem;
    }
    
    .cpu-controller__toolbar-brand {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .cpu-controller__toolbar-brand h1 {
        font-size: 1.125rem !important;
    }
    
    .cpu-controller__toolbar-brand p {
        font-size: 0.75rem !important;
    }
    
    .cpu-controller__toolbar-status {
        flex-direction: column;
        text-align: center;
        gap: 0.25rem;
    }
    
    .cpu-controller__toolbar-actions {
        gap: 0.5rem;
    }
    
    .cpu-controller__toolbar-actions .el-button {
        font-size: 0.75rem !important;
        padding: 0.5rem 0.75rem !important;
    }
    
    /* 卡片移动端优化 */
    .cpu-controller__card-content,
    .cpu-controller__form-content,
    .cpu-controller__device-content {
        padding: 1rem !important;
    }
    
    .cpu-controller__card-title span {
        font-size: 1rem !important;
    }
    
    /* 测试日志移动端适配 */
    .cpu-controller__log-layout {
        flex-direction: column !important;
        height: auto !important;
        gap: 1rem;
    }
    
    .cpu-controller__log-stats {
        width: 100% !important;
        display: grid !important;
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        gap: 0.5rem !important;
    }
    
    .cpu-controller__log-content {
        width: 100% !important;
        height: 300px !important;
    }
    
    /* 测试项目移动端优化 */
    .cpu-controller__test-item {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 1rem !important;
        padding: 1rem !important;
    }
    
    .cpu-controller__test-item-actions {
        width: 100% !important;
        justify-content: space-between !important;
    }
    
    .cpu-controller__test-item-buttons {
        gap: 0.25rem !important;
    }
    
    .cpu-controller__test-item-buttons .el-button {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.5rem !important;
        height: auto !important;
    }
    
    /* 页面滚动优化 */
    .gradient-bg {
        overflow-y: auto !important;
        height: auto !important;
        min-height: 100vh !important;
    }
    
    .main-content-area {
        overflow: visible !important;
    }
    
    /* 表单字段移动端优化 */
    .cpu-controller__field-label {
        font-size: 0.8rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    .el-input__wrapper,
    .el-textarea__inner {
        font-size: 0.875rem !important;
    }
    
    /* 按钮移动端优化 */
    .cpu-controller__device-actions .el-button,
    .cpu-controller__test-actions .el-button {
        font-size: 0.75rem !important;
        padding: 0.5rem !important;
        min-width: auto !important;
    }
    
    /* 进度条移动端优化 */
    .cpu-controller__main .custom-progress {
        height: 8px;
    }
    
    /* 统计卡片移动端优化 */
    .cpu-controller__stat-card {
        padding: 0.75rem !important;
    }
    
    .cpu-controller__stat-value {
        font-size: 1.25rem !important;
    }
    
    .cpu-controller__stat-label {
        font-size: 0.6875rem !important;
    }
}

@media (max-width: 480px) {
    /* 超小屏幕优化 */
    .cpu-controller__toolbar-container {
        margin: 0.25rem;
        padding: 0.75rem;
    }
    
    .cpu-controller__form-section,
    .cpu-controller__test-section {
        padding: 0.75rem !important;
    }
    
    .cpu-controller__card-content,
    .cpu-controller__form-content {
        padding: 0.75rem !important;
    }
    
    .cpu-controller__test-item {
        padding: 0.75rem !important;
    }
    
    .cpu-controller__toolbar-actions {
        flex-direction: column !important;
        width: 100%;
    }
    
    .cpu-controller__toolbar-actions .el-button {
        width: 100% !important;
        justify-content: center !important;
    }
    
    /* 日志统计改为单列 */
    .cpu-controller__log-stats {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }
    
    /* 超小屏字体优化 */
    .cpu-controller__toolbar-brand h1 {
        font-size: 1rem !important;
    }
    
    .cpu-controller__card-title span {
        font-size: 0.875rem !important;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes ping {
    75%, 100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* ===== 新增：CPU控制器表单组件主题适配 ===== */
.cpu-controller__main .el-form-item__label {
    color: var(--cpu-text-secondary) !important;
}

/* 深色主题下 el-select 输入框(wrapper)背景修正 */
[data-theme="dark"] .cpu-controller__main .el-select__wrapper {
    background-color: rgba(51, 65, 85, 0.8) !important;
    border-color: rgba(71, 85, 105, 0.6) !important;
    box-shadow: 0 0 0 1px rgba(71, 85, 105, 0.6) inset !important;
}
[data-theme="dark"] .cpu-controller__main .el-select__placeholder {
    color: var(--cpu-text-tertiary) !important;
}

/* 深色主题下 el-select 下拉面板背景修正 */
[data-theme="dark"] .cpu-controller__main .el-select-dropdown,
[data-theme="dark"] .cpu-controller__main .el-popper.is-light {
    background-color: #1e293b !important;
    border: 1px solid rgba(71, 85, 105, 0.6) !important;
    color: #d1d5db !important;
}
[data-theme="dark"] .cpu-controller__main .el-select-dropdown__item {
    color: #d1d5db !important;
}
[data-theme="dark"] .cpu-controller__main .el-select-dropdown__item:hover {
    background-color: rgba(59, 130, 246, 0.1) !important;
}
[data-theme="dark"] .cpu-controller__main .el-select-dropdown__item.is-selected {
    background-color: rgba(37, 99, 235, 0.15) !important;
    color: #ffffff !important;
}

/* 深色主题下全局 el-select dropdown(Popper) 样式 */
[data-theme="dark"] .el-select-dropdown,
[data-theme="dark"] .el-popper.el-select__popper {
    background-color: rgba(15, 23, 42, 0.98) !important;
    border: 1px solid rgba(59, 130, 246, 0.4) !important;
    backdrop-filter: blur(12px) !important;
}
[data-theme="dark"] .el-select-dropdown__list {
    background-color: transparent !important;
}

/* 深色主题下 el-select 下拉项Hover高亮 */
[data-theme="dark"] .el-select-dropdown__item.hover,
[data-theme="dark"] .el-select-dropdown__item.is-hovering {
    background-color: rgba(59, 130, 246, 0.25) !important;
    color: #ffffff !important;
}

/* CPU控制器Vue页面专用样式 */

/* ===== 折叠动画样式 ===== */
.collapse-enter-active,
.collapse-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
}

.collapse-enter-to,
.collapse-leave-from {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
}

/* ===== 产品类型配置卡片样式 ===== */
.product-type-config-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
}

.product-type-config-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* ===== M区映射显示样式 ===== */
.m-area-mapping-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.5rem;
}

.m-area-mapping-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background: white;
    border-radius: 0.375rem;
    border: 1px solid #f3f4f6;
    transition: all 0.2s ease-in-out;
}

.m-area-mapping-item:hover {
    border-color: #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
}

.m-value-badge {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    font-weight: 600;
}

/* ===== 测试项目锁定状态样式 ===== */
.test-item-locked {
    position: relative;
}

.test-item-locked::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 193, 7, 0.1) 30%, rgba(255, 193, 7, 0.1) 70%, transparent 70%);
    pointer-events: none;
    border-radius: 0.375rem;
}

.lock-badge {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fbbf24;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* ===== 产品类型选择器优化样式 ===== */
.product-type-selector {
    min-width: 200px;
}

.product-type-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.5rem;
}

.product-type-count {
    font-size: 0.75rem;
    color: #6b7280;
    background: #f3f4f6;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
}

/* ===== 配置详情面板样式 ===== */
.config-details-panel {
    background: #f9fafb;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin: 0.75rem 0;
    border: 1px solid #f3f4f6;
}

.config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.config-badge {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.125rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* ===== 操作提示样式 ===== */
.operation-hint {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
    line-height: 1.4;
}

.operation-hint .info-icon {
    color: #3b82f6;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .m-area-mapping-grid {
        grid-template-columns: 1fr;
    }
    
    .product-type-option {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .config-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* ===== 深色主题支持 ===== */
[data-theme="dark"] .product-type-config-card {
    border-color: #374151;
    background: #1f2937;
}

[data-theme="dark"] .config-details-panel {
    background: #111827;
    border-color: #374151;
}

[data-theme="dark"] .m-area-mapping-item {
    background: #1f2937;
    border-color: #374151;
}

[data-theme="dark"] .m-value-badge {
    background: #1e3a8a;
    color: #93c5fd;
}

[data-theme="dark"] .config-badge {
    background: #1e3a8a;
    color: #93c5fd;
}

[data-theme="dark"] .lock-badge {
    background: #451a03;
    color: #fbbf24;
    border-color: #92400e;
}

[data-theme="dark"] .product-type-count {
    background: #374151;
    color: #9ca3af;
}

[data-theme="dark"] .operation-hint {
    color: #9ca3af;
}

/* ===== 动画增强 ===== */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== 状态指示器 ===== */
.status-indicator {
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: inherit;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

/* ===== 工具提示样式 ===== */
.tooltip-content {
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    max-width: 300px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

/* ===== 加载状态样式 ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== 错误状态样式 ===== */
.error-message {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

[data-theme="dark"] .error-message {
    background: #451a03;
    color: #fbbf24;
    border-color: #92400e;
}

/* ===== 成功状态样式 ===== */
.success-message {
    background: #f0fdf4;
    color: #166534;
    border: 1px solid #bbf7d0;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

[data-theme="dark"] .success-message {
    background: #064e3b;
    color: #6ee7b7;
    border-color: #047857;
} 