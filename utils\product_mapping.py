from typing import Dict, Optional, List
from enum import Enum

class ProductSeries(Enum):
    """产品系列枚举"""
    ODM = "ODM产品"
    CARD_PLC = "卡片PLC产品"
    MC600 = "MC600系列"
    GL100 = "GL100系列"
    EC400 = "EC400系列"
    EC300 = "EC300系列"

class ProductMapping:
    """产品映射管理类"""
    
    # 各系列产品编码到型号的映射
    _series_mappings: Dict[ProductSeries, Dict[str, str]] = {
        ProductSeries.ODM: {
            'ODMZX040008': 'GR200-PNS-DIO32S-V1.0/中性',
            'ODMZX040007': 'GR200-PNS-DIO32D-V1.0/中性',
            'ODMZX040006': 'GR200-PNS-V1.0/中性',
            'ODMZX040005': 'GR200-ECS-DIO32S-V1.0/中性',
            'ODMZX040004': 'GR200-ECS-DIO32D-V1.0/中性',
            'ODMZX040003': 'GR200-ECS-V1.0/中性',
            'ODMZX040002': 'GL200-RTD4-V1.0/中性',
            'ODMZX040001': 'GL200-TC4-V1.0/中性',
            'ODMZX030002': 'GL200-AQ4-V1.0/中性',
            'ODMZX030001': 'GL200-AI4-V1.0/中性',
            'ODMZX020003': 'GL200-DQ16D-V1.0/中性',
            'ODMZX020002': 'GL200-DQ16S-V1.0/中性',
            'ODMZX020001': 'GL200-DI16-V1.0/中性',
            'ODMZX010053': 'MC622-CPU3A310G1616D-V2.0/中性',
            'ODMZX010052': 'MC622-CPU3A310G1616S-V2.0/中性',
            'ODMZX010051': 'MC621-CPU3S310G1616S-V2.0/中性',
            'ODMZX010050': 'MC621-CPU3S310G1616D-V2.0/中性',
            'ODMZX010049': 'MC612-CPU3A310G1616S-V2.0/中性',
            'ODMZX010048': 'MC612-CPU3A310G1616D-V2.0/中性',
            'ODMZX010047': 'MC611-CPU3S310G1616S-V2.0/中性',
            'ODMZX010046': 'MC611-CPU3S310G1616D-V2.0/中性',
            'ODMZX010045': 'MC522-CPU3A314G1616D-V2.0/中性',
            'ODMZX010044': 'MC521-CPU3S310G1616S-V2.0/中性',
            'ODMZX010043': 'MC521-CPU3S310G1616D-V2.0/中性',
            'ODMZX010042': 'MC512-CPU2A314G1616D-V2.0/中性',
            'ODMZX010041': 'MC511-CPU2A310G1616S-V2.0/中性',
            'ODMZX010040': 'MC511-CPU2A310G1616D-V2.0/中性',
            'ODMZX010039': 'MC501-CPU1A314G1616D-V2.0/中性',
            'ODMZX010038': 'EC412-CPU3A310G1616D-V2.0/中性',
            'ODMZX010037': 'EC412-CPU3A310G1616S-V2.0/中性',
            'ODMZX010036': 'EC411-CPU3S310G1616S-V2.0/中性',
            'ODMZX010035': 'EC411-CPU3S310G1616D-V2.0/中性',
            'ODMZX010034': 'EC321-CPU3S310G1616D-V2.0/中性',
            'ODMZX010033': 'EC321-CPU3S310G1616S-V2.0/中性',
            'ODMZX010032': 'EC312-CPU3A310G1616S-V2.0/中性',
            'ODMZX010031': 'EC312-CPU3A310G1616D-V2.0/中性',
            'ODMZX010030': 'EC311-CPU2A310G1616D-V2.0/中性',
            'ODMZX010029': 'EC311-CPU2A310G1616S-V2.0/中性',
            'ODMZX010028': 'EC310-CPU1A310G1616S-V2.0/中性',
            'ODMZX010027': 'EC310-CPU1A310G1616D-V2.0/中性',
            'ODMZX010026': 'MC622-CPU3A310G1616S-V1.0/中性',
            'ODMZX010025': 'MC622-CPU3A310G1616D-V1.0/中性',
            'ODMZX010024': 'MC621-CPU3S310G1616D-V1.0/中性',
            'ODMZX010023': 'MC621-CPU3S310G1616S-V1.0/中性',
            'ODMZX010022': 'MC612-CPU3A310G1616S-V1.0/中性',
            'ODMZX010021': 'MC612-CPU3A310G1616D-V1.0/中性',
            'ODMZX010020': 'MC611-CPU3S310G1616S-V1.0/中性',
            'ODMZX010019': 'MC611-CPU3S310G1616D-V1.0/中性',
            'ODMZX010018': 'MC522-CPU3A314G1616D-V1.0/中性',
            'ODMZX010017': 'MC521-CPU3S310G1616S-V1.0/中性',
            'ODMZX010016': 'MC521-CPU3S310G1616D-V1.0/中性',
            'ODMZX010015': 'MC512-CPU2A314G1616D-V1.0/中性',
            'ODMZX010014': 'MC511-CPU2A310G1616S-V1.0/中性',
            'ODMZX010013': 'MC511-CPU2A310G1616D-V1.0/中性',
            'ODMZX010012': 'MC501-CPU1A314G1616D-V1.0/中性',
            'ODMZX010011': 'EC431-CPU2A000N-V1.0/中性',
            'ODMZX010010': 'EC412-CPU3A310G1616S-V1.0/中性',
            'ODMZX010009': 'EC412-CPU3A310G1616D-V1.0/中性',
            'ODMZX010008': 'EC411-CPU3S310G1616S-V1.0/中性',
            'ODMZX010007': 'EC411-CPU3S310G1616D-V1.0/中性',
            'ODMZX010006': 'EC312-CPU3A310G1616D-V1.0/中性',
            'ODMZX010005': 'EC312-CPU3A310G1616S-V1.0/中性',
            'ODMZX010004': 'EC311-CPU2A310G1616S-V1.0/中性',
            'ODMZX010003': 'EC311-CPU2A310G1616D-V1.0/中性',
            'ODMZX010002': 'EC310-CPU1A310G1616S-V1.0/中性',
            'ODMZX010001': 'EC310-CPU1A310G1616D-V1.0/中性',
            'ODMWL020007': 'WL200-RTD4',
            'ODMWL020006': 'WL200-TC4',
            'ODMWL020005': 'WL200-AQ4',
            'ODMWL020004': 'WL200-AI4',
            'ODMWL020003': 'WL200-DQ16S',
            'ODMWL020002': 'WL200-DQ16D',
            'ODMWL020001': 'WL200-DI16',
            'ODMWL010010': 'WAC612-3A3G1616S',
            'ODMWL010009': 'WAC612-3A3G1616D',
            'ODMWL010008': 'WAC611-3S3G1616S',
            'ODMWL010007': 'WAC611-3S3G1616D',
            'ODMWL010006': 'WAC512-2A3G1616D',
            'ODMWL010005': 'WAC511-2A3G1616S',
            'ODMWL010004': 'WAC511-2A3G1616D',
            'ODMWL010003': 'WAC501-1A3G1616D',
            'ODMWL010002': 'WLC311-2A3G1616S',
            'ODMWL010001': 'WLC311-2A3G1616D',
            'ODMSF010001': 'SR310-CPU1A310G1616S',
            'ODMFAST050003': 'GR200-ECS-DIO32D-V1.0/RASCH',
            'ODMFAST050002': 'GR200-EPC-DIO32D-V1.0/RASCH',
            'ODMFAST050001': 'GR200-PNS-DIO32D-V1.0/RASCH',
            'ODMFAST040002': 'GL200-TC4-V1.0/RASCH',
            'ODMFAST040001': 'GL200-RTD4-V1.0/RASCH',
            'ODMFAST030002': 'GL200-AQ4-V1.0/RASCH',
            'ODMFAST030001': 'GL200-AI4-V1.0/RASCH',
            'ODMFAST020002': 'GL200-DQ16D-V1.0/RASCH',
            'ODMFAST020001': 'GL200-DI16-V1.0/RASCH',
            'ODMFAST010006': 'MC522-CPU3A314G1616D-V2.0/RASCH',
            'ODMFAST010005': 'MC512-CPU2A314G1616D-V2.0/RASCH',
            'ODMFAST010004': 'MC501-CPU1A314G1616D-V2.0/RASCH',
            'ODMFAST010003': 'MC522-CPU3A314G1616D-V1.0/RASCH',
            'ODMFAST010002': 'MC512-CPU2A314G1616D-V1.0/RASCH',
            'ODMFAST010001': 'MC501-CPU1A314G1616D-V1.0/RASCH'
        },
        ProductSeries.CARD_PLC: {
            '51703000007': 'GL200-RTD4-V1.0',
            '51703000006': 'GL200-TC4-V1.0',
            '51703000005': 'GL200-AQ4-V1.0',
            '51703000004': 'GL200-AI4-V1.0',
            '51703000003': 'GL200-DQ16D-V1.0',
            '51703000002': 'GL200-DQ16S-V1.0',
            '51703000001': 'GL200-DI16-V1.0',
            '51702000013': 'GR200-PNS-V2.0',
            '51702000011': 'GR200-EIP-V1.0',
            '51702000010': 'GR200-MBS-V1.0',
            '51702000009': 'GL200-CSL-V1.0',
            '51702000007': 'GR200-EPC-DIO32D-V1.0',
            '51702000006': 'GR200-PNS-V1.0',
            '51702000005': 'GR200-ECS-V1.0',
            '51702000004': 'GR200-PNS-DIO32S-V1.0',
            '51702000003': 'GR200-PNS-DIO32D-V1.0',
            '51702000002': 'GR200-ECS-DIO32S-V1.0',
            '51702000001': 'GR200-ECS-DIO32D-V1.0',
            '51701000110': 'MC622-CPU3A310G1616S-V2.0',
            '51701000109': 'MC622-CPU3A310G1616D-V2.0',
            '51701000108': 'MC621-CPU3S310G1616S-V2.0',
            '51701000107': 'MC621-CPU3S310G1616D-V2.0',
            '51701000106': 'MC612-CPU3A310G1616S-V2.0',
            '51701000105': 'MC612-CPU3A310G1616D-V2.0',
            '51701000104': 'MC611-CPU3S310G1616S-V2.0',
            '51701000103': 'MC611-CPU3S310G1616D-V2.0',
            '51701000102': 'MC522-CPU3A314G1616D-V2.0',
            '51701000101': 'MC521-CPU3S310G1616S-V2.0',
            '51701000100': 'MC521-CPU3S310G1616D-V2.0',
            '51701000099': 'MC512-CPU2A314G1616D-V2.0',
            '51701000098': 'MC511-CPU2A310G1616S-V2.0',
            '51701000097': 'MC511-CPU2A310G1616D-V2.0',
            '51701000096': 'MC501-CPU1A314G1616D-V2.0',
            '51701000095': 'EC412-CPU3A310G1616S-V2.0',
            '51701000094': 'EC412-CPU3A310G1616D-V2.0',
            '51701000093': 'EC411-CPU3S310G1616S-V2.0',
            '51701000092': 'EC411-CPU3S310G1616D-V2.0',
            '51701000091': 'EC321-CPU3S310G1616S-V2.0',
            '51701000090': 'EC321-CPU3S310G1616D-V2.0',
            '51701000089': 'EC312-CPU3A310G1616S-V2.0',
            '51701000088': 'EC312-CPU3A310G1616D-V2.0',
            '51701000086': 'EC311-CPU2A310G1616S-V2.0',
            '51701000085': 'EC311-CPU2A310G1616D-V2.0',
            '51701000084': 'EC310-CPU1A310G1616S-V2.0',
            '51701000083': 'EC310-CPU1A310G1616D-V2.0',
            '51701000068': 'MC622-CPU3A310G1616S-V1.0',
            '51701000066': 'MC621-CPU3S310G1616S-V1.0',
            '51701000062': 'MC612-CPU3A310G1616S-V1.0',
            '51701000061': 'MC612-CPU3A310G1616D-V1.0',
            '51701000060': 'MC611-CPU3S310G1616S-V1.0',
            '51701000059': 'MC611-CPU3S310G1616D-V1.0',
            '51701000056': 'MC521-CPU3S310G1616S-V1.0',
            '51701000055': 'MC521-CPU3S310G1616D-V1.0',
            '51701000052': 'MC511-CPU2A310G1616S-V1.0',
            '51701000051': 'MC511-CPU2A310G1616D-V1.0',
            '51701000048': 'EC431-CPU2A000N-V1.0',
            '51701000047': 'EC412-CPU3A310G1616S-V1.0',
            '51701000046': 'EC412-CPU3A310G1616D-V1.0',
            '51701000045': 'EC411-CPU3S310G1616S-V1.0',
            '51701000044': 'EC411-CPU3S310G1616D-V1.0',
            '51701000043': 'EC321-CPU3S310G1616S-V1.0',
            '51701000041': 'EC312-CPU3A310G1616S-V1.0',
            '51701000039': 'EC311-CPU2A310G1616S-V1.0',
            '51701000038': 'EC311-CPU2A310G1616D-V1.0',
            '51701000037': 'EC310-CPU1A310G1616S-V1.0',
            '51701000036': 'EC310-CPU1A310G1616D-V1.0',
            '51701000031': 'EC206C-CPU1A410G2416R4A-V1.0',
            '51701000030': 'EC206E-CPU1A410G2416R4A-V1.0',
            '51701000029': 'EC205C-CPU1A410G2422R0A-V1.0',
            '51701000028': 'EC205E-CPU1A410G2422R0A-V1.0',
            '51701000025': 'EC203C-CPU1A410G1608R4A-V1.0',
            '51701000024': 'EC203E-CPU1A410G1608R4A-V1.0',
            '51701000023': 'EC202C-CPU1A410G1614R0A-V1.0',
            '51701000022': 'EC202E-CPU1A410G1614R0A-V1.0',
            '51701000007': 'MC621-CPU3S310G1616D-V1.0',
            '51701000006': 'MC622-CPU3A310G1616D-V1.0',
            '51701000005': 'MC522-CPU3A314G1616D-V1.0',
            '51701000004': 'MC512-CPU2A314G1616D-V1.0',
            '51701000003': 'MC501-CPU1A314G1616D-V1.0',
            '51701000002': 'EC321-CPU3S310G1616D-V1.0',
            '51701000001': 'EC312-CPU3A310G1616D-V1.0'
        },
        ProductSeries.MC600: {
            '50801000042': 'MC601-CPU3310G1616D-V3.1',
            '50801000041': 'MC602-CPU3310G1616D-V3.1',
            '50801000040': 'MC600-CPU3310G1616D-V3.1',
            '50801000039': 'MC600-CPU3310G1616D-V2.2',
            '50801000038': 'MC601-CPU3310G1616D-V2.3',
            '50801000037': 'MC602-CPU3310G1616D-V2.2',
            '50801000030': 'MC602-CPU3310G1616S-V3.0',
            '50801000029': 'MC602-CPU3310G1616D-V3.0',
            '50801000028': 'MC601-CPU3310G1616S-V3.0',
            '50801000027': 'MC601-CPU3310G1616D-V3.0',
            '50801000026': 'MC600-CPU3310G1616S-V3.0',
            '50801000025': 'MC600-CPU3310G1616D-V3.0',
            '50801000024': 'MC600-CPU3310G1616D-V2.1',
            '50801000023': 'MC601-CPU3310G1616D-V2.2',
            '50801000022': 'MC602-CPU3310G1616S-V1.0',
            '50801000021': 'MC601-CPU3310G1616S-V1.0',
            '50801000020': 'MC600-CPU3310G1616S-V1.0',
            '50801000019': 'MC602-CPU3310G1616D-V2.1',
            '50801000018': 'MC601-CPU3310G1616D-V2.1',
            '50801000016': 'MC602-CPU3310G1616D-V2.0',
            '50801000015': 'MC601-CPU3310G1616D-V2.0',
            '50801000014': 'MC600-CPU3310G1616D-V2.0',
            '50801000013': 'MC600-CPU3310B-GL-V1.1',
            '50801000009': 'MC601-CPU3310B-GL',
            '50801000008': 'MC601-CPU4300B-GL',
            '50801000007': 'MC601-CPU2320B-GL',
            '50801000006': 'MC600-CPU3310B-GL',
            '50801000005': 'MC600-CPU4300B-GL',
            '50801000004': 'MC600-CPU2320B-GL',
            '50801000003': 'MC600-CPU4300B-EC',
            '50801000002': 'MC600-CPU3310B-EC',
            '50801000001': 'MC600-CPU2320B-EC'
        },
        ProductSeries.GL100: {
            '50302000026': 'GR100-PNS-DIO32D-B-V3.1',
            '50302000025': 'GR100-TCS-DIO32D-B-V3.1',
            '50302000024': 'GR100-PNS-DIO32D-B-V1.1',
            '50302000023': 'GR100-TCS-DIO32D-B-V1.2',
            '50302000013': 'GR100-TCS-DIO32S-B-V3.0',
            '50302000012': 'GR100-TCS-DIO32D-B-V3.0',
            '50302000011': 'GR100-PNS-DIO32S-B-V3.0',
            '50302000010': 'GR100-PNS-DIO32D-B-V3.0',
            '50302000009': 'GR100-PNS-DIO32D-B-V1.0',
            '50302000008': 'GR100-PNS-DIO32S-B-V1.0',
            '50302000007': 'GR100-TCS-V1.1',
            '50302000006': 'GR100-TCS-DIO32S-B-V1.1',
            '50302000005': 'GR100-TCS-DIO32D-B-V1.1',
            '50302000004': 'GR100-TCS-V1.0',
            '50302000002': 'GR100-TCS-DIO32S-B-V1.0',
            '50302000001': 'GR100-TCS-DIO32D-B-V1.0',
            '50301000050': 'GL100-DQ16D-V3.1',
            '50301000049': 'GL100-DQ16D-V1.1',
            '50301000048': 'GL100-DIO32D-V1.1',
            '50301000047': 'GL100-DIO32D-V3.1',
            '50301000037': 'GL100-DQ32S-V3.0',
            '50301000036': 'GL100-DQ32D-V3.0',
            '50301000035': 'GL100-DQ16S-V3.0',
            '50301000034': 'GL100-DQ16R-V3.0',
            '50301000033': 'GL100-DQ16D-V3.0',
            '50301000032': 'GL100-DIO32D-V3.0',
            '50301000031': 'GL100-DI32-V3.0',
            '50301000030': 'GL100-DI16-V3.0',
            '50301000029': 'GL100-DQ32D-V1.1',
            '50301000028': 'GL100-DQ32D-V3.1',
            '50301000027': 'GL100-PDS-V1.0',
            '50301000025': 'GL100-TC4-V1.1',
            '50301000024': 'GL100-AQ4-V1.0',
            '50301000023': 'GL100-DQ16D-V1.0',
            '50301000022': 'GL100-AQ4U-V1.1',
            '50301000021': 'GL100-AQ8I-V2.0',
            '50301000020': 'GL100-RTD4 V1.1',
            '50301000019': 'GL100-AQ4U-V1.0',
            '50301000018': 'GL100-AI4-V1.0',
            '50301000017': 'GL100-DQ16S-V1.0',
            '50301000016': 'GL100-DI16-V1.0',
            '50301000015': 'GL100-AQ8U-V1.1',
            '50301000014': 'GL100-RTD4-V1.0',
            '50301000013': 'GL100-TC4-V1.0',
            '50301000012': 'GL100-WB',
            '50301000011': 'GL100-AQ8 V1.0',
            '50301000010': 'GL100-AQ8U V1.0',
            '50301000009': 'GL100-AQ8I V1.0',
            '50301000008': 'GL100-AI8 V1.0',
            '50301000007': 'GL100-AI8U V1.0',
            '50301000006': 'GL100-AI8I V1.0',
            '50301000005': 'GL100-DIO32D V1.0',
            '50301000004': 'GL100-DQ32S V1.0',
            '50301000003': 'GL100-DQ32D V1.0',
            '50301000002': 'GL100-DQ16R V1.0',
            '50301000001': 'GL100-DI32 V1.0'
        },
        ProductSeries.EC400: {
            '50206000009': 'EC401-LCS-V1.0',
            '50206000007': 'EC400-WB V1.0',
            '50206000006': 'EC400-LCS V1.0',
            '50206000005': 'EC400-PSW V1.0',
            '50206000004': 'EC400-PDS V1.0',
            '50206000003': 'EC400-NLB V1.0',
            '50206000002': 'EC400-NLF V1.0',
            '50205000011': 'EC400-TC-RS485-V1.1',
            '50205000007': 'EC400-TCS-DIO32S-N',
            '50205000006': 'EC400-PNS-DIO32S-N',
            '50205000005': 'EC400-TCS-DIO32D-N',
            '50205000004': 'EC400-PNS-DIO32D-N',
            '50205000003': 'EC400-TC-PME4 V1.0',
            '50205000002': 'EC400-TC-HCE4 V1.0',
            '50205000001': 'EC400-TC-RS485 V1.0',
            '50204000030': 'EC400-AI8I V1.0',
            '50204000029': 'EC400-AI4 V1.1_GD32',
            '50204000028': 'EC400-DIO16D V1.1',
            '50204000027': 'EC400-AQ4U-V1.1_GD32',
            '50204000026': 'EC400-AI8 V1.0_GD32',
            '50204000025': 'EC400-AQ4I V1.0_GD32',
            '50204000024': 'EC400-DQ8R V1.0',
            '50204000023': 'EC400-RTD8 V1.0_GD32',
            '50204000022': 'EC400-AI4I V1.0_GD32',
            '50204000021': 'EC400-DQ32D V1.0',
            '50204000020': 'EC400-DQ32S V1.0',
            '50204000019': 'EC400-DI32 V1.0',
            '50204000018': 'EC400-RTD8 V1.0',
            '50204000017': 'EC400-RTD4 V1.0_GD32',
            '50204000016': 'EC400-TC4 V1.0_GD32',
            '50204000015': 'EC400-AQ4U V1.0_GD32',
            '50204000014': 'EC400-AQ4 V1.0_GD32',
            '50204000013': 'EC400-AI8I V1.0_GD32',
            '50204000012': 'EC400-AI4 V1.0_GD32',
            '50204000010': 'EC400-DIO16D V1.0',
            '50204000009': 'EC400-RTD4 V1.0',
            '50204000008': 'EC400-TC4 V1.0',
            '50204000007': 'EC400-AQ4U V1.0',
            '50204000006': 'EC400-AQ4 V1.0',
            '50204000005': 'EC400-AI4I V1.0',
            '50204000004': 'EC400-AI4 V1.0',
            '50204000003': 'EC400-DQ16D V1.0',
            '50204000002': 'EC400-DQ16S V1.0',
            '50204000001': 'EC400-DI16 V1.0',
            '50203000010': 'EC400-PNS-DIO32D-B-V1.2',
            '50203000009': 'EC400-PNS-DIO32S-B-V1.1',
            '50203000008': 'EC400-PNS-DIO32D-B-V1.1',
            '50203000007': 'EC400-PNS-DIO32S-B',
            '50203000006': 'EC400-TCS-DIO32S-B',
            '50203000005': 'EC400-PNS-DIO32D-B',
            '50203000003': 'EC400-TCS-DIO32D-B',
            '50203000002': 'EC400-PNS V1.0',
            '50203000001': 'EC400-TCS V1.0',
            '50202000002': 'EC400-PS2-6 V1.0',
            '50202000001': 'EC400-PS2-3 V1.0',
            '50201000013': 'EC401-0200N-V1.1',
            '50201000012': 'EC401-0200N-V1.0',
            '50201000011': 'EC401-3210G0000',
            '50201000010': 'EC400-0200N-XXX V1.0/AM4378核心板版本',
            '50201000008': 'EC200-02162404 V1.0',
            '50201000007': 'EC400-2120B-XXX V1.0',
            '50201000006': 'EC400-3110B-XXX V1.0',
            '50201000005': 'EC400-4100B-XXX V1.0',
            '50201000004': 'EC400-4224B-XXX V1.0',
            '50201000003': 'EC400-4220B-XXX V1.0',
            '50201000002': 'EC400-0200B-XXX V1.0',
            '50201000001': 'EC400-0200N-XXX V1.0'
        },
        ProductSeries.EC300: {
            '50701000036': 'EC302-CPU4210G1608D-V3.1',
            '50701000035': 'EC301-CPU4210G1608D-V3.1',
            '50701000034': 'EC302-CPU4210G1608D-V1.1',
            '50701000033': 'EC301-CPU4210G1608D-V1.1',
            '50701000032': 'EC300-CPU5200G1608D-V2.1',
            '50701000031': 'EC300-CPU4200G1608D-V2.1',
            '50701000024': 'EC302-CPU4210G1608D-V3.0',
            '50701000023': 'EC301-CPU4210G1608D-V3.0',
            '50701000022': 'EC302-CPU4210G1608D-V1.0',
            '50701000021': 'EC301-CPU3110G0000-V1.0',
            '50701000020': 'EC301-CPU4210G1608D-V1.0',
            '50701000019': 'EC300-CPU0210G3200-V2.0',
            '50701000018': 'EC300-CPU1200G3200-V2.0',
            '50701000017': 'EC300-CPU5200G1608D-V2.0',
            '50701000016': 'EC300-CPU4200G1608D-V2.0',
            '50701000013': 'EC300-CPU1200G3200-XXX-V1.1/32DI',
            '50701000012': 'EC300-4200B1608-XXX-V1.1/16DI/8DQ',
            '50701000011': 'EC300-CPU4210G2400-V2.0',
            '50701000010': 'EC300-4200B1608-XXX V1.0/16DI/8DQ',
            '50701000007': 'EC300-4210B1608-XXX V1.0/16DI/8DQ',
            '50701000006': 'EC300-4210B2400-XXX V1.0/24DI',
            '50701000005': 'EC300-0210B3200-XXX V1.0/32DI',
            '50701000004': 'EC300-1200B3200-XXX V1.0/32DI',
            '50701000003': 'EC300-5200B1608-XXX V1.0/16DI/8DQD',
            '50701000001': 'EC300-5220B0808-XXX V1.0/8DI/8DQ'
        }
    }

    @classmethod
    def get_model_by_code(cls, product_code: str) -> Optional[str]:
        """
        根据产品编码获取对应的产品型号
        
        Args:
            product_code: 产品编码
            
        Returns:
            对应的产品型号，如果找不到则返回 None
        """
        product_code = product_code.strip().upper()
        for series_mapping in cls._series_mappings.values():
            if product_code in series_mapping:
                return series_mapping[product_code]
        return None

    @classmethod
    def get_series_by_code(cls, product_code: str) -> Optional[ProductSeries]:
        """
        根据产品编码获取所属系列
        
        Args:
            product_code: 产品编码
            
        Returns:
            产品所属系列，如果找不到则返回 None
        """
        product_code = product_code.strip().upper()
        for series, mapping in cls._series_mappings.items():
            if product_code in mapping:
                return series
        return None

    @classmethod
    def get_all_mappings(cls) -> Dict[str, Dict[str, str]]:
        """
        获取所有产品编码和型号的映射关系，按系列分组
        
        Returns:
            包含所有映射关系的字典，key为系列名称，value为该系列的映射关系
        """
        return {series.value: mapping.copy() 
                for series, mapping in cls._series_mappings.items()}

    @classmethod
    def get_series_mappings(cls, series: ProductSeries) -> Dict[str, str]:
        """
        获取指定系列的产品映射关系
        
        Args:
            series: 产品系列
            
        Returns:
            指定系列的产品编码和型号映射关系
        """
        return cls._series_mappings.get(series, {}).copy()

    @classmethod
    def get_all_series(cls) -> List[str]:
        """
        获取所有产品系列名称
        
        Returns:
            所有产品系列名称列表
        """
        return [series.value for series in ProductSeries]

    @classmethod
    def is_valid_code(cls, product_code: str) -> bool:
        """
        检查产品编码是否有效
        
        Args:
            product_code: 产品编码
            
        Returns:
            产品编码是否有效
        """
        product_code = product_code.strip().upper()
        return any(product_code in mapping 
                  for mapping in cls._series_mappings.values())

    @classmethod
    def get_codes_by_series(cls, series: ProductSeries) -> List[str]:
        """
        获取指定系列的所有产品编码
        
        Args:
            series: 产品系列
            
        Returns:
            指定系列的所有产品编码列表
        """
        return list(cls._series_mappings.get(series, {}).keys())