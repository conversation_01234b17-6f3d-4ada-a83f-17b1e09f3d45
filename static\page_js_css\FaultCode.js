// 使用 window 对象来存储全局状态，避免重复声明
if (typeof window.faultCodeState === 'undefined') {
    window.faultCodeState = {
        isActive: false,
        resizeHandler: null
    };
}

function initFaultCodePage() {
    window.faultCodeState.isActive = true;
    const faultCodeContent = document.getElementById('fault-code-content');
    if (!faultCodeContent) {
        Logger.error('无法找到 fault-code-content 元素');
        return;
    }

    faultCodeContent.innerHTML = `
        <div class="container">
            <!-- <h1>故障码查询</h1> -->
            <div class="search-container">
                <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
                <input type="text" id="search-input" placeholder="搜索故障码或描述...">
            </div>
            <div class="overflow-x-auto">
                <table>
                    <thead>
                        <tr>
                            <th style="width: 100px;">故障码</th>
                            <th style="width: 200px;">描述</th>
                            <th>解决方案</th>
                        </tr>
                    </thead>
                    <tbody id="fault-codes-body">
                        <!-- Table rows will be dynamically inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    `;

    const faultCodes = [
        { code: "22", description: "系统启动失败", solution: "检查系统供电无误后，重新启动" },
        { code: "23", description: "系统初始化失败", solution: "检查系统供电无误后，重新启动" },
        { code: "25", description: "CPU温度过高", solution: "请检查系统散热条件" },
        { code: "32", description: "EtherCAT主站错误", solution: "检查 EtherCAT主站及其各子节点硬件连接、主站源地址（MAC）等参数设置是否正确，例如：网线断路、网口与实际使用是否一致、从站异常" },
        { code: "35", description: "Ethernet节点异常", solution: "检查Ethernet及其各子节点硬件连接、参数设置是否正确" },
        { code: "36", description: "Profinet主站异常", solution: "检查Profinet主站及各其子节点硬件连接、参数设置是否正确，例如：Ethernet网络接口及网络信息与实际不一致" },
        { code: "37", description: "Profinet从站异常", solution: "检查Profinet从站与主站设备硬件连接、参数设置是否正确，例如：与主站网络是否正常、网络设置是否同一个网段" },
        { code: "39", description: "Ethernet/IP scanner异常", solution: "检查Ethernet/IP scanner及其各子节点硬件连接、参数设置是否正确，例如：与从站网络是否正常、网络设置是否同一个网段" },
        { code: "3a", description: "Ethernet/IP adapter", solution: "检查Ethernet/IP adapter及其所属模块硬件连接、参数配置是否正确" },
        { code: "3c", description: "CAN节点异常", solution: "检查CAN节点通信网络、波特率是否正确" },
        { code: "3d", description: "CAN主站异常", solution: "检查CAN主站及其各子节点硬件连接、参数设置是否正确，例如：CAN通信的接线错误、从站ID与物理站号不一致" },
        { code: "3e", description: "CAN从站设备异常", solution: "检查CAN从站设备硬件连接、参数设置是否正确" },
        { code: "3f", description: "Modbus_tcp主站异常", solution: "检查Modbus_tcp主站及其各子节点硬件连接、参数设置是否正确，例如从站IP、端口设置是否正确" },
        { code: "40", description: "Modbus_tcp从站设备异常", solution: "检查Modbus_tcp从站设备硬件连接、参数设置是否正" },
        { code: "42", description: "Modbus_rtu节点异常", solution: "检查Modbus_rtu节点及其各子节点硬件连接、参数设置是否正确，例如COM端口选择错误" },
        { code: "43", description: "Modbus_rtu主站错误", solution: "检查Modbus_rtu主站及其各子节点硬件连接、参数设置是否正确，例如从站地址与实际不符，主站与从站物理连接断开" },
        { code: "44", description: "Modbus_rtu从站设备异常", solution: "检查Modbus_rtu从站设备硬件连接、参数设置是否正确" },
        { code: "5a", description: "应用程序停止", solution: "检查RUN/STOP开关是否处于STOP状态，若处于RUN状态，查看网页上是否关闭了应用程序，初始复位设备也显示5a" },
        { code: "5b", description: "应用程序异常", solution: "检查应用程序实现逻辑是否有误，例如：除0、空指针、数组越界" },
        { code: "5c", description: "应用程序占用内存过大", solution: "请在IDE'编译->清除全部' 后再重新下载或重启PLC，若仍然报错请优化应用程序或选择更高配置的PLC" },
        { code: "72", description: "本体IO模块异常", solution: "检查本体IO模块电源输入是否正常、输出模块通道是否短路" },
        { code: "73", description: "组态不匹配", solution: "出现73错误码后，数码管紧接显示的是异常模块的插槽号。按照显示检查软、硬件组态，并修正错误，例如73 02表示第二个模块硬件组态与软件组态不一致" },
        { code: "74", description: "扩展IO模块电源异常", solution: "出现74错误码后，数码管紧接显示的是异常模块的插槽号。如74 02表示第二个模块电源异常，请检查对应模块的接线是否正确，例如：检查对应扩展IO模块电源输入是否正常、输出模块通道是否短路" },
        { code: "75", description: "扩展IO模块过流/过温", solution: "出现75错误码后，数码管紧接显示的是异常模块的插槽号。按照显示检查对应模块" },
        { code: "76", description: "尾板异常", solution: "检查尾板是否安装稳固" },
        { code: "77", description: "扩展IO模块通道异常", solution: "出现77错误码后，数码管紧接显示的是异常模块的插槽号。按照显示检查对应扩展IO模块" },
    ];

    const searchInput = document.getElementById('search-input');
    const faultCodesBody = document.getElementById('fault-codes-body');

    function renderFaultCodes(codes) {
        const fragment = document.createDocumentFragment();
        if (codes.length === 0) {
            faultCodesBody.innerHTML = '';
            SweetAlert.info('未找到匹配的故障码');
            return;
        }

        codes.forEach(code => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td style="font-weight: 500;">${code.code}</td>
                <td>${code.description}</td>
                <td>${code.solution}</td>
            `;
            fragment.appendChild(row);
        });
        faultCodesBody.innerHTML = '';
        faultCodesBody.appendChild(fragment);
    }

    function filterFaultCodes(searchTerm) {
        try {
            return faultCodes.filter(code =>
                code.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                code.description.toLowerCase().includes(searchTerm.toLowerCase())
            );
        } catch (error) {
            Logger.error('搜索故障码失败:', error);
            SweetAlert.error('搜索故障码时发生错误');
            return [];
        }
    }

    // 如果已经存在之前的事件监听器，先移除它
    if (window.faultCodeState.searchHandler) {
        searchInput.removeEventListener('input', window.faultCodeState.searchHandler);
    }

    // 创建新的搜索处理函数
    let debounceTimer;
    const searchHandler = (e) => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            try {
                const filteredCodes = filterFaultCodes(e.target.value);
                renderFaultCodes(filteredCodes);
            } catch (error) {
                Logger.error('处理搜索请求失败:', error);
                SweetAlert.error('搜索处理失败，请重试');
            }
        }, 300);
    };

    // 保存搜索处理函数的引用
    window.faultCodeState.searchHandler = searchHandler;
    searchInput.addEventListener('input', searchHandler);

    // 修改响应式处理
    function handleResponsiveness() {
        if (!window.faultCodeState.isActive) return;
        
        const table = document.querySelector('table');
        if (!table) return;
        
        if (window.innerWidth <= 768) {
            table.classList.add('responsive-table');
        } else {
            table.classList.remove('responsive-table');
        }
    }

    // 如果已经存在之前的事件监听器，先移除它
    if (window.faultCodeState.resizeHandler) {
        window.removeEventListener('resize', window.faultCodeState.resizeHandler);
    }

    // 保存新的 resize handler 的引用
    window.faultCodeState.resizeHandler = handleResponsiveness;
    window.addEventListener('resize', window.faultCodeState.resizeHandler);

    // 初始化时显示加载状态
    try {
        // 初始渲染
        renderFaultCodes(faultCodes);
        handleResponsiveness();
    } catch (error) {
        Logger.error('初始化故障码列表失败:', error);
        SweetAlert.error('加载故障码列表失败，请刷新页面重试');
    }
}

// 添加清理函数
function cleanupFaultCode() {
    window.faultCodeState.isActive = false;
    // 移除所有事件监听器
    if (window.faultCodeState.resizeHandler) {
        window.removeEventListener('resize', window.faultCodeState.resizeHandler);
        window.faultCodeState.resizeHandler = null;
    }
    if (window.faultCodeState.searchHandler) {
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.removeEventListener('input', window.faultCodeState.searchHandler);
        }
        window.faultCodeState.searchHandler = null;
    }
}

// 导出这些函数
window.initFaultCodePage = initFaultCodePage;
window.cleanupFaultCode = cleanupFaultCode;
