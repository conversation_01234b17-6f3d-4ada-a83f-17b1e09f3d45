:root {
    --sidebar-width: 210px;
    --header-height: 60px;
    
    /* 修改菜单相关颜色 */
    --menu-bg: #304156;          /* 主菜单背景色 */
    --menu-sub-bg: #1f2d3d;      /* 子菜单背景色 */
    --menu-hover: #263445;       /* 菜单hover状态 */
    --menu-sub-hover: #001528;   /* 子菜单hover状态 */
    
    /* 修改标签相关颜色 */
    --tab-container-bg: #fff;    /* 标签容器背景色 */
    --tab-bg: #fff;              /* 普通标签背景色 */
    --tab-active-bg: #42b983;    /* 激活状态标签背景色 */
    
    /* 其他颜色保持不变 */
    --menu-text: #a0a8b7;
    --menu-active-text: #ffffff;
    --main-bg: #f4f6f9;
    --primary-color: #3c8dbc;
    /*--primary-color: #42b983 */;
    --secondary-color: #5cb85c;
    --accent-color: #f39c12;
    
    /* 字体大小变量 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    --spacing-base: 1rem;
    --border-radius: 0.25rem;
    font-size: 14px; /* 基准字体大小 */
    
    /* 添加字体变量 */
    --font-family-base: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}
body, html {
    margin: 0;
    padding: 0;
    font-family: var(--font-family-base);
    height: 100%;
    background-color: var(--main-bg);
    color: #333;
    -webkit-font-smoothing: antialiased;  /* 添加字体平滑 */
    -moz-osx-font-smoothing: grayscale;   /* 添加字体平滑 */
}
.app-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
}
.sidebar-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    width: var(--sidebar-width);
    background-color: var(--menu-bg);
    z-index: 1001;
    transition: all 0.3s ease;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}
.sidebar-container::-webkit-scrollbar {
    width: 6px;
}
.sidebar-container::-webkit-scrollbar-thumb {
    background-color: rgba(255,255,255,0.2);
    border-radius: 3px;
}
.main-container {
    margin-left: var(--sidebar-width);
    min-height: 100%;
    transition: margin-left 0.3s ease;
    position: relative;
}
.navbar {
    height: var(--header-height);
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 20px;
    justify-content: space-between;
}
.navbar-left {
    display: flex;
    align-items: center;
}
.hamburger {
    cursor: pointer;
    font-size: 20px;
    color: #5a5e66;
    margin-right: 15px;
    display: flex;
    align-items: center;
}
.app-main {
    padding: 20px;
}
.logo {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255,255,255,0.05);
    transition: all 0.3s;
    overflow: hidden;
}
.logo img {
    width: 40px;
    height: 40px;
    transition: all 0.3s;
}
.logo span {
    color: #fff;
    font-size: 20px;
    font-weight: 600;
    margin-left: 12px;
    transition: all 0.3s;
}
.menu {
    padding: 16px 0;
    background-color: var(--menu-bg);
}
.menu-item {
    color: var(--menu-text);
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    font-family: var(--font-family-base);
    font-weight: 500;
}
.menu-item i {
    margin-right: 16px;
    font-size: 18px;
    width: 24px;
    text-align: center;
    transition: all 0.3s;
}
.menu-item span {
    transition: all 0.3s;
}
.menu-item:hover {
    color: var(--menu-active-text);
    background-color: var(--menu-hover);
}
.menu-item.active {
    color: var(--menu-active-text);
    background-color: var(--menu-active-bg);
}
.menu-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: var(--primary-color);
}
.submenu {
    padding-left: 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
    opacity: 0;
}
.submenu.active {
    max-height: 500px;
    opacity: 1;
    transition: max-height 0.5s ease-in, opacity 0.5s ease-in;
}
.submenu .menu-item {
    padding-left: 35px;
}
.submenu .menu-item:hover {
    background-color: var(--menu-sub-hover);
}
.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    /* 删除之前添加的绝对定位相关的代码 */
}
.breadcrumb span {
    margin: 0 5px;
    color: #97a8be;
    display: flex;
    align-items: center;
}

.breadcrumb span:last-child {
    color: #606266;
}

.card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    padding: 20px;
    transition: all 0.3s;
}
.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}
#chart {
    height: 350px;
}
.dashboard-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}
.dashboard-item {
    flex: 1;
    min-width: 200px;
}
.dashboard-item .card {
    height: 100%;
}
.stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: #fff;
}
.stat-icon {
    font-size: 48px;
    margin-right: 20px;
    opacity: 0.8;
}
.stat-info h3 {
    font-size: 28px;
    margin: 0;
    font-weight: 600;
}
.stat-info p {
    margin: 5px 0 0;
    opacity: 0.8;
}
.tags-view-container {
    height: 40px;
    width: 100%;
    background: #fff;
    border-bottom: 1px solid #d8dce5;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.tags-view-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    overflow-x: auto;
}
.tags-view-wrapper::-webkit-scrollbar {
    display: none;
}
.tags-view-item {
    display: inline-flex;
    position: relative;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    border: 1px solid #d8dce5;
    color: #495060;
    background: #fff;
    padding: 0 12px;
    font-size: 12px;
    margin: 4px;
    user-select: none;
    transition: all 0.3s;
    border-radius: 4px;
}
.tags-view-item.active {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}
.tags-view-item .el-icon-close {
    width: 16px;
    height: 16px;
    line-height: 16px;
    vertical-align: middle;
    border-radius: 50%;
    text-align: center;
    transition: all .3s;
    transform-origin: 100% 50%;
    margin-left: 5px;
    display: inline-flex;
    align-items: center;
    justify-content: center;

}

.tags-view-item .el-icon-close:hover {
    background-color: rgba(0,0,0,0.1);
    color: #fff;
}

.tags-view-item .el-icon-close:before {
    transform: scale(0.8);
    display: inline-block;
}
.breadcrumb-item {
    cursor: pointer;
    color: #303133;  /* 更深的颜色 */
    font-weight: 500;  /* 稍微加粗 */
}

.breadcrumb-item:hover {
    color: #409EFF;  /* 鼠标悬停时显示蓝色 */
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #909399;  /* 灰色 */
    cursor: default;
    font-weight: normal;  /* 恢复正常粗细 */
}

.breadcrumb-item.active:hover {
    color: #909399;  /* 保持灰色，不变为蓝色 */
    text-decoration: none;
}

.navbar-right {
    display: flex;
    align-items: center;
}

.navbar-item {
    margin-left: 20px;
    cursor: pointer;
}

.navbar-item i {
    font-size: 18px;
    color: #5a5e66;
}

#language-select {
    border: none;
    background: transparent;
    font-size: 14px;
    color: #5a5e66;
}

.user-dropdown {
    position: relative;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #f9f9f9;
    min-width: 120px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
}

.dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}

.dropdown-content a:hover {
    background-color: #f1f1f1;
}

.user-dropdown:hover .dropdown-content {
    display: block;
}

/* 在文件末尾添加以下代码 */

.compact-layout .main-container {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.context-menu {
    display: none;
    position: absolute;
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    border-radius: 4px;
    overflow: hidden;
}

.context-menu-item {
    padding: 10px 15px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.context-menu-item:hover {
    background-color: var(--primary-color);
    color: #fff;
}

.tags-view-item.refreshing {
    animation: refresh-pulse 0.5s ease-in-out;
}

@keyframes refresh-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 添加以下新样式 */
.sidebar-container.collapsed {
    width: 64px;
}

.sidebar-container.collapsed .logo span,
.sidebar-container.collapsed .menu-item span,
.sidebar-container.collapsed .menu-item i.fa-chevron-down {
    display: none;
}

.sidebar-container.collapsed .menu-item {
    padding: 15px 20px;
    justify-content: center;
}

.sidebar-container.collapsed .submenu {
    display: none;
}

/* 添加新样式以确保图标居中 */
.sidebar-container.collapsed .menu-item i:not(.fa-chevron-down) {
    margin-right: 0;
    font-size: 20px;
}

/* 添加悬停效果以显示菜单项文本 */
.sidebar-container.collapsed .menu-item:hover {
    width: auto;
    padding-right: 20px;
}

.sidebar-container.collapsed .menu-item:hover span {
    display: inline-block;
    padding-left: 10px;
}

/* 在文件末尾添加以下样式 */
.menu-item .fa-chevron-down {
    margin-left: auto;
    transition: transform 0.3s;
}

.menu-item .fa-chevron-down.fa-rotate-180 {
    transform: rotate(180deg);
}

.submenu {
    padding-left: 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.submenu.active {
    max-height: 500px;
    transition: max-height 0.5s ease-in;
}

/* 添加一些动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.menu-item {
    animation: fadeIn 0.3s ease-out;
}

.submenu .menu-item {
    animation: fadeIn 0.3s ease-out;
}

/* 添加悬停效果 */
.menu-item:hover i {
    transform: scale(1.1);
}

.menu-item:hover span {
    padding-left: 5px;
}

@media (max-width: 768px) {
    :root {
        font-size: 14px; /* 在较小屏幕上略微减小字体大小 */
    }
    .sidebar-container.collapsed {
        width: 64px;
    }

    .sidebar-container.collapsed .logo span,
    .sidebar-container.collapsed .menu-item span,
    .sidebar-container.collapsed .menu-item i.fa-chevron-down {
        display: none;
    }

    .sidebar-container.collapsed .menu-item {
        padding: 15px 20px;
        justify-content: center;
    }

    .sidebar-container.collapsed .submenu {
        display: none;
    }

    .main-container {
        margin-left: 64px;
    }
}

h1 {
    font-size: clamp(1.5rem, 5vw, 2.25rem);
}

@media print {
    .sidebar-container, .navbar, .tags-view-container {
        display: none;
    }
    .main-container {
        margin-left: 0;
    }
}

/* 保留现有的样式 */

.fixed-header {
    position: fixed;
    top: 0;
    left: var(--sidebar-width);
    right: 0;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    transition: left 0.3s ease;
}

.sidebar-container.collapsed + .main-container .fixed-header {
    left: 64px;
}

.navbar {
    height: var(--header-height);
    display: flex;
    align-items: center;
    padding: 0 20px;
    justify-content: space-between;
}

.tags-view-container {
    height: 40px;
    width: 100%;
    background: #fff;
    border-bottom: 1px solid #d8dce5;
}

.breadcrumb-container {
    padding: 10px 20px;
    background-color: #fff;
    border-bottom: 1px solid #e5e7eb;
}

.app-main {
    padding: 20px;
    margin-top: calc(var(--header-height) + 40px + 41px); /* 导航栏高度 + 标签视图高度 + 面包屑高度 */
}

@media (max-width: 768px) {
    .fixed-header {
        left: 64px;
    }
    
    .app-main {
        margin-top: calc(var(--header-height) + 40px + 41px);
    }
}

/* 添加打印样式 */
@media print {
    .fixed-header {
        position: static;
    }
    
    .app-main {
        margin-top: 0;
    }
}


/* 模态框样式 用户修改密码*/
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 500px;
    border-radius: 5px;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-buttons {
    text-align: right;
    margin-top: 20px;
}

.form-buttons button {
    padding: 8px 15px;
    margin-left: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.form-buttons button[type="submit"] {
    background-color: #409EFF;
    color: white;
}

.form-buttons button.cancel {
    background-color: #909399;
    color: white;
}
/* 用户修改密码结束*/

.menu-sub {
    background-color: var(--menu-sub-bg);
}

.menu-sub .menu-item:hover {
    background-color: var(--menu-sub-hover);
}

.tab-container {
    background-color: var(--tab-container-bg);
}

.tab {
    background-color: var(--tab-bg);
}

.tab.active {
    background-color: var(--tab-active-bg);
}

/* 标题使用稍微粗一点的字重 */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-base);
    font-weight: 600;
}
