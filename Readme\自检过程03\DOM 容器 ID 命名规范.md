# DOM 容器 ID 命名规范与修改总结

## 已完成的修改总结

我们对代码进行了以下修改，以统一 DOM 容器 ID 命名格式和变量使用：

1. **删除了映射代码**：
   - 移除了 `stageMapping`、`roleMapping`、`reverseStageMapping` 和 `reverseRoleMapping` 的使用
   - 直接使用后端的阶段和角色名称，避免了不必要的转换

2. **统一了 DOM 元素 ID 格式**：
   - 使用 `${stage}-${role}` 格式，而不是 `${stage}${role}`
   - 移除了对混合格式的支持，如 `#${stage}-${role}, #${stage}${role}`

3. **使用常量而不是字符串字面量**：
   - 使用 `window.STAGES.ASSEMBLY`、`window.ROLES.FIRST` 等常量
   - 替换了 `'production'`、`'qc'` 等前端角色名称

4. **统一了阶段和角色名称**：
   - 使用后端的阶段名称：`assembly`、`test`、`packaging`
   - 使用后端的角色名称：`first`、`self`、`ipqc`

5. **修复了变量重复声明问题**：
   - 在 ScanSN.js 中，使用 `window.currentScannedProducts` 而不是创建本地变量
   - 在 SelfInspection.js 中，使用 `window.STAGES` 和 `window.ROLES` 而不是创建本地变量

## DOM 容器 ID 命名规范

为了保持代码的一致性和可维护性，我们建立了以下命名规范：

### 1. 阶段和角色的 DOM 容器 ID

使用以下格式：`${stage}-${role}`

例如：
- `assembly-first` - 组装前阶段的首检
- `test-self` - 测试前阶段的自检
- `packaging-ipqc` - 包装前阶段的 IPQC

### 2. 阶段内容容器

使用以下格式：`${stage}-content` 或 `${stage}Content`

例如：
- `assembly-content` - 组装前阶段的内容容器

### 3. 角色选项卡

使用以下格式：`${stage}-${role}-tab`

例如：
- `assembly-first-tab` - 组装前阶段首检的选项卡

### 4. 操作人员输入区域

使用以下格式：`${stage}-${role}-operator`

例如：
- `assembly-first-operator` - 组装前阶段首检的操作人员输入区域

### 5. 附件列表容器

使用以下格式：`${stage}-${role}-files`

例如：
- `assembly-first-files` - 组装前阶段首检的附件列表容器

### 6. 阶段进度显示

使用以下格式：`${stage}-progress`

例如：
- `assembly-progress` - 组装前阶段的进度显示

### 7. 阶段节点

使用以下格式：`${stage}-stage`

例如：
- `assembly-stage` - 组装前阶段的节点

## 阶段和角色名称规范

### 阶段名称

使用后端定义的阶段名称常量：

```javascript
window.STAGES = {
    ASSEMBLY: 'assembly',    // 组装前阶段
    TEST: 'test',            // 测试前阶段
    PACKAGING: 'packaging'   // 包装前阶段
};
```

### 角色名称

使用后端定义的角色名称常量：

```javascript
window.ROLES = {
    FIRST: 'first',    // 首检
    SELF: 'self',      // 自检
    IPQC: 'ipqc'       // IPQC
};
```

## 变量使用规范

1. **全局变量**：
   - 使用 `window` 对象存储全局变量，避免重复声明
   - 在声明前检查变量是否已存在：`if (typeof window.variableName === 'undefined')`

2. **常量引用**：
   - 直接使用 `window.STAGES` 和 `window.ROLES` 而不是创建本地引用
   - 例如：`window.STAGES.ASSEMBLY` 而不是 `STAGES.ASSEMBLY`

3. **数据结构**：
   - 使用一致的数据结构格式，如 `inspectionData.pageState.inspectionSubmissions[stage][role]`
   - 使用常量作为键：`[window.STAGES.ASSEMBLY][window.ROLES.FIRST]`

## 代码示例

### 正确的 DOM 元素选择方式

```javascript
// 选择操作人员输入框
const operatorInput = document.querySelector(`#${stage}-${type}-operator .self-inspection__operator-input input`);

// 选择阶段内容容器
const stageContent = document.getElementById(`${stage}Content`);

// 选择角色选项卡
const tab = document.getElementById(`${stage}-${role}-tab`);
```

### 正确的数据访问方式

```javascript
// 检查提交状态
const isSubmitted = inspectionData.pageState.inspectionSubmissions[stage][window.ROLES.FIRST].isSubmitted;

// 获取检验结果
const results = inspectionData.pageState.inspectionResults[stage][role];

// 获取附件列表
const files = inspectionData.pageState.attachments[stage][role];
```

## 总结

通过统一 DOM 容器 ID 命名格式和变量使用，我们解决了以下问题：

1. **ID 命名格式不一致**：统一使用 `${stage}-${role}` 格式
2. **角色名称使用不一致**：统一使用后端角色名称 `first`、`self`、`ipqc`
3. **选择器类型混用**：统一使用 ID 选择器
4. **阶段名称不一致**：统一使用后端阶段名称 `assembly`、`test`、`packaging`
5. **变量重复声明**：使用 `window` 对象存储全局变量，避免重复声明

这些修改使得代码更加一致，减少了混淆，并且使用了更清晰的命名约定，有助于提高代码的可维护性和可读性。
