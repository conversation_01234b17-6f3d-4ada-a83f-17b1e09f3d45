/* 作废版本页面样式 - 采用BEM命名规范 */

.obsolete-firmware {
    min-height: 100vh;
    background-color: #f5f5f5;
}

/* ===== 页面头部 ===== */
.obsolete-firmware__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.obsolete-firmware__header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.obsolete-firmware__header:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.obsolete-firmware__title {
    margin: 0;
    color: #303133;
    font-size: 20px;
    font-weight: 600;
    position: relative;
    padding-left: 16px;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.obsolete-firmware__title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 22px;
    background: linear-gradient(to bottom, #f56c6c, #fc9595);
    border-radius: 3px;
}

.obsolete-firmware__stats {
    display: flex;
    gap: 15px;
    align-items: center;
}

.obsolete-firmware__stats .el-tag {
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.obsolete-firmware__stats .el-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
}

/* ===== 搜索栏 ===== */
.obsolete-firmware__search-bar {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
}

.obsolete-firmware__search-input {
    flex: 1;
    min-width: 400px;
}

.obsolete-firmware__actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* ===== 表格容器 ===== */
.obsolete-firmware__table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* ===== 分页样式移到共享文件 ===== */
/* 参见 firmware-common-paging.css */

/* ===== 表格内容样式 ===== */
.obsolete-firmware__serial-link {
    font-weight: 600;
    text-decoration: none;
    color: #409eff;
}

.obsolete-firmware__serial-link:hover {
    text-decoration: underline;
}

.obsolete-firmware__products {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.obsolete-firmware__product-tag {
    margin: 2px 0;
}

.obsolete-firmware__status--obsolete { 
    color: #F56C6C; 
    font-weight: bold;
}

/* ===== 详情对话框样式 ===== */
.obsolete-firmware__detail-dialog .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    min-width: 700px;
    width: 70% !important;
    max-width: 1200px !important;
}

.obsolete-firmware__detail-dialog .el-dialog__header {
    background-color: #f9fafc;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    position: relative;
}

.obsolete-firmware__detail-dialog .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 0.2px;
}

.obsolete-firmware__detail-dialog .el-dialog__body {
    padding: 0;
}

.obsolete-firmware__detail-content {
    display: flex;
    gap: 30px;
    padding: 20px;
    min-height: 500px;
    background: linear-gradient(to bottom, #fafbfc 0%, #ffffff 100%);
    flex-wrap: wrap;
}

.obsolete-firmware__detail-left,
.obsolete-firmware__detail-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 300px;
}

.obsolete-firmware__status-tag {
    margin-bottom: 20px;
    font-size: 14px !important;
    padding: 8px 16px !important;
    height: auto !important;
    line-height: 1.5 !important;
    border-radius: 20px !important;
}

.obsolete-firmware__detail-section {
    margin-bottom: 25px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid #ebeef5;
    transition: all 0.3s ease;
    padding: 16px;
    width: 100%;
}

.obsolete-firmware__detail-section:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.obsolete-firmware__detail-section h4 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
    position: relative;
    padding-left: 12px;
}

.obsolete-firmware__detail-section h4::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: linear-gradient(to bottom, #f56c6c, #fc9595);
    border-radius: 2px;
}

.obsolete-firmware__detail-divider {
    width: 100%;
    height: 1px;
    background-color: #ebeef5;
    margin: 10px 0 15px 0;
}

.obsolete-firmware__detail-section p {
    margin: 8px 0;
    line-height: 1.6;
    color: #606266;
    word-wrap: break-word;
    word-break: break-all;
}

.obsolete-firmware__detail-section strong {
    color: #303133;
    font-weight: 600;
}

.obsolete-firmware__detail-products {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    word-wrap: break-word;
    word-break: break-all;
    width: 100%;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.obsolete-firmware__detail-product-tag {
    margin: 4px;
    transition: all 0.2s ease;
    white-space: normal;
    height: auto;
    line-height: 1.5;
    max-width: 100%;
    display: inline-block;
    padding: 4px 8px;
    word-break: break-all;
    overflow-wrap: anywhere;
}

.obsolete-firmware__detail-product-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.obsolete-firmware__detail-requirements {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    line-height: 1.6;
    color: #495057;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
    font-family: inherit;
    transition: all 0.2s ease;
    max-width: 100%;
    overflow: hidden;
}

.obsolete-firmware__detail-requirements:hover {
    border-color: #dcdfe6;
    background-color: #fcfcfc;
}

.obsolete-firmware__detail-description {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    line-height: 1.6;
    color: #495057;
    margin: 0;
    transition: all 0.2s ease;
    word-wrap: break-word;
    word-break: break-all;
    max-width: 100%;
    overflow: hidden;
}

.obsolete-firmware__detail-description:hover {
    border-color: #dcdfe6;
    background-color: #fcfcfc;
}

.obsolete-firmware__detail-reason {
    background-color: #fef0f0;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #fbc4c4;
    line-height: 1.6;
    color: #f56c6c;
    margin: 0;
    font-weight: 500;
    transition: all 0.2s ease;
    word-wrap: break-word;
    word-break: break-all;
    max-width: 100%;
    overflow: hidden;
}

.obsolete-firmware__detail-reason:hover {
    background-color: #fee6e6;
    border-color: #fab6b6;
}

.obsolete-firmware__detail-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #f9fafc;
}

.obsolete-firmware__detail-footer .el-button {
    transition: all 0.3s ease;
}

.obsolete-firmware__detail-footer .el-button:hover {
    transform: translateY(-2px);
}

/* ===== 恢复对话框样式 ===== */
.obsolete-firmware__restore-dialog .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.obsolete-firmware__restore-dialog .el-dialog__header {
    background-color: #f9fafc;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
}

.obsolete-firmware__restore-dialog .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 0.2px;
}

.obsolete-firmware__restore-content {
    padding: 20px;
    background: linear-gradient(to bottom, #fafbfc 0%, #ffffff 100%);
}

.obsolete-firmware__restore-info {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.obsolete-firmware__restore-info:hover {
    background-color: #fcfcfc;
    border-color: #dcdfe6;
}

.obsolete-firmware__restore-info p {
    margin: 0;
    color: #495057;
    font-weight: 500;
    line-height: 1.6;
}

.obsolete-firmware__restore-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #f9fafc;
}

.obsolete-firmware__restore-footer .el-button {
    transition: all 0.3s ease;
}

.obsolete-firmware__restore-footer .el-button--primary {
    background: linear-gradient(135deg, #67c23a, #85ce61);
    border-color: #67c23a;
    box-shadow: 0 2px 6px rgba(103, 194, 58, 0.4);
}

.obsolete-firmware__restore-footer .el-button--primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.5);
    background: linear-gradient(135deg, #5daf34, #70b541);
}

/* ===== 统计分析对话框样式 ===== */
.obsolete-firmware__statistics-dialog .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    width: 70% !important;
    max-width: 1000px;
    min-width: 700px;
}

.obsolete-firmware__statistics-dialog .el-dialog__header {
    background-color: #f9fafc;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
}

.obsolete-firmware__statistics-dialog .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 0.2px;
}

.obsolete-firmware__statistics-dialog .el-dialog__body {
    padding: 0;
    background: linear-gradient(to bottom, #fafbfc 0%, #ffffff 100%);
}

.obsolete-firmware__statistics-content {
    padding: 20px;
}

.obsolete-firmware__statistics-summary {
    margin-bottom: 30px;
}

.obsolete-firmware__stat-card {
    text-align: center;
    transition: all 0.3s ease;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    padding: 20px;
    border: 1px solid #ebeef5;
}

.obsolete-firmware__stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.obsolete-firmware__stat-number {
    font-size: 36px;
    font-weight: 700;
    color: #f56c6c;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #f56c6c, #fc9595);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.obsolete-firmware__stat-label {
    font-size: 14px;
    color: #606266;
}

.obsolete-firmware__reason-list {
    max-height: 400px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #ebeef5;
    padding: 10px 0;
}

.obsolete-firmware__reason-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border-bottom: 1px solid #ebeef5;
    gap: 15px;
    transition: all 0.3s ease;
    flex-wrap: wrap;
}

.obsolete-firmware__reason-item:last-child {
    border-bottom: none;
}

.obsolete-firmware__reason-item:hover {
    background-color: #f9fafc;
    padding-left: 20px;
}

.obsolete-firmware__reason-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: #f56c6c;
    color: white;
    border-radius: 50%;
    font-size: 14px;
    font-weight: bold;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
    transition: all 0.3s ease;
}

.obsolete-firmware__reason-item:hover .obsolete-firmware__reason-number {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
}

.obsolete-firmware__reason-info {
    flex: 1;
    min-width: 0;
    word-wrap: break-word;
    word-break: break-all;
}

.obsolete-firmware__reason-name {
    font-size: 14px;
    color: #303133;
    font-weight: 600;
    margin-bottom: 4px;
    word-break: break-all;
}

.obsolete-firmware__reason-count {
    font-size: 12px;
    color: #909399;
}

.obsolete-firmware__reason-bar {
    width: 150px;
    height: 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
    margin-top: 5px;
}

.obsolete-firmware__reason-progress {
    height: 100%;
    background: linear-gradient(90deg, #f56c6c, #fc9595);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.obsolete-firmware__statistics-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #f9fafc;
}

.obsolete-firmware__statistics-footer .el-button {
    transition: all 0.3s ease;
}

.obsolete-firmware__statistics-footer .el-button--primary {
    background: linear-gradient(135deg, #f56c6c, #fc9595);
    border-color: #f56c6c;
    box-shadow: 0 2px 6px rgba(245, 108, 108, 0.4);
}

.obsolete-firmware__statistics-footer .el-button--primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.5);
    background: linear-gradient(135deg, #e64545, #f77c7c);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
    .obsolete-firmware__detail-content {
        flex-direction: column;
        gap: 20px;
    }
    
    .obsolete-firmware__search-input {
        min-width: 250px;
    }
    
    .obsolete-firmware__statistics-dialog .el-dialog {
        width: 80% !important;
    }

    .obsolete-firmware__detail-left,
    .obsolete-firmware__detail-right {
        min-width: 100%;
    }
}

@media (max-width: 768px) {
    .obsolete-firmware__header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .obsolete-firmware__stats {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .obsolete-firmware__search-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .obsolete-firmware__search-input {
        min-width: auto;
    }
    
    .obsolete-firmware__actions {
        justify-content: flex-start;
    }
    
    .obsolete-firmware__reason-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .obsolete-firmware__reason-bar {
        width: 100%;
        margin-top: 10px;
    }
    
    .obsolete-firmware__statistics-summary .el-col {
        margin-bottom: 15px;
    }
    
    .obsolete-firmware__statistics-dialog .el-dialog {
        width: 95% !important;
        min-width: auto;
    }

    .obsolete-firmware__detail-dialog .el-dialog {
        width: 95% !important;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .obsolete-firmware__header,
    .obsolete-firmware__search-bar,
    .obsolete-firmware__table-container {
        margin: 0 -10px 20px -10px;
        border-radius: 0;
    }
    
    .obsolete-firmware__detail-dialog,
    .obsolete-firmware__statistics-dialog,
    .obsolete-firmware__restore-dialog {
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        height: 100vh !important;
        border-radius: 0 !important;
    }
}

/* ===== 滚动条样式 ===== */
.obsolete-firmware__reason-list::-webkit-scrollbar {
    width: 6px;
}

.obsolete-firmware__reason-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.obsolete-firmware__reason-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.obsolete-firmware__reason-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== 动画效果 ===== */
.obsolete-firmware__detail-dialog .el-dialog,
.obsolete-firmware__statistics-dialog .el-dialog,
.obsolete-firmware__restore-dialog .el-dialog {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.obsolete-firmware__stat-card,
.obsolete-firmware__reason-item,
.obsolete-firmware__detail-section {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: calc(var(--item-index, 0) * 0.1s);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 表格行状态样式 ===== */
.obsolete-firmware .el-table .el-table__row {
    transition: background-color 0.3s ease;
}

.obsolete-firmware .el-table .el-table__row:hover {
    background-color: #f5f7fa;
}

/* ===== Element Plus组件样式重写 ===== */
.obsolete-firmware .el-tag--danger {
    background-color: #fef0f0;
    border-color: #fbc4c4;
    color: #f56c6c;
}

.obsolete-firmware .el-tag--warning {
    background-color: #fdf6ec;
    border-color: #f5dab1;
    color: #e6a23c;
}

.obsolete-firmware .el-tag--success {
    background-color: #f0f9ff;
    border-color: #b3d8ff;
    color: #67c23a;
}

.obsolete-firmware .el-tag--info {
    background-color: #f4f4f5;
    border-color: #d3d4d6;
    color: #909399;
}

/* ===== 加载状态样式 ===== */
.obsolete-firmware .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
} 