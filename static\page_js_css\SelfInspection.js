﻿// 全局变量 - 使用window对象来避免重复声明
if (typeof window.productStatusRefreshTimer === 'undefined') {
    window.productStatusRefreshTimer = null;
}

// 在文件顶部添加这个函数
function showNotification(type, title, message) {
    if (typeof window.SweetAlert !== 'undefined') {
        switch(type) {
            case 'success':
                window.SweetAlert.success(message, title);
                break;
            case 'warning':
                window.SweetAlert.warning(message, title);
                break;
            case 'error':
                window.SweetAlert.error(message, title);
                break;
            default:
                window.SweetAlert.info(message, title);
        }
    } else {
        Logger.log(`${type}: ${title} - ${message}`);
        alert(`${title}: ${message}`);
    }
}

// 新增：获取当前时间字符串的函数
function getCurrentTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 检查是否已存在 SweetAlert 对象，如果存在则不重新定义
if (typeof window.SweetAlert === 'undefined') {
    // SweetAlert包装器
    window.SweetAlert = {
        success: function(message, title = '成功') {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: title,
                    text: message,
                    confirmButtonText: '确定'
                });
            } else {
                alert(`${title}: ${message}`);
            }
        },

        warning: function(message, title = '警告') {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'warning',
                    title: title,
                    text: message,
                    confirmButtonText: '确定'
                });
            } else {
                alert(`${title}: ${message}`);
            }
        },

        error: function(message, title = '错误') {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: title,
                    text: message,
                    confirmButtonText: '确定'
                });
            } else {
                alert(`${title}: ${message}`);
            }
        },

        info: function(message, title = '提示') {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'info',
                    title: title,
                    text: message,
                    confirmButtonText: '确定'
                });
            } else {
                alert(`${title}: ${message}`);
            }
        },

        confirm: function(message, title = '确认') {
            if (typeof Swal !== 'undefined') {
                return Swal.fire({
                    icon: 'question',
                    title: title,
                    text: message,
                    showCancelButton: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消'
                }).then(result => {
                    return result.isConfirmed;
                });
            } else {
                return Promise.resolve(confirm(`${title}: ${message}`));
            }
        },

        custom: function(options) {
            if (typeof Swal !== 'undefined') {
                return Swal.fire(options);
            } else {
                alert(options.title);
                return Promise.resolve(true);
            }
        }
    };
}

// 确保脚本不会被截断
document.addEventListener('DOMContentLoaded', function() {
    Logger.log('SelfInspection.js loaded successfully');
});

// 将 initSelfInspectionPage 函数绑定到 window 对象
window.initSelfInspectionPage = initSelfInspectionPage;

// 定义常量
// 使用window对象来避免重复声明
if (typeof window.STAGES === 'undefined') {
    window.STAGES = {
        ASSEMBLY: 'assembly',
        TEST: 'test',
        PACKAGING: 'packaging'
    };
}

if (typeof window.ROLES === 'undefined') {
    window.ROLES = {
        FIRST: 'first',
        SELF: 'self',
        IPQC: 'ipqc'
    };
}

// 不再创建本地变量引用，直接使用window对象上的变量
// 这样可以避免重复声明错误

// 动态数据存储 - 使用window对象来避免重复声明
if (typeof window.inspectionData === 'undefined') {
    window.inspectionData = {
        // 产品类型列表
        productTypes: [],
        // 当前选中的产品类型ID
        currentProductTypeId: null,
        // 当前工单ID
        currentWorkOrderId: null,
        // 当前产品列表
        products: [],
        // 当前选中的产品ID列表（用于检验）
        selectedProductIds: [],
        // 当前页面状态
        pageState: {
            // 检验项目数据，按阶段分组
            stages: {
                [window.STAGES.ASSEMBLY]: {title: "组装前阶段", description: "完成组装前的检测后进入测试前阶段", items: []},
                [window.STAGES.TEST]: {title: "测试前阶段", description: "完成测试前的检测后进入包装前阶段", items: []},
                [window.STAGES.PACKAGING]: {title: "包装前阶段", description: "完成包装前的检测后整个检验流程结束", items: []}
            },
            // 检验结果
            inspectionResults: {
                [window.STAGES.ASSEMBLY]: { [window.ROLES.FIRST]: [], [window.ROLES.SELF]: [], [window.ROLES.IPQC]: [] },
                [window.STAGES.TEST]: { [window.ROLES.FIRST]: [], [window.ROLES.SELF]: [], [window.ROLES.IPQC]: [] },
                [window.STAGES.PACKAGING]: { [window.ROLES.FIRST]: [], [window.ROLES.SELF]: [], [window.ROLES.IPQC]: [] }
            },
            // 提交状态
            inspectionSubmissions: {
                [window.STAGES.ASSEMBLY]: {
                    [window.ROLES.FIRST]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                    [window.ROLES.SELF]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                    [window.ROLES.IPQC]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false }
                },
                [window.STAGES.TEST]: {
                    [window.ROLES.FIRST]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                    [window.ROLES.SELF]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                    [window.ROLES.IPQC]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false }
                },
                [window.STAGES.PACKAGING]: {
                    [window.ROLES.FIRST]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                    [window.ROLES.SELF]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                    [window.ROLES.IPQC]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false }
                }
            },
            // 附件
            attachments: {
                [window.STAGES.ASSEMBLY]: { [window.ROLES.FIRST]: [], [window.ROLES.SELF]: [], [window.ROLES.IPQC]: [] },
                [window.STAGES.TEST]: { [window.ROLES.FIRST]: [], [window.ROLES.SELF]: [], [window.ROLES.IPQC]: [] },
                [window.STAGES.PACKAGING]: { [window.ROLES.FIRST]: [], [window.ROLES.SELF]: [], [window.ROLES.IPQC]: [] }
            }
        }
    };
}

// 为了保持代码兼容性，创建一个对window.inspectionData的引用
const inspectionData = window.inspectionData;

// 使用ScanSN.js中定义的currentScannedProducts变量

// 删除不需要的映射代码

// 当前确认操作的阶段和类型 - 使用window对象来避免重复声明
if (typeof window.currentConfirmStage === 'undefined') window.currentConfirmStage = "";
if (typeof window.currentConfirmType === 'undefined') window.currentConfirmType = "";

// 为了保持代码兼容性，创建对window对象属性的引用
const currentConfirmStage = window.currentConfirmStage;
const currentConfirmType = window.currentConfirmType;

// 当前上传附件的阶段和类型 - 使用window对象来避免重复声明
if (typeof window.currentUploadStage === 'undefined') window.currentUploadStage = "";
if (typeof window.currentUploadType === 'undefined') window.currentUploadType = "";

// 为了保持代码兼容性，创建对window对象属性的引用
const currentUploadStage = window.currentUploadStage;
const currentUploadType = window.currentUploadType;

// 初始化自检过程页面
function initSelfInspectionPage() {
    Logger.log('初始化自检过程页面');

    // 创建页面结构
    const container = document.getElementById('self-inspection-content');
    if (!container) {
        Logger.error('自检过程容器不存在!');
        return;
    }

    // 添加文件预览模态框样式
    addFilePreviewStyles();

    container.innerHTML = createSelfInspectionHTML();

    // 初始化页面逻辑
    initPage();

    // 检查并初始化数据库，然后加载产品类型
    initializeDatabase();
}

// 添加文件预览模态框样式
function addFilePreviewStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* 模态框整体样式优化 */
        .self-inspection-swal-popup {
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 0;
        }

        .self-inspection-swal-title {
            font-size: 22px;
            font-weight: 600;
            color: #2c3e50;
            padding: 20px 20px 0;
        }

        .self-inspection-swal-html-container {
            padding: 0 20px;
        }

        .self-inspection-swal-actions {
            padding: 15px;
            border-top: 1px solid #f1f1f1;
            background-color: #fafafa;
            border-radius: 0 0 12px 12px;
        }

        .self-inspection-swal-confirm-button {
            background-color: #4CAF50 !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            padding: 10px 24px !important;
            box-shadow: 0 2px 5px rgba(76, 175, 80, 0.2) !important;
        }

        .self-inspection-swal-cancel-button {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            padding: 10px 24px !important;
        }

        /* 上传区域样式 */
        .self-inspection__upload-section {
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
        }

        .self-inspection__upload-title {
            font-weight: 600;
            margin: 0;
            padding: 12px 15px;
            color: #2c3e50;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
        }

        .self-inspection__upload-title::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 16px;
            background-color: #4CAF50;
            margin-right: 10px;
            border-radius: 2px;
        }

        .self-inspection__file-list {
            max-height: 180px;
            overflow-y: auto;
            padding: 10px 15px;
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 0 0 8px 8px;
        }

        .self-inspection__file-list p {
            color: #6c757d;
            text-align: center;
            padding: 15px 0;
            margin: 0;
            font-style: italic;
        }

        /* 上传信息区域 */
        .self-inspection__upload-info {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
            color: #6c757d;
            border-left: 4px solid #4CAF50;
        }

        .self-inspection__upload-info p {
            margin: 8px 0;
        }

        .self-inspection__upload-warning {
            color: #dc3545;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .self-inspection__upload-warning::before {
            content: '⚠️';
            margin-right: 8px;
        }

        .self-inspection__upload-note {
            color: #fd7e14;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .self-inspection__upload-note::before {
            content: 'ℹ️';
            margin-right: 8px;
        }

        /* 文件项样式 */
        .self-inspection__attachment-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 8px;
            background-color: #f8f9fa;
            transition: all 0.2s ease;
            border-left: 3px solid #4CAF50;
        }

        .self-inspection__attachment-item:hover {
            background-color: #e9ecef;
        }

        .self-inspection__attachment-item:last-child {
            margin-bottom: 0;
        }

        .self-inspection__attachment-icon {
            margin-right: 12px;
            color: #4CAF50;
            font-size: 22px;
            width: 30px;
            text-align: center;
        }

        .self-inspection__attachment-info {
            flex: 1;
            overflow: hidden;
        }

        .self-inspection__attachment-name {
            font-weight: 500;
            margin-bottom: 4px;
            color: #2c3e50;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
        }

        .self-inspection__attachment-name:hover {
            color: #4CAF50;
            text-decoration: underline;
        }

        .self-inspection__attachment-size {
            font-size: 12px;
            color: #6c757d;
        }

        .self-inspection__attachment-delete {
            cursor: pointer;
            color: #dc3545;
            font-size: 18px;
            font-weight: bold;
            padding: 0 5px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .self-inspection__attachment-delete:hover {
            background-color: #dc3545;
            color: #fff;
        }

        /* 上传按钮样式 */
        .self-inspection__upload-btn {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-align: center;
            transition: all 0.2s ease;
            margin-top: 20px;
            box-shadow: 0 2px 5px rgba(76, 175, 80, 0.2);
        }

        /* 小型上传按钮样式 */
        .self-inspection__upload-btn--small {
            width: auto;
            display: inline-block;
            padding: 8px 16px;
            font-size: 14px;
            background-color: #2196F3;
            box-shadow: 0 2px 5px rgba(33, 150, 243, 0.2);
        }

        .self-inspection__upload-btn--small:hover {
            background-color: #1976D2;
            box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
        }

        .self-inspection__upload-btn:hover {
            background-color: #43a047;
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
        }

        .self-inspection__file-upload-input {
            display: none;
        }

        /* 导出按钮样式 */
        .self-inspection__export-section {
            margin: 15px 0;
            padding: 10px 0;
            border-top: 1px solid #e9ecef;
        }

        .self-inspection__export-btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
            text-align: center;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(40, 167, 69, 0.2);
        }

        .self-inspection__export-btn:hover {
            background-color: #218838;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
            transform: translateY(-1px);
        }

        .self-inspection__export-btn i {
            margin-right: 6px;
        }

        .self-inspection__export-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .self-inspection__export-btn:disabled:hover {
            background-color: #6c757d;
            transform: none;
            box-shadow: 0 2px 5px rgba(108, 117, 125, 0.2);
        }

        /* 预览模态框样式 */
        .file-preview-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 10000;
            justify-content: center;
            align-items: center;
        }

        .file-preview-content {
            background-color: white;
            border-radius: 8px;
            width: 80%;
            max-width: 900px;
            max-height: 80%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .file-preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .file-preview-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 18px;
            margin: 0;
        }

        .file-preview-close {
            cursor: pointer;
            font-size: 24px;
            color: #6c757d;
        }

        .file-preview-body {
            padding: 20px;
            overflow: auto;
            flex: 1;
        }

        .file-preview-image {
            max-width: 100%;
            max-height: 70vh;
            display: block;
            margin: 0 auto;
        }

        .file-preview-pdf {
            width: 100%;
            height: 70vh;
            border: none;
        }

        .file-preview-error {
            text-align: center;
            padding: 30px;
            color: #dc3545;
        }

        .file-preview-loading {
            text-align: center;
            padding: 30px;
            color: #4CAF50;
            font-weight: 500;
            font-size: 18px;
        }

        .file-preview-loading p {
            margin: 0;
            position: relative;
            display: inline-block;
        }

        .file-preview-loading p:after {
            content: '';
            animation: loading-dots 1.5s infinite;
        }

        @keyframes loading-dots {
            0% { content: ''; }
            25% { content: '.'; }
            50% { content: '..'; }
            75% { content: '...'; }
            100% { content: ''; }
        }

        /* 等待首检提示样式 */
        .self-inspection__wait-notice {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30px 20px;
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            color: #92400e;
            font-weight: 500;
            margin: 20px 0;
        }

        .self-inspection__wait-notice i {
            font-size: 18px;
            margin-right: 8px;
            color: #f59e0b;
        }

        /* 禁用状态的按钮样式增强 */
        .self-inspection__btn--disabled {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
            background-color: #9ca3af !important;
            border-color: #9ca3af !important;
        }

        .self-inspection__btn--disabled:hover {
            background-color: #9ca3af !important;
            border-color: #9ca3af !important;
            transform: none !important;
            box-shadow: none !important;
        }
    `;
    document.head.appendChild(style);
}

// 创建自检过程HTML结构
function createSelfInspectionHTML() {
    return `
    <div class="self-inspection">
        <!-- 标题区域 -->
        <div class="self-inspection__header">
            <h1 class="self-inspection__title">生产工序检验</h1>
            <div class="self-inspection__order-info">
                <div id="orderNoGroup" class="self-inspection__input-group">
                    <label for="orderNo" class="self-inspection__label">工单号 <span class="self-inspection__required">*</span></label>
                    <div style="display: flex;">
                        <input type="text" id="orderNo" class="self-inspection__input" placeholder="请输入工单号">
                        <button id="refreshWorkOrder" class="self-inspection__refresh-button" title="刷新工单状态">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="self-inspection__error-message">工单号不能为空</div>
                </div>
                <div id="serialNoGroup" class="self-inspection__input-group">
                    <label for="serialNo" class="self-inspection__label">产品型号</label>
                    <input type="text" id="serialNo" class="self-inspection__input" placeholder="请输入产品型号">
                </div>
                <div id="productTypeGroup" class="self-inspection__input-group">
                    <label for="productType" class="self-inspection__label">产品类型 <span class="self-inspection__required">*</span></label>
                    <select id="productType" class="self-inspection__input">
                        <option value="">请选择产品类型</option>
                    </select>
                </div>
                <div id="reworkGroup" class="self-inspection__input-group">
                    <label for="isReworkWorkOrderSelect" class="self-inspection__label">返工工单 <span class="self-inspection__required">*</span></label>
                    <select id="isReworkWorkOrderSelect" class="self-inspection__input">
                        <option value="false" selected>否</option>
                        <option value="true">是</option>
                    </select>
                </div>
                <div id="skipInspectionGroup" class="self-inspection__input-group">
                    <label class="self-inspection__label">跳过检验</label>
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <label class="self-inspection__switch">
                            <input type="checkbox" id="skipInspectionSwitch" onchange="toggleSkipInspection()">
                            <span class="self-inspection__slider"></span>
                        </label>
                        <button id="confirmSkipButton" class="self-inspection__btn self-inspection__btn--danger" 
                                onclick="confirmSkipInspection()" style="display: none;">
                            确认跳过
                        </button>
                    </div>
                    <div class="self-inspection__help-text" style="margin-top: 5px; font-size: 12px; color: #666;">
                        危险操作：开启后可直接完成所有检验阶段，请谨慎使用
                    </div>
                </div>
            </div>
            <div class="self-inspection__progress">
                <div class="self-inspection__progress-label">
                    <span>总体进度</span>
                    <span id="progressPercentage">0%</span>
                </div>
                <div class="self-inspection__progress-bar">
                    <div id="progressInner" class="self-inspection__progress-inner" style="width: 0%"></div>
                </div>
            </div>

            <!-- 工单状态概览区域 -->
            <div id="workOrderStatusOverview" class="self-inspection__status-overview" style="display: none;">
                <div class="self-inspection__status-title">工单检验状态</div>
                <div class="self-inspection__status-table">
                    <div class="self-inspection__status-header">
                        <div class="self-inspection__status-cell">阶段/角色</div>
                        <div class="self-inspection__status-cell">首检</div>
                        <div class="self-inspection__status-cell">自检</div>
                        <div class="self-inspection__status-cell">IPQC</div>
                    </div>
                    <!-- 状态行将通过JS动态生成 -->
                </div>
            </div>
        </div>

        <!-- 时间线 -->
        <div class="self-inspection__timeline">
            <div class="self-inspection__timeline-line"></div>
            ${createStageHTML(window.STAGES.ASSEMBLY, '1', false)}
            ${createStageHTML(window.STAGES.TEST, '2', true)}
            ${createStageHTML(window.STAGES.PACKAGING, '3', true)}
        </div>

        <!-- 完成消息 -->
        <div id="completionMessage" class="self-inspection__completion-message">
            <div class="self-inspection__completion-icon">✓</div>
            <h2 class="self-inspection__completion-title">检验流程已完成</h2>
            <p class="self-inspection__completion-info">所有阶段的检测项目均已通过，产品可以交付。</p>
            <div class="self-inspection__completion-details">
                <p>工单号: <span id="completionOrderNo"></span></p>
                <p>SN号: <span id="completionSerialNo"></span></p>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="self-inspection__notification">
        <div id="notificationIcon" class="self-inspection__notification-icon warning">⚠️</div>
        <div class="self-inspection__notification-content">
            <div id="notificationTitle" class="self-inspection__notification-title">提示</div>
            <div id="notificationMessage" class="self-inspection__notification-message">这是一条提示消息</div>
        </div>
    </div>`;
}

// 创建阶段HTML
function createStageHTML(stage, number, locked) {
    const stageData = inspectionData.pageState.stages[stage] ||
                    {title: getDefaultStageTitle(stage), description: getDefaultStageDescription(stage)};

    return `
    <div id="${stage}-stage" class="self-inspection__stage">
        <div class="self-inspection__stage-node${locked ? ' self-inspection__stage-node--locked' : ''}">${number}</div>
        <div class="self-inspection__stage-card">
            <div class="self-inspection__stage-header" onclick="toggleStage('${stage}')">
                <div>
                    <div class="self-inspection__stage-title">${stageData.title}</div>
                    <div class="self-inspection__stage-description">${stageData.description}</div>
                </div>
                <div class="self-inspection__stage-status">
                    <div id="${stage}-progress" class="self-inspection__stage-progress">0%</div>
                    <div id="${stage}-toggle" class="self-inspection__stage-toggle${locked ? ' self-inspection__stage-toggle--collapsed' : ''}">展开</div>
                </div>
            </div>
            <div id="${stage}-content" class="self-inspection__stage-content${locked ? '' : ' self-inspection__stage-content--expanded'}">
                <div class="self-inspection__inspection-tabs">
                    <div class="self-inspection__inspection-tab self-inspection__inspection-tab--active" onclick="switchTab('${stage}', window.ROLES.FIRST)">首检</div>
                    <div class="self-inspection__inspection-tab" onclick="switchTab('${stage}', window.ROLES.SELF)">自检</div>
                    <div class="self-inspection__inspection-tab" onclick="switchTab('${stage}', window.ROLES.IPQC)">IPQC</div>
                </div>
                ${createInspectionContent(stage, window.ROLES.FIRST)}
                ${createInspectionContent(stage, window.ROLES.SELF)}
                ${createInspectionContent(stage, window.ROLES.IPQC)}
            </div>
        </div>
    </div>`;
}

// 获取默认阶段标题
function getDefaultStageTitle(stage) {
    switch(stage) {
        case window.STAGES.ASSEMBLY: return "组装前阶段";
        case window.STAGES.TEST: return "测试前阶段";
        case window.STAGES.PACKAGING: return "包装前阶段";
        default: return "未知阶段";
    }
}

// 获取默认阶段描述
function getDefaultStageDescription(stage) {
    switch(stage) {
        case window.STAGES.ASSEMBLY: return "完成组装前的检测后进入测试前阶段";
        case window.STAGES.TEST: return "完成测试前的检测后进入包装前阶段";
        case window.STAGES.PACKAGING: return "完成包装前的检测后整个检验流程结束";
        default: return "";
    }
}

// 创建检验内容HTML
function createInspectionContent(stage, role) {
    let submitButtonText = '';
    if (role === window.ROLES.FIRST) {
        submitButtonText = '提交首检';
    } else if (role === window.ROLES.SELF) {
        submitButtonText = '提交自检';
    } else { // ipqc
        submitButtonText = '提交IPQC';
    }

    return `
    <div id="${stage}-${role}" class="self-inspection__inspection-content${role === window.ROLES.FIRST ? ' self-inspection__inspection-content--active' : ''}">
        <div id="${stage}-${role}-operator" class="self-inspection__operator-info">
            <div class="self-inspection__operator-header">
                <span class="self-inspection__operator-label">操作人员:</span>
            </div>
            <div class="self-inspection__operator-input">
                <input type="text" class="self-inspection__input" readonly>
            </div>
        </div>

        <div class="self-inspection__select-all">
            <div class="self-inspection__checkbox-container">
                <input type="checkbox" id="${stage}-${role}-selectAll" class="self-inspection__checkbox" onchange="toggleInspectionSelectAll('${stage}', '${role}')">
                <label for="${stage}-${role}-selectAll" class="self-inspection__checkbox-label">全选</label>
            </div>
        </div>

        <div id="${stage}-${role}-items" class="self-inspection__inspection-items"></div>

        <div class="self-inspection__attachments">
            <div class="self-inspection__attachments-title">已上传文件</div>
            <div id="${stage}-${role}-files" class="self-inspection__attachment-list"></div>
            <button class="self-inspection__upload-btn self-inspection__upload-btn--small" onclick="showUploadDialog('${stage}', '${role}')">上传附件</button>
        </div>

        ${role === window.ROLES.SELF ? `
        <div class="self-inspection__export-section">
            <button class="self-inspection__export-btn" onclick="exportSNList('${stage}', '${role}')" title="导出当前阶段的SN号列表">
                <i class="fas fa-download"></i> 导出SN号列表
            </button>
        </div>
        ` : ''}

        <div class="self-inspection__action-bar">
            <button class="self-inspection__btn self-inspection__btn--reset" onclick="resetInspection('${stage}', '${role}')">重置</button>
            <button class="self-inspection__btn self-inspection__btn--submit" onclick="submitInspection('${stage}', '${role}')">${submitButtonText}</button>
        </div>
    </div>`;
}

// 新增函数：获取当前登录用户并设置到操作人员输入框
function fetchAndSetCurrentUser() {
    Logger.log('Fetching current user info...');
    fetch('/api/user/info') // 调用后端接口
        .then(response => {
            if (!response.ok) {
                // 如果用户未登录或token过期，接口会返回401或其他错误
                // 在这种情况下，我们不抛出错误，而是静默失败，让用户手动输入
                Logger.warn('Failed to get user info, likely not logged in or session expired. Status:', response.status);
                return null; // 返回 null 表示无法获取用户信息
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success && data.username) {
                Logger.log('Current user:', data.username);
                const username = data.username;

                // 找到所有阶段和角色的操作人员输入框
                const operatorInputs = document.querySelectorAll('.self-inspection__operator-input input');

                operatorInputs.forEach(input => {
                    // 检查该部分是否已提交，如果未提交，则填充用户名
                    const operatorInfoDiv = input.closest('.self-inspection__operator-info');
                    // 只有在父级 div 不包含 'submitted' 类时才填充
                    if (operatorInfoDiv && !operatorInfoDiv.classList.contains('self-inspection__operator-info--submitted')) {
                        // 直接设置用户名，因为输入框是只读的
                        input.value = username; // 设置输入框的值
                    }
                });
            } else if (data) {
                // 接口返回成功但没有用户名，或明确返回失败
                Logger.warn('Could not get username from API:', data.message || 'No username provided or API error');
            }
            // 对于获取用户信息失败(response.ok 为 false) 的情况，我们之前返回了 null，这里不做任何操作
        })
        .catch(error => {
            // 网络层面的错误
            Logger.error('Network error fetching user info:', error);
            // 可以选择性地通知用户网络问题
            // showNotification('error', '网络错误', '无法连接服务器获取用户信息');
        });
}

// 初始化页面
function initPage() {
    Logger.log('初始化页面');

    // 添加SN号扫描样式
    if (typeof addScanSNStyles === 'function') {
        addScanSNStyles();
    } else {
        Logger.warn('addScanSNStyles函数未定义，无法添加SN号扫描样式');
    }

    // 确保所有阶段和角色的DOM元素都已正确创建
    ensureAllDOMElementsExist();

    // 获取当前登录用户并填充到操作人员输入框
    getCurrentUserAndFillOperatorInputs();

    // 工单号输入框事件
    const orderNoInput = document.getElementById('orderNo');
    if (orderNoInput) {
        orderNoInput.addEventListener('focus', function() {
            document.getElementById('orderNoGroup').classList.remove('self-inspection__input-group--error');
        });

        orderNoInput.addEventListener('blur', function() {
            const orderNo = orderNoInput.value.trim();
            if (orderNo) {
                loadWorkOrderData(orderNo);
            }
        });
    }

    // 刷新按钮事件
    const refreshButton = document.getElementById('refreshWorkOrder');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            const orderNo = document.getElementById('orderNo').value.trim();
            if (orderNo) {
                loadWorkOrderData(orderNo);
                showNotification('info', '刷新中', '正在刷新工单状态...');
            } else {
                showNotification('warning', '无法刷新', '请先输入工单号');
            }
        });
    }

    // 产品类型选择事件
    const productTypeSelect = document.getElementById('productType');
    if (productTypeSelect) {
        productTypeSelect.addEventListener('change', function() {
            const selectedTypeId = productTypeSelect.value;
            if (selectedTypeId) {
                inspectionData.currentProductTypeId = selectedTypeId;
                loadInspectionItems(selectedTypeId);

                // 检查是否有工单号
                const orderNo = document.getElementById('orderNo').value.trim();
                if (orderNo) {
                    loadWorkOrderData(orderNo, true);
                }
            }
        });
    }

    // 添加产品状态实时更新功能
    setupProductStatusRefresh();
}

// 获取当前登录用户并填充到操作人员输入框
function getCurrentUserAndFillOperatorInputs() {
    Logger.log('获取当前登录用户并填充到操作人员输入框');

    // 尝试从多个可能的API端点获取当前用户信息
    fetch('/api/user/info')
        .then(response => {
            if (!response.ok) {
                // 如果第一个API失败，尝试其他API
                return fetch('/api/work-order/current-user');
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success && data.username) {
                // 保存当前用户名到全局变量，以便在其他地方使用
                window.currentLoggedInUser = data.username;
                fillOperatorInputs(data.username);
                return;
            }

            // 如果第一个API失败，尝试其他API
            return fetch('/api/work-order/current-user');
        })
        .then(response => {
            if (response && response.json) {
                return response.json();
            }
            return null;
        })
        .then(data => {
            if (data && data.success && data.username && !window.currentLoggedInUser) {
                window.currentLoggedInUser = data.username;
                fillOperatorInputs(data.username);
            }
        })
        .catch(error => {
            Logger.warn('获取当前用户信息失败:', error);
        });
}

// 填充所有操作人员输入框
function fillOperatorInputs(username) {
    if (!username) return;

    Logger.log('填充操作人员输入框:', username);

    // 找到所有阶段和角色的操作人员输入框
    for (const stage of [window.STAGES.ASSEMBLY, window.STAGES.TEST, window.STAGES.PACKAGING]) {
        for (const role of [window.ROLES.FIRST, window.ROLES.SELF, window.ROLES.IPQC]) {
            // 检查该阶段和角色是否已提交
            const isSubmitted = inspectionData.pageState.inspectionSubmissions[stage][role].isSubmitted;

            // 如果未提交，填充用户名
            if (!isSubmitted) {
                const operatorInput = document.querySelector(`#${stage}-${role}-operator .self-inspection__operator-input input`);
                if (operatorInput) {
                    operatorInput.value = username;
                }
            }
        }
    }

}

// 设置产品状态实时更新
function setupProductStatusRefresh() {
    // 每10秒刷新一次产品状态
    const refreshInterval = 10000; // 10秒

    // 清除可能存在的旧定时器
    if (window.productStatusRefreshTimer !== null) {
        clearInterval(window.productStatusRefreshTimer);
    }

    // 设置新的定时器
    window.productStatusRefreshTimer = setInterval(() => {
        const orderNo = document.getElementById('orderNo').value.trim();
        if (orderNo && inspectionData.currentWorkOrderId) {
            // 获取当前活动的阶段和角色
            const activeStage = getCurrentActiveStage();
            const activeRole = getCurrentActiveRole();

            if (activeStage && activeRole) {
                // 获取后端的阶段和角色名称
                const apiStage = activeStage;
                const apiRole = activeRole;

                // 查询产品状态
                refreshProductStatus(inspectionData.currentWorkOrderId, apiStage, apiRole);
            }
        }
    }, refreshInterval);
}

// 获取当前活动的阶段
function getCurrentActiveStage() {
    // 使用后端阶段名称
    const stages = [window.STAGES.ASSEMBLY, window.STAGES.TEST, window.STAGES.PACKAGING];
    for (const stage of stages) {
        const content = document.getElementById(`${stage}-content`);
        if (content && content.classList.contains('self-inspection__stage-content--expanded')) {
            return stage;
        }
    }
    return null;
}

// 获取当前活动的角色
function getCurrentActiveRole() {
    const activeStage = getCurrentActiveStage();
    if (!activeStage) return null;

    // 使用后端角色名称
    const roles = [window.ROLES.FIRST, window.ROLES.SELF, window.ROLES.IPQC];
    for (const role of roles) {
        const content = document.getElementById(`${activeStage}-${role}`);
        if (content && content.classList.contains('self-inspection__inspection-content--active')) {
            return role;
        }
    }
    return null;
}

// 刷新产品状态
function refreshProductStatus(workOrderId, stage, role) {
    // 确保工单ID被正确编码
    const encodedWorkOrderId = encodeURIComponent(workOrderId);
    fetch(`/api/quality-inspection/work-order/${encodedWorkOrderId}/inspection-status?stage=${stage}&role=${role}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新产品状态显示
                updateProductStatusDisplay(data.data, stage, role);

                // 只有最终提交才禁用提交按钮
                if (data.data.is_final_submitted) {
                    disableSubmitButton(stage, role);
                }

                // 如果是部分提交，更新UI状态但不禁用按钮
                if (data.data.has_partial_submission && !data.data.is_final_submitted) {
                    Logger.log('检测到部分提交状态，可继续添加产品');
                    // 可以在这里添加额外的UI提示或处理
                }
            }
        })
        .catch(error => {
            Logger.error('Error refreshing product status:', error);
        });
}

// 更新产品状态显示
function updateProductStatusDisplay(statusData, apiStage, apiRole) {
    // 更新进度信息
    const stage = apiStage;
    const role = apiRole;

    // 更新进度条
    const progressElement = document.getElementById(`${stage}-progress`);
    if (progressElement) {
        const percentage = statusData.total_count > 0
            ? Math.round((statusData.completed_count / statusData.total_count) * 100)
            : 0;
        progressElement.textContent = `${percentage}%`;
    }

    // 添加或更新产品状态列表
    const contentElement = document.getElementById(`${stage}-${role}`);
    if (contentElement) {
        // 查找或创建产品状态容器
        let statusContainer = document.getElementById(`${stage}-${role}-product-status`);
        if (!statusContainer) {
            statusContainer = document.createElement('div');
            statusContainer.id = `${stage}-${role}-product-status`;
            statusContainer.className = 'self-inspection__product-status';

            // 插入到操作人员信息之后
            const operatorInfo = contentElement.querySelector('.self-inspection__operator-info');
            if (operatorInfo) {
                operatorInfo.insertAdjacentElement('afterend', statusContainer);
            } else {
                contentElement.prepend(statusContainer);
            }
        }

        // 更新产品状态内容
        statusContainer.innerHTML = `
            <div class="self-inspection__product-status-header">
                <h3>产品检验状态</h3>
                <div class="self-inspection__product-status-summary">
                    <span>总数: ${statusData.total_count}</span>
                    <span>已完成: ${statusData.completed_count}</span>
                    <span>未完成: ${statusData.remaining_count}</span>
                </div>
            </div>
            <div class="self-inspection__workorder-count">
                <i class="fas fa-clipboard-list"></i>
                <span>工单数量: ${(inspectionData.workOrderDetails && inspectionData.workOrderDetails.productionQuantity) ? 
                inspectionData.workOrderDetails.productionQuantity : 
                (statusData.total_count > 0 ? Math.ceil(statusData.total_count / 10) : 0)}</span>
            </div>
            <div class="self-inspection__product-status-list">
                ${statusData.products.map(product => `
                    <div class="self-inspection__product-item ${product.is_completed ? 'self-inspection__product-item--completed' : ''}">
                        <div class="self-inspection__product-sn">${product.serial_no}</div>
                        <div class="self-inspection__product-status">
                            ${product.is_completed
                                ? `<span class="self-inspection__status-tag self-inspection__status-tag--completed">
                                     <i class="fas fa-check" style="margin-right:4px;font-size:10px;"></i>已完成
                                   </span>
                                   <span class="self-inspection__inspector">${product.inspector || ''}</span>`
                                : `<span class="self-inspection__status-tag self-inspection__status-tag--pending">未完成</span>
                                   <span class="self-inspection__inspector" style="visibility:hidden;">占位</span>`}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        // 显示提交状态
        if (statusData.is_final_submitted) {
            // 最终提交状态
            statusContainer.innerHTML += `
                <div class="self-inspection__final-submitted-notice">
                    此阶段的检验已完成最终提交，无法再添加新产品
                </div>
            `;
        } else if (statusData.has_partial_submission) {
            // 部分提交状态
            statusContainer.innerHTML += `
                <div class="self-inspection__partial-submitted-notice">
                    已部分提交检验结果，您可以继续扫描添加更多产品
                </div>
            `;
        }
        
        // 更新导出按钮状态（仅自检阶段）
        if (role === window.ROLES.SELF) {
            updateExportButtonState(stage, role, statusData.products.length > 0);
        }
    }
}

// 禁用提交按钮
function disableSubmitButton(stage, role) {
    const actionBar = document.querySelector(`#${stage}-${role} .self-inspection__action-bar`);
    if (actionBar) {
        const submitButton = actionBar.querySelector('.self-inspection__btn--submit');
        if (submitButton) {
            submitButton.classList.add('self-inspection__btn--disabled');
            submitButton.disabled = true;
            submitButton.textContent = '已提交';
        }
    }
}

// 加载产品类型
function loadProductTypes() {
    Logger.log('开始加载产品类型...');

    // 显示加载中
    showLoading();

    // 调用API获取产品类型
    fetch('/api/quality-inspection/product-types')
        .then(response => response.json())
        .then(data => {
            hideLoading();

            if (data.success) {
                Logger.log('产品类型加载成功:', data.data);

                // 如果数据库中没有产品类型，进行初始化
                if (!data.data || data.data.length === 0) {
                    Logger.warn('没有发现产品类型，重新初始化...');
                    showNotification('warning', '数据缺失', '没有找到产品类型，系统将尝试初始化数据');
                    initProductTypes();
                    return;
                }

                // 保存产品类型数据
                inspectionData.productTypes = data.data;

                // 填充产品类型下拉框
                const productTypeSelect = document.getElementById('productType');
                if (productTypeSelect) {
                    // 清空选项
                    productTypeSelect.innerHTML = '<option value="">请选择产品类型</option>';

                    // 添加新选项
                    data.data.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type.id;
                        option.textContent = type.type_name;
                        productTypeSelect.appendChild(option);
                    });

                    // 默认选择五代PLC
                    const defaultTypeName = '五代PLC';
                    let found = false;

                    // 记录所有可用选项，帮助调试
                    Logger.log("初始化时可用的产品类型选项:");
                    for (let i = 0; i < productTypeSelect.options.length; i++) {
                        if (productTypeSelect.options[i].value) {
                            Logger.log(`- 选项ID=${productTypeSelect.options[i].value}, 名称=${productTypeSelect.options[i].textContent}`);
                        }
                    }

                    // 直接查找名称为"五代PLC"的选项
                    for (let i = 0; i < productTypeSelect.options.length; i++) {
                        const option = productTypeSelect.options[i];
                        if (option.textContent === defaultTypeName) {
                            option.selected = true;
                            inspectionData.currentProductTypeId = option.value;
                            Logger.log(`默认选择产品类型: ${option.textContent} (选项ID=${option.value})`);
                            found = true;
                            break;
                        }
                    }

                    // 如果没有找到"五代PLC"，尝试查找ID为2的选项
                    if (!found) {
                        for (let i = 0; i < productTypeSelect.options.length; i++) {
                            const option = productTypeSelect.options[i];
                            if (option.value == 2) {
                                option.selected = true;
                                inspectionData.currentProductTypeId = option.value;
                                Logger.log(`默认选择产品类型ID=2: ${option.textContent}`);
                                found = true;
                                break;
                            }
                        }
                    }

                    // 如果仍然没有找到，使用第一个有效选项
                    if (!found && productTypeSelect.options.length > 1) {
                        // 跳过第一个空选项（"请选择产品类型"）
                        const option = productTypeSelect.options[1];
                        option.selected = true;
                        inspectionData.currentProductTypeId = option.value;
                        Logger.log(`默认选择第一个产品类型: ${option.textContent} (选项ID=${option.value})`);
                    }

                    // 加载检验项目
                    if (inspectionData.currentProductTypeId) {
                        loadInspectionItems(inspectionData.currentProductTypeId);
                        Logger.log(`加载产品类型ID: ${inspectionData.currentProductTypeId}, 选择的文本: ${productTypeSelect.options[productTypeSelect.selectedIndex].textContent}`);
                    }
                } else {
                    Logger.error('无法找到产品类型选择框');
                    showNotification('error', '界面错误', '无法找到产品类型选择框');
                }
            } else {
                Logger.error('加载产品类型失败:', data.message);
                showNotification('error', '加载失败', data.message || '无法加载产品类型');
            }
        })
        .catch(error => {
            hideLoading();
            Logger.error('Error loading product types:', error);
            showNotification('error', '加载失败', '网络错误，请稍后重试');
        });
}

// 加载检验项目
function loadInspectionItems(productTypeId, stage, role) {
    Logger.log('加载检验项目', productTypeId, stage, role);

    if (!productTypeId) return;

    // 显示加载中
    showLoading();

    // 调用API获取检验项目
    fetch(`/api/quality-inspection/inspection-items?product_type_id=${productTypeId}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();

            if (data.success) {
                Logger.log('检验项目加载成功', data.data);

                // 保存检验项目数据
                const stages = data.data;
                for (const apiStage in stages) {
                    if (!inspectionData.pageState.stages[apiStage]) {
                        inspectionData.pageState.stages[apiStage] = {
                            title: getDefaultStageTitle(apiStage),
                            description: getDefaultStageDescription(apiStage),
                            items: []
                        };
                    }

                    inspectionData.pageState.stages[apiStage].items = stages[apiStage];

                    // 初始化检验结果（如果尚未初始化）
                    if (!inspectionData.pageState.inspectionResults[apiStage].first ||
                        inspectionData.pageState.inspectionResults[apiStage].first.length !== stages[apiStage].length) {
                        inspectionData.pageState.inspectionResults[apiStage].first = Array(stages[apiStage].length).fill(false);
                    }

                    if (!inspectionData.pageState.inspectionResults[apiStage].self ||
                        inspectionData.pageState.inspectionResults[apiStage].self.length !== stages[apiStage].length) {
                        inspectionData.pageState.inspectionResults[apiStage].self = Array(stages[apiStage].length).fill(false);
                    }

                    if (!inspectionData.pageState.inspectionResults[apiStage].ipqc ||
                        inspectionData.pageState.inspectionResults[apiStage].ipqc.length !== stages[apiStage].length) {
                        inspectionData.pageState.inspectionResults[apiStage].ipqc = Array(stages[apiStage].length).fill(false);
                    }
                }

                // 渲染检验项目
                renderAllInspectionItems();

                // 更新进度
                updateProgress();

                // 如果指定了阶段和角色，切换到对应的选项卡
                if (stage && role) {
                    Logger.log('切换到指定的选项卡', stage, role);

                    // 将API阶段和角色转换为前端阶段和角色
                    let frontendStage = stage;
                    let frontendRole = role;

                    // 不再需要转换阶段和角色，直接使用后端阶段和角色名称

                    // 确保阶段内容已展开
                    const stageContent = document.getElementById(`${frontendStage}-content`);
                    if (stageContent) {
                        stageContent.classList.add('self-inspection__stage-content--expanded');
                        const toggle = document.getElementById(`${frontendStage}-toggle`);
                        if (toggle) {
                            toggle.classList.remove('self-inspection__stage-toggle--collapsed');
                            toggle.textContent = '展开';
                        }
                    }

                    // 切换到对应的选项卡
                    setTimeout(() => {
                        switchTab(frontendStage, frontendRole);
                    }, 100);
                }
            } else {
                showNotification('error', '加载失败', data.message || '无法加载检验项目');
            }
        })
        .catch(error => {
            hideLoading();
            Logger.error('Error loading inspection items:', error);
            showNotification('error', '加载失败', '网络错误，请稍后重试');
        });
}

// 重置页面状态
function resetPageState() {
    inspectionData.pageState = {
        stages: {
            [window.STAGES.ASSEMBLY]: { title: '组装前阶段', description: '装配前的质量检验', items: [] },
            [window.STAGES.TEST]: { title: '测试前阶段', description: '测试前的质量检验', items: [] },
            [window.STAGES.PACKAGING]: { title: '包装前阶段', description: '包装前的质量检验', items: [] }
        },
        inspectionResults: {
            [window.STAGES.ASSEMBLY]: {
                [window.ROLES.FIRST]: [],
                [window.ROLES.SELF]: [],
                [window.ROLES.IPQC]: []
            },
            [window.STAGES.TEST]: {
                [window.ROLES.FIRST]: [],
                [window.ROLES.SELF]: [],
                [window.ROLES.IPQC]: []
            },
            [window.STAGES.PACKAGING]: {
                [window.ROLES.FIRST]: [],
                [window.ROLES.SELF]: [],
                [window.ROLES.IPQC]: []
            }
        },
        inspectionSubmissions: {
            [window.STAGES.ASSEMBLY]: {
                [window.ROLES.FIRST]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                [window.ROLES.SELF]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                [window.ROLES.IPQC]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false }
            },
            [window.STAGES.TEST]: {
                [window.ROLES.FIRST]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                [window.ROLES.SELF]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                [window.ROLES.IPQC]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false }
            },
            [window.STAGES.PACKAGING]: {
                [window.ROLES.FIRST]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                [window.ROLES.SELF]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false },
                [window.ROLES.IPQC]: { operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false }
            }
        },
        attachments: {
            [window.STAGES.ASSEMBLY]: {
                [window.ROLES.FIRST]: [],
                [window.ROLES.SELF]: [],
                [window.ROLES.IPQC]: []
            },
            [window.STAGES.TEST]: {
                [window.ROLES.FIRST]: [],
                [window.ROLES.SELF]: [],
                [window.ROLES.IPQC]: []
            },
            [window.STAGES.PACKAGING]: {
                [window.ROLES.FIRST]: [],
                [window.ROLES.SELF]: [],
                [window.ROLES.IPQC]: []
            }
        }
    };
    Logger.log('页面状态已重置，附件结构已初始化');
}

// 加载工单数据
function loadWorkOrderData(orderNo, isCreating = false) {
    if (!orderNo) return;

    // 显示加载中
    showLoading();

    // 首先从工单管理系统获取工单信息
    fetch(`/api/sn-print-record/get-work-order-details?processOrder=${encodeURIComponent(orderNo)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.order) {
                // 工单存在于工单管理系统中
                const productModel = data.order.ord_productModel || '';
                const productTypeId = data.order.ord_product_type_id;
                const productionQuantity = data.order.ord_productionQuantity || 0;
                const snLength = data.order.ord_snlenth || 0;
                
                // 根据工单号前缀自动判断是否为返工工单
                const isReworkWorkOrder = orderNo.startsWith('FG');
                
                // 自动设置返工工单下拉框
                const reworkSelect = document.getElementById('isReworkWorkOrderSelect');
                if (reworkSelect) {
                    reworkSelect.value = isReworkWorkOrder ? 'true' : 'false';
                    Logger.log(`自动设置工单 ${orderNo} 为${isReworkWorkOrder ? '返工' : '普通'}工单`);
                }

                // 保存重要工单信息到全局状态
                if (typeof inspectionData.workOrderDetails === 'undefined') {
                    inspectionData.workOrderDetails = {};
                }
                inspectionData.workOrderDetails = {
                    productionQuantity: parseInt(productionQuantity, 10) || 0,
                    snLength: parseInt(snLength, 10) || 0,
                    productCode: data.order.ord_productCode || '' // 新增：保存产品编码
                };
                Logger.log(`工单信息: 生产数量=${productionQuantity}, SN长度=${snLength}, 产品编码=${data.order.ord_productCode}`);

                // 填充产品型号
                const serialNoInput = document.getElementById('serialNo');
                if (serialNoInput) {
                    serialNoInput.value = productModel;
                }

                // 如果有产品类型ID，设置产品类型
                if (productTypeId) {
                    const productTypeSelect = document.getElementById('productType');
                    if (productTypeSelect) {
                        // 将productTypeId转换为数字，确保比较正确
                        const numericProductTypeId = parseInt(productTypeId, 10);

                        Logger.log(`从工单管理系统获取的产品类型ID: ${numericProductTypeId}`);

                        // 直接使用orderproduct_type_id作为选择依据
                        // 创建映射关系：orderproduct_type_id -> 产品类型名称（用于日志）
                        const productTypeMapping = {
                            1: "四代PLC",
                            2: "五代PLC"
                        };

                        // 获取对应的产品类型名称（仅用于日志）
                        const expectedTypeName = productTypeMapping[numericProductTypeId];
                        Logger.log(`期望选择的产品类型: ${expectedTypeName} (ID=${numericProductTypeId})`);

                        // 记录所有可用选项，帮助调试
                        Logger.log("可用的产品类型选项:");
                        for (let i = 0; i < productTypeSelect.options.length; i++) {
                            if (productTypeSelect.options[i].value) {
                                Logger.log(`- 选项ID=${productTypeSelect.options[i].value}, 名称=${productTypeSelect.options[i].textContent}`);
                            }
                        }

                        // 直接根据orderproduct_type_id选择对应的选项
                        let found = false;
                        for (let i = 0; i < productTypeSelect.options.length; i++) {
                            const option = productTypeSelect.options[i];
                            // 检查选项文本是否与期望的产品类型名称匹配
                            if (option.textContent === expectedTypeName) {
                                option.selected = true;
                                inspectionData.currentProductTypeId = option.value;
                                Logger.log(`已选择产品类型: ${option.textContent} (选项ID=${option.value})`);
                                found = true;
                                break;
                            }
                        }

                        // 如果没有找到匹配的选项，尝试直接使用orderproduct_type_id
                        if (!found) {
                            Logger.log(`未找到名称为 ${expectedTypeName} 的选项，尝试直接使用ID=${numericProductTypeId}`);

                            // 尝试直接设置选择框的值
                            productTypeSelect.value = numericProductTypeId.toString();

                            // 检查是否设置成功
                            if (productTypeSelect.value === numericProductTypeId.toString()) {
                                inspectionData.currentProductTypeId = numericProductTypeId.toString();
                                Logger.log(`已直接设置产品类型ID=${numericProductTypeId}`);
                                found = true;
                            }
                        }

                        // 如果仍然没有找到匹配的选项，记录错误但不改变当前选择
                        if (!found) {
                            Logger.warn(`未能设置产品类型，请检查产品类型配置是否正确`);
                        }

                        // 无论如何，加载对应的检验项目
                        if (inspectionData.currentProductTypeId) {
                            loadInspectionItems(inspectionData.currentProductTypeId);
                        }
                    }
                }

                // 继续调用质检API获取工单信息
                checkQualityInspectionWorkOrder(orderNo, isCreating);
            } else {
                // 工单不存在于工单管理系统中
                hideLoading();
                showNotification('error', '工单不存在', '该工单号在工单管理系统中不存在，无法进行质检');
            }
        })
        .catch(error => {
            hideLoading();
            Logger.error('Error loading work order from management system:', error);
            showNotification('error', '加载失败', '网络错误，请稍后重试');
        });
}

// 检查质检系统中的工单信息
function checkQualityInspectionWorkOrder(orderNo, isCreating = false) {
    // 确保工单号被正确编码（处理特殊字符如"/"）
    const encodedOrderNo = encodeURIComponent(orderNo);
    Logger.log(`检查质检系统中的工单信息: ${orderNo} (编码后: ${encodedOrderNo})`);
    
    // 调用质检API获取工单信息
    fetch(`/api/quality-inspection/work-order/${encodedOrderNo}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 工单存在于质检系统中，处理工单数据
                const workOrder = data.data.work_order;
                const productTypeId = workOrder.product_type_id.toString();
                
                Logger.log(`质检系统工单产品类型ID: ${productTypeId}, 当前选择的产品类型ID: ${inspectionData.currentProductTypeId}`);
                
                // 注意：我们保留当前的产品类型ID，不使用质检系统的ID
                // 这确保了与工单管理系统保持一致
                // 但是要先确认当前选择的产品类型ID是否有效
                if (!inspectionData.currentProductTypeId) {
                    Logger.log(`当前未选择产品类型，将使用质检系统的产品类型ID: ${productTypeId}`);
                    inspectionData.currentProductTypeId = productTypeId;
                    const productTypeSelect = document.getElementById('productType');
                    if (productTypeSelect) {
                        productTypeSelect.value = productTypeId;
                    }
                }
                
                // 使用当前的产品类型ID加载检验项目
                loadInspectionItemsAndThenProcessWorkOrder(inspectionData.currentProductTypeId, data.data);
                return; // 提前返回，避免重复处理

                // 以下代码已经通过上面的return跳过，不会执行
                // 如果产品类型匹配，直接处理工单数据
                // processWorkOrderData(data.data);
            } else {
                // 工单不存在于质检系统中，但存在于工单管理系统中，创建新工单
                createWorkOrder(orderNo);
            }
        })
        .catch(error => {
            hideLoading();
            Logger.error('Error checking quality inspection work order:', error);
            showNotification('error', '加载失败', '网络错误，请稍后重试');
        });
}

// 先加载检验项目，然后再处理工单数据
function loadInspectionItemsAndThenProcessWorkOrder(productTypeId, workOrderData) {
    // 调用API获取检验项目
    fetch(`/api/quality-inspection/inspection-items?product_type_id=${productTypeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 重置页面状态
                resetPageState();

                // 保存检验项目数据
                const stages = data.data;
                for (const stage in stages) {
                    if (!inspectionData.pageState.stages[stage]) {
                        inspectionData.pageState.stages[stage] = {
                            title: getDefaultStageTitle(stage),
                            description: getDefaultStageDescription(stage),
                            items: []
                        };
                    }

                    inspectionData.pageState.stages[stage].items = stages[stage];

                    // 初始化检验结果
                    inspectionData.pageState.inspectionResults[stage].first = Array(stages[stage].length).fill(false);
                    inspectionData.pageState.inspectionResults[stage].self = Array(stages[stage].length).fill(false);
                    inspectionData.pageState.inspectionResults[stage].ipqc = Array(stages[stage].length).fill(false);
                }

                // 处理工单数据
                processWorkOrderData(workOrderData);
            } else {
                hideLoading();
                showNotification('error', '加载失败', data.message || '无法加载检验项目');
            }
        })
        .catch(error => {
            hideLoading();
            Logger.error('Error loading inspection items:', error);
            showNotification('error', '加载失败', '网络错误，请稍后重试');
        });
}

// 处理工单数据
function processWorkOrderData(data) {
    inspectionData.currentWorkOrderId = data.work_order.id;
    inspectionData.currentWorkOrder = data.work_order;

    // 检查是否为返工工单
    if (data.work_order.is_rework) {
        inspectionData.isReworkWorkOrder = true;

        // 更新返工工单下拉框
        const reworkSelect = document.getElementById('isReworkWorkOrderSelect');
        if (reworkSelect) {
            reworkSelect.value = (data.work_order && data.work_order.is_rework) ? 'true' : 'false';
        }

        // 显示返工标识
        showReworkWorkOrderBadge();
    } else {
        inspectionData.isReworkWorkOrder = false;
    }

    // 保存产品列表
    if (data.products) {
        inspectionData.products = data.products;
    }

    // 处理检验状态记录 - 新API返回的是inspection_statuses而不是records
    if (data.inspection_statuses) {
        // 将检验状态转换为提交记录格式
        const submissions = [];
        const stagesMap = {};

        // 按阶段和角色分组，找出最新的检验记录作为提交记录
        data.inspection_statuses.forEach(status => {
            const key = `${status.stage}-${status.inspector_role}`;
            if (!stagesMap[key] || new Date(status.inspection_time) > new Date(stagesMap[key].inspection_time)) {
                stagesMap[key] = status;
            }
        });

        // 转换为提交记录格式
        for (const key in stagesMap) {
            const status = stagesMap[key];
            submissions.push({
                stage: status.stage,
                inspector_role: status.inspector_role,
                submitted_by: status.inspector,
                submitted_at: status.inspection_time,
                is_final: status.is_final
            });
        }

        // 处理提交记录
        processSubmissions(submissions);
    }

    // 处理附件
    processAttachments(data.attachments);

    // 渲染页面
    renderAllInspectionItems();
    renderAllAttachments();
    updateProgress();

    // 更新工单状态显示
    updateWorkOrderStatus();

    // 设置自动刷新
    setupAutoRefresh();

    hideLoading();

    // 显示成功提示
    showNotification('success', '工单加载成功', '已加载最新的检验状态');
}

// 创建工单
function createWorkOrder(orderNo) {
    if (!orderNo || !inspectionData.currentProductTypeId) {
        showNotification('error', '工单号和产品类型不能为空');
        return;
    }

    // 根据工单号前缀自动判断是否为返工工单
    const isReworkByPrefix = orderNo.startsWith('FG');
    
    // 获取下拉框选择值，如果已根据前缀设置过，使用已设置的值
    let isReworkWorkOrder = document.getElementById('isReworkWorkOrderSelect')?.value === 'true';
    
    // 如果界面下拉框值与前缀判断不一致，以前缀判断为准并更新下拉框
    if (isReworkByPrefix !== isReworkWorkOrder) {
        isReworkWorkOrder = isReworkByPrefix;
        const reworkSelect = document.getElementById('isReworkWorkOrderSelect');
        if (reworkSelect) {
            reworkSelect.value = isReworkWorkOrder ? 'true' : 'false';
            Logger.log(`根据前缀调整工单 ${orderNo} 为${isReworkWorkOrder ? '返工' : '普通'}工单`);
        }
    }

    // 显示加载中
    showLoading();

    // 首先检查工单是否存在于工单管理系统中
    fetch(`/api/sn-print-record/get-work-order-details?processOrder=${encodeURIComponent(orderNo)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.order) {
                // 工单存在于工单管理系统中，继续创建质检工单
                createQualityInspectionWorkOrder(orderNo, isReworkWorkOrder);
            } else {
                // 工单不存在于工单管理系统中，禁止创建
                hideLoading();
                showNotification('error', '工单不存在', '该工单号在工单管理系统中不存在，无法创建质检工单');
            }
        })
        .catch(error => {
            hideLoading();
            Logger.error('Error checking work order in management system:', error);
            showNotification('error', '验证失败', '网络错误，请稍后重试');
        });
}

// 创建质检工单
function createQualityInspectionWorkOrder(orderNo, isReworkWorkOrder) {
    // 根据是否为返工工单调用不同的API
    const apiUrl = isReworkWorkOrder ?
        '/api/quality-inspection/rework-work-order' :
        '/api/quality-inspection/work-order';

    fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            work_order_no: orderNo,
            product_type_id: inspectionData.currentProductTypeId
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            // 保存工单ID和信息
            inspectionData.currentWorkOrderId = data.data ? data.data.id : data.work_order.id;
            inspectionData.currentWorkOrder = data.data || data.work_order;
            inspectionData.isReworkWorkOrder = isReworkWorkOrder;

            showNotification('success', isReworkWorkOrder ? '返工工单创建成功' : '工单创建成功');

            // 加载检验项目
            loadInspectionItems(inspectionData.currentProductTypeId);

            // 更新界面状态
            updateWorkOrderStatus();

            // 如果是返工工单，显示返工标识
            if (isReworkWorkOrder) {
                showReworkWorkOrderBadge();
            }
        } else {
            showNotification('error', '创建工单失败', data.message || '未知错误');
        }
    })
    .catch(error => {
        hideLoading();
        Logger.error('Error creating work order:', error);
        showNotification('error', '创建失败', '网络错误，请稍后重试');
    });
}

// 处理检验记录
function processRecords(records) {
    // 在新的API中，我们不再使用inspection_records表，而是使用inspection_statuses
    // 这个函数保留用于兼容旧代码，但实际上不再需要处理单个检验项目的记录

    // 确保检验结果数组已初始化
    for (const stage in inspectionData.pageState.stages) {
        if (!inspectionData.pageState.inspectionResults[stage]) {
            inspectionData.pageState.inspectionResults[stage] = {};
        }

        for (const role of [window.ROLES.FIRST, window.ROLES.SELF, window.ROLES.IPQC]) {
            if (!inspectionData.pageState.inspectionResults[stage][role]) {
                const itemCount = inspectionData.pageState.stages[stage].items.length;
                inspectionData.pageState.inspectionResults[stage][role] = Array(itemCount).fill(false);
            }
        }
    }

    // 根据提交状态，检查阶段是否完成，解锁下一阶段
    for (const apiStage in inspectionData.pageState.inspectionSubmissions) {
        const stage = apiStage;
        const submissions = inspectionData.pageState.inspectionSubmissions[apiStage];

        if (submissions.first.isSubmitted && submissions.self.isSubmitted && submissions.ipqc.isSubmitted) {
            const stageElement = document.getElementById(`${stage}-stage`);
            const stageNode = stageElement?.querySelector('.self-inspection__stage-node');
            const stageCard = stageElement?.querySelector('.self-inspection__stage-card');

            if (stageNode) {
                stageNode.classList.add('self-inspection__stage-node--completed');
                stageNode.innerHTML = '✓';
            }
            if (stageCard) {
                stageCard.classList.add('self-inspection__stage-card--completed');
            }

            // 自动收缩已完成的阶段
            collapseStage(stage);

            // 解锁下一阶段
            let nextStage = null;
            if (stage === window.STAGES.ASSEMBLY) {
                nextStage = window.STAGES.TEST;
            } else if (stage === window.STAGES.TEST) {
                nextStage = window.STAGES.PACKAGING;
            }

            if (nextStage) {
                unlockStage(nextStage);
            }
        }
    }

    // 检查整个流程是否完成
    checkProcessCompletion();
}

// 处理附件
function processAttachments(attachments) {
    if (!attachments || attachments.length === 0) return;

    // 重置附件
    for (const stage in inspectionData.pageState.attachments) {
        inspectionData.pageState.attachments[stage] = {
            first: [],
            self: [],
            ipqc: []
        };
    }

    // 按阶段和角色分组
    attachments.forEach(attachment => {
        const stage = attachment.stage;
        const role = attachment.inspector_role;

        if (inspectionData.pageState.attachments[stage] && inspectionData.pageState.attachments[stage][role]) {
            inspectionData.pageState.attachments[stage][role].push({
                id: attachment.id,
                name: attachment.file_name,
                type: getFileType(attachment.file_name),
                size: formatFileSize(attachment.file_size)
            });
        }
    });
}

// 渲染所有检验项目
function renderAllInspectionItems() {
    // 渲染每个阶段的每个角色的检验项目
    for (const stage in inspectionData.pageState.stages) {
        for (const role in inspectionData.pageState.inspectionResults[stage]) {
            renderInspectionItems(stage, role);

            // 如果已提交，更新操作人员信息
            const submission = inspectionData.pageState.inspectionSubmissions[stage][role];
            if (submission.isSubmitted) {
                updateOperatorInfo(stage, role, submission.operator, submission.submittedAt);
            }
        }
    }
}

// 渲染所有附件
function renderAllAttachments() {
    // 渲染每个阶段的每个角色的附件
    for (const stage in inspectionData.pageState.attachments) {
        for (const role in inspectionData.pageState.attachments[stage]) {
            // 添加渲染附件列表的函数
            renderAttachmentFiles(stage, role);
        }
    }
}

// 渲染检验项目
function renderInspectionItems(stage, type) {
    Logger.log('渲染检验项目', stage, type);

    // 尝试多种可能的容器ID格式
    let container = document.getElementById(`${stage}-${type}-items`);

    // 如果找不到，尝试其他可能的ID格式
    if (!container) {
        container = document.getElementById(`${stage}${type}Items`);
    }

    // 如果仍然找不到，尝试创建容器
    if (!container) {
        Logger.warn(`Container not found for ${stage}-${type}-items or ${stage}${type}Items, attempting to create it`);

        // 查找父容器
        const parentContainer = document.getElementById(`${stage}-${type}`) || document.getElementById(`${stage}${type}`);

        if (parentContainer) {
            // 创建容器
            container = document.createElement('div');
            container.id = `${stage}-${type}-items`;
            container.className = 'self-inspection__items';

            // 查找插入位置
            const insertBefore = parentContainer.querySelector('.self-inspection__action-bar');

            if (insertBefore) {
                parentContainer.insertBefore(container, insertBefore);
            } else {
                parentContainer.appendChild(container);
            }

            Logger.log(`Created container with ID: ${container.id}`);
        } else {
            Logger.error(`Parent container not found for ${stage}-${type} or ${stage}${type}`);
            return;
        }
    }

    container.innerHTML = '';

    // 直接使用后端的阶段和角色名称
    if (![window.STAGES.ASSEMBLY, window.STAGES.TEST, window.STAGES.PACKAGING].includes(stage) || ![window.ROLES.FIRST, window.ROLES.SELF, window.ROLES.IPQC].includes(type)) {
        Logger.error(`Invalid stage or role: ${stage}, ${type}`);
        container.innerHTML = '<p>无法加载检验项目：无效的阶段或角色</p>';
        return;
    }

    // 获取检验项目和结果
    const items = inspectionData.pageState.stages[stage]?.items || [];
    const results = inspectionData.pageState.inspectionResults[stage]?.[type] || [];

    // 检查禁用条件
    const isFinalSubmission = inspectionData.pageState.inspectionSubmissions[stage]?.[type]?.isFinalSubmission || false;
    let disabled = isFinalSubmission ? 'disabled' : '';
    
    // 新增：检查首检前置条件（自检和IPQC需要等待首检完成）
    let needWaitFirstInspection = false;
    if (type === window.ROLES.SELF || type === window.ROLES.IPQC) {
        const firstInspectionStatus = inspectionData.pageState.inspectionSubmissions[stage][window.ROLES.FIRST];
        if (!firstInspectionStatus.isSubmitted || !firstInspectionStatus.isFinalSubmission) {
            disabled = 'disabled';
            needWaitFirstInspection = true;
        }
    }

    if (items.length === 0) {
        container.innerHTML = '<p>暂无检验项目</p>';
        return;
    }

    // 如果需要等待首检，显示提示信息
    if (needWaitFirstInspection) {
        const roleText = type === window.ROLES.SELF ? '自检' : 'IPQC';
        container.innerHTML = `
            <div class="self-inspection__wait-notice">
                <i class="fas fa-clock" style="margin-right: 8px; color: #f59e0b;"></i>
                <span>请等待首检完成最终提交后再进行${roleText}</span>
            </div>
        `;
        return;
    }

    items.forEach((item, index) => {
        const itemId = `${stage}-${type}-item-${index}`;
        const checked = results[index] || false;

        const itemHtml = `
            <div class="self-inspection__checkbox-container">
                <input type="checkbox" id="${itemId}" class="self-inspection__checkbox" ${checked ? 'checked' : ''} ${disabled} onchange="updateInspectionItem('${stage}', '${type}', ${index}, this.checked)">
                <label for="${itemId}" class="self-inspection__checkbox-label">${item.item_name}</label>
            </div>
        `;

        container.innerHTML += itemHtml;
    });

    // 更新全选状态
    updateSelectAllStatus(stage, type);

    // 只有最终提交时才更新按钮状态为已完成
    if (isFinalSubmission) {
        const actionBar = document.querySelector(`#${stage}-${type} .self-inspection__action-bar`);
        if (actionBar) {
            actionBar.innerHTML = `
                <button class="self-inspection__btn self-inspection__btn--reset self-inspection__btn--disabled" disabled>重置</button>
                <span class="self-inspection__completed-tag">已完成</span>
            `;
        }
    }
    // 如果需要等待首检，也需要禁用操作按钮
    else if (needWaitFirstInspection) {
        const actionBar = document.querySelector(`#${stage}-${type} .self-inspection__action-bar`);
        if (actionBar) {
            const roleText = type === window.ROLES.SELF ? '自检' : 'IPQC';
            actionBar.innerHTML = `
                <button class="self-inspection__btn self-inspection__btn--reset self-inspection__btn--disabled" disabled>重置</button>
                <button class="self-inspection__btn self-inspection__btn--submit self-inspection__btn--disabled" disabled>提交${roleText}</button>
            `;
        }
    }
}

// 更新操作人员信息
function updateOperatorInfo(stage, type, operator, submittedAt) {
    Logger.log('更新操作人员信息', stage, type, operator, submittedAt);

    // 使用统一的容器ID格式
    let operatorInfo = document.getElementById(`${stage}-${type}-operator`);

    if (operatorInfo) {
        operatorInfo.classList.add('self-inspection__operator-info--submitted');
        operatorInfo.innerHTML = `
            <div class="self-inspection__operator-header">
                <span class="self-inspection__operator-label">操作人员: ${operator}</span>
                <span class="self-inspection__submitted-tag">已提交</span>
            </div>
            <div class="self-inspection__operator-time">提交时间: ${submittedAt}</div>
        `;
    } else {
        Logger.warn(`Operator info container not found for ${stage}-${type}-operator`);

        // 查找父容器
        const parentContainer = document.getElementById(`${stage}-${type}`);

        if (parentContainer) {
            // 查找操作人员输入区域
            const operatorInputArea = parentContainer.querySelector('.self-inspection__operator-info');

            if (operatorInputArea) {
                operatorInputArea.id = `${stage}-${type}-operator`;
                operatorInputArea.classList.add('self-inspection__operator-info--submitted');
                operatorInputArea.innerHTML = `
                    <div class="self-inspection__operator-header">
                        <span class="self-inspection__operator-label">操作人员: ${operator}</span>
                        <span class="self-inspection__submitted-tag">已提交</span>
                    </div>
                    <div class="self-inspection__operator-time">提交时间: ${submittedAt}</div>
                `;
                Logger.log(`Updated operator info in container: ${operatorInputArea.id}`);
            } else {
                Logger.error(`Operator input area not found in parent container for ${stage}-${type}`);
            }
        } else {
            Logger.error(`Parent container not found for ${stage}-${type}`);
        }
    }
}

// 更新检验项目
function updateInspectionItem(stage, role, index, checked) {
    // 直接使用后端的阶段和角色名称
    if (inspectionData.pageState.inspectionResults[stage] &&
        inspectionData.pageState.inspectionResults[stage][role]) {
        inspectionData.pageState.inspectionResults[stage][role][index] = checked;
        updateSelectAllStatus(stage, role);
        updateProgress();
    } else {
        Logger.error(`Cannot update inspection item for non-existent stage/role: ${stage}-${role}`);
    }
}

// 更新全选状态
function updateSelectAllStatus(stage, role) {
    Logger.log('更新全选状态', stage, role);

    // 直接使用后端的阶段和角色名称
    if (![window.STAGES.ASSEMBLY, window.STAGES.TEST, window.STAGES.PACKAGING].includes(stage) || ![window.ROLES.FIRST, window.ROLES.SELF, window.ROLES.IPQC].includes(role)) {
        Logger.error(`Invalid stage or role: ${stage}, ${role}`);
        return;
    }

    const results = inspectionData.pageState.inspectionResults[stage]?.[role] || [];
    const allChecked = results.length > 0 && results.every(item => item);

    // 检查禁用条件
    const isFinalSubmission = inspectionData.pageState.inspectionSubmissions[stage]?.[role]?.isFinalSubmission || false;
    let shouldDisable = isFinalSubmission;
    
    // 新增：检查首检前置条件
    if (role === window.ROLES.SELF || role === window.ROLES.IPQC) {
        const firstInspectionStatus = inspectionData.pageState.inspectionSubmissions[stage][window.ROLES.FIRST];
        if (!firstInspectionStatus.isSubmitted || !firstInspectionStatus.isFinalSubmission) {
            shouldDisable = true;
        }
    }

    // 使用统一的ID格式
    const selectAllCheckbox = document.getElementById(`${stage}-${role}-selectAll`);

    if (selectAllCheckbox) {
        selectAllCheckbox.checked = allChecked;
        selectAllCheckbox.disabled = shouldDisable;
    } else {
        Logger.warn(`Select all checkbox not found for ${stage}-${role}-selectAll`);

        // 尝试查找父容器
        const parentContainer = document.getElementById(`${stage}-${role}`);

        if (parentContainer) {
            // 查找全选复选框
            const selectAllContainer = parentContainer.querySelector('.self-inspection__select-all');

            if (selectAllContainer) {
                const checkbox = selectAllContainer.querySelector('input[type="checkbox"]');

                if (checkbox) {
                    checkbox.id = `${stage}-${role}-selectAll`;
                    checkbox.checked = allChecked;
                    checkbox.disabled = shouldDisable;
                    Logger.log(`Updated select all checkbox: ${checkbox.id}`);
                } else {
                    Logger.error(`Select all checkbox element not found in container`);
                }
            } else {
                Logger.error(`Select all container not found in parent container`);
            }
        } else {
            Logger.error(`Parent container not found for ${stage}-${role}`);
        }
    }
}

// 切换全选（自检页面专用）
function toggleInspectionSelectAll(stage, role) {
    Logger.log('切换全选', stage, role);

    // 使用统一的ID格式
    const selectAllCheckbox = document.getElementById(`${stage}-${role}-selectAll`);

    if (!selectAllCheckbox) {
        Logger.error(`Select all checkbox not found for ${stage}-${role}-selectAll`);
        return;
    }

    const checked = selectAllCheckbox.checked;
    Logger.log('全选状态:', checked);

    // 直接使用后端的阶段和角色名称
    if (![window.STAGES.ASSEMBLY, window.STAGES.TEST, window.STAGES.PACKAGING].includes(stage) || ![window.ROLES.FIRST, window.ROLES.SELF, window.ROLES.IPQC].includes(role)) {
        Logger.error(`Invalid stage or role: ${stage}, ${role}`);
        return;
    }

    if (inspectionData.pageState.inspectionResults[stage] &&
        inspectionData.pageState.inspectionResults[stage][role]) {
        inspectionData.pageState.inspectionResults[stage][role] =
            inspectionData.pageState.inspectionResults[stage][role].map(() => checked);
        renderInspectionItems(stage, role);
        updateProgress();
    } else {
        Logger.error(`Cannot toggle select all for non-existent stage/role: ${stage}-${role}`);
    }
}

// 切换检验类型选项卡
function switchTab(stage, role) {
    Logger.log('切换检验类型选项卡', stage, role);

    // 首先确保阶段内容已展开
    const stageContent = document.getElementById(`${stage}-content`);
    if (stageContent && !stageContent.classList.contains('self-inspection__stage-content--expanded')) {
        // 如果阶段内容未展开，先展开它
        stageContent.classList.add('self-inspection__stage-content--expanded');
        const toggle = document.getElementById(`${stage}-toggle`);
        if (toggle) {
            toggle.classList.remove('self-inspection__stage-toggle--collapsed');
            toggle.textContent = '展开';
        }
    }

    // 更新选项卡状态
    const tabContainer = document.querySelector(`#${stage}-content .self-inspection__inspection-tabs`);
    if (!tabContainer) {
        Logger.error(`找不到选项卡容器: #${stage}-content .self-inspection__inspection-tabs`);
        return;
    }

    const tabs = tabContainer.querySelectorAll('.self-inspection__inspection-tab');
    tabs.forEach(tab => tab.classList.remove('self-inspection__inspection-tab--active'));

    let tabIndex;
    if (role === window.ROLES.FIRST) {
        tabIndex = 1;
    } else if (role === window.ROLES.SELF) {
        tabIndex = 2;
    } else { // ipqc
        tabIndex = 3;
    }

    const activeTab = tabContainer.querySelector(`.self-inspection__inspection-tab:nth-child(${tabIndex})`);
    if (activeTab) {
        activeTab.classList.add('self-inspection__inspection-tab--active');
    } else {
        Logger.error(`找不到选项卡: .self-inspection__inspection-tab:nth-child(${tabIndex})`);
    }

    // 更新内容
    const contentContainer = document.querySelector(`#${stage}-content`);
    if (!contentContainer) {
        Logger.error(`找不到内容容器: #${stage}-content`);
        return;
    }

    const contents = contentContainer.querySelectorAll('.self-inspection__inspection-content');
    contents.forEach(content => content.classList.remove('self-inspection__inspection-content--active'));

    const activeContent = document.getElementById(`${stage}-${role}`);
    if (activeContent) {
        activeContent.classList.add('self-inspection__inspection-content--active');
    } else {
        Logger.error(`找不到内容元素: #${stage}-${role}`);
    }
}

// 切换阶段展开/折叠
function toggleStage(stage) {
    const stageElement = document.getElementById(`${stage}-stage`);
    const content = document.getElementById(`${stage}-content`);
    const toggle = document.getElementById(`${stage}-toggle`);

    // 检查阶段是否被锁定
    const stageNode = stageElement?.querySelector('.self-inspection__stage-node');
    if (stageNode && stageNode.classList.contains('self-inspection__stage-node--locked')) {
        showNotification('warning', '阶段未解锁', '请先完成前一阶段的检验');
        return;
    }

    if (content && toggle) { // 确保元素存在
        if (content.classList.contains('self-inspection__stage-content--expanded')) {
            // 当前是展开状态 -> 折叠
            collapseStage(stage); // 调用统一的折叠函数
        } else {
            // 当前是折叠状态 -> 展开
            content.classList.add('self-inspection__stage-content--expanded');
            toggle.classList.remove('self-inspection__stage-toggle--collapsed');
            toggle.textContent = '展开'; // 确保文本是"展开"
        }
    }
}

// 收缩阶段内容
function collapseStage(stage) {
    const content = document.getElementById(`${stage}-content`);
    const toggle = document.getElementById(`${stage}-toggle`);

    if (content && toggle) {
        content.classList.remove('self-inspection__stage-content--expanded');
        toggle.classList.add('self-inspection__stage-toggle--collapsed');
        toggle.textContent = '收起'; // 确保收缩后按钮文本是"收起"
    }
}

// 解锁阶段
function unlockStage(stage) {
    const stageNode = document.querySelector(`#${stage}-stage .self-inspection__stage-node`);
    if (stageNode) {
        stageNode.classList.remove('self-inspection__stage-node--locked');
    }
}

// 重置检验
function resetInspection(stage, role) {
    // 直接使用后端的阶段和角色名称
    if (inspectionData.pageState.inspectionSubmissions[stage][role].isSubmitted) {
        return;
    }

    if (inspectionData.pageState.inspectionResults[stage] &&
        inspectionData.pageState.inspectionResults[stage][role]) {
        inspectionData.pageState.inspectionResults[stage][role] =
            inspectionData.pageState.inspectionResults[stage][role].map(() => false);
        // 不再需要清空操作人员输入框，因为它现在是只读且自动填充
        renderInspectionItems(stage, role);
        updateProgress();
    } else {
         Logger.error(`Cannot reset inspection for non-existent stage/role: ${stage}-${role}`);
    }
}

// 提交检验
function submitInspection(stage, type) {
    Logger.log('提交检验', stage, type);

    // 直接使用阶段和角色名称
    const apiStage = stage;
    const apiRole = type;

    // 检查首检前置条件（自检和IPQC需要等待首检完成）
    if (apiRole === window.ROLES.SELF || apiRole === window.ROLES.IPQC) {
        const firstInspectionStatus = inspectionData.pageState.inspectionSubmissions[apiStage][window.ROLES.FIRST];
        if (!firstInspectionStatus.isSubmitted || !firstInspectionStatus.isFinalSubmission) {
            const roleText = apiRole === window.ROLES.SELF ? '自检' : 'IPQC';
            SweetAlert.warning(`请等待首检完成最终提交后再进行${roleText}`, '首检未完成');
            return;
        }
    }

    // 验证工单号
    const orderNoElement = document.getElementById('orderNo');
    let orderNo = "";

    if (orderNoElement) {
        orderNo = orderNoElement.value.trim();
        if (!orderNo) {
            const orderNoGroup = document.getElementById('orderNoGroup');
            if (orderNoGroup) {
                orderNoGroup.classList.add('self-inspection__input-group--error');
            }
            SweetAlert.warning('提交检验前，必须先填写工单号', '请填写工单号');
            return;
        }
    } else {
        Logger.warn('找不到工单号输入框，尝试从URL或其他地方获取');
        // 尝试从URL获取工单号
        const urlParams = new URLSearchParams(window.location.search);
        orderNo = urlParams.get('orderNo') || "";

        if (!orderNo) {
            SweetAlert.warning('无法获取工单号，请刷新页面重试', '工单号缺失');
            return;
        }
    }

    // 验证产品类型
    if (!inspectionData.currentProductTypeId) {
        SweetAlert.warning('提交检验前，必须先选择产品类型', '请选择产品类型');
        return;
    }

    // 验证操作人员
    let operator = "";
    const operatorInput = document.querySelector(`#${stage}-${type}-operator .self-inspection__operator-input input`);

    if (operatorInput) {
        operator = operatorInput.value.trim();
        if (!operator) {
            SweetAlert.warning('提交检验前，必须填写操作人员', '请填写操作人员');
            operatorInput.focus();
            return;
        }
    } else {
        // 如果找不到操作人员输入框，直接使用当前登录用户名
        operator = window.currentLoggedInUser || '未知操作员';
        Logger.log(`找不到操作人员输入框，使用当前登录用户名: ${operator}`);

        // 保存当前状态
        window.currentConfirmStage = stage;
        window.currentConfirmType = type;
        window.currentOperator = operator;

        // 继续提交流程
        showSubmitOptions();
        return;
    }

    // 检查是否已经提交过
    if (inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].isSubmitted) {
        SweetAlert.warning('此阶段的检验已经提交过，不能重复提交', '已提交');
        return;
    }

    // 验证是否所有项目已选中
    const results = inspectionData.pageState.inspectionResults[apiStage][apiRole];
    if (!results || !results.every(item => item)) {
        SweetAlert.warning('请先完成所有检验项目', '检验未完成');
        return;
    }

    // 检查是否有选中的产品
    if (!inspectionData.selectedProductIds || inspectionData.selectedProductIds.length === 0) {
        // 显示SN号扫描界面
        showScanSerialNumberUI(apiStage, apiRole);
        return;
    }

    // 保存当前状态
    window.currentConfirmStage = stage;
    window.currentConfirmType = type;
    window.currentOperator = operator;

    // 显示提交选项
    showSubmitOptions();
}

// 显示提交选项对话框
function showSubmitOptions() {
    // 检查是否是自检角色
    const apiRole = window.currentConfirmType;
    const isSelfInspection = apiRole === 'self';

    // 构建对话框内容
    let dialogContent = `
        <p>请选择提交方式：</p>
        <div style="display: flex; flex-direction: column; gap: 10px; margin-top: 20px;">
            <button id="partialSubmitBtn" style="width: 100%; padding: 10px 16px; background-color: #f1f5f9; color: #334155; border: 1px solid #cbd5e1; border-radius: 0.5rem; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s; box-shadow: 0 1px 2px rgba(0,0,0,0.05);">
                部分提交 (仅提交已扫描的产品)
            </button>
            <button id="finalSubmitBtn" style="width: 100%; padding: 10px 16px; background-color: #2563eb; color: white; border: 1px solid #2563eb; border-radius: 0.5rem; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s; box-shadow: 0 2px 4px rgba(37,99,235,0.15);">
                最终提交 (完成产品检验)
            </button>
        </div>
    `;

    // 为所有角色添加说明
    const roleText = isSelfInspection ? '自检' : (apiRole === 'first' ? '首检' : 'IPQC');
    dialogContent += `
        <div style="margin-top: 15px; padding: 10px; border-left: 3px solid #f59e0b; background-color: rgba(245, 158, 11, 0.1);">
            <p style="margin: 0; font-size: 14px;">
                <strong>${roleText}说明：</strong>
            </p>
            <p style="margin: 5px 0 0; font-size: 14px;">
                • <strong>部分提交</strong>：仅提交已扫描的产品，之后可继续扫描其他产品
            </p>
            <p style="margin: 5px 0 0; font-size: 14px;">
                • <strong>最终提交</strong>：完成当前阶段的${roleText}，<strong>未扫描的产品将不会被包含在检验记录中</strong>
            </p>
        </div>
    `;

    SweetAlert.custom({
        title: '提交方式',
        html: dialogContent,
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: '取消',
        customClass: {
            popup: 'self-inspection-swal-popup',
            title: 'self-inspection-swal-title',
            htmlContainer: 'self-inspection-swal-html-container',
            actions: 'self-inspection-swal-actions',
            cancelButton: 'self-inspection__btn self-inspection__btn--reset'
        },
        didOpen: () => {
            // 添加鼠标悬停效果
            const partialBtn = document.getElementById('partialSubmitBtn');
            const finalBtn = document.getElementById('finalSubmitBtn');

            if (partialBtn) {
                partialBtn.addEventListener('mouseover', () => {
                    partialBtn.style.backgroundColor = '#e2e8f0';
                    partialBtn.style.borderColor = '#94a3b8';
                });
                partialBtn.addEventListener('mouseout', () => {
                    partialBtn.style.backgroundColor = '#f1f5f9';
                    partialBtn.style.borderColor = '#cbd5e1';
                });
            }

            if (finalBtn) {
                finalBtn.addEventListener('mouseover', () => {
                    finalBtn.style.backgroundColor = '#1d4ed8';
                    finalBtn.style.boxShadow = '0 4px 6px rgba(37,99,235,0.2)';
                });
                finalBtn.addEventListener('mouseout', () => {
                    finalBtn.style.backgroundColor = '#2563eb';
                    finalBtn.style.boxShadow = '0 2px 4px rgba(37,99,235,0.15)';
                });
            }

            document.getElementById('partialSubmitBtn').addEventListener('click', () => {
                Swal.close();
                confirmSubmission(false); // 部分提交
            });

            document.getElementById('finalSubmitBtn').addEventListener('click', () => {
                // 对所有角色都添加额外确认
                Swal.close();

                // 显示最终提交确认对话框
                SweetAlert.custom({
                    title: '确认最终提交',
                    html: `
                        <p style="font-size: 15px; color: #1e293b;">您确定要进行最终提交吗？</p>
                        <p style="color: #f59e0b; margin-top: 10px; padding: 10px; border-left: 3px solid #f59e0b; background-color: rgba(245, 158, 11, 0.1);">
                            <i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>
                            最终提交后，此阶段的${isSelfInspection ? '自检' : (apiRole === 'first' ? '首检' : 'IPQC')}将被标记为完成，<strong>未扫描的产品将不会被包含在检验记录中</strong>。
                        </p>
                    `,
                    showClass: {
                        popup: 'animate__animated animate__fadeIn'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOut'
                    },
                    showCancelButton: true,
                    confirmButtonText: '确认提交',
                    cancelButtonText: '取消',
                    confirmButtonColor: '#2563eb',
                    cancelButtonColor: '#64748b',
                    buttonsStyling: true,
                    customClass: {
                        popup: 'self-inspection-swal-popup',
                        title: 'self-inspection-swal-title',
                        htmlContainer: 'self-inspection-swal-html-container',
                        actions: 'self-inspection-swal-actions',
                        confirmButton: 'self-inspection__btn self-inspection__btn--submit',
                        cancelButton: 'self-inspection__btn self-inspection__btn--reset'
                    },
                    buttonsStyling: true,
                    cancelButtonText: '<span style="color: white; font-weight: 500;">取消</span>'
                }).then((result) => {
                    if (result.isConfirmed) {
                        confirmSubmission(true); // 最终提交
                    }
                });
            });
        }
    });
}

// 确认提交
function confirmSubmission(isFinalSubmission = false) {
    const stage = window.currentConfirmStage;
    const type = window.currentConfirmType;

    // 直接使用阶段和角色名称
    const apiStage = stage;
    const apiRole = type;

    // 只有当是最终提交并且已经进行过最终提交时，才不允许重复提交
    if (isFinalSubmission && inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].isFinalSubmission) {
        SweetAlert.warning('此阶段的检验已经最终提交过，不能重复提交', '已最终提交');
        return;
    }

    // 获取操作人员信息
    let operator = "";

    // 首先检查是否有保存的操作人员信息
    if (typeof window.currentOperator !== 'undefined' && window.currentOperator) {
        operator = window.currentOperator;
    } else {
        // 尝试从输入框获取
        const operatorInput = document.querySelector(`#${stage}-${type}-operator .self-inspection__operator-input input`);
        if (operatorInput) {
            operator = operatorInput.value.trim();
        } else {
            // 尝试从已保存的状态获取
            operator = inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].operator;

            // 如果仍然没有，使用当前登录用户名
            if (!operator) {
                operator = window.currentLoggedInUser || "未知操作员";
                Logger.warn(`无法获取操作人员信息，使用当前登录用户名: ${operator}`);
            }
        }
    }

    // 如果工单不存在，先创建工单
    if (!inspectionData.currentWorkOrderId) {
        // 尝试从多个位置获取工单号
        let orderNo = "";
        const orderNoElement = document.getElementById('orderNo');

        if (orderNoElement) {
            orderNo = orderNoElement.value.trim();
        } else {
            // 尝试从URL获取
            const urlParams = new URLSearchParams(window.location.search);
            orderNo = urlParams.get('orderNo') || "";
        }

        if (!orderNo) {
            SweetAlert.warning('无法获取工单号，请刷新页面重试', '工单号缺失');
            return;
        }

        // 先检查工单是否存在于工单管理系统中
        showLoading();

        // 使用Promise来处理异步操作
        fetch(`/api/sn-print-record/get-work-order-details?processOrder=${encodeURIComponent(orderNo)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.order) {
                    // 工单存在于工单管理系统中，继续创建质检工单
                    createWorkOrder(orderNo);
                } else {
                    // 工单不存在于工单管理系统中，禁止创建
                    hideLoading();
                    SweetAlert.warning('该工单号在工单管理系统中不存在，无法创建质检工单', '工单不存在');
                }
            })
            .catch(error => {
                hideLoading();
                Logger.error('Error checking work order in management system:', error);
                SweetAlert.warning('验证工单信息时发生错误，请稍后重试', '验证失败');
            });
        return;
    }

    // 准备提交的记录数据
    const records = [];
    const items = inspectionData.pageState.stages[apiStage].items;
    const results = inspectionData.pageState.inspectionResults[apiStage][apiRole];

    items.forEach((item, index) => {
        if (results[index]) {
            records.push({
                work_order_id: inspectionData.currentWorkOrderId,
                inspection_item_id: item.id,
                inspector_role: apiRole,
                is_checked: true,
                checked_by: operator
            });
        }
    });

    // 提交记录
    saveInspectionRecords(records, stage, type, operator, isFinalSubmission);
}

// 保存检验记录
function saveInspectionRecords(records, stage, type, operator, isFinalSubmission) {
    // 直接使用阶段和角色名称
    const apiStage = stage;
    const apiRole = type;

    if (records.length === 0) {
        showNotification('error', '提交失败', '没有要保存的记录');
        return;
    }

    // 显示加载中
    showLoading();

    // 调用API保存记录
    fetch('/api/quality-inspection/records', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            records: records,
            product_ids: inspectionData.selectedProductIds,
            is_final_submission: isFinalSubmission
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            // 如果是最终提交，更新提交状态
            if (isFinalSubmission) {
                // 更新提交状态
                inspectionData.pageState.inspectionSubmissions[apiStage][apiRole] = {
                    operator: operator,
                    submittedAt: getCurrentTime(),
                    isSubmitted: true,
                    isFinalSubmission: true
                };

                // 更新操作人员信息区域
                updateOperatorInfo(stage, type, operator, inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].submittedAt);

                // 更新检验项目为禁用状态
                renderInspectionItems(stage, type);

                // 检查阶段是否完成，并解锁下一阶段
                checkStageCompletion(stage);

                // 如果是首检完成，需要刷新同阶段其他角色的UI状态
                if (apiRole === window.ROLES.FIRST) {
                    Logger.log('首检完成，刷新同阶段其他角色UI状态');
                    // 刷新自检和IPQC的UI
                    renderInspectionItems(stage, window.ROLES.SELF);
                    renderInspectionItems(stage, window.ROLES.IPQC);
                    updateSelectAllStatus(stage, window.ROLES.SELF);
                    updateSelectAllStatus(stage, window.ROLES.IPQC);
                }

                // 显示成功提示，如果是部分提交，显示特殊提示
                if (data.partial_submission) {
                    SweetAlert.custom({
                        icon: 'success',
                        title: '最终提交成功',
                        html: `
                            <p class="swal2-html-container-custom">检验结果已成功提交，但请注意：</p>
                            <div class="self-inspection__completion-details">
                                <p>工单总产品数: <span>${data.total_products}</span></p>
                                <p>已检验产品数: <span>${data.completed_products}</span></p>
                                <p>未检验产品数: <span>${data.total_products - data.completed_products}</span></p>
                            </div>
                            <p class="swal2-html-container-custom" style="margin-top: 10px; color: #f59e0b;">
                                <i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>
                                您已完成最终提交，但工单中仍有部分产品未检验。这些产品将不会被包含在检验记录中。
                            </p>
                        `,
                        confirmButtonText: '确认',
                        customClass: {
                            container: 'self-inspection-swal-container',
                            popup: 'self-inspection-swal-popup',
                            title: 'self-inspection-swal-title',
                            htmlContainer: 'self-inspection-swal-html-container',
                            icon: 'self-inspection-swal-icon',
                            confirmButton: 'self-inspection-swal-confirm-button'
                        }
                    });
                } else {
                    SweetAlert.success('检验结果已成功提交，所有产品检验已完成', '最终提交成功');
                }
            } else {
                // 部分提交成功
                // 清空已选产品列表，允许继续扫描新产品
                inspectionData.selectedProductIds = [];

                // 显示成功提示
                SweetAlert.success(`已成功提交${data.products_completed}个产品的检验结果，您可以继续扫描其他产品`, '部分提交成功');

                // 更新进度
                updateProgress();

                // 刷新工单状态
                refreshWorkOrderData(document.getElementById('orderNo').value.trim());
            }
        } else {
            showNotification('error', '提交失败', data.message || '保存记录失败');
        }
    })
    .catch(error => {
        hideLoading();
        Logger.error('Error saving inspection records:', error);
        const errorMessage = error instanceof Error ? error.message : '网络错误或处理失败，请检查控制台';
        showNotification('error', '提交失败', `保存检验记录时出错: ${errorMessage}`);
    });
}

// 检查阶段是否完成
function checkStageCompletion(stage) {
    // 直接使用后端的阶段名称
    const firstSubmitted = inspectionData.pageState.inspectionSubmissions[stage][window.ROLES.FIRST].isSubmitted;
    const selfSubmitted = inspectionData.pageState.inspectionSubmissions[stage][window.ROLES.SELF].isSubmitted;
    const ipqcSubmitted = inspectionData.pageState.inspectionSubmissions[stage][window.ROLES.IPQC].isSubmitted;

    if (firstSubmitted && selfSubmitted && ipqcSubmitted) {
        // 更新阶段状态为已完成
        const stageElement = document.getElementById(`${stage}-stage`);
        const stageNode = stageElement?.querySelector('.self-inspection__stage-node');
        const stageCard = stageElement?.querySelector('.self-inspection__stage-card');

        if (stageNode) {
             stageNode.classList.add('self-inspection__stage-node--completed');
             stageNode.innerHTML = '✓';
        }
        if (stageCard) {
            stageCard.classList.add('self-inspection__stage-card--completed');
        }

        // 自动收缩已完成的阶段
        collapseStage(stage);

        // 解锁下一阶段
        let nextStage = null;
        if (stage === window.STAGES.ASSEMBLY) {
            nextStage = window.STAGES.TEST;
        } else if (stage === window.STAGES.TEST) {
            nextStage = window.STAGES.PACKAGING;
        }

        if (nextStage) {
            unlockStage(nextStage);
        }

        // 检查整个流程是否完成
        checkProcessCompletion();
    }
}

// 检查整个流程是否完成
function checkProcessCompletion() {
    const allStagesCompleted = [window.STAGES.ASSEMBLY, window.STAGES.TEST, window.STAGES.PACKAGING].every(stage =>
        inspectionData.pageState.inspectionSubmissions[stage][window.ROLES.FIRST].isSubmitted &&
        inspectionData.pageState.inspectionSubmissions[stage][window.ROLES.SELF].isSubmitted &&
        inspectionData.pageState.inspectionSubmissions[stage][window.ROLES.IPQC].isSubmitted
    );

    if (allStagesCompleted) {
        // 显示完成消息
        const orderNo = document.getElementById('orderNo').value;
        const serialNo = document.getElementById('serialNo').value || 'N/A';

        SweetAlert.custom({
            icon: 'success',
            title: '检验流程已完成',
            html: `
                <p class="swal2-html-container-custom">所有阶段的检测项目均已通过，产品可以交付。</p>
                <div class="self-inspection__completion-details">
                    <p>工单号: <span>${orderNo}</span></p>
                    <p>SN号: <span>${serialNo}</span></p>
                </div>
            `,
            confirmButtonText: '完成',
            customClass: {
                container: 'self-inspection-swal-container',
                popup: 'self-inspection-swal-popup',
                title: 'self-inspection-swal-title',
                htmlContainer: 'self-inspection-swal-html-container',
                icon: 'self-inspection-swal-icon',
                confirmButton: 'self-inspection-swal-confirm-button'
            }
        });
    }
}

// 更新进度
function updateProgress() {
    // 计算总项目数
    let totalItems = 0;
    let checkedItems = 0;
    let completedStages = 0;
    let totalStages = 0;

    for (const apiStage in inspectionData.pageState.stages) {
        totalStages++;
        const stageItems = inspectionData.pageState.stages[apiStage].items.length;
        totalItems += stageItems * 3; // 首检、自检、IPQC

        // 检查该阶段是否所有角色都已最终提交
        const firstSubmissionStatus = inspectionData.pageState.inspectionSubmissions[apiStage][window.ROLES.FIRST];
        const selfSubmissionStatus = inspectionData.pageState.inspectionSubmissions[apiStage][window.ROLES.SELF];
        const ipqcSubmissionStatus = inspectionData.pageState.inspectionSubmissions[apiStage][window.ROLES.IPQC];

        const allFinallySubmitted =
            (firstSubmissionStatus.isSubmitted && firstSubmissionStatus.isFinalSubmission) &&
            (selfSubmissionStatus.isSubmitted && selfSubmissionStatus.isFinalSubmission) &&
            (ipqcSubmissionStatus.isSubmitted && ipqcSubmissionStatus.isFinalSubmission);

        if (allFinallySubmitted) {
            // 如果所有角色都已最终提交，计算为全部完成
            checkedItems += stageItems * 3;
            completedStages++;
        } else {
            // 计算各角色选中的项目数
            for (const apiRole in inspectionData.pageState.inspectionResults[apiStage]) {
                checkedItems += inspectionData.pageState.inspectionResults[apiStage][apiRole]
                    .filter(item => item).length;
            }
        }

        // 更新阶段进度
        updateStageProgress(apiStage);
    }

    // 计算总体进度百分比
    let progressPercentage = 0;
    if (completedStages === totalStages && totalStages > 0) {
        // 如果所有阶段都完成，显示100%
        progressPercentage = 100;
    } else {
        // 否则根据已完成项目计算百分比
        progressPercentage = totalItems > 0 ? Math.round((checkedItems / totalItems) * 100) : 0;
    }

    // 更新进度条
    const progressPercentageElem = document.getElementById('progressPercentage');
    const progressInnerElem = document.getElementById('progressInner');
    if (progressPercentageElem) progressPercentageElem.textContent = `${progressPercentage}%`;
    if (progressInnerElem) progressInnerElem.style.width = `${progressPercentage}%`;
}

// 更新阶段进度
function updateStageProgress(apiStage) {
    const stageItems = inspectionData.pageState.stages[apiStage].items.length;
    const totalItems = stageItems * 3; // 首检、自检、IPQC

    // 检查该阶段是否所有角色都已最终提交
    const firstSubmissionStatus = inspectionData.pageState.inspectionSubmissions[apiStage][window.ROLES.FIRST];
    const selfSubmissionStatus = inspectionData.pageState.inspectionSubmissions[apiStage][window.ROLES.SELF];
    const ipqcSubmissionStatus = inspectionData.pageState.inspectionSubmissions[apiStage][window.ROLES.IPQC];

    const allFinallySubmitted =
        (firstSubmissionStatus.isSubmitted && firstSubmissionStatus.isFinalSubmission) &&
        (selfSubmissionStatus.isSubmitted && selfSubmissionStatus.isFinalSubmission) &&
        (ipqcSubmissionStatus.isSubmitted && ipqcSubmissionStatus.isFinalSubmission);

    // 如果所有角色都已最终提交，直接显示100%
    let progressPercentage = 0;
    if (allFinallySubmitted) {
        progressPercentage = 100;
    } else {
        // 计算已选中的项目数
        let checkedItems = 0;
        for (const apiRole in inspectionData.pageState.inspectionResults[apiStage]) {
            checkedItems += inspectionData.pageState.inspectionResults[apiStage][apiRole]
                .filter(item => item).length;
        }

        // 计算阶段进度百分比
        progressPercentage = totalItems > 0 ? Math.round((checkedItems / totalItems) * 100) : 0;
    }

    // 更新阶段进度显示
    const stageProgressElem = document.getElementById(`${apiStage}-progress`);
    if (stageProgressElem) {
        stageProgressElem.textContent = `${progressPercentage}%`;
    }

    // 如果进度为100%，更新阶段卡片样式
    if (progressPercentage === 100) {
        const stageCard = document.querySelector(`#${apiStage}-stage .self-inspection__stage-card`);
        if (stageCard) {
            stageCard.classList.add('self-inspection__stage-card--completed');
        }
    }
}

// 待上传文件列表 - 使用window对象来避免重复声明
if (typeof window.pendingFiles === 'undefined') {
    window.pendingFiles = [];
}

// 为了保持代码兼容性，创建对window.pendingFiles的引用
const pendingFiles = window.pendingFiles;

// 显示上传附件对话框
function showUploadDialog(stage, type) {
    // 清空待上传文件列表
    window.pendingFiles = [];

    // 确保阶段和角色有效
    if (!stage || !type) {
        Logger.error(`Invalid stage or role: ${stage}-${type}`);
        showNotification('error', '无法上传附件', '无效的阶段或角色');
        return;
    }

    // 不再检查提交状态，允许在任何时候上传附件
    window.currentUploadStage = stage;
    window.currentUploadType = type;

    Logger.log(`设置上传阶段和角色: ${stage}-${type}`);

    // 直接使用阶段和角色名称
    const apiStage = stage;
    const apiRole = type;

    // 确保提交状态数据结构存在
    if (!inspectionData.pageState.inspectionSubmissions) {
        inspectionData.pageState.inspectionSubmissions = {};
    }

    if (!inspectionData.pageState.inspectionSubmissions[apiStage]) {
        inspectionData.pageState.inspectionSubmissions[apiStage] = {};
    }

    if (!inspectionData.pageState.inspectionSubmissions[apiStage][apiRole]) {
        inspectionData.pageState.inspectionSubmissions[apiStage][apiRole] = {
            operator: "",
            submittedAt: "",
            isSubmitted: false,
            isFinalSubmission: false
        };
    }

    // 检查是否已提交，用于显示不同的提示信息
    const isSubmitted = inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].isSubmitted;

    // 更新对话框标题
    const stageTitle = getDefaultStageTitle(stage);
    let typeTitle = '';
    if (type === window.ROLES.FIRST) {
        typeTitle = '首检';
    } else if (type === window.ROLES.SELF) {
        typeTitle = '自检';
    } else { // window.ROLES.IPQC
        typeTitle = 'IPQC';
    }

    // 使用SweetAlert2创建上传对话框
    SweetAlert.custom({
        title: `上传检验附件`,
        html: `
            <p class="swal2-html-container-custom">为"${stageTitle} ${typeTitle}"上传附件</p>
            <div class="self-inspection__upload-section">
                <div class="self-inspection__upload-title">已上传文件</div>
                <div id="sweetalert-file-list" class="self-inspection__file-list"></div>
                <div class="self-inspection__upload-title">待上传文件</div>
                <div id="pending-file-list" class="self-inspection__file-list"></div>
            </div>
            <div class="self-inspection__upload-info">
                <p><strong>支持的文件类型：</strong>图片(JPG/PNG/GIF)、PDF文档</p>
                <p><strong>文件大小限制：</strong>最大10MB</p>
                ${isSubmitted ? '<p class="self-inspection__upload-warning"><strong>注意：</strong>检验已提交，上传的附件将不可删除</p>' : '<p class="self-inspection__upload-note"><strong>注意：</strong>请确保上传的文档正确，上传后将不可删除</p>'}
            </div>
            <label for="sweetalert-file-upload" class="self-inspection__upload-btn">
                选择文件
                <input type="file" id="sweetalert-file-upload" class="self-inspection__file-upload-input" accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.pdf">
            </label>
        `,
        showCancelButton: true,
        confirmButtonText: '完成',
        cancelButtonText: '关闭',
        customClass: {
            container: 'self-inspection-swal-container',
            popup: 'self-inspection-swal-popup',
            title: 'self-inspection-swal-title',
            htmlContainer: 'self-inspection-swal-html-container',
            actions: 'self-inspection-swal-actions',
            confirmButton: 'self-inspection-swal-confirm-button',
            cancelButton: 'self-inspection-swal-cancel-button'
        },
        didOpen: () => {
            // 渲染已上传文件列表
            renderSweetAlertFileList();

            // 渲染待上传文件列表
            renderPendingFileList();

            // 添加文件选择事件监听
            const fileUploadInput = document.getElementById('sweetalert-file-upload');
            if (fileUploadInput) {
                fileUploadInput.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        const file = e.target.files[0];

                        // 验证文件
                        if (validateFile(file)) {
                            // 添加到待上传列表
                            addToPendingFiles(file);

                            // 清空input，允许选择相同文件
                            fileUploadInput.value = '';
                        }
                    }
                });
            }

            // 将必要的函数暴露到全局作用域，以便在HTML中调用
            window.removeFromPendingFiles = removeFromPendingFiles;
        },
        preConfirm: () => {
            // 如果没有待上传文件，直接关闭对话框
            if (pendingFiles.length === 0) {
                return true;
            }

            // 点击完成按钮时，显示确认提示
            return SweetAlert.confirm(
                `您选择了${pendingFiles.length}个文件准备上传，请确认所有文档已正确选择，上传后的附件将不可删除。`,
                '确认上传文件'
            ).then(confirmed => {
                if (!confirmed) {
                    return false; // 阻止对话框关闭
                }

                // 上传所有待上传文件
                uploadPendingFiles();
                return true;
            });
        }
    });
}

// 添加文件到待上传列表
function addToPendingFiles(file) {
    // 检查是否已存在相同文件名的文件
    const existingIndex = pendingFiles.findIndex(f => f.name === file.name);
    if (existingIndex !== -1) {
        // 替换已存在的文件
        pendingFiles[existingIndex] = file;
    } else {
        // 添加新文件
        pendingFiles.push(file);
    }

    // 更新待上传文件列表
    renderPendingFileList();
}

// 从待上传列表中移除文件
function removeFromPendingFiles(index) {
    pendingFiles.splice(index, 1);
    renderPendingFileList();
}

// 渲染待上传文件列表
function renderPendingFileList() {
    const container = document.getElementById('pending-file-list');
    if (!container) return;

    if (pendingFiles.length === 0) {
        container.innerHTML = '<p>暂无待上传文件</p>';
        return;
    }

    container.innerHTML = '';

    pendingFiles.forEach((file, index) => {
        const fileType = getFileType(file.name);
        const fileSize = formatFileSize(file.size);

        const fileHtml = `
            <div class="self-inspection__attachment-item">
                <div class="self-inspection__attachment-icon">${getFileIcon(fileType)}</div>
                <div class="self-inspection__attachment-info">
                    <div class="self-inspection__attachment-name" onclick="previewPendingFile(${index})">${file.name}</div>
                    <div class="self-inspection__attachment-size">${fileSize}</div>
                </div>
                <div class="self-inspection__attachment-delete" onclick="removeFromPendingFiles(${index})">×</div>
            </div>
        `;

        container.innerHTML += fileHtml;
    });
}

// 预览待上传文件
function previewPendingFile(index) {
    if (index < 0 || index >= pendingFiles.length) {
        showNotification('error', '无法预览', '找不到文件信息');
        return;
    }

    const file = pendingFiles[index];
    const fileName = file.name;
    const fileType = getFileType(fileName);

    // 创建预览模态框
    const modal = document.createElement('div');
    modal.className = 'file-preview-modal';
    modal.id = 'filePreviewModal';
    modal.style.display = 'flex';

    // 根据文件类型创建不同的预览内容
    let previewContent = '';

    // 为待上传文件创建临时URL，用于预览
    const objectUrl = URL.createObjectURL(file);

    if (fileType === 'image') {
        previewContent = `
            <img src="${objectUrl}" class="file-preview-image" alt="${fileName}" onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\\'file-preview-error\\'><p>图片加载失败</p></div>';">
        `;
    } else if (fileType === 'pdf') {
        previewContent = `
            <iframe src="${objectUrl}" class="file-preview-pdf" onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\\'file-preview-error\\'><p>PDF加载失败</p></div>';"></iframe>
        `;
    } else {
        previewContent = `
            <div class="file-preview-error">
                <p>无法预览此类型的文件</p>
            </div>
        `;
    }

    modal.innerHTML = `
        <div class="file-preview-content">
            <div class="file-preview-header">
                <h3 class="file-preview-title">${fileName}</h3>
                <div class="file-preview-close" onclick="closeFilePreview()">&times;</div>
            </div>
            <div class="file-preview-body">
                ${previewContent}
            </div>
        </div>
    `;

    // 添加到文档中
    document.body.appendChild(modal);

    // 添加ESC键关闭功能
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeFilePreview();
        }
    });

    // 点击模态框背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeFilePreview();
        }
    });
}

// 上传所有待上传文件
function uploadPendingFiles() {
    if (pendingFiles.length === 0) return;

    // 调试日志
    Logger.log(`准备上传文件到阶段和角色: ${window.currentUploadStage}-${window.currentUploadType}`);

    // 显示加载中
    showLoading();

    // 创建一个计数器，用于跟踪上传完成的文件数量
    let completedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    // 上传每个文件
    pendingFiles.forEach(file => {
        // 使用全局变量而不是局部变量
        uploadFile(file, window.currentUploadStage, window.currentUploadType, false)
            .then(success => {
                completedCount++;
                if (success) {
                    successCount++;
                } else {
                    errorCount++;
                }

                // 检查是否所有文件都已上传完成
                if (completedCount === pendingFiles.length) {
                    hideLoading();

                    // 显示上传结果
                    if (errorCount === 0) {
                        showNotification('success', '上传成功', `已成功上传${successCount}个文件`);
                    } else if (successCount === 0) {
                        showNotification('error', '上传失败', `所有文件上传失败`);
                    } else {
                        showNotification('warning', '部分上传成功', `成功上传${successCount}个文件，${errorCount}个文件上传失败`);
                    }

                    // 清空待上传文件列表
                    window.pendingFiles = [];

                    // 更新附件列表
                    renderAttachmentFiles(window.currentUploadStage, window.currentUploadType);
                }
            });
    });
}

// 获取文件类型
function getFileType(fileName) {
    if (!fileName) return 'unknown';

    const extension = fileName.split('.').pop().toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
        return 'image';
    } else if (['pdf'].includes(extension)) {
        return 'pdf';
    } else if (['doc', 'docx'].includes(extension)) {
        return 'word';
    } else if (['xls', 'xlsx'].includes(extension)) {
        return 'excel';
    } else if (['ppt', 'pptx'].includes(extension)) {
        return 'powerpoint';
    } else if (['txt', 'log', 'md'].includes(extension)) {
        return 'text';
    } else {
        return 'unknown';
    }
}

// 获取文件图标
function getFileIcon(fileType) {
    switch (fileType) {
        case 'image':
            return '<i class="fas fa-file-image"></i>';
        case 'pdf':
            return '<i class="fas fa-file-pdf"></i>';
        case 'word':
            return '<i class="fas fa-file-word"></i>';
        case 'excel':
            return '<i class="fas fa-file-excel"></i>';
        case 'powerpoint':
            return '<i class="fas fa-file-powerpoint"></i>';
        case 'text':
            return '<i class="fas fa-file-alt"></i>';
        default:
            return '<i class="fas fa-file"></i>';
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 验证文件类型和大小
function validateFile(file) {
    // 检查文件类型
    const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/bmp',
        'image/webp',
        'application/pdf'
    ];

    if (!allowedTypes.includes(file.type)) {
        showNotification('error', '文件类型不支持', '请上传图片(JPG/PNG/GIF)或PDF文档');
        return false;
    }

    // 检查文件大小 (10MB = 10 * 1024 * 1024 bytes)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        showNotification('error', '文件过大', '文件大小不能超过10MB');
        return false;
    }

    return true;
}

// 上传文件到服务器
function uploadFileToServer(file) {
    // 验证文件类型和大小
    if (!validateFile(file)) {
        return;
    }

    // 如果工单ID不存在，先创建工单
    if (!inspectionData.currentWorkOrderId) {
        const orderNo = document.getElementById('orderNo').value.trim();
        if (!orderNo) {
            showNotification('error', '上传失败', '请先填写工单号');
            return;
        }

        // 显示加载中
        showLoading();

        // 先检查工单是否存在于工单管理系统中
        fetch(`/api/sn-print-record/get-work-order-details?processOrder=${encodeURIComponent(orderNo)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.order) {
                    // 工单存在于工单管理系统中，继续创建质检工单
                    // 创建工单后再上传文件
                    return fetch('/api/quality-inspection/work-order', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            work_order_no: orderNo,
                            product_type_id: inspectionData.currentProductTypeId
                        })
                    });
                } else {
                    // 工单不存在于工单管理系统中，禁止创建
                    hideLoading();
                    showNotification('error', '工单不存在', '该工单号在工单管理系统中不存在，无法创建质检工单');
                    throw new Error('工单不存在于工单管理系统中');
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success) {
                    inspectionData.currentWorkOrderId = data.data.id;
                    // 添加到待上传列表，而不是直接上传
                    addToPendingFiles(file);
                } else {
                    showNotification('error', '上传失败', data.message || '无法创建工单');
                }
            })
            .catch(error => {
                if (error.message === '工单不存在于工单管理系统中') {
                    // 已经显示了错误消息，不需要再次显示
                    return;
                }
                hideLoading();
                Logger.error('Error creating work order:', error);
                showNotification('error', '上传失败', '网络错误，请稍后重试');
            });
    } else {
        // 添加到待上传列表，而不是直接上传
        addToPendingFiles(file);
    }
}

// 上传文件
function uploadFile(file, stage, type, showNotifications = true) {
    return new Promise((resolve) => {
        const apiStage = stage;
        const apiRole = type;

        // 确保阶段和角色有效
        if (!apiStage || !apiRole) {
            Logger.error(`Invalid stage or role: ${apiStage}-${apiRole}`);
            if (showNotifications) {
                showNotification('error', '上传失败', '无效的阶段或角色');
            }
            resolve(false);
            return;
        }

        // 获取操作人员
        // 修改开始：优先使用 window.currentLoggedInUser
        let determinedOperator = '';
        if (window.currentLoggedInUser) {
            determinedOperator = window.currentLoggedInUser;
        } else {
            // 后备方案：尝试从输入框获取 (主要用于未提交状态或 window.currentLoggedInUser 未设置的情况)
            const operatorInputContext = document.querySelector(`#${apiStage}-${apiRole}-operator .self-inspection__operator-input input`);
            if (operatorInputContext && operatorInputContext.value.trim()) {
                determinedOperator = operatorInputContext.value.trim();
            }
        }
        // 修改结束

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        formData.append('work_order_id', inspectionData.currentWorkOrderId);
        formData.append('stage', apiStage);
        formData.append('inspector_role', apiRole);
        formData.append('uploaded_by', determinedOperator || '未知操作员'); // 使用获取到的操作员

        // 确保提交状态数据结构存在
        if (!inspectionData.pageState.inspectionSubmissions) {
            inspectionData.pageState.inspectionSubmissions = {};
        }

        if (!inspectionData.pageState.inspectionSubmissions[apiStage]) {
            inspectionData.pageState.inspectionSubmissions[apiStage] = {};
        }

        if (!inspectionData.pageState.inspectionSubmissions[apiStage][apiRole]) {
            inspectionData.pageState.inspectionSubmissions[apiStage][apiRole] = {
                operator: "",
                submittedAt: "",
                isSubmitted: false,
                isFinalSubmission: false
            };
        }

        // 检查是否已提交，如果已提交，标记附件为不可删除
        const isSubmitted = inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].isSubmitted;
        formData.append('non_deletable', isSubmitted ? 'true' : 'false');

        // 调用API上传文件
        fetch('/api/quality-inspection/attachments', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 添加到附件列表
                const newFile = {
                    id: data.data.id,
                    name: data.data.file_name,
                    type: getFileType(data.data.file_name),
                    size: formatFileSize(data.data.file_size),
                    url: data.data.file_url, // 确保存储文件URL
                    nonDeletable: isSubmitted || data.data.non_deletable === true // 如果已提交或后端标记为不可删除，则设置为不可删除
                };

                // 确保附件数据结构存在
                if (!inspectionData.pageState.attachments) {
                    inspectionData.pageState.attachments = {};
                }

                if (!inspectionData.pageState.attachments[apiStage]) {
                    inspectionData.pageState.attachments[apiStage] = {};
                }

                if (!inspectionData.pageState.attachments[apiStage][apiRole]) {
                    inspectionData.pageState.attachments[apiStage][apiRole] = [];
                }

                inspectionData.pageState.attachments[apiStage][apiRole].push(newFile);

                // 如果需要显示通知，则更新UI并显示通知
                if (showNotifications) {
                    // 更新附件列表
                    renderSweetAlertFileList();
                    renderAttachmentFiles(stage, type);

                    // 显示成功通知
                    showNotification('success', '上传成功', '文件已成功上传');
                }

                resolve(true);
            } else {
                if (showNotifications) {
                    showNotification('error', '上传失败', data.message || '文件上传失败');
                }
                Logger.error('Error uploading file:', data.message);
                resolve(false);
            }
        })
        .catch(error => {
            if (showNotifications) {
                showNotification('error', '上传失败', '网络错误，请稍后重试');
            }
            Logger.error('Error uploading file:', error);
            resolve(false);
        });
    });
}

// 渲染SweetAlert2文件列表
function renderSweetAlertFileList() {
    const container = document.getElementById('sweetalert-file-list');
    if (!container) return;

    container.innerHTML = '';

    // 直接使用阶段和角色名称
    const apiStage = window.currentUploadStage;
    const apiRole = window.currentUploadType;

    // 确保阶段和角色有效
    if (!apiStage || !apiRole) {
        Logger.error(`Invalid stage or role: ${apiStage}-${apiRole}`);
        container.innerHTML = '<p>无效的阶段或角色</p>';
        return;
    }

    // 确保inspectionData和pageState存在
    if (!inspectionData || !inspectionData.pageState) {
        Logger.error('inspectionData or pageState is undefined');
        container.innerHTML = '<p>数据结构未初始化</p>';
        return;
    }

    // 确保附件数据结构存在
    if (!inspectionData.pageState.attachments) {
        inspectionData.pageState.attachments = {};
    }

    if (!inspectionData.pageState.attachments[apiStage]) {
        inspectionData.pageState.attachments[apiStage] = {};
    }

    if (!inspectionData.pageState.attachments[apiStage][apiRole]) {
        inspectionData.pageState.attachments[apiStage][apiRole] = [];
    }

    const files = inspectionData.pageState.attachments[apiStage][apiRole];

    // 确保提交状态数据结构存在
    if (!inspectionData.pageState.inspectionSubmissions) {
        inspectionData.pageState.inspectionSubmissions = {};
    }

    if (!inspectionData.pageState.inspectionSubmissions[apiStage]) {
        inspectionData.pageState.inspectionSubmissions[apiStage] = {};
    }

    if (!inspectionData.pageState.inspectionSubmissions[apiStage][apiRole]) {
        inspectionData.pageState.inspectionSubmissions[apiStage][apiRole] = {
            operator: "",
            submittedAt: "",
            isSubmitted: false,
            isFinalSubmission: false
        };
    }

    // 检查是否已提交，用于决定是否显示删除按钮
    const isSubmitted = inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].isSubmitted;

    if (files.length === 0) {
        container.innerHTML = '<p>暂无已上传文件</p>';
        return;
    }

    files.forEach(file => {
        // 如果已提交或文件标记为不可删除，则不显示删除按钮
        const deleteButton = (!isSubmitted && !file.nonDeletable)
            ? `<div class="self-inspection__attachment-delete" onclick="deleteAttachment(${file.id})">×</div>`
            : '';

        const fileHtml = `
            <div class="self-inspection__attachment-item">
                <div class="self-inspection__attachment-icon">${getFileIcon(file.type)}</div>
                <div class="self-inspection__attachment-info">
                    <div class="self-inspection__attachment-name" onclick="previewFile('${file.url || ''}', '${file.name}', '${file.type}')">${file.name}</div>
                    <div class="self-inspection__attachment-size">${file.size}</div>
                </div>
                ${deleteButton}
            </div>
        `;

        container.innerHTML += fileHtml;
    });
}

// 删除附件的函数
function deleteAttachment(attachmentId) {
    // 首先检查附件是否可删除
    let canDelete = false;
    let targetStage = null;
    let targetRole = null;
    let targetFile = null;

    // 查找附件并检查是否可删除
    for (const apiStage in inspectionData.pageState.attachments) {
        for (const apiRole in inspectionData.pageState.attachments[apiStage]) {
            const files = inspectionData.pageState.attachments[apiStage][apiRole];
            const file = files.find(f => f.id === attachmentId);

            if (file) {
                targetStage = apiStage;
                targetRole = apiRole;
                targetFile = file;

                // 检查是否已提交或文件标记为不可删除
                const isSubmitted = inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].isSubmitted;
                canDelete = !isSubmitted && !file.nonDeletable;

                break;
            }
        }
        if (targetFile) break;
    }

    // 如果找不到附件或附件不可删除，显示错误提示
    if (!targetFile) {
        showNotification('error', '删除失败', '找不到指定的附件');
        return;
    }

    if (!canDelete) {
        showNotification('warning', '无法删除', '该附件已提交或标记为不可删除');
        return;
    }

    // 使用SweetAlert2确认对话框
    SweetAlert.confirm(
        '是否确认删除附件？一旦删除将不可恢复。',
        '确认删除附件'
    ).then(confirmed => {
        if (confirmed) {
            showLoading();

            // 调用API删除附件
            fetch(`/api/quality-inspection/attachments/${attachmentId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success) {
                    // 从附件列表中删除
                    const files = inspectionData.pageState.attachments[targetStage][targetRole];
                    const index = files.findIndex(f => f.id === attachmentId);
                    if (index !== -1) {
                        files.splice(index, 1);

                        // 更新附件列表
                        renderSweetAlertFileList();
                        renderAttachmentFiles(targetStage, targetRole);
                    }

                    showNotification('success', '删除成功', '附件已成功删除');
                } else {
                    showNotification('error', '删除失败', data.message || '无法删除附件');
                }
            })
            .catch(error => {
                hideLoading();
                Logger.error('Error deleting attachment:', error);
                showNotification('error', '删除失败', '网络错误，请稍后重试');
            });
        }
    });
}

// 显示加载中
function showLoading() {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: '处理中...',
            allowOutsideClick: false,
            allowEscapeKey: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }
}

// 隐藏加载中
function hideLoading() {
    if (typeof Swal !== 'undefined') {
        Swal.close();
    }
}

// 初始化数据
function initializeDatabase() {
    Logger.log('加载产品类型数据...');

    // 显示加载中
    showLoading();

    // 直接加载产品类型，假设数据库已经通过脚本初始化
    loadProductTypes();
}

// 初始化产品类型 - 此函数已不再使用，保留仅为兼容
function initProductTypes() {
    Logger.log('初始化产品类型功能已禁用，直接加载现有产品类型...');
    showNotification('info', '提示', '系统使用预先配置的产品类型数据');

    // 直接加载产品类型
    loadProductTypes();
}

// 以下初始化函数已不再使用，保留仅为兼容
// 在生产环境中，这些初始化操作应该通过数据库脚本完成
function initInspectionItemsForPLC5(/* productTypeId */) {
    Logger.log('五代PLC检验项目初始化功能已禁用');
    return Promise.resolve({ success: true });
}

function initInspectionItemsForPLC4(/* productTypeId */) {
    Logger.log('四代PLC检验项目初始化功能已禁用');
    return Promise.resolve({ success: true });
}

// 处理从后端加载的提交记录
function processSubmissions(submissions) {
    if (!submissions || submissions.length === 0) {
        Logger.log("No existing submissions found for this work order.");
        return;
    }

    Logger.log("Processing loaded submissions:", submissions);

    // 先重置所有提交状态，确保只使用后端加载的数据
    for (const stageKey in inspectionData.pageState.inspectionSubmissions) {
        for (const roleKey in inspectionData.pageState.inspectionSubmissions[stageKey]) {
            inspectionData.pageState.inspectionSubmissions[stageKey][roleKey] = {
                operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false
            };
        }
    }

    // 从新的API返回格式中提取提交记录
    submissions.forEach(sub => {
        const apiStage = sub.stage; // 后端 stage (assembly, test, packaging)
        const apiRole = sub.inspector_role; // 后端 role (first, self, ipqc)

        if (inspectionData.pageState.inspectionSubmissions[apiStage] &&
            inspectionData.pageState.inspectionSubmissions[apiStage][apiRole])
        {
            // 判断提交类型
            const isFinalSubmission = sub.is_final || (sub.submission_type === 'final');

            // 更新提交状态 - 只有最终提交才设置isSubmitted为true
            inspectionData.pageState.inspectionSubmissions[apiStage][apiRole] = {
                operator: sub.submitted_by,
                submittedAt: sub.submitted_at,
                isSubmitted: isFinalSubmission, // 只有最终提交才标记为已提交
                isFinalSubmission: isFinalSubmission
            };

            // 重要：如果有提交记录且是最终提交，将该角色的所有检验项目标记为已完成
            if (isFinalSubmission &&
                inspectionData.pageState.inspectionResults[apiStage] &&
                inspectionData.pageState.inspectionResults[apiStage][apiRole]) {
                // 将所有项目设置为已选中
                inspectionData.pageState.inspectionResults[apiStage][apiRole] =
                    inspectionData.pageState.inspectionResults[apiStage][apiRole].map(() => true);
            }

            Logger.log(`Set submission status for ${apiStage}-${apiRole} to ${isFinalSubmission ? 'finally submitted' : 'partially submitted'}.`);
        } else {
            Logger.warn(`Could not map loaded submission for stage ${apiStage}, role ${apiRole}`);
        }
    });

    // 检查每个阶段是否完成，并更新UI
    for (const apiStage in inspectionData.pageState.inspectionSubmissions) {
        const submissions = inspectionData.pageState.inspectionSubmissions[apiStage];
        // 只有所有角色都是最终提交，才判断为完成
        const allFinallySubmitted =
            submissions[window.ROLES.FIRST].isFinalSubmission &&
            submissions[window.ROLES.SELF].isFinalSubmission &&
            submissions[window.ROLES.IPQC].isFinalSubmission;

        if (allFinallySubmitted) {
            // 更新阶段节点状态为已完成
            const uiStage = apiStage;
            const stageNode = document.querySelector(`#${uiStage}-stage .self-inspection__stage-node`);
            const stageCard = document.querySelector(`#${uiStage}-stage .self-inspection__stage-card`);

            if (stageNode) {
                stageNode.classList.add('self-inspection__stage-node--completed');
                stageNode.innerHTML = '✓';
            }

            if (stageCard) {
                stageCard.classList.add('self-inspection__stage-card--completed');
            }

            // 解锁下一阶段
            if (apiStage === window.STAGES.ASSEMBLY) {
                unlockStage(window.STAGES.TEST);
            } else if (apiStage === window.STAGES.TEST) {
                unlockStage(window.STAGES.PACKAGING);
            }

            // 更新阶段进度为100%
            updateStageProgress(apiStage);
        }
    }
}

// 添加渲染附件列表的函数
function renderAttachmentFiles(stage, type) {
    // 确保阶段和角色有效
    if (!stage || !type) {
        Logger.error(`Invalid stage or role: ${stage}-${type}`);
        return;
    }

    const container = document.getElementById(`${stage}-${type}-files`);
    if (!container) {
        Logger.error(`Container not found for ${stage}-${type}-files`);
        return;
    }

    container.innerHTML = '';

    // 直接使用阶段和角色名称
    const apiStage = stage;
    const apiRole = type;

    // 确保inspectionData和pageState存在
    if (!inspectionData || !inspectionData.pageState) {
        Logger.error('inspectionData or pageState is undefined');
        container.innerHTML = '<p>数据结构未初始化</p>';
        return;
    }

    // 确保附件数据结构存在
    if (!inspectionData.pageState.attachments) {
        inspectionData.pageState.attachments = {};
    }

    if (!inspectionData.pageState.attachments[apiStage]) {
        inspectionData.pageState.attachments[apiStage] = {};
    }

    if (!inspectionData.pageState.attachments[apiStage][apiRole]) {
        inspectionData.pageState.attachments[apiStage][apiRole] = [];
    }

    const files = inspectionData.pageState.attachments[apiStage][apiRole];

    // 确保提交状态数据结构存在
    if (!inspectionData.pageState.inspectionSubmissions) {
        inspectionData.pageState.inspectionSubmissions = {};
    }

    if (!inspectionData.pageState.inspectionSubmissions[apiStage]) {
        inspectionData.pageState.inspectionSubmissions[apiStage] = {};
    }

    if (!inspectionData.pageState.inspectionSubmissions[apiStage][apiRole]) {
        inspectionData.pageState.inspectionSubmissions[apiStage][apiRole] = {
            operator: "",
            submittedAt: "",
            isSubmitted: false,
            isFinalSubmission: false
        };
    }

    // 检查是否已提交，用于决定是否显示删除按钮
    const isSubmitted = inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].isSubmitted;

    if (files.length === 0) {
        container.innerHTML = '<p>暂无已上传文件</p>';
        return;
    }

    files.forEach(file => {
        // 如果已提交或文件标记为不可删除，则不显示删除按钮
        const deleteButton = (!isSubmitted && !file.nonDeletable)
            ? `<div class="self-inspection__attachment-delete" onclick="deleteAttachment(${file.id})">×</div>`
            : '';

        const fileHtml = `
            <div class="self-inspection__attachment-item">
                <div class="self-inspection__attachment-icon">${getFileIcon(file.type)}</div>
                <div class="self-inspection__attachment-info">
                    <div class="self-inspection__attachment-name" onclick="previewFile('${file.url || ''}', '${file.name}', '${file.type}')">${file.name}</div>
                    <div class="self-inspection__attachment-size">${file.size}</div>
                </div>
                ${deleteButton}
            </div>
        `;

        container.innerHTML += fileHtml;
    });
}

// 注释：setupAutoRefresh函数已在下方定义，此处不再重复

// 更新工单状态显示
function updateWorkOrderStatus() {
    const statusContainer = document.getElementById('workOrderStatusOverview');
    if (!statusContainer) return;

    // 显示状态概览区域
    statusContainer.style.display = 'block';

    // 阶段映射
    const stageNames = {
        [window.STAGES.ASSEMBLY]: '组装前',
        [window.STAGES.TEST]: '测试前',
        [window.STAGES.PACKAGING]: '包装前'
    };

    // 生成状态表格内容
    let tableContent = `
        <div class="self-inspection__status-header">
            <div class="self-inspection__status-cell">阶段/角色</div>
            <div class="self-inspection__status-cell">首检</div>
            <div class="self-inspection__status-cell">自检</div>
            <div class="self-inspection__status-cell">IPQC</div>
        </div>
    `;

    // 为每个阶段生成状态行
    for (const apiStage in inspectionData.pageState.inspectionSubmissions) {
        const submissions = inspectionData.pageState.inspectionSubmissions[apiStage];

        tableContent += `
            <div class="self-inspection__status-row">
                <div class="self-inspection__status-cell">${stageNames[apiStage] || apiStage}</div>
        `;

        // 添加每个角色的状态
        for (const apiRole of [window.ROLES.FIRST, window.ROLES.SELF, window.ROLES.IPQC]) {
            const isSubmitted = submissions[apiRole].isSubmitted;
            const operator = submissions[apiRole].operator;
            const submittedAt = submissions[apiRole].submittedAt;

            if (isSubmitted) {
                tableContent += `
                    <div class="self-inspection__status-cell self-inspection__status-completed">
                        <div class="self-inspection__status-icon">✓</div>
                        <div class="self-inspection__status-info">
                            <div>${operator}</div>
                            <div class="self-inspection__status-time">${submittedAt}</div>
                        </div>
                    </div>
                `;
            } else {
                tableContent += `
                    <div class="self-inspection__status-cell self-inspection__status-pending">
                        <div class="self-inspection__status-icon">-</div>
                        <div class="self-inspection__status-info">未完成</div>
                    </div>
                `;
            }
        }

        tableContent += `</div>`;
    }

    // 更新表格内容
    const tableElement = statusContainer.querySelector('.self-inspection__status-table');
    if (tableElement) {
        tableElement.innerHTML = tableContent;
    }

    Logger.log('工单状态显示已更新');
}

// 添加自动刷新功能
function setupAutoRefresh() {
    // 每60秒刷新一次工单状态
    const refreshInterval = 60000; // 60秒

    // 清除可能存在的旧定时器
    if (window.workOrderRefreshTimer) {
        clearInterval(window.workOrderRefreshTimer);
    }

    // 设置新的定时器
    window.workOrderRefreshTimer = setInterval(() => {
        const orderNo = document.getElementById('orderNo').value.trim();
        if (orderNo && inspectionData.currentWorkOrderId) {
            // 静默刷新工单数据（不显示加载动画）
            refreshWorkOrderData(orderNo);
        }
    }, refreshInterval);
}

// 显示返工工单标识
function showReworkWorkOrderBadge() {
    const workOrderInfoElement = document.querySelector('.self-inspection__order-info');
    if (workOrderInfoElement) {
        // 检查是否已存在返工标识
        if (!workOrderInfoElement.querySelector('.rework-badge')) {
            const badge = document.createElement('div');
            badge.className = 'rework-badge';
            badge.textContent = '返工工单';
            badge.style.display = 'inline-block';
            badge.style.backgroundColor = '#ff9800';
            badge.style.color = 'white';
            badge.style.padding = '2px 8px';
            badge.style.borderRadius = '4px';
            badge.style.fontSize = '12px';
            badge.style.marginLeft = '10px';
            badge.style.verticalAlign = 'middle';

            // 添加到工单信息区域
            const productTypeGroup = document.getElementById('productTypeGroup');
            if (productTypeGroup) {
                productTypeGroup.appendChild(badge);
            } else {
                workOrderInfoElement.appendChild(badge);
            }
        }
    }
}

// 静默刷新工单数据
function refreshWorkOrderData(orderNo) {
    // 确保工单号被正确编码
    const encodedOrderNo = encodeURIComponent(orderNo);
    fetch(`/api/quality-inspection/work-order/${encodedOrderNo}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) { // Ensure data.data exists
                // 先重置提交状态，确保只使用后端返回的提交记录
                for (const stageKey in inspectionData.pageState.inspectionSubmissions) {
                    for (const roleKey in inspectionData.pageState.inspectionSubmissions[stageKey]) {
                        inspectionData.pageState.inspectionSubmissions[stageKey][roleKey] = {
                            operator: "", submittedAt: "", isSubmitted: false, isFinalSubmission: false
                        };
                    }
                }

                // 处理检验记录和提交状态
                processRecords(data.data.records); // This might be for older data or other purposes
                processAttachments(data.data.attachments);

                // ** MODIFIED SECTION TO HANDLE inspection_statuses SIMILAR TO processWorkOrderData **
                if (data.data.inspection_statuses) {
                    const submissionsToProcess = [];
                    const stagesMap = {};

                    // Group by stage and role, taking the latest inspection_time for each combination
                    data.data.inspection_statuses.forEach(status => {
                        const key = `${status.stage}-${status.inspector_role}`;
                        if (!stagesMap[key] || new Date(status.inspection_time) > new Date(stagesMap[key].inspection_time)) {
                            stagesMap[key] = status;
                        }
                    });

                    // Convert to the format expected by processSubmissions
                    for (const key in stagesMap) {
                        const status = stagesMap[key];
                        submissionsToProcess.push({
                            stage: status.stage,
                            inspector_role: status.inspector_role,
                            submitted_by: status.inspector, // Map 'inspector' to 'submitted_by'
                            submitted_at: status.inspection_time, // Map 'inspection_time' to 'submitted_at'
                            is_final: status.is_final // Backend already provides 'is_final' derived from submission_type
                        });
                    }
                    processSubmissions(submissionsToProcess);
                } else if (data.data.submissions) { // Fallback for older API structure if needed
                    processSubmissions(data.data.submissions);
                }
                // ** END MODIFIED SECTION **

                // 更新UI
                updateProgress();
                updateWorkOrderStatus();

                Logger.log('工单数据已自动刷新');
            }
        })
        .catch(error => {
            Logger.error('自动刷新工单数据失败:', error);
        });
}

// 处理从后端加载的附件
function processAttachments(attachments) {
    if (!attachments || attachments.length === 0) {
        Logger.log("No existing attachments found for this work order.");
        return;
    }

    Logger.log("Processing loaded attachments:", attachments);

    // 先重置所有附件，确保只使用后端加载的数据
    for (const stageKey in inspectionData.pageState.attachments) {
        for (const roleKey in inspectionData.pageState.attachments[stageKey]) {
            inspectionData.pageState.attachments[stageKey][roleKey] = [];
        }
    }

    // 处理附件数据
    attachments.forEach(attachment => {
        const apiStage = attachment.stage;
        const apiRole = attachment.inspector_role;

        if (inspectionData.pageState.attachments[apiStage] &&
            inspectionData.pageState.attachments[apiStage][apiRole]) {

            // 检查是否已提交，用于决定附件是否可删除
            const isSubmitted = inspectionData.pageState.inspectionSubmissions[apiStage][apiRole].isSubmitted;

            // 添加到附件列表
            const newFile = {
                id: attachment.id,
                name: attachment.file_name,
                type: getFileType(attachment.file_name),
                size: formatFileSize(attachment.file_size || 0),
                url: attachment.file_url,
                nonDeletable: isSubmitted || attachment.non_deletable === true // 如果已提交或后端标记为不可删除，则设置为不可删除
            };

            inspectionData.pageState.attachments[apiStage][apiRole].push(newFile);
            Logger.log(`Added attachment for ${apiStage}-${apiRole}: ${attachment.file_name}, deletable: ${!newFile.nonDeletable}`);
        } else {
            Logger.warn(`Could not map loaded attachment for stage ${apiStage}, role ${apiRole}`);
        }
    });
}

// 渲染所有附件
function renderAllAttachments() {
    // 遍历所有阶段和角色
    for (const apiStage in inspectionData.pageState.attachments) {
        const stage = apiStage;
        for (const apiRole in inspectionData.pageState.attachments[apiStage]) {
            const role = apiRole;
            renderAttachmentFiles(stage, role);
        }
    }
}

// 文件预览功能
function previewFile(url, fileName, fileType) {
    // 获取文件ID
    const fileId = findFileIdByName(fileName);

    if (!fileId) {
        // 如果找不到文件ID，但有URL（可能是待上传文件的临时URL）
        if (url) {
            showFilePreviewModal(url, fileName, fileType);
        } else {
            showNotification('error', '无法预览', '找不到文件信息');
        }
        return;
    }

    // 构建文件下载URL
    const fileUrl = `/api/quality-inspection/attachments/${fileId}/file`;
    Logger.log(`构建文件下载URL: ${fileUrl}`);

    // 显示文件预览模态框
    showFilePreviewModal(fileUrl, fileName, fileType);
}

// 根据文件名查找文件ID
function findFileIdByName(fileName) {
    // 遍历所有附件，查找匹配的文件名
    for (const apiStage in inspectionData.pageState.attachments) {
        for (const apiRole in inspectionData.pageState.attachments[apiStage]) {
            const files = inspectionData.pageState.attachments[apiStage][apiRole];
            const file = files.find(f => f.name === fileName);
            if (file) {
                return file.id;
            }
        }
    }
    return null;
}

// 显示文件预览模态框
function showFilePreviewModal(url, fileName, fileType) {
    // 创建预览模态框
    const modal = document.createElement('div');
    modal.className = 'file-preview-modal';
    modal.id = 'filePreviewModal';
    modal.style.display = 'flex';

    // 根据文件类型创建不同的预览内容
    let previewContent = '';

    // 检查是否是blob URL（待上传文件的临时URL）
    if (url.startsWith('blob:')) {
        // 对于blob URL，直接显示预览，不进行HEAD请求
        Logger.log('检测到blob URL，直接显示预览');
        continueShowPreview();
    } else {
        // 对于非blob URL，先检查URL是否有效
        fetch(url, { method: 'HEAD' })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    throw new Error('服务器返回了JSON而不是文件');
                }

                // URL有效，继续显示预览
                continueShowPreview();
            })
            .catch(error => {
                Logger.error('Error checking file URL:', error);
                // 显示错误信息
                previewContent = `
                    <div class="file-preview-error">
                        <p>无法加载文件: ${error.message}</p>
                        <p>请联系系统管理员</p>
                    </div>
                `;

                updateModalContent();
            });
    }

    function continueShowPreview() {
        if (fileType === 'image') {
            previewContent = `
                <img src="${url}" class="file-preview-image" alt="${fileName}" onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\\'file-preview-error\\'><p>图片加载失败</p></div>';">
            `;
        } else if (fileType === 'pdf') {
            previewContent = `
                <iframe src="${url}" class="file-preview-pdf" onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\\'file-preview-error\\'><p>PDF加载失败</p></div>';"></iframe>
            `;
        } else {
            previewContent = `
                <div class="file-preview-error">
                    <p>无法预览此类型的文件</p>
                    <p>请<a href="${url}" target="_blank" style="color: #4CAF50; text-decoration: underline;">点击此处</a>下载文件</p>
                </div>
            `;
        }

        updateModalContent();
    }

    function updateModalContent() {
        modal.innerHTML = `
            <div class="file-preview-content">
                <div class="file-preview-header">
                    <h3 class="file-preview-title">${fileName}</h3>
                    <div class="file-preview-close" onclick="closeFilePreview()">&times;</div>
                </div>
                <div class="file-preview-body">
                    ${previewContent}
                </div>
            </div>
        `;
    }

    // 初始显示加载中
    previewContent = `
        <div class="file-preview-loading">
            <p>加载中...</p>
        </div>
    `;

    updateModalContent();

    // 添加到文档中
    document.body.appendChild(modal);

    // 添加ESC键关闭功能
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeFilePreview();
        }
    });

    // 点击模态框背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeFilePreview();
        }
    });
}

// 关闭文件预览
function closeFilePreview() {
    const modal = document.getElementById('filePreviewModal');
    if (modal) {
        modal.remove();
    }
}

// 确保所有阶段和角色的DOM元素都已正确创建
function ensureAllDOMElementsExist() {
    Logger.log('确保所有DOM元素都已正确创建');

    // 遍历所有阶段和角色
    for (const stage of [window.STAGES.ASSEMBLY, window.STAGES.TEST, window.STAGES.PACKAGING]) {
        for (const role of [window.ROLES.FIRST, window.ROLES.SELF, window.ROLES.IPQC]) {
            // 检查并创建主容器
            ensureElementExists(`${stage}-${role}`, 'div', 'self-inspection__inspection-content');

            // 检查并创建操作人员容器
            ensureElementExists(`${stage}-${role}-operator`, 'div', 'self-inspection__operator-info');

            // 检查并创建全选容器
            const selectAllId = `${stage}-${role}-selectAll`;
            if (!document.getElementById(selectAllId)) {
                const parentContainer = document.getElementById(`${stage}-${role}`);
                if (parentContainer) {
                    const selectAllContainer = parentContainer.querySelector('.self-inspection__select-all');
                    if (!selectAllContainer) {
                        const newSelectAllContainer = document.createElement('div');
                        newSelectAllContainer.className = 'self-inspection__select-all';
                        newSelectAllContainer.innerHTML = `
                            <div class="self-inspection__checkbox-container">
                                <input type="checkbox" id="${selectAllId}" class="self-inspection__checkbox" onchange="toggleInspectionSelectAll('${stage}', '${role}')">
                                <label for="${selectAllId}" class="self-inspection__checkbox-label">全选</label>
                            </div>
                        `;

                        // 添加到父容器的开头
                        if (parentContainer.firstChild) {
                            parentContainer.insertBefore(newSelectAllContainer, parentContainer.firstChild);
                        } else {
                            parentContainer.appendChild(newSelectAllContainer);
                        }
                        Logger.log(`已创建全选容器: ${selectAllId}`);
                    }
                }
            }

            // 检查并创建检验项目容器
            ensureElementExists(`${stage}-${role}-items`, 'div', 'self-inspection__inspection-items');

            // 检查并创建附件容器
            ensureElementExists(`${stage}-${role}-files`, 'div', 'self-inspection__attachment-list');

            // 检查并创建操作按钮容器
            const actionBarSelector = `#${stage}-${role} .self-inspection__action-bar`;
            const actionBar = document.querySelector(actionBarSelector);
            if (!actionBar) {
                const parentContainer = document.getElementById(`${stage}-${role}`);
                if (parentContainer) {
                    const newActionBar = document.createElement('div');
                    newActionBar.className = 'self-inspection__action-bar';
                    newActionBar.innerHTML = `
                        <button class="self-inspection__btn self-inspection__btn--reset" onclick="resetInspection('${stage}', '${role}')">重置</button>
                        <button class="self-inspection__btn self-inspection__btn--submit" onclick="submitInspection('${stage}', '${role}')">提交${role === window.ROLES.SELF ? '自检' : (role === window.ROLES.FIRST ? '首检' : 'IPQC')}</button>
                    `;
                    parentContainer.appendChild(newActionBar);
                    Logger.log(`已创建操作按钮容器: ${actionBarSelector}`);
                }
            }
        }
    }
}

// 确保元素存在，如果不存在则创建
function ensureElementExists(id, tagName, className) {
    if (!document.getElementById(id)) {
        Logger.log(`创建元素: ${id}`);
        const element = document.createElement(tagName);
        element.id = id;
        if (className) {
            element.className = className;
        }

        // 查找父容器
        const idParts = id.split('-');
        if (idParts.length >= 2) {
            const parentId = `${idParts[0]}-${idParts[1]}`;
            const parentElement = document.getElementById(parentId);
            if (parentElement) {
                parentElement.appendChild(element);
                return true;
            }
        }

        // 如果找不到父容器，添加到body
        document.body.appendChild(element);
        return true;
    }
    return false;
}

// ===== 跳过检验功能相关函数 =====

// 切换跳过检验开关状态
function toggleSkipInspection() {
    const skipSwitch = document.getElementById('skipInspectionSwitch');
    const confirmButton = document.getElementById('confirmSkipButton');
    
    if (skipSwitch && confirmButton) {
        if (skipSwitch.checked) {
            confirmButton.style.display = 'inline-block';
            showNotification('warning', '注意', '跳过检验是危险操作，将直接完成所有检验阶段');
        } else {
            confirmButton.style.display = 'none';
        }
    }
}

// 确认跳过检验操作
function confirmSkipInspection() {
    // 检查是否有有效的工单
    if (!inspectionData.currentWorkOrderId) {
        showNotification('error', '操作失败', '请先输入有效的工单号');
        return;
    }

    // 获取工单号用于显示
    const orderNoElement = document.getElementById('orderNo');
    const orderNo = orderNoElement ? orderNoElement.value : '未知工单';

    // 二次确认对话框
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: '危险操作确认',
            html: `
                <div style="text-align: left; margin: 20px 0;">
                    <p><strong>⚠️ 您即将跳过所有检验流程</strong></p>
                    <p>工单号：<code>${orderNo}</code></p>
                    <p>此操作将：</p>
                    <ul style="margin: 10px 0;">
                        <li>直接标记所有检验阶段为完成状态</li>
                        <li>将工单状态设置为已完成</li>
                        <li><strong style="color: red;">此操作不可撤销</strong></li>
                    </ul>
                    <p>请确认您有权限执行此操作，且确实需要跳过检验。</p>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '确认跳过',
            cancelButtonText: '取消',
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                executeSkipInspection();
            }
        });
    } else {
        // 如果没有SweetAlert，使用原生确认对话框
        const confirmed = confirm(`危险操作确认\n\n您即将跳过工单"${orderNo}"的所有检验流程。\n此操作将直接标记所有阶段为完成状态，且不可撤销。\n\n确定要继续吗？`);
        if (confirmed) {
            executeSkipInspection();
        }
    }
}

// 执行跳过检验的API调用
function executeSkipInspection() {
    showLoading();
    
    fetch(`/api/quality-inspection/work-order/${inspectionData.currentWorkOrderId}/skip-all-stages`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            showNotification('success', '操作成功', '已成功跳过所有检验阶段');
            
            // 重置跳过检验开关
            const skipSwitch = document.getElementById('skipInspectionSwitch');
            const confirmButton = document.getElementById('confirmSkipButton');
            if (skipSwitch) {
                skipSwitch.checked = false;
            }
            if (confirmButton) {
                confirmButton.style.display = 'none';
            }
            
            // 刷新页面状态以显示完成状态
            const orderNoElement = document.getElementById('orderNo');
            if (orderNoElement && orderNoElement.value) {
                refreshWorkOrderData(orderNoElement.value);
            }
        } else {
            showNotification('error', '操作失败', data.message || '跳过检验失败，请稍后重试');
        }
    })
    .catch(error => {
        hideLoading();
        Logger.error('Skip inspection error:', error);
        showNotification('error', '网络错误', '请检查网络连接后重试');
    });
}

// 将必要的函数暴露到全局作用域
window.switchTab = switchTab;
window.toggleStage = toggleStage;
window.toggleInspectionSelectAll = toggleInspectionSelectAll;
window.updateInspectionItem = updateInspectionItem;
window.resetInspection = resetInspection;
window.submitInspection = submitInspection;
window.showUploadDialog = showUploadDialog;
window.deleteAttachment = deleteAttachment;
window.removeFromPendingFiles = removeFromPendingFiles;
window.previewFile = previewFile;
window.previewPendingFile = previewPendingFile;
window.closeFilePreview = closeFilePreview;
window.findFileIdByName = findFileIdByName;
window.showFilePreviewModal = showFilePreviewModal;
window.initSelfInspectionPage = initSelfInspectionPage;
window.ensureAllDOMElementsExist = ensureAllDOMElementsExist;
// 暴露跳过检验相关函数
window.toggleSkipInspection = toggleSkipInspection;
window.confirmSkipInspection = confirmSkipInspection;

// 导出SN号列表功能
function exportSNList(stage, role) {
    Logger.log('导出SN号列表', stage, role);
    
    // 检查是否为自检阶段
    if (role !== window.ROLES.SELF) {
        showNotification('warning', '导出功能仅在自检阶段可用');
        return;
    }
    
    // 获取当前显示的产品状态数据
    const statusContainer = document.getElementById(`${stage}-${role}-product-status`);
    if (!statusContainer) {
        showNotification('warning', '没有找到产品状态数据', '请先加载工单数据');
        return;
    }
    
    // 从产品状态列表中提取SN号
    const productItems = statusContainer.querySelectorAll('.self-inspection__product-item');
    if (productItems.length === 0) {
        showNotification('warning', '没有可导出的SN号', '当前阶段没有产品数据');
        return;
    }
    
    // 提取SN号和状态信息
    const snList = [];
    productItems.forEach((item, index) => {
        const snElement = item.querySelector('.self-inspection__product-sn');
        const statusElement = item.querySelector('.self-inspection__status-tag');
        const inspectorElement = item.querySelector('.self-inspection__inspector');
        
        if (snElement) {
            const serialNo = snElement.textContent.trim();
            const isCompleted = statusElement && statusElement.classList.contains('self-inspection__status-tag--completed');
            const inspector = inspectorElement && inspectorElement.textContent.trim() !== '占位' ? inspectorElement.textContent.trim() : '';
            
            snList.push({
                序号: index + 1,
                SN号: serialNo,
                检验状态: isCompleted ? '已完成' : '未完成',
                检验员: inspector || ''
            });
        }
    });
    
    if (snList.length === 0) {
        showNotification('warning', '没有有效的SN号数据可导出');
        return;
    }
    
    // 获取工单信息
    const orderNo = document.getElementById('orderNo')?.value || '未知工单';
    const stageDisplayName = getStageDisplayName(stage);
    const currentTime = getCurrentTime();
    
    // 生成CSV内容
    const csvContent = generateCSVContent(snList, {
        工单号: orderNo,
        阶段: stageDisplayName,
        角色: '自检',
        导出时间: currentTime,
        总数量: snList.length,
        已完成数量: snList.filter(item => item.检验状态 === '已完成').length,
        未完成数量: snList.filter(item => item.检验状态 === '未完成').length
    });
    
    // 下载CSV文件
    const fileName = `${orderNo}_${stageDisplayName}_自检_SN号列表_${currentTime.replace(/[:\s-]/g, '')}.csv`;
    downloadCSV(csvContent, fileName);
    
    // 显示成功提示
    showNotification('success', '导出成功', `已导出${snList.length}个SN号到文件: ${fileName}`);
}

// 生成CSV内容
function generateCSVContent(snList, metadata) {
    let csvContent = '\uFEFF'; // 添加BOM以支持中文
    
    // 添加元数据信息
    csvContent += '工单信息\n';
    csvContent += `工单号,${metadata.工单号}\n`;
    csvContent += `阶段,${metadata.阶段}\n`;
    csvContent += `角色,${metadata.角色}\n`;
    csvContent += `导出时间,${metadata.导出时间}\n`;
    csvContent += `总数量,${metadata.总数量}\n`;
    csvContent += `已完成数量,${metadata.已完成数量}\n`;
    csvContent += `未完成数量,${metadata.未完成数量}\n`;
    csvContent += '\n';
    
    // 添加SN号列表标题
    csvContent += 'SN号列表\n';
    
    // 添加表头
    const headers = Object.keys(snList[0]);
    csvContent += headers.join(',') + '\n';
    
    // 添加数据行
    snList.forEach(item => {
        const row = headers.map(header => {
            const value = item[header] || '';
            // 如果值包含逗号、换行符或双引号，需要用双引号包围并转义
            if (value.toString().includes(',') || value.toString().includes('\n') || value.toString().includes('"')) {
                return '"' + value.toString().replace(/"/g, '""') + '"';
            }
            return value;
        });
        csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
}

// 下载CSV文件
function downloadCSV(csvContent, fileName) {
    try {
        // 创建Blob对象
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // 创建下载链接
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', fileName);
        link.style.visibility = 'hidden';
        
        // 添加到DOM并触发下载
        document.body.appendChild(link);
        link.click();
        
        // 清理
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        Logger.log('CSV文件下载成功:', fileName);
    } catch (error) {
        Logger.error('CSV文件下载失败:', error);
        showNotification('error', '下载失败', '无法下载CSV文件，请重试');
    }
}

// 获取阶段显示名称
function getStageDisplayName(stage) {
    switch(stage) {
        case window.STAGES.ASSEMBLY: return '组装前阶段';
        case window.STAGES.TEST: return '测试前阶段';
        case window.STAGES.PACKAGING: return '包装前阶段';
        default: return '未知阶段';
    }
}

// 更新导出按钮状态
function updateExportButtonState(stage, role, hasProducts) {
    const exportButton = document.querySelector(`#${stage}-${role} .self-inspection__export-btn`);
    if (exportButton) {
        if (hasProducts) {
            exportButton.disabled = false;
            exportButton.title = '点击导出当前阶段的SN号列表';
        } else {
            exportButton.disabled = true;
            exportButton.title = '没有可导出的SN号数据';
        }
    }
}

// 动态添加导出按钮到现有页面
function addExportButtonToExistingPage() {
    const stages = [window.STAGES.ASSEMBLY, window.STAGES.TEST, window.STAGES.PACKAGING];
    
    stages.forEach(stage => {
        const selfContentElement = document.getElementById(`${stage}-${window.ROLES.SELF}`);
        if (selfContentElement) {
            // 检查是否已经有导出按钮
            const existingExportSection = selfContentElement.querySelector('.self-inspection__export-section');
            if (!existingExportSection) {
                // 查找附件区域
                const attachmentsSection = selfContentElement.querySelector('.self-inspection__attachments');
                const actionBar = selfContentElement.querySelector('.self-inspection__action-bar');
                
                if (attachmentsSection && actionBar) {
                    // 创建导出区域
                    const exportSection = document.createElement('div');
                    exportSection.className = 'self-inspection__export-section';
                    exportSection.innerHTML = `
                        <button class="self-inspection__export-btn" onclick="exportSNList('${stage}', '${window.ROLES.SELF}')" title="导出当前阶段的SN号列表">
                            <i class="fas fa-download"></i> 导出SN号列表
                        </button>
                    `;
                    
                    // 插入到附件区域和操作栏之间
                    selfContentElement.insertBefore(exportSection, actionBar);
                    
                    console.log(`已添加导出按钮到 ${stage}-${window.ROLES.SELF}`);
                    
                    // 检查是否有产品数据来设置按钮状态
                    const statusContainer = document.getElementById(`${stage}-${window.ROLES.SELF}-product-status`);
                    if (statusContainer) {
                        const productItems = statusContainer.querySelectorAll('.self-inspection__product-item');
                        updateExportButtonState(stage, window.ROLES.SELF, productItems.length > 0);
                    } else {
                        // 默认禁用按钮
                        updateExportButtonState(stage, window.ROLES.SELF, false);
                    }
                }
            }
        }
    });
}

// 暴露导出函数到全局作用域
window.exportSNList = exportSNList;
window.addExportButtonToExistingPage = addExportButtonToExistingPage;