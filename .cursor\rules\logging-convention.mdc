---
description: 
globs: 
alwaysApply: false
---
# 集中式日志记录规范

本项目的日志记录遵循集中管理模式，旨在方便开发调试的同时，确保生产环境的代码整洁与性能。

### 核心理念

我们使用一个全局的 `Logger` 对象来替代原生的 `console` 调用。这允许我们通过一个统一的开关来控制整个应用程序的日志输出。

### 关键文件

- **日志工具**: `[static/js/utils/Logger.js](mdc:static/js/utils/Logger.js)` - 这是日志系统的核心，定义了 `Logger` 对象和 `DEBUG` 开关。
- **详细文档**: `[loggerswitch.md](mdc:loggerswitch.md)` - 提供了该日志系统的完整设计、用法和最佳实践。

### 使用规则

1.  **替换 `console`**: 在所有JavaScript文件中，必须使用 `Logger.log()`, `Logger.error()`, `Logger.warn()`, `Logger.info()` 来代替对应的 `console` 方法。
2.  **控制日志输出**:
    - **开发环境**: 在 `[static/js/utils/Logger.js](mdc:static/js/utils/Logger.js)` 中，将 `DEBUG` 设置为 `true` 来开启所有日志。
    - **生产环境**: 将 `DEBUG` 设置为 `false` 来关闭所有日志，从而提升性能并避免信息泄露。
3.  **加载顺序**: 必须确保 `[static/js/utils/Logger.js](mdc:static/js/utils/Logger.js)` 在 `templates/index.html` 中先于其他业务脚本加载。

### 已实施模块

该日志规范已在以下主要模块中得到应用：

- `ProductTestQuery.js`
- `FaultQuery.js`
- `CouplerVue.js`
- `IOModuleVue.js`
- `CPUControllerVue.js`
- `script.js`

在对项目进行任何修改或添加新功能时，请务必遵守此日志记录规范。

