#!/bin/bash

# 加载配置
source "$(dirname "$0")/config.sh"

echo "开始系统初始化..."
check_root

# 更新系统
echo "正在更新系统..."
apt update && apt upgrade -y
check_status "系统更新失败"

# 安装基础包
echo "正在安装基础包..."
apt install -y build-essential python3-dev python3-pip python3-venv supervisor git curl wget \
    libevent-dev python3-gevent ufw pkg-config default-libmysqlclient-dev \
    libssl-dev libffi-dev zlib1g-dev
check_status "基础包安装失败"

# 创建项目目录结构
echo "创建项目目录结构..."

# 创建主目录
create_directory "${PROJECT_PATH}"

# 创建一级子目录
create_directory "${PROJECT_PATH}/environment"
create_directory "${PROJECT_PATH}/INDEX"
create_directory "${PROJECT_PATH}/database"
create_directory "${PROJECT_PATH}/models"
create_directory "${PROJECT_PATH}/routes"
create_directory "${PROJECT_PATH}/services"
create_directory "${PROJECT_PATH}/static"
create_directory "${PROJECT_PATH}/templates"
create_directory "${LOG_PATH}"

# 创建static子目录
create_directory "${STATIC_PATH}/page_js_css"

# 创建INDEX子目录的占位文件
touch "${PROJECT_PATH}/INDEX/device.py"
touch "${PROJECT_PATH}/INDEX/plc_assembly.py"

# 创建database子目录的占位文件
touch "${PROJECT_PATH}/database/db_manager.py"

# 创建models子目录的占位文件
touch "${PROJECT_PATH}/models/user.py"
touch "${PROJECT_PATH}/models/assembly.py"

# 创建routes子目录的占位文件
touch "${PROJECT_PATH}/routes/cpu_controller.py"
touch "${PROJECT_PATH}/routes/io_module.py"
touch "${PROJECT_PATH}/routes/coupler.py"
touch "${PROJECT_PATH}/routes/board_test.py"
touch "${PROJECT_PATH}/routes/fault_entry.py"
touch "${PROJECT_PATH}/routes/fault_code.py"
touch "${PROJECT_PATH}/routes/document_center.py"
touch "${PROJECT_PATH}/routes/barcode_comparison.py"
touch "${PROJECT_PATH}/routes/barcode_binding.py"
touch "${PROJECT_PATH}/routes/batch_query.py"
touch "${PROJECT_PATH}/routes/product_test_query.py"
touch "${PROJECT_PATH}/routes/fault_query.py"

# 创建services子目录的占位文件
touch "${PROJECT_PATH}/services/assembly_service.py"

# 创建templates文件
touch "${PROJECT_PATH}/templates/index.html"
touch "${PROJECT_PATH}/templates/login.html"

# 创建主应用文件
touch "${PROJECT_PATH}/app.py"
touch "${PROJECT_PATH}/check_versions.py"
touch "${PROJECT_PATH}/requirements.txt"
touch "${PROJECT_PATH}/app.log"

# 配置防火墙
echo "配置防火墙..."
ufw allow ${SERVER_PORT}/tcp
ufw allow ${DB_PORT}/tcp
check_status "防火墙配置失败"

# 系统参数优化
echo "优化系统参数..."
cat > /etc/sysctl.d/99-kmlc-plc.conf << EOF
# 优化网络参数
net.ipv4.tcp_max_syn_backlog = 4096
net.core.somaxconn = 4096
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000
net.ipv4.tcp_tw_reuse = 1

# TCP连接优化
net.core.netdev_max_backlog = 16384
net.ipv4.tcp_slow_start_after_idle = 0
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
EOF

# 应用系统参数
sysctl -p /etc/sysctl.d/99-kmlc-plc.conf
check_status "系统参数配置失败"

# 设置目录权限
echo "设置目录权限..."
chown -R "${APP_USER}:${APP_GROUP}" "${PROJECT_PATH}"
chmod -R 755 "${PROJECT_PATH}"
chmod 644 "${PROJECT_PATH}/app.log"
check_status "权限设置失败"

# 移除过时的内核参数
sed -i '/tcp_tw_recycle/d' /etc/sysctl.conf

# 配置日志轮转
echo "配置日志轮转..."
cat > /etc/logrotate.d/${PROJECT_NAME} << EOF
${LOG_PATH}/*.log {
    daily
    rotate ${LOG_ROTATE_COUNT}
    compress
    delaycompress
    missingok
    notifempty
    create 0640 ${APP_USER} ${APP_GROUP}
    sharedscripts
    postrotate
        supervisorctl restart ${PROJECT_NAME} >/dev/null 2>&1 || true
    endscript
}
EOF

echo "系统初始化完成！" 