// 作废版本页面Vue应用
(function() {
    'use strict';
    
    Logger.log('Loading Obsolete Firmware Vue App...');
    
    // 检查共享分页功能是否已加载
    if (!window.FirmwarePagination) {
        Logger.error('FirmwarePagination not loaded. Please include firmware-common-paging.js first.');
        return;
    }
    
    if (!window.Vue || !window.ElementPlus) {
        Logger.error('Vue or ElementPlus not loaded');
        return;
    }

    const { createApp, ref, reactive, computed, onMounted, onUnmounted, nextTick } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    
    const ObsoleteFirmwareApp = {
        setup() {
            // ===== 响应式数据 =====
            const searchQuery = ref('');
            const loading = ref(false);
            const sortProp = ref('');
            const sortOrder = ref('');
            
            // 对话框状态
            const versionDetailDialogVisible = ref(false);
            const statisticsDialogVisible = ref(false);
            
            // 当前用户信息
            const currentUser = reactive({
                id: '001',
                name: '管理员',
                role: 'admin'
            });
            
            // 版本详情数据
            const versionDetails = reactive({
                id: '',
                serialNumber: '',
                name: '',
                version: '',
                developer: '',
                products: [],
                versionRequirements: '',
                description: '',
                approveTime: '',
                obsoleteTime: '',
                effectiveDays: 0,
                usageCount: 0,
                downloadCount: 0,
                // 新增技术规格字段
                buildTime: '',
                backplaneVersion: '',
                ioVersion: ''
            });
            
            // 统计数据
            const statistics = reactive({
                totalObsolete: 0,
                avgEffectiveDays: 0,
                topReasons: [],
                monthlyObsolete: []
            });
            
            // 状态映射
            const statusMap = reactive({
                'active': '已生效',
                'pending': '待审核',
                'rejected': '审核退回',
                'obsolete': '已作废'
            });
            
            // 获取全局数据管理器
            const dataManager = window.FirmwareDataManager;
            
            // 作废固件列表
            const obsoleteFirmwareList = ref([]);
            
            // 加载数据 - 改为异步
            const loadData = async () => {
                loading.value = true;
                try {
                    const data = await dataManager.getObsoleteFirmware();
                    obsoleteFirmwareList.value = data || [];
                    Logger.log('[Obsolete Firmware] 数据加载成功:', data.length, '条记录');
                } catch (error) {
                    Logger.error('[Obsolete Firmware] 数据加载失败:', error);
                    ElMessage.error('数据加载失败: ' + error.message);
                    obsoleteFirmwareList.value = [];
                } finally {
                    loading.value = false;
                }
            };
            
            // 事件监听器
            let eventListeners = [];
            
            // 全局数据变化监听器 - 移到外部确保作用域正确
            const handleGlobalDataChange = async () => {
                Logger.log('[Obsolete Firmware] 收到全局数据变化事件，重新加载数据');
                await loadData();
            };
            
            // ===== 计算属性 =====
            // 过滤和排序后的完整列表
            const filteredAndSortedList = computed(() => {
                let list = [...obsoleteFirmwareList.value];
                
                // 搜索过滤
                if (searchQuery.value) {
                    const query = searchQuery.value.toLowerCase();
                    list = list.filter(item => 
                        item.serialNumber.toLowerCase().includes(query) ||
                        item.name.toLowerCase().includes(query) ||
                        item.version.toLowerCase().includes(query) ||
                        item.developer.toLowerCase().includes(query) ||
                        item.obsoleteReason.toLowerCase().includes(query) ||
                        item.obsoleteBy.toLowerCase().includes(query) ||
                        item.products.some(p => p.model.toLowerCase().includes(query))
                    );
                }
                
                // 排序
                if (sortProp.value && sortOrder.value) {
                    list.sort((a, b) => {
                        let aVal = a[sortProp.value];
                        let bVal = b[sortProp.value];
                        
                        if (sortProp.value === 'products') {
                            aVal = a.products.map(p => p.model).join(', ');
                            bVal = b.products.map(p => p.model).join(', ');
                        }
                        
                        if (typeof aVal === 'string') {
                            aVal = aVal.toLowerCase();
                            bVal = bVal.toLowerCase();
                        }
                        
                        if (aVal < bVal) return sortOrder.value === 'ascending' ? -1 : 1;
                        if (aVal > bVal) return sortOrder.value === 'ascending' ? 1 : -1;
                        return 0;
                    });
                }
                
                return list;
            });
            
            // 使用共享分页功能（在filteredAndSortedList定义之后）
            const pagination = window.FirmwarePagination.usePagination({
                dataList: filteredAndSortedList,
                defaultPageSize: 10,
                pageSizeOptions: [10, 20, 50, 100]
            });
            
            // 当前页显示的数据（使用共享分页功能）
            const filteredObsoleteList = pagination.paginatedData;
            
            // 统计计算（基于完整的过滤列表）
            const totalUsageCount = computed(() => {
                return filteredAndSortedList.value.reduce((sum, item) => sum + item.usageCount, 0);
            });
            
            const avgEffectiveDays = computed(() => {
                if (filteredAndSortedList.value.length === 0) return 0;
                const totalDays = filteredAndSortedList.value.reduce((sum, item) => sum + item.effectiveDays, 0);
                return Math.round(totalDays / filteredAndSortedList.value.length);
            });
            
            // ===== 方法 =====
            
            // 搜索相关
            const handleSearch = () => {
                Logger.log('搜索:', searchQuery.value);
                // 搜索时重置到第一页
                pagination.resetToFirstPage();
            };
            
            const handleSearchClear = () => {
                searchQuery.value = '';
                // 清空搜索时重置到第一页
                pagination.resetToFirstPage();
            };
            
            // 排序
            const handleSortChange = (column) => {
                sortProp.value = column.prop;
                sortOrder.value = column.order;
                Logger.log('排序变更:', column.prop, column.order);
            };
            
            // 显示版本详情
            const showVersionDetail = (row) => {
                Object.assign(versionDetails, {
                    id: row.id,
                    serialNumber: row.serialNumber,
                    name: row.name,
                    version: row.version,
                    developer: row.developer,
                    products: row.products || [],
                    versionRequirements: row.versionRequirements || '',
                    description: row.description || '',
                    approveTime: row.approveTime,
                    obsoleteTime: row.obsoleteTime,
                    effectiveDays: row.effectiveDays,
                    usageCount: row.usageCount,
                    downloadCount: row.downloadCount,
                    obsoleteReason: row.obsoleteReason,
                    obsoleteBy: row.obsoleteBy,
                    // 新增技术规格字段
                    buildTime: row.buildTime || '',
                    backplaneVersion: row.backplaneVersion || '',
                    ioVersion: row.ioVersion || ''
                });
                versionDetailDialogVisible.value = true;
            };
            

            
            // 计算统计数据
            const calculateStatistics = () => {
                loading.value = true;
                
                // 模拟计算统计数据
                setTimeout(() => {
                    statistics.totalObsolete = filteredAndSortedList.value.length;
                    statistics.avgEffectiveDays = avgEffectiveDays.value;
                    
                    // 作废原因统计
                    const reasonCount = {};
                    filteredAndSortedList.value.forEach(item => {
                        const reason = item.obsoleteReason.split('，')[0].split(',')[0]; // 取第一个原因关键词
                        reasonCount[reason] = (reasonCount[reason] || 0) + 1;
                    });
                    
                    statistics.topReasons = Object.entries(reasonCount)
                        .map(([reason, count]) => ({ reason, count }))
                        .sort((a, b) => b.count - a.count)
                        .slice(0, 5);
                    
                    statisticsDialogVisible.value = true;
                    loading.value = false;
                }, 1000);
            };
            
            // 导出数据
            const exportData = () => {
                if (window.FirmwareUtils && window.FirmwareUtils.exportToExcel) {
                    const exportData = filteredAndSortedList.value.map(item => ({
                        'ERP流水号': item.serialNumber,
                        '固件名称': item.name,
                        '软件版本': item.version,
                        '构建日期': item.buildTime || '',
                        '背板总线版本': item.backplaneVersion || '',
                        '高速IO版本': item.ioVersion || '',
                        '状态': statusMap[item.status],
                        '研发者': item.developer,
                        '适用产品': item.products.map(p => p.model).join(', '),
                        '生效时间': item.approveTime,
                        '作废时间': item.obsoleteTime,
                        '生效天数': item.effectiveDays,
                        '使用次数': item.usageCount,
                        '下载次数': item.downloadCount,
                        '作废原因': item.obsoleteReason,
                        '作废操作人': item.obsoleteBy
                    }));
                    
                    window.FirmwareUtils.exportToExcel(exportData, '作废固件版本列表');
                } else {
                    ElMessage.info('正在导出作废版本数据...');
                }
            };
            
            // 刷新数据
            const refreshData = () => {
                loading.value = true;
                // 模拟API调用
                setTimeout(() => {
                    ElMessage.success('数据已刷新');
                    loading.value = false;
                }, 1000);
            };
            
            // 格式化生效天数
            const formatEffectiveDays = (days) => {
                if (days >= 365) {
                    const years = Math.floor(days / 365);
                    const remainingDays = days % 365;
                    return remainingDays > 0 ? `${years}年${remainingDays}天` : `${years}年`;
                } else if (days >= 30) {
                    const months = Math.floor(days / 30);
                    const remainingDays = days % 30;
                    return remainingDays > 0 ? `${months}个月${remainingDays}天` : `${months}个月`;
                } else {
                    return `${days}天`;
                }
            };
            
            // 获取生效天数颜色
            const getEffectiveDaysColor = (days) => {
                if (days < 30) return 'danger';
                if (days < 90) return 'warning';
                if (days < 365) return 'success';
                return 'primary';
            };
            
            // 生命周期
            onMounted(async () => {
                Logger.log('Obsolete Firmware page mounted');
                await loadData();
                
                // 监听数据更新事件
                const onDataUpdate = async () => {
                    Logger.log('[Obsolete Firmware] 收到数据更新事件，重新加载数据');
                    await loadData();
                };
                
                eventListeners = [
                    { event: 'firmware-obsoleted', handler: onDataUpdate },
                    { event: 'version-updated', handler: onDataUpdate },
                    { event: 'firmware-approved', handler: onDataUpdate },
                    { event: 'firmware-rejected', handler: onDataUpdate },
                    { event: 'global-data-refresh', handler: onDataUpdate }
                ];
                
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.on(event, handler);
                });
                
                // 添加全局window事件监听（用于跨页面通信）
                window.addEventListener('firmware-data-changed', handleGlobalDataChange);
            });
            
            onUnmounted(() => {
                // 清理事件监听器
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.off(event, handler);
                });
                
                // 清理全局事件监听器
                window.removeEventListener('firmware-data-changed', handleGlobalDataChange);
            });
            
            return {
                // 响应式数据
                searchQuery,
                loading,
                currentUser,
                statusMap,
                obsoleteFirmwareList,
                filteredObsoleteList,
                totalUsageCount,
                avgEffectiveDays,
                
                // 分页数据（使用共享分页功能）
                ...pagination,
                
                // 对话框状态
                versionDetailDialogVisible,
                statisticsDialogVisible,
                versionDetails,
                statistics,
                
                // 方法
                handleSearch,
                handleSearchClear,
                handleSortChange,
                showVersionDetail,
                calculateStatistics,
                exportData,
                refreshData,
                formatEffectiveDays,
                getEffectiveDaysColor
            };
        },
        
        template: `
        <div class="firmware-container obsolete-firmware firmware-page">
            <div class="obsolete-firmware__header">
                <div class="obsolete-firmware__header-left">
                    <h3 class="obsolete-firmware__title">作废固件版本</h3>
                    <span class="firmware-page__tip obsolete-firmware__tip">
                        （单击ERP流水号，查看概要）
                    </span>
                </div>
                <div class="obsolete-firmware__stats">
                    <el-tag type="danger" size="large">
                        作废版本: {{ totalCount }} 个
                    </el-tag>
                    <el-tag type="warning" size="large">
                        平均生效: {{ avgEffectiveDays }} 天
                    </el-tag>
                    <el-tag type="info" size="large">
                        总使用次数: {{ totalUsageCount }}
                    </el-tag>
                </div>
            </div>
            
            <div class="obsolete-firmware__search-bar">
                <el-input
                    class="obsolete-firmware__search-input"
                    placeholder="请输入ERP流水号、固件名称、软件版本、研发者、作废原因或适用产品进行搜索"
                    v-model="searchQuery"
                    clearable
                    @clear="handleSearchClear"
                    @keyup.enter="handleSearch">
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
                
                <div class="obsolete-firmware__actions">
                    <el-button type="success" @click="calculateStatistics" :loading="loading" class="firmware-page__action-button firmware-page__action-button--success">
                        <el-icon><PieChart /></el-icon>
                        统计分析
                    </el-button>
                    <el-button type="info" @click="refreshData" :loading="loading" class="firmware-page__action-button firmware-page__action-button--info">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                    <el-button type="primary" @click="exportData" class="firmware-page__action-button firmware-page__action-button--primary">
                        <el-icon><Download /></el-icon>
                        导出数据
                    </el-button>
                </div>
            </div>
            
            <div class="obsolete-firmware__table-container">
                <el-table 
                    :data="filteredObsoleteList" 
                    style="width: 100%"
                    border
                    size="small"
                    v-loading="loading"
                    @sort-change="handleSortChange"
                    :default-sort="{prop: 'obsoleteTime', order: 'descending'}">
                    
                    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                    
                    <el-table-column prop="serialNumber" label="ERP流水号" width="150" sortable="custom" show-overflow-tooltip>
                        <template #default="scope">
                            <el-link 
                                type="primary"
                                @click="showVersionDetail(scope.row)"
                                class="obsolete-firmware__serial-link">
                                {{ scope.row.serialNumber }}
                            </el-link>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="name" label="固件名称" min-width="180" sortable="custom" show-overflow-tooltip></el-table-column>
                    
                    <el-table-column prop="version" label="软件版本" width="120" sortable="custom">
                        <template #default="scope">
                            <el-tag type="danger" effect="plain">{{ scope.row.version }}</el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="status" label="状态" width="100" sortable="custom">
                        <template #default="scope">
                            <el-tag type="danger">{{ statusMap[scope.row.status] }}</el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="products" label="适用产品" width="180" sortable="custom">
                        <template #default="scope">
                            <div class="obsolete-firmware__products">
                                <el-tag 
                                    v-for="product in scope.row.products.slice(0, 2)" 
                                    :key="product.code"
                                    size="small"
                                    type="info"
                                    class="obsolete-firmware__product-tag">
                                    {{ product.model }}
                                </el-tag>
                                <el-tag 
                                    v-if="scope.row.products.length > 2"
                                    size="small"
                                    type="info"
                                    class="obsolete-firmware__product-tag">
                                    +{{ scope.row.products.length - 2 }}
                                </el-tag>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="developer" label="研发者" width="100" sortable="custom" show-overflow-tooltip></el-table-column>
                    
                    <el-table-column prop="effectiveDays" label="生效天数" width="120" sortable="custom" align="center">
                        <template #default="scope">
                            <el-tag 
                                :type="getEffectiveDaysColor(scope.row.effectiveDays)"
                                size="small">
                                {{ formatEffectiveDays(scope.row.effectiveDays) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="usageCount" label="使用次数" width="100" sortable="custom" align="center"></el-table-column>
                    <el-table-column prop="downloadCount" label="下载次数" width="100" sortable="custom" align="center"></el-table-column>
                    <el-table-column prop="approveTime" label="生效时间" width="160" sortable="custom"></el-table-column>
                    <el-table-column prop="obsoleteTime" label="作废时间" width="160" sortable="custom"></el-table-column>
                    <el-table-column prop="obsoleteBy" label="作废操作人" width="120" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="obsoleteReason" label="作废原因" min-width="200" show-overflow-tooltip></el-table-column>
                </el-table>
                
                <!-- 分页组件（使用共享模板） -->
                <div class="table-footer">
                    <div class="table-length">
                        <div class="page-size-selector">
                            每页
                            <select @change="handleSizeChange($event.target.value)">
                                <option 
                                    v-for="size in pageSizes" 
                                    :key="size" 
                                    :value="size" 
                                    :selected="size === pageSize">
                                    {{ size }}
                                </option>
                            </select>
                            条
                        </div>
                        <div class="total-count">
                            共计 <span>{{ totalCount }}</span> 条
                        </div>
                    </div>
                    <div class="table-pagination">
                        <button 
                            class="btn btn-icon" 
                            @click="goToFirstPage" 
                            :disabled="currentPage === 1">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button 
                            class="btn btn-icon" 
                            @click="goToPrevPage" 
                            :disabled="currentPage === 1">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <div class="pagination-pages">
                            <span v-if="startPage > 1" class="pagination-ellipsis">...</span>
                            <button 
                                v-for="page in visiblePages" 
                                :key="page"
                                class="btn btn-page" 
                                :class="{ active: page === currentPage }"
                                @click="goToPage(page)">
                                {{ page }}
                            </button>
                            <span v-if="endPage < totalPages" class="pagination-ellipsis">...</span>
                        </div>
                        <button 
                            class="btn btn-icon" 
                            @click="goToNextPage" 
                            :disabled="currentPage === totalPages">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button 
                            class="btn btn-icon" 
                            @click="goToLastPage" 
                            :disabled="currentPage === totalPages">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 版本详情对话框 -->
            <el-dialog
                title="作废版本详情"
                v-model="versionDetailDialogVisible"
                class="obsolete-firmware__detail-dialog">
                
                <div class="obsolete-firmware__detail-content">
                    <div class="obsolete-firmware__detail-left">
                        <el-tag type="danger" size="large" class="obsolete-firmware__status-tag">
                            已作废
                        </el-tag>
                        
                        <div class="obsolete-firmware__detail-section" style="--item-index: 0">
                            <h4>基本信息</h4>
                            <div class="obsolete-firmware__detail-divider"></div>
                            <p><strong>ERP流水号：</strong>{{ versionDetails.serialNumber }}</p>
                            <p><strong>固件名称：</strong>{{ versionDetails.name }}</p>
                            <p><strong>软件版本：</strong>{{ versionDetails.version }}</p>
                            <p><strong>构建日期：</strong>{{ versionDetails.buildTime || '暂无' }}</p>
                            <p><strong>背板总线版本：</strong>{{ versionDetails.backplaneVersion || '暂无' }}</p>
                            <p><strong>高速IO版本：</strong>{{ versionDetails.ioVersion || '暂无' }}</p>
                            <p><strong>研发者：</strong>{{ versionDetails.developer }}</p>
                        </div>
                        
                        <div class="obsolete-firmware__detail-section" style="--item-index: 1">
                            <h4>生命周期</h4>
                            <div class="obsolete-firmware__detail-divider"></div>
                            <p><strong>生效时间：</strong>{{ versionDetails.approveTime }}</p>
                            <p><strong>作废时间：</strong>{{ versionDetails.obsoleteTime }}</p>
                            <p><strong>生效天数：</strong>
                                <el-tag :type="getEffectiveDaysColor(versionDetails.effectiveDays)">
                                    {{ formatEffectiveDays(versionDetails.effectiveDays) }}
                                </el-tag>
                            </p>
                            <p><strong>作废操作人：</strong>{{ versionDetails.obsoleteBy }}</p>
                        </div>
                        
                        <div class="obsolete-firmware__detail-section" style="--item-index: 2">
                            <h4>作废原因</h4>
                            <div class="obsolete-firmware__detail-divider"></div>
                            <p class="obsolete-firmware__detail-reason">{{ versionDetails.obsoleteReason }}</p>
                        </div>
                    </div>
                    
                    <div class="obsolete-firmware__detail-right">
                        <div class="obsolete-firmware__detail-section" style="--item-index: 3">
                            <h4>适用产品</h4>
                            <div class="obsolete-firmware__detail-divider"></div>
                            <div class="obsolete-firmware__detail-products">
                                <el-tag 
                                    v-for="product in versionDetails.products" 
                                    :key="product.code"
                                    type="info"
                                    class="obsolete-firmware__detail-product-tag">
                                    {{ product.model }}
                                </el-tag>
                                <span v-if="!versionDetails.products.length">暂无</span>
                            </div>
                        </div>
                        
                        <div class="obsolete-firmware__detail-section" style="--item-index: 4">
                            <h4>使用统计</h4>
                            <div class="obsolete-firmware__detail-divider"></div>
                            <p><strong>使用次数：</strong>{{ versionDetails.usageCount }} 次</p>
                            <p><strong>下载次数：</strong>{{ versionDetails.downloadCount }} 次</p>
                        </div>
                        
                        <div class="obsolete-firmware__detail-section" style="--item-index: 5">
                            <h4>版本使用要求</h4>
                            <div class="obsolete-firmware__detail-divider"></div>
                            <pre class="obsolete-firmware__detail-requirements">{{ versionDetails.versionRequirements || '暂无' }}</pre>
                        </div>
                        
                        <div class="obsolete-firmware__detail-section" style="--item-index: 6">
                            <h4>版本描述</h4>
                            <div class="obsolete-firmware__detail-divider"></div>
                            <p class="obsolete-firmware__detail-description">{{ versionDetails.description || '暂无' }}</p>
                        </div>
                    </div>
                </div>
                
                <template #footer>
                    <div class="obsolete-firmware__detail-footer">
                        <el-button @click="versionDetailDialogVisible = false">关闭</el-button>
                    </div>
                </template>
            </el-dialog>
            
            <!-- 统计分析对话框 -->
            <el-dialog
                title="作废版本统计分析"
                v-model="statisticsDialogVisible"
                class="obsolete-firmware__statistics-dialog">
                
                <div class="obsolete-firmware__statistics-content">
                    <div class="obsolete-firmware__statistics-summary">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-card class="obsolete-firmware__stat-card">
                                    <div class="obsolete-firmware__stat-number">{{ statistics.totalObsolete }}</div>
                                    <div class="obsolete-firmware__stat-label">作废版本总数</div>
                                </el-card>
                            </el-col>
                            <el-col :span="8">
                                <el-card class="obsolete-firmware__stat-card">
                                    <div class="obsolete-firmware__stat-number">{{ statistics.avgEffectiveDays }}</div>
                                    <div class="obsolete-firmware__stat-label">平均生效天数</div>
                                </el-card>
                            </el-col>
                            <el-col :span="8">
                                <el-card class="obsolete-firmware__stat-card">
                                    <div class="obsolete-firmware__stat-number">{{ totalUsageCount }}</div>
                                    <div class="obsolete-firmware__stat-label">总使用次数</div>
                                </el-card>
                            </el-col>
                        </el-row>
                    </div>
                    
                    <el-row :gutter="20" style="margin-top: 20px;">
                        <el-col :span="24">
                            <el-card>
                                <template #header>
                                    <span>作废原因分析</span>
                                </template>
                                <div class="obsolete-firmware__reason-list">
                                    <div 
                                        v-for="(item, index) in statistics.topReasons" 
                                        :key="item.reason"
                                        class="obsolete-firmware__reason-item">
                                        <span class="obsolete-firmware__reason-number">{{ index + 1 }}</span>
                                        <div class="obsolete-firmware__reason-info">
                                            <div class="obsolete-firmware__reason-name">{{ item.reason }}</div>
                                            <div class="obsolete-firmware__reason-count">{{ item.count }} 个版本</div>
                                        </div>
                                        <div class="obsolete-firmware__reason-bar">
                                            <div 
                                                class="obsolete-firmware__reason-progress"
                                                :style="{ width: (item.count / statistics.totalObsolete * 100) + '%' }">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
                
                <template #footer>
                    <div class="obsolete-firmware__statistics-footer">
                        <el-button @click="statisticsDialogVisible = false">关闭</el-button>
                        <el-button type="primary" @click="exportData">
                            <el-icon><Download /></el-icon>
                            导出统计报告
                        </el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
        `
    };

    const app = createApp(ObsoleteFirmwareApp);
        
    // 注册Element Plus图标 - 添加安全检查
    if (window.ElementPlusIconsVue && typeof window.ElementPlusIconsVue === 'object') {
        for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
            app.component(key, component);
        }
    }
    
    app.use(ElementPlus);

    const mountApp = () => {
        try {
            app.mount('#obsolete-firmware-app-container');
            Logger.log('Obsolete Firmware Vue app mounted successfully');
            
            // 保存应用实例供后续使用
            window.currentFirmwareApp = app;
        } catch (error) {
            Logger.error('Failed to mount Obsolete Firmware Vue app:', error);
        }
    };

    // Defer mounting until the DOM is fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', mountApp);
    } else {
        mountApp();
    }
})(); 