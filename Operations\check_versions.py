import sys
import pkg_resources
import re
from pathlib import Path

# ANSI 颜色代码
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'

def read_requirements():
    """读取 requirements.txt 文件中的版本要求"""
    requirements = {}
    req_file = Path(__file__).parent / 'requirements.txt'
    
    try:
        with open(req_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 解析包名和版本号
                    parts = re.split('==|>=|<=|~=', line)
                    if len(parts) == 2:
                        requirements[parts[0]] = line
    except FileNotFoundError:
        print(f"{Colors.YELLOW}Warning: requirements.txt not found{Colors.ENDC}")
    
    return requirements

def check_package_version(package_name, required_version=None):
    """检查包的安装版本"""
    try:
        installed = pkg_resources.get_distribution(package_name)
        version = installed.version
        status = f"{Colors.GREEN}已安装{Colors.ENDC}"
        
        if required_version:
            req_spec = pkg_resources.Requirement.parse(required_version)
            if installed.version not in req_spec:
                status = f"{Colors.RED}版本不匹配{Colors.ENDC}"
                return f"{package_name}: 已安装 v{version} (要求: {required_version}) - {status}"
        
        return f"{package_name}: v{version} - {status}"
    except pkg_resources.DistributionNotFound:
        return f"{package_name}: {Colors.RED}未安装{Colors.ENDC}"
    except Exception as e:
        return f"{package_name}: {Colors.RED}检查失败 ({str(e)}){Colors.ENDC}"

def check_direct_import(module_name, import_name=None):
    """尝试直接导入模块并获取版本"""
    import_name = import_name or module_name
    try:
        module = __import__(import_name)
        version = getattr(module, '__version__', '未知')
        return f"{module_name} (imported): v{version} - {Colors.GREEN}成功{Colors.ENDC}"
    except ImportError:
        return f"{module_name}: {Colors.RED}导入失败{Colors.ENDC}"
    except Exception as e:
        return f"{module_name}: {Colors.RED}检查失败 ({str(e)}){Colors.ENDC}"

def main():
    # 获取 requirements.txt 中的版本要求
    required_versions = read_requirements()
    
    # 打印系统信息
    print(f"\n{Colors.BLUE}=== 系统信息 ==={Colors.ENDC}")
    print(f"Python 版本: {sys.version.split()[0]}")
    
    # 检查必需的包
    print(f"\n{Colors.BLUE}=== 包版本检查 ==={Colors.ENDC}")
    packages = [
        'Flask',
        'flask-cors',
        'PyJWT',
        'SQLAlchemy',
        'mysqlclient',
        'requests',
        'Werkzeug'
    ]
    
    for package in packages:
        required = required_versions.get(package)
        print(check_package_version(package, required))
    
    # 直接导入检查
    print(f"\n{Colors.BLUE}=== 导入测试 ==={Colors.ENDC}")
    imports = [
        ('Flask', 'flask'),
        ('Flask-CORS', 'flask_cors'),
        ('PyJWT', 'jwt'),
        ('SQLAlchemy', 'sqlalchemy'),
        ('mysqlclient', 'MySQLdb'),
        ('requests', 'requests'),
        ('Werkzeug', 'werkzeug')
    ]
    
    for name, import_name in imports:
        print(check_direct_import(name, import_name))

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}检查被用户中断{Colors.ENDC}")
    except Exception as e:
        print(f"\n{Colors.RED}检查过程出错: {str(e)}{Colors.ENDC}")
    
    # 在 Windows 上保持窗口打开
    if sys.platform.startswith('win'):
        input("\n按 Enter 键退出...") 