// 使用记录页面Vue应用
(function() {
    'use strict';
    
    Logger.log('Loading Usage Record Vue App...');
    
    // 检查共享分页功能是否已加载
    if (!window.FirmwarePagination) {
        Logger.error('FirmwarePagination not loaded. Please include firmware-common-paging.js first.');
        return;
    }
    
    if (!window.Vue || !window.ElementPlus) {
        Logger.error('Vue or ElementPlus not loaded');
        return;
    }

    const { createApp, ref, reactive, computed, onMounted, onUnmounted, nextTick } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    
    const UsageRecordApp = {
        setup() {
            // ===== 响应式数据 =====
            const searchQuery = ref('');
            const loading = ref(false);
            const sortProp = ref('');
            const sortOrder = ref('');
            
            // 时间范围筛选
            const dateRange = ref([]);
            
            // 对话框状态
            const usageDetailDialogVisible = ref(false);
            const statisticsDialogVisible = ref(false);
            
            // 使用详情数据
            const usageDetails = reactive({
                workOrder: '',
                productCode: '',
                productModel: '',
                productionCount: 0,
                firmwareInfo: {
                    serialNumber: '',
                    name: '',
                    version: '',
                    developer: '',
                    approveTime: ''
                },
                versions: {
                    software: '',
                    backplane: '',
                    highSpeedIO: ''
                },
                buildTime: '',
                usageTime: '',
                usageUser: '',
                notes: '',
                downloadPath: ''
            });
            
            // 统计数据
            const statistics = reactive({
                totalRecords: 0,
                totalProduction: 0,
                popularFirmware: [],
                usageByMonth: [],
                topUsers: []
            });
            
            // 获取全局数据管理器
            const dataManager = window.FirmwareDataManager;
            
            // 使用记录列表
            const usageRecords = ref([]);
            
            // ===== 录入PCBA序列号和SN号相关 =====
            const entryDialogVisible = ref(false);
            const entryForm = reactive({ snDigits: '', pcba: '', sn: '', unlockInputs: false });
            const entryCount = ref(0); // 录入计数
            const entryRow = ref(null); // 改为响应式数据
            
            // ===== 序列号明细对话框相关 =====
            const entryDetailDialogVisible = ref(false);
            const entryDetailList = ref([]);
            const entryDetailLoading = ref(false);
            const entryDetailSearch = ref(''); // 明细搜索关键词
            
            // 验证序列号长度的函数
            const validateSerialLength = (value, fieldName) => {
                const digits = parseInt(entryForm.snDigits);
                if (!digits || digits <= 0) return;
                
                const trimmedValue = value && value.trim();
                if (trimmedValue && trimmedValue.length !== digits) {
                    ElMessage.warning(`${fieldName}长度应为${digits}位，当前为${trimmedValue.length}位`);
                }
            };
            
            // 加载数据 - 改为异步，支持时间范围参数
            const loadData = async () => {
                loading.value = true;
                try {
                    const params = {};
                    
                    // 如果用户选择了时间范围，传递给后端
                    if (dateRange.value && dateRange.value.length === 2) {
                        // 安全的日期转换函数
                        const formatDate = (date) => {
                            if (!date) return null;
                            
                            // 如果已经是字符串格式的日期，直接返回
                            if (typeof date === 'string') {
                                // 验证字符串格式是否为 YYYY-MM-DD
                                if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
                                    return date;
                                }
                                // 尝试解析字符串为Date对象
                                const parsedDate = new Date(date);
                                if (!isNaN(parsedDate.getTime())) {
                                    return parsedDate.toISOString().split('T')[0];
                                }
                            }
                            
                            // 如果是Date对象，转换为字符串
                            if (date instanceof Date && !isNaN(date.getTime())) {
                                return date.toISOString().split('T')[0];
                            }
                            
                            // 如果都不是，返回null
                            return null;
                        };
                        
                        const startDate = formatDate(dateRange.value[0]);
                        const endDate = formatDate(dateRange.value[1]);
                        
                        if (startDate && endDate) {
                            params.start_date = startDate;
                            params.end_date = endDate;
                        }
                    }
                    
                    const data = await dataManager.getDownloadRecords(params);
                    usageRecords.value = data?.items || data || []; // 兼容不同的响应格式
                    Logger.log('[Usage Record] 数据加载成功:', (data?.items || data || []).length, '条记录');
                } catch (error) {
                    Logger.error('[Usage Record] 数据加载失败:', error);
                    ElMessage.error('数据加载失败: ' + error.message);
                    usageRecords.value = [];
                } finally {
                    loading.value = false;
                }
            };
            
            // 事件监听器
            let eventListeners = [];
            
            // ===== 计算属性 =====
            const filteredUsageRecords = computed(() => {
                let list = [...usageRecords.value];
                
                // 搜索过滤
                if (searchQuery.value) {
                    const query = searchQuery.value.toLowerCase();
                    list = list.filter(item => {
                        // 安全的字符串搜索函数
                        const safeIncludes = (field) => {
                            return field && typeof field === 'string' && field.toLowerCase().includes(query);
                        };
                        
                        return safeIncludes(item.workOrder) ||
                               safeIncludes(item.productCode) ||
                               safeIncludes(item.productModel) ||
                               safeIncludes(item.firmwareName) ||
                               safeIncludes(item.firmwareVersion) ||
                               safeIncludes(item.userName) ||
                               safeIncludes(item.developer) ||
                               safeIncludes(item.notes) ||
                               safeIncludes(item.softwareVersion) ||
                               safeIncludes(item.buildTime) ||
                               safeIncludes(item.backplaneVersion) ||
                               safeIncludes(item.ioVersion);
                    });
                }
                
                // 注意：时间范围过滤已移除，因为后端已经按时间过滤了数据
                
                // 排序
                if (sortProp.value && sortOrder.value) {
                    list.sort((a, b) => {
                        let aVal = a[sortProp.value];
                        let bVal = b[sortProp.value];
                        
                        // 处理null/undefined值，将其放在排序的末尾
                        if (aVal == null && bVal == null) return 0;
                        if (aVal == null) return 1;
                        if (bVal == null) return -1;
                        
                        // 对于字符串类型，转为小写进行比较
                        if (typeof aVal === 'string' && typeof bVal === 'string') {
                            aVal = aVal.toLowerCase();
                            bVal = bVal.toLowerCase();
                        }
                        
                        if (aVal < bVal) return sortOrder.value === 'ascending' ? -1 : 1;
                        if (aVal > bVal) return sortOrder.value === 'ascending' ? 1 : -1;
                        return 0;
                    });
                }
                
                return list;
            });
            
            // 使用共享分页功能（在filteredUsageRecords定义之后）
            const pagination = window.FirmwarePagination.usePagination({
                dataList: filteredUsageRecords,
                defaultPageSize: 10,
                pageSizeOptions: [10, 20, 50, 100]
            });
            
            // 当前页显示的数据（使用共享分页功能）
            const paginatedUsageRecords = pagination.paginatedData;
            
            // 统计信息计算
            const totalProductionCount = computed(() => {
                return filteredUsageRecords.value.reduce((sum, record) => {
                    return sum + (record.burnCount || 0);
                }, 0);
            });
            
            const uniqueFirmwareCount = computed(() => {
                const uniqueFirmware = new Set(
                    filteredUsageRecords.value.map(record => record.serialNumber)
                );
                return uniqueFirmware.size;
            });
            
            // 过滤后的明细列表（用于搜索）
            const filteredEntryDetailList = computed(() => {
                if (!entryDetailSearch.value.trim()) {
                    return entryDetailList.value;
                }
                
                const searchTerm = entryDetailSearch.value.toLowerCase().trim();
                return entryDetailList.value.filter(item => {
                    // 搜索SN号和PCBA序列号
                    const snMatch = item.snNumber && item.snNumber !== '无' && 
                                   item.snNumber.toLowerCase().includes(searchTerm);
                    const pcbaMatch = item.pcbaNumber && item.pcbaNumber !== '无' && 
                                     item.pcbaNumber.toLowerCase().includes(searchTerm);
                    return snMatch || pcbaMatch;
                });
            });
            
            // ===== 方法 =====
            
            // 搜索相关
            const handleSearch = () => {
                Logger.log('搜索:', searchQuery.value);
                // 搜索时重置到第一页
                pagination.resetToFirstPage();
            };
            
            const handleSearchClear = () => {
                searchQuery.value = '';
                // 清空搜索时重置到第一页
                pagination.resetToFirstPage();
            };
            
            // 排序
            const handleSortChange = (column) => {
                sortProp.value = column.prop;
                sortOrder.value = column.order;
                Logger.log('排序变更:', column.prop, column.order);
            };
            
            // 时间范围变更 - 改为触发后端查询
            const handleDateRangeChange = (value) => {
                dateRange.value = value;
                Logger.log('时间范围:', value);
                // 时间范围变更时重新从后端加载数据
                loadData();
            };
            
            // 清空筛选条件
            const clearFilters = () => {
                searchQuery.value = '';
                dateRange.value = [];
                // 清空筛选时重新加载数据（因为时间范围清空会影响后端查询）
                loadData();
                ElMessage.success('筛选条件已清空');
            };
            
            // 显示使用详情
            const showUsageDetail = (row) => {
                Object.assign(usageDetails, {
                    workOrder: row.workOrder,
                    productCode: row.productCode,
                    productModel: row.productModel,
                    productionCount: row.burnCount,
                    firmwareInfo: {
                        serialNumber: row.serialNumber,
                        name: row.firmwareName,
                        version: row.firmwareVersion,
                        developer: row.developer,
                        approveTime: row.approveTime
                    },
                    versions: {
                        software: row.softwareVersion,
                        backplane: row.backplaneVersion,
                        highSpeedIO: row.ioVersion
                    },
                    buildTime: row.buildTime,
                    usageTime: row.usageTime,
                    usageUser: row.userName,
                    notes: row.notes || '无',
                    downloadPath: `/downloads/${row.serialNumber}_${row.firmwareVersion}.bin`
                });
                usageDetailDialogVisible.value = true;
            };
            
            // 计算统计数据
            const calculateStatistics = () => {
                loading.value = true;
                
                // 模拟计算统计数据
                setTimeout(() => {
                    statistics.totalRecords = filteredUsageRecords.value.length;
                    statistics.totalProduction = totalProductionCount.value;
                    
                    // 热门固件统计
                    const firmwareUsage = {};
                    filteredUsageRecords.value.forEach(record => {
                        const key = `${record.firmwareName} ${record.firmwareVersion}`;
                        if (!firmwareUsage[key]) {
                            firmwareUsage[key] = {
                                name: key,
                                serialNumber: record.serialNumber,
                                count: 0,
                                totalProduction: 0
                            };
                        }
                        firmwareUsage[key].count++;
                        firmwareUsage[key].totalProduction += record.burnCount;
                    });
                    
                    statistics.popularFirmware = Object.values(firmwareUsage)
                        .sort((a, b) => b.totalProduction - a.totalProduction)
                        .slice(0, 5);
                    
                    // 使用用户统计
                    const userUsage = {};
                    filteredUsageRecords.value.forEach(record => {
                        if (!userUsage[record.userName]) {
                            userUsage[record.userName] = {
                                name: record.userName,
                                count: 0,
                                totalProduction: 0
                            };
                        }
                        userUsage[record.userName].count++;
                        userUsage[record.userName].totalProduction += record.burnCount;
                    });
                    
                    statistics.topUsers = Object.values(userUsage)
                        .sort((a, b) => b.totalProduction - a.totalProduction)
                        .slice(0, 5);
                    
                    statisticsDialogVisible.value = true;
                    loading.value = false;
                }, 1000);
            };
            
            // 导出数据
            const exportData = () => {
                if (window.FirmwareUtils && window.FirmwareUtils.exportToExcel) {
                    const exportData = filteredUsageRecords.value.map(item => ({
                        'ERP流水号': item.serialNumber,
                        '固件名称': item.firmwareName,
                        '固件版本': item.firmwareVersion,
                        '工单号': item.workOrder,
                        '产品编码': item.productCode,
                        '产品型号': item.productModel,
                        '生产数量': item.burnCount,
                        '软件版本': item.softwareVersion,
                        '构建日期': item.buildTime,
                        '背板总线版本': item.backplaneVersion,
                        '高速IO版本': item.ioVersion,
                        '使用时间': item.usageTime,
                        '使用人': item.userName,
                        '备注': item.notes || ''
                    }));
                    
                    // 安全的日期格式化，复用之前定义的formatDate函数
                    const formatDateSafe = (date) => {
                        if (!date) return null;
                        
                        // 如果已经是字符串格式的日期，直接返回
                        if (typeof date === 'string') {
                            // 验证字符串格式是否为 YYYY-MM-DD
                            if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
                                return date;
                            }
                            // 尝试解析字符串为Date对象
                            const parsedDate = new Date(date);
                            if (!isNaN(parsedDate.getTime())) {
                                return parsedDate.toISOString().split('T')[0];
                            }
                        }
                        
                        // 如果是Date对象，转换为字符串
                        if (date instanceof Date && !isNaN(date.getTime())) {
                            return date.toISOString().split('T')[0];
                        }
                        
                        return null;
                    };
                    
                    const dateStr = dateRange.value.length === 2 ? 
                        (() => {
                            const startDate = formatDateSafe(dateRange.value[0]);
                            const endDate = formatDateSafe(dateRange.value[1]);
                            return startDate && endDate ? `_${startDate}_${endDate}` : '';
                        })() : '';
                    
                    window.FirmwareUtils.exportToExcel(exportData, `固件使用记录${dateStr}`);
                } else {
                    ElMessage.info('正在导出使用记录数据...');
                }
            };
            
            // 刷新数据
            const refreshData = () => {
                loading.value = true;
                // 模拟API调用
                setTimeout(() => {
                    loadData();
                    ElMessage.success('数据已刷新');
                    loading.value = false;
                }, 1000);
            };
            
            // ===== 录入PCBA序列号和SN号相关 =====
            const openEntryDialog = async (row) => {
                entryForm.snDigits = '';
                entryForm.pcba = '';
                entryForm.sn = '';
                entryForm.unlockInputs = false;
                entryRow.value = row; // 保存当前行数据
                
                try {
                    // 从后端获取录入进度
                    const response = await fetch(`/api/firmware/serial-entry/${row.workOrder}/progress`);
                    const result = await response.json();
                    
                    if (result.success) {
                        entryCount.value = result.data.progress.totalEntered;
                    } else {
                        entryCount.value = 0; // 重置录入计数
                        Logger.warn('获取录入进度失败:', result.message);
                    }
                } catch (error) {
                    entryCount.value = 0; // 重置录入计数
                    Logger.warn('获取录入进度失败:', error);
                }
                
                entryDialogVisible.value = true;
            };

            const submitEntry = async () => {
                // 验证SN号位数
                const digits = parseInt(entryForm.snDigits);
                if (!digits || digits <= 0) {
                    ElMessage.error('请输入有效的产品SN号位数');
                    return;
                }
                
                // 根据工单号前缀判断录入类型
                const isFGOrGZWorkOrder = entryRow.value?.workOrder?.startsWith('FG') || entryRow.value?.workOrder?.startsWith('GZ');
                let serialType, serialValue;
                
                if (entryForm.unlockInputs) {
                    // 解除限制模式：根据用户实际输入判断
                    const snValue = entryForm.sn?.trim();
                    const pcbaValue = entryForm.pcba?.trim();
                    
                    if (!snValue && !pcbaValue) {
                        ElMessage.error('请至少录入SN号或PCBA序列号中的一项');
                        return;
                    }
                    
                    if (snValue && pcbaValue) {
                        ElMessage.error('只能录入SN号或PCBA序列号中的一项，不能同时录入');
                        return;
                    }
                    
                    if (snValue) {
                        if (snValue.length !== digits) {
                            ElMessage.error(`SN号长度必须为${digits}位`);
                            return;
                        }
                        serialType = 'SN';
                        serialValue = snValue;
                    } else {
                        if (pcbaValue.length !== digits) {
                            ElMessage.error(`PCBA序列号长度必须为${digits}位`);
                            return;
                        }
                        serialType = 'PCBA';
                        serialValue = pcbaValue;
                    }
                } else {
                    // 原有限制模式
                    if (isFGOrGZWorkOrder) {
                        // FG开头或GZ开头的工单，只能录入SN号
                        const snValue = entryForm.sn.trim();
                        if (!snValue) {
                            ElMessage.error('请录入SN号');
                            return;
                        }
                        if (snValue.length !== digits) {
                            ElMessage.error(`SN号长度必须为${digits}位`);
                            return;
                        }
                        serialType = 'SN';
                        serialValue = snValue;
                    } else {
                        // 非FG/GZ开头的工单，只能录入PCBA序列号
                        const pcbaValue = entryForm.pcba.trim();
                        if (!pcbaValue) {
                            ElMessage.error('请录入PCBA序列号');
                            return;
                        }
                        if (pcbaValue.length !== digits) {
                            ElMessage.error(`PCBA序列号长度必须为${digits}位`);
                            return;
                        }
                        serialType = 'PCBA';
                        serialValue = pcbaValue;
                    }
                }
                
                try {
                    loading.value = true;
                    
                    // 调用后端API录入序列号
                    const response = await fetch(`/api/firmware/serial-entry/${entryRow.value.workOrder}/entry`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            serialType: serialType,
                            serialValue: serialValue,
                            serialDigits: digits
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // 录入成功，更新计数和显示消息
                        entryCount.value = result.data.entryProgress.enteredCount;
                        const progress = result.data.entryProgress;
                        
                        if (progress.isCompleted) {
                            ElMessage.success(`录入完成！已录入数量与生产数量一致(${progress.targetCount})`);
                        } else {
                            const remaining = progress.remaining;
                            ElMessage.info(`录入成功！还需录入${remaining}条记录`);
                        }
                        
                        // 清空输入框，准备下一次录入
                        entryForm.sn = '';
                        entryForm.pcba = '';
                        
                        // 如果已完成所有录入，延迟关闭对话框
                        if (progress.isCompleted) {
                            setTimeout(() => {
                                entryDialogVisible.value = false;
                            }, 1500);
                        }
                    } else {
                        ElMessage.error(result.message || '录入失败');
                    }
                } catch (error) {
                    Logger.error('录入序列号失败:', error);
                    ElMessage.error('录入失败: ' + error.message);
                } finally {
                    loading.value = false;
                }
            };
            
            // 显示序列号录入明细
            const showEntryDetail = async (row) => {
                entryRow.value = row; // 保存当前行数据，供删除后刷新使用
                entryDetailDialogVisible.value = true;
                entryDetailLoading.value = true;
                entryDetailList.value = [];
                entryDetailSearch.value = ''; // 清空搜索框
                
                try {
                    // 调用后端API获取序列号录入明细（设置较大的perPage以获取所有记录）
                    const response = await fetch(`/api/firmware/serial-entry/${row.workOrder}/records?perPage=1500`);
                    const result = await response.json();
                    
                    if (result.success && result.data.records) {
                        entryDetailList.value = result.data.records.map(record => ({
                            id: record.id,  // 保留id字段，用于删除操作
                            serialType: record.serialType,
                            snNumber: record.serialType === 'SN' ? record.firmwareSn : '无',
                            pcbaNumber: record.serialType === 'PCBA' ? record.firmwarePcba : '无',
                            inputTime: record.inputTime,
                            inputUser: record.inputUser
                        }));
                    } else {
                        entryDetailList.value = [];
                        ElMessage.warning(result.message || '暂无序列号录入记录');
                    }
                } catch (error) {
                    Logger.error('获取序列号明细失败:', error);
                    ElMessage.error('获取明细信息失败: ' + error.message);
                    entryDetailList.value = [];
                } finally {
                    entryDetailLoading.value = false;
                }
            };
            
            // 生命周期
            onMounted(async () => {
                Logger.log('Usage Record page mounted');
                await loadData();
                
                // 监听数据更新事件
                const onDataUpdate = async () => {
                    Logger.log('[Usage Record] 收到数据更新事件，重新加载数据');
                    await loadData();
                };
                
                eventListeners = [
                    { event: 'download-record-added', handler: onDataUpdate },
                    { event: 'firmware-downloaded', handler: onDataUpdate }
                ];
                
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.on(event, handler);
                });
            });
            
            onUnmounted(() => {
                // 清理事件监听器
                eventListeners.forEach(({ event, handler }) => {
                    dataManager.off(event, handler);
                });
            });
            
            // 删除使用记录
            const deleteUsageRecord = async (row) => {
                ElMessageBox.confirm(`确定要删除该条使用记录吗？<br><br><strong>工单号：</strong>${row.workOrder}<br><strong>产品型号：</strong>${row.productModel}<br><strong>固件：</strong>${row.firmwareName} ${row.firmwareVersion}`, '删除确认', {
                    type: 'warning',
                    dangerouslyUseHTMLString: true,
                    confirmButtonText: '确定删除',
                    cancelButtonText: '取消'
                }).then(async () => {
                    try {
                        const response = await fetch(`/api/firmware/usage/delete/${row.id}`, {
                            method: 'DELETE'
                        });
                        const result = await response.json();
                        if (result.success) {
                            ElMessage.success('使用记录删除成功');
                            // 重新加载数据
                            await loadData();
                        } else {
                            ElMessage.error(result.message || '删除失败');
                        }
                    } catch (e) {
                        ElMessage.error('删除失败: ' + e.message);
                    }
                }).catch(() => {
                    // 用户取消删除
                });
            };

            // 在setup()方法内添加如下方法：
            const handleDeleteDetail = async (row) => {
                ElMessageBox.confirm('确定要删除该条序列号记录吗？', '提示', { type: 'warning' })
                    .then(async () => {
                        try {
                            const response = await fetch(`/api/firmware/serial-entry/detail/${row.id}`, {
                                method: 'DELETE'
                            });
                            const result = await response.json();
                            if (result.success) {
                                ElMessage.success('删除成功');
                                // 重新加载明细 - 使用保存的工单行数据
                                if (entryRow.value && entryRow.value.workOrder) {
                                    showEntryDetail(entryRow.value);
                                } else {
                                    // 如果entryRow不可用，直接刷新明细列表
                                    entryDetailDialogVisible.value = false;
                                    ElMessage.warning('请重新打开明细查看');
                                }
                            } else {
                                ElMessage.error(result.message || '删除失败');
                            }
                        } catch (e) {
                            ElMessage.error('删除失败: ' + e.message);
                        }
                    });
            };
            
            return {
                // 响应式数据
                searchQuery,
                loading,
                dateRange,
                usageRecords,
                filteredUsageRecords,
                paginatedUsageRecords,
                totalProductionCount,
                uniqueFirmwareCount,
                
                // 分页数据（使用共享分页功能）
                ...pagination,
                
                // 对话框状态
                usageDetailDialogVisible,
                statisticsDialogVisible,
                usageDetails,
                statistics,
                
                // 方法
                handleSearch,
                handleSearchClear,
                handleSortChange,
                handleDateRangeChange,
                clearFilters,
                showUsageDetail,
                calculateStatistics,
                exportData,
                refreshData,
                
                // ===== 录入PCBA序列号和SN号相关 =====
                entryDialogVisible,
                entryForm,
                entryCount,
                entryRow,
                openEntryDialog,
                submitEntry,
                validateSerialLength,
                
                // ===== 序列号明细对话框相关 =====
                entryDetailDialogVisible,
                entryDetailList,
                entryDetailLoading,
                entryDetailSearch,
                filteredEntryDetailList,
                showEntryDetail,
                deleteUsageRecord,
                handleDeleteDetail
            };
        },
        
        template: `
        <div class="firmware-container usage-record firmware-page">
            <div class="usage-record__header">
                <div class="usage-record__header-left">
                    <h3 class="usage-record__title">固件使用记录</h3>
                    <span class="firmware-page__tip usage-record__tip">
                        （单击工单号，查看概要。Tip:若工单产品是cpu,不用录入明细。若工单产品是io或耦合器：新生产录入pcba序列号；返工录入SN号）
                    </span>
                </div>
                <div class="usage-record__stats">
                    <el-tag type="success" size="large">
                        记录数: {{ filteredUsageRecords.length }}
                    </el-tag>
                    <el-tag type="warning" size="large">
                        固件种类: {{ uniqueFirmwareCount }}
                    </el-tag>
                    <el-tag type="info" size="large">
                        总生产量: {{ totalProductionCount }}
                    </el-tag>
                </div>
            </div>
            
            <div class="usage-record__search-bar">
                <el-input
                    class="usage-record__search-input"
                    placeholder="请输入工单号、产品编码/型号、固件信息、使用人或备注进行搜索"
                    v-model="searchQuery"
                    clearable
                    @clear="handleSearchClear"
                    @keyup.enter="handleSearch">
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
                
                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    @change="handleDateRangeChange"
                    class="usage-record__date-picker">
                </el-date-picker>
                
                <div class="usage-record__actions">
                    <el-button @click="clearFilters" class="firmware-page__action-button firmware-page__action-button--info">
                        <el-icon><RefreshLeft /></el-icon>
                        清空筛选
                    </el-button>
                    <el-button type="success" @click="calculateStatistics" :loading="loading" class="firmware-page__action-button firmware-page__action-button--success">
                        <el-icon><PieChart /></el-icon>
                        统计分析
                    </el-button>
                    <el-button type="info" @click="refreshData" :loading="loading" class="firmware-page__action-button firmware-page__action-button--info">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                    <el-button type="primary" @click="exportData" class="firmware-page__action-button firmware-page__action-button--primary">
                        <el-icon><Download /></el-icon>
                        导出数据
                    </el-button>
                </div>
            </div>
            
            <div class="usage-record__table-container">
                <el-table 
                    :data="paginatedUsageRecords" 
                    style="width: 100%"
                    border
                    size="small"
                    v-loading="loading"
                    @sort-change="handleSortChange"
                    :default-sort="{prop: 'usageTime', order: 'descending'}">
                    
                    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                    
                    <el-table-column prop="serialNumber" label="ERP流水号" width="150" sortable="custom" show-overflow-tooltip></el-table-column>
                    
                    <el-table-column prop="firmwareName" label="固件名称" min-width="200" sortable="custom" show-overflow-tooltip></el-table-column>
                    

                    <el-table-column prop="firmwareStatus" label="是否作废" width="100" align="center" sortable="custom">
                        <template #default="scope">
                            <el-tag :type="scope.row.firmwareStatus === 'obsolete' ? 'danger' : 'success'">
                                {{ scope.row.firmwareStatus === 'obsolete' ? '是' : '否' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="workOrder" label="工单号" width="200" sortable="custom" show-overflow-tooltip>
                        <template #default="scope">
                            <el-link 
                                type="primary"
                                @click="showUsageDetail(scope.row)"
                                class="usage-record__work-order-link">
                                {{ scope.row.workOrder }}
                            </el-link>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="productCode" label="产品编码" width="130" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="productModel" label="产品型号" width="220" sortable="custom" show-overflow-tooltip></el-table-column>
                    
                    <el-table-column prop="burnCount" label="生产数量" width="100" sortable="custom" align="right">
                        <template #default="scope">
                            <el-tag type="warning" size="small">{{ scope.row.burnCount }}</el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="softwareVersion" label="软件版本" width="120" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="buildTime" label="构建日期" width="160" sortable="custom"></el-table-column>
                    <el-table-column prop="backplaneVersion" label="背板总线版本" width="130" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="ioVersion" label="高速IO版本" width="130" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="usageTime" label="使用时间" width="160" sortable="custom"></el-table-column>
                    <el-table-column prop="userName" label="使用人" width="100" sortable="custom" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="notes" label="备注" min-width="110" show-overflow-tooltip></el-table-column>
                    <el-table-column label="删除记录" width="100" align="center">
                        <template #default="scope">
                            <el-button size="small" type="danger" @click="deleteUsageRecord(scope.row)" class="firmware-page__action-button firmware-page__action-button--danger">删除</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="160" align="center" fixed="right">
                        <template #default="scope">
                            <el-button size="small" type="primary" @click="openEntryDialog(scope.row)" class="firmware-page__action-button firmware-page__action-button--primary">录入</el-button>
                            <el-button size="small" type="info" @click="showEntryDetail(scope.row)" class="firmware-page__action-button firmware-page__action-button--info">明细</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页组件（使用共享模板） -->
                <div class="table-footer">
                    <div class="table-length">
                        <div class="page-size-selector">
                            每页
                            <select @change="handleSizeChange($event.target.value)">
                                <option 
                                    v-for="size in pageSizes" 
                                    :key="size" 
                                    :value="size" 
                                    :selected="size === pageSize">
                                    {{ size }}
                                </option>
                            </select>
                            条
                        </div>
                        <div class="total-count">
                            共计 <span>{{ totalCount }}</span> 条
                        </div>
                    </div>
                    <div class="table-pagination">
                        <button 
                            class="btn btn-icon" 
                            @click="goToFirstPage" 
                            :disabled="currentPage === 1">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button 
                            class="btn btn-icon" 
                            @click="goToPrevPage" 
                            :disabled="currentPage === 1">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <div class="pagination-pages">
                            <span v-if="startPage > 1" class="pagination-ellipsis">...</span>
                            <button 
                                v-for="page in visiblePages" 
                                :key="page"
                                class="btn btn-page" 
                                :class="{ active: page === currentPage }"
                                @click="goToPage(page)">
                                {{ page }}
                            </button>
                            <span v-if="endPage < totalPages" class="pagination-ellipsis">...</span>
                        </div>
                        <button 
                            class="btn btn-icon" 
                            @click="goToNextPage" 
                            :disabled="currentPage === totalPages">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button 
                            class="btn btn-icon" 
                            @click="goToLastPage" 
                            :disabled="currentPage === totalPages">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 使用详情对话框 -->
            <el-dialog
                title="工单固件使用详情"
                v-model="usageDetailDialogVisible"
                width="60%"
                class="usage-record__detail-dialog">
                
                <div class="usage-record__detail-content">
                    <div class="usage-record__detail-header">
                        <div class="usage-record__detail-title">
                            <el-tag type="success" size="large" class="usage-record__tag-primary">
                                <i class="el-icon-document"></i> 工单号：{{ usageDetails.workOrder }}
                            </el-tag>
                        </div>
                    </div>
                    
                    <div class="usage-record__detail-body">
                        <div class="usage-record__detail-left">
                            <div class="usage-record__detail-panel">
                                <div class="usage-record__panel-header">
                                    <i class="el-icon-tickets"></i> 工单信息
                                </div>
                                <div class="usage-record__panel-content">
                                    <div class="usage-record__info-grid">
                                        <div class="usage-record__info-item">
                                            <span class="usage-record__info-label">产品编码</span>
                                            <span class="usage-record__info-value">{{ usageDetails.productCode }}</span>
                                        </div>
                                        <div class="usage-record__info-item">
                                            <span class="usage-record__info-label">产品型号</span>
                                            <span class="usage-record__info-value">{{ usageDetails.productModel }}</span>
                                        </div>
                                        <div class="usage-record__info-item">
                                            <span class="usage-record__info-label">生产数量</span>
                                            <span class="usage-record__info-value usage-record__info-highlight">{{ usageDetails.productionCount }}</span>
                                        </div>
                                        <div class="usage-record__info-item">
                                            <span class="usage-record__info-label">使用人</span>
                                            <span class="usage-record__info-value">{{ usageDetails.usageUser }}</span>
                                        </div>
                                        <div class="usage-record__info-item">
                                            <span class="usage-record__info-label">使用时间</span>
                                            <span class="usage-record__info-value">{{ usageDetails.usageTime }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="usage-record__notes-section">
                                        <div class="usage-record__notes-title">备注信息</div>
                                        <div class="usage-record__notes">
                                            {{ usageDetails.notes }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="usage-record__detail-right">
                            <div class="usage-record__detail-panel">
                                <div class="usage-record__panel-header">
                                    <i class="el-icon-cpu"></i> 固件信息
                                </div>
                                <div class="usage-record__panel-content">
                                    <div class="usage-record__firmware-card">
                                        <div class="usage-record__firmware-info">
                                            <div class="usage-record__firmware-item">
                                                <span class="usage-record__firmware-label">ERP流水号</span>
                                                <span class="usage-record__firmware-value">{{ usageDetails.firmwareInfo.serialNumber }}</span>
                                            </div>
                                            <div class="usage-record__firmware-item">
                                                <span class="usage-record__firmware-label">固件名称</span>
                                                <span class="usage-record__firmware-value">{{ usageDetails.firmwareInfo.name }}</span>
                                            </div>
                                            <div class="usage-record__firmware-item">
                                                <span class="usage-record__firmware-label">软件版本</span>
                                                <span class="usage-record__firmware-value usage-record__firmware-value--highlight">{{ usageDetails.versions.software }}</span>
                                            </div>
                                            <div class="usage-record__firmware-item">
                                                <span class="usage-record__firmware-label">构建日期</span>
                                                <span class="usage-record__firmware-value usage-record__firmware-value--highlight">{{ usageDetails.buildTime }}</span>
                                            </div>
                                            <div class="usage-record__firmware-item">
                                                <span class="usage-record__firmware-label">背板总线版本</span>
                                                <span class="usage-record__firmware-value usage-record__firmware-value--highlight">{{ usageDetails.versions.backplane || '无' }}</span>
                                            </div>
                                            <div class="usage-record__firmware-item">
                                                <span class="usage-record__firmware-label">高速IO版本</span>
                                                <span class="usage-record__firmware-value usage-record__firmware-value--highlight">{{ usageDetails.versions.highSpeedIO || '无' }}</span>
                                            </div>
                                            <div class="usage-record__firmware-item">
                                                <span class="usage-record__firmware-label">研发者</span>
                                                <span class="usage-record__firmware-value">{{ usageDetails.firmwareInfo.developer }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <template #footer>
                    <div class="usage-record__detail-footer">
                        <el-button @click="usageDetailDialogVisible = false" class="usage-record__button">
                            <i class="el-icon-close"></i> 关闭
                        </el-button>
                    </div>
                </template>
            </el-dialog>
            
            <!-- 统计分析对话框 -->
            <el-dialog
                title="使用记录统计分析"
                v-model="statisticsDialogVisible"
                width="60%"
                class="usage-record__statistics-dialog">
                
                <div class="usage-record__statistics-content">
                    <div class="usage-record__statistics-header">
                        <div class="usage-record__statistics-title">使用数据概览</div>
                    </div>
                    
                    <div class="usage-record__statistics-summary">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div class="usage-record__stat-card">
                                    <div class="usage-record__stat-icon">
                                        <i class="el-icon-data-line"></i>
                                    </div>
                                    <div class="usage-record__stat-content">
                                        <div class="usage-record__stat-number">{{ statistics.totalRecords }}</div>
                                        <div class="usage-record__stat-label">总使用记录</div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="usage-record__stat-card">
                                    <div class="usage-record__stat-icon">
                                        <i class="el-icon-box"></i>
                                    </div>
                                    <div class="usage-record__stat-content">
                                        <div class="usage-record__stat-number">{{ statistics.totalProduction }}</div>
                                        <div class="usage-record__stat-label">总生产数量</div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="usage-record__stat-card">
                                    <div class="usage-record__stat-icon">
                                        <i class="el-icon-cpu"></i>
                                    </div>
                                    <div class="usage-record__stat-content">
                                        <div class="usage-record__stat-number">{{ statistics.popularFirmware.length }}</div>
                                        <div class="usage-record__stat-label">活跃固件数</div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                    
                    <div class="usage-record__statistics-section">
                        <div class="usage-record__statistics-header">
                            <div class="usage-record__statistics-title">使用数据排行</div>
                        </div>
                        
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="usage-record__ranking-panel">
                                    <div class="usage-record__ranking-header">
                                        <i class="el-icon-medal"></i> 热门固件排行
                                    </div>
                                    <div class="usage-record__ranking-body">
                                        <div class="usage-record__ranking-list">
                                            <div 
                                                v-for="(item, index) in statistics.popularFirmware" 
                                                :key="item.serialNumber"
                                                class="usage-record__ranking-item">
                                                <span class="usage-record__ranking-number" :class="'usage-record__ranking-' + (index < 3 ? ['gold', 'silver', 'bronze'][index] : 'normal')">
                                                    {{ index + 1 }}
                                                </span>
                                                <div class="usage-record__ranking-info">
                                                    <div class="usage-record__ranking-name">{{ item.name }}</div>
                                                    <div class="usage-record__ranking-details">
                                                        <div class="usage-record__ranking-stat">
                                                            <i class="el-icon-download"></i> 使用{{ item.count }}次
                                                        </div>
                                                        <div class="usage-record__ranking-stat">
                                                            <i class="el-icon-box"></i> 生产{{ item.totalProduction }}台
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="usage-record__ranking-bar">
                                                    <div class="usage-record__ranking-progress" :style="{ width: (item.totalProduction / statistics.totalProduction * 100) + '%' }"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                            
                            <el-col :span="12">
                                <div class="usage-record__ranking-panel">
                                    <div class="usage-record__ranking-header">
                                        <i class="el-icon-user"></i> 活跃用户排行
                                    </div>
                                    <div class="usage-record__ranking-body">
                                        <div class="usage-record__ranking-list">
                                            <div 
                                                v-for="(item, index) in statistics.topUsers" 
                                                :key="item.name"
                                                class="usage-record__ranking-item">
                                                <span class="usage-record__ranking-number" :class="'usage-record__ranking-' + (index < 3 ? ['gold', 'silver', 'bronze'][index] : 'normal')">
                                                    {{ index + 1 }}
                                                </span>
                                                <div class="usage-record__ranking-info">
                                                    <div class="usage-record__ranking-name">{{ item.name }}</div>
                                                    <div class="usage-record__ranking-details">
                                                        <div class="usage-record__ranking-stat">
                                                            <i class="el-icon-files"></i> 使用{{ item.count }}次
                                                        </div>
                                                        <div class="usage-record__ranking-stat">
                                                            <i class="el-icon-box"></i> 生产{{ item.totalProduction }}台
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="usage-record__ranking-bar">
                                                    <div class="usage-record__ranking-progress" :style="{ width: (item.totalProduction / statistics.totalProduction * 100) + '%' }"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                
                <template #footer>
                    <div class="usage-record__statistics-footer">
                        <el-button @click="statisticsDialogVisible = false" class="usage-record__button-cancel">
                            <i class="el-icon-close"></i> 关闭
                        </el-button>
                        <el-button type="primary" @click="exportData" class="usage-record__button-export">
                            <i class="el-icon-download"></i> 导出统计报告
                        </el-button>
                    </div>
                </template>
            </el-dialog>

            <!-- 录入PCBA序列号和SN号的模态框 -->
            <el-dialog 
                title="录入产品序列号信息" 
                v-model="entryDialogVisible" 
                width="800px"
                :close-on-click-modal="false"
                class="usage-record__entry-dialog">
                
                <div class="usage-record__entry-content">
                    <div class="usage-record__entry-header">
                        <div class="usage-record__workorder-info">
                            <div class="usage-record__workorder-card">
                                <div class="usage-record__workorder-title">
                                    <i class="el-icon-document"></i> 工单信息
                                </div>
                                <div class="usage-record__workorder-details">
                                    <div class="usage-record__workorder-item">
                                        <span class="usage-record__workorder-label">工单号</span>
                                        <span class="usage-record__workorder-value">{{ entryRow?.workOrder || '' }}</span>
                                    </div>
                                    <div class="usage-record__workorder-item">
                                        <span class="usage-record__workorder-label">产品型号</span>
                                        <span class="usage-record__workorder-value">{{ entryRow?.productModel || '' }}</span>
                                    </div>
                                    <div class="usage-record__workorder-item">
                                        <span class="usage-record__workorder-label">生产数量</span>
                                        <span class="usage-record__workorder-value">{{ entryRow?.burnCount || 0 }} 台</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="usage-record__entry-progress">
                            <div class="usage-record__stat-box usage-record__stat-box--entered">
                                <div class="usage-record__stat-label">已录入</div>
                                <div class="usage-record__stat-value usage-record__stat-value--entered">
                                    <i class="el-icon-check"></i> {{ entryCount }}
                                </div>
                            </div>
                            <div class="usage-record__stat-box usage-record__stat-box--remaining">
                                <div class="usage-record__stat-label">剩余</div>
                                <div class="usage-record__stat-value usage-record__stat-value--remaining">
                                    <i :class="entryCount >= (entryRow?.burnCount || 0) ? 'el-icon-circle-check' : 'el-icon-warning'"></i>
                                    {{ Math.max(0, (entryRow?.burnCount || 0) - entryCount) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="usage-record__entry-body">
                        <div class="usage-record__entry-form">
                            <el-form :model="entryForm" label-width="120px">
                                <div class="usage-record__form-group">
                                    <div class="usage-record__form-title">录入设置</div>
                                    <el-form-item label="产品SN号位数" required class="usage-record__form-item">
                                        <el-input 
                                            v-model="entryForm.snDigits" 
                                            placeholder="请输入产品SN号位数（如：6、8、10等）"
                                            type="number"
                                            :min="1"
                                            :max="50"
                                            class="usage-record__input">
                                            <template #append>位</template>
                                        </el-input>
                                        <div class="usage-record__form-help">设定后，序列号的长度必须与此位数一致</div>
                                    </el-form-item>
                                    
                                    <div class="usage-record__switch-container">
                                        <el-icon class="usage-record__switch-icon"><Setting /></el-icon>
                                        <el-switch
                                            v-model="entryForm.unlockInputs"
                                            active-text="解除限制"
                                            inactive-text="按工单类型"
                                            class="usage-record__switch">
                                        </el-switch>
                                        <span class="usage-record__switch-emoji">💡</span>
                                        <span class="usage-record__switch-help">开启后可同时启用SN号和PCBA序列号输入框，根据实际需要灵活录入</span>
                                    </div>
                                </div>
                                
                                <div class="usage-record__form-group">
                                    <div class="usage-record__form-title">
                                        序列号录入
                                        <el-tag v-if="!entryForm.unlockInputs" size="small" :type="(entryRow?.workOrder?.startsWith('FG') || entryRow?.workOrder?.startsWith('GZ')) ? 'success' : 'warning'" class="usage-record__form-tag">
                                            {{ (entryRow?.workOrder?.startsWith('FG') || entryRow?.workOrder?.startsWith('GZ')) ? 'FG/GZ工单：仅SN号' : '其他工单：仅PCBA序列号' }}
                                        </el-tag>
                                        <el-tag v-else size="small" type="info" class="usage-record__form-tag">
                                            自由录入：可选择SN号或PCBA序列号，不可同时录入
                                        </el-tag>
                                    </div>
                                    
                                    <el-form-item label="PCBA序列号" class="usage-record__form-item">
                                        <el-input 
                                            v-model="entryForm.pcba" 
                                            :placeholder="entryForm.unlockInputs ? '请输入PCBA序列号（可选）' : ((entryRow?.workOrder?.startsWith('FG') || entryRow?.workOrder?.startsWith('GZ')) ? '该工单类型不需要录入PCBA序列号' : '请输入PCBA序列号')"
                                            :disabled="entryForm.unlockInputs ? false : (entryRow?.workOrder?.startsWith('FG') || entryRow?.workOrder?.startsWith('GZ'))"
                                            clearable
                                            @blur="validateSerialLength(entryForm.pcba, 'PCBA序列号')"
                                            @keyup.enter="submitEntry"
                                            class="usage-record__input">
                                        </el-input>
                                    </el-form-item>
                                    
                                    <el-form-item label="SN号" class="usage-record__form-item">
                                        <el-input 
                                            v-model="entryForm.sn" 
                                            :placeholder="entryForm.unlockInputs ? '请输入SN号（可选）' : (!(entryRow?.workOrder?.startsWith('FG') || entryRow?.workOrder?.startsWith('GZ')) ? '该工单类型不需要录入SN号' : '请输入SN号')"
                                            :disabled="entryForm.unlockInputs ? false : !(entryRow?.workOrder?.startsWith('FG') || entryRow?.workOrder?.startsWith('GZ'))"
                                            clearable
                                            @blur="validateSerialLength(entryForm.sn, 'SN号')"
                                            @keyup.enter="submitEntry"
                                            class="usage-record__input">
                                        </el-input>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>
                
                <template #footer>
                    <div class="usage-record__entry-footer">
                        <el-button @click="entryDialogVisible = false" class="usage-record__button-cancel">
                            <i class="el-icon-close"></i> 取消
                        </el-button>
                        <el-button type="primary" @click="submitEntry" class="usage-record__button-submit">
                            <i class="el-icon-check"></i> 保存录入
                        </el-button>
                    </div>
                </template>
            </el-dialog>

            <!-- 序列号录入明细对话框 -->
            <el-dialog 
                title="工单序列号录入明细" 
                v-model="entryDetailDialogVisible" 
                width="900px"
                class="usage-record__entry-detail-dialog">
                
                <div class="usage-record__entry-detail-content">
                    <div class="usage-record__entry-detail-header">
                        <div class="usage-record__entry-detail-info">
                            <div class="usage-record__entry-detail-workorder">
                                <i class="el-icon-document-checked"></i> 
                                工单：<span class="usage-record__entry-detail-value">{{ entryRow?.workOrder || '未选择' }}</span>
                            </div>
                            <div class="usage-record__entry-detail-product">
                                <i class="el-icon-goods"></i> 
                                产品型号：<span class="usage-record__entry-detail-value">{{ entryRow?.productModel || '未知' }}</span>
                            </div>
                            <div class="usage-record__entry-detail-count">
                                <i class="el-icon-tickets"></i> 
                                共计<span class="usage-record__entry-detail-value">{{ entryDetailList.length }}</span>条记录
                            </div>
                        </div>

                    </div>
                    
                    <div class="usage-record__entry-detail-body" v-loading="entryDetailLoading">
                        <!-- 搜索框 -->
                        <div class="usage-record__entry-search">
                            <el-input
                                v-model="entryDetailSearch"
                                placeholder="搜索SN号或PCBA序列号..."
                                clearable
                                class="usage-record__search-input"
                                style="margin-bottom: 15px;">
                                <template #prefix>
                                    <el-icon><Search /></el-icon>
                                </template>
                            </el-input>
                        </div>
                        
                        <div class="usage-record__entry-detail-table">
                            <el-table 
                                :data="filteredEntryDetailList" 
                                style="width: 100%"
                                border
                                size="small"
                                empty-text="暂无匹配的序列号记录"
                                class="usage-record__entry-table">
                                
                                <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                                
                                <el-table-column prop="serialType" label="类型" width="120" align="center">
                                    <template #default="scope">
                                        <div class="usage-record__type-tag" :class="scope.row.serialType === 'SN' ? 'usage-record__type-tag--sn' : 'usage-record__type-tag--pcba'">
                                            <i :class="scope.row.serialType === 'SN' ? 'el-icon-price-tag' : 'el-icon-cpu'"></i>
                                            {{ scope.row.serialType === 'SN' ? 'SN号' : 'PCBA序列号' }}
                                        </div>
                                    </template>
                                </el-table-column>
                                
                                <el-table-column prop="snNumber" label="SN号" width="210" align="center">
                                    <template #default="scope">
                                        <div class="usage-record__serial-value" :class="scope.row.snNumber === '无' ? 'usage-record__serial-empty' : 'usage-record__serial-sn'">
                                            {{ scope.row.snNumber }}
                                        </div>
                                    </template>
                                </el-table-column>
                                
                                <el-table-column prop="pcbaNumber" label="PCBA序列号" width="200" align="center">
                                    <template #default="scope">
                                        <div class="usage-record__serial-value" :class="scope.row.pcbaNumber === '无' ? 'usage-record__serial-empty' : 'usage-record__serial-pcba'">
                                            {{ scope.row.pcbaNumber }}
                                        </div>
                                    </template>
                                </el-table-column>
                                
                                <el-table-column prop="inputTime" label="录入时间" width="180" align="center">
                                    <template #default="scope">
                                        <div class="usage-record__time-value">
                                            <i class="el-icon-time"></i>
                                            {{ scope.row.inputTime }}
                                        </div>
                                    </template>
                                </el-table-column>
                                
                                <el-table-column prop="inputUser" label="录入人员" width="120" align="center">
                                    <template #default="scope">
                                        <div class="usage-record__user-value">
                                            <i class="el-icon-user"></i>
                                            {{ scope.row.inputUser }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="80" align="center">
                                    <template #default="scope">
                                        <el-button type="danger" size="small" @click="handleDeleteDetail(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
                
                <template #footer>
                    <div class="usage-record__entry-detail-footer">
                        <el-button @click="entryDetailDialogVisible = false" class="usage-record__button-cancel">
                            <i class="el-icon-close"></i> 关闭
                        </el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
        `
    };

    const app = createApp(UsageRecordApp);
        
    // 注册Element Plus图标 - 添加安全检查
    if (window.ElementPlusIconsVue && typeof window.ElementPlusIconsVue === 'object') {
        for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
            app.component(key, component);
        }
    }
    
    app.use(ElementPlus);

    const mountApp = () => {
        try {
            app.mount('#usage-record-app-container');
            Logger.log('Usage Record Vue app mounted successfully');
            
            // 保存应用实例供后续使用
            window.currentFirmwareApp = app;
        } catch (error) {
            Logger.error('Failed to mount Usage Record Vue app:', error);
        }
    };

    // Defer mounting until the DOM is fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', mountApp);
    } else {
        mountApp();
    }
})(); 