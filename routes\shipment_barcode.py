from flask import Blueprint, request, jsonify, send_file
from database.db_manager import DatabaseManager
from models.shipment import Order, SNRecord
from sqlalchemy.exc import IntegrityError
from datetime import datetime
from jwt import decode
import logging
import xlsxwriter
from io import BytesIO
from werkzeug.utils import secure_filename
import pandas as pd
import os

# 导入版本比对模块中的查询函数，复用现有逻辑
from routes.version_comparison import get_work_order_by_sn
from models.firmware import Firmware, DownloadRecord
from models.modelwork_order import WorkOrder

# 导入JWT配置
from config import JWT_SECRET

# 创建蓝图
shipment_barcode_bp = Blueprint('shipment_barcode', __name__)
logger = logging.getLogger(__name__)

def sanitize_string(value):
    """去除字符串首尾的空格"""
    return value.strip() if isinstance(value, str) else value

@shipment_barcode_bp.route('/add', methods=['POST'])
def add_shipment_record():
    try:
        data = request.get_json()
        
        # 获取并清理数据
        box_number = sanitize_string(data.get('boxNumber', ''))
        shipment_number = sanitize_string(data.get('shipmentNumber', ''))
        customer_name = sanitize_string(data.get('customerName', ''))
        sn_list = [sanitize_string(sn) for sn in data.get('snList', [])]
        
        # 获取当前用户信息
        token = request.cookies.get('token')
        if not token:
            return jsonify({'success': False, 'message': '未登录或登录已过期'}), 401
            
        user_data = decode(token, JWT_SECRET, algorithms=["HS256"])
        username = user_data['username']
        
        # 验证必填字段
        if not all([box_number, shipment_number, customer_name, sn_list]):
            return jsonify({
                'success': False,
                'message': '请填写所有必填字段'
            }), 400
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 创建订单记录
            new_order = Order(
                box_serial_number=box_number,
                shipping_number=shipment_number,
                customer_name=customer_name,
                created_user=username
            )
            
            try:
                session.add(new_order)
                session.flush()
                
                # 批量创建SN记录
                sn_records = []
                duplicate_sns = []
                for sn in sn_list:
                    # 检查SN号是否已存在
                    existing_sn = session.query(SNRecord).filter(
                        SNRecord.sn_number == sn
                    ).first()
                    
                    if existing_sn:
                        duplicate_sns.append(sn)
                    
                    sn_records.append(
                        SNRecord(
                            order_id=new_order.order_id,
                            sn_number=sn,
                            created_user=username
                        )
                    )
                
                session.bulk_save_objects(sn_records)
                session.commit()
                
                # 如果有重复的SN号，在成功保存后返回提示信息
                if duplicate_sns:
                    return jsonify({
                        'success': True,
                        'message': '出货记录保存成功',
                        'warning': f'以下SN号已存在于系统中：{", ".join(duplicate_sns)}',
                        'hasDuplicates': True
                    })
                
                return jsonify({
                    'success': True,
                    'message': '出货记录保存成功'
                })
                
            except Exception as e:
                session.rollback()
                logger.error(f"Database error: {str(e)}")
                return jsonify({
                    'success': False,
                    'message': '数据库错误，请联系管理员'
                }), 500
                    
    except Exception as e:
        logger.error(f"Error in add_shipment_record: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请联系管理员'
        }), 500

@shipment_barcode_bp.route('/check-duplicate', methods=['POST'])
def check_duplicate():
    try:
        data = request.get_json()
        field = data.get('field')
        value = sanitize_string(data.get('value', ''))
        
        if not field or not value:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            if field == 'boxNumber':
                # 由于不再是唯一约束，这里可以返回false或者检查是否存在相同的组合
                exists = False  # 或者根据业务需求修改检查逻辑
            elif field == 'shipmentNumber':
                # 由于不再是唯一约束，这里可以返回false或者检查是否存在相同的组合
                exists = False  # 或者根据业务需求修改检查逻辑
            elif field == 'snNumber':
                exists = session.query(SNRecord).filter(
                    SNRecord.sn_number == value
                ).first() is not None
            else:
                return jsonify({
                    'success': False,
                    'message': '无效的字段名'
                }), 400
                
            return jsonify({
                'success': True,
                'exists': exists
            })
            
    except Exception as e:
        logger.error(f"Error in check_duplicate: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请联系管理员'
        }), 500

@shipment_barcode_bp.route('/add-sn', methods=['POST'])
def add_single_sn():
    try:
        data = request.get_json()
        
        # 获取并清理数据
        box_number = sanitize_string(data.get('boxNumber', ''))
        shipment_number = sanitize_string(data.get('shipmentNumber', ''))
        customer_name = sanitize_string(data.get('customerName', ''))
        sn_number = sanitize_string(data.get('snNumber', ''))
        
        # 获取当前用户信息
        token = request.cookies.get('token')
        if not token:
            return jsonify({'success': False, 'message': '未登录或登录已过期'}), 401
            
        user_data = decode(token, JWT_SECRET, algorithms=["HS256"])
        username = user_data['username']
        
        # 验证必填字段
        if not all([box_number, shipment_number, customer_name, sn_number]):
            return jsonify({
                'success': False,
                'message': '请填写所有必填字段'
            }), 400
        
        db = DatabaseManager()
        with db.get_session() as session:
            try:
                # 检查是否已存在相同的箱单流水号和出库单号的订单
                order = session.query(Order).filter(
                    Order.box_serial_number == box_number,
                    Order.shipping_number == shipment_number
                ).first()
                
                # 如果订单不存在，创建新订单
                if not order:
                    order = Order(
                        box_serial_number=box_number,
                        shipping_number=shipment_number,
                        customer_name=customer_name,
                        created_user=username
                    )
                    session.add(order)
                    session.flush()  # 获取order_id
                
                # 检查SN号是否已存在
                existing_sn = session.query(SNRecord).filter(
                    SNRecord.sn_number == sn_number
                ).first()
                
                # 创建新的SN记录
                sn_record = SNRecord(
                    order_id=order.order_id,
                    sn_number=sn_number,
                    created_user=username
                )
                
                session.add(sn_record)
                session.commit()
                
                # 返回新添加的SN记录信息，如果是重复的SN号则添加警告信息
                response_data = {
                    'success': True,
                    'message': 'SN号添加成功',
                    'data': {
                        'recordId': sn_record.record_id,
                        'snNumber': sn_record.sn_number,
                        'createdAt': sn_record.created_at.strftime('%Y-%m-%d %H:%M:%S')
                    }
                }
                
                if existing_sn:
                    response_data['warning'] = f'SN号 {sn_number} 已存在于系统中'
                    response_data['hasDuplicates'] = True
                
                return jsonify(response_data)
                
            except Exception as e:
                session.rollback()
                logger.error(f"Database error: {str(e)}")
                return jsonify({
                    'success': False,
                    'message': '数据库错误，请联系管理员'
                }), 500
                    
    except Exception as e:
        logger.error(f"Error in add_single_sn: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请联系管理员'
        }), 500

def get_sn_version_status_core(session, sn_number, expected_work_order=None):
    """
    核心版本状态检查逻辑，供多个功能复用
    
    Args:
        session: 数据库session
        sn_number: SN号
        expected_work_order: 期望的工单号，用于批次验证
        
    Returns:
        dict: 包含is_active、status_text、actual_work_order、batch_status的字典
    """
    try:
        # 获取工单号（版本检查需要）
        work_order = get_work_order_by_sn(session, sn_number)
        
        # 批次状态检查逻辑 - 简化处理
        batch_status = ''  # 默认为空
        if expected_work_order and expected_work_order.strip() != '无':
            # 只在需要批次比对时进行比对
            if work_order:
                batch_status = '正常' if work_order == expected_work_order.strip() else '异常'
            else:
                batch_status = '异常'
        
        if not work_order:
            return {
                'is_active': None, 
                'status_text': '未知',
                'actual_work_order': None,
                'batch_status': batch_status
            }
        
        # 版本状态检查逻辑
        try:
            # 使用工单号查询download_record表，获取ERP流水号
            download_record = session.query(DownloadRecord).filter(
                DownloadRecord.work_order == work_order,
                DownloadRecord.is_deleted == False
            ).order_by(DownloadRecord.create_time.desc()).first()
            
            if download_record:
                # 根据ERP流水号查询firmware表，检查status字段
                firmware = session.query(Firmware).filter(
                    Firmware.serial_number == download_record.serial_number,
                    Firmware.is_deleted == False
                ).first()
                
                if firmware:
                    is_active = firmware.status == 'active'
                    status_text = '正常' if is_active else '异常、需升级'
                    return {
                        'is_active': is_active, 
                        'status_text': status_text,
                        'actual_work_order': work_order,
                        'batch_status': batch_status
                    }
                else:
                    return {
                        'is_active': None, 
                        'status_text': '未知',
                        'actual_work_order': work_order,
                        'batch_status': batch_status
                    }
            else:
                return {
                    'is_active': None, 
                    'status_text': '未知',
                    'actual_work_order': work_order,
                    'batch_status': batch_status
                }
                
        except Exception as e:
            logger.warning(f"SN {sn_number} 版本状态检查失败: {str(e)}")
            return {
                'is_active': None, 
                'status_text': '未知',
                'actual_work_order': work_order if 'work_order' in locals() else None,
                'batch_status': batch_status if 'batch_status' in locals() else ''
            }
            
    except Exception as e:
        logger.warning(f"SN {sn_number} 版本状态检查异常: {str(e)}")
        return {
            'is_active': None, 
            'status_text': '未知',
            'actual_work_order': None,
            'batch_status': ''
        }


@shipment_barcode_bp.route('/get-sn-list', methods=['GET'])
def get_sn_list():
    try:
        box_number = request.args.get('boxNumber', '').strip()
        shipment_number = request.args.get('shipmentNumber', '').strip()
        expected_work_order = request.args.get('workOrder', '').strip()
        
        if not box_number or not shipment_number:
            return jsonify({
                'success': False,
                'message': '流水和出库单号不能为空'
            }), 400
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 查找订单
            order = session.query(Order).filter(
                Order.box_serial_number == box_number,
                Order.shipping_number == shipment_number
            ).first()
            
            if not order:
                return jsonify({
                    'success': True,
                    'data': []
                })
            
            # 查询该订单下的所有SN记录，按创建时间降序排序
            sn_records = session.query(SNRecord).filter(
                SNRecord.order_id == order.order_id
            ).order_by(SNRecord.created_at.desc()).all()
            
            # 格式化结果，增加版本状态和批次状态
            result = []
            for record in sn_records:
                # 检查版本状态和批次状态（使用公共逻辑）
                version_info = get_sn_version_status_core(session, record.sn_number, expected_work_order)
                
                result.append({
                    'recordId': record.record_id,
                    'snNumber': record.sn_number,
                    'createdAt': record.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'versionStatus': version_info['status_text'],
                    'batchStatus': version_info['batch_status']
                })
            
            return jsonify({
                'success': True,
                'data': result
            })
            
    except Exception as e:
        logger.error(f"Error in get_sn_list: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请联系管理员'
        }), 500

@shipment_barcode_bp.route('/delete-sn/<int:record_id>', methods=['DELETE'])
def delete_sn(record_id):
    try:
        # 获取当前用户信息
        token = request.cookies.get('token')
        if not token:
            return jsonify({'success': False, 'message': '未登录或登录已过期'}), 401
            
        user_data = decode(token, JWT_SECRET, algorithms=["HS256"])
        username = user_data['username']
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 查找SN记录
            sn_record = session.query(SNRecord).filter(
                SNRecord.record_id == record_id
            ).first()
            
            if not sn_record:
                return jsonify({
                    'success': False,
                    'message': '未找到该SN记录'
                }), 404
            
            # 删除SN记录
            session.delete(sn_record)
            session.commit()
            
            return jsonify({
                'success': True,
                'message': 'SN号删除成功'
            })
            
    except Exception as e:
        logger.error(f"Error in delete_sn: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请联系管理员'
        }), 500

@shipment_barcode_bp.route('/export-excel', methods=['POST'])
def export_excel():
    try:
        data = request.get_json()
        box_number = data.get('boxNumber')
        shipment_number = data.get('shipmentNumber')
        customer_name = data.get('customerName')
        
        if not all([box_number, shipment_number, customer_name]):
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        # 创建一个内存中的Excel文件
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('SN列表')

        # 设置表头样式
        header_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#f3f4f6',
            'border': 1
        })

        # 设置数据行样式
        data_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })

        # 调整表头顺序，与列表显示顺序一致，增加版本状态和批次状态列
        headers = ['序号', '产品SN号', '版本状态', '批次状态', '录入时间', '储存状态', '箱单流水号', '出库单号', '客户名称']
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # 从数据库获取SN记录
        db = DatabaseManager()
        with db.get_session() as session:
            order = session.query(Order).filter(
                Order.box_serial_number == box_number,
                Order.shipping_number == shipment_number
            ).first()

            if not order:
                return jsonify({
                    'success': False,
                    'message': '未找到相关订单'
                }), 404

            sn_records = session.query(SNRecord).filter(
                SNRecord.order_id == order.order_id
            ).order_by(SNRecord.created_at.desc()).all()

            # 写入数据行，调整列顺序与表头一致，增加版本状态和批次状态
            for row, record in enumerate(sn_records, start=1):
                # 检查版本状态和批次状态（使用公共逻辑）
                # 需要获取工单号参数进行批次比对
                expected_work_order = data.get('workOrder', '无')
                version_info = get_sn_version_status_core(session, record.sn_number, expected_work_order)
                version_status = version_info['status_text']
                batch_status = version_info['batch_status']
                
                worksheet.write(row, 0, row, data_format)  # 序号
                worksheet.write(row, 1, record.sn_number, data_format)  # 产品SN号
                worksheet.write(row, 2, version_status, data_format)  # 版本状态
                worksheet.write(row, 3, batch_status, data_format)  # 批次状态
                worksheet.write(row, 4, record.created_at.strftime('%Y-%m-%d %H:%M:%S'), data_format)  # 录入时间
                worksheet.write(row, 5, '完成', data_format)  # 储存状态
                worksheet.write(row, 6, box_number, data_format)  # 箱单流水号
                worksheet.write(row, 7, shipment_number, data_format)  # 出库单号
                worksheet.write(row, 8, customer_name, data_format)  # 客户名称

            # 调整列宽，增加版本状态和批次状态列的宽度
            column_widths = [8, 20, 12, 12, 20, 10, 15, 15, 20]  # 根据内容调整各列宽度
            for col, width in enumerate(column_widths):
                worksheet.set_column(col, col, width)

        workbook.close()
        output.seek(0)

        # 生成文件名
        filename = f'出货条码_{box_number}_{shipment_number}_{datetime.now().strftime("%Y%m%d")}.xlsx'
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"Error in export_excel: {str(e)}")
        return jsonify({
            'success': False,
            'message': '导出失败，请联系管理员'
        }), 500

@shipment_barcode_bp.route('/import-excel', methods=['POST'])
def import_excel():
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '未找到上传的文件'
            }), 400

        file = request.files['file']
        if not file or not file.filename:
            return jsonify({
                'success': False,
                'message': '未选择文件'
            }), 400

        # 获取其他表单数据
        box_number = request.form.get('boxNumber')
        shipment_number = request.form.get('shipmentNumber')
        customer_name = request.form.get('customerName')

        if not all([box_number, shipment_number, customer_name]):
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        # 读取Excel文件
        df = pd.read_excel(file)
        
        # 验证Excel文件格式
        required_columns = ['产品SN号']  # 可以根据需要添加更多必需列
        if not all(col in df.columns for col in required_columns):
            return jsonify({
                'success': False,
                'message': 'Excel文件格式不正确，请确保包含产品SN号列'
            }), 400

        # 获取当前用户信息
        token = request.cookies.get('token')
        if not token:
            return jsonify({'success': False, 'message': '未登录或登录已过期'}), 401
        user_data = decode(token, JWT_SECRET, algorithms=["HS256"])
        username = user_data['username']

        db = DatabaseManager()
        with db.get_session() as session:
            # 检查或创建订单
            order = session.query(Order).filter(
                Order.box_serial_number == box_number,
                Order.shipping_number == shipment_number
            ).first()

            if not order:
                order = Order(
                    box_serial_number=box_number,
                    shipping_number=shipment_number,
                    customer_name=customer_name,
                    created_user=username
                )
                session.add(order)
                session.flush()

            # 导入SN记录
            import_count = 0
            for _, row in df.iterrows():
                sn_number = str(row['产品SN号']).strip()
                if not sn_number:
                    continue

                # 检查SN号是否已存在
                existing_sn = session.query(SNRecord).filter(
                    SNRecord.sn_number == sn_number
                ).first()

                if not existing_sn:
                    sn_record = SNRecord(
                        order_id=order.order_id,
                        sn_number=sn_number,
                        created_user=username
                    )
                    session.add(sn_record)
                    import_count += 1

            session.commit()

            return jsonify({
                'success': True,
                'message': '导入成功',
                'importCount': import_count
            })

    except Exception as e:
        logger.error(f"Error in import_excel: {str(e)}")
        return jsonify({
            'success': False,
            'message': '导入失败，请检查文件格式或联系管理员'
        }), 500

@shipment_barcode_bp.route('/check-sn-info', methods=['POST'])
def check_sn_info():
    """
    检查SN号的版本状态（用于实时检查和提示）
    
    功能：版本状态检查：SN号 → 测试记录表 → 工单号 → download_record表 → firmware表 → status是否为active
    
    Returns:
        JSON响应包含版本状态信息
    """
    try:
        data = request.get_json()
        sn_number = sanitize_string(data.get('sn', ''))
        expected_work_order = sanitize_string(data.get('workOrder', ''))
        
        if not sn_number:
            return jsonify({
                'success': False,
                'message': '请提供SN号'
            }), 400
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 使用公共逻辑进行版本状态和批次状态检查
            version_info = get_sn_version_status_core(session, sn_number, expected_work_order)
            
            if not version_info['actual_work_order']:
                return jsonify({
                    'success': False,
                    'message': f'未找到SN号 {sn_number} 对应的工单记录'
                }), 404
            
            # 初始化返回结果
            result = {
                'success': True,
                'sn': sn_number,
                'work_order': version_info['actual_work_order'],
                'version_status': {
                    'is_active': version_info['is_active'],
                    'message': None
                },
                'batch_status': {
                    'status': version_info['batch_status'],
                    'message': None
                }
            }
            
            # 版本状态检查
            if version_info['is_active'] is False:
                result['version_status']['message'] = '软件版本异常'
            
            # 批次状态检查
            if version_info['batch_status'] == '异常':
                result['batch_status']['message'] = '批次号异常'
            
            logger.info(f"SN {sn_number} 检查完成 - 版本: {version_info['status_text']}, 批次: {version_info['batch_status']}")
            
            return jsonify(result)
            
    except Exception as e:
        logger.error(f"SN信息检查失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请联系管理员'
        }), 500 