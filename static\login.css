:root {
    --primary-color: #0066cc;
    --hover-color: #004d99;
    --active-color: #003d7a;
    --success-color: #2e7d32;
    --error-color: #d32f2f;
    --bg-color: #1e272e;
    --box-bg: rgba(28, 36, 44, 0.8);
    --input-bg: rgba(28, 36, 44, 0.6);
    --glow-color: #0066cc;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Source Sans Pro', sans-serif;
    background: var(--bg-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* 动态背景 */
.background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 0;
    background: var(--bg-color);
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
    position: relative;
    z-index: 1;
}

.login-box {
    background: var(--box-bg);
    border-radius: 15px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    padding: 40px;
    animation: boxAppear 0.6s ease-out;
}

@keyframes boxAppear {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.logo-container {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
}

.login-header img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    position: relative;
    z-index: 1;
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    filter: blur(20px);
    opacity: 0.3;
    animation: glow 2s ease-in-out infinite;
}

@keyframes glow {
    0%, 100% { transform: translate(-50%, -50%) scale(0.8); }
    50% { transform: translate(-50%, -50%) scale(1.2); }
}

.login-header h2 {
    margin: 0;
    color: #fff;
    font-size: 28px;
    font-weight: 600;
}

.subtitle {
    color: #7f8fa6;
    margin: 10px 0 0;
    font-size: 16px;
}

.login-form .form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 18px;
    z-index: 1;
}

.form-group input {
    width: 100%;
    padding: 12px 20px 12px 45px;
    background: var(--input-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 16px;
    color: #fff;
    box-sizing: border-box;
    transition: all 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 15px rgba(0, 168, 255, 0.3);
}

.input-focus-effect {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s;
}

.form-group input:focus ~ .input-focus-effect {
    width: 100%;
}

.error-message {
    color: var(--error-color);
    font-size: 14px;
    margin: -15px 0 15px;
    min-height: 20px;
    text-align: center;
}

button {
    width: 100%;
    padding: 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 45px;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 77, 153, 0.4);
    background: var(--hover-color);
}

button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(0, 77, 153, 0.4);
    background: var(--active-color);
}

.button-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

button:hover .button-effect {
    width: 200%;
    height: 200%;
}

.loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 0.8s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.success-checkmark {
    display: none;
    color: #fff;
    font-size: 20px;
    animation: checkmark-appear 0.3s ease-out;
}

@keyframes checkmark-appear {
    from {
        transform: scale(0);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

button:disabled {
    background: #4d88b8;
    color: rgba(255, 255, 255, 0.8);
    cursor: not-allowed;
    transform: none;
}

@keyframes success-animation {
    0% {
        background: var(--primary-color);
    }
    50% {
        background: var(--success-color);
    }
    100% {
        background: var(--primary-color);
    }
}

.success {
    animation: success-animation 1.5s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }
    
    .login-box {
        padding: 20px;
    }
    
    .login-header h2 {
        font-size: 24px;
    }
}

/* 在现有样式的基础上添加以下内容 */

.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    color: #fff;
}

.checkbox-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 35px;
    cursor: pointer;
    font-size: 14px;
    user-select: none;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: var(--input-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.checkbox-container:hover input ~ .checkmark {
    border-color: var(--primary-color);
}

.checkbox-container input:checked ~ .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 7px;
    top: 3px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container input:checked ~ .checkmark {
    animation: checkmark-pop 0.3s ease-out;
}

@keyframes checkmark-pop {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.error-message {
    color: var(--error-color);
    font-weight: 500;
    font-size: 14px;
    margin: -15px 0 15px;
    min-height: 20px;
    text-align: center;
}

button:focus {
    outline: 3px solid rgba(0, 102, 204, 0.5);
    outline-offset: 2px;
}

button:focus:not(:focus-visible) {
    outline: none;
}

button:focus-visible {
    outline: 3px solid rgba(0, 102, 204, 0.5);
    outline-offset: 2px;
}

.form-group {
    position: relative;
    margin-bottom: 25px;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 5px;
    margin: 0;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.7);
    width: 30px;
    height: 30px;
    min-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: color 0.3s ease;
    overflow: visible;
}

.password-toggle:hover {
    color: #fff;
    transform: translateY(-50%);
    box-shadow: none;
    background: none;
}

.password-toggle:active {
    transform: translateY(-50%);
    box-shadow: none;
    background: none;
}

.password-toggle:focus {
    outline: none;
}

.password-toggle .fas {
    font-size: 16px;
    line-height: 1;
}

/* 确保密码输入框右侧有足够空间显示图标 */
#password {
    padding-right: 50px !important;
    box-sizing: border-box;
}

/* 底部版权信息样式 */
.footer {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    width: 100%;
    padding: 12px;
    text-align: center;
    z-index: 1000;
    background: var(--bg-color);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.copyright {
    color: rgba(255, 255, 255, 0.4);
    font-size: 13.5px;
    line-height: 1.6;
    max-width: 900px;
    margin: 0 auto;
    font-weight: 300;
    letter-spacing: 0.3px;
} 