// 使用IIFE创建模块作用域
(function() {
    // 打标记录查询状态管理
    window.markingRecordState = {
        currentPage: 1,
        pageSize: 10,
        pageSizeOptions: [10, 20, 50, 100],
        totalCount: 0,
        totalPages: 0,
        queryType: 'orderNo', // 默认查询类型：工单号
        queryValue: '', // 查询值
        sortField: '', 
        sortOrder: 'asc',
        isInitialSearch: true,
        queryVoidedOnly: false, // 新增：仅查询作废记录的状态
        selectedRecords: new Set(), // 添加用于跟踪选中记录的集合
        allSelected: false // 添加用于跟踪是否全选的标志
    };

    function initMarkingRecordPage() {
        // 加载SweetAlert2库
        if (!document.getElementById('sweetalert2-script')) {
            const script = document.createElement('script');
            script.id = 'sweetalert2-script';
            script.src = '/static/lib/sweetalert2/sweetalert2.min.js';
            document.head.appendChild(script);
            
            if (!document.getElementById('sweetalert2-css')) {
                const link = document.createElement('link');
                link.id = 'sweetalert2-css';
                link.rel = 'stylesheet';
                link.href = '/static/lib/sweetalert2/sweetalert2.min.css';
                document.head.appendChild(link);
            }
        }
        
        const content = document.getElementById('content');
        content.innerHTML = createMarkingRecordHTML();
        
        initEventListeners();
        updatePagination(); // 初始加载时也更新一次分页，确保显示
    }

    function createMarkingRecordHTML() {
        return `
            <section class="marking-record">
                <header>
                    <h1 class="marking-record__title">查询类型</h1>
                </header>
                
                <div class="marking-record__header">
                    <div class="marking-record__type">
                        <div class="marking-record__type-options">
                            <label class="marking-record__type-option">
                                <input type="radio" name="query-type" value="orderNo" class="marking-record__type-radio" checked>
                                <span class="marking-record__type-text">工单号</span>
                            </label>
                            <label class="marking-record__type-option">
                                <input type="radio" name="query-type" value="serialNumber" class="marking-record__type-radio">
                                <span class="marking-record__type-text">SN号</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="marking-record__filter">
                        <button type="button" class="marking-record__filter-btn">
                            <span class="marking-record__filter-text">高级查询</span>
                            <i class="fas fa-chevron-down marking-record__filter-icon"></i>
                        </button>
                    </div>
                </div>
                
                <div class="marking-record__advanced-filter" style="display: none;">
                    <div class="marking-record__date-range">
                        <div class="marking-record__date-field">
                            <label class="marking-record__date-label">开始日期</label>
                            <input type="date" class="marking-record__date-input" id="start-date">
                        </div>
                        <div class="marking-record__date-field">
                            <label class="marking-record__date-label">结束日期</label>
                            <input type="date" class="marking-record__date-input" id="end-date">
                        </div>
                    </div>
                </div>
                
                <form class="marking-record__form">
                    <div class="marking-record__form-group">
                        <input type="text" id="query-input" class="marking-record__input" placeholder="请输入工单号">
                    </div>
                    <div class="marking-record__form-group marking-record__form-group--checkbox">
                        <label class="marking-record__checkbox-label">
                            <input type="checkbox" id="query-voided-checkbox" class="marking-record__checkbox">
                            <span class="marking-record__checkbox-text">仅查询作废记录</span>
                        </label>
                    </div>
                    
                    <div class="marking-record__buttons">
                        <button type="button" id="search-btn" class="marking-record__btn marking-record__btn--search">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <button type="button" id="reset-btn" class="marking-record__btn marking-record__btn--reset">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                        <button type="button" id="export-marking-btn" class="marking-record__btn marking-record__btn--export" style="display: none;">
                            <i class="fas fa-file-export"></i> 导出数据
                        </button>
                    </div>
                </form>
                
                <div class="marking-record__results">
                    <div class="marking-record__table-container">
                        <table class="marking-record__table">
                            <thead>
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="select-all-checkbox-marking" class="marking-record__checkbox">
                                    </th>
                                    <th width="5%">序号</th>
                                    <th width="18%" class="sortable" onclick="window.sortBy('work_order_no')">加工单号 <i class="fas fa-sort"></i></th>
                                    <th width="12%" class="sortable" onclick="window.sortBy('product_code')">产品编码 <i class="fas fa-sort"></i></th>
                                    <th width="19%" class="sortable" onclick="window.sortBy('product_model')">产品型号 <i class="fas fa-sort"></i></th>
                                    <th width="15%" class="sortable" onclick="window.sortBy('serial_number')">SN号 <i class="fas fa-sort"></i></th>
                                    <th width="9%" class="sortable" onclick="window.sortBy('sn_status')">SN号状态 <i class="fas fa-sort"></i></th>
                                    <th width="9%" class="sortable" onclick="window.sortBy('production_quantity')">生产数量 <i class="fas fa-sort"></i></th>
                                    <th width="9%" class="sortable" onclick="window.sortBy('operator')">操作人员 <i class="fas fa-sort"></i></th>
                                    <th width="10%" class="sortable" onclick="window.sortBy('operation_time')">操作时间 <i class="fas fa-sort"></i></th>
                                    <th width="8%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="results-body">
                                <!-- 结果将通过JavaScript动态添加 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="marking-record__pagination">
                        <div class="marking-record__pagination-info">
                            <span>每页</span>
                            <select id="page-size" class="marking-record__page-size">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>条</span>
                            <span class="marking-record__total">共 <span id="total-count">0</span> 条</span>
                        </div>
                        <div class="marking-record__pagination-controls" id="pagination-controls">
                            <!-- 分页控件将通过JavaScript动态添加 -->
                        </div>
                    </div>
                </div>
            </section>
        `;
    }

    function initEventListeners() {
        // 查询类型切换
        const queryTypeRadios = document.querySelectorAll('input[name="query-type"]');
        const queryInput = document.getElementById('query-input');
        
        queryTypeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                const queryType = e.target.value;
                window.markingRecordState.queryType = queryType;
                
                // 根据查询类型更新输入框占位符
                switch(queryType) {
                    case 'serialNumber':
                        queryInput.placeholder = '请输入SN号';
                        break;
                    case 'orderNo':
                        queryInput.placeholder = '请输入工单号';
                        break;
                }
            });
        });
        
        // 高级查询按钮点击事件
        const filterBtn = document.querySelector('.marking-record__filter-btn');
        const advancedFilter = document.querySelector('.marking-record__advanced-filter');
        
        filterBtn.addEventListener('click', () => {
            const isVisible = advancedFilter.style.display !== 'none';
            advancedFilter.style.display = isVisible ? 'none' : 'block';
            filterBtn.classList.toggle('active');
        });
        
        // 日期选择器初始化
        const startDateInput = document.getElementById('start-date');
        const endDateInput = document.getElementById('end-date');
        
        // 清空默认日期值
        startDateInput.value = '';
        endDateInput.value = '';
        
        // 确保结束日期不能早于开始日期
        startDateInput.addEventListener('change', () => {
            if (endDateInput.value && startDateInput.value > endDateInput.value) {
                endDateInput.value = startDateInput.value;
            }
            endDateInput.min = startDateInput.value;
        });
        
        // 确保开始日期不能晚于结束日期
        endDateInput.addEventListener('change', () => {
            if (startDateInput.value && endDateInput.value < startDateInput.value) {
                startDateInput.value = endDateInput.value;
            }
            startDateInput.max = endDateInput.value;
        });
        
        // 新增：仅查询作废记录复选框事件
        const queryVoidedCheckbox = document.getElementById('query-voided-checkbox');
        queryVoidedCheckbox.addEventListener('change', (e) => {
            window.markingRecordState.queryVoidedOnly = e.target.checked;
        });
        
        // 查询按钮点击事件
        const searchBtn = document.getElementById('search-btn');
        searchBtn.addEventListener('click', () => {
            const queryValue = queryInput.value.trim();
            const startDate = startDateInput.value;
            const endDate = endDateInput.value;
            
            if (!queryValue && !startDate && !endDate) {
                Swal.fire({
                    icon: 'warning',
                    title: '请输入至少一个查询条件',
                    text: '可以输入查询内容或选择日期范围',
                    showConfirmButton: false,
                    timer: 2000
                });
                return;
            }
            
            window.markingRecordState.queryValue = queryValue;
            window.markingRecordState.currentPage = 1; 
            window.markingRecordState.isInitialSearch = true; 
            searchMarkingRecords();
        });
        
        // 重置按钮点击事件
        const resetBtn = document.getElementById('reset-btn');
        resetBtn.addEventListener('click', () => {
            queryInput.value = '';
            window.markingRecordState.queryValue = '';
            
            const defaultRadio = document.querySelector('input[name="query-type"][value="orderNo"]');
            if (defaultRadio) {
                defaultRadio.checked = true;
            }
            window.markingRecordState.queryType = 'orderNo';
            queryInput.placeholder = '请输入工单号';
            
            startDateInput.value = '';
            endDateInput.value = '';
            queryVoidedCheckbox.checked = false; // 重置复选框
            window.markingRecordState.queryVoidedOnly = false; // 重置状态
            
            // 清空结果
            document.getElementById('results-body').innerHTML = '';
            document.getElementById('total-count').textContent = '0';
            document.getElementById('pagination-controls').innerHTML = '';
            window.markingRecordState.totalCount = 0;
            window.markingRecordState.totalPages = 0;
            
            // 隐藏导出按钮并重置选中状态
            hideExportMarkingButton();
            window.markingRecordState.selectedRecords.clear();
            window.markingRecordState.allSelected = false;
            const selectAllCheckboxMarking = document.getElementById('select-all-checkbox-marking');
            if(selectAllCheckboxMarking) selectAllCheckboxMarking.checked = false;
            updateExportMarkingButtonText(); 
        });
        
        // 添加全选复选框事件监听
        const selectAllCheckboxMarking = document.getElementById('select-all-checkbox-marking');
        if (selectAllCheckboxMarking) {
            selectAllCheckboxMarking.addEventListener('change', handleSelectAllMarking);
        }
        
        // 导出按钮点击事件
        const exportMarkingBtn = document.getElementById('export-marking-btn');
        if (exportMarkingBtn) {
            exportMarkingBtn.addEventListener('click', exportMarkingData); // 将创建 exportMarkingData 函数
        }
        
        // 每页显示数量变更事件
        const pageSizeSelect = document.getElementById('page-size');
        pageSizeSelect.addEventListener('change', (e) => {
            window.markingRecordState.pageSize = parseInt(e.target.value);
            window.markingRecordState.currentPage = 1; 
            
            if (window.markingRecordState.totalCount > 0) {
                searchMarkingRecords();
            }
        });
        
        // 回车键触发查询
        queryInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchBtn.click();
            }
        });
    }

    // 搜索打标记录
    function searchMarkingRecords() {
        const { queryType, queryValue, currentPage, pageSize, sortField, sortOrder, isInitialSearch, queryVoidedOnly } = window.markingRecordState;
        
        const advancedFilterVisible = document.querySelector('.marking-record__advanced-filter').style.display !== 'none';
        
        const startDate = advancedFilterVisible ? document.getElementById('start-date').value : '';
        const endDate = advancedFilterVisible ? document.getElementById('end-date').value : '';
        
        showLoadingIndicator();
        
        const queryParams = new URLSearchParams({
            type: queryType,
            value: queryValue,
            page: currentPage,
            pageSize: pageSize
        });
        
        if (queryVoidedOnly) {
            queryParams.append('query_voided', 'true');
        }
        
        if (sortField && sortField.trim() !== '') {
            queryParams.append('sort_field', sortField);
            queryParams.append('sort_order', sortOrder);
        }
        
        if (advancedFilterVisible) {
            if (startDate) {
                queryParams.append('startDate', startDate);
            }
            if (endDate) {
                queryParams.append('endDate', endDate);
            }
        }
        
        Logger.log('查询参数:', Object.fromEntries(queryParams));
        
        fetchMarkingRecords(queryParams.toString())
            .then(data => {
                if (data.success) {
                    renderResults(data.records, data.totalCount);
                    
                    if (sortField) {
                        updateSortIcons(sortField);
                    }
                    
                    if (data.totalCount > 0) {
                        showExportMarkingButton();
                    } else {
                        hideExportMarkingButton();
                    }
                    
                    if (isInitialSearch) {
                        showSearchResultNotification(data.totalCount);
                        window.markingRecordState.isInitialSearch = false;
                    }
                } else {
                    showError(data.message || '查询失败');
                    showErrorNotification(data.message || '查询失败，请稍后重试');
                }
            })
            .catch(error => {
                Logger.error('查询出错:', error);
                showError('查询出错，请稍后再试');
                showErrorNotification(error.message || '请稍后再试');
            })
            .finally(() => {
                hideLoadingIndicator();
            });
    }

    function fetchMarkingRecords(queryParams) {
        return new Promise((resolve, reject) => {
            fetch(`/api/marking-records/search?${queryParams}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => resolve(data))
                .catch(error => reject(error));
        });
    }

    function showLoadingIndicator() {
        const tableContainer = document.querySelector('.marking-record__table-container');
        if (tableContainer && !tableContainer.querySelector('.table-loading-indicator')) {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'table-loading-indicator';
            loadingIndicator.innerHTML = '<div class="loading-spinner"></div>';
            tableContainer.appendChild(loadingIndicator);
        }
    }

    function hideLoadingIndicator() {
        const tableContainer = document.querySelector('.marking-record__table-container');
        const loadingIndicator = tableContainer ? tableContainer.querySelector('.table-loading-indicator') : null;
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    function showSearchResultNotification(totalCount) {
        if (totalCount > 0) {
            Swal.fire({
                icon: 'success',
                title: '查询成功',
                text: `共找到 ${totalCount} 条匹配记录`,
                position: 'center',
                showConfirmButton: false,
                timer: 2000
            });
        } else {
            Swal.fire({
                icon: 'info',
                title: '未找到记录',
                text: '没有找到匹配的记录，请尝试其他查询条件',
                position: 'center',
                showConfirmButton: false,
                timer: 2000
            });
        }
    }

    function showErrorNotification(message) {
        Swal.fire({
            icon: 'error',
            title: '查询出错',
            text: message,
            confirmButtonText: '确定'
        });
    }

    function renderResults(records, totalCount) {
        const resultsBody = document.getElementById('results-body');
        const totalCountElement = document.getElementById('total-count');
        
        window.markingRecordState.totalCount = totalCount;
        window.markingRecordState.totalPages = Math.ceil(totalCount / window.markingRecordState.pageSize);
        
        totalCountElement.textContent = totalCount;
        resultsBody.innerHTML = '';
        
        if (records.length === 0) {
            resultsBody.innerHTML = '<tr><td colspan="11" class="marking-record__no-data">未找到匹配的记录</td></tr>'; // Colspan 11
            document.getElementById('pagination-controls').innerHTML = '';
            hideExportMarkingButton(); // 确保无数据时隐藏导出按钮
            return;
        } else {
            if(totalCount > 0) showExportMarkingButton(); else hideExportMarkingButton();
        }
        
        const startIndex = (window.markingRecordState.currentPage - 1) * window.markingRecordState.pageSize + 1;
        
        records.forEach((record, index) => {
            const row = document.createElement('tr');
            const { statusText, statusClass } = getSNStatusInfo(record.sn_status); 
            const recordId = record.id || `${record.work_order_no}_${record.serial_number}`; // 优先用id

            row.innerHTML = `
                <td>
                    <input type="checkbox" class="marking-record__checkbox row-checkbox-marking" data-record-id="${recordId}">
                </td>
                <td>${startIndex + index}</td>
                <td>${record.work_order_no || '-'}</td>
                <td>${record.product_code || '-'}</td>
                <td>${record.product_model || '-'}</td>
                <td>${record.serial_number || '-'}</td>
                <td><span class="marking-record__status ${statusClass}">${statusText}</span></td>
                <td>${record.production_quantity || '-'}</td>
                <td>${record.operator || '-'}</td>
                <td>${formatDateTime(record.operation_time)}</td>
                <td>${createActionButtons(record)}</td>
            `;
            
            resultsBody.appendChild(row);
        });
        
        document.querySelectorAll('.row-checkbox-marking').forEach(checkbox => {
            checkbox.addEventListener('change', handleRowSelectMarking);
        });
        
        updatePagination();
        updateExportMarkingButtonText();
    }

    // 示例: SN号状态处理函数 (需要根据您的实际状态调整)
    function getSNStatusInfo(status) {
        let statusText = '未知';
        let statusClass = '';
        
        // 根据后端返回的数字状态进行判断
        switch(status) {
            case 1: // 1 表示正常
                statusText = '正常';
                statusClass = 'marking-record__status--completed'; // 使用绿色表示正常
                break;
            case 2: // 2 表示作废
                statusText = '作废';
                statusClass = 'marking-record__status--pending'; // 使用灰色/警告色表示作废
                break;
            default:
                statusText = status !== null && status !== undefined ? status.toString() : '未知';
                statusClass = 'marking-record__status--in-progress'; // 其他未知状态的默认样式
                break;
        }
        return { statusText, statusClass };
    }
    
    function formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        const date = new Date(dateTimeStr);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    }

    function showError(message) {
        const resultsBody = document.getElementById('results-body');
        resultsBody.innerHTML = `<tr><td colspan="11" class="marking-record__no-data">${message}</td></tr>`; // Colspan 11
        
        document.getElementById('total-count').textContent = '0';
        document.getElementById('pagination-controls').innerHTML = '';
        window.markingRecordState.totalCount = 0;
        window.markingRecordState.totalPages = 0;
        hideExportMarkingButton(); // 出错时隐藏导出按钮
    }

    function updatePagination() {
        const paginationControls = document.getElementById('pagination-controls');
        let { currentPage, totalPages } = window.markingRecordState;

        // 如果 totalPages 无效或为0，我们仍然希望显示一个禁用的分页结构，默认为1页
        const displayTotalPages = Math.max(totalPages, 0); // 保证 totalPages 不为负
        const displayCurrentPage = currentPage;

        let paginationHTML = '';

        // 首页按钮
        paginationHTML += `
            <button class="marking-record__pagination-btn ${displayCurrentPage === 1 || displayTotalPages === 0 ? 'disabled' : ''}" 
                    title="首页"
                    onclick="window.goToFirstPage()" ${displayCurrentPage === 1 || displayTotalPages === 0 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-left"></i>
            </button>
        `;
        
        // 上一页按钮
        paginationHTML += `
            <button class="marking-record__pagination-btn ${displayCurrentPage === 1 || displayTotalPages === 0 ? 'disabled' : ''}" 
                    title="上一页"
                    onclick="window.goToPrevPage()" ${displayCurrentPage === 1 || displayTotalPages === 0 ? 'disabled' : ''}>
                <i class="fas fa-angle-left"></i>
            </button>
        `;
        
        // 页码按钮
        // 如果没有页数 (displayTotalPages === 0)，则显示一个禁用的页码 1
        // 否则按正常逻辑显示页码
        if (displayTotalPages === 0) {
            paginationHTML += `
                <button class="marking-record__pagination-btn active disabled">1</button>
            `;
        } else {
            let startPage = Math.max(1, displayCurrentPage - 2);
            let endPage = Math.min(displayTotalPages, startPage + 4);
    
            if (endPage - startPage < 4 && displayTotalPages > 4) {
                startPage = Math.max(1, endPage - 4);
            }
            // 如果总页数小于5，确保startPage是1
             if (displayTotalPages <= 4) {
                startPage = 1;
            }
    
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <button class="marking-record__pagination-btn ${i === displayCurrentPage ? 'active' : ''}" 
                            onclick="window.goToPage(${i})">
                        ${i}
                    </button>
                `;
            }
        }
        
        // 下一页按钮
        paginationHTML += `
            <button class="marking-record__pagination-btn ${displayCurrentPage === displayTotalPages || displayTotalPages === 0 ? 'disabled' : ''}" 
                    title="下一页"
                    onclick="window.goToNextPage()" ${displayCurrentPage === displayTotalPages || displayTotalPages === 0 ? 'disabled' : ''}>
                <i class="fas fa-angle-right"></i>
            </button>
        `;
        
        // 末页按钮
        paginationHTML += `
            <button class="marking-record__pagination-btn ${displayCurrentPage === displayTotalPages || displayTotalPages === 0 ? 'disabled' : ''}" 
                    title="末页"
                    onclick="window.goToLastPage()" ${displayCurrentPage === displayTotalPages || displayTotalPages === 0 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-right"></i>
            </button>
        `;
        
        paginationControls.innerHTML = paginationHTML;
    }

    function goToPage(page) {
        window.markingRecordState.currentPage = page;
        window.markingRecordState.isInitialSearch = false;
        searchMarkingRecords();
    }

    function goToFirstPage() {
        window.markingRecordState.currentPage = 1;
        window.markingRecordState.isInitialSearch = false;
        searchMarkingRecords();
    }

    function goToPrevPage() {
        if (window.markingRecordState.currentPage > 1) {
            window.markingRecordState.currentPage--;
            window.markingRecordState.isInitialSearch = false;
            searchMarkingRecords();
        }
    }

    function goToNextPage() {
        if (window.markingRecordState.currentPage < window.markingRecordState.totalPages) {
            window.markingRecordState.currentPage++;
            window.markingRecordState.isInitialSearch = false;
            searchMarkingRecords();
        }
    }

    function goToLastPage() {
        window.markingRecordState.currentPage = window.markingRecordState.totalPages;
        window.markingRecordState.isInitialSearch = false;
        searchMarkingRecords();
    }

    function sortBy(field) {
        if (window.markingRecordState.sortField === field) {
            window.markingRecordState.sortOrder = window.markingRecordState.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            window.markingRecordState.sortField = field;
            window.markingRecordState.sortOrder = 'asc';
        }
        
        window.markingRecordState.currentPage = 1;
        window.markingRecordState.isInitialSearch = false;
        
        updateSortIcons(field);
        searchMarkingRecords();
    }

    function updateSortIcons(activeField) {
        const headers = document.querySelectorAll('.marking-record__table th.sortable');
        headers.forEach(header => {
            const icon = header.querySelector('i');
            if (icon) {
                const fieldAttribute = header.getAttribute('onclick');
                if (fieldAttribute) {
                    const fieldMatch = fieldAttribute.match(/sortBy\('([^']+)'\)/);
                    if (fieldMatch && fieldMatch[1]) {
                        const field = fieldMatch[1];
                        if (field === activeField) {
                            icon.className = window.markingRecordState.sortOrder === 'asc' 
                                ? 'fas fa-sort-up' 
                                : 'fas fa-sort-down';
                            header.classList.add('active');
                        } else {
                            icon.className = 'fas fa-sort';
                            header.classList.remove('active');
                        }
                    }
                }
            }
        });
    }

    window.sortBy = sortBy;
    window.goToPage = goToPage;
    window.goToFirstPage = goToFirstPage;
    window.goToPrevPage = goToPrevPage;
    window.goToNextPage = goToNextPage;
    window.goToLastPage = goToLastPage;

    // 新增：作废打标记录函数
    function voidMarkingRecord(workOrderNo, serialNumber) {
        Swal.fire({
            title: '确认作废?',
            text: `您确定要作废SN号 "${serialNumber}" 吗？作废后，此SN号将可重新使用。`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: '是的，作废它!',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                showLoadingIndicator(); // 显示加载指示
                fetch('/api/marking-records/void', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        work_order_no: workOrderNo, 
                        serial_number: serialNumber 
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    hideLoadingIndicator(); // 隐藏加载指示
                    if (data.success) {
                        Swal.fire(
                            '已作废!',
                            data.message || `SN号 ${serialNumber} 已成功作废。`,
                            'success'
                        );
                        searchMarkingRecords(); // 重新加载数据
                    } else {
                        Swal.fire(
                            '作废失败!',
                            data.message || '作废过程中发生错误。',
                            'error'
                        );
                    }
                })
                .catch(error => {
                    hideLoadingIndicator(); // 隐藏加载指示
                    Logger.error('作废SN记录出错:', error);
                    Swal.fire(
                        '作废出错!',
                        '网络请求失败或服务器无响应。',
                        'error'
                    );
                });
            }
        });
    }
    window.voidMarkingRecord = voidMarkingRecord; // 将函数暴露到全局作用域

    // 新增：生成操作按钮的辅助函数
    function createActionButtons(record) {
        if (record.sn_status === 1) { // 状态为1 (正常) 时才显示作废按钮
            // 使用 `` 来确保引号正确处理，并传递字符串参数
            return `<button class="marking-record__action-btn marking-record__action-btn--void" 
                            onclick="window.voidMarkingRecord('${record.work_order_no}', '${record.serial_number}')"
                            title="作废此SN记录">
                        <i class="fas fa-times-circle"></i> 作废
                    </button>`;
        } else if (record.sn_status === 2) { // 状态为2 (作废)
             return '<span class="marking-record__status-text--voided">已作废</span>';
        }
        return ''; // 其他状态暂无操作
    }

    // 显示导出按钮
    function showExportMarkingButton() {
        const exportBtn = document.getElementById('export-marking-btn');
        if (exportBtn) {
            exportBtn.style.display = 'inline-flex';
        }
    }

    // 隐藏导出按钮
    function hideExportMarkingButton() {
        const exportBtn = document.getElementById('export-marking-btn');
        if (exportBtn) {
            exportBtn.style.display = 'none';
        }
    }

    // 更新导出按钮文本
    function updateExportMarkingButtonText() {
        const exportBtn = document.getElementById('export-marking-btn');
        if (exportBtn) {
            const selectedCount = window.markingRecordState.selectedRecords.size;
            if (selectedCount > 0) {
                exportBtn.innerHTML = `<i class="fas fa-file-export"></i> 导出选中(${selectedCount})`;
            } else {
                exportBtn.innerHTML = `<i class="fas fa-file-export"></i> 导出数据`;
            }
        }
    }

    // 处理全选/取消全选
    function handleSelectAllMarking(e) {
        const isChecked = e.target.checked;
        window.markingRecordState.allSelected = isChecked;
        
        const checkboxes = document.querySelectorAll('.row-checkbox-marking');
        checkboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
            const recordId = checkbox.getAttribute('data-record-id');
            if (isChecked) {
                window.markingRecordState.selectedRecords.add(recordId);
            } else {
                window.markingRecordState.selectedRecords.delete(recordId);
            }
        });
        updateExportMarkingButtonText();
    }

    // 处理单行选择
    function handleRowSelectMarking(e) {
        const checkbox = e.target;
        const recordId = checkbox.getAttribute('data-record-id');
        
        if (checkbox.checked) {
            window.markingRecordState.selectedRecords.add(recordId);
        } else {
            window.markingRecordState.selectedRecords.delete(recordId);
            document.getElementById('select-all-checkbox-marking').checked = false;
            window.markingRecordState.allSelected = false;
        }
        
        const allCheckboxes = document.querySelectorAll('.row-checkbox-marking');
        const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
        
        if (allChecked && allCheckboxes.length > 0) {
            document.getElementById('select-all-checkbox-marking').checked = true;
            window.markingRecordState.allSelected = true;
        }
        updateExportMarkingButtonText();
    }

    // 导出数据功能
    function exportMarkingData() {
        const { totalCount, selectedRecords, queryType, queryValue, sortField, sortOrder, queryVoidedOnly } = window.markingRecordState;
        
        if (totalCount <= 0 && selectedRecords.size === 0) {
            Swal.fire({
                icon: 'warning',
                title: '无数据可导出',
                text: '请先查询数据或选择要导出的记录',
                confirmButtonText: '确定'
            });
            return;
        }
        
        Swal.fire({
            title: '正在准备导出...',
            text: '请稍候',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        const queryParams = new URLSearchParams({
            type: queryType,
            value: queryValue,
            export: 'true' // 标志这是导出请求
        });

        if (queryVoidedOnly) {
            queryParams.append('query_voided', 'true');
        }
        
        if (sortField && sortField.trim() !== '') {
            queryParams.append('sort_field', sortField);
            queryParams.append('sort_order', sortOrder);
        }
        
        // 处理高级查询的日期参数
        const advancedFilterVisible = document.querySelector('.marking-record__advanced-filter').style.display !== 'none';
        if (advancedFilterVisible) {
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            if (startDate) {
                queryParams.append('startDate', startDate);
            }
            if (endDate) {
                queryParams.append('endDate', endDate);
            }
        }
        
        // 如果有选中的记录，添加到查询参数
        if (selectedRecords.size > 0) {
            const selectedIds = Array.from(selectedRecords);
            queryParams.append('selected_records', JSON.stringify(selectedIds));
            // 如果只想导出选中的，可以考虑不传其他过滤参数，或者由后端决定如何处理
            // queryParams.append('export_selected_only', 'true'); // 可选，根据后端设计
        }
        
        fetch(`/api/marking-records/export?${queryParams.toString()}`)
            .then(response => {
                if (!response.ok) {
                    // 尝试解析错误信息
                    return response.json().then(err => {
                        throw new Error(err.message || '导出失败，服务器响应不正确');
                    }).catch(() => {
                        throw new Error(`导出失败，HTTP状态: ${response.status}`);
                    });
                }
                // 检查content-type，确保是excel文件
                const contentType = response.headers.get("content-type");
                if (!contentType || !contentType.includes("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
                     // 如果不是预期的excel类型，尝试解析json错误
                    return response.json().then(errData => {
                        throw new Error(errData.message || "服务器未返回有效的Excel文件");
                    }).catch(() => {
                        throw new Error("服务器响应类型不正确，期望Excel文件");
                    });
                }
                return response.blob();
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                
                const date = new Date();
                const timestamp = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}` +
                                  `${String(date.getHours()).padStart(2, '0')}${String(date.getMinutes()).padStart(2, '0')}`;
                
                const filenameSuffix = selectedRecords.size > 0 ? `_已选${selectedRecords.size}条` : '';
                a.download = `打标记录_${timestamp}${filenameSuffix}.xlsx`;
                
                document.body.appendChild(a);
                a.click();
                
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                Swal.fire({
                    icon: 'success',
                    title: '导出成功',
                    text: selectedRecords.size > 0 ? `已成功导出 ${selectedRecords.size} 条选中数据` : '数据已成功导出',
                    showConfirmButton: false,
                    timer: 2000
                });
            })
            .catch(error => {
                Logger.error('导出错误:', error);
                Swal.fire({
                    icon: 'error',
                    title: '导出失败',
                    text: error.message || '请稍后再试，或联系管理员',
                    confirmButtonText: '确定'
                });
            });
    }

    window.initMarkingRecordPage = initMarkingRecordPage;
})(); 