from flask import Blueprint, jsonify, request, current_app
from INDEX.device import CPUInfoManager
import os
from werkzeug.utils import secure_filename
import requests
from jwt import decode
from functools import wraps
from database.db_manager import DatabaseManager
from datetime import datetime
from sqlalchemy import text
from models.assembly import CompleteProduct
from models.firmware import FirmwareSerialDetail, DownloadRecord

cpu_controller_bp = Blueprint('cpu_controller', __name__)

def get_current_user():
    token = request.cookies.get('token')
    if token:
        try:
            payload = decode(token, current_app.config['JWT_SECRET'], algorithms=["HS256"])
            return payload.get('username')
        except:
            return None
    return None

@cpu_controller_bp.route('/get-current-user', methods=['GET'])
def get_current_user_route():
    username = get_current_user()
    return jsonify({
        'success': True,
        'username': username
    })

@cpu_controller_bp.route('/device-info', methods=['GET'])
def get_device_info():
    try:
        ip = request.args.get('ip')
        if not ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})

        # 将请求转发到本地代理
        proxy_url = f"http://127.0.0.1:5000/proxy/device-info?ip={ip}"
        response = requests.get(proxy_url)
        
        if response.status_code == 200:
            return response.json()
        else:
            return jsonify({'success': False, 'message': '代理服务器请求失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@cpu_controller_bp.route('/load-program', methods=['POST'])
def load_program():
    try:
        data = request.get_json()
        ip = data.get('ip')
        if not ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})

        cpu_manager = CPUInfoManager()
        cpu_manager.ip_address = ip
        cpu_manager.load_program()
        
        return jsonify({'success': True, 'message': '程序加载成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@cpu_controller_bp.route('/reset-device', methods=['POST'])
def reset_device():
    try:
        data = request.get_json()
        ip = data.get('ip')
        if not ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})

        cpu_manager = CPUInfoManager()
        cpu_manager.ip_address = ip
        cpu_manager.reset_device()
        
        return jsonify({'success': True, 'message': '恢复出厂设置成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@cpu_controller_bp.route('/upload-file', methods=['POST'])
def upload_file():
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有文件被上传'})
        
        file = request.files['file']
        ip = request.form.get('ip')
        
        if not ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})
        
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'})
        
        if not file.filename.endswith('.zip'):
            return jsonify({'success': False, 'message': '只支持上传zip文件'})

        cpu_manager = CPUInfoManager()
        cpu_manager.ip_address = ip

        # 保存文件到临时目录
        filename = secure_filename(file.filename)
        temp_path = os.path.join('temp', filename)
        os.makedirs('temp', exist_ok=True)
        file.save(temp_path)

        # 上传文件到设备
        if cpu_manager.upload_file(temp_path):
            # 删除临时文件
            os.remove(temp_path)
            return jsonify({'success': True, 'message': '文件上传成功'})
        else:
            # 删除临时文件
            os.remove(temp_path)
            return jsonify({'success': False, 'message': '文件上传失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@cpu_controller_bp.route('/update-app', methods=['POST'])
def update_app():
    try:
        data = request.get_json()
        ip = data.get('ip')
        filename = data.get('filename')
        
        if not ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})
        
        if not filename:
            return jsonify({'success': False, 'message': '文件名不能为空'})

        cpu_manager = CPUInfoManager()
        cpu_manager.ip_address = ip

        if cpu_manager.update_app(filename):
            return jsonify({'success': True, 'message': '程序更新成功'})
        else:
            return jsonify({'success': False, 'message': '程序更新失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@cpu_controller_bp.route('/submit-test', methods=['POST'])
def submit_test():
    try:
        data = request.get_json()
        print("Received data:", data)
        db = DatabaseManager()
        
        pro_status = data.get('pro_status')
        if pro_status is None:
            return jsonify({
                'success': False,
                'message': '产品状态不能为空'
            }), 400

        with db.get_session() as session:
            # 首先检查 SN 号是否存在
            check_sql = text("SELECT pro_status, maintenance, rework FROM cpu_table WHERE pro_sn = :pro_sn")
            result = session.execute(check_sql, {'pro_sn': data['pro_sn']}).fetchone()

            if result:
                # SN号已存在
                if pro_status == 1:  # 新品
                    return jsonify({
                        'success': False,
                        'message': f'产品SN号 {data["pro_sn"]} 已存在，新品不能使用重复的SN号'
                    }), 400
                
                # 获取现有的维修和返工次数
                existing_maintenance = result[1]
                existing_rework = result[2]
                
                # 更新维修或返工次数
                if pro_status == 2:  # 维修
                    maintenance = existing_maintenance + 1
                    rework = existing_rework
                else:  # 返工
                    maintenance = existing_maintenance
                    rework = existing_rework + 1

                # 更新现有记录，当是维修或返工时清除比对相关字段
                update_sql = text("""
                    UPDATE cpu_table 
                    SET tester = :tester,
                        work_order = :work_order,
                        work_qty = :work_qty,
                        pro_model = :pro_model,
                        pro_status = :pro_status,
                        pro_code = :pro_code,
                        pro_batch = :pro_batch,
                        remarks = :remarks,
                        maintenance = :maintenance,
                        rework = :rework,
                        device_name = :device_name,
                        serial = :serial,
                        sw_version = :sw_version,
                        build_date = :build_date,
                        back_ver = :back_ver,
                        high_speed_io_version = :high_speed_io_version,
                        backplane = :backplane,
                        body_io = :body_io,
                        led_tube = :led_tube,
                        led_bulb = :led_bulb,
                        net_port = :net_port,
                        rs485_1 = :rs485_1,
                        rs485_2 = :rs485_2,
                        rs232 = :rs232,
                        canbus = :canbus,
                        ethercat = :ethercat,
                        usb_drive = :usb_drive,
                        sd_slot = :sd_slot,
                        debug_port = :debug_port,
                        dip_switch = :dip_switch,
                        reset_btn = :reset_btn,
                        test_status = :test_status,
                        test_time = CURRENT_TIMESTAMP,
                        comparison_time = CASE 
                            WHEN :pro_status IN (2, 3) THEN NULL 
                            ELSE comparison_time 
                        END,
                        comparison_result = CASE 
                            WHEN :pro_status IN (2, 3) THEN NULL 
                            ELSE comparison_result 
                        END,
                        comparison_user = CASE 
                            WHEN :pro_status IN (2, 3) THEN NULL 
                            ELSE comparison_user 
                        END
                    WHERE pro_sn = :pro_sn
                """)
            else:
                # SN号不存在（首次写入）
                if pro_status == 1:  # 新品
                    maintenance = 0
                    rework = 0
                elif pro_status == 2:  # 维修
                    maintenance = 1  # 首次维修设为1
                    rework = 0
                else:  # 返工
                    maintenance = 0
                    rework = 1  # 首次返工设为1
                    
                update_sql = text("""
                    INSERT INTO cpu_table (
                        tester, work_order, work_qty, pro_model, pro_status,
                        pro_code,
                        pro_sn, pro_batch, remarks, rework, maintenance,
                        device_name, serial, sw_version, build_date, back_ver,
                        high_speed_io_version,
                        backplane, body_io, led_tube, led_bulb, net_port,
                        rs485_1, rs485_2, rs232, canbus, ethercat,
                        usb_drive, sd_slot, debug_port, dip_switch, reset_btn,
                        test_status
                    ) VALUES (
                        :tester, :work_order, :work_qty, :pro_model, :pro_status,
                        :pro_code,
                        :pro_sn, :pro_batch, :remarks, :rework, :maintenance,
                        :device_name, :serial, :sw_version, :build_date, :back_ver,
                        :high_speed_io_version,
                        :backplane, :body_io, :led_tube, :led_bulb, :net_port,
                        :rs485_1, :rs485_2, :rs232, :canbus, :ethercat,
                        :usb_drive, :sd_slot, :debug_port, :dip_switch, :reset_btn,
                        :test_status
                    )
                """)

            # 处理测试结果
            test_results = {
                '通过': 1,
                '不通过': 2
            }

            # 检查每个测试项目的结果
            test_items = {
                'backplane': data['backplane_result'],
                'body_io': data['body_io_result'],
                'led_tube': data['led_tube_result'],
                'led_bulb': data['led_bulb_result'],
                'net_port': data['net_port_result'],
                'rs485_1': data['rs485_1_result'],
                'rs485_2': data['rs485_2_result'],
                'rs232': data['rs232_result'],
                'canbus': data['canbus_result'],
                'ethercat': data['ethercat_result'],
                'usb_drive': data['usb_drive_result'],
                'sd_slot': data['sd_slot_result'],
                'debug_port': data['debug_port_result'],
                'dip_switch': data['dip_switch_result'],
                'reset_btn': data['reset_btn_result']
            }

            # 收集未通过的测试项目
            failed_tests = {k: v for k, v in test_items.items() if v == '不通过'}
            
            # 准备 cpu_table 的参数
            params = {
                'tester': data['tester'],
                'work_order': data['work_order'],
                'work_qty': int(data['work_qty']),
                'pro_model': data['pro_model'],
                'pro_code': data['pro_code'],
                'pro_status': pro_status,
                'pro_sn': data['pro_sn'],
                'pro_batch': data.get('pro_batch', 'N/A'),
                'remarks': data.get('remarks', 'N/A'),
                'rework': rework,
                'maintenance': maintenance,
                'device_name': data.get('device_name', 'N/A'),
                'serial': data.get('serial', 'N/A'),
                'sw_version': data.get('sw_version', 'N/A'),
                'build_date': data.get('build_date', 'N/A'),
                'back_ver': data.get('back_ver', 'N/A'),
                'high_speed_io_version': data.get('high_speed_io_version', 'N/A'),
                'product_type': 'cpu_module',
                **{k: test_results[v] for k, v in test_items.items()},
                'test_status': 'pass' if not failed_tests else 'ng'
            }

            # 执行 cpu_table 的更新或插入
            session.execute(update_sql, params)

            # 如果有未通过的测试项目，写入故障表
            if failed_tests:
                # 准备故障表的错误字段参数
                error_params = {
                    'burn_err': 0,
                    'power_err': 0,
                    'backplane_err': 2 if 'backplane' in failed_tests else 0,
                    'body_io_err': 2 if 'body_io' in failed_tests else 0,
                    'led_tube_err': 2 if 'led_tube' in failed_tests else 0,
                    'led_bulb_err': 2 if 'led_bulb' in failed_tests else 0,
                    'net_port_err': 2 if 'net_port' in failed_tests else 0,
                    'rs485_1_err': 2 if 'rs485_1' in failed_tests else 0,
                    'rs485_2_err': 2 if 'rs485_2' in failed_tests else 0,
                    'rs232_err': 2 if 'rs232' in failed_tests else 0,
                    'canbus_err': 2 if 'canbus' in failed_tests else 0,
                    'ethercat_err': 2 if 'ethercat' in failed_tests else 0,
                    'usb_drive_err': 2 if 'usb_drive' in failed_tests else 0,
                    'sd_slot_err': 2 if 'sd_slot' in failed_tests else 0,
                    'debug_port_err': 2 if 'debug_port' in failed_tests else 0,
                    'dip_switch_err': 2 if 'dip_switch' in failed_tests else 0,
                    'reset_btn_err': 2 if 'reset_btn' in failed_tests else 0,
                    'other_err': 'N/A'
                }

                # 准备故障表的基本参数
                fault_params = {
                    'tester': data['tester'],
                    'work_order': data['work_order'],
                    'work_qty': int(data['work_qty']),
                    'pro_model': data['pro_model'],
                    'pro_status': pro_status,
                    'pro_sn': data['pro_sn'],
                    'pro_batch': data.get('pro_batch', 'N/A'),
                    'remarks': data.get('remarks', 'N/A'),
                    'rework': rework,
                    'pro_code': data['pro_code'],
                    'maintenance': maintenance,
                    **error_params
                }

                # 插入故障记录
                fault_sql = text("""
                    INSERT INTO faultEntry_table (
                        tester, work_order, work_qty, pro_model, pro_status,
                        pro_code,
                        pro_sn, pro_batch, remarks, rework, maintenance,
                        burn_err, power_err, backplane_err, body_io_err,
                        led_tube_err, led_bulb_err, net_port_err, rs485_1_err,
                        rs485_2_err, rs232_err, canbus_err, ethercat_err,
                        usb_drive_err, sd_slot_err, debug_port_err, dip_switch_err,
                        reset_btn_err, other_err
                    ) VALUES (
                        :tester, :work_order, :work_qty, :pro_model, :pro_status,
                        :pro_code,
                        :pro_sn, :pro_batch, :remarks, :rework, :maintenance,
                        :burn_err, :power_err, :backplane_err, :body_io_err,
                        :led_tube_err, :led_bulb_err, :net_port_err, :rs485_1_err,
                        :rs485_2_err, :rs232_err, :canbus_err, :ethercat_err,
                        :usb_drive_err, :sd_slot_err, :debug_port_err, :dip_switch_err,
                        :reset_btn_err, :other_err
                    )
                """)
                
                print("Fault Parameters for SQL:", fault_params)
                session.execute(fault_sql, fault_params)

            session.commit()
            
            return jsonify({
                'success': True,
                'message': '测试信息提交成功' + ('，并已记录故障信息' if failed_tests else '')
            })
        
    except Exception as e:
        print(f"Error details: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交失败：{str(e)}'
        }), 500

@cpu_controller_bp.route('/check-sn', methods=['GET'])
def check_sn():
    """
    检查SN号是否已经绑定PCBA
    
    路由说明：
    - 方法：GET
    - URL：/api/cpu-controller/check-sn
    - 参数：sn (查询参数)
    
    功能描述：
    1. 接收SN号作为参数
    2. 在complete_products表中查询该SN号
    3. 返回查询结果，表明该SN号是否已绑定PCBA
    """
    try:
        # 获取查询参数中的SN号
        sn = request.args.get('sn')
        if not sn:
            return jsonify({
                'success': False,
                'message': '请提供SN号'
            }), 400
            
        # 创建数据库连接
        db = DatabaseManager()
        with db.get_session() as session:
            # 在complete_products表中查询SN号
            result = session.query(CompleteProduct).filter(
                CompleteProduct.product_sn == sn
            ).first()
            
            # 根据查询结果返回不同的响应
            if result:
                return jsonify({
                    'success': True,
                    'exists': True,
                    'message': 'SN号已绑定PCBA'
                })
            else:
                return jsonify({
                    'success': True,
                    'exists': False,
                    'message': 'SN号未绑定PCBA'
                })
                
    except Exception as e:
        # 异常处理
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500

@cpu_controller_bp.route('/get-version-info', methods=['GET'])
def get_version_info():
    """
    根据加工单号获取软件版本相关信息
    
    查询流程：
    1. 使用work_order直接查询download_record表获取版本信息
    
    返回字段：software_version, build_time, backplane_version, io_version
    """
    try:
        # 获取查询参数 - 支持两种参数名以保持兼容性
        work_order = request.args.get('work_order') or request.args.get('product_sn')
        
        if not work_order:
            return jsonify({
                'success': False,
                'message': '请提供加工单号'
            }), 400
            
        # 创建数据库连接
        db = DatabaseManager()
        with db.get_session() as session:
            # 直接查询download_record表
            # 使用work_order查询版本信息
            result = session.query(
                DownloadRecord.software_version,
                DownloadRecord.build_time,
                DownloadRecord.backplane_version,
                DownloadRecord.io_version
            ).filter(
                DownloadRecord.work_order == work_order
            ).order_by(
                DownloadRecord.create_time.desc()  # 获取最新的记录
            ).first()
            
            if result:
                return jsonify({
                    'success': True,
                    'data': {
                        'software_version': result.software_version,
                        'build_time': result.build_time,
                        'backplane_version': result.backplane_version,
                        'io_version': result.io_version
                    },
                    'message': '版本信息获取成功'
                })
            else:
                return jsonify({
                    'success': True,
                    'data': {
                        'software_version': '',
                        'build_time': '',
                        'backplane_version': '',
                        'io_version': ''
                    },
                    'message': '未找到相关版本信息'
                })
                
    except Exception as e:
        # 异常处理
        return jsonify({
            'success': False,
            'message': f'查询版本信息失败：{str(e)}'
        }), 500
