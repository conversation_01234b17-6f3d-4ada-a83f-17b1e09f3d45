/* 待审核固件页面样式 - 采用BEM命名规范 */

.pending-firmware {
    min-height: 100vh;
    background-color: #f5f5f5;
}

/* ===== 页面头部 ===== */
.pending-firmware__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.pending-firmware__header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.pending-firmware__header:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.pending-firmware__title {
    margin: 0;
    color: #303133;
    font-size: 20px;
    font-weight: 600;
    position: relative;
    padding-left: 16px;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.pending-firmware__title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 22px;
    background: linear-gradient(to bottom, #E6A23C, #F7DFC1);
    border-radius: 3px;
}

.pending-firmware__stats {
    display: flex;
    gap: 15px;
    align-items: center;
}

.pending-firmware__stats .el-tag {
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.pending-firmware__stats .el-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
}

/* ===== 搜索栏 ===== */
.pending-firmware__search-bar {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
}

.pending-firmware__search-input {
    flex: 1;
    min-width: 400px;
}

.pending-firmware__actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* ===== 表格容器 ===== */
.pending-firmware__table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* ===== 表格内容样式 ===== */
.pending-firmware__no-old-serial {
    color: #909399;
    font-style: italic;
}

.pending-firmware__serial-link {
    font-weight: 600;
    text-decoration: none;
    color: #409eff;
}

.pending-firmware__serial-link:hover {
    text-decoration: underline;
}

.pending-firmware__products {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.pending-firmware__product-tag {
    margin: 2px;
    transition: all 0.2s ease;
    white-space: normal;
    max-width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    height: auto;
    line-height: 1.5;
    padding: 5px 10px;
}

/* ===== 详情对话框样式 ===== */
.pending-firmware__detail-dialog .el-dialog {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 700px;
    width: 70% !important;
    max-width: 1200px !important;
}

.pending-firmware__detail-dialog .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f8f9fa;
}

.pending-firmware__detail-dialog .el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

.pending-firmware__detail-dialog .el-dialog__body {
    padding: 0;
}

.pending-firmware__detail-content {
    padding: 0;
}

/* 详情头部 */
.pending-firmware__detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(to right, #fbfcfd, #f6f8fb);
    border-bottom: 1px solid #ebeef5;
}

.pending-firmware__status-tag {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 16px;
    box-shadow: 0 2px 6px rgba(230, 162, 60, 0.2);
}

.pending-firmware__source {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #606266;
}

/* 详情内容区域 */
.pending-firmware__detail-body {
    display: flex;
    min-height: 400px;
    flex-wrap: wrap;
}

.pending-firmware__detail-left {
    flex: 1;
    padding: 20px;
    border-right: 1px solid #ebeef5;
    min-width: 340px;
}

.pending-firmware__detail-right {
    flex: 1.5;
    padding: 20px;
    background-color: #fafafa;
    min-width: 350px;
}

.pending-firmware__detail-section {
    margin-bottom: 20px;
}

.pending-firmware__detail-section:last-child {
    margin-bottom: 0;
}

.pending-firmware__section-header {
    font-size: 15px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
    position: relative;
}

.pending-firmware__section-header::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 30px;
    height: 2px;
    background-color: #E6A23C;
}

.pending-firmware__section-content {
    color: #606266;
    word-wrap: break-word;
    word-break: break-all;
}

/* 信息项 */
.pending-firmware__info-item {
    margin-bottom: 10px;
    line-height: 1.6;
}

.pending-firmware__info-label {
    font-weight: 500;
    color: #606266;
    display: inline-block;
    min-width: 105px;
}

.pending-firmware__info-value {
    color: #303133;
    word-wrap: break-word;
    word-break: break-all;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.pending-firmware__info-highlight {
    color: #E6A23C;
    font-weight: 600;
}

/* 产品列表 */
.pending-firmware__product-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.pending-firmware__product-tag:hover {
    transform: translateY(-2px);
}

.pending-firmware__empty-text {
    color: #909399;
    font-style: italic;
}

/* 版本要求和描述 */
.pending-firmware__requirements,
.pending-firmware__description {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
    line-height: 1.6;
    margin: 0;
    max-height: 180px;
    overflow-y: auto;
}

/* 底部按钮区域 */
.pending-firmware__detail-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 12px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #f8f9fa;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
    .pending-firmware__search-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .pending-firmware__search-input {
        max-width: 100%;
        margin-bottom: 10px;
    }
    
    .pending-firmware__actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .pending-firmware__header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .pending-firmware__stats {
        justify-content: center;
    }
    
    .pending-firmware__detail-body {
        flex-direction: column;
    }
    
    .pending-firmware__detail-left {
        border-right: none;
        border-bottom: 1px solid #ebeef5;
    }
    
    .pending-firmware__actions {
        justify-content: flex-end;
        width: 100%;
    }
    
    .pending-firmware__search-input {
        min-width: auto;
        width: 100%;
    }
    
    .pending-firmware__search-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .pending-firmware__info-grid {
        grid-template-columns: 1fr;
    }
    
    .firmware-modal__row {
        flex-direction: column;
    }
}

/* ===== 加载状态和动画 ===== */
.pending-firmware .el-loading-mask {
    border-radius: 8px;
}

.pending-firmware__serial-link {
    transition: all 0.3s ease;
}

.pending-firmware__product-tag {
    transition: all 0.2s ease;
}

.pending-firmware__product-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== 状态特定样式 ===== */
.pending-firmware .el-tag--warning {
    background-color: #fdf6ec;
    border-color: #faecd8;
    color: #e6a23c;
}

.pending-firmware .el-button--success {
    background-color: #67c23a;
    border-color: #67c23a;
}

.pending-firmware .el-button--danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
}

/* ===== 表格增强样式 ===== */
.pending-firmware .el-table {
    border-radius: 0 0 8px 8px;
}

.pending-firmware .el-table th {
    background-color: #fafafa;
    font-weight: 600;
    color: #303133;
}

.pending-firmware .el-table--border td,
.pending-firmware .el-table--border th {
    border-right: 1px solid #ebeef5;
}

.pending-firmware .el-table tbody tr:hover td {
    background-color: #f5f7fa;
}

/* ===== 按钮组优化 ===== */
.pending-firmware .el-button-group .el-button {
    margin-left: 0;
}

.pending-firmware .el-button-group .el-button:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.pending-firmware .el-button-group .el-button:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.pending-firmware .el-button-group .el-button:not(:first-child):not(:last-child) {
    border-radius: 0;
}

/* 待审核状态样式 */
.pending-firmware__status--pending { 
    color: #E6A23C; 
    font-weight: bold;
}

.pending-firmware__type-tag {
    margin-right: 8px;
}

/* ===== 新的现代化模态框样式 ===== */

/* 状态和标签区域 */
.pending-firmware__status-section {
    background: linear-gradient(to right, #fbfcfd, #f6f8fb);
    border-radius: 8px;
    margin-bottom: 20px;
    border: none !important;
    box-shadow: none !important;
}

.pending-firmware__status-badge {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.pending-firmware__status-tag {
    padding: 8px 16px;
    font-size: 14px !important;
    border-radius: 20px !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
    transition: all 0.3s ease !important;
}

.pending-firmware__status-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(230, 162, 60, 0.4);
}

.pending-firmware__source-tag {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #606266;
    font-size: 14px;
}

/* 信息网格布局 */
.pending-firmware__info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
}

.pending-firmware__info-item {
    padding: 8px 0;
    overflow: hidden;
}

.pending-firmware__info-label {
    color: #909399;
    font-size: 13px;
    margin-bottom: 4px;
}

.pending-firmware__info-value {
    color: #303133;
    font-size: 14px;
    font-weight: 500;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
}

.pending-firmware__info-value.highlight {
    color: #E6A23C;
    font-weight: 600;
}

/* 产品容器 */
.pending-firmware__products-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px 0;
}

.pending-firmware__product-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.pending-firmware__no-data {
    color: #909399;
    font-style: italic;
    padding: 8px 0;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 代码块和描述块 */
.pending-firmware__code-block, 
.pending-firmware__description-block {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #ebeef5;
    font-size: 14px;
    line-height: 1.6;
    color: #303133;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.pending-firmware__code-block:hover, 
.pending-firmware__description-block:hover {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    border-color: #dcdfe6;
}

/* 按钮样式 */
.firmware-modal__button-reject {
    background: linear-gradient(135deg, #f56c6c, #e74c3c) !important;
    border-color: #f56c6c !important;
    box-shadow: 0 2px 6px rgba(245, 108, 108, 0.4) !important;
    transition: all 0.25s ease !important;
}

.firmware-modal__button-reject:hover {
    background: linear-gradient(135deg, #e74c3c, #d73c2c) !important;
    border-color: #e74c3c !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(245, 108, 108, 0.5) !important;
}

/* 滚动条样式 */
.pending-firmware__code-block::-webkit-scrollbar,
.pending-firmware__description-block::-webkit-scrollbar {
    width: 6px;
}

.pending-firmware__code-block::-webkit-scrollbar-track,
.pending-firmware__description-block::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.pending-firmware__code-block::-webkit-scrollbar-thumb,
.pending-firmware__description-block::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.pending-firmware__code-block::-webkit-scrollbar-thumb:hover,
.pending-firmware__description-block::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 弹出动画 */
.pending-firmware__detail-dialog .el-dialog {
    transform: translateY(-20px);
    opacity: 0;
    animation: pending-dialog-fade-in 0.3s ease forwards;
}

@keyframes pending-dialog-fade-in {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 详情内容区域的产品标签 */
.pending-firmware__detail-section .pending-firmware__product-tag {
    margin: 5px;
    display: inline-block;
    white-space: normal;
    height: auto;
    line-height: 1.5;
    max-width: 100%;
}

/* ===== 操作按钮样式 ===== */
.pending-firmware__action-button {
    margin-right: 8px;
    transition: all 0.3s ease;
}

.pending-firmware__action-button:last-child {
    margin-right: 0;
}

.pending-firmware__action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
} 