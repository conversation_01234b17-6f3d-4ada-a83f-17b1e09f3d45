from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text, Float, UniqueConstraint, Enum
from sqlalchemy.orm import relationship
from database.db_manager import Base
import datetime

class ProductType(Base):
    __tablename__ = 'product_types'

    id = Column(Integer, primary_key=True, autoincrement=True)
    type_code = Column(String(20), nullable=False, unique=True)
    type_name = Column(String(50), nullable=False)
    description = Column(String(200), nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.now)

    # 关系
    work_orders = relationship("QualityInspectionWorkOrder", back_populates="product_type")
    inspection_items = relationship("InspectionItem", back_populates="product_type")

class QualityInspectionWorkOrder(Base):
    __tablename__ = 'quality_inspection_work_orders'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_order_no = Column(String(50), nullable=False, unique=True)
    product_type_id = Column(Integer, ForeignKey('product_types.id'), nullable=False)
    status = Column(String(20), default='pending')
    is_rework = Column(Boolean, default=False, comment='是否为返工工单')
    assembly_stage_completed = Column(Boolean, default=False, comment='组装前阶段是否完成')
    test_stage_completed = Column(Boolean, default=False, comment='测试前阶段是否完成')
    packaging_stage_completed = Column(Boolean, default=False, comment='包装前阶段是否完成')
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    # 关系
    product_type = relationship("ProductType", back_populates="work_orders")
    inspection_attachments = relationship("InspectionAttachment", back_populates="work_order")
    products = relationship("Product", back_populates="work_order")
    inspection_statuses = relationship("ProductInspectionStatus", back_populates="work_order")

class Product(Base):
    __tablename__ = 'products'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_order_id = Column(Integer, ForeignKey('quality_inspection_work_orders.id'), nullable=False)
    serial_no = Column(String(50), nullable=False)  # 移除unique=True
    is_rework = Column(Boolean, default=False, comment='是否为返工产品')
    original_product_id = Column(Integer, nullable=True, comment='原始产品ID（如果是返工产品）')
    original_work_order_id = Column(Integer, nullable=True, comment='原始工单ID（如果是返工产品）')
    created_at = Column(DateTime, default=datetime.datetime.now)

    # 关系
    work_order = relationship("QualityInspectionWorkOrder", back_populates="products")
    inspection_statuses = relationship("ProductInspectionStatus", back_populates="product")

    # 联合唯一约束: 确保同一工单内SN号唯一
    __table_args__ = (
        UniqueConstraint('work_order_id', 'serial_no', name='uk_work_order_serial_no'),
    )

    def __repr__(self):
        return f"<Product(id={self.id}, serial_no='{self.serial_no}', is_rework={self.is_rework})>"

class InspectionItem(Base):
    __tablename__ = 'inspection_items'

    id = Column(Integer, primary_key=True, autoincrement=True)
    product_type_id = Column(Integer, ForeignKey('product_types.id'), nullable=False)
    stage = Column(String(20), nullable=False)  # assembly, test, packaging
    item_no = Column(Integer, nullable=False)
    item_name = Column(String(200), nullable=False)
    display_order = Column(Integer, default=0)
    is_required = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.datetime.now)

    # 关系
    product_type = relationship("ProductType", back_populates="inspection_items")

    # 联合唯一约束
    __table_args__ = (
        UniqueConstraint('product_type_id', 'stage', 'item_no', name='uk_product_stage_no'),
    )

class InspectionAttachment(Base):
    __tablename__ = 'inspection_attachments'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_order_id = Column(Integer, ForeignKey('quality_inspection_work_orders.id'), nullable=False)
    stage = Column(String(20), nullable=False)  # assembly, test, packaging
    inspector_role = Column(String(20), nullable=False)  # first, self, ipqc
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(255), nullable=False)
    file_type = Column(String(50), nullable=False)
    file_size = Column(Integer, nullable=False)
    upload_time = Column(DateTime, default=datetime.datetime.now)
    uploaded_by = Column(String(50), nullable=False)

    # 关系
    work_order = relationship("QualityInspectionWorkOrder", back_populates="inspection_attachments")

class ProductInspectionStatus(Base):
    __tablename__ = 'product_inspection_status'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_order_id = Column(Integer, ForeignKey('quality_inspection_work_orders.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    stage = Column(String(20), nullable=False, comment='检验阶段：assembly-组装前/test-测试前/packaging-包装前')
    inspector_role = Column(String(20), nullable=False, comment='检验人员角色：first-首检/self-自检/ipqc-IPQC')
    is_passed = Column(Boolean, nullable=False, default=True, comment='是否通过')
    inspector = Column(String(50), nullable=False, comment='检验人')
    inspection_time = Column(DateTime, default=datetime.datetime.now, comment='检验时间')
    submission_type = Column(Enum('partial', 'final'), nullable=False, default='final', 
                          comment='提交类型：partial-部分提交，final-最终提交')

    # 关系
    work_order = relationship("QualityInspectionWorkOrder", back_populates="inspection_statuses")
    product = relationship("Product", back_populates="inspection_statuses")

    # 联合唯一约束: 确保每个产品在每个阶段每个角色只有一条记录
    __table_args__ = (
        UniqueConstraint('product_id', 'stage', 'inspector_role', name='uk_product_inspection'),
    )

    def __repr__(self):
        return f"<ProductInspectionStatus(product_id={self.product_id}, stage='{self.stage}', role='{self.inspector_role}', passed={self.is_passed})>"