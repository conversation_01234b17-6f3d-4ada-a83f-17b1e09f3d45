/* ===== 现代化版本比对页面样式 ===== */

/* CSS自定义属性 - 响应式设计系统 */
:root {
  /* 主色调 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  /* 成功色 */
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-500: #10b981;
  --success-600: #059669;
  
  /* 错误色 */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  
  /* 警告色 */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  /* 中性色 - 增强对比度 */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* 背景渐变 */
  --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --card-gradient: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  
  /* 边框半径 - 使用rem单位 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* 响应式间距系统 - 更紧凑 */
  --space-2xs: 0.125rem; /* 2px */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 0.75rem;   /* 12px */
  --space-lg: 1rem;      /* 16px */
  --space-xl: 1.25rem;   /* 20px */
  --space-2xl: 1.5rem;   /* 24px */
  
  /* 响应式字体大小 - 使用clamp() */
  --font-size-xs: clamp(0.625rem, 0.5rem + 0.625vw, 0.75rem);    /* 10-12px */
  --font-size-sm: clamp(0.75rem, 0.625rem + 0.625vw, 0.875rem);  /* 12-14px */
  --font-size-base: clamp(0.875rem, 0.75rem + 0.625vw, 1rem);    /* 14-16px */
  --font-size-lg: clamp(1rem, 0.875rem + 0.625vw, 1.125rem);     /* 16-18px */
  --font-size-xl: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);      /* 18-20px */
  --font-size-2xl: clamp(1.25rem, 1.125rem + 0.625vw, 1.375rem); /* 20-22px */
  
  /* 动画 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 边框颜色 - 确保清晰可见 */
  --border-light: #e2e8f0;
  --border-medium: #cbd5e1;
  --border-strong: #94a3b8;
  --border-accent: #3b82f6;
}

/* 主容器 - 现代化背景，占满屏幕宽度 */
.version-comparison {
  min-height: 100vh;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: var(--space-lg);
  position: relative;
  width: 100%;
  box-sizing: border-box;
  font-size: var(--font-size-base);
  font-family: -apple-system, BlinkMacSystemFont, 'Inter', sans-serif;
}

.version-comparison::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 主要内容区域 - 现代化网格布局，占满宽度 */
.version-comparison__main {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(22rem, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
  width: 100%;
  box-sizing: border-box;
}

/* 现代化卡片样式 - 增强边框对比度，统一字体 */
.version-comparison__card {
  background: var(--card-gradient);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-xl);
  border: 2px solid var(--border-light);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  font-size: var(--font-size-base);
}

.version-comparison__card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.25rem;
  background: var(--bg-gradient);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.version-comparison__card:hover {
  transform: translateY(-0.25rem);
  box-shadow: var(--shadow-2xl);
  border-color: var(--border-medium);
}

/* 卡片头部 - 现代化设计，统一字体 */
.version-comparison__card-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 2px solid var(--border-light);
  position: relative;
}

.version-comparison__card-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 3.75rem;
  height: 2px;
  background: var(--bg-gradient);
  border-radius: var(--radius-sm);
}

.version-comparison__card-header .el-icon {
  font-size: var(--font-size-xl);
  color: var(--primary-600);
  margin-right: var(--space-md);
  padding: var(--space-sm);
  background: var(--primary-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-100);
  transition: all var(--transition-fast);
}

.version-comparison__card:hover .version-comparison__card-header .el-icon {
  background: var(--primary-100);
  border-color: var(--primary-200);
  transform: scale(1.05);
}

.version-comparison__card-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-800);
  margin: 0;
  line-height: 1.2;
}

.version-comparison__card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-top: var(--space-xs);
  font-weight: 500;
}

/* 信息展示区域 - 增强边框可见性，统一字体，更紧凑间距 */
.version-comparison__baseline-info,
.version-comparison__target-info {
  background: linear-gradient(145deg, #ffffff, var(--gray-50));
  border-radius: var(--radius-xl);
  padding: var(--space-md);
  margin-bottom: var(--space-md);
  border: 2px solid var(--border-light);
  transition: all var(--transition-normal);
  position: relative;
  font-size: var(--font-size-base);
}

.version-comparison__baseline-info:hover,
.version-comparison__target-info:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-md);
}

.version-comparison__baseline-info--empty,
.version-comparison__target-info--empty {
  text-align: center;
  color: var(--gray-500);
  font-style: italic;
  padding: var(--space-xl);
  background: linear-gradient(145deg, var(--gray-50), var(--gray-100));
  border-style: dashed;
  border-color: var(--border-medium);
  font-size: var(--font-size-base);
}

/* 信息项 - 现代化布局，统一字体，更紧凑间距 */
.version-comparison__info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-fast);
}

.version-comparison__info-item:hover {
  background: rgba(255, 255, 255, 1);
  border-color: var(--border-medium);
  transform: translateX(0.25rem);
}

.version-comparison__info-item:last-child {
  margin-bottom: 0;
}

.version-comparison__info-label {
  color: var(--gray-600);
  font-weight: 600;
  font-size: var(--font-size-base);
}

.version-comparison__info-value {
  color: var(--gray-800);
  font-weight: 700;
  font-family: 'Courier New', monospace;
  padding: var(--space-2xs) var(--space-xs);
  background: var(--primary-50);
  border-radius: var(--radius-sm);
  border: 1px solid var(--primary-200);
  font-size: var(--font-size-base);
}

/* 输入区域 - 现代化样式，更紧凑间距 */
.version-comparison__baseline-input,
.version-comparison__target-input {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.version-comparison__baseline-input .el-input,
.version-comparison__target-input .el-input {
  flex: 1;
}

/* 基准值设置区域 - 更紧凑间距 */
.version-comparison__baseline-values {
  border-top: 2px solid var(--border-light);
  padding-top: var(--space-md);
  margin-top: var(--space-md);
  position: relative;
}

.version-comparison__baseline-values::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 3.75rem;
  height: 2px;
  background: var(--bg-gradient);
}

.version-comparison__baseline-section-title {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--gray-700);
  margin-bottom: var(--space-md);
  text-align: center;
  position: relative;
}

.version-comparison__form-item {
  margin-bottom: var(--space-sm);
}

.version-comparison__form-label {
  display: block;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-xs);
}

/* 比对结果区域 - 现代化设计，统一字体，更紧凑间距 */
.version-comparison__comparison-result {
  border-top: 2px solid var(--border-light);
  padding-top: var(--space-md);
  margin-top: var(--space-md);
  position: relative;
  animation: fadeInUp var(--transition-normal);
}

.version-comparison__comparison-result::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 3.75rem;
  height: 2px;
  background: var(--bg-gradient);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(1.25rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.version-comparison__result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
  font-weight: 700;
  color: var(--gray-800);
  font-size: var(--font-size-base);
}

.version-comparison__result-items {
  display: grid;
  gap: var(--space-xs);
}

.version-comparison__result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), var(--gray-50));
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  transition: all var(--transition-fast);
}

.version-comparison__result-item:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
  transform: translateX(0.25rem);
}

.version-comparison__result-label {
  color: var(--gray-700);
  font-weight: 600;
  font-size: var(--font-size-base);
}

/* 操作面板 - 现代化设计，统一字体，更紧凑间距 */
.version-comparison__panel-section {
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 2px solid var(--border-light);
  position: relative;
}

.version-comparison__panel-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.version-comparison__panel-section::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 3.75rem;
  height: 2px;
  background: var(--bg-gradient);
}

.version-comparison__panel-section:last-child::after {
  display: none;
}

.version-comparison__panel-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.version-comparison__panel-button {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  font-size: var(--font-size-base);
}

.version-comparison__panel-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.version-comparison__panel-button:hover::before {
  left: 100%;
}

/* 统计信息 - 现代化网格，统一字体，更紧凑间距 */
.version-comparison__stats-title,
.version-comparison__usage-title {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--gray-700);
  margin-bottom: var(--space-md);
  text-align: center;
  position: relative;
}

.version-comparison__stats-grid {
  display: grid;
  gap: var(--space-xs);
}

.version-comparison__stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), var(--gray-50));
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  transition: all var(--transition-fast);
}

.version-comparison__stats-item:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
  transform: scale(1.02);
}

.version-comparison__stats-label {
  color: var(--gray-600);
  font-weight: 600;
  font-size: var(--font-size-base);
}

.version-comparison__stats-value {
  font-weight: 700;
  color: var(--gray-800);
  font-size: var(--font-size-base);
  padding: var(--space-2xs) var(--space-xs);
  border-radius: var(--radius-sm);
  background: var(--gray-100);
  border: 1px solid var(--border-light);
}

.version-comparison__stats-value--success {
  color: var(--success-600);
  background: var(--success-50);
  border-color: var(--success-200);
}

.version-comparison__stats-value--error {
  color: var(--error-600);
  background: var(--error-50);
  border-color: var(--error-200);
}

/* 使用说明，统一字体，更紧凑间距 */
.version-comparison__usage-list {
  font-size: var(--font-size-base);
  color: var(--gray-600);
  line-height: 1.6;
}

.version-comparison__usage-item {
  margin-bottom: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-md);
  border-left: 0.25rem solid var(--primary-300);
  border: 1px solid var(--border-light);
  border-left: 0.25rem solid var(--primary-300);
  transition: all var(--transition-fast);
  font-size: var(--font-size-base);
}

.version-comparison__usage-item:hover {
  border-left-color: var(--primary-500);
  background: rgba(255, 255, 255, 1);
  border-color: var(--border-medium);
  transform: translateX(0.25rem);
}

.version-comparison__test-data {
  margin-top: var(--space-sm);
  padding: var(--space-sm);
  background: linear-gradient(145deg, var(--primary-50), var(--primary-100));
  border-radius: var(--radius-lg);
  border: 2px solid var(--primary-200);
  font-size: var(--font-size-base);
}

.version-comparison__test-link {
  color: var(--primary-600);
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-fast);
  font-size: var(--font-size-base);
  background: transparent;
  border: 1px solid var(--primary-600);
  border-radius: var(--radius-md);
  padding: var(--space-xs) var(--space-sm);
  cursor: pointer;
  display: inline-block;
}

.version-comparison__test-link:hover {
  color: var(--primary-700);
  background: var(--primary-50);
  border-color: var(--primary-700);
  transform: translateY(-0.0625rem);
}

/* 比对记录表格区域 - 现代化设计，占满宽度，统一字体，更紧凑间距 */
.version-comparison__records {
  background: var(--card-gradient);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-xl);
  border: 2px solid var(--border-light);
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.version-comparison__records::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.25rem;
  background: var(--bg-gradient);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.version-comparison__records-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-md);
  border-bottom: 2px solid var(--border-light);
  position: relative;
}

.version-comparison__records-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6.25rem;
  height: 2px;
  background: var(--bg-gradient);
}

.version-comparison__records-title {
  display: flex;
  align-items: center;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-800);
}

.version-comparison__records-title .el-icon {
  font-size: var(--font-size-2xl);
  color: var(--primary-600);
  margin-right: var(--space-md);
  padding: var(--space-sm);
  background: var(--primary-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-200);
}

.version-comparison__records-count {
  margin-left: var(--space-md);
  padding: var(--space-2xs) var(--space-sm);
  background: linear-gradient(145deg, var(--primary-50), var(--primary-100));
  color: var(--primary-700);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-sm);
  font-weight: 600;
  border: 1px solid var(--primary-200);
}

.version-comparison__records-subtitle {
  font-size: var(--font-size-base);
  color: var(--gray-600);
  margin-top: var(--space-xs);
  font-weight: 500;
}

/* 记录表格操作按钮区域 */
.version-comparison__records-actions {
  display: flex;
  gap: var(--space-md);
  align-items: center;
}

.version-comparison__records-actions .el-button {
  font-size: var(--font-size-sm) !important;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-lg);
}

/* 空状态 - 现代化设计，统一字体 */
.version-comparison__records-empty {
  text-align: center;
  padding: var(--space-2xl);
  color: var(--gray-500);
}

.version-comparison__records-empty-icon {
  font-size: 4rem;
  color: var(--gray-400);
  margin-bottom: var(--space-lg);
}

.version-comparison__records-empty-text {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--space-xs);
  color: var(--gray-600);
}

.version-comparison__records-empty-desc {
  font-size: var(--font-size-base);
  color: var(--gray-500);
}

/* Element Plus 组件覆盖 - 增强边框可见性，统一字体 */
.version-comparison .el-input__wrapper {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  border: 2px solid var(--border-light) !important;
  background-color: #ffffff !important;
}

.version-comparison .el-input__wrapper:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-medium) !important;
}

.version-comparison .el-input__wrapper.is-focus {
  border-color: var(--border-accent) !important;
  box-shadow: 0 0 0 0.1875rem rgba(59, 130, 246, 0.1) !important;
}

.version-comparison .el-input__inner {
  color: var(--gray-800) !important;
  font-weight: 500;
  font-size: var(--font-size-base) !important;
}

.version-comparison .el-button {
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent !important;
  font-size: var(--font-size-base) !important;
}

.version-comparison .el-button--primary {
  border-color: var(--primary-600) !important;
}

.version-comparison .el-button--danger {
  border-color: var(--error-600) !important;
}

.version-comparison .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.version-comparison .el-button:hover::before {
  left: 100%;
}

.version-comparison .el-button:hover {
  transform: translateY(-0.125rem);
  box-shadow: var(--shadow-lg);
}

.version-comparison .el-tag {
  border-radius: var(--radius-xl);
  font-weight: 600;
  border: 1px solid transparent;
  padding: var(--space-2xs) var(--space-sm);
  font-size: var(--font-size-sm) !important;
}

.version-comparison .el-tag.el-tag--success {
  background: linear-gradient(145deg, var(--success-50), var(--success-100));
  color: var(--success-600);
  border-color: var(--success-200);
  box-shadow: var(--shadow-sm);
}

.version-comparison .el-tag.el-tag--danger {
  background: linear-gradient(145deg, var(--error-50), var(--error-100));
  color: var(--error-600);
  border-color: var(--error-200);
  box-shadow: var(--shadow-sm);
}

.version-comparison .el-table {
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 2px solid var(--border-light);
  font-size: var(--font-size-base) !important;
  width: 100% !important;
  margin-top: var(--space-lg);
}

.version-comparison .el-table th.el-table__cell {
  background: linear-gradient(145deg, var(--gray-50), var(--gray-100));
  font-weight: 700;
  color: var(--gray-700);
  border-bottom: 2px solid var(--border-medium);
  font-size: var(--font-size-base) !important;
  padding: var(--space-sm) var(--space-md) !important;
}

.version-comparison .el-table td.el-table__cell {
  border-bottom: 1px solid var(--border-light);
  font-size: var(--font-size-base) !important;
  padding: var(--space-xs) var(--space-md) !important;
}

.version-comparison .el-table tr:hover td {
  background: linear-gradient(145deg, var(--primary-50), rgba(59, 130, 246, 0.05));
}

/* 响应式设计 - 统一的媒体查询断点，保持全宽度 */

/* 大屏幕 (1440px及以上) */
@media screen and (min-width: 1440px) {
  .version-comparison__main {
    grid-template-columns: repeat(auto-fit, minmax(24rem, 1fr));
    gap: var(--space-xl);
  }
}

/* 中等屏幕 (1200px - 1439px) */
@media screen and (max-width: 1439px) {
  .version-comparison__main {
    grid-template-columns: repeat(auto-fit, minmax(20rem, 1fr));
    gap: var(--space-lg);
  }
}

/* 小屏幕 (992px - 1199px) */
@media screen and (max-width: 1199px) {
  .version-comparison__main {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-md);
  }
  
  .version-comparison__panel {
    grid-column: span 2;
  }
}

/* 平板 (768px - 991px) */
@media screen and (max-width: 991px) {
  .version-comparison {
    padding: var(--space-md);
  }
  
  .version-comparison__main {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .version-comparison__baseline-input,
  .version-comparison__target-input {
    flex-direction: column;
    gap: var(--space-xs);
  }
  
  .version-comparison__panel-actions {
    gap: var(--space-xs);
  }
  
  .version-comparison__card {
    padding: var(--space-lg);
  }
  
  .version-comparison__records {
    padding: var(--space-lg);
    border-radius: var(--radius-xl);
  }
}

/* 手机 (768px以下) */
@media screen and (max-width: 767px) {
  .version-comparison {
    padding: var(--space-sm);
  }
  
  .version-comparison__card {
    padding: var(--space-md);
    border-radius: var(--radius-xl);
  }
  
  .version-comparison__records {
    padding: var(--space-md);
  }
  
  .version-comparison__card-title {
    font-size: var(--font-size-lg);
  }
  
  .version-comparison__records-title {
    font-size: var(--font-size-xl);
  }
}

/* 超小屏幕 (480px以下) */
@media screen and (max-width: 479px) {
  .version-comparison {
    padding: var(--space-xs);
  }
  
  .version-comparison__card {
    padding: var(--space-sm);
  }
  
  .version-comparison__records {
    padding: var(--space-sm);
  }
}

/* 确保表格在小屏幕上可以横向滚动 */
@media screen and (max-width: 1200px) {
  .version-comparison__records {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .version-comparison .el-table {
    min-width: 56.25rem; /* 900px -> 56.25rem */
  }
}

/* 打印媒体查询 */
@media print {
  .version-comparison {
    padding: 0;
  }
  
  .version-comparison__main {
    grid-template-columns: 1fr;
  }
  
  .version-comparison__panel {
    display: none;
  }
  
  .version-comparison__records {
    overflow: visible;
  }
}

/* 动画增强 */
.version-comparison__card {
  animation: slideInUp 0.6s ease-out;
}

.version-comparison__card:nth-child(2) {
  animation-delay: 0.1s;
}

.version-comparison__card:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(1.875rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 性能优化 */
.version-comparison * {
  will-change: auto;
}

.version-comparison__card:hover,
.version-comparison__stats-item:hover,
.version-comparison__info-item:hover {
  will-change: transform;
} 