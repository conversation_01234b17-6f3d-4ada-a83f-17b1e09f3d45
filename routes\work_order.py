from flask import Blueprint, request, jsonify, send_file
from database.db_manager import DatabaseManager
from models.modelwork_order import WorkOrder
from models.modelquality_inspection import QualityInspectionWorkOrder
from sqlalchemy import desc, asc, text, func
from datetime import datetime
import logging
from jwt import decode
import pandas as pd
from io import BytesIO

work_order_bp = Blueprint('work_order', __name__)
logger = logging.getLogger(__name__)

# 添加获取当前用户信息的函数
def get_current_user():
    try:
        token = request.cookies.get('token')
        if token:
            # 使用与 app.py 相同的 JWT_SECRET
            JWT_SECRET = 'your-jwt-secret'  # 建议通过配置文件或环境变量管理
            user_data = decode(token, JWT_SECRET, algorithms=["HS256"])
            return user_data.get('username')
    except Exception as e:
        logger.error(f"Error getting current user: {str(e)}")
    return None

# 添加获取当前用户的路由
@work_order_bp.route('/current-user', methods=['GET'])
def get_current_user_info():
    try:
        username = get_current_user()
        if username:
            return jsonify({
                'success': True,
                'username': username
            })
        return jsonify({
            'success': False,
            'message': '未获取到用户信息'
        }), 401
    except Exception as e:
        logger.error(f"Error getting current user info: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取用户信息失败'
        }), 500

@work_order_bp.route('/orders', methods=['GET'])
def get_orders():
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        search_keyword = request.args.get('searchKeyword', '')
        sort_field = request.args.get('sortField', 'ord_createTime')
        sort_order = request.args.get('sortOrder', 'desc')
        
        db = DatabaseManager()
        with db.get_session() as session:
            query = session.query(WorkOrder)
            
            # 搜索条件
            if search_keyword:
                query = query.filter(WorkOrder.dbord_processOrderNo.like(f'%{search_keyword}%'))
            
            # 排序 - 只对数据库实际存在的字段进行排序
            if sort_field and sort_field != 'production_status':  # 添加判断
                db_field = f'dbord_{sort_field[4:]}' if sort_field.startswith('ord_') else sort_field
                order_func = desc if sort_order == 'desc' else asc
                query = query.order_by(order_func(getattr(WorkOrder, db_field)))
            
            # 计算总数
            total_count = query.count()
            
            # 分页
            orders = query.offset((page - 1) * page_size).limit(page_size).all()
            
            # 获取每个工单的状态信息
            orders_with_status = []
            for order in orders:
                order_dict = order.to_dict()
                status_info = order.get_production_status(session)
                order_dict['production_status'] = status_info
                orders_with_status.append(order_dict)
            
            return jsonify({
                'success': True,
                'orders': orders_with_status,
                'total': total_count
            })
            
    except Exception as e:
        logger.error(f"Error getting orders: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取工单列表失败'
        }), 500

@work_order_bp.route('/orders', methods=['POST'])
def create_order():
    try:
        data = request.get_json()
        
        # 如果前端没有提供创建人员，则使用当前登录用户
        if 'ord_creator' not in data:
            current_user = get_current_user()
            if current_user:
                data['ord_creator'] = current_user
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 检查工单号是否已存在
            if session.query(WorkOrder).filter(
                WorkOrder.dbord_processOrderNo == data['ord_processOrderNo']
            ).first():
                return jsonify({
                    'success': False,
                    'message': '工单号已存在'
                }), 400
            
            # 转换前端字段名到数据库字段名
            db_data = {
                'dbord_' + key[4:]: value 
                for key, value in data.items() 
                if key.startswith('ord_')
            }
            
            # 单独处理 orderproduct_type_id
            if 'orderproduct_type_id' in data:
                db_data['orderproduct_type_id'] = data['orderproduct_type_id']
            
            # 设置创建时间
            create_time = datetime.now()
            db_data['dbord_createTime'] = create_time
            
            # 生成批次号
            batch_number = generate_batch_number(
                session, 
                data['ord_productCode'],
                create_time
            )
            db_data['dbord_probatch'] = batch_number
            
            new_order = WorkOrder(**db_data)
            session.add(new_order)
            session.commit()
            
            return jsonify({
                'success': True,
                'message': '工单创建成功',
                'order': new_order.to_dict()
            })
            
    except Exception as e:
        logger.error(f"Error creating order: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e) if str(e) else '创建工单失败'
        }), 500

@work_order_bp.route('/orders/<int:order_id>', methods=['PUT'])
def update_order(order_id):
    try:
        data = request.get_json()
        
        # 获取排序参数
        sort_field = request.args.get('sortField', 'ord_createTime')
        sort_order = request.args.get('sortOrder', 'desc')
        
        # 转换前端字段名到数据库字段名
        db_data = {
            'dbord_' + key[4:]: value 
            for key, value in data.items() 
            if key.startswith('ord_')
        }
        
        # 单独处理 orderproduct_type_id
        if 'orderproduct_type_id' in data:
            db_data['orderproduct_type_id'] = data['orderproduct_type_id']
        
        db = DatabaseManager()
        with db.get_session() as session:
            order = session.query(WorkOrder).get(order_id)
            if not order:
                return jsonify({
                    'success': False,
                    'message': '工单不存在'
                }), 404
                
            # 检查加工单号是否被修改以及是否与其他工单冲突
            new_process_order_no = db_data.get('dbord_processOrderNo')
            if new_process_order_no and new_process_order_no != order.dbord_processOrderNo:
                existing_order = session.query(WorkOrder).filter(
                    WorkOrder.dbord_processOrderNo == new_process_order_no,
                    WorkOrder.id != order_id  # 排除当前工单自身
                ).first()
                if existing_order:
                    return jsonify({
                        'success': False,
                        'message': f'加工单号 \'{new_process_order_no}\' 已被其他工单使用'
                    }), 400

            for key, value in db_data.items():
                # 不允许修改批次号、产品编码、完成状态、SN长度
                if key not in ['dbord_probatch', 'dbord_productCode', 'dbord_completed_flag', 'dbord_snlenth']:
                    setattr(order, key, value)
                
            session.commit()
            
            # 重新查询最新数据，应用排序
            db_field = f'dbord_{sort_field[4:]}' if sort_field.startswith('ord_') else sort_field
            # 处理 orderproduct_type_id 排序
            if sort_field == 'orderproduct_type_id':
                db_field = 'orderproduct_type_id'
            
            order_func = desc if sort_order == 'desc' else asc
            
            query = session.query(WorkOrder).order_by(order_func(getattr(WorkOrder, db_field)))
            orders = query.all()
            
            # 获取每个工单的状态信息
            orders_with_status = []
            for order in orders:
                order_dict = order.to_dict()
                status_info = order.get_production_status(session)
                order_dict['production_status'] = status_info
                orders_with_status.append(order_dict)
            
            return jsonify({
                'success': True,
                'message': '工单更新成功',
                'orders': orders_with_status,
                'total': len(orders)
            })
            
    except Exception as e:
        logger.error(f"Error updating order: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e) if str(e) else '更新工单失败'
        }), 500

@work_order_bp.route('/orders/<int:order_id>', methods=['DELETE'])
def delete_order(order_id):
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            order = session.query(WorkOrder).get(order_id)
            if not order:
                return jsonify({
                    'success': False,
                    'message': '工单不存在'
                }), 404
                
            session.delete(order)
            session.commit()
            
            return jsonify({
                'success': True,
                'message': '工单删除成功'
            })
            
    except Exception as e:
        logger.error(f"Error deleting order: {str(e)}")
        return jsonify({
            'success': False,
            'message': '删除工单失败'
        }), 500

@work_order_bp.route('/orders/batch', methods=['DELETE'])
def batch_delete_orders():
    try:
        order_ids = request.get_json().get('orderIds', [])
        
        db = DatabaseManager()
        with db.get_session() as session:
            session.query(WorkOrder).filter(WorkOrder.id.in_(order_ids)).delete(synchronize_session=False)
            session.commit()
            
            return jsonify({
                'success': True,
                'message': '工单批量删除成功'
            })
            
    except Exception as e:
        logger.error(f"Error batch deleting orders: {str(e)}")
        return jsonify({
            'success': False,
            'message': '批量删除工单失败'
        }), 500

@work_order_bp.route('/orders/export', methods=['GET'])
def export_orders():
    try:
        selected_ids = request.args.get('ids')
        
        db = DatabaseManager()
        with db.get_session() as session:
            query = session.query(WorkOrder)
            
            if selected_ids:
                ids = [int(id_) for id_ in selected_ids.split(',')]
                query = query.filter(WorkOrder.id.in_(ids))
                
            orders = query.all()
            if not orders:
                return jsonify({
                    'success': False,
                    'message': '没有数据可导出'
                }), 404
                
            data = [order.to_dict() for order in orders]
            
            # 创建 DataFrame
            df = pd.DataFrame(data)
            
            # 定义列名映射
            column_mapping = {
                'id': '序号',
                'ord_creator': '创建人员',
                'ord_processOrderNo': '加工单号',
                'ord_probatch': '产品批次',
                'ord_productionQuantity': '生产数量',
                'ord_productCode': '产品编码',
                'ord_productType': '产品类型',
                'ord_productModel': '产品型号',
                'ord_customerName': '客户名称',
                'ord_remark': '备注',
                'ord_createTime': '创建时间',
                'ord_completed_flag': '完成状态'
            }
            
            # 重命名列
            df = df.rename(columns=column_mapping)
            
            # 选择并排序列
            columns_order = ['序号', '创建人员', '加工单号', '产品批次', '生产数量',
                           '产品编码', '产品类型', '产品型号',
                           '客户名称',
                           '备注', '创建时间', '完成状态']
            # 确保只选择存在的列
            df = df[[col for col in columns_order if col in df.columns]]
            
            # 创建内存文件对象
            output = BytesIO()
            
            # 使用 xlsxwriter 引擎创建 Excel 文件
            with pd.ExcelWriter(output, engine='xlsxwriter', mode='xlsx') as writer:
                df.to_excel(writer, sheet_name='工单信息', index=False)
                
                # 获取 xlsxwriter 工作簿和工作表对象
                workbook = writer.book
                worksheet = writer.sheets['工单信息']
                
                # 创建标题格式
                header_format = workbook.add_format({
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'fg_color': '#D9D9D9',
                    'border': 1
                })
                
                # 创建单元格格式
                cell_format = workbook.add_format({
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1
                })
                
                # 设置列宽和格式
                for idx, col in enumerate(df.columns):
                    # 计算最大宽度（考虑中文字符）
                    max_length = max(
                        max(len(str(val)) * 1.5 if any('\u4e00' <= c <= '\u9fff' for c in str(val)) else len(str(val))
                            for val in df[col].astype(str)),
                        len(col) * 1.5 if any('\u4e00' <= c <= '\u9fff' for c in col) else len(col)
                    ) + 2
                    
                    # 设置列宽（最小8，最大50）
                    worksheet.set_column(idx, idx, min(max(8, max_length), 50))
                    
                    # 设置标题格式
                    worksheet.write(0, idx, col, header_format)
                    
                    # 设置数据行格式
                    for row in range(1, len(df) + 1):
                        worksheet.write(row, idx, df.iloc[row-1][col], cell_format)
                
                # 冻结首行
                worksheet.freeze_panes(1, 0)
            
            output.seek(0)
            
            # 生成更有意义的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if selected_ids:
                # 如果是选中导出，添加选中数量信息
                count = len(orders)
                filename = f'已选择{count}个工单_{timestamp}.xlsx'
            else:
                # 如果是全部导出，添加总数信息
                total = len(orders)
                filename = f'全部工单_{total}条_{timestamp}.xlsx'
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )
            
    except Exception as e:
        logger.error(f"Error exporting orders: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500 

@work_order_bp.route('/by-number', methods=['GET'])
def get_order_by_number():
    try:
        order_number = request.args.get('orderNumber', '').strip()
        if not order_number:
            return jsonify({
                'success': False,
                'message': '工单号不能为空'
            })
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 根据工单号查询工单信息
            order = session.query(WorkOrder).filter_by(
                dbord_processOrderNo=order_number
            ).first()
            
            if order:
                # 查询质检工单状态
                quality_order = session.query(QualityInspectionWorkOrder).filter_by(
                    work_order_no=order_number
                ).first()
                
                order_dict = order.to_dict()
                # 添加组装前阶段和包装前阶段完成状态
                order_dict['assembly_stage_completed'] = quality_order.assembly_stage_completed if quality_order else 0
                order_dict['test_stage_completed'] = quality_order.test_stage_completed if quality_order else 0
                order_dict['packaging_stage_completed'] = quality_order.packaging_stage_completed if quality_order else 0
                
                return jsonify({
                    'success': True,
                    'order': order_dict
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '未找到对应的工单'
                })
                
    except Exception as e:
        logger.error(f"Error getting order by number: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取工单信息失败'
        }), 500

# 自动生成符合规则的批次号
def generate_batch_number(session, product_code, create_time):
    """
    生成批次号，每年从1开始重新计数
    格式: YYMMXXXX
    例如: 23090001 表示2023年9月的第1个批次
    """
    try:
        # 1. 获取当前年份的时间范围
        year_start = datetime(create_time.year, 1, 1)
        year_end = datetime(create_time.year, 12, 31, 23, 59, 59)
        
        # 2. 查询同一年度、同一产品编码的工单数量
        max_batch = session.query(WorkOrder).filter(
            WorkOrder.dbord_productCode == product_code,
            WorkOrder.dbord_createTime >= year_start,
            WorkOrder.dbord_createTime <= year_end
        ).count()
        
        # 3. 生成新的序号 (4位数，从1开始)
        new_sequence = str(max_batch + 1).zfill(4)
        
        # 4. 格式化时间部分 (YYMM)，只显示年月
        time_part = create_time.strftime('%y%m')
        
        # 5. 组合批次号: 时间部分直接拼接序号
        batch_number = f"{time_part}{new_sequence}"
        
        return batch_number
    except Exception as e:
        logger.error(f"Error generating batch number: {str(e)}")
        raise

@work_order_bp.route('/orders/<int:order_id>/status', methods=['GET'])
def get_order_status(order_id):
    """获取工单状态信息"""
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            order = session.query(WorkOrder).get(order_id)
            if not order:
                return jsonify({
                    'success': False,
                    'message': '工单不存在'
                }), 404
                
            status_info = order.get_production_status(session)
            
            return jsonify({
                'success': True,
                'status': status_info
            })
            
    except Exception as e:
        logger.error(f"Error getting order status: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取工单状态失败'
        }), 500 

@work_order_bp.route('/orders/<int:order_id>', methods=['GET'])
def get_order(order_id):
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            order = session.query(WorkOrder).get(order_id)
            
            if not order:
                return jsonify({
                    'success': False,
                    'message': '工单不存在'
                }), 404
                
            # 获取工单状态信息
            order_dict = order.to_dict()
            status_info = order.get_production_status(session)
            order_dict['production_status'] = status_info
                
            return jsonify({
                'success': True,
                'order': order_dict
            })
            
    except Exception as e:
        logger.error(f"Error getting order: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取工单信息失败'
        }), 500 

@work_order_bp.route('/orders/<int:order_id>/complete', methods=['POST'])
def mark_order_as_completed(order_id):
    """手动标记工单为完成状态"""
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            order = session.query(WorkOrder).get(order_id)
            if not order:
                return jsonify({
                    'success': False,
                    'message': '工单不存在'
                }), 404
                
            order.dbord_completed_flag = True
            session.commit()
            
            return jsonify({
                'success': True,
                'message': '工单已标记为完成状态'
            })
            
    except Exception as e:
        logger.error(f"Error marking order as completed: {str(e)}")
        return jsonify({
            'success': False,
            'message': '标记工单完成失败'
        }), 500 

@work_order_bp.route('/validate-sn-customer', methods=['POST'])
def validate_sn_customer():
    """
    验证 SN 所属工单的客户名称是否与指定的出库客户名称匹配。
    """
    try:
        data = request.get_json()
        sn_number = data.get('sn_number')
        shipment_customer_name = data.get('shipment_customer_name')

        if not sn_number or not shipment_customer_name:
            # Return 400 Bad Request if parameters are missing
            return jsonify({'success': False, 'message': '缺少 SN 号或出库客户名称参数。'}), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 1. 根据 SN 查找关联的工单号 (dbord_processOrderNo)
            # Using UNION as SN could be in either table
            sql_query = text("""
                SELECT work_order FROM coupler_table WHERE pro_sn = :sn
                UNION
                SELECT work_order FROM cpu_table WHERE pro_sn = :sn
                LIMIT 1
            """)
            # Use scalar_one_or_none to handle cases where SN is not found gracefully
            work_order_no = session.execute(sql_query, {'sn': sn_number}).scalar_one_or_none()

            if work_order_no is None:
                # SN not found in production records
                return jsonify({
                    'success': True, # API call succeeded
                    'valid': False,  # Validation failed
                    'message': f'SN [{sn_number}] 未在生产记录中找到。'
                })

            # 2. 根据工单号查找工单记录
            order = session.query(WorkOrder).filter(
                WorkOrder.dbord_processOrderNo == work_order_no
            ).first()

            if order is None:
                # Data inconsistency: SN exists but the linked WorkOrder doesn't
                logger.error(f"Data inconsistency: Work order number '{work_order_no}' found for SN '{sn_number}', but WorkOrder record not found.")
                # Return 500 Internal Server Error for data consistency issues
                return jsonify({'success': False, 'message': '服务器内部错误：无法找到关联的工单信息。'}), 500

            order_customer_name = order.dbord_customerName

            # 3. 进行比较
            if not order_customer_name:
                # If order has no customer name, it's not customer-specific, validation passes
                return jsonify({'success': True, 'valid': True})
            else:
                # Compare case-insensitively after stripping whitespace
                if order_customer_name.strip().lower() == shipment_customer_name.strip().lower():
                    # Customer names match
                    return jsonify({'success': True, 'valid': True})
                else:
                    # Customer names do not match
                    return jsonify({
                        'success': True, # API call succeeded
                        'valid': False, # Validation failed
                        'message': f'SN对应工单客户 [{order_customer_name}] 与当前出库客户 [{shipment_customer_name}] 不符，请核对。'
                    })

    except Exception as e:
        # Log the detailed error for debugging
        logger.error(f"Error in validate_sn_customer: {str(e)}", exc_info=True)
        # Return 500 Internal Server Error for unexpected exceptions
        return jsonify({'success': False, 'message': f'服务器验证时出错。'}), 500 