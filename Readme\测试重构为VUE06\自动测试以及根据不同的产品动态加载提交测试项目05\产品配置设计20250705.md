# 产品配置设计文档
**文档版本**: v1.0  
**创建日期**: 2025-07-05  
**适用范围**: Vue测试系统模块（CPU控制器、耦合器、IO模块等）

## 📋 概述

产品配置功能是Vue测试系统中的核心组件，用于根据不同产品类型动态配置测试项目。本文档详细说明了产品配置卡片的UI设计、交互逻辑和技术实现方案，为后期开发提供标准化指南。

## 🎯 设计目标

### 1. 功能目标
- **灵活配置**: 支持多种产品类型，每种类型对应不同的测试项目组合
- **用户友好**: 直观的UI界面，清晰的配置信息展示
- **一致性**: 在不同模块间保持统一的设计风格和交互模式
- **可扩展**: 易于添加新的产品类型和测试项目

### 2. 技术目标
- **响应式设计**: 适配不同屏幕尺寸
- **性能优化**: 高效的数据管理和UI更新
- **代码复用**: 标准化的组件结构便于跨模块复用
- **维护友好**: 清晰的代码组织和文档说明

## 🎨 UI设计规范

### 1. 整体布局结构

```
┌─────────────────────────────────────────────────────────┐
│                    产品配置卡片                          │
├─────────────────────────────────────────────────────────┤
│ 🔧 产品配置                           [显示详情] [隐藏详情] │
├─────────────────────────────────────────────────────────┤
│ 产品类型: [下拉选择器 ▼]                                  │
├─────────────────────────────────────────────────────────┤
│ ┌─ 当前配置 ────────────────────── [N个测试项目] ┐       │
│ │ 配置描述信息                                    │       │
│ │ ┌─ 详细配置(可展开) ─────────────────────┐   │       │
│ │ │ ✓ 测试项目1 (类别)                      │   │       │
│ │ │ ✓ 测试项目2 (类别)                      │   │       │
│ │ │ ✓ 测试项目3 (类别)                      │   │       │
│ │ └─────────────────────────────────────────┘   │       │
│ └─────────────────────────────────────────────────┘       │
├─────────────────────────────────────────────────────────┤
│ ℹ️ 操作提示信息                                           │
└─────────────────────────────────────────────────────────┘
```

### 2. 视觉设计元素

#### 卡片样式
```css
.module-controller__card {
    /* 玻璃效果背景 */
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
```

#### 图标设计
```html
<!-- 配置图标 -->
<div class="module-controller__icon module-controller__icon--indigo">
    <i data-lucide="settings-2" class="w-4 h-4 text-white"></i>
</div>
```

#### 颜色规范
- **主色调**: Indigo（#6366f1）- 配置功能专用色
- **成功色**: Green（#10b981）- 启用状态
- **信息色**: Blue（#3b82f6）- 信息提示
- **中性色**: Gray（#6b7280）- 辅助文本

### 3. 响应式适配

```css
/* 桌面端 */
@media (min-width: 1024px) {
    .product-config__grid { grid-template-columns: 1fr 1fr; }
}

/* 平板端 */
@media (max-width: 768px) {
    .product-config__grid { grid-template-columns: 1fr; }
}

/* 移动端 */
@media (max-width: 480px) {
    .product-config__card { padding: 12px; }
}
```

## ⚡ 交互设计

### 1. 用户操作流程

```mermaid
graph TD
    A[用户进入页面] --> B[显示默认配置]
    B --> C[用户点击下拉选择器]
    C --> D[显示所有可用配置]
    D --> E[用户选择新配置]
    E --> F[更新配置显示]
    F --> G[记录操作日志]
    G --> H[显示成功提示]
    
    I[用户点击详情按钮] --> J{当前状态}
    J -->|隐藏| K[展开详细配置]
    J -->|显示| L[折叠详细配置]
    K --> M[显示测试项目列表]
    L --> N[隐藏测试项目列表]
```

### 2. 交互状态管理

#### 状态类型
```typescript
interface ProductConfigState {
    selectedType: string;        // 当前选择的产品类型
    showDetails: boolean;        // 是否显示详细配置
    loading: boolean;           // 加载状态
    config: ProductConfig;      // 当前配置对象
}
```

#### 状态转换
```javascript
// 配置切换
const handleProductTypeChange = (newType) => {
    selectedProductType.value = newType;
    showSuccessMessage();
    logConfigChange();
    updateTestItems();
};

// 详情切换
const toggleDetails = () => {
    showProductTypeDetails.value = !showProductTypeDetails.value;
    refreshIcons();
};
```

### 3. 用户反馈机制

#### 即时反馈
- **选择反馈**: 下拉选择器显示当前选中项
- **状态反馈**: 配置卡片显示当前配置信息
- **操作反馈**: ElMessage成功提示

#### 视觉反馈
- **hover效果**: 卡片和按钮的悬停状态
- **active效果**: 按钮点击时的视觉反馈
- **transition**: 平滑的展开/折叠动画

## 🛠️ 技术实现

### 1. 数据结构设计

#### 产品配置对象
```javascript
const PRODUCT_TYPE_CONFIGS = {
    'config_key': {
        name: '配置显示名称',
        description: '配置描述信息',
        enabledTestCount: 5,                    // 启用的测试项目数量
        enabledTests: [0, 1, 2, 3, 4],         // 启用的测试项目索引
        testMapping: [                          // 测试项目映射
            { 
                index: 0, 
                testName: '测试项目名称', 
                category: '测试类别' 
            }
        ]
    }
};
```

#### 选项数据结构
```javascript
const PRODUCT_TYPE_OPTIONS = Object.keys(PRODUCT_TYPE_CONFIGS).map(key => ({
    value: key,
    label: PRODUCT_TYPE_CONFIGS[key].name,
    description: PRODUCT_TYPE_CONFIGS[key].description,
    count: PRODUCT_TYPE_CONFIGS[key].enabledTestCount
}));
```

### 2. Vue组件实现

#### 响应式数据
```javascript
// 产品类型选择相关
const selectedProductType = ref('default_config');
const showProductTypeDetails = ref(false);
```

#### 计算属性
```javascript
// 当前产品配置
const currentProductConfig = computed(() => {
    return PRODUCT_TYPE_CONFIGS[selectedProductType.value] || 
           PRODUCT_TYPE_CONFIGS['default_config'];
});

// 启用的测试项目
const enabledTestItems = computed(() => {
    const config = currentProductConfig.value;
    return testResults.value.map((item, index) => ({
        ...item,
        enabled: config.enabledTests.includes(index),
        configIndex: config.enabledTests.indexOf(index)
    }));
});
```

#### 事件处理函数
```javascript
const handleProductTypeChange = (type) => {
    const config = PRODUCT_TYPE_CONFIGS[type];
    if (!config) return;
    
    // 记录日志
    addTestLog('info', 'PRODUCT_TYPE', 
              `切换产品类型: ${config.name}`, 
              `启用测试项目: ${config.enabledTestCount}个`);
    
    // 显示成功提示
    ElMessage.success(`已切换到: ${config.name}`);
};
```

### 3. 模板结构

```vue
<template>
<!-- 产品配置卡片 -->
<div class="module-controller__card theme-card glass-effect card-hover">
    <div class="module-controller__card-content">
        <!-- 卡片头部 -->
        <div class="module-controller__progress-header">
            <div class="module-controller__card-title">
                <div class="module-controller__icon module-controller__icon--indigo">
                    <i data-lucide="settings-2" class="w-4 h-4 text-white"></i>
                </div>
                <span class="module-controller__title-section theme-text-primary">
                    产品配置
                </span>
            </div>
            <div class="module-controller__progress-tabs">
                <el-button
                    size="small"
                    @click="showProductTypeDetails = !showProductTypeDetails"
                    class="h-8 text-xs"
                >
                    {{ showProductTypeDetails ? '隐藏详情' : '显示详情' }}
                </el-button>
            </div>
        </div>

        <!-- 产品类型选择器 -->
        <div class="mb-4">
            <div class="flex items-center space-x-4">
                <label class="text-sm font-medium theme-text-secondary min-w-fit">
                    产品类型:
                </label>
                <el-select 
                    v-model="selectedProductType" 
                    @change="handleProductTypeChange"
                    placeholder="选择产品类型"
                    class="flex-1"
                >
                    <el-option
                        v-for="option in PRODUCT_TYPE_OPTIONS"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                    >
                        <div class="flex items-center justify-between w-full">
                            <span>{{ option.label }}</span>
                            <span class="text-xs text-gray-500">
                                {{ option.count }}项测试
                            </span>
                        </div>
                    </el-option>
                </el-select>
            </div>
        </div>

        <!-- 当前配置显示 -->
        <div class="bg-gray-50 rounded-lg p-3 mb-4">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">当前配置</span>
                <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                    {{ currentProductConfig.enabledTestCount }} 个测试项目
                </span>
            </div>
            <p class="text-sm text-gray-600 mb-2">
                {{ currentProductConfig.description }}
            </p>
            
            <!-- 详细配置 -->
            <transition name="collapse">
                <div v-show="showProductTypeDetails" 
                     class="mt-3 pt-3 border-t border-gray-200">
                    <div class="space-y-2">
                        <p class="text-xs font-medium text-gray-700 mb-2">
                            启用的测试项目:
                        </p>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            <div 
                                v-for="mapping in currentProductConfig.testMapping" 
                                :key="mapping.index"
                                class="flex items-center space-x-2 text-xs bg-white rounded px-2 py-1"
                            >
                                <span class="bg-green-100 text-green-700 px-1.5 py-0.5 rounded">
                                    ✓
                                </span>
                                <span class="text-gray-700">{{ mapping.testName }}</span>
                                <span class="text-gray-500">({{ mapping.category }})</span>
                            </div>
                        </div>
                    </div>
                </div>
            </transition>
        </div>

        <!-- 操作提示 -->
        <div class="flex items-start space-x-2 text-xs text-gray-500">
            <i data-lucide="info" class="w-4 h-4 mt-0.5 text-blue-500"></i>
            <div class="flex-1">
                <p>选择产品类型后，测试项目将根据配置显示相应的测试内容。</p>
                <p class="mt-1">不同产品类型包含不同的测试项目组合，请根据实际产品选择合适的配置。</p>
            </div>
        </div>
    </div>
</div>
</template>
```

## 📚 开发指南

### 1. 新模块集成步骤

#### 步骤1: 定义产品配置
```javascript
// 1. 在setup()函数开头定义配置对象
const MODULE_PRODUCT_TYPE_CONFIGS = {
    'config_type_1': {
        name: '配置名称1',
        description: '配置描述1',
        enabledTestCount: 3,
        enabledTests: [0, 1, 2],
        testMapping: [
            { index: 0, testName: '测试项目1', category: '类别1' }
        ]
    }
    // ... 更多配置
};
```

#### 步骤2: 添加响应式数据
```javascript
// 2. 在响应式数据部分添加
const selectedProductType = ref('default_config');
const showProductTypeDetails = ref(false);
```

#### 步骤3: 创建计算属性
```javascript
// 3. 添加计算属性
const currentProductConfig = computed(() => {
    return MODULE_PRODUCT_TYPE_CONFIGS[selectedProductType.value] || 
           MODULE_PRODUCT_TYPE_CONFIGS['default_config'];
});
```

#### 步骤4: 实现处理函数
```javascript
// 4. 添加事件处理函数
const handleProductTypeChange = (type) => {
    const config = MODULE_PRODUCT_TYPE_CONFIGS[type];
    if (!config) return;
    
    addTestLog('info', 'PRODUCT_TYPE', `切换产品类型: ${config.name}`);
    ElMessage.success(`已切换到: ${config.name}`);
};
```

#### 步骤5: 更新return对象
```javascript
// 5. 在return对象中导出
return {
    // ... 其他数据和方法
    selectedProductType,
    showProductTypeDetails,
    currentProductConfig,
    handleProductTypeChange,
    MODULE_PRODUCT_TYPE_OPTIONS
};
```

#### 步骤6: 添加模板
将上述模板结构添加到右侧测试区域，通常位于测试进度卡片之前。

### 2. 配置定制指南

#### 针对不同模块的配置定制

**CPU控制器模块示例**:
```javascript
const CPU_PRODUCT_TYPE_CONFIGS = {
    'standard_5comm': {
        name: '标准5通信产品',
        description: 'RS485*2 + RS232 + CANbus + EtherCAT',
        mAreaTestCount: 5,  // CPU模块特有：M区控制
        testMapping: [
            { index: 0, testName: 'RS485_1通信', mValue: 'M0', category: '通信' }
        ]
    }
};
```

**耦合器模块示例**:
```javascript
const COUPLER_PRODUCT_TYPE_CONFIGS = {
    'all_features': {
        name: 'All（全功能版本）',
        description: 'Backplane Bus通信+Body I/O输入输出+Led数码管+Led灯珠+网口',
        enabledTestCount: 5,  // 耦合器模块特有：启用数量
        enabledTests: [0, 1, 2, 3, 4],
        testMapping: [
            { index: 0, testName: 'Backplane Bus通信', category: '通信' }
        ]
    }
};
```

### 3. 最佳实践

#### 命名规范
```javascript
// 配置对象命名: {MODULE}_PRODUCT_TYPE_CONFIGS
const CPU_PRODUCT_TYPE_CONFIGS = {};
const COUPLER_PRODUCT_TYPE_CONFIGS = {};
const IO_PRODUCT_TYPE_CONFIGS = {};

// 选项数组命名: {MODULE}_PRODUCT_TYPE_OPTIONS
const CPU_PRODUCT_TYPE_OPTIONS = [];
const COUPLER_PRODUCT_TYPE_OPTIONS = [];
const IO_PRODUCT_TYPE_OPTIONS = [];
```

#### 代码组织
```javascript
// 1. 在文件顶部定义静态配置
const MODULE_PRODUCT_TYPE_CONFIGS = { /* ... */ };

// 2. 在setup()函数中按顺序组织
setup() {
    // 静态配置 → 响应式数据 → 计算属性 → 方法函数 → return
}
```

#### 样式继承
```css
/* 使用模块前缀避免样式冲突 */
.cpu-controller__card { /* CPU模块样式 */ }
.coupler-controller__card { /* 耦合器模块样式 */ }
.io-controller__card { /* IO模块样式 */ }
```

### 4. 测试和验证

#### 功能测试清单
- [ ] 配置选择器工作正常
- [ ] 配置切换时UI正确更新
- [ ] 详情展开/折叠动画流畅
- [ ] 日志记录功能正常
- [ ] 消息提示显示正确
- [ ] 响应式布局适配正确

#### 兼容性测试
- [ ] 不同浏览器兼容性
- [ ] 移动端适配
- [ ] 不同分辨率显示
- [ ] 深色/浅色主题切换

## 🔧 维护说明

### 1. 新增产品类型
```javascript
// 在配置对象中添加新的类型
const NEW_CONFIG = {
    'new_product_type': {
        name: '新产品类型',
        description: '新产品描述',
        enabledTestCount: 4,
        enabledTests: [0, 1, 3, 4],
        testMapping: [
            { index: 0, testName: '新测试项目', category: '新类别' }
        ]
    }
};
```

### 2. 修改现有配置
```javascript
// 更新配置信息时需要考虑：
// 1. 是否影响现有用户的配置选择
// 2. 是否需要数据迁移
// 3. 是否需要更新测试逻辑
```

### 3. 性能优化
```javascript
// 使用计算属性缓存结果
const computedConfig = computed(() => { /* ... */ });

// 使用防抖优化频繁操作
const debouncedHandler = getDebounced('configChange', handler, 300);
```

## 📖 参考资料

### 1. 相关文件
- `static/page_js_css/CPUControllerVue.js` - CPU控制器实现参考
- `static/page_js_css/CouplerVue.js` - 耦合器实现参考
- `static/page_js_css/CouplerVue.css` - 样式实现参考

### 2. 技术栈
- **Vue 3**: Composition API
- **Element Plus**: UI组件库
- **Lucide Icons**: 图标库
- **CSS3**: 现代CSS特性

### 3. 设计原则
- **一致性**: 保持与现有组件的设计一致性
- **可用性**: 提供直观易用的操作界面
- **可维护性**: 清晰的代码结构和文档说明
- **可扩展性**: 便于添加新功能和配置类型

---

**文档维护**: 当产品配置功能有重大更新时，请及时更新本文档  
**版本记录**: v1.0 (2025-07-05) - 初始版本创建 