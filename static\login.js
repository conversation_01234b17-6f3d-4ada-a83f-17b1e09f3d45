// 登录表单处理
document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    const errorMessage = document.getElementById('errorMessage');
    const loginButton = document.getElementById('loginButton');
    const buttonText = loginButton.querySelector('.button-text');
    const loadingSpinner = loginButton.querySelector('.loading-spinner');
    const successCheckmark = loginButton.querySelector('.success-checkmark');
    
    // 禁用按钮并显示加载状态
    loginButton.disabled = true;
    buttonText.textContent = '登录中...';
    loadingSpinner.style.display = 'block';
    errorMessage.textContent = '';
    
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                username, 
                password,
                remember_me: rememberMe 
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 显示成功状态
            loadingSpinner.style.display = 'none';
            successCheckmark.style.display = 'block';
            buttonText.textContent = '登录成功';
            loginButton.classList.add('success');
            
            // 存储token
            localStorage.setItem('token', data.token);
            
            // 延迟跳转，让用户看到成功动画
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 跳转前的过渡动画
            document.querySelector('.login-box').style.opacity = '0';
            await new Promise(resolve => setTimeout(resolve, 300));
            
            window.location.href = '/';
        } else {
            // 显示错误信息
            loadingSpinner.style.display = 'none';
            buttonText.textContent = '登录';
            loginButton.disabled = false;
            errorMessage.textContent = data.message || '登录失败';
            
            // 错误抖动动画
            loginButton.classList.add('error-shake');
            setTimeout(() => {
                loginButton.classList.remove('error-shake');
            }, 500);
        }
    } catch (error) {
        console.error('Login error:', error);
        loadingSpinner.style.display = 'none';
        buttonText.textContent = '登录';
        loginButton.disabled = false;
        errorMessage.textContent = '网络连接错误，请稍后重试';
    }
});

// 添加记住密码的本地存储功能
document.addEventListener('DOMContentLoaded', () => {
    const rememberMe = localStorage.getItem('rememberMe') === 'true';
    document.getElementById('rememberMe').checked = rememberMe;
    
    document.getElementById('rememberMe').addEventListener('change', (e) => {
        localStorage.setItem('rememberMe', e.target.checked);
    });
});

// 添加按钮点击涟漪效果
document.getElementById('loginButton').addEventListener('click', function(e) {
    const button = e.currentTarget;
    const effect = button.querySelector('.button-effect');
    
    // 重置动画
    effect.style.width = '0';
    effect.style.height = '0';
    
    // 触发重排
    void effect.offsetWidth;
    
    // 开始新动画
    effect.style.width = '200%';
    effect.style.height = '200%';
});

// 添加密码可见性切换功能
document.getElementById('passwordToggle').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// 更新年份
function updateYear() {
    const year = new Date().getFullYear();
    document.getElementById('currentYear').textContent = year;
}

// 初始化年份显示
document.addEventListener('DOMContentLoaded', () => {
    updateYear();
}); 