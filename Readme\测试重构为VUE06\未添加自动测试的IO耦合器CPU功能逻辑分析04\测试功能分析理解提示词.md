请详细分析 `static/page_js_css/CPUController.js` 文件的功能实现，重点关注以下四个核心功能模块，并按照指定顺序生成结构化的markdown文档：

**分析重点：**
1. **工单验证逻辑**：分析工单号输入后的验证机制，包括：
   - 工单号输入的触发机制和防抖处理
   - 工单状态检查的具体验证规则
   - 工单信息查询的API调用流程
   - 验证失败时的错误处理和用户提示

2. **检测状态逻辑**：分析产品检测相关的状态判断，包括：
   - SN号输入后的检测流程
   - PCBA绑定状态的检查机制
   - 产品状态对检测流程的影响
   - 检测失败时的处理逻辑

3. **版本比较逻辑**：分析版本信息的获取和比较机制，包括：
   - 自动版本信息获取的触发条件
   - 版本信息的数据来源和获取方式
   - 自动获取版本与手动输入版本的比较规则
   - 版本不一致时的错误处理

4. **测试提交逻辑**：分析测试数据的提交流程，包括：
   - 提交前的数据验证步骤
   - 表单数据的收集和格式转换
   - 测试结果的默认状态和用户选择处理
   - 提交成功后的状态管理和界面重置

**输出要求：**
- 创建名为 `CPU测试功能逻辑20250625.md` 的文档
- 使用markdown格式，按照1、2、3、4的顺序组织内容
- 每个功能模块要包含具体的业务逻辑说明
- 重点说明各模块之间的逻辑关系和数据流转
- 对关键的验证规则和错误处理机制要详细描述
- 包含代码片段说明关键实现逻辑
