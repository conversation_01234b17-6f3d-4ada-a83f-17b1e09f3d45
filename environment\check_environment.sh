#!/bin/bash

source "$(dirname "$0")/config.sh"

check_system_requirements() {
    # 检查系统内存
    local total_mem=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$total_mem" -lt 4 ]; then
        echo "警告: 系统内存小于4GB"
    fi
    
    # 检查磁盘空间
    local free_space=$(df -h "${PROJECT_PATH}" | awk 'NR==2 {print $4}')
    if [ "${free_space%G*}" -lt 10 ]; then
        echo "警告: 可用磁盘空间不足10GB"
    fi
    
    # 检查CPU核心数
    local cpu_cores=$(nproc)
    if [ "$cpu_cores" -lt 2 ]; then
        echo "警告: CPU核心数少于2"
    fi
}

check_network() {
    # 检查端口可用性
    if netstat -tln | grep -q ":${SERVER_PORT}"; then
        echo "错误: 端口 ${SERVER_PORT} 已被占用"
        exit 1
    fi
    
    # 检查网络连接
    if ! ping -c 1 ${DB_HOST} >/dev/null 2>&1; then
        echo "警告: 无法连接到数据库主机"
    fi
} 