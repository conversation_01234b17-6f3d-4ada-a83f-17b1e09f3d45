// IO模块Vue版本 - 现代化UI重构版
(function() {
    'use strict';
    
    Logger.log('Loading IO Module Vue App with Modern UI...');
    
    // 检查依赖
    if (!window.Vue || !window.ElementPlus) {
        Logger.error('Vue or ElementPlus not loaded');
        return;
    }

    const { createApp, ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } = Vue;
    const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;
    
    const IOModuleVueApp = {
        setup() {
            // 响应式数据 - 保留原有业务逻辑
            const loading = ref(false);
            const requiresPcbaCheck = ref(true);
            
            // 新增现代化UI状态
            const basicInfoCollapsed = ref(false);
            const showTestLog = ref(true);
            const autoScroll = ref(true);
            const isDarkMode = ref(true); // 默认深色主题
            const testLogs = ref([]);
            const currentTestIndex = ref(-1);
            const testRunning = ref(false);
            const autoSubmitEnabled = ref(false); // 自动提交开关，默认关闭
            
            // 表单数据 - 保留原有结构
            const formData = reactive({
                // 基本信息
                tester: '',
                testTime: '',
                orderNumber: '',
                productionQuantity: '',
                productCode: '',
                productModel: '',
                productStatus: '',
                productSN: '',
                batchNumber: '',
                snByteCount: '',
                remarks: '',
                
                // 版本信息
                autoVersion: '',
                autoDate: '',
                ioVersion: '',
                ioBuildDate: ''
            });
            
            // 测试项目数据 - 3个IO测试项目（现代化UI版本）
            const testItems = ref([
                { name: "Backplane Bus通信", code: "backplane_bus", result: "", category: "通信", icon: "database", duration: 0 },
                { name: "Body I/O输入输出", code: "body_io", result: "", category: "硬件", icon: "zap", duration: 0 },
                { name: "Led灯珠", code: "led_bulb", result: "", category: "硬件", icon: "zap", duration: 0 }
            ]);
            
            const selectAll = ref(false);
            
            // 表单验证规则 - 保留原有规则
            const rules = reactive({
                tester: [{ required: true, message: '请输入测试人员', trigger: 'blur' }],
                orderNumber: [{ required: true, message: '请输入加工单号', trigger: 'blur' }],
                productionQuantity: [{ required: true, message: '请输入生产数量', trigger: 'blur' }],
                productCode: [{ required: true, message: '请输入产品编码', trigger: 'blur' }],
                productModel: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
                productStatus: [{ required: true, message: '请选择产品状态', trigger: 'change' }],
                productSN: [{ required: true, message: '请输入产品SN号', trigger: 'blur' }],
                snByteCount: [{ required: true, message: '请输入产品SN号字节数', trigger: 'blur' }],
                ioVersion: [{ required: true, message: '请输入IO软件版本', trigger: 'blur' }],
                ioBuildDate: [{ required: true, message: '请输入IO构建日期', trigger: 'blur' }]
            });
            
            // 表单引用
            const formRef = ref(null);
            
            // 产品状态选项
            const productStatusOptions = [
                { label: '选择产品状态', value: '', disabled: true },
                { label: '新品', value: 'new' },
                { label: '维修', value: 'used' },
                { label: '返工', value: 'refurbished' }
            ];
            
            // 测试结果选项
            const testResultOptions = [
                { label: '通过', value: '通过' },
                { label: '不通过', value: '不通过' }
            ];
            
            // 计算属性 - 保留原有逻辑
            const isVersionConsistent = computed(() => {
                if (!formData.autoVersion || !formData.autoDate) return true;
                
                const versionMatch = !formData.autoVersion || !formData.ioVersion || 
                                   formData.autoVersion === formData.ioVersion;
                const dateMatch = !formData.autoDate || !formData.ioBuildDate || 
                                formData.autoDate === formData.ioBuildDate;
                
                return versionMatch && dateMatch;
            });
            
            // 测试统计计算属性
            const testStats = computed(() => {
                const passed = testItems.value.filter(item => item.result === '通过').length;
                const failed = testItems.value.filter(item => item.result === '不通过').length;
                const pending = testItems.value.filter(item => !item.result || item.result === '').length;
                return { passed, failed, pending };
            });
            
            // 计算属性 - 对标CPUControllerVue.js
            const passedTests = computed(() => 
                testItems.value.filter(item => item.result === '通过').length
            );
            
            const failedTests = computed(() => 
                testItems.value.filter(item => item.result === '不通过').length
            );
            
            const totalTests = computed(() => testItems.value.length);
            
            const testProgress = computed(() => 
                ((passedTests.value + failedTests.value) / totalTests.value) * 100
            );
            
            const overallResult = computed(() => {
                if (failedTests.value > 0) {
                    return 'NG';
                } else if (passedTests.value === totalTests.value && totalTests.value > 0) {
                    return 'PASS';
                }
                return '';
            });
            
            // BEM架构重构：样式计算属性
            const progressBarStyle = computed(() => ({
                '--progress-width': `${testProgress.value}%`
            }));
            
            // ===== 性能优化：计算属性memoization =====
            

            
            // ===== BEM架构重构：样式计算属性 =====
            
            // 工具栏动态类名计算 - 对标CPUControllerVue
            const toolbarClasses = computed(() => ({
                'glass-effect': true,
                'border-b': true,
                'py-4': true,
                'shadow-xl': true,
                'backdrop-blur-xl': true,
                'io-module__toolbar': true,
                'io-module__toolbar--dark': isDarkMode.value,
                'io-module__toolbar--light': !isDarkMode.value
            }));
            
            // 测试区域动态类名计算
            const testSectionClasses = computed(() => ({
                'io-module__test-section': true,
                'test-section': true
            }));
            
            // 工具函数
            const debounce = (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            };
            
            // ===== 新增现代化UI功能 + 性能优化 =====
            
            // 日志管理配置
            const logConfig = {
                maxLogs: 500,           // 最大日志条数
                batchSize: 50,          // 批量清理数量
                levels: ['system', 'success', 'error', 'warning', 'info'],
                enabledLevels: ref(['system', 'success', 'error', 'warning', 'info'])
            };
            
            // 测试日志功能 - 性能优化版本
            const addTestLog = (level, category, message, details) => {
                // 性能优化：日志条数限制
                if (testLogs.value.length >= logConfig.maxLogs) {
                    testLogs.value = testLogs.value.slice(-logConfig.maxLogs + logConfig.batchSize);
                }
                
                // 级别过滤：只添加启用的日志级别
                if (!logConfig.enabledLevels.value.includes(level)) {
                    return;
                }
                
                const newLog = {
                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                    timestamp: new Date().toLocaleTimeString('zh-CN', {
                        hour12: false,
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        fractionalSecondDigits: 3,
                    }),
                    level,
                    category,
                    message,
                    details,
                };
                testLogs.value.push(newLog);

                // 性能优化：防抖滚动
                if (autoScroll.value) {
                    nextTick(() => {
                        const logContainer = document.getElementById('io-test-log-container');
                        if (logContainer) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
                    });
                }
            };
            
            // 日志级别过滤
            const filteredLogs = computed(() => {
                return testLogs.value.filter(log => 
                    logConfig.enabledLevels.value.includes(log.level)
                );
            });
            
            // 日志导出功能
            const exportLogs = () => {
                const logText = filteredLogs.value.map(log => 
                    `[${log.timestamp}] [${log.category}] ${log.level.toUpperCase()}: ${log.message}${log.details ? ' - ' + log.details : ''}`
                ).join('\n');
                
                const blob = new Blob([logText], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `io-module-test-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                ElMessage.success('测试日志已导出');
            };
            
            // 日志级别切换
            const toggleLogLevel = (level) => {
                const index = logConfig.enabledLevels.value.indexOf(level);
                if (index > -1) {
                    logConfig.enabledLevels.value.splice(index, 1);
                } else {
                    logConfig.enabledLevels.value.push(level);
                }
            };

            // 自动测试功能 - 性能优化版本
            const runAutoTest = async () => {
                testRunning.value = true;
                currentTestIndex.value = 0;
                showTestLog.value = true;

                addTestLog('system', 'SYSTEM', '=== 自动测试开始 ===');
                addTestLog('info', 'INIT', '初始化测试环境...');
                addTestLog('info', 'VERSION', `检查版本信息: ${formData.ioVersion || '未设置'}`);
                addTestLog('success', 'VERSION', '版本检查完成，开始执行测试序列');

                try {
                    for (let i = 0; i < testItems.value.length; i++) {
                        // 检查是否需要停止测试
                        if (!testRunning.value) {
                            addTestLog('warning', 'SYSTEM', '测试被手动停止');
                            return;
                        }

                        currentTestIndex.value = i;
                        const currentTest = testItems.value[i];

                        addTestLog('info', 'TEST', `开始测试: ${currentTest.name}`, `类别: ${currentTest.category}`);
                        testItems.value[i].result = 'testing';

                        // 性能优化：减少不必要的DOM操作
                        const testStartTime = performance.now();
                        
                        // 模拟测试过程 - 配置化失败率
                        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

                        const result = Math.random() > 0.15 ? '通过' : '不通过'; // TODO: 配置化
                        const duration = Math.round(performance.now() - testStartTime);

                        testItems.value[i].result = result;
                        testItems.value[i].duration = duration;

                        if (result === '通过') {
                            addTestLog('success', 'RESULT', `✓ ${currentTest.name} 测试通过`, `耗时: ${duration}ms`);
                        } else {
                            addTestLog('error', 'RESULT', `✗ ${currentTest.name} 测试失败`, `耗时: ${duration}ms`);
                        }
                    }

                    currentTestIndex.value = -1;
                    testRunning.value = false;

                    addTestLog('system', 'SUMMARY', '=== 测试完成 ===');
                    addTestLog('info', 'SUMMARY', `总计: ${totalTests.value} 项, 通过: ${passedTests.value} 项, 失败: ${failedTests.value} 项`);

                    ElMessage.success(`测试完成，通过 ${passedTests.value}/${totalTests.value} 项`);
                    
                } catch (error) {
                    addTestLog('error', 'SYSTEM', `测试异常: ${error.message}`);
                    ElMessage.error('测试过程中发生错误');
                } finally {
                    // 性能优化：批量重新渲染图标
                    nextTick(() => {
                        if (window.lucide) {
                            window.lucide.createIcons();
                        }
                    });
                }
            };

            // 停止测试
            const stopTest = () => {
                testRunning.value = false;
                currentTestIndex.value = -1;
                // 重置正在测试的项目状态为空
                testItems.value.forEach(item => {
                    if (item.result === 'testing') {
                        item.result = '';
                    }
                });
                addTestLog('warning', 'SYSTEM', '=== 测试已手动停止 ===');
                ElMessage.info('测试已停止');
            };
            
            // 主题切换功能
            const toggleTheme = () => {
                isDarkMode.value = !isDarkMode.value;
                applyTheme();
                saveThemePreference();
            };

            const applyTheme = () => {
                const htmlElement = document.documentElement;
                
                if (isDarkMode.value) {
                    htmlElement.setAttribute('data-theme', 'dark');
                } else {
                    htmlElement.removeAttribute('data-theme');
                }
                
                nextTick(() => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                });
            };

            const saveThemePreference = () => {
                localStorage.setItem('io-module-theme', isDarkMode.value ? 'dark' : 'light');
            };

            const loadThemePreference = () => {
                const saved = localStorage.getItem('io-module-theme');
                if (saved) {
                    isDarkMode.value = saved === 'dark';
                }
                applyTheme();
            };
            
            // 测试项目图标样式计算
            const getTestItemIconClass = (result) => {
                switch (result) {
                    case '通过':
                        return 'text-green-500';
                    case '不通过':
                        return 'text-red-500';
                    case 'testing':
                        return 'text-blue-500 animate-pulse';
                    default:
                        return 'theme-text-tertiary';
                }
            };
            
            // 日志级别样式
            const getLogLevelClass = (level) => {
                const classes = {
                    success: 'log-success',
                    error: 'log-error',
                    warning: 'log-warning',
                    info: 'log-info',
                    system: 'log-system'
                };
                return classes[level] || '';
            };
            
            // 清除测试日志
            const clearTestLogs = () => {
                testLogs.value = [];
            };
            
            // ===== 输入处理函数 =====
            
            // 处理加工单号输入 - 大写转换和去除空格
            const handleOrderNumberInput = (value) => {
                if (typeof value === 'string') {
                    formData.orderNumber = value.trim().toUpperCase();
                }
            };
            
            // 处理产品SN号输入 - 大写转换和去除空格
            const handleProductSNInput = (value) => {
                if (typeof value === 'string') {
                    formData.productSN = value.trim().toUpperCase();
                }
            };
            
            // ===== 保留原有业务逻辑函数 =====
            
            // 工单号查询 - 保持原有逻辑
            const queryOrderInfo = async (orderNumber) => {
                if (!orderNumber) {
                    // 清空相关字段
                    formData.productionQuantity = '';
                    formData.productCode = '';
                    formData.productModel = '';
                    formData.batchNumber = '';
                    formData.snByteCount = '';
                    requiresPcbaCheck.value = true;
                    return;
                }

                try {
                    const response = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNumber)}`);
                    const data = await response.json();
                    
                    if (data.success && data.order) {
                        // 检查测试前阶段是否完成
                        if (!data.order.test_stage_completed) {
                            formData.productionQuantity = '';
                            formData.productCode = '';
                            formData.productModel = '';
                            formData.batchNumber = '';
                            formData.snByteCount = '';
                            ElMessage.warning('该工单未完成测试前阶段外观检验');
                            requiresPcbaCheck.value = true;
                            return;
                        }
                        
                        // 自动填充字段
                        formData.productionQuantity = data.order.ord_productionQuantity;
                        formData.productCode = data.order.ord_productCode;
                        formData.productModel = data.order.ord_productModel;
                        formData.batchNumber = data.order.ord_probatch;
                        formData.snByteCount = data.order.ord_snlenth;
                        
                        // 设置PCBA检查标志
                        requiresPcbaCheck.value = data.order.ord_requires_pcba_check !== false;
                        
                        Logger.log(`工单 ${orderNumber}: ${requiresPcbaCheck.value ? '需要PCBA绑定' : '无需PCBA绑定'}`);
                        
                        // 添加日志记录
                        addTestLog('success', 'WORK_ORDER', `工单信息获取成功: ${orderNumber}`, 
                                 `产品: ${data.order.ord_productModel}, 数量: ${data.order.ord_productionQuantity}`);
                        
                        // 查询成功后自动折叠基本信息（仅在首次查询成功时）
                        if (!basicInfoCollapsed.value) {
                            basicInfoCollapsed.value = true;
                        }
                    } else {
                        // 清空相关字段
                        formData.productionQuantity = '';
                        formData.productCode = '';
                        formData.productModel = '';
                        formData.batchNumber = '';
                        formData.snByteCount = '';
                        
                        if (data.message) {
                            ElMessage.warning(data.message);
                            addTestLog('warning', 'WORK_ORDER', `工单查询失败: ${data.message}`);
                        }
                        requiresPcbaCheck.value = true;
                    }
                } catch (error) {
                    Logger.error('Error fetching order details:', error);
                    ElMessage.error('获取工单信息失败');
                    addTestLog('error', 'WORK_ORDER', `获取工单信息失败: ${error.message}`);
                    requiresPcbaCheck.value = true;
                }
            };
            
            // 防抖查询工单信息
            const debouncedQueryOrderInfo = debounce(queryOrderInfo, 500);
            
            // ===== 增强的错误提示系统 =====
            
            // 字段中文名称映射表（精简版）
            const fieldNameMap = {
                tester: '测试人员',
                orderNumber: '加工单号', 
                productionQuantity: '生产数量',
                productCode: '产品编码',
                productModel: '产品型号',
                productStatus: '产品状态',
                productSN: '产品SN号',
                snByteCount: 'SN号字节数',
                ioVersion: 'IO软件版本',
                ioBuildDate: 'IO构建日期'
            };
            
            // 统一的焦点管理函数（增强版）
            const focusToField = (fieldType) => {
                nextTick(() => {
                    let input;
                    switch (fieldType) {
                        case 'sn':
                        case 'productSN':
                            // 尝试多个选择器，支持展开和折叠状态下的SN输入框
                            const snSelectors = [
                                'input[placeholder*="产品SN号"]',
                                'input[placeholder*="请输入产品SN号"]',
                                '.io-module__form-collapsed input[placeholder*="SN"]',
                                '.io-module__form-expanded input[placeholder*="SN"]'
                            ];
                            for (const selector of snSelectors) {
                                input = document.querySelector(selector);
                                if (input && input.offsetParent !== null) break; // 确保元素可见
                            }
                            break;
                        case 'ioVersion':
                            input = document.querySelector('input[placeholder*="IO软件版本"]');
                            break;
                        case 'ioDate':
                        case 'ioBuildDate':
                            input = document.querySelector('input[placeholder*="IO构建日期"]');
                            break;
                        case 'snByteCount':
                            input = document.querySelector('.el-form-item:has(label:contains("SN字节数")) input');
                            break;
                        default:
                            // 通用选择器
                            input = document.querySelector(`input[placeholder*="${fieldNameMap[fieldType]}"]`) ||
                                   document.querySelector(`.el-form-item:has(label:contains("${fieldNameMap[fieldType]}")) input`);
                    }
                    
                    if (input) {
                        input.focus();
                        input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        const fieldName = fieldNameMap[fieldType] || fieldType;
                        addTestLog('info', 'FOCUS', `焦点已设置到${fieldName}输入框`);
                    }
                });
            };
            
            // 简化的表单验证错误处理函数
            const handleValidationErrors = (errors) => {
                if (!errors || Object.keys(errors).length === 0) return;
                
                const firstErrorField = Object.keys(errors)[0];
                const firstError = errors[firstErrorField][0];
                const fieldName = fieldNameMap[firstErrorField] || firstErrorField;
                
                // 显示具体错误信息
                const errorMessage = `${fieldName}：${firstError.message}`;
                ElMessage({
                    message: errorMessage,
                    type: 'warning',
                    duration: 4000,
                    showClose: true
                });
                
                addTestLog('warning', 'VALIDATE', `表单验证失败 - ${errorMessage}`);
                
                // 自动聚焦到错误字段
                focusToField(firstErrorField);
                
                return true; // 返回true表示处理了错误
            };
            
            // 自动获取版本信息 - 保持原有逻辑
            const autoFetchVersionInfo = async (productSN) => {
                const productStatus = formData.productStatus;
                
                if (!['new', 'used', 'refurbished'].includes(productStatus)) {
                    Logger.log('产品状态不支持自动获取版本信息，跳过');
                    return;
                }

                try {
                    const response = await fetch(`/api/io-modulevue/get-version-info?product_sn=${encodeURIComponent(productSN)}&product_status=${encodeURIComponent(productStatus)}`);
                    const data = await response.json();
                    
                    if (data.success && data.data) {
                        if (data.data.software_version) {
                            formData.autoVersion = data.data.software_version;
                        }
                        
                        if (data.data.build_time) {
                            formData.autoDate = data.data.build_time;
                        }
                        
                        if (data.data.software_version || data.data.build_time) {
                            Logger.log(`版本信息自动获取成功 (${productStatus}模式):`, data.message);
                            addTestLog('success', 'VERSION', `版本信息自动获取成功`, 
                                     `版本: ${data.data.software_version || 'N/A'}, 构建: ${data.data.build_time || 'N/A'}`);
                        } else {
                            Logger.log(`版本信息为空 (${productStatus}模式):`, data.message);
                            addTestLog('info', 'VERSION', `版本信息为空`, data.message);
                        }
                    } else {
                        Logger.log('未找到相关版本信息或查询失败:', data.message);
                        addTestLog('warning', 'VERSION', `版本信息查询失败`, data.message);
                    }
                } catch (error) {
                    Logger.error('自动获取版本信息失败:', error);
                    addTestLog('error', 'VERSION', `版本信息获取异常: ${error.message}`);
                }
                
                // 版本信息获取完成后，触发自动提交检查
                if (autoSubmitEnabled.value) {
                    setTimeout(() => {
                        triggerAutoSubmitIfEnabled(productSN);
                    }, 500);
                }
            };
            
            // 新增：添加一个辅助函数，用于验证SN的订货号是否正确
            const validateSnOrderNumber = async (sn, productCode) => {
                if (!productCode) {
                    ElMessage.error('产品编码缺失，无法验证SN');
                    addTestLog('error', 'SN_CHECK', 'SN验证失败: 产品编码缺失');
                    return false;
                }
                try {
                    const response = await fetch(`/api/products/validate-sn-order?sn=${encodeURIComponent(sn)}&productCode=${encodeURIComponent(productCode)}`);
                    const result = await response.json();
                    
                    if (!result.success) {
                        ElMessage.error(result.message || 'SN与工单订货号不匹配！');
                        addTestLog('error', 'SN_CHECK', `SN验证失败: ${result.message || 'SN与工单订货号不匹配！'}`);
                        return false;
                    }
                    addTestLog('success', 'SN_CHECK', `SN订货号验证通过: ${sn}`);
                    return true;
                } catch (error) {
                    Logger.error('验证 SN 订货号失败:', error);
                    ElMessage.error('验证SN失败，请稍后重试');
                    addTestLog('error', 'SN_CHECK', `SN验证异常: ${error.message}`);
                    return false;
                }
            };
            
            // SN检查 - 保持原有逻辑
            const checkSN = async (sn) => {
                if (!sn) return;
                
                // 新增步骤1：验证SN的订货号是否正确
                const isOrderNumberValid = await validateSnOrderNumber(sn, formData.productCode);
                if (!isOrderNumberValid) {
                    formData.productSN = ''; // 清空输入框
                    focusToField('sn');    // 重新获取焦点
                    return; // 如果订货号不匹配，则中断后续所有操作
                }
                
                // 如果当前工单不需要PCBA检查，则跳过检查
                if (!requiresPcbaCheck.value) {
                    Logger.log('当前工单无需PCBA绑定检查，允许继续测试');
                    addTestLog('info', 'SN_CHECK', `跳过PCBA检查: ${sn}`, '当前工单无需PCBA绑定');
                    await autoFetchVersionInfo(sn);
                    return;
                }

                try {
                    const response = await fetch(`/api/io-modulevue/check-sn?sn=${encodeURIComponent(sn)}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        if (!data.exists) {
                            ElMessage.warning('该SN号未绑定PCBA！');
                            addTestLog('warning', 'SN_CHECK', `SN未绑定PCBA: ${sn}`);
                            formData.productSN = '';
                            // 验证失败时聚焦到SN输入框
                            focusToField('sn');
                            return;
                        } else {
                            addTestLog('success', 'SN_CHECK', `SN验证通过: ${sn}`, 'PCBA绑定正常');
                            await autoFetchVersionInfo(sn);
                        }
                    } else {
                        Logger.error('检查SN号失败:', data.message);
                        addTestLog('error', 'SN_CHECK', `SN检查失败: ${data.message}`);
                        // API调用失败时也聚焦到SN输入框
                        focusToField('sn');
                    }
                } catch (error) {
                    Logger.error('检查SN号时发生错误:', error);
                    ElMessage.error('检查SN号失败，请检查网络连接');
                    addTestLog('error', 'SN_CHECK', `SN检查异常: ${error.message}`);
                    // 网络错误时也聚焦到SN输入框
                    focusToField('sn');
                }
            };
            
            // ===== 自动提交功能 - 对标CouplerVue.js =====
            
            // 执行自动校验
            const performAutoValidation = () => {
                const validationResults = {
                    allValid: false,
                    errors: []
                };

                // 1. SN有效性校验
                const productSN = String(formData.productSN || '').trim();
                const snByteCount = parseInt(formData.snByteCount);

                if (!productSN) {
                    validationResults.errors.push('产品SN号为空');
                } else if (isNaN(snByteCount)) {
                    validationResults.errors.push('SN号字节数无效');
                } else if (productSN.length !== snByteCount) {
                    validationResults.errors.push(`SN号长度不符合要求: 期望${snByteCount}字节, 实际${productSN.length}字节`);
                }

                // 2. 版本比对校验
                if (!isVersionConsistent.value) {
                    validationResults.errors.push('版本信息不一致');
                }

                // 3. 表单基本校验
                if (!formData.tester || !formData.orderNumber || !formData.productCode ||
                    !formData.productModel || !formData.ioVersion || !formData.ioBuildDate) {
                    validationResults.errors.push('必填字段未完整填写');
                }

                // 最终判断
                if (validationResults.errors.length === 0) {
                    validationResults.allValid = true;
                }

                return validationResults;
            };

            // 触发自动提交流程
            const triggerAutoSubmitIfEnabled = async (sn) => {
                if (!autoSubmitEnabled.value) {
                    return;
                }

                addTestLog('info', 'AUTO_SUBMIT', `开始自动提交校验流程: SN=${sn}`);

                // 执行自动校验
                const validation = performAutoValidation();

                if (!validation.allValid) {
                    const firstError = validation.errors[0] || '未知校验错误';
                    addTestLog('warning', 'AUTO_SUBMIT', `自动提交校验失败，转为手动模式: ${validation.errors.join('; ')}`);
                    ElMessage({
                        message: `自动提交失败：${firstError}。请手动检查并提交。`,
                        type: 'warning',
                        duration: 5000,
                        showClose: true,
                    });
                    return;
                }

                addTestLog('success', 'AUTO_SUBMIT', '所有校验通过，开始自动提交流程');

                try {
                    // 步骤A: 自动执行"全通过"操作
                    addTestLog('info', 'AUTO_SUBMIT', '执行自动全通过操作');
                    setAllPass();

                    // 等待一小段时间确保UI更新
                    await new Promise(resolve => setTimeout(resolve, 600));

                    // 步骤B: 自动执行"提交测试"操作
                    addTestLog('info', 'AUTO_SUBMIT', '执行自动提交测试操作');
                    await submitForm();

                    addTestLog('success', 'AUTO_SUBMIT', '自动提交流程完成');

                } catch (error) {
                    addTestLog('error', 'AUTO_SUBMIT', `自动提交过程中发生错误: ${error.message}`);
                    ElMessage.error('自动提交失败，请手动操作');
                }
            };
            
            // ===== UI交互函数 =====
            
            // 基本信息折叠切换
            const toggleBasicInfo = () => {
                basicInfoCollapsed.value = !basicInfoCollapsed.value;
                nextTick(() => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                });
            };
            
            // 全选处理
            const handleSelectAll = (value) => {
                testItems.value.forEach(item => {
                    item.selected = value;
                });
            };
            
            // 个别选择处理
            const handleItemSelect = () => {
                selectAll.value = testItems.value.every(item => item.selected);
            };
            
            // 测试结果变化处理
            const handleTestResultChange = (item, newResult) => {
                item.result = newResult;
                addTestLog('info', 'MANUAL', `手动设置测试结果: ${item.name} = ${newResult}`);
            };
            
            // 设置测试结果
            const setTestResult = (index, result) => {
                testItems.value[index].result = result;
                if (testItems.value[index].duration) {
                    delete testItems.value[index].duration;
                }
                nextTick(() => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                });
                const testName = testItems.value[index].name;
                const statusText = result === '通过' ? '通过' : '失败';
                ElMessage.success(`${testName} 已标记为${statusText}`);
            };
            
            const clearAllResults = () => {
                testItems.value.forEach(item => {
                    item.result = '';
                    delete item.duration;
                });
                nextTick(() => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                });
                ElMessage.success('已清除所有测试结果');
            };
            
            const setAllPass = () => {
                testItems.value.forEach(item => {
                    item.result = '通过';
                });
                nextTick(() => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                });
            };
            
            // ===== 表单提交函数 - 保持原有逻辑 =====
            
            const submitForm = async () => {
                // 增强的表单验证 - 显示具体错误信息
                try {
                    await formRef.value.validate();
                    addTestLog('success', 'VALIDATE', '表单验证通过');
                } catch (validationErrors) {
                    if (validationErrors && typeof validationErrors === 'object') {
                        const handled = handleValidationErrors(validationErrors);
                        if (handled) return;
                    }
                    // 兜底处理
                    ElMessage.warning('请完善必填信息');
                    addTestLog('warning', 'VALIDATE', '表单验证失败，请检查必填字段');
                    return;
                }
                
                // 验证版本信息一致性 - 增强错误提示
                if (!isVersionConsistent.value) {
                    if (formData.autoVersion && formData.ioVersion && formData.autoVersion !== formData.ioVersion) {
                        const errorMsg = `IO软件版本不匹配：自动获取版本"${formData.autoVersion}"与手动输入版本"${formData.ioVersion}"不一致`;
                        ElMessage({
                            message: errorMsg,
                            type: 'error',
                            duration: 5000,
                            showClose: true
                        });
                        addTestLog('error', 'VALIDATION', '版本验证失败：软件版本不一致');
                        focusToField('ioVersion');
                        return;
                    }
                    
                    if (formData.autoDate && formData.ioBuildDate && formData.autoDate !== formData.ioBuildDate) {
                        const errorMsg = `IO构建日期不匹配：自动获取日期"${formData.autoDate}"与手动输入日期"${formData.ioBuildDate}"不一致`;
                        ElMessage({
                            message: errorMsg,
                            type: 'error',
                            duration: 5000,
                            showClose: true
                        });
                        addTestLog('error', 'VALIDATION', '版本验证失败：构建日期不一致');
                        focusToField('ioBuildDate');
                        return;
                    }
                }
                
                // 验证产品SN号长度 - 增强错误提示
                const snByteCount = parseInt(formData.snByteCount);
                if (isNaN(snByteCount)) {
                    ElMessage({
                        message: 'SN号字节数格式错误：请输入有效的数字',
                        type: 'error',
                        duration: 4000,
                        showClose: true
                    });
                    addTestLog('error', 'VALIDATION', 'SN字节数格式无效');
                    focusToField('snByteCount');
                    return;
                }
                
                const productSN = String(formData.productSN || '');
                if (productSN.length !== snByteCount) {
                    const errorMsg = `SN号长度不符合要求：当前SN号"${productSN}"长度为${productSN.length}个字符，要求长度为${snByteCount}个字符`;
                    ElMessage({
                        message: errorMsg,
                        type: 'error',
                        duration: 5000,
                        showClose: true
                    });
                    addTestLog('error', 'VALIDATION', `SN长度验证失败：期望${snByteCount}字节，实际${productSN.length}字节`);
                    focusToField('productSN');
                    return;
                }
                
                // 收集表单数据
                const submitData = {
                    // 基本信息
                    tester: formData.tester,
                    test_time: formData.testTime,
                    work_order: String(formData.orderNumber || '').trim(),
                    work_qty: String(formData.productionQuantity || '').trim(),
                    pro_model: String(formData.productModel || '').trim(),
                    pro_code: String(formData.productCode || '').trim(),
                    pro_sn: String(formData.productSN || '').trim(),
                    pro_batch: formData.batchNumber || 'N/A',
                    remarks: formData.remarks || 'N/A',
                    
                    // 版本信息
                    io_version: formData.ioVersion || 'N/A',
                    io_build_date: formData.ioBuildDate || 'N/A',
                    
                    // 测试结果 (采用默认通过策略：只有明确标记为"不通过"的才是失败)
                    backplane: testItems.value[0].result === '不通过' ? 2 : 1,
                    body_io: testItems.value[1].result === '不通过' ? 2 : 1,
                    led_bulb: testItems.value[2].result === '不通过' ? 2 : 1
                };
                
                // 产品状态映射
                const productStatusMap = {
                    'new': 1,
                    'used': 2,
                    'refurbished': 3
                };
                
                submitData.pro_status = productStatusMap[formData.productStatus];
                if (!submitData.pro_status) {
                    ElMessage.warning('请选择产品状态！');
                    return;
                }
                
                // 设置维修和返工次数
                submitData.maintenance = submitData.pro_status === 2 ? 1 : 0;
                submitData.rework = submitData.pro_status === 3 ? 1 : 0;
                
                Logger.log('提交的数据：', submitData);
                addTestLog('info', 'SUBMIT', '开始提交测试数据...', JSON.stringify(submitData, null, 2));
                
                try {
                    loading.value = true;
                    
                    const response = await fetch('/api/io-modulevue/submit-test', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(submitData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        ElMessage.success('测试信息提交成功！');
                        addTestLog('success', 'SUBMIT', '测试数据提交成功', result.message || '数据已保存到数据库');
                        
                        // 保存需要保留的字段值
                        const savedValues = {
                            tester: formData.tester,
                            orderNumber: formData.orderNumber,
                            productionQuantity: formData.productionQuantity,
                            productCode: formData.productCode,
                            productModel: formData.productModel,
                            productStatus: formData.productStatus,
                            batchNumber: formData.batchNumber,
                            snByteCount: formData.snByteCount,
                            remarks: formData.remarks,
                            ioVersion: formData.ioVersion,
                            ioBuildDate: formData.ioBuildDate
                        };
                        
                        // 重置表单
                        await formRef.value.resetFields();
                        
                        // 恢复保存的值
                        Object.assign(formData, savedValues);
                        
                        // 重置测试项目
                        testItems.value.forEach(item => {
                            item.result = '';
                            item.selected = false;
                            item.duration = 0;
                        });
                        selectAll.value = false;
                        
                        // 清空特定字段
                        formData.productSN = '';
                        formData.autoVersion = '';
                        formData.autoDate = '';
                        
                        addTestLog('info', 'RESET', '表单已重置，准备下一个测试');
                        
                        // 重新渲染图标确保状态正确显示
                        nextTick(() => {
                            if (window.lucide) {
                                window.lucide.createIcons();
                            }
                        });
                        
                        // 提交成功后聚焦到产品SN输入框（基本信息卡片状态保持不变）
                        focusToField('sn');
                        
                    } else {
                        ElMessage.error(result.message);
                        addTestLog('error', 'SUBMIT', `提交失败: ${result.message}`);
                    }
                } catch (error) {
                    ElMessage.error('请检查网络连接');
                    Logger.error('提交错误：', error);
                    addTestLog('error', 'SUBMIT', `提交异常: ${error.message}`);
                } finally {
                    loading.value = false;
                }
            };
            
            // 生命周期钩子
            onMounted(() => {
                Logger.log('IO Module Vue App mounted');
                
                // 加载主题偏好
                loadThemePreference();
                
                // 初始化主题和图标
                nextTick(() => {
                    applyTheme();
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                });
                
                // 设置默认测试时间
                const now = new Date();
                formData.testTime = now.toISOString().slice(0, 10);
                
                // 获取当前用户
                getCurrentUser();
                
                // 添加初始化日志
                addTestLog('system', 'INIT', 'IO模块测试系统初始化完成');
                addTestLog('info', 'SYSTEM', `当前主题: ${isDarkMode.value ? '深色' : '浅色'}模式`);
                
                // 监听系统主题变化
                if (window.matchMedia) {
                    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
                    mediaQuery.addEventListener('change', (e) => {
                        const saved = localStorage.getItem('io-module-theme');
                        if (!saved) {
                            isDarkMode.value = e.matches;
                            applyTheme();
                        }
                    });
                }
            });
            
            // 获取当前用户
            const getCurrentUser = async () => {
                try {
                    const response = await fetch('/api/io-modulevue/get-current-user');
                    const data = await response.json();
                    
                    if (data.success && data.username) {
                        formData.tester = data.username;
                        addTestLog('success', 'USER', `当前用户: ${data.username}`);
                    }
                } catch (error) {
                    Logger.error('获取当前用户失败:', error);
                    addTestLog('warning', 'USER', '获取当前用户失败，请手动输入');
                }
            };
            
            // 监听工单号变化
            watch(() => formData.orderNumber, (newVal) => {
                if (newVal) {
                    debouncedQueryOrderInfo(newVal);
                }
            });
            
            // 监听产品状态变化 - 重新获取版本信息
            watch(() => formData.productStatus, () => {
                // 清空自动获取的版本信息
                formData.autoVersion = '';
                formData.autoDate = '';
                
                // 如果有产品SN号，重新获取版本信息
                if (['new', 'used', 'refurbished'].includes(formData.productStatus) && formData.productSN) {
                    autoFetchVersionInfo(formData.productSN);
                }
            });
            
            // 返回模板需要的数据和方法
            return {
                // 响应式数据
                loading,
                requiresPcbaCheck,
                basicInfoCollapsed,
                showTestLog,
                autoScroll,
                isDarkMode,
                testLogs,
                currentTestIndex,
                testRunning,
                autoSubmitEnabled,
                formData,
                testItems,
                selectAll,
                rules,
                formRef,
                productStatusOptions,
                testResultOptions,
                
                // 计算属性
                isVersionConsistent,
                testStats,
                passedTests,
                failedTests,
                totalTests,
                testProgress,
                overallResult,
                progressBarStyle,
                toolbarClasses,
                testSectionClasses,
                
                // 现代化UI方法
                addTestLog,
                runAutoTest,
                stopTest,
                toggleTheme,
                getTestItemIconClass,
                getLogLevelClass,
                clearTestLogs,
                toggleBasicInfo,
                setTestResult,
                clearAllResults,
                setAllPass,
                
                // 新增性能优化和日志管理
                filteredLogs,
                exportLogs,
                toggleLogLevel,
                logConfig,
                
                // 原有业务方法
                queryOrderInfo,
                autoFetchVersionInfo,
                checkSN,
                focusToField,
                handleSelectAll,
                handleItemSelect,
                handleTestResultChange,
                submitForm,
                getCurrentUser,
                
                // 增强的错误处理功能
                handleValidationErrors,
                fieldNameMap,
                
                // 自动提交功能
                performAutoValidation,
                triggerAutoSubmitIfEnabled,
                
                // 输入处理功能
                handleOrderNumberInput,
                handleProductSNInput
            };
        },
        
        template: `
        <div class="io-module__main gradient-bg">
            <!-- 顶部工具栏 -->
            <div :class="toolbarClasses">
                <div class="io-module__toolbar-container">
                    <div class="io-module__toolbar-left">
                        <!-- Logo和标题 -->
                        <div class="io-module__toolbar-brand">
                            <div class="relative">
                                <div class="io-module__icon io-module__icon--orange rounded-xl shadow-lg">
                                    <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
                            </div>
                            <div>
                                <h1 class="io-module__title-main theme-text-primary">IO模块测试系统</h1>
                                <p class="io-module__text-status" :class="isDarkMode ? 'text-orange-300' : 'text-orange-600'">Professional IO Testing Platform v2.1</p>
                            </div>
                        </div>
                        
                        <!-- 分隔线 -->
                        <div class="h-8 w-px bg-gray-300"></div>
                        
                        <!-- 版本信息状态 -->
                        <div class="io-module__toolbar-status">
                            <div class="relative">
                                <div :class="['w-3 h-3 rounded-full shadow-lg status-indicator', isVersionConsistent ? 'bg-green-500 status-connected' : 'bg-yellow-500 status-disconnected']"></div>
                            </div>
                            <div>
                                <span class="text-sm font-medium theme-text-primary">
                                    {{ isVersionConsistent ? '版本一致' : '版本检查' }}
                                </span>
                                <p class="text-xs" :class="isDarkMode ? 'text-gray-300' : 'text-gray-600'">
                                    {{ formData.ioVersion ? '版本已设置' : '请设置版本' }}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="io-module__toolbar-actions">
                        <!-- 主题切换按钮 -->
                        <el-button 
                            @click="toggleTheme"
                            class="theme-toggle-btn"
                            :title="isDarkMode ? '切换到浅色主题' : '切换到深色主题'"
                        >
                            <i :data-lucide="isDarkMode ? 'moon' : 'sun'" class="w-5 h-5"></i>
                        </el-button>
                        
                        <!-- 自动提交开关 -->
                        <div class="flex items-center gap-2 px-3 py-1 rounded-lg bg-opacity-20 backdrop-blur-sm"
                             :class="isDarkMode ? 'bg-gray-700' : 'bg-white'"
                             style="border: 1px solid var(--io-card-border);">
                            <span class="text-sm font-medium" :class="isDarkMode ? 'text-gray-300' : 'text-gray-700'">
                                自动提交
                            </span>
                            <el-switch
                                v-model="autoSubmitEnabled"
                                :active-color="isDarkMode ? '#00d4ff' : '#2563eb'"
                                :inactive-color="isDarkMode ? '#4b5563' : '#d1d5db'"
                                size="small"
                                @change="(val) => addTestLog('info', 'AUTO_SUBMIT', '自动提交功能已' + (val ? '开启' : '关闭'))"
                            />
                        </div>
                        
                        <el-button 
                            v-if="!testRunning"
                            type="success"
                            @click="runAutoTest"
                            class="shadow-lg"
                        >
                            <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                            自动测试
                        </el-button>
                        
                        <el-button 
                            v-else
                            type="danger"
                            @click="stopTest"
                            class="shadow-lg"
                        >
                            <i data-lucide="square" class="w-4 h-4 mr-2"></i>
                            停止测试
                        </el-button>
                        
                        <el-button 
                            type="primary"
                            :loading="loading"
                            @click="submitForm"
                            class="shadow-lg"
                        >
                            <i data-lucide="sparkles" class="w-4 h-4 mr-2"></i>
                            提交测试
                        </el-button>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="io-module__main-content main-content-area">
                <!-- 左侧表单区域 -->
                <div class="io-module__form-section form-section">
                    <el-form 
                        ref="formRef"
                        :model="formData" 
                        :rules="rules" 
                        @submit.prevent="submitForm">
                        <div class="io-module__space-y">
                            <!-- 基本信息卡片 -->
                            <div class="io-module__card theme-card glass-effect card-hover">
                                <div class="p-6 cursor-pointer" @click="toggleBasicInfo">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                                <i data-lucide="shield" class="w-4 h-4 text-white"></i>
                                            </div>
                                            <span class="io-module__title-section theme-text-primary">基本信息</span>
                                        </div>
                                        <i :data-lucide="basicInfoCollapsed ? 'chevron-down' : 'chevron-up'" class="w-5 h-5 text-blue-300"></i>
                                    </div>
                                </div>
                                
                                <!-- 基本信息表单 -->
                                <div class="io-module__form-content">
                                    <!-- 折叠状态：显示关键信息 -->
                                    <div v-if="basicInfoCollapsed" class="io-module__form-collapsed">
                                        <!-- 加工单号、产品型号 -->
                                        <div class="io-module__grid-2">
                                            <div class="io-module__field-group">
                                                <label class="io-module__field-label">加工单号</label>
                                                <el-input
                                                    v-model="formData.orderNumber"
                                                    @input="handleOrderNumberInput"
                                                    placeholder="输入工单号自动查询"
                                                    class="h-10"
                                                ></el-input>
                                            </div>
                                            <div class="io-module__field-group">
                                                <label class="io-module__field-label">产品型号</label>
                                                <el-input
                                                    v-model="formData.productModel"
                                                    class="h-10"
                                                ></el-input>
                                            </div>
                                        </div>
                                        <!-- 产品SN号、SN字节数 -->
                                        <div class="io-module__grid-2">
                                            <div class="io-module__field-group">
                                                <label class="io-module__field-label">产品SN号 *</label>
                                                <el-input
                                                    v-model="formData.productSN"
                                                    @input="handleProductSNInput"
                                                    @keyup.enter.prevent="checkSN(formData.productSN)"
                                                    @blur="checkSN(formData.productSN)"
                                                    placeholder="请输入产品SN号"
                                                    class="h-10"
                                                ></el-input>
                                            </div>
                                            <div class="io-module__field-group">
                                                <label class="io-module__field-label">SN字节数 *</label>
                                                <el-input
                                                    v-model="formData.snByteCount"
                                                    type="number"
                                                    class="h-10"
                                                ></el-input>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 展开状态：显示所有字段 -->
                                    <transition name="collapse">
                                        <div v-show="!basicInfoCollapsed" class="io-module__form-expanded">
                                            <!-- 第一行：测试人员、测试时间、批次号 -->
                                            <div class="io-module__grid-3">
                                                <el-form-item label="测试人员" prop="tester">
                                                    <el-input v-model="formData.tester"></el-input>
                                                </el-form-item>
                                                <el-form-item label="测试时间">
                                                    <el-input 
                                                        v-model="formData.testTime" 
                                                        type="date">
                                                    </el-input>
                                                </el-form-item>
                                                <el-form-item label="批次号">
                                                    <el-input v-model="formData.batchNumber"></el-input>
                                                </el-form-item>
                                            </div>
                                            
                                            <!-- 第二行：产品编码、生产数量、SN字节数 -->
                                            <div class="io-module__grid-3">
                                                <el-form-item label="产品编码" prop="productCode">
                                                    <el-input v-model="formData.productCode"></el-input>
                                                </el-form-item>
                                                <el-form-item label="生产数量" prop="productionQuantity">
                                                    <el-input v-model="formData.productionQuantity"></el-input>
                                                </el-form-item>
                                                <el-form-item label="SN字节数" prop="snByteCount">
                                                    <el-input v-model="formData.snByteCount" type="number"></el-input>
                                                </el-form-item>
                                            </div>
                                            
                                            <!-- 第三行：加工单号、产品型号 -->
                                            <div class="io-module__grid-2">
                                                <el-form-item label="加工单号" prop="orderNumber">
                                                    <el-input 
                                                        v-model="formData.orderNumber"
                                                        @input="handleOrderNumberInput"
                                                        placeholder="输入工单号自动查询">
                                                    </el-input>
                                                </el-form-item>
                                                <el-form-item label="产品型号" prop="productModel">
                                                    <el-input v-model="formData.productModel"></el-input>
                                                </el-form-item>
                                            </div>
                                            
                                            <!-- 第四行：产品SN号、产品状态 -->
                                            <div class="io-module__grid-2">
                                                <el-form-item label="产品SN号" prop="productSN">
                                                    <el-input 
                                                        v-model="formData.productSN"
                                                        @input="handleProductSNInput"
                                                        @keyup.enter.prevent="checkSN(formData.productSN)"
                                                        @blur="checkSN(formData.productSN)"
                                                        placeholder="请输入产品SN号">
                                                    </el-input>
                                                </el-form-item>
                                                <el-form-item label="产品状态" prop="productStatus">
                                                    <el-select v-model="formData.productStatus" class="w-full" placeholder="请选择产品状态">
                                                        <el-option 
                                                            v-for="option in productStatusOptions" 
                                                            :key="option.value"
                                                            :label="option.label" 
                                                            :value="option.value"
                                                            :disabled="option.disabled">
                                                        </el-option>
                                                    </el-select>
                                                </el-form-item>
                                            </div>
                                            
                                            <!-- 第五行：备注 -->
                                            <el-form-item label="备注">
                                                <el-input 
                                                    v-model="formData.remarks" 
                                                    type="textarea" 
                                                    :rows="2" 
                                                    placeholder="请输入备注信息">
                                                </el-input>
                                            </el-form-item>
                                        </div>
                                    </transition>
                                </div>
                            </div>
                            
                            <!-- 版本信息卡片 -->
                            <div class="io-module__card theme-card glass-effect card-hover">
                                <div class="io-module__card-content">
                                    <div class="io-module__card-header">
                                        <div class="io-module__card-title">
                                            <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                                <i data-lucide="git-branch" class="w-4 h-4 text-white"></i>
                                            </div>
                                            <span class="io-module__title-section theme-text-primary">版本信息</span>
                                        </div>
                                        <div v-if="!isVersionConsistent" class="text-yellow-600 text-sm">
                                            <i data-lucide="alert-triangle" class="w-4 h-4 inline mr-1"></i>
                                            版本不一致
                                        </div>
                                    </div>

                                    <div class="io-module__device-content">
                                        <!-- 自动获取版本信息 -->
                                        <div class="io-module__grid-2">
                                            <div class="io-module__field-group">
                                                <label class="io-module__field-label">自动获取版本</label>
                                                <el-input 
                                                    v-model="formData.autoVersion" 
                                                    readonly 
                                                    placeholder="SN输入后自动获取"
                                                    class="bg-gray-50">
                                                </el-input>
                                            </div>
                                            <div class="io-module__field-group">
                                                <label class="io-module__field-label">自动获取日期</label>
                                                <el-input 
                                                    v-model="formData.autoDate" 
                                                    readonly 
                                                    placeholder="SN输入后自动获取"
                                                    class="bg-gray-50">
                                                </el-input>
                                            </div>
                                        </div>

                                        <!-- 手动输入版本信息 -->
                                        <div class="io-module__grid-2">
                                            <el-form-item label="IO软件版本" prop="ioVersion" class="io-top-label">
                                                <el-input 
                                                    v-model="formData.ioVersion" 
                                                    placeholder="请输入IO软件版本">
                                                </el-input>
                                            </el-form-item>
                                            <el-form-item label="IO构建日期" prop="ioBuildDate" class="io-top-label">
                                                <el-input 
                                                    v-model="formData.ioBuildDate" 
                                                    placeholder="请输入IO构建日期">
                                                </el-input>
                                            </el-form-item>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-form>
                </div>
                
                <!-- 右侧测试区域 -->
                <div :class="testSectionClasses">
                    <div class="space-y-6">
                        <!-- 测试进度卡片 -->
                        <div class="io-module__card theme-card glass-effect card-hover">
                            <div class="io-module__card-content">
                                <div class="io-module__progress-header">
                                    <div class="io-module__card-title">
                                        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                                            <i data-lucide="target" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="io-module__title-section theme-text-primary">测试进度</span>
                                    </div>
                                    <div class="io-module__progress-tabs">
                                        <el-button
                                            size="small"
                                            :type="!showTestLog ? 'primary' : ''"
                                            @click="showTestLog = false"
                                            class="h-8 text-xs"
                                        >
                                            进度视图
                                        </el-button>
                                        <el-button
                                            size="small"
                                            :type="showTestLog ? 'primary' : ''"
                                            @click="showTestLog = true"
                                            class="h-8 text-xs"
                                        >
                                            测试日志
                                        </el-button>
                                    </div>
                                </div>

                                <!-- 进度条 -->
                                <div class="mb-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm theme-text-secondary">总体进度</span>
                                        <span class="text-sm font-mono font-semibold theme-text-primary">{{ Math.round(testProgress) }}%</span>
                                    </div>
                                    <div class="custom-progress">
                                        <div class="custom-progress-bar io-module__progress-bar" :style="progressBarStyle"></div>
                                    </div>
                                </div>

                                <!-- 进度视图 -->
                                <div v-if="!showTestLog">
                                    <div class="io-module__progress-stats">
                                        <div class="io-module__stat-card io-module__stat-card--success">
                                            <div class="io-module__stat-value text-green-600">{{ passedTests }}</div>
                                            <div class="io-module__stat-label text-green-600">通过</div>
                                        </div>
                                        <div class="io-module__stat-card io-module__stat-card--danger">
                                            <div class="io-module__stat-value text-red-600">{{ failedTests }}</div>
                                            <div class="io-module__stat-label text-red-600">失败</div>
                                        </div>
                                        <div class="io-module__stat-card io-module__stat-card--neutral">
                                            <div class="io-module__stat-value text-gray-600">{{ totalTests - passedTests - failedTests }}</div>
                                            <div class="io-module__stat-label text-gray-600">待测</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 测试日志视图 - 水平布局 -->
                                <div v-else class="io-module__log-layout">
                                    <!-- 左侧统计信息 (20%) - 4行垂直布局 -->
                                    <div class="io-module__log-stats">
                                        <!-- 通过 -->
                                        <div class="io-module__compact-stat io-module__compact-stat--success">
                                            <span>通过</span>
                                            <span>{{ passedTests }}</span>
                                        </div>
                                        
                                        <!-- 失败 -->
                                        <div class="io-module__compact-stat io-module__compact-stat--danger">
                                            <span>失败</span>
                                            <span>{{ failedTests }}</span>
                                        </div>
                                        
                                        <!-- 待测 -->
                                        <div class="io-module__compact-stat io-module__compact-stat--neutral">
                                            <span>待测</span>
                                            <span>{{ totalTests - passedTests - failedTests }}</span>
                                        </div>
                                        
                                        <!-- 结果 -->
                                        <div :class="[
                                            'io-module__compact-stat',
                                            overallResult === 'PASS' ? 'io-module__compact-stat--success' :
                                            overallResult === 'NG' ? 'io-module__compact-stat--danger' :
                                            'io-module__compact-stat--neutral'
                                        ]">
                                            <span>结果</span>
                                            <span>{{ overallResult || '--' }}</span>
                                        </div>
                                    </div>
                                    
                                    <!-- 右侧测试日志 (80%) -->
                                    <div class="io-module__log-content">
                                        <div class="io-module__log-controls">
                                            <div class="io-module__log-indicators">
                                                <span class="text-gray-500">测试日志</span>
                                                <!-- 日志级别过滤按钮 -->
                                                <div 
                                                    v-for="level in logConfig.levels" 
                                                    :key="level"
                                                    class="io-module__log-indicator cursor-pointer"
                                                    @click="toggleLogLevel(level)"
                                                    :class="{ 'opacity-50': !logConfig.enabledLevels.value.includes(level) }"
                                                >
                                                    <div :class="['io-module__log-dot', \`io-module__log-dot--\${level}\`]"></div>
                                                    <span :class="[
                                                        level === 'success' ? 'text-green-600' :
                                                        level === 'error' ? 'text-red-600' :
                                                        level === 'warning' ? 'text-yellow-600' :
                                                        level === 'info' ? 'text-blue-600' :
                                                        'text-purple-600'
                                                    ]">{{ level.toUpperCase() }}</span>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <el-button
                                                    size="small"
                                                    @click="autoScroll = !autoScroll"
                                                    class="h-6 px-2 text-xs"
                                                >
                                                    {{ autoScroll ? '🔒 自动滚动' : '🔓 手动滚动' }}
                                                </el-button>
                                                <el-button 
                                                    size="small" 
                                                    @click="exportLogs" 
                                                    class="h-6 px-2 text-xs"
                                                    :disabled="filteredLogs.length === 0"
                                                >
                                                    导出
                                                </el-button>
                                                <el-button size="small" @click="clearTestLogs" class="h-6 px-2 text-xs">
                                                    清空
                                                </el-button>
                                            </div>
                                        </div>

                                        <div id="io-test-log-container" class="flex-1 log-container rounded-lg p-3 overflow-y-auto text-xs">
                                            <div v-if="filteredLogs.length === 0" class="flex items-center justify-center h-full text-gray-500">
                                                <div class="text-center">
                                                    <div class="text-2xl mb-2">📋</div>
                                                    <div>{{ testLogs.length === 0 ? '点击"自动测试"开始记录日志' : '当前过滤条件下无日志' }}</div>
                                                </div>
                                            </div>
                                            <div v-else class="space-y-1">
                                                <div
                                                    v-for="log in filteredLogs"
                                                    :key="log.id"
                                                    class="flex items-start space-x-2"
                                                >
                                                    <span class="text-gray-400 shrink-0">{{ log.timestamp }}</span>
                                                    <span :class="['shrink-0 font-semibold', getLogLevelClass(log.level)]">
                                                        [{{ log.category }}]
                                                    </span>
                                                    <div class="flex-1 min-w-0">
                                                        <div :class="getLogLevelClass(log.level)">{{ log.message }}</div>
                                                        <div v-if="log.details" class="text-gray-400 text-xs mt-1 ml-2">
                                                            └─ {{ log.details }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 测试项目卡片 -->
                        <div class="io-module__card theme-card glass-effect card-hover">
                            <div class="io-module__card-content">
                                <div class="io-module__progress-header">
                                    <div class="io-module__card-title">
                                        <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-lg flex items-center justify-center">
                                            <i data-lucide="layers" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="io-module__title-section theme-text-primary">测试项目</span>
                                    </div>
                                    <div class="io-module__test-actions">
                                        <el-button size="small" @click="clearAllResults" :disabled="testRunning" class="h-8">
                                            <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                                            清除
                                        </el-button>
                                        <el-button size="small" type="success" @click="setAllPass" :disabled="testRunning" class="h-8">
                                            <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                            全通过
                                        </el-button>
                                    </div>
                                </div>

                                <div class="io-module__test-items">
                                    <div
                                        v-for="(item, index) in testItems"
                                        :key="item.name"
                                        :class="[
                                            'io-module__test-item test-item-bg',
                                            currentTestIndex === index ? 'io-module__test-item--active active shadow-lg' : ''
                                        ]"
                                    >
                                        <div class="io-module__test-item-info">
                                            <div :class="[
                                                'io-module__test-item-icon',
                                                getTestItemIconClass(item.result)
                                            ]">
                                                <i :data-lucide="item.icon" class="w-4 h-4"></i>
                                            </div>
                                            <div class="io-module__test-item-details">
                                                <div class="io-module__test-item-name">{{ item.name }}</div>
                                                <div class="io-module__test-item-meta">
                                                    <span>{{ item.category }}</span>
                                                    <span v-if="item.duration">• {{ item.duration }}ms</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="io-module__test-item-actions">
                                            <!-- Testing State -->
                                            <div v-if="item.result === 'testing'" class="io-module__test-item-status">
                                                <i data-lucide="loader-2" class="w-4 h-4 animate-spin text-blue-600"></i>
                                                <span class="text-xs text-blue-600 font-medium">测试中...</span>
                                            </div>

                                            <!-- Pass State -->
                                            <el-tag v-else-if="item.result === '通过'" type="success" size="small">
                                                <i data-lucide="check-circle"></i>
                                                <span>通过</span>
                                            </el-tag>

                                            <!-- Fail State -->
                                            <el-tag v-else-if="item.result === '不通过'" type="danger" size="small">
                                                <i data-lucide="x-circle"></i>
                                                <span>失败</span>
                                            </el-tag>

                                            <!-- Pending State -->
                                            <el-tag v-else type="info" size="small">
                                                <i data-lucide="clock" style="vertical-align: middle;"></i>
                                                <span style="vertical-align: middle;">待测</span>
                                            </el-tag>

                                            <!-- Action Buttons -->
                                            <div v-if="!testRunning && item.result !== 'testing'" class="io-module__test-item-buttons">
                                                <el-button
                                                    size="small"
                                                    :type="item.result === '通过' ? 'success' : ''"
                                                    @click="setTestResult(index, '通过')"
                                                    class="h-7 px-3 text-xs"
                                                >
                                                    通过
                                                </el-button>
                                                <el-button
                                                    size="small"
                                                    :type="item.result === '不通过' ? 'danger' : ''"
                                                    @click="setTestResult(index, '不通过')"
                                                    class="h-7 px-3 text-xs"
                                                >
                                                    失败
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `
    };
    
    // 挂载应用
    const mountApp = () => {
        try {
            const app = createApp(IOModuleVueApp);
            app.use(ElementPlus);
            app.mount('#io-module-vue-app-container');
            Logger.log('IO Module Vue App mounted successfully');
        } catch (error) {
            Logger.error('Failed to mount IO Module Vue App:', error);
        }
    };
    
    // 确保DOM加载完成后挂载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', mountApp);
    } else {
        mountApp();
    }
})();