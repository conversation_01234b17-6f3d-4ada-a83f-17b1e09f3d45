from flask import Blueprint, jsonify, request
from database.db_manager import DatabaseManager
from sqlalchemy import text
import logging

logger = logging.getLogger(__name__)
fault_query_bp = Blueprint('fault_query', __name__)

@fault_query_bp.route('/search', methods=['GET'])
def search_fault():
    try:
        # 获取查询参数
        order_number = request.args.get('orderNumber')
        sn = request.args.get('sn')
        product_code = request.args.get('productCode')
        fault_type = request.args.get('faultType')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        
        # 修改条件判断，确保每个参数都被正确检查
        has_query_params = any([
            order_number and order_number.strip(),
            sn and sn.strip(),
            product_code and product_code.strip(),
            fault_type and fault_type.strip(),
            start_date and start_date.strip(),
            end_date and end_date.strip()
        ])
        
        if not has_query_params:
            return jsonify({
                'success': False,
                'message': '请至少提供一个查询条件'
            }), 400

        # 构建查询条件
        conditions = []
        params = {}
        
        if order_number and order_number.strip():
            conditions.append("work_order LIKE :order_number")
            params['order_number'] = f'%{order_number.strip()}%'
            
        if sn and sn.strip():
            conditions.append("pro_sn LIKE :sn")
            params['sn'] = f'%{sn.strip()}%'
            
        if product_code and product_code.strip():
            conditions.append("pro_code = :product_code")
            params['product_code'] = product_code.strip()
            
        if fault_type:
            # 根据故障组类型构建条件
            fault_type_conditions = {
                '基础功能': [
                    "burn_err = 2",
                    "power_err = 2"
                ],
                '通信接口': [
                    "backplane_err = 2",
                    "rs485_1_err = 2",
                    "rs485_2_err = 2",
                    "rs232_err = 2",
                    "canbus_err = 2",
                    "ethercat_err = 2"
                ],
                'IO接口': [
                    "body_io_err = 2",
                    "net_port_err = 2",
                    "usb_drive_err = 2",
                    "sd_slot_err = 2",
                    "debug_port_err = 2"
                ],
                '指示与控制': [
                    "led_tube_err = 2",
                    "led_bulb_err = 2",
                    "dip_switch_err = 2",
                    "reset_btn_err = 2"
                ]
            }
            
            if fault_type in fault_type_conditions:
                conditions.append(f"({' OR '.join(fault_type_conditions[fault_type])})")

        if start_date:
            conditions.append("DATE(test_time) >= DATE(:start_date)")
            params['start_date'] = start_date
            
        if end_date:
            conditions.append("DATE(test_time) <= DATE(:end_date)")
            params['end_date'] = end_date

        # 构建完整的SQL查询
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        query = text(f"""
            SELECT 
                work_order,
                pro_model,
                pro_code,
                work_qty,
                pro_status,
                maintenance,
                rework,
                test_time,
                tester,
                pro_sn,
                pro_batch,
                remarks,
                burn_err,
                power_err,
                backplane_err,
                body_io_err,
                led_tube_err,
                led_bulb_err,
                net_port_err,
                rs485_1_err,
                rs485_2_err,
                rs232_err,
                canbus_err,
                ethercat_err,
                usb_drive_err,
                sd_slot_err,
                debug_port_err,
                dip_switch_err,
                reset_btn_err,
                other_err
            FROM faultEntry_table
            WHERE {where_clause}
            ORDER BY test_time DESC
        """)

        db = DatabaseManager()
        with db.get_session() as session:
            results = session.execute(query, params)
            
            data = []
            for row in results:
                data.append({
                    'orderNumber': row.work_order,
                    'productModel': row.pro_model,
                    'productCode': row.pro_code,
                    'workQty': row.work_qty,
                    'productStatus': {
                        1: '新品',
                        2: '维修品',
                        3: '返工品'
                    }.get(row.pro_status, '未知'),
                    'maintenanceCount': row.maintenance,
                    'reworkCount': row.rework,
                    'testTime': row.test_time.strftime('%Y-%m-%d %H:%M:%S') if row.test_time else None,
                    'tester': row.tester,
                    # 详情信息
                    'serialNumber': row.pro_sn,
                    'productBatch': row.pro_batch,
                    'remarks': row.remarks,
                    'faultDetails': {
                        'burn': row.burn_err == 2,
                        'power': row.power_err == 2,
                        'backplane': row.backplane_err == 2,
                        'bodyIO': row.body_io_err == 2,
                        'ledTube': row.led_tube_err == 2,
                        'ledBulb': row.led_bulb_err == 2,
                        'netPort': row.net_port_err == 2,
                        'rs485_1': row.rs485_1_err == 2,
                        'rs485_2': row.rs485_2_err == 2,
                        'rs232': row.rs232_err == 2,
                        'canbus': row.canbus_err == 2,
                        'ethercat': row.ethercat_err == 2,
                        'usbDrive': row.usb_drive_err == 2,
                        'sdSlot': row.sd_slot_err == 2,
                        'debugPort': row.debug_port_err == 2,
                        'dipSwitch': row.dip_switch_err == 2,
                        'resetBtn': row.reset_btn_err == 2,
                        'other': row.other_err if row.other_err != 'N/A' else None
                    }
                })

            return jsonify({
                'success': True,
                'data': data,
                'message': f'找到 {len(data)} 条记录'
            })

    except Exception as e:
        logger.error("故障品查询失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500 