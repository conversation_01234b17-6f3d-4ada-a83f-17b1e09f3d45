# 自检过程功能简要分析 (20250626)

本文档旨在深入分析"自检过程"页面的核心功能、业务逻辑、数据流以及错误处理机制，为后续的功能迭代和问题排查提供清晰的参考。

---

## 1. 核心功能概述

"自检过程"页面是质量检验系统的核心环节，它承载了从加载工单、录入受检产品、执行检验、上传附件到提交结果的完整业务闭环。页面被设计为三个主要检验阶段（**组装前、测试前、包装前**），每个阶段又包含三种检验角色（**首检、自检、IPQC**），形成了一个 `3x3` 的检验矩阵。

其核心功能点包括：
- **工单加载**：输入工单号，系统自动从工单管理和质检系统拉取详细信息。
- **SN号录入与验证**：在检验前，必须扫描或输入产品的SN号。系统会进行严格验证，包括SN号与订货号是否匹配、SN是否重复等。
- **动态检验项目**：根据产品类型，动态加载对应的检验项目清单。
- **分步提交与最终提交**：支持对部分产品进行"部分提交"，也支持对所有产品完成后的"最终提交"，后者会锁定当前检验环节。
- **进度跟踪**：实时更新各阶段的完成状态，并以可视化的方式（如进度条、状态标记）展示。
- **附件管理**：支持上传图片、文档等附件作为检验凭证。

---

## 2. 核心模块与文件

页面的功能由以下三个核心文件协同实现：

- **`static/page_js_css/SelfInspection.js`**
  - **角色**：**页面总控制器**。
  - **职责**：负责整个页面的UI渲染、全局状态管理（`inspectionData`对象）、事件监听、页面初始化以及对其他模块（如SN扫描）的调度。它定义了工单加载、检验提交流程的主函数。

- **`static/page_js_css/ScanSN.js`**
  - **角色**：**SN号扫描与处理模块**。
  - **职责**：负责动态生成SN扫描界面，处理用户输入的SN号，调用后端API进行验证，并在前端维护一个已扫描产品列表。该模块被`SelfInspection.js`在需要时调用。

- **`routes/quality_inspection.py`**
  - **角色**：**后端业务逻辑核心**。
  - **职责**：提供了所有与质检相关的API接口。负责处理数据库的增删改查，实现工单、产品、检验记录、附件的业务逻辑，并确保数据的一致性和安全性。

---

## 3. 核心业务流程与数据流

页面的整体功能围绕着清晰的数据流展开，主要分为以下三大步骤：

### 步骤一：加载工单信息

此流程在用户输入工单号并失焦后触发。

1.  **用户输入**：在工单号输入框输入工单号。
2.  **前端触发**：`SelfInspection.js` 中的 `loadWorkOrderData` 函数被调用。
3.  **API调用 (1)**：前端首先向 **工单管理系统** 发起请求 (`GET /api/sn-print-record/get-work-order-details`)，获取工单的基本信息，如**产品型号、产品编码（订货号）、生产数量**等。
4.  **前端状态更新 (1)**：`loadWorkOrderData` 函数将获取到的基本信息存入全局的 `inspectionData.workOrderDetails` 对象中。**这是我们刚刚修复的关键点，确保了`productCode`被保存**。
5.  **API调用 (2)**：接着，前端向 **质检系统** 发起请求 (`GET /api/quality-inspection/work-order/{orderNo}`)，获取该工单在质检系统中的详细状态，包括已检验的产品列表、各阶段的提交记录、已上传的附件等。
6.  **前端状态更新 (2)**：`processWorkOrderData` 函数被调用，它解析来自质检系统的响应，并将数据填充到 `inspectionData` 对象的各个部分（如`pageState`, `products`等）。
7.  **UI渲染**：页面根据 `inspectionData` 中的数据，渲染出各个阶段的选项卡、显示已完成的检验状态、锁定已提交的环节。

### 步骤二：SN号扫描与验证

当用户切换到任一检验环节的选项卡时，SN扫描界面出现。

1.  **用户输入**：在SN输入框中输入SN号，按回车或点击"确认"。
2.  **前端触发**：`ScanSN.js` 中的 `processScanedSN` 函数被调用。
3.  **订货号验证 (前置)**：函数首先从 `inspectionData.workOrderDetails` 中获取产品编码，然后调用 `validateSnForOrderNumber` 函数。
4.  **API调用 (验证)**：`validateSnForOrderNumber` 向后端发起请求 (`GET /api/products/validate-sn-order`)，验证SN号与产品编码是否匹配。如果失败，流程中断。
5.  **SN全面检查**：如果订货号验证通过，`processScanedSN` 继续调用 `checkSerialNumber` (`GET /api/quality-inspection/check-serial-number`)，检查SN是否已存在于当前或其他工单。
6.  **产品关联**：根据检查结果，调用 `addProductToWorkOrder` 或 `addReworkProduct` (`POST`请求) 将SN号与当前工单在数据库中进行关联。
7.  **前端UI更新**：`addToScannedList` 和 `updateScannedProductList` 函数被调用，将验证通过的SN号动态添加到页面的"已扫描产品"列表中，并更新计数。

### 步骤三：执行检验与提交

用户扫描完SN并点击"开始检验"后进入此流程。

1.  **加载检验项**：`proceedToInspection` 函数被调用，它会根据当前产品类型和检验阶段，通过 `loadInspectionItems` 从后端获取对应的检验项目列表并渲染到页面上。
2.  **用户操作**：用户勾选检验项目，上传必要的附件。
3.  **前端触发**：用户点击"提交"按钮，`submitInspection` 函数被调用。
4.  **API调用**：`saveInspectionRecords` 函数整理好数据后，向后端发起请求 (`POST /api/quality-inspection/records`)，将检验结果、产品ID列表、提交类型（部分/最终）等信息发送出去。
5.  **后端处理**：`quality_inspection.py` 中的 `save_inspection_records` 函数接收数据，将检验结果写入数据库。如果是"最终提交"，则调用 `check_work_order_completion` 检查并更新整个工单或阶段的状态。
6.  **UI刷新**：提交成功后，前端会刷新页面状态，锁定已完成的环节，并清空输入为下一次检验做准备。

---

## 4. 常见错误提示与原因分析

| 错误提示 | 触发位置 | 根本原因 |
| :--- | :--- | :--- |
| **无法验证SN号 - 未能获取到当前工单的产品编码** | SN扫描 | `loadWorkOrderData`未能从工单系统获取并保存产品编码(`productCode`)到`inspectionData`对象。 |
| **SN号与订货号不匹配** | SN扫描 | 用户输入的SN号，根据其内部规则解析出的订货号，与当前工单的订货号不一致。 |
| **工单不存在** | 工单号输入 | 用户输入的工单号在后台的工单管理系统中查询不到。 |
| **SN号已存在于当前工单** | SN扫描 | 用户重复扫描了已经为当前工单添加过的SN号。前端已有去重，此提示主要来自后端保护。 |
| **SN号已存在于其他工单** | SN扫描 | 在一个非返工的普通工单中，输入了一个已经被其他工单使用过的SN号。 |
| **阶段...的...检验已最终提交，无法重复提交** | 提交检验 | 该检验环节（如"组装前-首检"）已经执行过"最终提交"，系统已将其锁定，防止数据被篡改。 |
| **产品...已被其他人检验完成** | 提交检验 | 在"自检"环节，该产品已被一个用户提交过。为防止数据冲突，系统不允许其他用户重复提交。 |

---

## 5. 总结

"自检过程"页面在架构上实现了前后端分离、模块化设计。前端通过一个全局`inspectionData`对象管理页面状态，实现了不同模块间的解耦和数据共享。后端的API设计遵循RESTful风格，职责清晰。整个业务流程形成了一个从数据加载、用户操作、数据验证到持久化存储的完整闭环，配合明确的错误提示，保证了系统的健壮性和用户体验。 