/* 所有固件页面样式 - 采用BEM命名规范 */

/* 主容器 */
.all-firmware {
    min-height: 100vh;
}

/* 自定义标题样式 */
.all-firmware .firmware-page__header {
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.all-firmware .firmware-page__header:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.all-firmware .firmware-page__title {
    margin: 0;
    color: #303133;
    font-size: 20px;
    font-weight: 600;
    position: relative;
    padding-left: 16px;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.all-firmware .firmware-page__title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 22px;
    background: linear-gradient(to bottom, #6777ef, #5566d4);
    border-radius: 3px;
}

/* 搜索栏特定样式 */
.all-firmware__search-bar {
    /* 继承通用样式，这里可以添加页面特定的覆盖 */
}

.all-firmware__search-input {
    /* 继承通用样式 */
}

.all-firmware__action-buttons {
    /* 继承通用样式 */
}

.all-firmware__tip {
    color: #5555ff; 
    font-size: 12px; 
    margin-left: 15px; 
    line-height: 32px;
    transition: all 0.3s ease;
}

.all-firmware__tip:hover {
    color: #4444ff;
}

/* 状态样式 - 页面特定的BEM修饰符 */
.all-firmware__status--active { 
    color: #67C23A; 
    font-weight: bold;
}

.all-firmware__status--pending { 
    color: #E6A23C; 
    font-weight: bold;
}

.all-firmware__status--rejected { 
    color: #F56C6C; 
    font-weight: bold;
}

.all-firmware__status--obsolete { 
    color: #F56C6C; 
    font-weight: bold;
}

/* 版本号链接样式（与工单号链接保持一致） */
.all-firmware__version-link {
    font-weight: 600;
    text-decoration: none;
    color: #409eff;
    white-space: nowrap;
}

.all-firmware__version-link:hover {
    text-decoration: underline;
}

/* 产品标签样式 */
.all-firmware__product-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.all-firmware__product-tag {
    /* Element Plus的tag组件会自动应用样式 */
    margin-right: 4px;
    margin-bottom: 4px;
}

/* 表格特定样式 */
.all-firmware__table {
    /* 这里可以添加表格的特定样式覆盖 */
}

.all-firmware__table .el-table__row:hover {
    background-color: #f5f7fa;
}

/* 操作按钮样式 */
.all-firmware__action-button {
    margin-right: 8px;
}

.all-firmware__action-button:last-child {
    margin-right: 0;
}

/* 对话框相关样式 */
.all-firmware__dialog {
    /* 对话框特定样式 */
}

.all-firmware__form-row {
    display: flex;
    gap: 20px;
}

.all-firmware__form-row .el-form-item {
    flex: 1;
}

.all-firmware__upload-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.all-firmware__upload-tip {
    color: #999;
    font-size: 12px;
    line-height: 1;
    margin-left: 10px;
    white-space: nowrap;
}

/* 版本历史对话框样式 */
.firmware-modal.firmware-history-modal .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
}

.firmware-history {
    padding: 0;
}

.firmware-history__timeline .el-timeline {
    padding-left: 0;
}

.firmware-history__timeline .el-timeline-item__tail {
    width: 2px;
    background: #e4e7ed;
}

.firmware-history__timeline .el-timeline-item__node {
    width: 16px;
    height: 16px;
    left: -2px;
    box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.2);
    transition: all 0.3s ease;
}

.firmware-history__timeline .el-timeline-item__node--danger {
    box-shadow: 0 0 0 4px rgba(245, 108, 108, 0.2);
}

.firmware-history__timeline .el-timeline-item:hover .el-timeline-item__node {
    transform: scale(1.2);
}

.firmware-history__item {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    overflow: hidden;
    animation: fadeIn 0.5s ease forwards;
    opacity: 0;
    transform: translateY(10px);
    animation-delay: calc(var(--item-index, 0) * 0.1s);
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.firmware-history__item:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-3px);
}

.firmware-history__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(to right, #fafbfc, #f8f9fa);
    border-bottom: 1px solid #ebeef5;
}

.firmware-history__timestamp {
    font-size: 14px;
    color: #909399;
    font-weight: 500;
}

.firmware-history__status {
    font-size: 14px;
    padding: 4px 10px;
    border-radius: 16px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.firmware-history__status--active {
    background-color: rgba(103, 194, 58, 0.1);
    color: #67C23A;
    border: 1px solid rgba(103, 194, 58, 0.2);
}

.firmware-history__status--obsolete {
    background-color: rgba(245, 108, 108, 0.1);
    color: #F56C6C;
    border: 1px solid rgba(245, 108, 108, 0.2);
}

.firmware-history__status--rejected {
    background-color: rgba(144, 147, 153, 0.1);
    color: #909399;
    border: 1px solid rgba(144, 147, 153, 0.2);
}

.firmware-history__status--pending {
    background-color: rgba(230, 162, 60, 0.1);
    color: #E6A23C;
    border: 1px solid rgba(230, 162, 60, 0.2);
}

.firmware-history__content {
    display: flex;
    padding: 0;
}

.firmware-history__left {
    flex: 1;
    padding: 16px;
    border-right: 1px solid #ebeef5;
    min-width: 320px;
}

.firmware-history__right {
    flex: 1.5;
    padding: 16px;
    background-color: rgba(249, 250, 252, 0.4);
}

.firmware-history__info-item {
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.6;
}

.firmware-history__info-label {
    display: inline-block;
    font-weight: 600;
    color: #2c3e50;
    min-width: 105px;
}

.firmware-history__info-value {
    color: #606266;
}

.firmware-history__section {
    margin-bottom: 14px;
}

.firmware-history__section-title {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    padding-left: 10px;
    position: relative;
}

.firmware-history__section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 14px;
    background: linear-gradient(to bottom, #6777ef, #5566d4);
    border-radius: 1.5px;
}

.firmware-history__section-content {
    font-size: 14px;
    color: #606266;
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #ebeef5;
    white-space: pre-line;
}

.firmware-history__divider {
    height: 1px;
    background-color: #ebeef5;
    margin: 16px 0;
}

.firmware-history__empty {
    text-align: center;
    padding: 60px 20px;
    color: #909399;
}

.firmware-history__empty-icon {
    font-size: 48px;
    color: #dcdfe6;
    margin-bottom: 16px;
}

.firmware-history__empty-text {
    font-size: 14px;
    line-height: 1.6;
}

.firmware-history__footer {
    display: flex;
    justify-content: flex-end;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-top: 1px solid #ebeef5;
}

/* 版本历史中的产品标签样式 */
.firmware-history__product-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.firmware-history__product-tag {
    margin: 2px;
    transition: all 0.2s ease;
    white-space: normal;
    max-width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    height: auto;
    line-height: 1.5;
    padding: 5px 10px;
}

.firmware-history__product-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.firmware-history__empty-text {
    color: #909399;
    font-style: italic;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .firmware-history__content {
        flex-direction: column;
    }
    
    .firmware-history__left {
        border-right: none;
        border-bottom: 1px solid #ebeef5;
        min-width: auto;
    }
}

/* 加载状态 */
.all-firmware__loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

/* 空状态 */
.all-firmware__empty {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.all-firmware__empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 16px;
}

.all-firmware__empty-text {
    font-size: 14px;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .all-firmware__form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .all-firmware__version-content {
        flex-direction: column;
    }
    
    .all-firmware__version-right {
        margin-left: 0;
        border-left: none;
        border-top: 1px solid #eee;
        padding-left: 5px;
        padding-top: 15px;
        margin-top: 15px;
    }
}

@media (max-width: 768px) {
    .all-firmware__action-buttons {
        flex-direction: column;
        gap: 8px;
    }
    
    .all-firmware__tip {
        margin-left: 0;
        margin-top: 8px;
        text-align: center;
    }
    
    .all-firmware__upload-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .all-firmware__upload-tip {
        margin-left: 0;
        margin-top: 8px;
    }
} 