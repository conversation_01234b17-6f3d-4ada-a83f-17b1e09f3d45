from flask import Blueprint, jsonify, request, current_app
from jwt import decode
from database.db_manager import DatabaseManager
from sqlalchemy import text
import logging
from datetime import datetime
from models.laser_standard import LaserStandard

# 设置日志记录器
logger = logging.getLogger(__name__)

sn_print_record_bp = Blueprint('sn_print_record_controller', __name__)

def get_current_username_from_token():
    """辅助函数：从JWT Token中获取当前用户名"""
    token = request.cookies.get('token')
    if token:
        try:
            payload = decode(token, current_app.config['JWT_SECRET'], algorithms=["HS256"])
            return payload.get('username')
        except Exception as e:
            logger.error(f"解码JWT Token时出错: {e}")
            return None
    return None

@sn_print_record_bp.route('/get-current-user', methods=['GET'])
def get_current_user_api():
    """API端点：获取当前登录用户的用户名"""
    username = get_current_username_from_token()
    if username:
        return jsonify({'success': True, 'username': username})
    else:
        return jsonify({'success': False, 'message': '无法获取用户信息，请重新登录'}), 401

@sn_print_record_bp.route('/get-work-order-details', methods=['GET'])
def get_work_order_details_api():
    """API端点：根据加工单号获取工单详情"""
    process_order_no = request.args.get('processOrder')

    if not process_order_no:
        return jsonify({'success': False, 'message': '加工单号不能为空'}), 400

    db = DatabaseManager()
    try:
        with db.get_session() as session:
            query = text("""
                SELECT 
                    dbord_productModel, 
                    dbord_productCode, 
                    dbord_productionQuantity,
                    dbord_snlenth,
                    orderproduct_type_id
                FROM workordermanagement 
                WHERE dbord_processOrderNo = :process_order_no
            """)
            result = session.execute(query, {'process_order_no': process_order_no}).fetchone()

            if result:
                # 将数据库字段名映射到前端期望的字段名
                order_details = {
                    'ord_productModel': result.dbord_productModel,
                    'ord_productCode': result.dbord_productCode,
                    'ord_productionQuantity': result.dbord_productionQuantity,
                    'ord_snlenth': result.dbord_snlenth,  # 新增：SN号位数
                    'ord_product_type_id': result.orderproduct_type_id  # 新增：产品类型ID
                }
                return jsonify({'success': True, 'order': order_details})
            else:
                return jsonify({'success': False, 'message': f'未找到加工单号为 {process_order_no} 的工单信息'})
    except Exception as e:
        logger.error(f"查询工单详情时出错 (加工单号: {process_order_no}): {e}")
        return jsonify({'success': False, 'message': '查询工单信息时发生服务器错误'}), 500 

@sn_print_record_bp.route('/save-single-verified-sn', methods=['POST'])
def save_single_verified_sn():
    """API端点：保存单个已验证的SN记录"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['processOrder', 'productModel', 'productCode', 
                          'quantity', 'operator', 'sn']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False, 
                    'message': f'缺少必要字段: {field}'
                }), 400
        
        db = DatabaseManager()
        try:
            with db.get_session() as session:
                # 检查是否已存在相同记录
                existing = session.query(LaserStandard).filter_by(
                    laser_order_no=data['processOrder'],
                    laser_sn=data['sn']
                ).first()
                
                if existing:
                    return jsonify({
                        'success': False,
                        'message': f'SN {data["sn"]} 已存在于数据库中'
                    }), 409
                
                # 创建并保存记录
                laser_record = LaserStandard(
                    laser_order_no=data['processOrder'],
                    laser_product_model=data['productModel'],
                    laser_product_code=data['productCode'],
                    laser_sn=data['sn'],
                    laser_sn_status=1,  # 正常状态
                    laser_quantity=data['quantity'],
                    laser_operator=data['operator'],
                    laser_operation_time=datetime.now()
                )
                session.add(laser_record)
                session.commit()
                
                return jsonify({
                    'success': True,
                    'message': f'SN {data["sn"]} 已成功保存到数据库'
                })
                
        except Exception as e:
            session.rollback() if 'session' in locals() else None
            logger.error(f"保存单个SN记录时出错: {e}")
            return jsonify({
                'success': False,
                'message': f'保存SN记录时发生数据库错误: {str(e)}'
            }), 500
            
    except Exception as e:
        logger.error(f"处理保存单个SN记录请求时出错: {e}")
        return jsonify({
            'success': False,
            'message': f'处理请求时出错: {str(e)}'
        }), 500

@sn_print_record_bp.route('/check-sn-exists', methods=['GET'])
def check_sn_exists():
    """API端点：检查SN是否已存在于数据库中
    
    用于前端提前检查SN是否重复
    """
    sn = request.args.get('sn')
    if not sn:
        return jsonify({
            'success': False,
            'message': 'SN参数不能为空'
        }), 400
        
    db = DatabaseManager()
    try:
        with db.get_session() as session:
            exists = session.query(LaserStandard).filter_by(laser_sn=sn).first() is not None
            return jsonify({
                'success': True,
                'exists': exists
            })
    except Exception as e:
        logger.error(f"检查SN存在性时出错: {e}")
        return jsonify({
            'success': False,
            'message': f'检查SN时出错: {str(e)}'
        }), 500 

@sn_print_record_bp.route('/get-next-sequence', methods=['GET'])
def get_next_sequence_api():
    """获取下一个可用的流水号
    
    基于产品编码和周期(年份+周数)查询当前最大流水号并返回下一个可用值
    """
    product_code = request.args.get('productCode')
    year = request.args.get('year')  # 年份后两位，如 "25"
    week = request.args.get('week')  # 周数，如 "17"
    order_number = request.args.get('orderNumber')

    if not all([product_code, year, week, order_number]):
        return jsonify({
            'success': False, 
            'message': '缺少必要参数 (productCode, year, week, orderNumber)'
        }), 400

    if not (len(year) == 2 and year.isdigit() and len(week) == 2 and week.isdigit()):
        return jsonify({
            'success': False, 
            'message': '年份或周期格式不正确'
        }), 400

    db = DatabaseManager()
    try:
        with db.get_session() as session:
            # 构造SN前缀：订货号 + 年份(2位) + 周期(2位)
            sn_prefix = f"{order_number}{year}{week}"
            
            # 查询指定产品编码且SN以指定前缀开头的记录
            query = text("""
                SELECT laser_sn
                FROM laser_standard
                WHERE laser_product_code = :product_code 
                AND laser_sn LIKE :sn_prefix_pattern
                ORDER BY laser_sn DESC
                LIMIT 1
            """)
            
            result = session.execute(query, {
                'product_code': product_code,
                'sn_prefix_pattern': f"{sn_prefix}%"
            }).fetchone()

            if result:
                # 从完整SN中提取流水号部分
                current_sn = result[0]
                sequence_str = current_sn[len(sn_prefix):]
                try:
                    current_sequence = int(sequence_str)
                    next_sequence = current_sequence + 1
                except ValueError:
                    logger.error(f"无法从SN {current_sn} 中解析流水号部分")
                    next_sequence = 1
            else:
                # 未找到记录，从1开始
                next_sequence = 1

            return jsonify({
                'success': True, 
                'nextSequence': next_sequence,
                'snPrefix': sn_prefix  # 返回前缀供前端验证
            })

    except Exception as e:
        logger.error(f"获取下一流水号时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取下一流水号时出错: {str(e)}'
        }), 500 