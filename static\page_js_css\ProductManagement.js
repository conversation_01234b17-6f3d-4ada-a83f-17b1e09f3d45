// 在文件开头添加依赖检查
(function checkDependencies() {
    if (typeof Swal === 'undefined') {
        Logger.error('SweetAlert2 未加载');
        return;
    }
})();

// 将变量声明移动到 window 对象上
if (typeof window.productManagementVars === 'undefined') {
    window.productManagementVars = {
        editingProductId: null
    };
}

// 产品管理页面的状态管理
window.productManagementState = {
    isActive: false,
    currentPage: 1,
    pageSize: 10,
    pageSizeOptions: [10, 20, 50, 100],
    products: [],
    totalCount: 0,
    sortField: 'Basic_creationTime',
    sortOrder: 'desc',
    searchKeyword: ''
};

// 初始化产品管理页面
window.initProductManagement = function() {
    window.productManagementState.isActive = true;
    const productManagementContent = document.getElementById('product-management-content');
    
    if (!productManagementContent) {
        Logger.error('无法找到 product-management-content 元素');
        return;
    }

    const searchSectionHtml = `
        <section class="search-section card">
            <div class="search-form">
                <div class="search-input-group">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="productSearch" placeholder="请输入产品编码搜索">
                    </div>
                    <div class="search-buttons">
                        <button class="btn btn-search" onclick="searchProducts()">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <button class="btn btn-reset" onclick="resetSearch()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </section>
    `;

    productManagementContent.innerHTML = `
        <div id="product-management-page" class="container">
            <header class="page-header">
                <h1>产品信息管理</h1>
            </header>

            ${searchSectionHtml}

            <section class="operation-section card">
                <div class="button-group">
                    <button class="btn btn-primary" onclick="openAddProductModal()">
                        <i class="fas fa-plus"></i> 添加产品
                    </button>
                    <button class="btn btn-danger" onclick="deleteSelectedProducts()">
                        <i class="fas fa-trash"></i> 删除产品
                    </button>
                    <button class="btn btn-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> 导出Excel
                    </button>
                    <label class="btn btn-info">
                        <i class="fas fa-file-import"></i> 导入Excel
                        <input type="file" id="importExcel" accept=".xlsx,.xls" style="display:none" onchange="importFromExcel(event)">
                    </label>
                    <span style="margin-left: 12px; color: #1890ff; font-size: 13px;">（双击产品编码进入产品编辑状态）</span>
                </div>
            </section>

            <section class="table-section card">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th onclick="sortBy('Basic_productCode')">产品编码 <i class="fas fa-sort"></i></th>
                            <th onclick="sortBy('Basic_productName')">产品型号 <i class="fas fa-sort"></i></th>
                            <th onclick="sortBy('Basic_productType')">产品类型 <i class="fas fa-sort"></i></th>
                            <th onclick="sortBy('Basic_productSeries')">品牌 <i class="fas fa-sort"></i></th>
                            <th onclick="sortBy('Basic_orderNumber')">订货号 <i class="fas fa-sort"></i></th>
                            <th onclick="sortBy('snLength')">SN长度 <i class="fas fa-sort"></i></th>
                            <th onclick="sortBy('Basic_creator')">创建人员 <i class="fas fa-sort"></i></th>
                            <th onclick="sortBy('Basic_creationTime')">创建时间 <i class="fas fa-sort"></i></th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <!-- 数据将通过 JavaScript 动态插入 -->
                    </tbody>
                </table>
                <div class="table-footer">
                    <div class="table-length">
                        <div class="page-size-selector">
                            每页
                            <select id="pageSizeSelect" onchange="changePageSize()">
                                ${window.productManagementState.pageSizeOptions.map(size => 
                                    `<option value="${size}" ${size === window.productManagementState.pageSize ? 'selected' : ''}>
                                        ${size}
                                    </option>`
                                ).join('')}
                            </select>
                            条
                        </div>
                        <div class="total-count">
                            共计 <span id="totalRecords">0</span> 条
                        </div>
                    </div>
                    <div class="table-pagination">
                        <button class="btn btn-icon" onclick="goToFirstPage()" id="firstPageBtn">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="btn btn-icon" onclick="goToPrevPage()" id="prevPageBtn">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <div class="pagination-pages" id="paginationPages">
                            <!-- 页码将通过 JavaScript 动态生成 -->
                        </div>
                        <button class="btn btn-icon" onclick="goToNextPage()" id="nextPageBtn">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="btn btn-icon" onclick="goToLastPage()" id="lastPageBtn">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </section>
        </div>

        <!-- 新增/编辑产品的模态框 -->
        <div class="product-management-modal" id="productModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">添加产品</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="productForm" class="two-column-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="Basic_creator">创建人员 <span class="required">*</span></label>
                                <input type="text" id="Basic_creator" required readonly>
                            </div>
                            <div class="form-group">
                                <label for="Basic_creationTime">创建时间</label>
                                <input type="text" id="Basic_creationTime" disabled>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="Basic_productSeries">品牌 <span class="required">*</span></label>
                                <input type="text" id="Basic_productSeries" required>
                            </div>
                            <div class="form-group">
                                <label for="Basic_productType">产品类型 <span class="required">*</span></label>
                                <input type="text" id="Basic_productType" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="Basic_productName">产品型号 <span class="required">*</span></label>
                                <input type="text" id="Basic_productName" required>
                            </div>
                            <div class="form-group">
                                <label for="Basic_productCode">产品编码 <span class="required">*</span></label>
                                <input type="text" id="Basic_productCode" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="Basic_orderNumber">订货号 <span class="required">*</span></label>
                                <input type="text" id="Basic_orderNumber" required>
                            </div>
                            <div class="form-group">
                                <label for="snLength">SN长度 <span class="required">*</span></label>
                                <input type="number" id="snLength" required min="1">
                            </div>
                        </div>
                        <div class="form-row full-width">
                            <div class="form-group">
                                <label for="Basic_remarks">备注</label>
                                <textarea id="Basic_remarks" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeProductModal()">取消</button>
                    <button class="btn btn-primary" onclick="saveProduct()">保存</button>
                </div>
            </div>
        </div>
    `;

    // 等待 DOM 更新完成后再加载数据
    setTimeout(() => {
        initEventListeners();
        loadProducts();
    }, 0);
}

// 初始化事件监听器
function initEventListeners() {
    // 搜索框防抖
    const searchInput = document.getElementById('productSearch');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(searchProducts, 500));
    }

    // 模态框关闭按钮
    const closeBtn = document.querySelector('#productModal .close');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeProductModal);
    }
}

// 搜索产品
async function searchProducts() {
    const keyword = document.getElementById('productSearch').value.trim();
    window.productManagementState.searchKeyword = keyword;
    window.productManagementState.currentPage = 1;
    await loadProducts();
}

// 重置搜索
async function resetSearch() {
    document.getElementById('productSearch').value = '';
    window.productManagementState.searchKeyword = '';
    window.productManagementState.currentPage = 1;
    window.productManagementState.sortField = null;
    window.productManagementState.sortOrder = 'asc';
    await loadProducts();
}

// 加载产品列表
async function loadProducts() {
    if (!window.productManagementState.isActive) return;

    try {
        const { currentPage, pageSize, searchKeyword, sortField, sortOrder } = window.productManagementState;
        const queryParams = new URLSearchParams({
            page: currentPage,
            pageSize: pageSize,
            search: searchKeyword || '',
            sortField: sortField || '',
            sortOrder: sortOrder || ''
        });

        const response = await fetch(`/api/products?${queryParams}`);
        const data = await response.json();

        if (data.success) {
            window.productManagementState.products = data.data;
            window.productManagementState.totalCount = data.total;
            
            // 只在产品管理页面激活时才更新UI
            if (window.productManagementState.isActive) {
                // 先检查必要的元素是否存在
                const productsTableBody = document.getElementById('productsTableBody');
                if (productsTableBody) {
                    renderProductsTable();
                    updatePagination();
                    updateDisplayRange();
                }
            }
        } else {
            throw new Error(data.message || '加载失败');
        }
    } catch (error) {
        Logger.error('Error loading products:', error);
        if (window.productManagementState.isActive) {
            // 使用 Swal 而不是 window.SweetAlert
            Swal.fire({
                title: '错误',
                text: '加载失败，请稍后重试',
                icon: 'error'
            });
        }
    }
}

// 渲染产品表格
function renderProductsTable() {
    const tbody = document.getElementById('productsTableBody');
    if (!tbody) return;

    const products = window.productManagementState.products || [];

    tbody.innerHTML = products.map(product => `
        <tr>
            <td>
                <input type="checkbox" 
                       class="product-checkbox" 
                       data-id="${product.id}" 
                       value="${product.id}">
            </td>
            <td class="dblclick-edit" 
                ondblclick="openEditProductModal(${product.id})"
                title="${escapeHtml(product.Basic_productCode)}">${escapeHtml(product.Basic_productCode)}</td>
            <td title="${escapeHtml(product.Basic_productName)}">${escapeHtml(product.Basic_productName)}</td>
            <td title="${escapeHtml(product.Basic_productType)}">${escapeHtml(product.Basic_productType)}</td>
            <td title="${escapeHtml(product.Basic_productSeries)}">${escapeHtml(product.Basic_productSeries)}</td>
            <td title="${escapeHtml(product.Basic_orderNumber)}">${escapeHtml(product.Basic_orderNumber)}</td>
            <td title="${escapeHtml(product.snLength)}">${escapeHtml(product.snLength)}</td>
            <td title="${escapeHtml(product.Basic_creator)}">${escapeHtml(product.Basic_creator)}</td>
            <td title="${escapeHtml(product.Basic_creationTime)}">${escapeHtml(product.Basic_creationTime)}</td>
            <td title="${escapeHtml(product.Basic_remarks)}">${escapeHtml(product.Basic_remarks)}</td>
            <td>
                <button class="btn btn-sm btn-edit" onclick="openEditProductModal(${product.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
            </td>
        </tr>
    `).join('');
}

// 排序功能
function sortBy(field) {
    if (window.productManagementState.sortField === field) {
        window.productManagementState.sortOrder = window.productManagementState.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
        window.productManagementState.sortField = field;
        window.productManagementState.sortOrder = 'asc';
    }
    
    updateSortIcons(field);
    loadProducts();
}

// 更新排序图标
function updateSortIcons(activeField) {
    const headers = document.querySelectorAll('th[onclick]');
    headers.forEach(header => {
        const icon = header.querySelector('i');
        if (icon) {
            const field = header.getAttribute('onclick').match(/'([^']+)'/)[1];
            if (field === activeField) {
                icon.className = `fas fa-sort-${window.productManagementState.sortOrder === 'asc' ? 'up' : 'down'}`;
            } else {
                icon.className = 'fas fa-sort';
            }
        }
    });
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.product-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 删除选中产品
async function deleteSelectedProducts() {
    const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
    
    const selectedIds = Array.from(selectedCheckboxes)
        .map(checkbox => parseInt(checkbox.dataset.id)); // 使用 data-id 属性获取ID
    
    if (selectedIds.length === 0) {
        Swal.fire({
            title: '提示',
            text: '请选择要删除的产品',
            icon: 'warning'
        });
        return;
    }

    // 使用正确的 SweetAlert2 API
    const result = await Swal.fire({
        title: '确认删除',
        text: '确认删除选中的产品吗？此操作不可恢复！',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6'
    });

    if (result.isConfirmed) {
        try {
            const response = await fetch('/api/products/batch-delete', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ ids: selectedIds })
            });

            const data = await response.json();

            if (data.success) {
                await Swal.fire({
                    title: '成功',
                    text: data.message,
                    icon: 'success'
                });
                await loadProducts(); // 重新加载产品列表
                document.getElementById('selectAll').checked = false;
            } else {
                await Swal.fire({
                    title: '错误',
                    text: data.message || '删除失败',
                    icon: 'error'
                });
            }
        } catch (error) {
            Logger.error('Error deleting products:', error);
            await Swal.fire({
                title: '错误',
                text: '删除失败，请稍后重试',
                icon: 'error'
            });
        }
    }
}

// 打开添加产品模态框
async function openAddProductModal() {
    const form = document.getElementById('productForm');
    form.reset();
    window.productManagementVars.editingProductId = null;
    document.getElementById('modalTitle').textContent = '添加产品';

    // 获取当前用户信息并设置创建人员
    try {
        const response = await fetch('/api/user/info');
        const data = await response.json();
        if (data.success) {
            const creatorInput = document.getElementById('Basic_creator');
            creatorInput.value = data.username;
            creatorInput.readOnly = true;  // 设置为只读
        }
    } catch (error) {
        Logger.error('Error getting user info:', error);
    }

    // 设置创建时间为当前时间
    const now = new Date().toLocaleString();
    document.getElementById('Basic_creationTime').value = now;

    const modal = document.getElementById('productModal');
    modal.style.display = 'block';
}

// 关闭模态框
function closeProductModal() {
    const modal = document.getElementById('productModal');
    modal.style.display = 'none';
}

// 保存产品
async function saveProduct() {
    const form = document.getElementById('productForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // 获取表单数据
    const formData = {
        Basic_creator: document.getElementById('Basic_creator').value.trim(),
        Basic_creationTime: document.getElementById('Basic_creationTime').value.trim(),
        Basic_productSeries: document.getElementById('Basic_productSeries').value.trim(),
        Basic_productType: document.getElementById('Basic_productType').value.trim(),
        Basic_productName: document.getElementById('Basic_productName').value.trim(),
        Basic_productCode: document.getElementById('Basic_productCode').value.trim(),
        Basic_orderNumber: document.getElementById('Basic_orderNumber').value.trim(),
        snLength: parseInt(document.getElementById('snLength').value),
        Basic_remarks: document.getElementById('Basic_remarks').value.trim()
    };

    const url = window.productManagementVars.editingProductId ? 
        `/api/products/${window.productManagementVars.editingProductId}` : 
        '/api/products';
    const method = window.productManagementVars.editingProductId ? 'PUT' : 'POST';

    try {
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const data = await response.json();
        if (data.success) {
            window.SweetAlert.success(data.message);
            closeProductModal();
            
            // 保存当前的排序状态
            const currentSortField = window.productManagementState.sortField;
            const currentSortOrder = window.productManagementState.sortOrder;
            
            // 重新加载数据，但保持排序状态
            await loadProducts();
            
            // 如果排序状态被重置，手动恢复
            if (window.productManagementState.sortField !== currentSortField || 
                window.productManagementState.sortOrder !== currentSortOrder) {
                window.productManagementState.sortField = currentSortField;
                window.productManagementState.sortOrder = currentSortOrder;
                updateSortIcons(currentSortField);
                await loadProducts();
            }
        } else {
            // 优化错误提示
            let errorMessage = data.message;
            if (errorMessage && errorMessage.includes('Duplicate entry')) {
                if (errorMessage.includes('productmanagement.dbBasic_productCode')) {
                    errorMessage = '产品编码已存在,请使用其他编码';
                } else if (errorMessage.includes('productmanagement.dbBasic_productName')) {
                    errorMessage = '产品型号已存在,请使用其他型号';
                }
            }
            window.SweetAlert.error(errorMessage || '保存失败');
        }
    } catch (error) {
        Logger.error('Error saving product:', error);
        // 优化未知错误的提示
        let errorMessage = '保存失败';
        if (error.message && error.message.includes('Duplicate entry')) {
            if (error.message.includes('productmanagement.dbBasic_productCode')) {
                errorMessage = '产品编码已存在,请使用其他编码';
            } else if (error.message.includes('productmanagement.dbBasic_productName')) {
                errorMessage = '产品型号已存在,请使用其他型号';
            }
        }
        window.SweetAlert.error(errorMessage);
    }
}

// Excel导出功能
async function exportToExcel() {
    try {
        const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
        const selectedIds = Array.from(selectedCheckboxes)
            .map(checkbox => parseInt(checkbox.dataset.id));
        
        const url = '/api/products/export' + 
            (selectedIds.length ? `?ids=${selectedIds.join(',')}` : '');
            
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error('导出失败');
        }
        
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = `产品信息_${formatDate(new Date())}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(downloadUrl);
        document.body.removeChild(a);
        
        await Swal.fire({
            title: '成功',
            text: '导出成功',
            icon: 'success',
            timer: 1500
        });
    } catch (error) {
        Logger.error('Error exporting to Excel:', error);
        await Swal.fire({
            title: '错误',
            text: '导出失败，请稍后重试',
            icon: 'error'
        });
    }
}

// Excel导入功能
async function importFromExcel(event) {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('/api/products/import', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();
        if (data.success) {
            window.SweetAlert.success('导入成功');
            await loadProducts();
        } else {
            window.SweetAlert.error(data.message || '导入失败');
        }
    } catch (error) {
        Logger.error('Error importing from Excel:', error);
        window.SweetAlert.error('导入失败');
    }

    // 清空文件输入框
    event.target.value = '';
}

// 辅助函数
function escapeHtml(str) {
    if (str === null || str === undefined) return '';
    return String(str)
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 更新显示范围
function updateDisplayRange() {
    // 添加安全检查
    const elements = {
        totalCount: document.getElementById('totalCount'),
        currentRange: document.getElementById('currentRange')
    };

    // 如果必要的元素不存在，直接返回
    if (!elements.totalCount || !elements.currentRange) {
        return;
    }

    const { currentPage, pageSize, totalCount } = window.productManagementState;
    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(start + pageSize - 1, totalCount);
    
    elements.totalCount.textContent = totalCount;
    elements.currentRange.textContent = `${start}-${end}`;
}

// 清理函数
window.cleanupProductManagement = function() {
    window.productManagementState.isActive = false;
    // 清理事件监听器
    const searchInput = document.getElementById('productSearch');
    if (searchInput) {
        searchInput.removeEventListener('input', debounce(searchProducts, 500));
    }
};

// 分页相关函数
function updatePagination() {
    const { currentPage, totalCount, pageSize } = window.productManagementState;
    const totalPages = Math.ceil(totalCount / pageSize);
    window.productManagementState.totalPages = totalPages;

    // 添加安全检查
    const elements = {
        totalRecords: document.getElementById('totalRecords'),
        paginationPages: document.getElementById('paginationPages'),
        firstPageBtn: document.getElementById('firstPageBtn'),
        prevPageBtn: document.getElementById('prevPageBtn'),
        nextPageBtn: document.getElementById('nextPageBtn'),
        lastPageBtn: document.getElementById('lastPageBtn')
    };

    // 如果必要的元素不存在，说明可能在页面切换中，直接返回
    if (!elements.totalRecords || !elements.paginationPages) {
        return;
    }

    // 更新总条数
    elements.totalRecords.textContent = totalCount;

    // 生成页码
    let pagesHtml = '';
    
    // 确定显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);
    
    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }

    // 添加页码按钮
    if (startPage > 1) {
        pagesHtml += `<span class="pagination-ellipsis">...</span>`;
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pagesHtml += `
            <button class="btn btn-page ${i === currentPage ? 'active' : ''}" 
                    onclick="goToPage(${i})">
                ${i}
            </button>
        `;
    }
    
    if (endPage < totalPages) {
        pagesHtml += `<span class="pagination-ellipsis">...</span>`;
    }

    elements.paginationPages.innerHTML = pagesHtml;

    // 只有在所有导航按钮都存在时才更新状态
    if (elements.firstPageBtn && elements.prevPageBtn && 
        elements.nextPageBtn && elements.lastPageBtn) {
        elements.firstPageBtn.disabled = currentPage === 1;
        elements.prevPageBtn.disabled = currentPage === 1;
        elements.nextPageBtn.disabled = currentPage === totalPages;
        elements.lastPageBtn.disabled = currentPage === totalPages;
    }
}

// 页码导航函数
function goToPage(page) {
    window.productManagementState.currentPage = page;
    loadProducts();
}

function goToFirstPage() {
    goToPage(1);
}

function goToPrevPage() {
    const { currentPage } = window.productManagementState;
    if (currentPage > 1) {
        goToPage(currentPage - 1);
    }
}

function goToNextPage() {
    const { currentPage, totalPages } = window.productManagementState;
    if (currentPage < totalPages) {
        goToPage(currentPage + 1);
    }
}

function goToLastPage() {
    goToPage(window.productManagementState.totalPages);
}

function changePageSize() {
    const newSize = parseInt(document.getElementById('pageSizeSelect').value);
    window.productManagementState.pageSize = newSize;
    window.productManagementState.currentPage = 1;
    loadProducts();
}

// 添加编辑产品的模态框打开函数
async function openEditProductModal(productId) {
    window.productManagementVars.editingProductId = productId;
    document.getElementById('modalTitle').textContent = '编辑产品';
    
    try {
        // 获取产品详情
        const response = await fetch(`/api/products/${productId}`);
        const data = await response.json();
        
        if (data.success) {
            const product = data.product;
            
            // 填充表单
            document.getElementById('Basic_creator').value = product.Basic_creator;
            
            // 确保日期格式正确
            let creationTime = product.Basic_creationTime;
            if (creationTime && !creationTime.includes('/')) {
                // 如果日期使用连字符格式，转换为斜杠格式
                creationTime = creationTime.replace(/-/g, '/');
            }
            document.getElementById('Basic_creationTime').value = creationTime;
            
            document.getElementById('Basic_productSeries').value = product.Basic_productSeries;
            document.getElementById('Basic_productType').value = product.Basic_productType;
            document.getElementById('Basic_productName').value = product.Basic_productName;
            document.getElementById('Basic_productCode').value = product.Basic_productCode;
            document.getElementById('Basic_orderNumber').value = product.Basic_orderNumber;
            document.getElementById('snLength').value = product.snLength;
            document.getElementById('Basic_remarks').value = product.Basic_remarks;
            
            // 显示模态框
            const modal = document.getElementById('productModal');
            modal.style.display = 'block';
        } else {
            Swal.fire({
                title: '错误',
                text: data.message || '获取产品信息失败',
                icon: 'error'
            });
        }
    } catch (error) {
        Logger.error('Error fetching product details:', error);
        Swal.fire({
            title: '错误',
            text: '获取产品信息失败',
            icon: 'error'
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('product-management-content')) {
        window.initProductManagement();
    }
}); 