# Vue 测试表单自动保存与提交功能技术文档

## 1. 功能概述

本文档详细阐述了耦合器测试页面 (CouplerVue) 中"自动提交"功能的设计与实现。该功能旨在优化高重复性的测试流程，当特定条件满足时，系统能够自动将所有测试项标记为"通过"并提交测试结果，从而极大提升测试效率，减少人工操作。

此功能的核心是**"有条件的自动化"**，它并非无脑执行，而是在一系列严格的校验通过后才触发，确保了数据提交的准确性和流程的完整性。

### 1.1. 目标用户与场景

-   **目标用户**: 生产线测试人员。
-   **核心场景**: 在大批量、重复性的耦合器测试中，当产品SN号有效、版本信息一致且基础信息完备时，测试人员只需输入SN号，即可由系统自动完成后续的"全通过"和"提交"操作，无缝衔接下一次测试。

### 1.2. UI 交互界面

该功能在UI上主要体现在以下两点：

1.  **自动提交开关**: 位于页面右上角工具栏，是一个 `el-switch` 组件。用户可以随时开启或关闭此功能。系统会通过测试日志实时反馈开关状态。
2.  **实时日志反馈**: 在"测试日志"窗口中，自动提交流程的每一步（如：启动校验、校验结果、执行操作、提交结果）都会有清晰的日志记录，方便用户追踪和调试。

---

## 2. 核心逻辑流程

该功能的工作流由用户输入SN号触发，并以状态机的形式依次执行。以下是完整的执行步骤拆解：

1.  **前提条件：用户启用自动提交**
    -   用户在UI界面上，必须**手动打开"自动提交"开关**。`autoSubmitEnabled` 响应式变量被设置为 `true`。这是整个流程启动的最高优先级前提。

2.  **步骤 1：用户输入SN号 (流程起点)**
    -   用户在"产品SN号"输入框中输入SN，并按下 `Enter` 键。
    -   此操作触发 `checkSN(sn)` 函数，自动提交流程正式开始。

3.  **步骤 2：SN有效性预检 (`checkSN` 函数)**
    -   **分支A：当前工单需要PCBA绑定检查** (`requiresPcbaCheck.value` 为 `true`)
        -   系统向后端API `/api/coupler-controller-vue/check-sn` 发送请求，验证SN号是否已绑定PCBA。
        -   **若验证失败**：流程中止。系统会弹出警告信息，清空SN输入框，并自动聚焦，提示用户重新输入。
        -   **若验证成功**：流程继续，调用 `autoFetchVersionInfo(sn)` 函数。
    -   **分支B：当前工单无需PCBA绑定检查**
        -   系统跳过后端API验证，直接调用 `autoFetchVersionInfo(sn)` 函数。

4.  **步骤 3：获取版本信息并触发提交检查 (`autoFetchVersionInfo` 函数)**
    -   系统向后端API `/api/coupler-controller-vue/get-version-info` 发送请求，尝试自动获取该SN对应的版本与构建日期。
    -   无论版本信息是否获取成功，此函数在执行完毕后，会**延时500毫秒**后调用 `triggerAutoSubmitIfEnabled(sn)` 函数。
    -   **设计说明**: 这是整个流程中**唯一且最终**的自动提交触发点，确保了在SN检查（无论是否需要PCBA）完成后，流程统一汇集于此，避免了重复触发。500毫秒的延迟是为了等待Vue的DOM更新，确保版本信息已渲染到UI上。

5.  **步骤 4：自动提交流程触发器 (`triggerAutoSubmitIfEnabled` 函数)**
    -   首先，检查 `autoSubmitEnabled` 是否为 `true`。如果用户在此期间关闭了开关，则流程在此处安全中止。
    -   调用 `performAutoValidation()` 函数，开始执行核心的自动校验。

6.  **步骤 5：执行核心自动校验 (`performAutoValidation` 函数)**
    -   此函数是自动提交的"看门人"，它会依次执行以下所有检查：
        1.  **SN号长度校验**: 检查 `formData.productSN` 的长度是否与 `formData.snByteCount` 严格相等。
        2.  **版本一致性校验**: 检查 `isVersionConsistent` 计算属性是否为 `true`（即自动获取的版本与手动输入的版本是否匹配）。
        3.  **表单必填项校验**: 检查测试人员、工单号、产品型号、软件版本等关键字段是否都已填写。
    -   **校验结果处理**：
        -   **若所有校验全部通过** (`allValid: true`)：函数返回成功状态，日志记录"所有校验通过"。流程进入下一步"执行阶段"。
        -   **若任何一项校验失败**：函数返回失败状态。日志会详细记录失败原因（如"SN号长度不符合要求"），并向用户弹出警告消息"自动提交校验失败，请手动操作"。**流程在此处完全中止**。

7.  **步骤 6：执行阶段 (校验通过后)**
    -   **操作A：自动全通过**
        -   系统调用 `setAllPass()` 函数，将所有测试项目的结果设置为 `pass`。
        -   日志记录"执行自动全通过操作"。
    -   **操作B：等待UI更新**
        -   系统执行一个 `await new Promise(resolve => setTimeout(resolve, 900))`，等待900毫秒。
        -   **设计说明**: 这个延迟至关重要，它确保了`setAllPass()`操作导致的UI变化（如测试项状态标签的更新）有足够的时间渲染完成，然后再执行提交。
    -   **操作C：自动提交**
        -   系统调用 `submitForm()` 函数。

8.  **步骤 7：提交与重置 (`submitForm` 函数)**
    -   此函数执行标准的表单提交逻辑，将完整的测试数据发送到后端API `/api/coupler-controller-vue/submit-test`。
    -   **若提交成功**：
        -   表单被重置，但会保留"测试人员"、"工单号"等可复用的信息。
        -   **产品SN号被清空**，同时光标自动聚焦到"产品SN号"输入框。
        -   至此，一次完整的自动提交流程结束，系统已准备好进行下一次测试。
    -   **若提交失败**：
        -   系统会显示后端返回的错误信息。
        -   表单数据不会被清空，允许用户修正问题后手动提交。

---

## 3. 边界条件与错误处理

-   **用户中途关闭开关**: 流程会在 `triggerAutoSubmitIfEnabled` 函数的入口处被安全拦截，不会执行任何自动操作。
-   **网络或API错误**: 在SN检查、版本获取或最终提交的任何一个API调用环节失败，流程都会中止，并在日志中记录下错误信息，同时UI上会弹出相应的错误提示。
-   **校验失败**: `performAutoValidation` 函数确保了任何不满足预设条件的提交都会被提前拦截，并给予用户明确的提示，要求转为手动操作。
-   **重复触发**: 通过将触发点统一收敛到 `autoFetchVersionInfo` 函数的末尾，并移除了 `checkSN` 函数中的冗余调用，彻底解决了重复执行的问题。

## 4. 总结

该"自动提交"功能通过严谨的逻辑流程和周密的边界条件处理，实现了在保证数据准确性的前提下，最大化地提升了测试效率。其清晰的日志反馈和智能的校验机制，使其成为一个可靠且用户友好的自动化工具。 