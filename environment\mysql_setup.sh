#!/bin/bash

# 加载配置
source "$(dirname "$0")/config.sh"

echo "开始配置MySQL..."
check_root

# 创建必要的目录
echo "创建MySQL目录..."
mkdir -p /var/run/mysqld
mkdir -p /var/log/mysql
mkdir -p /etc/mysql/mysql.conf.d
chown -R mysql:mysql /var/run/mysqld
chown -R mysql:mysql /var/log/mysql

# 安装MySQL
echo "安装MySQL..."
apt install -y mysql-server mysql-client libmysqlclient-dev
check_status "MySQL安装失败"

# 配置MySQL
echo "配置MySQL..."

# 动态计算MySQL缓冲池大小(使用系统内存的50%)
SYSTEM_MEMORY_GB=$(awk '/MemTotal/ {printf "%.0f\n", $2/(1024*1024)}' /proc/meminfo)
BUFFER_POOL_SIZE="$(($SYSTEM_MEMORY_GB/2))G"

# 确保配置目录存在
mkdir -p /etc/mysql/mysql.conf.d

cat > /etc/mysql/mysql.conf.d/mysqld.cnf << EOF
[mysqld]
bind-address = ${DB_HOST}
port = ${DB_PORT}
character-set-server = ${DB_CHARSET}
collation-server = ${DB_COLLATE}

max_connections = 150
innodb_buffer_pool_size = ${BUFFER_POOL_SIZE}
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 优化配置
innodb_file_per_table = 1
innodb_buffer_pool_instances = 4
innodb_read_io_threads = 4
innodb_write_io_threads = 4
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000

# 基本设置
user = mysql
pid-file = /var/run/mysqld/mysqld.pid
socket = /var/run/mysqld/mysqld.sock
datadir = /var/lib/mysql
log-error = /var/log/mysql/error.log
EOF

# 设置权限
chown -R mysql:mysql /etc/mysql
chmod 644 /etc/mysql/mysql.conf.d/mysqld.cnf

# 启动MySQL服务
echo "启动MySQL服务..."
systemctl start mysql
systemctl enable mysql
check_status "MySQL服务启动失败"

# 等待MySQL完全启动
echo "等待MySQL启动..."
sleep 5

# 配置root用户和数据库
echo "配置数据库..."
mysql -u root << EOF
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '${DB_PASSWORD}';
CREATE DATABASE IF NOT EXISTS ${DB_NAME} CHARACTER SET ${DB_CHARSET} COLLATE ${DB_COLLATE};
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY '${DB_PASSWORD}';
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO 'root'@'%';
FLUSH PRIVILEGES;
EOF
check_status "MySQL配置失败"

# 重启MySQL服务
echo "重启MySQL服务..."
systemctl restart mysql
check_status "MySQL重启失败"

echo "MySQL配置完成！" 