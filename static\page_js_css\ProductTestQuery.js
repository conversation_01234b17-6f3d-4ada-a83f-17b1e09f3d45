(function() {
    // 检查是否已经存在全局变量
    if (typeof window.productTestPageState === 'undefined') {
        window.productTestPageState = {
            currentPage: 1,
            pageSize: 10,
            totalItems: 0,
            totalPages: 10,
            data: [],
            sortField: 'testTime', // 默认排序字段
            sortOrder: 'desc'      // 默认排序顺序
        };
    }

    if (typeof window.productTestColumnState === 'undefined') {
        window.productTestColumnState = {
            checkbox: true,
            orderNumber: true,
            serialNumber: true,
            productCode: true,
            productModel: true,
            productStatus: true,
            maintenanceCount: true,
            reworkCount: true,
            testResult: true,
            tester: true,
            testTime: true,
            comparisonResult: true,
            comparisonTime: true,
            comparisonUser: true  // 添加比对人员列设置
        };
    }

    // 1. 定义列配置常量
    const PRODUCT_TEST_COLUMNS = [
        'orderNumber', 'serialNumber', 'productCode', 'productModel', 
        'productStatus', 'maintenanceCount', 'reworkCount', 'testResult',
        'tester', 'testTime', 'comparisonResult', 'comparisonTime', 'comparisonUser'  // 添加比对人员
    ];

    function initProductTestQueryPage() {
        const productTestQueryContent = document.getElementById('product-test-query-content');
        if (!productTestQueryContent) {
            Logger.error('无法找到 product-test-query-content 元素');
            return;
        }
        
        productTestQueryContent.innerHTML = `
            <div class="product-test-query">
                <!-- 1. 查询区域 -->
                <div class="query-section">
                    <form class="query-form">
                        <div class="form-item">
                            <label class="form-label">工单号</label>
                            <input type="text" class="form-input" id="product-test-order-number" placeholder="请输入工单号">
                        </div>
                        <div class="form-item">
                            <label class="form-label">SN号</label>
                            <input type="text" class="form-input" id="product-test-model" placeholder="请输入SN号">
                        </div>
                        <div class="form-item">
                            <label class="form-label">产品编码</label>
                            <input type="text" class="form-input" id="product-test-code" placeholder="请输入产品编码">
                        </div>
                        <div class="form-item">
                            <label class="form-label">测试结果</label>
                            <select class="form-select" id="product-test-result">
                                <option value="">全部</option>
                                <option value="pass">通过</option>
                                <option value="fail">不通过</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-input" id="product-test-start-date">
                        </div>
                        <div class="form-item">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-input" id="product-test-end-date">
                        </div>
                    </form>
                    <div class="query-buttons">
                        <button class="btn btn-default" onclick="resetProductTestForm()">重置</button>
                        <button class="btn btn-primary" onclick="searchProductTest()">查询</button>
                    </div>
                </div>

                <!-- 2. 工具栏区域 -->
                <div class="toolbar-section">
                    <div class="toolbar-left">
                        <button class="btn btn-export" onclick="exportProductTestData()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                        <span style="margin-left: 12px; color: #1890ff; font-size: 13px;">（双击SN号查看详情）</span>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-default" onclick="toggleProductTestColumnSettings()">
                            <i class="fas fa-cog"></i> 列设置
                        </button>
                        <button class="btn btn-default" onclick="refreshProductTestTable()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 3. 表格区域 -->
                <div class="table-section">
                    <table class="data-table">
                        <thead>
                            <tr class="table-header">
                                <th class="table-cell-fixed" style="width: 50px;">
                                    <input type="checkbox" id="product-test-select-all" onclick="toggleProductTestSelectAll()">
                                </th>
                                <th class="table-cell-wide" style="width: 180px;">工单号</th>
                                <th class="table-cell-wide" style="width: 180px;">SN号</th>
                                <th class="table-cell-wide sortable" style="width: 180px;" onclick="sortByProductTest('productCode')">
                                    产品编码 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-wide" style="width: 180px;">产品型号</th>
                                <th class="table-cell-normal sortable" style="width: 100px;" onclick="sortByProductTest('productStatus')">
                                    产品状态 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-normal sortable" style="width: 80px;" onclick="sortByProductTest('maintenanceCount')">
                                    维修次数 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-normal sortable" style="width: 80px;" onclick="sortByProductTest('reworkCount')">
                                    返工次数 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-normal" style="width: 100px;">测试状态</th>
                                <th class="table-cell-medium" style="width: 120px;">测试人员</th>
                                <th class="table-cell-medium sortable" style="width: 160px;" onclick="sortByProductTest('testTime')">
                                    测试时间 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-normal sortable" style="width: 100px;" onclick="sortByProductTest('comparisonResult')">
                                    比对结果 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-medium" style="width: 160px;">比对时间</th>
                                <th class="table-cell-medium" style="width: 120px;">比对人员</th>
                                <th class="table-cell-action" style="width: 120px;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="product-test-results-tbody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 4. 分页区域 -->
                <div class="pagination-section">
                    <div class="page-size">
                        <span>每页</span>
                        <select class="form-select" onchange="changeProductTestPageSize(this.value)">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                        </select>
                        <span>条</span>
                    </div>
                    <div class="pagination-controls" id="product-test-pagination-controls">
                        <button class="page-button" onclick="changeProductTestPage(${productTestPageState.currentPage - 1})" 
                                ${productTestPageState.currentPage === 1 ? 'disabled' : ''}>
                            上一页
                        </button>
                        <button class="page-button active" onclick="changeProductTestPage(1)">1</button>
                        <button class="page-button" onclick="changeProductTestPage(2)">2</button>
                        <button class="page-button" onclick="changeProductTestPage(3)">3</button>
                        <span>...</span>
                        <button class="page-button" onclick="changeProductTestPage(${productTestPageState.totalPages})">
                            ${productTestPageState.totalPages || 1}
                        </button>
                        <button class="page-button" onclick="changeProductTestPage(${productTestPageState.currentPage + 1})"
                                ${productTestPageState.currentPage === productTestPageState.totalPages ? 'disabled' : ''}>
                            下一页
                        </button>
                        <div class="page-jump">
                            <span>跳至</span>
                            <input type="text" class="page-input" value="${productTestPageState.currentPage}" 
                                   onchange="jumpToProductTestPage(this.value)">
                            <span>页</span>
                        </div>
                        <span class="total-count">共 ${productTestPageState.totalItems} 条</span>
                    </div>
                </div>

                <!-- 列设置弹窗 -->
                <div id="product-test-column-settings-modal" class="modal product-test-column-modal">
                    <div class="modal-content product-test-column-modal__content">
                        <div class="modal-header">
                            <h3>列设置</h3>
                            <span class="close" onclick="closeProductTestColumnSettings()">&times;</span>
                        </div>
                        <div class="modal-body">
                            <div class="column-list">
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="product-test-select-all-columns" onclick="toggleAllProductTestColumns()">
                                        <span>全选</span>
                                    </label>
                                </div>
                                <div class="column-divider"></div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-orderNumber" checked>
                                        <span>工单号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-serialNumber" checked>
                                        <span>SN号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-productCode" checked>
                                        <span>产品编码</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-productModel" checked>
                                        <span>产品型号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-productStatus" checked>
                                        <span>产品状态</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-maintenanceCount" checked>
                                        <span>维修次数</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-reworkCount" checked>
                                        <span>返工次数</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-testResult" checked>
                                        <span>测试状态</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-tester" checked>
                                        <span>测试人员</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-testTime" checked>
                                        <span>测试时间</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-comparisonResult" checked>
                                        <span>比对结果</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-comparisonTime" checked>
                                        <span>比对时间</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-product-test-comparisonUser" checked>
                                        <span>比对人员</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" onclick="applyProductTestColumnSettings()">确定</button>
                            <button class="btn btn-default" onclick="closeProductTestColumnSettings()">取消</button>
                        </div>
                    </div>
                </div>

                <!-- 详态框 -->
                <div id="product-test-detail-modal" class="product-test-query__modal">
                    <div class="product-test-query__modal-content">
                        <div class="product-test-query__modal-header">
                            <h2 class="product-test-query__modal-title">测试详细信息</h2>
                            <span class="product-test-query__modal-close">&times;</span>
                        </div>
                        <div class="product-test-query__modal-body">
                            <!-- 基本信息卡片 -->
                            <div class="product-test-query__info-card">
                                <h3 class="product-test-query__card-title">基本信息</h3>
                                <div class="product-test-query__info-grid">
                                    <!-- 基本信息内容将在下面填充 -->
                                </div>
                            </div>

                            <!-- 设备/版本信息卡片 -->
                            <div class="product-test-query__info-card product-test-query__info-card--device">
                                <!-- 动态内容 -->
                            </div>

                            <!-- 测试结果卡片 -->
                            <div class="product-test-query__info-card">
                                <div class="product-test-query__card-header" style="display: flex; justify-content: space-between; align-items: end; margin-bottom: 16px; padding-bottom: 0; border-bottom: 2px solid #e6f4ff;">
                                    <h3 class="product-test-query__card-title" style="margin: 0; border-bottom: 2px solid transparent; padding-bottom: 12px;">测试结果</h3>
                                    <div class="product-test-query__test-tools" style="display: flex; align-items: center; gap: 12px;">
                                        <!-- 测试工具按钮将在这里动态添加 -->
                                    </div>
                                </div>
                                <div class="product-test-query__test-grid">
                                    <!-- 动态内容 -->
                                </div>
                                <!-- M区日志内联容器 -->
                                <div id="m-area-log-content-inline" class="m-area-log-inline-container" style="display: none; margin-top: 16px; background: #fafafa; border: 1px solid #d9d9d9; border-radius: 6px; overflow: hidden;">
                                    <!-- M区日志内容将在这里显示 -->
                                </div>
                            </div>

                            <!-- PCBA绑定信息卡片 -->
                            <div class="product-test-query__info-card product-test-query__info-card--pcba">
                                <h3 class="product-test-query__card-title">PCBA绑定息</h3>
                                <div class="product-test-query__info-grid">
                                    <!-- 动态内容 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- M区日志模态框 -->
                <div id="m-area-log-modal" class="product-test-query__modal">
                    <div class="product-test-query__modal-content" style="max-width: 800px;">
                        <div class="product-test-query__modal-header">
                            <h2 class="product-test-query__modal-title">测试日志</h2>
                            <span class="product-test-query__modal-close" onclick="closeMAreaLogModal()">&times;</span>
                        </div>
                        <div class="product-test-query__modal-body">
                            <div id="m-area-log-content">
                                <!-- M区日志内容将在这里填充 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化排序图标状态
        updateSortIconsProductTest();
        
        // 初始化事件监听
        initProductTestEventListeners();
    }

    function initProductTestEventListeners() {
        // 输入框回车事件
        const inputs = ['product-test-order-number', 'product-test-model'];
        inputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        searchProductTest();
                    }
                });
            }
        });

        // 获取模态框素
        const detailModal = document.getElementById('product-test-detail-modal');
        if (detailModal) {
            // 模态框关闭按钮事件
            const closeBtn = detailModal.querySelector('.product-test-query__modal-close');
            if (closeBtn) {
                closeBtn.onclick = () => detailModal.style.display = 'none';
            }

            // 点击模态框外部关闭
            window.onclick = (e) => {
                if (e.target === detailModal) {
                    detailModal.style.display = 'none';
                }
            };

            // 修改这部分代码，将拖动功能的初化移到模态框显示时
            const modalContent = detailModal.querySelector('.product-test-query__modal-content');
            const modalHeader = detailModal.querySelector('.product-test-query__modal-header');

            if (modalContent && modalHeader) {
                let isDragging = false;
                let currentX;
                let currentY;
                let initialX;
                let initialY;
                let xOffset = 0;
                let yOffset = 0;

                modalHeader.addEventListener('mousedown', dragStart);
                document.addEventListener('mousemove', drag);
                document.addEventListener('mouseup', dragEnd);

                function dragStart(e) {
                    if (e.target === modalHeader || e.target.parentElement === modalHeader) {
                        initialX = e.clientX - xOffset;
                        initialY = e.clientY - yOffset;
                        isDragging = true;
                        modalContent.classList.add('dragging');
                    }
                }

                function drag(e) {
                    if (isDragging) {
                        e.preventDefault();
                        
                        currentX = e.clientX - initialX;
                        currentY = e.clientY - initialY;

                        // 限制拖动范围
                        const modalRect = modalContent.getBoundingClientRect();
                        const viewportWidth = window.innerWidth;
                        const viewportHeight = window.innerHeight;

                        // 限制左右范围
                        if (currentX < -(viewportWidth - modalRect.width) / 2) {
                            currentX = -(viewportWidth - modalRect.width) / 2;
                        }
                        if (currentX > (viewportWidth - modalRect.width) / 2) {
                            currentX = (viewportWidth - modalRect.width) / 2;
                        }

                        // 限制上下范围
                        if (currentY < -(viewportHeight - modalRect.height) / 2) {
                            currentY = -(viewportHeight - modalRect.height) / 2;
                        }
                        if (currentY > (viewportHeight - modalRect.height) / 2) {
                            currentY = (viewportHeight - modalRect.height) / 2;
                        }

                        xOffset = currentX;
                        yOffset = currentY;

                        setTranslate(currentX, currentY, modalContent);
                    }
                }

                function dragEnd(e) {
                    initialX = currentX;
                    initialY = currentY;
                    isDragging = false;
                    modalContent.classList.remove('dragging');
                }

                function setTranslate(xPos, yPos, el) {
                    el.style.transform = `translate(${xPos}px, ${yPos}px)`;
                }
            }
        }

        // 添加分页大小选择器的件监听
        const pageSizeSelect = document.querySelector('.page-size select');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                changeProductTestPageSize(e.target.value);
            });
        }

        // 添加触摸事件监听器
        const touchElements = document.querySelectorAll('.scrollable');
        touchElements.forEach(element => {
            element.addEventListener('touchstart', handler, { passive: true });
            element.addEventListener('touchmove', handler, { passive: true });
        });

        // 添加表格中复选框的变化监听
        const tbody = document.getElementById('product-test-results-tbody');
        if (tbody) {
            tbody.addEventListener('change', (e) => {
                if (e.target.type === 'checkbox') {
                    // 更新全选框状态
                    const selectAll = document.getElementById('product-test-select-all');
                    const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
                    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                    selectAll.checked = allChecked;
                    
                    // 更新导出按钮文本
                    updateExportButtonText();
                }
            });
        }
    }

    async function searchProductTest() {
        const orderNumber = document.getElementById('product-test-order-number').value.trim();
        const serialNumber = document.getElementById('product-test-model').value.trim();
        const productCode = document.getElementById('product-test-code').value.trim(); // 获取产品编码
        const testResult = document.getElementById('product-test-result').value;
        const startDate = document.getElementById('product-test-start-date').value;
        const endDate = document.getElementById('product-test-end-date').value;
        
        if (!orderNumber && !serialNumber && !productCode && !testResult && !startDate && !endDate) {
            showToast('请至少输入一个查询条件', 'warning');
            return;
        }

        if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
            showToast('开始日期不能大于结束日期', 'warning');
            return;
        }

        try {
            const params = new URLSearchParams();
            if (orderNumber) params.append('orderNumber', orderNumber);
            if (serialNumber) params.append('serialNumber', serialNumber);
            if (productCode) params.append('productCode', productCode); // 添加产品编码参数
            if (testResult) {
                params.append('testResult', testResult === 'pass' ? 'pass' : 'fail');
            }
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);
            
            // 添加分页参数
            params.append('page', productTestPageState.currentPage);
            params.append('pageSize', productTestPageState.pageSize);
            
            // 添加排序参数
            params.append('sortField', productTestPageState.sortField);
            params.append('sortOrder', productTestPageState.sortOrder);

            showToast('正在查询...', 'info');

            const response = await fetch(`/api/product-test-query/search?${params.toString()}`);
            const result = await response.json();

            Logger.log('Search result:', result);
            Logger.log('Page state after update:', productTestPageState);

            if (result.success) {
                if (result.data && result.data.length > 0) {
                    // 确保正确设置总条数
                    productTestPageState.data = result.data;
                    productTestPageState.totalItems = result.totalCount || result.data.length; // 添加备选值
                    productTestPageState.currentPage = result.page || 1;
                    productTestPageState.pageSize = result.pageSize || 10;
                    productTestPageState.totalPages = Math.ceil(productTestPageState.totalItems / productTestPageState.pageSize);
                    
                    updateProductTestResultsTable(result.data);
                    const tableSection = document.querySelector('.table-section');
                    if (tableSection) {
                        tableSection.style.display = 'block';
                    }
                    showToast(result.message || `查询成功，共找到 ${productTestPageState.totalItems} 条记录`, 'success');
                } else {
                    showToast('未找到匹配的记录', 'warning');
                    const tableSection = document.querySelector('.table-section');
                    if (tableSection) {
                        tableSection.style.display = 'none';
                    }
                    // 清空表格内容
                    const tbody = document.getElementById('product-test-results-tbody');
                    if (tbody) {
                        tbody.innerHTML = '';
                    }
                }
            } else {
                showToast(result.message || '查询失败', 'error');
            }
        } catch (error) {
            Logger.error('查询失败:', error);
            showToast('查询失败，请稍后重试', 'error');
        }
    }

    // 表格行渲染函数
    function renderTableRow(item) {
        return `
            <tr>
                ${productTestColumnState.checkbox ? `
                    <td class="table-cell-center table-cell-fixed">
                        <input type="checkbox" class="row-checkbox" data-index="${item.index}">
                    </td>
                ` : ''}
                ${productTestColumnState.orderNumber ? `<td class="table-cell-center table-cell-wide text-code" title="${item.orderNumber || '-'}">${item.orderNumber || '-'}</td>` : ''}
                ${productTestColumnState.serialNumber ? `<td class="table-cell-center table-cell-wide text-code dblclick-detail" title="${item.serialNumber || '-'}" ondblclick="showProductTestDetail('${item.serialNumber}')">${item.serialNumber || '-'}</td>` : ''}
                ${productTestColumnState.productCode ? `<td class="table-cell-center table-cell-wide text-code" title="${item.productCode || '-'}">${item.productCode || '-'}</td>` : ''}
                ${productTestColumnState.productModel ? `<td class="table-cell-center table-cell-wide text-code" title="${item.productModel || '-'}">${item.productModel || '-'}</td>` : ''}
                ${productTestColumnState.productStatus ? `<td class="table-cell-center table-cell-normal text-status" title="${item.productStatus || '-'}">${item.productStatus || '-'}</td>` : ''}
                ${productTestColumnState.maintenanceCount ? `<td class="table-cell-center table-cell-normal text-number" title="${item.maintenanceCount || '0'}">${item.maintenanceCount || '0'}</td>` : ''}
                ${productTestColumnState.reworkCount ? `<td class="table-cell-center table-cell-normal text-number" title="${item.reworkCount || '0'}">${item.reworkCount || '0'}</td>` : ''}
                ${productTestColumnState.testResult ? `
                    <td class="table-cell-center table-cell-normal text-status" title="${item.testResult === 'pass' ? '通过' : '不通过'}">
                        <span class="test-result-tag ${item.testResult === 'pass' ? 'test-result-pass' : 'test-result-fail'}">
                            ${item.testResult === 'pass' ? '通过' : '不通过'}
                        </span>
                    </td>
                ` : ''}
                ${productTestColumnState.tester ? `<td class="table-cell-center table-cell-medium text-name" title="${item.tester || '-'}">${item.tester || '-'}</td>` : ''}
                ${productTestColumnState.testTime ? `<td class="table-cell-center table-cell-medium text-date" title="${formatDate(item.testTime) || '-'}">${formatDate(item.testTime) || '-'}</td>` : ''}
                ${productTestColumnState.comparisonResult ? 
                    `<td class="table-cell-center table-cell-medium" title="${item.comparisonResult === 'PASS' ? '通过' : (item.comparisonResult || '-')}">
                        ${item.comparisonResult === 'PASS' ? 
                            '<span class="test-result-tag test-result-pass">通过</span>' : 
                            item.comparisonResult || '-'}
                    </td>` : ''}
                ${productTestColumnState.comparisonTime ? 
                    `<td class="table-cell-center table-cell-medium text-date" title="${formatDate(item.comparisonTime) || '-'}">
                        ${formatDate(item.comparisonTime) || '-'}
                    </td>` : ''}
                ${productTestColumnState.comparisonUser ? `<td class="table-cell-center table-cell-medium text-name" title="${item.comparisonUser || '-'}">${item.comparisonUser || '-'}</td>` : ''}
                <td class="table-cell-center table-cell-action">
                    <button class="btn btn-detail" onclick="showProductTestDetail('${item.serialNumber}')">
                        <i class="fas fa-eye"></i> 详情
                    </button>
                </td>
            </tr>
        `;
    }

    // 更新表格函数，添加排序图标
    function updateProductTestResultsTable(data) {
        const tbody = document.getElementById('product-test-results-tbody');
        if (!tbody) return;

        // 更新表头
        const thead = document.querySelector('.table-header');
        if (thead) {
            thead.innerHTML = `
                ${productTestColumnState.checkbox ? `
                    <th class="table-cell-fixed">
                        <input type="checkbox" id="product-test-select-all" onclick="toggleProductTestSelectAll()">
                    </th>
                ` : ''}
                ${productTestColumnState.orderNumber ? `<th class="table-cell-wide">工单号</th>` : ''}
                ${productTestColumnState.serialNumber ? `<th class="table-cell-wide">SN号</th>` : ''}
                ${productTestColumnState.productCode ? `
                    <th class="table-cell-wide sortable" onclick="sortByProductTest('productCode')">
                        产品编码 <i class="fas fa-sort"></i>
                    </th>
                ` : ''}
                ${productTestColumnState.productModel ? `<th class="table-cell-wide">产品型号</th>` : ''}
                ${productTestColumnState.productStatus ? `
                    <th class="table-cell-normal sortable" onclick="sortByProductTest('productStatus')">
                        产品状态 <i class="fas fa-sort"></i>
                    </th>
                ` : ''}
                ${productTestColumnState.maintenanceCount ? `
                    <th class="table-cell-normal sortable" onclick="sortByProductTest('maintenanceCount')">
                        维修次数 <i class="fas fa-sort"></i>
                    </th>
                ` : ''}
                ${productTestColumnState.reworkCount ? `
                    <th class="table-cell-normal sortable" onclick="sortByProductTest('reworkCount')">
                        返工次数 <i class="fas fa-sort"></i>
                    </th>
                ` : ''}
                ${productTestColumnState.testResult ? `<th class="table-cell-normal">测试状态</th>` : ''}
                ${productTestColumnState.tester ? `<th class="table-cell-medium">测试人员</th>` : ''}
                ${productTestColumnState.testTime ? `
                    <th class="table-cell-medium sortable" onclick="sortByProductTest('testTime')">
                        测试时间 <i class="fas fa-sort"></i>
                    </th>
                ` : ''}
                ${productTestColumnState.comparisonResult ? `
                    <th class="table-cell-normal sortable" onclick="sortByProductTest('comparisonResult')">
                        比对结果 <i class="fas fa-sort"></i>
                    </th>
                ` : ''}
                ${productTestColumnState.comparisonTime ? `<th class="table-cell-medium">比对时间</th>` : ''}
                ${productTestColumnState.comparisonUser ? `<th class="table-cell-medium">比对人员</th>` : ''}
                <th class="table-cell-action">操作</th>
            `;
            
            // 更新排序图标
            updateSortIconsProductTest();
        }

        // 使用 renderTableRow 函数渲染表格行
        tbody.innerHTML = data.map((item, index) => {
            item.index = index;  // 添加索引
            return renderTableRow(item);
        }).join('');

        // 绑定事件和更新状态
        bindCheckboxEvents();
        updateProductTestPagination();
        updateExportButtonText();
    }

    function updateProductTestPagination() {
        const paginationControls = document.getElementById('product-test-pagination-controls');
        if (!paginationControls) return;
        
        // 计算显示的页码范围
        let startPage = Math.max(1, productTestPageState.currentPage - 2);
        let endPage = Math.min(productTestPageState.totalPages, startPage + 4);
        
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }
        
        let html = `
            <button class="page-button" onclick="changeProductTestPage(${productTestPageState.currentPage - 1})" 
                    ${productTestPageState.currentPage === 1 ? 'disabled' : ''}>
                上一页
            </button>
        `;
        
        // 显示第一页
        if (startPage > 1) {
            html += `
                <button class="page-button" onclick="changeProductTestPage(1)">1</button>
                ${startPage > 2 ? '<span>...</span>' : ''}
            `;
        }
        
        // 显示中间页码
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="page-button ${i === productTestPageState.currentPage ? 'active' : ''}" 
                        onclick="changeProductTestPage(${i})">
                    ${i}
                </button>
            `;
        }
        
        // 显示后一页
        if (endPage < productTestPageState.totalPages) {
            html += `
                ${endPage < productTestPageState.totalPages - 1 ? '<span>...</span>' : ''}
                <button class="page-button" onclick="changeProductTestPage(${productTestPageState.totalPages})">
                    ${productTestPageState.totalPages}
                </button>
            `;
        }
        
        html += `
            <button class="page-button" onclick="changeProductTestPage(${productTestPageState.currentPage + 1})"
                    ${productTestPageState.currentPage === productTestPageState.totalPages ? 'disabled' : ''}>
                下一页
            </button>
            <div class="page-jump">
                <span>跳至</span>
                <input type="text" class="page-input" value="${productTestPageState.currentPage}" 
                       onchange="jumpToProductTestPage(this.value)">
                <span>页</span>
            </div>
            <span class="total-count">共 ${productTestPageState.totalItems || 0} 条</span>
        `;
        
        paginationControls.innerHTML = html;
    }

    function changeProductTestPage(page) {
        if (page < 1 || page > productTestPageState.totalPages || page === productTestPageState.currentPage) {
            return;
        }
        
        productTestPageState.currentPage = page;
        // 使用静默查询
        silentSearch();
    }

    function jumpToProductTestPage(page) {
        const pageNum = parseInt(page);
        if (isNaN(pageNum)) return;
        
        const targetPage = Math.min(Math.max(1, pageNum), productTestPageState.totalPages);
        if (targetPage === productTestPageState.currentPage) return;
        
        productTestPageState.currentPage = targetPage;
        // 使用静默查询
        silentSearch();
    }

    function changeProductTestPageSize(size) {
        const newSize = parseInt(size);
        if (isNaN(newSize) || newSize === productTestPageState.pageSize) return;
        
        productTestPageState.pageSize = newSize;
        productTestPageState.currentPage = 1;
        // 使用静默查询
        silentSearch();
    }

    function toggleProductTestColumnSettings() {
        const modal = document.getElementById('product-test-column-settings-modal');
        if (!modal) return;

        // 检查所有列的状态来设置全选框
        const allChecked = PRODUCT_TEST_COLUMNS.every(col => productTestColumnState[col]);
        
        // 设置全选框状态
        const selectAll = document.getElementById('product-test-select-all-columns');
        if (selectAll) {
            selectAll.checked = allChecked;
            selectAll.onchange = () => toggleAllProductTestColumns();
        }
        
        // 设置各列复选框状态
        PRODUCT_TEST_COLUMNS.forEach(col => {
            const checkbox = document.getElementById(`col-product-test-${col}`);
            if (checkbox) {
                checkbox.checked = productTestColumnState[col];
                checkbox.onchange = () => updateProductTestSelectAllState();
            }
        });

        modal.style.display = 'block';
    }

    function closeProductTestColumnSettings() {
        const modal = document.getElementById('product-test-column-settings-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    function applyProductTestColumnSettings() {
        // 更新列状态
        PRODUCT_TEST_COLUMNS.forEach(col => {
            const checkbox = document.getElementById(`col-product-test-${col}`);
            if (checkbox) {
                productTestColumnState[col] = checkbox.checked;
            }
        });
        
        // 立即更新表格显示
        if (productTestPageState.data.length > 0) {
            updateProductTestResultsTable(productTestPageState.data);
        }
        
        closeProductTestColumnSettings();
        showToast('列设置已更新', 'success');
    }

    function toggleAllProductTestColumns() {
        const selectAll = document.getElementById('product-test-select-all-columns');
        const isChecked = selectAll.checked;
        
        // 更新所有列复选框状态
        PRODUCT_TEST_COLUMNS.forEach(col => {
            const checkbox = document.getElementById(`col-product-test-${col}`);
            if (checkbox) {
                checkbox.checked = isChecked;
            }
            // 更新列状态(checkbox列除外)
            if (col !== 'checkbox') {
                productTestColumnState[col] = isChecked;
            }
        });
    }

    function updateProductTestSelectAllState() {
        const selectAll = document.getElementById('product-test-select-all-columns');
        const allChecked = PRODUCT_TEST_COLUMNS.every(col => {
            const checkbox = document.getElementById(`col-product-test-${col}`);
            return checkbox ? checkbox.checked : false;
        });
        selectAll.checked = allChecked;
    }

    function resetProductTestForm() {
        const form = document.querySelector('.query-form');
        if (form) {
            form.reset();
        }

        productTestPageState.currentPage = 1;
        productTestPageState.data = [];
        productTestPageState.totalItems = 0;
        productTestPageState.totalPages = 0;
        // 重置排序状态
        productTestPageState.sortField = 'testTime';
        productTestPageState.sortOrder = 'desc';
        // 更新排序图标
        updateSortIconsProductTest();

        const tbody = document.getElementById('product-test-results-tbody');
        if (tbody) {
            tbody.innerHTML = '';
        }

        const tableSection = document.querySelector('.table-section');
        if (tableSection) {
            tableSection.style.display = 'none';
        }

        updateProductTestPagination();
        showToast('查询条件已重置', 'info');

        // 更新导出按钮文本
        updateExportButtonText();
    }

    async function showProductTestDetail(serialNumber) {
        try {
            const encodedSerialNumber = encodeURIComponent(serialNumber);
            const response = await fetch(`/api/product-test-query/detail/${encodedSerialNumber}`);
            const result = await response.json();
            
            if (!result.success) {
                showToast(result.message || '获取详情失败', 'error');
                return;
            }

            // Store the details data
            await updateProductTestDetails(serialNumber, result.data);

            const modal = document.getElementById('product-test-detail-modal');
            if (!modal) {
                Logger.error('找不到模态框素');
                return;
            }

            const modalContent = modal.querySelector('.product-test-query__modal-content');
            const modalHeader = modal.querySelector('.product-test-query__modal-header');
            const modalClose = modal.querySelector('.product-test-query__modal-close');

            // 重置模态框的位置和式
            function resetModalPosition() {
                modalContent.style.transform = 'translate(-50%, -50%)';
                modalContent.style.top = '50%';
                modalContent.style.left = '50%';
                modalContent.style.margin = '0';
                modalContent.style.transition = 'none';
            }

            // 每次打开时重置位置
            resetModalPosition();

            // 简化关闭逻辑
            const closeModal = () => {
                modal.style.display = 'none';
                resetModalPosition();
            };

            // 关闭按钮事件
            if (modalClose) {
                modalClose.onclick = closeModal;
            }

            // 点击遮罩层关闭
            modal.onclick = (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            };

            // ESC键关闭
            const escKeyHandler = (e) => {
                if (e.key === 'Escape' && modal.style.display === 'block') {
                    closeModal();
                }
            };
            document.addEventListener('keydown', escKeyHandler);

            // 在显示模态框之前，确保内容已经准备好
            await updateModalContent(result);
            
            // 显示模态框
            modal.style.display = 'block';

            // 显示后调整滚动位置
            const modalBody = modalContent.querySelector('.product-test-query__modal-body');
            if (modalBody) {
                modalBody.scrollTop = 0;
            }

            // 清理数
            return () => {
                document.removeEventListener('keydown', escKeyHandler);
                modalHeader.removeEventListener('mousedown', dragStart);
                document.removeEventListener('mousemove', drag);
                document.removeEventListener('mouseup', dragEnd);
            };
        } catch (error) {
            Logger.error('获取详情失败:', error);
            showToast('获取详情失败，请稍后重试', 'error');
        }
    }

    // 添加更新模态框内容的辅助函数
    async function updateModalContent(result) {
        Logger.log('接收到的详情数据:', result);

        // 更新模态框标题
        const modalTitle = document.querySelector('.product-test-query__modal-title');
        if (modalTitle) {
            // 获取模态框头部
            const modalHeader = modalTitle.parentElement;
            
            // 清除原有的关闭按钮（如果存在）
            const existingCloseBtn = modalHeader.querySelector('.product-test-query__modal-close');
            if (existingCloseBtn) {
                existingCloseBtn.remove();
            }
            
            // 清除原有的导出按钮（如果存在）
            const existingExportBtn = modalHeader.querySelector('.export-detail-btn');
            if (existingExportBtn) {
                existingExportBtn.remove();
            }
            
            // 更新标题文本
            modalTitle.textContent = `${result.productType}测试详细信息`;
            
            // 创建按钮容器
            const buttonContainer = document.createElement('div');
            buttonContainer.className = 'modal-header-buttons';
            buttonContainer.style.cssText = 'display: flex; align-items: center; gap: 8px;';
            
            // 添加导出图片按钮
            const exportButton = document.createElement('button');
            exportButton.className = 'btn btn-primary export-detail-btn';
            exportButton.innerHTML = '<i class="fas fa-image"></i> 导出图片';
            exportButton.onclick = () => exportDetailAsJPG(result.data.basicInfo.serialNumber);
            
            // 添加关闭按钮
            const closeButton = document.createElement('span');
            closeButton.className = 'product-test-query__modal-close';
            closeButton.innerHTML = '&times;';
            closeButton.onclick = () => {
                const modal = document.getElementById('product-test-detail-modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            };
            
            // 将按钮添加到容器中
            buttonContainer.appendChild(exportButton);
            buttonContainer.appendChild(closeButton);
            
            // 将按钮容器添加到模态框头部
            modalHeader.appendChild(buttonContainer);
        }

        // 获取基本信息
        const basicInfo = result.data.basicInfo || {};

        // 创建一个flex容器来放基本信息和设备信息
        const modalBody = document.querySelector('.product-test-query__modal-body');
        if (modalBody) {
            // 首先创建一个flex容器
            modalBody.innerHTML = `
                <div class="product-test-query__info-container" style="display: flex; gap: 24px; margin-top: 32px;">
                    <!-- 基本信息卡片 - 占据2/3宽度 -->
                    <div class="product-test-query__info-card" style="flex: 2;">
                        <h3 class="product-test-query__card-title">基本信息</h3>
                        <div class="product-test-query__info-grid" style="font-size: 14px;">
                            <!-- 基本信息内容将在下面填充 -->
                        </div>
                    </div>

                    <!-- 设备信息卡片 - 占据1/3宽度 -->
                    <div class="product-test-query__info-card product-test-query__info-card--device" style="flex: 1;">
                        <h3 class="product-test-query__card-title">设备信息</h3>
                        <div class="product-test-query__device-info">
                            <!-- 设备信息容将在下面填充 -->
                        </div>
                    </div>
                </div>

                <!-- 测试结果卡片 -->
                <div class="product-test-query__info-card">
                    <div class="product-test-query__card-header" style="display: flex; justify-content: space-between; align-items: end; margin-bottom: 16px; padding-bottom: 0; border-bottom: 2px solid #e6f4ff;">
                        <h3 class="product-test-query__card-title" style="margin: 0; border-bottom: 2px solid transparent; padding-bottom: 12px;">测试结果</h3>
                        <div class="product-test-query__test-tools" style="display: flex; align-items: center; gap: 12px;">
                            <!-- 测试工具按钮将在这里动态添加 -->
                        </div>
                    </div>
                    <div class="product-test-query__test-grid">
                        <!-- 测试结果内容 -->
                    </div>
                    <!-- M区日志内联容器 -->
                    <div id="m-area-log-content-inline" class="m-area-log-inline-container" style="display: none; margin-top: 16px; background: #fafafa; border: 1px solid #d9d9d9; border-radius: 6px; overflow: hidden;">
                        <!-- M区日志内容将在这里显示 -->
                    </div>
                </div>

                <!-- PCBA绑定信息卡片 -->
                <div class="product-test-query__info-card product-test-query__info-card--pcba">
                    <h3 class="product-test-query__card-title">PCBA绑定信息</h3>
                    <div class="product-test-query__info-grid">
                        <!-- PCBA绑定信息内容 -->
                    </div>
                </div>
            `;

            // 更新基本信息内容
            const infoGrid = modalBody.querySelector('.product-test-query__info-grid');
            if (infoGrid) {
                infoGrid.innerHTML = `
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <!-- 第一行：工单号、生产数量 -->
                        <div style="display: flex; gap: 32px;">
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">工单号：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.orderNumber || '-'}</div>
                            </div>
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">生产量：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.workQty || '-'}</div>
                            </div>
                        </div>

                        <!-- 第二行：产品型号、产品编码 -->
                        <div style="display: flex; gap: 32px;">
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">产品型号：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.productModel || '-'}</div>
                            </div>
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">产品编码：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.productCode || '-'}</div>
                            </div>
                        </div>

                        <!-- 第三行：维修次数、返工次数 -->
                        <div style="display: flex; gap: 32px;">
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">维修次数：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.maintenanceCount || '0'}</div>
                            </div>
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">返工次数：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.reworkCount || '0'}</div>
                            </div>
                        </div>

                        <!-- 第四行：产品状态、测试结果 -->
                        <div style="display: flex; gap: 32px;">
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">产品状态：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.productStatus || '-'}</div>
                            </div>
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">测试结果：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.testResult === 'pass' ? '通过' : '不通过'}</div>
                            </div>
                        </div>

                        <!-- 第五行：测试人员、测试时间 -->
                        <div style="display: flex; gap: 32px;">
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">测试人员：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.tester || '-'}</div>
                            </div>
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">测试时间：</div>
                                <div style="margin-left: 8px; flex: 1;">${formatDate(basicInfo.testTime) || '-'}</div>
                            </div>
                        </div>

                        <!-- 新增：比对人员、比对时间 -->
                        <div style="display: flex; gap: 32px;">
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">比对人员：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.comparisonUser || '-'}</div>
                            </div>
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">比对时间：</div>
                                <div style="margin-left: 8px; flex: 1;">${formatDate(basicInfo.comparisonTime) || '-'}</div>
                            </div>
                        </div>

                        <!-- 第六行：产品批次号、备注 -->
                        <div style="display: flex; gap: 32px;">
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">产品批次号：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.productBatch || '-'}</div>
                            </div>
                            <div style="flex: 1; display: flex; white-space: nowrap;">
                                <div style="width: 100px; padding-left: 20px;">备注：</div>
                                <div style="margin-left: 8px; flex: 1;">${basicInfo.remarks || '-'}</div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 更新设备信息内容
            const deviceInfo = modalBody.querySelector('.product-test-query__device-info');
            if (deviceInfo) {
                const deviceData = result.data.deviceInfo || {};
                let deviceInfoHTML = '';

                switch (result.productType) {
                    case 'CPU控制器':
                        deviceInfoHTML = `
                            <div style="display: flex; flex-direction: column; gap: 16px; font-size: 14px;">
                                <div>设备名称：${deviceData.deviceName || '-'}</div>
                                <div>序列号：${deviceData.serialNumber || '-'}</div>
                                <div>背板版本：${deviceData.backplaneVersion || '-'}</div>
                                <div>高速脉冲：${deviceData.highSpeedPulse || '-'}</div>
                                <div>软件版本：${deviceData.softwareVersion || '-'}</div>
                                <div>构建日期：${deviceData.buildDate || '-'}</div>
                            </div>
                        `;
                        break;
                    case '耦合器':
                        deviceInfoHTML = `
                            <div style="display: flex; flex-direction: column; gap: 16px; font-size: 14px;">
                                <div>耦合器软件版本：${deviceData.couplerVersion || '-'}</div>
                                <div>耦合器构建日期：${deviceData.couplerBuildDate || '-'}</div>
                            </div>
                        `;
                        break;
                    case 'IO模块':
                        deviceInfoHTML = `
                            <div style="display: flex; flex-direction: column; gap: 16px; font-size: 14px;">
                                <div>IO软件版本：${deviceData.couplerVersion || '-'}</div>
                                <div>IO构建日期：${deviceData.couplerBuildDate || '-'}</div>
                            </div>
                        `;
                        break;
                }

                deviceInfo.innerHTML = deviceInfoHTML;
            }

            // 更新测试结果内容
            const testGrid = modalBody.querySelector('.product-test-query__test-grid');
            if (testGrid) {
                const testResults = result.data.testResults || {};
                
                // 定义测试项目的显示顺序
                const testOrder = [
                    'rs485_1',     // RS485_1
                    'rs232',       // RS232
                    'canbus',      // CANbus
                    'ethercat',    // EtherCAT
                    'backplane',   // 背板通信
                    'bodyIO',      // Body I/O
                    'ledBulb',     // LED灯珠
                    'ledTube',     // LED数码管
                    'dipSwitch',   // 拨码开关
                    'netPort',     // 网口
                    'debugPort',   // 调试串口
                    'sdSlot',      // SD卡槽
                    'usbDrive',    // USB接口
                    'resetBtn'     // 复位按钮
                ];

                // 按指定顺序创建测试项目数组
                let testItemsToShow = [];
                testOrder.forEach(key => {
                    if (testResults.hasOwnProperty(key)) {
                        const value = testResults[key];
                        // 根据产品类型进行过滤
                        if (result.productType === 'IO模块') {
                            // IO 模块：去掉 netPort、ledTube，并隐藏值为 0 的测试项
                            if (key !== 'netPort' && key !== 'ledTube' && value !== 0) {
                                testItemsToShow.push([key, value]);
                            }
                        } else if (result.productType === 'CPU控制器') {
                            // CPU 控制器：仅隐藏值为 0 的测试项
                            if (value !== 0) {
                                testItemsToShow.push([key, value]);
                            }
                        } else if (result.productType === '耦合器') {
                            // 耦合器：仅隐藏值为 0 的测试项
                            if (value !== 0) {
                                testItemsToShow.push([key, value]);
                            }
                        }
                    }
                });

                let testResultsHTML = testItemsToShow
                    .map(([key, value]) => {
                        const testCode = getTestCodeByKey(key);
                        const isAuto = isAutoTest(testCode, result.data.basicInfo.test_auto_info);
                        
                        return `
                            <div class="product-test-query__test-item">
                                <label class="product-test-query__test-label">${formatTestItemName(key)}</label>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span class="product-test-query__test-value product-test-query__test-value--${value === 1 ? 'pass' : 'fail'}">
                                        ${value === 1 ? '通过' : '不通过'}
                                    </span>
                                    ${isAuto ? `<span class="auto-tag" style="background: #e6f7ff; color: #1890ff; padding: 1px 6px; border-radius: 3px; font-size: 10px; font-weight: bold; border: 1px solid #91d5ff;">
                                        <i class="fas fa-robot" style="margin-right: 2px;"></i>AUTO
                                    </span>` : ''}
                                </div>
                            </div>
                        `;
                    }).join('');

                testGrid.innerHTML = testResultsHTML;
            }
            
            // 为CPU控制器添加测试工具栏
            if (result.productType === 'CPU控制器') {
                const testToolsContainer = modalBody.querySelector('.product-test-query__test-tools');
                if (testToolsContainer) {
                    // 获取auto统计信息
                    const autoStats = getAutoStatistics(result.data.basicInfo.test_auto_info);
                    
                    testToolsContainer.innerHTML = `
                        ${autoStats.total > 0 ? `
                            <span style="color: #666; font-size: 12px; display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-chart-bar" style="color: #1890ff;"></i>
                                自动${autoStats.autoCount}项 手动${autoStats.manualCount}项
                            </span>
                        ` : ''}
                        <button class="m-area-log-toggle-btn" onclick="showFullMAreaLogModal('${result.data.basicInfo.serialNumber}')" 
                                style="background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%); color: white; border: none; padding: 4px 10px; border-radius: 4px; font-size: 11px; cursor: pointer; display: flex; align-items: center; gap: 4px; box-shadow: 0 2px 4px rgba(24,144,255,0.2);">
                            <i class="fas fa-lock" style="font-size: 9px;"></i>
                            <span>测试日志</span>
                        </button>
                    `;
                }
            }

            // 更新PCBA绑定信息
            const pcbaInfo = result.data.pcbaBindingInfo || {};
            const pcbaGrid = document.querySelector('.product-test-query__info-card--pcba .product-test-query__info-grid');
            if (pcbaGrid) {
                // 定义需要显示的PCBA信息字段
                const pcbaFields = [
                    { label: '单板组装人员', value: pcbaInfo.boardTester, group: 'assembly' },
                    { label: '单板组装时间', value: pcbaInfo.boardAssembledTime, isDate: true, group: 'assembly' },
                    { label: '外壳组装人员', value: pcbaInfo.shellTester, group: 'assembly' },
                    { label: '外壳组装时间', value: pcbaInfo.shellAssembledTime, isDate: true, group: 'assembly' },
                    { label: '组装ID', value: pcbaInfo.assemblyId },
                    { label: 'SN号', value: pcbaInfo.productSN },
                    { label: '板子A序列号', value: pcbaInfo.boardASN },
                    { label: '板子B序列号', value: pcbaInfo.boardBSN },
                    { label: '板子C序列号', value: pcbaInfo.boardCSN },
                    { label: '板子D序列号', value: pcbaInfo.boardDSN }
                ];

                // 过滤出有值的字段
                const validFields = pcbaFields.filter(field => {
                    if (field.isDate) {
                        return field.value && field.value !== '0001-01-01T00:00:00' && field.value !== 'Invalid Date';
                    }
                    return field.value && field.value !== '-' && field.value !== '';
                });

                if (validFields.length > 0) {
                    // 分离组装信息和其他信息
                    const assemblyFields = validFields.filter(field => field.group === 'assembly');
                    const otherFields = validFields.filter(field => !field.group);

                    pcbaGrid.innerHTML = `
                        <div style="display: flex; flex-direction: column; gap: 16px; font-size: 14px;">
                            <!-- 第一行：组装相关信息 -->
                            ${assemblyFields.length > 0 ? `
                                <div style="display: flex; gap: 32px; flex-wrap: wrap;">
                                    ${assemblyFields.map(field => `
                                        <div>${field.label}：${field.isDate ? formatDate(field.value) : field.value}</div>
                                    `).join('')}
                                </div>
                            ` : ''}

                            <!-- 其他信息每项单独一行 -->
                            ${otherFields.map(field => `
                                <div>${field.label}：${field.value}</div>
                            `).join('')}
                        </div>
                    `;
                } else {
                    // 当没有有效的PCBA字段时，显示SN号
                    pcbaGrid.innerHTML = `
                        <div style="font-size: 14px;">
                            <div style="color: red; margin-bottom: 8px;">该产品未绑定PCBA</div>
                            <div>SN号：${basicInfo.serialNumber || '-'}</div>
                        </div>
                    `;
                }
            }
        }
    }

    // 在 exportProductTestData 函数之前添加一个新的函数
    async function fetchAllDetailsForExport() {
        const allData = productTestPageState.data;
        showToast('正在获取详细数据...', 'info');
        
        try {
            // 使用 Promise.all 并发获取所有详情数据
            const detailPromises = allData.map(async (item) => {
                try {
                    const response = await fetch(`/api/product-test-query/detail/${item.serialNumber}`);
                    const result = await response.json();
                    if (result.success) {
                        // 将详情数据合并到原数据中
                        return {
                            ...item,
                            detail: result.data
                        };
                    }
                    return item;
                } catch (error) {
                    Logger.error(`获取 ${item.serialNumber} 的详情失败:`, error);
                    return item;
                }
            });

            const detailedData = await Promise.all(detailPromises);
            productTestPageState.data = detailedData;
            return true;
        } catch (error) {
            Logger.error('获取详情数据失败:', error);
            showToast('获取详情数据失败', 'error');
            return false;
        }
    }

    // 修改 exportProductTestData 函数
    async function exportProductTestData() {
        try {
            // 获取选中的行数据
            const selectedRows = getSelectedRowsData();
            let dataToExport;
            
            if (selectedRows.length > 0) {
                // 如果有选中的行，使用选中的数据
                dataToExport = selectedRows;
            } else {
                // 如果没有选中的行，获取所有数据（不页）
                const orderNumber = document.getElementById('product-test-order-number').value.trim();
                const serialNumber = document.getElementById('product-test-model').value.trim();
                const testResult = document.getElementById('product-test-result').value;
                const startDate = document.getElementById('product-test-start-date').value;
                const endDate = document.getElementById('product-test-end-date').value;
                
                const params = new URLSearchParams();
                if (orderNumber) params.append('orderNumber', orderNumber);
                if (serialNumber) params.append('serialNumber', serialNumber);
                if (testResult) params.append('testResult', testResult === 'pass' ? 'pass' : 'fail');
                if (startDate) params.append('startDate', startDate);
                if (endDate) params.append('endDate', endDate);
                
                // 设置pageSize为-1，表示获取所有数据
                params.append('pageSize', -1);
                params.append('page', 1);
                
                showToast('正在获取所有数据...', 'info');
                const response = await fetch(`/api/product-test-query/search?${params.toString()}`);
                const result = await response.json();
                
                if (!result.success || !result.data || result.data.length === 0) {
                    showToast('没有可导出的数据', 'warning');
                    return;
                }
                
                dataToExport = result.data;
            }

            if (!dataToExport || dataToExport.length === 0) {
                showToast('没有可导出的数据', 'warning');
                return;
            }

            // 保存原始数据
            const originalData = [...productTestPageState.data];
            
            // 临时替换数据为要导出的数据
            productTestPageState.data = dataToExport;
            
            // 获取详细数据
            const detailsFetched = await fetchAllDetailsForExport();
            if (!detailsFetched) {
                // 恢复原始数据
                productTestPageState.data = originalData;
                return;
            }

            // 修改基础配置项，添加生产量和备注字段
            const baseConfig = [
                { header: '工单号', field: 'orderNumber' },
                { header: 'SN号', field: 'serialNumber', format: 'text' },
                { header: '产品型号', field: 'productModel' },
                { header: '产品编码', field: 'productCode' },
                { header: '产品批次号', field: 'productBatch' },
                { header: '生产量', field: 'detail.basicInfo.workQty' },
                { header: '产品状态', field: 'productStatus' },
                { header: '维修次数', field: 'maintenanceCount', format: 'number' },
                { header: '返工次数', field: 'reworkCount', format: 'number' },
                { header: '测试结果', field: 'testResult', format: 'testResult' },
                { header: '测试人员', field: 'tester' },
                { header: '测试时间', field: 'testTime', format: 'date' },
                { header: '比对结果', field: 'comparisonResult', format: 'comparisonResult' },
                { header: '比对时间', field: 'comparisonTime', format: 'date' },
                { header: '备注', field: 'detail.basicInfo.remarks' },
                { header: '比对人员', field: 'comparisonUser' }
            ];

            // 修改设备信息配置，统一字段名
            const deviceInfoConfig = {
                'CPU控制器': [
                    { header: '设备名称', field: 'detail.deviceInfo.deviceName' },
                    { header: '序列号', field: 'detail.deviceInfo.serialNumber', format: 'text' },
                    { header: '背板版本', field: 'detail.deviceInfo.backplaneVersion' },
                    { header: '高速脉冲', field: 'detail.deviceInfo.highSpeedPulse' },
                    { header: '软件版本', field: 'detail.deviceInfo.softwareVersion' },
                    { header: '构建日期', field: 'detail.deviceInfo.buildDate' }
                ],
                '耦合器': [
                    { header: '耦合器软件版本', field: 'detail.deviceInfo.couplerVersion' },
                    { header: '耦合器构建日期', field: 'detail.deviceInfo.couplerBuildDate' }
                ],
                'IO模块': [
                    // 修改显示名称，但使用相同的字段
                    { header: 'IO软件版本', field: 'detail.deviceInfo.couplerVersion' },
                    { header: 'IO构建日期', field: 'detail.deviceInfo.couplerBuildDate' }
                ]
            };

            // 测试结果配置 - 根据产品类型过滤
            const testResultsConfig = [
                { header: '背板通信', field: 'detail.testResults.backplane', format: 'testItem' },
                { header: 'Body I/O', field: 'detail.testResults.bodyIO', format: 'testItem' },
                { header: 'LED数码管', field: 'detail.testResults.ledTube', format: 'testItem' },
                { header: 'LED灯珠', field: 'detail.testResults.ledBulb', format: 'testItem' },
                { header: '网口', field: 'detail.testResults.netPort', format: 'testItem' },
                { header: 'RS485', field: 'detail.testResults.rs485_1', format: 'testItem' },
                //{ header: 'RS485_2', field: 'detail.testResults.rs485_2', format: 'testItem' },
                { header: 'RS232', field: 'detail.testResults.rs232', format: 'testItem' },
                { header: 'CANbus', field: 'detail.testResults.canbus', format: 'testItem' },
                { header: 'EtherCAT', field: 'detail.testResults.ethercat', format: 'testItem' },
                { header: 'USB接口', field: 'detail.testResults.usbDrive', format: 'testItem' },
                { header: 'SD卡槽', field: 'detail.testResults.sdSlot', format: 'testItem' },
                { header: '调试串口', field: 'detail.testResults.debugPort', format: 'testItem' },
                { header: '拨码开关', field: 'detail.testResults.dipSwitch', format: 'testItem' },
                { header: '复位按钮', field: 'detail.testResults.resetBtn', format: 'testItem' }
            ];

            // PCBA绑定信息配置
            const pcbaConfig = [
                { header: '组装ID', field: 'detail.pcbaBindingInfo.assemblyId' },
                { header: '板子A序列号', field: 'detail.pcbaBindingInfo.boardASN', format: 'text' },
                { header: '板子B序列号', field: 'detail.pcbaBindingInfo.boardBSN', format: 'text' },
                { header: '板子C序列号', field: 'detail.pcbaBindingInfo.boardCSN', format: 'text' },
                { header: '板子D序列号', field: 'detail.pcbaBindingInfo.boardDSN', format: 'text' },
                { header: '单板组装人员', field: 'detail.pcbaBindingInfo.boardTester' },
                { header: '单板组装时间', field: 'detail.pcbaBindingInfo.boardAssembledTime', format: 'date' },
                { header: '外壳组装人员', field: 'detail.pcbaBindingInfo.shellTester' },
                { header: '外壳组装时间', field: 'detail.pcbaBindingInfo.shellAssembledTime', format: 'date' }
            ];

            // 根据第一条记录的产品类型确定设备信息配置
            const productType = productTestPageState.data[0]?.detail?.productType || 'CPU控制器';
            const deviceConfig = deviceInfoConfig[productType] || deviceInfoConfig['CPU控制器'];

            // 根据产品类型过滤测试结果配置
            let filteredTestConfig = testResultsConfig;
            if (productType === 'IO模块') {
                filteredTestConfig = testResultsConfig.filter(config => 
                    !['netPort', 'ledTube'].includes(config.field.split('.').pop())
                );
            }

            // 合并所有配置
            const exportConfig = [
                ...baseConfig,
                ...deviceConfig,
                ...filteredTestConfig,
                ...pcbaConfig
            ];

            // 提取表头
            const headers = exportConfig.map(config => config.header);

            // 处理数据行
            const rows = productTestPageState.data.map(item => {
                return exportConfig.map(config => {
                    // 获取嵌套对象的值
                    const getValue = (obj, path) => {
                        const value = path.split('.').reduce((acc, part) => {
                            // 明确检查值是否为 undefined 或 null，但保留 0
                            return acc && (acc[part] !== undefined && acc[part] !== null) ? acc[part] : null;
                        }, obj);
                        
                        // 明确处理数值 0 的情况
                        return value === 0 ? 0 : (value ?? null);  // 使用 ?? 运符
                    };

                    let value = getValue(item, config.field);

                    // 根据不同的格式处理数据
                    switch (config.format) {
                        case 'date':
                            value = value ? formatDate(value) : '-';
                            break;
                        case 'text':
                            value = value ? `\t${value}` : '-';
                            break;
                        case 'testResult':
                            value = value === 'pass' ? '通过' : '不通过';
                            break;
                        case 'comparisonResult':
                            value = value === 'PASS' ? '通过' : (value || '-');
                            break;
                        case 'testItem':
                            // 明确处理所有可能的测试结果值
                            if (typeof value === 'boolean') {
                                value = value ? '通过' : '不通过';
                            } else if (typeof value === 'number') {
                                // 明确处理 0 和 1 的情况
                                value = value === 1 ? '通过' : '不通过';
                            } else {
                                value = '-';
                            }
                            break;
                        case 'number':
                            // 专门处理数字类型，确保 0 被正确显示
                            value = (value === 0 || value) ? value.toString() : '-';
                            break;
                        default:
                            // 对于普通字段，保留 0 值
                            value = (value === 0 || value) ? value : '-';
                    }

                    return value;
                });
            });

            // 生成CSV内容
            const worksheet = [headers, ...rows];
            const csvContent = '\uFEFF' + worksheet.map(row => 
                row.map(cell => 
                    `"${(cell || '').toString().replace(/"/g, '""')}"`
                ).join(',')
            ).join('\n');

            // 创建并下载文件
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `成品测试记录_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 恢复原始数据
            productTestPageState.data = originalData;

            showToast('导出成功', 'success');
        } catch (error) {
            Logger.error('导出失败:', error);
            showToast('导出失败，请稍后重试', 'error');
        }
    }

    function refreshProductTestTable() {
        if (!productTestPageState.data || productTestPageState.data.length === 0) {
            showToast('没有数据可刷新', 'warning');
            return;
        }
        searchProductTest();
    }

    function toggleProductTestSelectAll() {
        const selectAll = document.getElementById('product-test-select-all');
        const checkboxes = document.querySelectorAll('#product-test-results-tbody input[type="checkbox"]');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        // 更新导出按钮的状态提示
        updateExportButtonText();
    }

    // 添加新函数：更新导出按钮文本
    function updateExportButtonText() {
        const selectedCount = getSelectedRowsData().length;
        const exportButton = document.querySelector('.btn-export');
        if (exportButton) {
            if (selectedCount > 0) {
                exportButton.innerHTML = `<i class="fas fa-download"></i> 导出选中(${selectedCount})`;
            } else {
                exportButton.innerHTML = `<i class="fas fa-download"></i> 导出数据`;
            }
        }
    }

    // 添加独立的工具函，不再依赖BatchQuery.js
    function formatDate(dateString) {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    function showToast(message, type = 'info') {
        // 创建容器（如果不存在）
        let toastContainer = document.querySelector('.product-test-query__toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'product-test-query__toast-container';
            document.body.appendChild(toastContainer);
        }
        
        // 创建新的 toast 元素
        const toast = document.createElement('div');
        toast.className = `product-test-query__toast product-test-query__toast--${type}`;
        
        // 设置图标
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="fas fa-check-circle"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-times-circle"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-circle"></i>';
                break;
            case 'info':
                icon = '<i class="fas fa-info-circle"></i>';
                break;
        }
        
        // 设置内容
        toast.innerHTML = `
            <div class="product-test-query__toast-content">
                ${icon}
                <span>${message}</span>
            </div>
        `;
        
        // 添加到容器
        toastContainer.appendChild(toast);
        
        // 触发动画
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });
        
        // 设置自动移除
        setTimeout(() => {
            toast.classList.remove('show');
            toast.classList.add('hide');
            
            // 动画结束后移除元素
            setTimeout(() => {
                toast.remove();
                // 如容器为空，也移除容器
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            }, 300);
        }, 3000);
    }

    // 加触摸事件处理函
    function handler(event) {
        // 触摸事件默认处理
        return true;
    }

    // 添加辅助函数用格式化测试目名称
    function formatTestItemName(key) {
        const nameMap = {
            'backplane': '背板通信',
            'bodyIO': 'Body I/O',
            'ledTube': 'LED数码管',
            'ledBulb': 'LED灯珠',
            'netPort': '网口',
            'rs485_1': 'RS485',
            'rs485_2': 'RS485_2',
            'rs232': 'RS232',
            'canbus': 'CANbus',
            'ethercat': 'EtherCAT',
            'usbDrive': 'USB接口',
            'sdSlot': 'SD卡槽',
            'debugPort': '调试串口',
            'dipSwitch': '拨码开关',
            'resetBtn': '复位按钮'
        };
        return nameMap[key] || key;
    }

    // 修改全局函数导出，移除共用函数的引用
    window.initProductTestQueryPage = initProductTestQueryPage;
    window.searchProductTest = searchProductTest;
    window.resetProductTestForm = resetProductTestForm;
    window.showProductTestDetail = showProductTestDetail;
    window.toggleProductTestColumnSettings = toggleProductTestColumnSettings;
    window.closeProductTestColumnSettings = closeProductTestColumnSettings;
    window.applyProductTestColumnSettings = applyProductTestColumnSettings;
    window.exportProductTestData = exportProductTestData;
    window.refreshProductTestTable = refreshProductTestTable;
    window.toggleProductTestSelectAll = toggleProductTestSelectAll;
    window.changeProductTestPage = changeProductTestPage;
    window.jumpToProductTestPage = jumpToProductTestPage;
    window.changeProductTestPageSize = changeProductTestPageSize;
    window.toggleAllProductTestColumns = toggleAllProductTestColumns;
    window.updateProductTestSelectAllState = updateProductTestSelectAllState;
    window.sortByProductTest = sortByProductTest; // 导出排序函数

    // 添加页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 尝试初始化页面
        const maxRetries = 5;
        let retryCount = 0;
        
        function tryInitialize() {
            const contentElement = document.getElementById('product-test-query-content');
            if (contentElement) {
                Logger.log('成品测试查询页面初始化开始...');
                initProductTestQueryPage();
            } else if (retryCount < maxRetries) {
                Logger.log(`等待内容元素加载... (尝试 ${retryCount + 1}/${maxRetries})`);
                retryCount++;
                setTimeout(tryInitialize, 200);
            } else {
                Logger.error('无法找到 product-test-query-content 元素，页面初始化失败');
            }
        }

        tryInitialize();
    });

    // Add this function to store details data
    async function updateProductTestDetails(serialNumber, detailData) {
        // 先打印数据结构以便调试
        Logger.log('Detail data:', detailData);
        
        // Find the item in the data array
        const itemIndex = productTestPageState.data.findIndex(item => item.serialNumber === serialNumber);
        if (itemIndex !== -1) {
            // 直接将详情数据合并到现有数据中
            productTestPageState.data[itemIndex] = {
                ...productTestPageState.data[itemIndex],
                ...detailData  // 直接合并整个详情数据对象
            };
            
            // 打印合并后的数据以便验证
            Logger.log('Updated item:', productTestPageState.data[itemIndex]);
        }
    }

    // 修改 getSelectedRowsData 函数
    function getSelectedRowsData() {
        const tbody = document.getElementById('product-test-results-tbody');
        if (!tbody) return [];

        const selectedRows = [];
        const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
        const currentPageData = productTestPageState.data;

        checkboxes.forEach((checkbox, index) => {
            if (checkbox.checked && currentPageData[index]) {
                selectedRows.push(currentPageData[index]);
            }
        });

        return selectedRows;
    }

    // 添加新的函数用于绑定复选框事件
    function bindCheckboxEvents() {
        const tbody = document.getElementById('product-test-results-tbody');
        if (!tbody) return;

        // 绑定单个复选事件
        const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                // 更新全选框状态
                const selectAll = document.getElementById('product-test-select-all');
                const allCheckboxes = tbody.querySelectorAll('input[type="checkbox"]');
                const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
                selectAll.checked = allChecked;
                
                // 更新导出按钮文本
                updateExportButtonText();
            });
        });

        // 绑定全选框事件
        const selectAll = document.getElementById('product-test-select-all');
        if (selectAll) {
            selectAll.addEventListener('change', () => {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAll.checked;
                });
                updateExportButtonText();
            });
        }
    }

    // 添加一个新的函数用于静默查询
    async function silentSearch() {
        try {
            // 获取当前的查询条件
            const orderNumber = document.getElementById('product-test-order-number').value.trim();
            const serialNumber = document.getElementById('product-test-model').value.trim();
            const productCode = document.getElementById('product-test-code').value.trim(); // 获取产品编码
            const testResult = document.getElementById('product-test-result').value;
            const startDate = document.getElementById('product-test-start-date').value;
            const endDate = document.getElementById('product-test-end-date').value;

            // 如果没有任何查询条件，或者没有之前的查询结果，则不执行查询
            if ((!orderNumber && !serialNumber && !productCode && !testResult && !startDate && !endDate) || 
                !productTestPageState.data.length) {
                return;
            }

            const params = new URLSearchParams();
            if (orderNumber) params.append('orderNumber', orderNumber);
            if (serialNumber) params.append('serialNumber', serialNumber);
            if (productCode) params.append('productCode', productCode); // 添加产品编码参数
            if (testResult) {
                params.append('testResult', testResult === 'pass' ? 'pass' : 'fail');
            }
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);
            
            // 添加分页参数
            params.append('page', productTestPageState.currentPage);
            params.append('pageSize', productTestPageState.pageSize);
            
            // 添加排序参数
            params.append('sortField', productTestPageState.sortField);
            params.append('sortOrder', productTestPageState.sortOrder);

            const response = await fetch(`/api/product-test-query/search?${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                if (result.data && result.data.length > 0) {
                    productTestPageState.data = result.data;
                    productTestPageState.totalItems = result.totalCount || result.data.length;
                    productTestPageState.currentPage = result.page || 1;
                    productTestPageState.pageSize = result.pageSize || 10;
                    productTestPageState.totalPages = Math.ceil(productTestPageState.totalItems / productTestPageState.pageSize);
                    
                    updateProductTestResultsTable(result.data);
                }
            }
        } catch (error) {
            Logger.error('分页查询失败:', error);
        }
    }

    // 获取并填充测试日志到导出内容（仅CPU控制器）
    async function includeTestLogForExport(modalBody, serialNumber) {
        try {
            // 检查是否为CPU控制器产品且存在日志容器
            const logContainer = modalBody.querySelector('#m-area-log-content-inline');
            const isLogContainerExists = logContainer && logContainer.style.display === 'none';
            
            if (!isLogContainerExists) return false;
            
            // 获取M区测试日志
            const response = await fetch(`/api/cpu-controller-vue/get-m-area-log?pro_sn=${encodeURIComponent(serialNumber)}`);
            const result = await response.json();
            
            if (result.success && result.has_log) {
                // 使用完整版日志格式化函数，提供专业详细的日志内容
                const logContent = formatMAreaLogContent(result.log_data, serialNumber, result.test_time, result.tester);
                logContainer.innerHTML = logContent;
                logContainer.style.display = 'block';
                return true;
            }
            return false;
        } catch (error) {
            Logger.warn('获取测试日志失败，将跳过日志内容:', error);
            return false;
        }
    }

    // 添加导出JPG功能
    async function exportDetailAsJPG(serialNumber) {
        try {
            showToast('正在生成图片...', 'info');
            
            const modalBody = document.querySelector('.product-test-query__modal-body');
            if (!modalBody) {
                throw new Error('找不到要导出的内容');
            }
            
            // 记录原始日志容器状态用于恢复
            const logContainer = modalBody.querySelector('#m-area-log-content-inline');
            const originalLogDisplay = logContainer ? logContainer.style.display : null;
            const originalLogContent = logContainer ? logContainer.innerHTML : null;
            
            try {
                // 尝试包含测试日志
                const logIncluded = await includeTestLogForExport(modalBody, serialNumber);
                if (logIncluded) {
                    showToast('已包含测试日志，正在生成图片...', 'info');
                }
                
                // 创建临时容器
                const exportDiv = document.createElement('div');
                exportDiv.className = 'export-detail-container';
                exportDiv.style.cssText = `
                    position: absolute;
                    left: -9999px;
                    top: -9999px;
                    background: white;
                    width: ${modalBody.offsetWidth}px;
                    padding: 20px;
                    font-family: Arial, sans-serif;
                    height: auto;
                    overflow: visible;
                `;
                
                // 复制内容
                exportDiv.innerHTML = modalBody.innerHTML;
                
                // ===== 关键修复：展开所有滚动日志容器 =====
                const expandLogContainers = (root) => {
                    if (!root) return;
                    const selectors = ['.timeline-format', '.m-area-log-inline-container', '#m-area-log-content-inline'];
                    selectors.forEach(sel => {
                        const nodes = root.querySelectorAll(sel);
                        nodes.forEach(node => {
                            node.style.maxHeight = 'none';
                            node.style.overflow = 'visible';
                            node.style.height = 'auto';
                        });
                    });
                };
                // 对临时导出节点立即展开
                expandLogContainers(exportDiv);
                document.body.appendChild(exportDiv);

                // 配置html2canvas
                const options = {
                    scale: 2,
                    useCORS: true,
                    backgroundColor: '#ffffff',
                    logging: false,
                    onclone: (clonedDoc) => {
                        const clonedDiv = clonedDoc.querySelector('.export-detail-container');
                        if (clonedDiv) {
                            // 优化表格样式
                            const tables = clonedDiv.querySelectorAll('table');
                            tables.forEach(table => {
                                table.style.borderCollapse = 'collapse';
                                table.style.width = '100%';
                            });
                            
                            // 优化文本样式
                            clonedDiv.style.color = '#333';
                            clonedDiv.style.fontSize = '14px';

                            // 确保PCBA绑定信息卡片可见
                            const pcbaCard = clonedDiv.querySelector('.product-test-query__info-card--pcba');
                            if (pcbaCard) {
                                pcbaCard.style.display = 'block';
                                pcbaCard.style.visibility = 'visible';
                                pcbaCard.style.height = 'auto';
                                pcbaCard.style.overflow = 'visible';
                                pcbaCard.style.opacity = '1';
                                pcbaCard.style.paddingBottom = '0';
                                pcbaCard.style.marginBottom = '0';
                            }

                            // 防止测试统计文本换行
                            const testStats = clonedDiv.querySelector('.product-test-query__test-tools span');
                            if (testStats) {
                                testStats.style.whiteSpace = 'nowrap';
                                testStats.style.fontSize = '11px';  // 略微减小字体以确保单行
                                testStats.style.gap = '6px';  // 减小间隙
                            }

                            // 移除多余空白
                            clonedDiv.style.margin = '0';
                            clonedDiv.style.padding = '20px 20px 0 20px';
                        }
                    }
                };

                // 延迟执行以确保渲染完成
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 设置精确的捕捉尺寸，不手动乘以scale
                options.width = exportDiv.offsetWidth;
                options.height = exportDiv.offsetHeight;

                // 生成图片
                const canvas = await html2canvas(exportDiv, options);
                const imgData = canvas.toDataURL('image/jpeg', 0.95);
                
                // 下载图片
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const link = document.createElement('a');
                link.download = `${serialNumber}_测试详情_${timestamp}.jpg`;
                link.href = imgData;
                
                document.body.appendChild(link);
                link.click();
                
                // 清理临时元素
                document.body.removeChild(link);
                document.body.removeChild(exportDiv);
                
                showToast('图片导出成功', 'success');
                
            } finally {
                // 恢复原始日志容器状态
                if (logContainer && originalLogDisplay !== null) {
                    logContainer.style.display = originalLogDisplay;
                    if (originalLogContent !== null) {
                        logContainer.innerHTML = originalLogContent;
                    }
                }
            }
            
        } catch (error) {
            Logger.error('图片导出失败:', error);
            showToast('图片导出失败，请稍后重试', 'error');
        }
    }

    // 添加样式
    const exportDetailStyle = document.createElement('style');
    exportDetailStyle.textContent = `
        .export-detail-btn {
            margin-left: 16px;
            padding: 6px 12px;
            font-size: 14px;
            border-radius: 4px;
            background-color: #1890ff;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        .export-detail-btn:hover {
            background-color: #40a9ff;
        }
        
        .export-detail-btn i {
            margin-right: 6px;
        }
        
        .export-detail-container table {
            border: 1px solid #ddd;
            margin-bottom: 16px;
        }
        
        .export-detail-container td,
        .export-detail-container th {
            border: 1px solid #ddd;
            padding: 8px;
        }

        /* M区日志相关样式 */
        .product-test-query__test-item--m-area {
            border: 1px solid #d9f7be;
            background: #f6ffed;
            padding: 12px;
            border-radius: 6px;
            margin-top: 8px;
        }

        .m-area-log-toggle-btn:hover {
            background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
        }

        .m-area-log-toggle-btn:disabled {
            background: #f5f5f5 !important;
            color: #bfbfbf !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
        }

        .m-area-log-inline-container {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                max-height: 500px;
                transform: translateY(0);
            }
        }

        .m-area-log-compact {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .m-area-log-compact:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: box-shadow 0.3s ease;
        }
    `;
    document.head.appendChild(exportDetailStyle);

    // 添加排序函数
    function sortByProductTest(field) {
        if (productTestPageState.sortField === field) {
            // 如果点击的是当前排序字段，则切换排序方向
            productTestPageState.sortOrder = productTestPageState.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果点击的是新字段，则更新排序字段并默认使用降序
            productTestPageState.sortField = field;
            productTestPageState.sortOrder = 'desc';
        }

        // 更新排序图标
        updateSortIconsProductTest();

        // 重置到第一页
        productTestPageState.currentPage = 1;

        // 重新加载数据
        silentSearch();
    }

    // 更新排序图标
    function updateSortIconsProductTest() {
        // 获取所有排序图标
        const sortableHeaders = document.querySelectorAll('.sortable');
        if (!sortableHeaders.length) return;

        // 遍历所有可排序的表头
        sortableHeaders.forEach(header => {
            // 获取排序图标
            const icon = header.querySelector('i.fas');
            if (!icon) return;

            // 获取字段名
            const fieldName = header.getAttribute('onclick').match(/sortByProductTest\('(.+?)'\)/)[1];

            // 更新图标样式
            if (fieldName === productTestPageState.sortField) {
                // 当前排序字段
                if (productTestPageState.sortOrder === 'asc') {
                    icon.className = 'fas fa-sort-up';
                } else {
                    icon.className = 'fas fa-sort-down';
                }
            } else {
                // 非当前排序字段
                icon.className = 'fas fa-sort';
            }
        });
    }

    // 新增：判断测试项目是否为自动测试
    function isAutoTest(testCode, autoInfo) {
        try {
            if (!autoInfo) return false;
            const parsedAutoInfo = typeof autoInfo === 'string' ? JSON.parse(autoInfo) : autoInfo;
            return parsedAutoInfo[testCode] === true;
        } catch (error) {
            console.warn('解析auto信息失败:', error);
            return false;
        }
    }

    // 新增：获取auto统计信息
    function getAutoStatistics(autoInfo) {
        try {
            if (!autoInfo) {
                return { autoCount: 0, manualCount: 0, total: 0 };
            }
            const parsedAutoInfo = typeof autoInfo === 'string' ? JSON.parse(autoInfo) : autoInfo;
            const autoCount = Object.values(parsedAutoInfo).filter(v => v).length;
            const manualCount = Object.values(parsedAutoInfo).filter(v => !v).length;
            return { autoCount, manualCount, total: autoCount + manualCount };
        } catch (error) {
            console.warn('获取auto统计失败:', error);
            return { autoCount: 0, manualCount: 0, total: 0 };
        }
    }

    // 新增：测试项目代码到名称的映射
    const TEST_CODE_TO_NAME_MAP = {
        'rs485_1': 'RS485_通信',
        'rs232': 'RS232通信', 
        'canbus': 'CANbus通信',
        'ethercat': 'EtherCAT通信',
        'backplane_bus': 'Backplane Bus通信',
        'body_io': 'Body I/O输入输出',
        'led_tube': 'Led数码管',
        'led_bulb': 'Led灯珠',
        'usb_drive': 'U盘接口',
        'sd_slot': 'SD卡',
        'debug_port': '调试串口',
        'net_port': '网口',
        'dip_switch': '拨码开关',
        'reset_btn': '复位按钮'
    };

    // 新增：根据测试项目key获取对应的code
    function getTestCodeByKey(testKey) {
        // 将数据库字段名转换为测试项目code
        const keyToCodeMap = {
            'rs485_1': 'rs485_1',
            'rs485': 'rs485_1', // 兼容性处理
            'rs232': 'rs232',
            'canbus': 'canbus', 
            'ethercat': 'ethercat',
            'backplane': 'backplane_bus',
            'bodyIo': 'body_io',
            'bodyIO': 'body_io',
            'body_io': 'body_io',
            'ledTube': 'led_tube',
            'led_tube': 'led_tube',
            'ledBulb': 'led_bulb',
            'led_bulb': 'led_bulb',
            'usbDrive': 'usb_drive',
            'usb_drive': 'usb_drive',
            'sdSlot': 'sd_slot',
            'sd_slot': 'sd_slot',
            'debugPort': 'debug_port',
            'debug_port': 'debug_port',
            'netPort': 'net_port',
            'net_port': 'net_port',
            'dipSwitch': 'dip_switch',
            'dip_switch': 'dip_switch',
            'resetBtn': 'reset_btn',
            'reset_btn': 'reset_btn'
        };
        return keyToCodeMap[testKey] || testKey;
    }

    
    // 新增：在模态框内切换M区日志显示
    async function toggleMAreaLogInModal(serialNumber) {
        const container = document.getElementById('m-area-log-content-inline');
        const toggleBtn = document.querySelector('.m-area-log-toggle-btn');
        
        if (!container || !toggleBtn) return;
        
        if (container.style.display === 'none') {
            // 显示M区日志
            try {
                // 更新按钮状态
                toggleBtn.innerHTML = `
                    <i class="fas fa-spinner fa-spin" style="font-size: 10px;"></i>
                    <span>加载中...</span>
                `;
                toggleBtn.disabled = true;
                
                const response = await fetch(`/api/cpu-controller-vue/get-m-area-log?pro_sn=${encodeURIComponent(serialNumber)}`);
                const result = await response.json();
                
                if (result.success && result.has_log) {
                    // 创建紧凑版的M区日志内容
                    const logContent = createCompactMAreaLogContent(result.log_data, serialNumber, result.test_time, result.tester);
                    container.innerHTML = logContent;
                    container.style.display = 'block';
                    
                    // 更新按钮状态
                    toggleBtn.innerHTML = `
                        <i class="fas fa-eye-slash" style="font-size: 10px;"></i>
                        <span>隐藏日志</span>
                    `;
                    toggleBtn.disabled = false;
                    
                    // 平滑滚动到日志区域
                    setTimeout(() => {
                        container.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }, 100);
                } else {
                    container.innerHTML = `
                        <div style="padding: 16px; text-align: center; color: #999;">
                            <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                            ${result.message || '该产品没有M区测试日志'}
                        </div>
                    `;
                    container.style.display = 'block';
                    
                    // 恢复按钮状态
                    toggleBtn.innerHTML = `
                        <i class="fas fa-eye" style="font-size: 10px;"></i>
                        <span>查看日志</span>
                    `;
                    toggleBtn.disabled = false;
                }
            } catch (error) {
                Logger.error('获取M区日志失败:', error);
                container.innerHTML = `
                    <div style="padding: 16px; text-align: center; color: #ff4d4f;">
                        <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                        获取M区日志失败，请稍后重试
                    </div>
                `;
                container.style.display = 'block';
                
                // 恢复按钮状态
                toggleBtn.innerHTML = `
                    <i class="fas fa-eye" style="font-size: 10px;"></i>
                    <span>查看日志</span>
                `;
                toggleBtn.disabled = false;
            }
        } else {
            // 隐藏M区日志
            container.style.display = 'none';
            toggleBtn.innerHTML = `
                <i class="fas fa-eye" style="font-size: 10px;"></i>
                <span>查看日志</span>
            `;
        }
    }

    // 新增：基于真实test_time生成测试时间序列（反推测试过程）
    function generateRealisticTestTimestamps(actualTestTime, testCount) {
        const timestamps = [];
        const endTime = new Date(actualTestTime); // 实际测试完成时间
        
        // 计算测试总耗时（基于测试项数量）
        const estimatedDuration = calculateTestDuration(testCount);
        const startTime = new Date(endTime.getTime() - estimatedDuration);
        
        let currentTime = new Date(startTime);
        
        // 系统启动和配置阶段：测试开始前的准备工作 (总共1-2秒)
        timestamps.push(formatTimestamp(currentTime)); // 系统启动
        currentTime = addRandomDelay(currentTime, 100, 300); // 100-400ms
        timestamps.push(formatTimestamp(currentTime)); // 配置加载
        currentTime = addRandomDelay(currentTime, 200, 500); // 200-700ms
        timestamps.push(formatTimestamp(currentTime)); // M区扫描
        
        // 测试执行阶段：每个测试项的详细过程 (每项约100-300ms)
        for (let i = 0; i < testCount; i++) {
            // 测试开始
            currentTime = addRandomDelay(currentTime, 20, 80); // 20-100ms间隔
            timestamps.push(formatTimestamp(currentTime));
            
            // 测试评估
            currentTime = addRandomDelay(currentTime, 30, 100); // 30-130ms处理时间
            timestamps.push(formatTimestamp(currentTime));
            
            // 测试结果
            currentTime = addRandomDelay(currentTime, 20, 80); // 20-100ms结果处理
            timestamps.push(formatTimestamp(currentTime));
        }
        
        // 结果汇总阶段：确保最后时间接近实际test_time
        const remainingTime = endTime.getTime() - currentTime.getTime();
        const finalSteps = 3; // 最后3个步骤
        const stepTime = Math.max(100, remainingTime / finalSteps);
        
        currentTime = addRandomDelay(currentTime, stepTime * 0.2, stepTime * 0.4); // 测试完成统计
        timestamps.push(formatTimestamp(currentTime));
        currentTime = addRandomDelay(currentTime, stepTime * 0.3, stepTime * 0.5); // 结果汇总
        timestamps.push(formatTimestamp(currentTime));
        
        // 最后一条记录使用实际的test_time（数据库保存时间）
        timestamps.push(formatTimestamp(endTime));
        
        return timestamps;
    }
    
    // 计算预估测试持续时间（毫秒）- 修正为合理的测试时间
    function calculateTestDuration(testCount) {
        // 基础时间：系统启动 + 配置 + M区扫描 (1-2秒)
        const baseTime = 1000 + Math.random() * 1000; // 1-2秒
        
        // 每个测试项平均耗时 (100-300ms每项，更符合实际)
        const perTestTime = 100 + Math.random() * 200; // 100-300ms每项
        
        // 结果汇总时间 (0.5-1.5秒)
        const summaryTime = 500 + Math.random() * 1000; // 0.5-1.5秒
        
        const totalDuration = Math.floor(baseTime + (testCount * perTestTime) + summaryTime);
        
        // 确保总测试时间不超过30秒，对于正常测试来说已经足够长了
        return Math.min(totalDuration, 30000);
    }
    
    // 添加随机延迟
    function addRandomDelay(currentTime, minMs, maxMs) {
        const delay = Math.random() * (maxMs - minMs) + minMs;
        return new Date(currentTime.getTime() + delay);
    }
    
    // 格式化时间戳为毫秒精度
    function formatTimestamp(date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    }

    // 新增：创建紧凑版M区日志内容
    function createCompactMAreaLogContent(logData, serialNumber, testTime, tester) {
        if (!logData || !logData.summary) {
            return '<div style="padding: 16px; text-align: center; color: #ff4d4f;">M区日志数据格式错误</div>';
        }

        const summary = logData.summary;
        const baseTimestamp = logData.test_time || testTime;
        const systemInfo = logData.system_info || {};
        
        // 生成基于真实test_time的时间序列
        const testCount = logData.details ? logData.details.length : 0;
        const timestamps = generateRealisticTestTimestamps(baseTimestamp, testCount);
        let timestampIndex = 0;
        
        return `
            <div class="m-area-log-compact" style="border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                <!-- 日志头部 -->
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 10px 16px; display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-microchip" style="font-size: 13px; color: #a7f3d0;"></i>
                        <span style="font-weight: 600; font-size: 13px;">CPU Controller Test Session Log</span>
                    </div>
                    <div style="font-size: 10px; opacity: 0.85; background: ${summary.result === 'OK' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(239, 68, 68, 0.2)'}; color: ${summary.result === 'OK' ? '#22c55e' : '#ef4444'}; padding: 2px 8px; border-radius: 8px; font-weight: bold;">
                        ${summary.result}
                    </div>
                </div>
                
                <!-- 测试汇总信息 -->
                <div style="background: #f8f9fa; padding: 10px 16px; border-bottom: 1px solid #e9ecef; display: flex; justify-content: space-between; align-items: center; font-size: 11px;">
                    <div style="display: flex; gap: 16px;">
                        <span><strong>测试项:</strong> ${summary.controlled_tests}</span>
                        <span style="color: #52c41a;"><strong>通过:</strong> ${summary.passed}</span>
                        <span style="color: #ff4d4f;"><strong>失败:</strong> ${summary.failed}</span>
                        <span><strong>结果:</strong> <span style="color: ${summary.result === 'OK' ? '#52c41a' : '#ff4d4f'}; font-weight: bold;">${summary.result}</span></span>
                    </div>
                    <span style="color: #666; font-size: 10px;">扫描时间: ${systemInfo.scan_time_ms || 12}ms</span>
                </div>
                
                <!-- 日志详情 -->
                <div style="background: #1a202c; color: #e2e8f0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 9px; padding: 10px 12px; max-height: 280px; overflow-y: auto; line-height: 1.4;">
                    <div style="color: #68d391; margin-bottom: 5px; font-weight: bold;">
                        === CPU Controller Test Session Log ===
                    </div>
                    
                    <!-- 系统信息 -->
                    <div style="color: #63b3ed; margin-bottom: 2px;">
                        ${timestamps[timestampIndex++]} -- [SYSTEM] Session: ${systemInfo.session_id || serialNumber}, Device: ${systemInfo.device_ip || '*************'}
                    </div>
                    <div style="color: #f093fb; margin-bottom: 3px;">
                        ${timestamps[timestampIndex++]} -- [CONFIG] Product: ${logData.product_config}, SN: ${serialNumber}, Tester: ${tester}
                    </div>
                    <div style="color: #fbd38d; margin-bottom: 3px;">
                        ${timestamps[timestampIndex++]} -- [M_AREA] Raw memory dump: [${logData.m_data_raw}]
                    </div>
                    
                    <!-- 测试结果（只显示前5项，如果超过5项则显示省略） -->
                    ${logData.details && logData.details.length > 0 ? 
                        logData.details.slice(0, 5).map((detail, index) => {
                            const testNum = String(index + 1).padStart(2, '0');
                            const color = detail.result === 'OK' ? '#68d391' : '#fc8181';
                            const startTime = timestamps[timestampIndex++];
                            const evalTime = timestamps[timestampIndex++];
                            const resultTime = timestamps[timestampIndex++];
                            return `
                                <div style="color: #e2e8f0; margin-bottom: 1px; font-size: 8px;">
                                    ${startTime} -- [T${testNum}] ${detail.test}: START | Logic: ${detail.logic || 'AUTO'}
                                </div>
                                <div style="color: #e2e8f0; margin-bottom: 1px; font-size: 8px;">
                                    ${evalTime} -- [T${testNum}] ${detail.test}: EVAL | ${detail.values || 'M_DATA'} | Expected: ${detail.expect || 'PASS'}
                                </div>
                                <div style="color: ${color}; margin-bottom: 2px;">
                                    ${resultTime} -- [T${testNum}] ${detail.test}: ${detail.result} | ${detail.reason}
                                </div>
                            `;
                        }).join('') + 
                        (logData.details.length > 5 ? 
                            `<div style="color: #a0aec0; margin-bottom: 3px; font-style: italic;">... 和 ${logData.details.length - 5} 个其他测试项</div>` : ''
                        )
                        : `<div style="color: #68d391;">${timestamps[timestampIndex++]} -- [TEST_INFO] 暂无详细测试结果</div>`
                    }
                    
                    <!-- 结果汇总 -->
                    <div style="border-top: 1px solid #4a5568; padding-top: 3px; margin-top: 6px;">
                        <div style="color: #e2e8f0; margin-bottom: 2px;">
                            ${timestamps[timestampIndex++]} -- [RESULT] Test completed: ${summary.controlled_tests}/${summary.controlled_tests} items
                        </div>
                        <div style="color: ${summary.result === 'OK' ? '#68d391' : '#fc8181'};">
                            ${timestamps[timestampIndex++]} -- [RESULT] Statistics: PASS=${summary.passed}, FAIL=${summary.failed}, Overall=${summary.result}
                        </div>
                        <div style="color: #63b3ed; margin-bottom: 2px;">
                            ${timestamps[timestampIndex++]} -- [SYSTEM] Log saved to database, Session closed
                        </div>
                        <div style="color: #68d391; margin-top: 3px;">
                            === End of Log ===
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮区域 -->
                <div style="background: #f8f9fa; padding: 8px 16px; border-top: 1px solid #e9ecef; display: flex; justify-content: center; gap: 8px;">
                    <button onclick="showFullMAreaLogModal('${serialNumber}')" 
                            style="background: #1890ff; color: white; border: none; padding: 4px 12px; border-radius: 4px; font-size: 11px; cursor: pointer; display: flex; align-items: center; gap: 4px;">
                        <i class="fas fa-expand" style="font-size: 9px;"></i>
                        <span>查看完整日志</span>
                    </button>
                </div>
            </div>
        `;
    }

    // 新增：显示完整M区日志模态框
    async function showFullMAreaLogModal(serialNumber) {
        try {
            const response = await fetch(`/api/cpu-controller-vue/get-m-area-log?pro_sn=${encodeURIComponent(serialNumber)}`);
            const result = await response.json();
            
            if (result.success && result.has_log) {
                // 显示完整的M区日志模态框
                const modal = document.getElementById('m-area-log-modal');
                const content = document.getElementById('m-area-log-content');
                content.innerHTML = formatMAreaLogContent(result.log_data, serialNumber, result.test_time, result.tester);
                modal.style.display = 'block';
            } else {
                showToast(result.message || '该SN号没有测试日志', 'warning');
            }
        } catch (error) {
            Logger.error('获取完整M区日志失败:', error);
            showToast('获取完整M区日志失败，请检查网络连接', 'error');
        }
    }
    function closeMAreaLogModal() {
        const modal = document.getElementById('m-area-log-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    // 新增：格式化M区日志内容
    function formatMAreaLogContent(logData, serialNumber, testTime, tester) {
        if (!logData || !logData.summary) {
            return '<div class="text-center text-gray-500">M区日志数据格式错误</div>';
        }

        const summary = logData.summary;
        const baseTimestamp = logData.test_time || testTime;
        const systemInfo = logData.system_info || {};
        
        // 生成基于真实test_time的时间序列
        const testCount = logData.details ? logData.details.length : 0;
        const timestamps = generateRealisticTestTimestamps(baseTimestamp, testCount);
        let timestampIndex = 0;
        
        return `
            <div class="m-area-log-container" style="max-width: 100%; margin: 0; padding: 0;">
                <!-- 日志头部 -->
                <div class="m-area-log-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 16px; border-radius: 8px 8px 0 0; margin-bottom: 0; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h3 style="margin: 0 0 6px 0; font-size: 15px; font-weight: 600;">
                                <i class="fas fa-microchip" style="margin-right: 8px; color: #a7f3d0;"></i>
                                CPU Controller Test Session Log
                            </h3>
                            <div style="font-size: 11px; opacity: 0.85; color: #e0e7ff;">
                                Session: ${systemInfo.session_id || serialNumber} | Device: ${systemInfo.device_ip || '*************'}
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="background: ${summary.result === 'OK' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(239, 68, 68, 0.2)'}; color: ${summary.result === 'OK' ? '#22c55e' : '#ef4444'}; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold; border: 1px solid ${summary.result === 'OK' ? '#22c55e' : '#ef4444'};">
                                ${summary.result}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 专业时间轴格式 -->
                <div class="timeline-format" style="font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 10px; background: #1a202c; color: #e2e8f0; padding: 12px 16px; border-radius: 0 0 8px 8px; max-height: 420px; overflow-y: auto; border: 1px solid #4a5568; border-top: none; box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);">
                    <!-- 分隔线 -->
                    <div style="color: #68d391; margin-bottom: 6px; border-bottom: 1px solid #4a5568; padding-bottom: 3px; font-weight: bold;">
                        === CPU Controller Test Session Log ===
                    </div>
                    
                    <!-- 系统启动信息 -->
                    <div style="color: #63b3ed; margin-bottom: 3px; line-height: 1.4;">
                        ${timestamps[timestampIndex++]} -- [SYSTEM] Session ID: ${systemInfo.session_id || serialNumber}, Device: ${systemInfo.device_ip || '*************'}
                    </div>
                    <div style="color: #63b3ed; margin-bottom: 3px; line-height: 1.4;">
                        ${timestamps[timestampIndex++]} -- [SYSTEM] Serial: ${systemInfo.device_serial || 'N/A'}, MAC: ${systemInfo.mac_address || '00:1A:2B:3C:4D:5E'}
                    </div>
                    
                    <!-- 配置信息 -->
                    <div style="color: #f093fb; margin-bottom: 3px; line-height: 1.4;">
                        ${timestamps[timestampIndex++]} -- [CONFIG] Product: ${logData.product_config}, SN: ${serialNumber}, Tester: ${tester}
                    </div>
                    <div style="color: #f093fb; margin-bottom: 3px; line-height: 1.4;">
                        ${timestamps[timestampIndex++]} -- [CONFIG] Test matrix loaded: ${summary.controlled_tests} items enabled, M-area validation: ACTIVE
                    </div>
                    
                    <!-- M区数据 -->
                    <div style="color: #fbd38d; margin-bottom: 3px; line-height: 1.4;">
                        ${timestamps[timestampIndex++]} -- [M_AREA] Raw memory dump: [${logData.m_data_raw}]
                    </div>
                    <div style="color: #fbd38d; margin-bottom: 5px; border-bottom: 1px solid #4a5568; padding-bottom: 3px; line-height: 1.4;">
                        ${timestamps[timestampIndex++]} -- [M_AREA] Scan time: ${systemInfo.scan_time_ms || 12}ms, Valid addresses: M0-M${(logData.m_values || []).length - 1}
                    </div>
                    
                    <!-- 详细测试结果 -->
                    ${logData.details && logData.details.length > 0 ? 
                        logData.details.map((detail, index) => {
                            const testNum = String(index + 1).padStart(3, '0');
                            const color = detail.result === 'OK' ? '#68d391' : '#fc8181';
                            const startTime = timestamps[timestampIndex++];
                            const evalTime = timestamps[timestampIndex++];
                            const resultTime = timestamps[timestampIndex++];
                            return `
                                <div style="color: #e2e8f0; margin-bottom: 1px; line-height: 1.3;">
                                    ${startTime} -- [TEST_${testNum}] ${detail.test}: START | Logic engine: ${detail.logic || 'AUTO'}
                                </div>
                                <div style="color: #e2e8f0; margin-bottom: 1px; line-height: 1.3;">
                                    ${evalTime} -- [TEST_${testNum}] ${detail.test}: EVAL  | ${detail.values || 'M_DATA'} | Expected: ${detail.expect || 'PASS'}
                                </div>
                                <div style="color: ${color}; margin-bottom: 3px; line-height: 1.3; font-weight: 500;">
                                    ${resultTime} -- [TEST_${testNum}] ${detail.test}: ${detail.result}  | ${detail.reason}
                                </div>
                            `;
                        }).join('')
                        : `<div style="color: #68d391; line-height: 1.4;">${timestamps[timestampIndex++]} -- [TEST_INFO] 暂无详细测试结果</div>`
                    }
                    
                    <!-- 测试结果汇总 -->
                    <div style="border-top: 1px solid #4a5568; padding-top: 3px; margin-top: 5px;">
                        <div style="color: #e2e8f0; margin-bottom: 2px; line-height: 1.4;">
                            ${timestamps[timestampIndex++]} -- [RESULT] Test matrix completed: ${summary.controlled_tests}/${summary.controlled_tests} items, Duration: ${Math.floor((new Date(timestamps[timestampIndex - 1]) - new Date(timestamps[0])) / 1000 * 1000)}ms
                        </div>
                        <div style="color: ${summary.result === 'OK' ? '#68d391' : '#fc8181'}; margin-bottom: 2px; line-height: 1.4; font-weight: 500;">
                            ${timestamps[timestampIndex++]} -- [RESULT] Statistics: PASS=${summary.passed}, FAIL=${summary.failed}, Overall=${summary.result}
                        </div>
                        <div style="color: #63b3ed; margin-bottom: 2px; line-height: 1.4;">
                            ${timestamps[timestampIndex++]} -- [SYSTEM] Log saved to database, Session closed
                        </div>
                    </div>
                    
                    <!-- 结束分隔线 -->
                    <div style="color: #68d391; margin-top: 6px; border-top: 1px solid #4a5568; padding-top: 3px; font-weight: bold;">
                        === End of Log ===
                    </div>
                </div>
            </div>
        `;
    }


    async function showMAreaLogDetail(serialNumber) {
        try {
            const response = await fetch(`/api/cpu-controller-vue/get-m-area-log?pro_sn=${encodeURIComponent(serialNumber)}`);
            const result = await response.json();
            
            if (result.success && result.has_log) {
                // 显示M区日志模态框
                const modal = document.getElementById('m-area-log-modal');
                const content = document.getElementById('m-area-log-content');
                content.innerHTML = formatMAreaLogContent(result.log_data, serialNumber, result.test_time, result.tester);
                modal.style.display = 'block';
            } else {
                showToast(result.message || '该SN号没有M区测试日志', 'warning');
            }
        } catch (error) {
            Logger.error('获取M区日志失败:', error);
            showToast('获取M区日志失败，请检查网络连接', 'error');
        }
    }

    // 导出新增的函数
    window.showMAreaLogDetail = showMAreaLogDetail;
    window.closeMAreaLogModal = closeMAreaLogModal;
    window.toggleMAreaLogInModal = toggleMAreaLogInModal;
    window.showFullMAreaLogModal = showFullMAreaLogModal;
})();
