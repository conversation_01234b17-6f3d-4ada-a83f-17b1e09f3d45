(function() {
    // 检查是否已经存在全局变量
    if (typeof window.pageState === 'undefined') {
        window.pageState = {
            currentPage: 1,
            pageSize: 10,
            totalItems: 0,
            totalPages: 0,
            data: [],
            // 添加排序相关变量
            sortField: null,
            sortOrder: 'asc'
        };
    }

    if (typeof window.columnState === 'undefined') {
        window.columnState = {
            checkbox: true,
            orderNumber: true,
            productModel: true,
            productCode: true,
            serialNumber: true,
            status: true,
            tester: true,
            createdAt: true
        };
    }

    // 1. 首先定义列配置常量
    const BATCH_COLUMNS = [
        'orderNumber', 'productModel', 'productCode', 'serialNumber', 
        'status', 'tester', 'createdAt'
    ];

    function initBatchQueryPage() {
        const batchQueryContent = document.getElementById('batch-query-content');
        if (!batchQueryContent) {
            Logger.error('无法找到 batch-query-content 元素');
            return;
        }
        
        batchQueryContent.innerHTML = `
            <div class="container">
                <!-- 1. 查询区域 -->
                <div class="query-section">
                    <form class="query-form">
                        <div class="form-item">
                            <label class="form-label">工单号</label>
                            <input type="text" class="form-input" id="order-number-input" placeholder="请输入工单号">
                        </div>
                        <div class="form-item">
                            <label class="form-label">SN号</label>
                            <input type="text" class="form-input" id="sn-input" placeholder="请输入SN号">
                        </div>
                        <div class="form-item">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-input" id="start-date">
                        </div>
                        <div class="form-item">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-input" id="end-date">
                        </div>
                    </form>
                    <div class="query-buttons">
                        <button class="btn btn-default" onclick="resetForm()">重置</button>
                        <button class="btn btn-primary" onclick="searchBatch()">查询</button>
                    </div>
                </div>

                <!-- 2. 工具栏区 -->
                <div class="toolbar-section">
                    <div class="toolbar-left">
                        <button class="btn btn-export" onclick="exportData()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-default" onclick="toggleColumnSettings()">
                            <i class="fas fa-cog"></i> 列设置
                        </button>
                        <button class="btn btn-default" onclick="refreshTable()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 3. 表格区域 -->
                <div class="table-section">
                    <table class="data-table">
                        <thead>
                            <tr class="table-header">
                                <th class="table-cell-fixed" style="width: 50px;">
                                    <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                                </th>
                                <th class="table-cell-wide sortable" style="width: 180px;" onclick="sortByBatch('orderNumber')">
                                    工单号 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-wide sortable" style="width: 180px;" onclick="sortByBatch('productModel')">
                                    产品型号 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-wide sortable" style="width: 180px;" onclick="sortByBatch('productCode')">
                                    产品编码 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-wide sortable" style="width: 180px;" onclick="sortByBatch('serialNumber')">
                                    SN号 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-normal sortable" style="width: 100px;" onclick="sortByBatch('status')">
                                    状态 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-normal sortable" style="width: 100px;" onclick="sortByBatch('tester')">
                                    组装人员 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-normal sortable" style="width: 160px;" onclick="sortByBatch('createdAt')">
                                    组装时间 <i class="fas fa-sort"></i>
                                </th>
                                <th class="table-cell-action" style="width: 120px;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="results-tbody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 4. 分页区域 -->
                <div class="pagination-section">
                    <div class="page-size">
                        <span>每页</span>
                        <select class="form-select">
                            <option>10</option>
                            <option>20</option>
                            <option>50</option>
                        </select>
                        <span>条</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="page-button">上一页</button>
                        <button class="page-button active">1</button>
                        <button class="page-button">2</button>
                        <button class="page-button">3</button>
                        <span>...</span>
                        <button class="page-button">10</button>
                        <button class="page-button">下一页</button>
                        <div class="page-jump">
                            <span>跳至</span>
                            <input type="text" class="page-input" value="1">
                            <span>页</span>
                        </div>
                    </div>
                </div>

                <!-- 列设置弹窗 -->
                <div id="column-settings-modal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>列设置</h3>
                            <span class="close" onclick="closeColumnSettings()">&times;</span>
                        </div>
                        <div class="modal-body">
                            <div class="column-list">
                                <!-- 添加全选复选框 -->
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="select-all" onclick="toggleAllColumns()">
                                        <span>全选</span>
                                    </label>
                                </div>
                                <div class="column-divider"></div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-orderNumber" checked>
                                        <span>工单号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-productModel" checked>
                                        <span>产品型号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-productCode" checked>
                                        <span>产品编码</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-serialNumber" checked>
                                        <span>SN号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-status" checked>
                                        <span>状态</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-tester" checked>
                                        <span>组装人员</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-createdAt" checked>
                                        <span>组装时间</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" onclick="applyColumnSettings()">确定</button>
                            <button class="btn btn-default" onclick="closeColumnSettings()">取消</button>
                        </div>
                    </div>
                </div>

                <!-- 详情模态框 -->
                <div id="detail-modal" class="batch-modal">
                    <div class="batch-modal__content">
                        <div class="batch-modal__header">
                            <h2>详细信息</h2>
                            <span class="close">&times;</span>
                        </div>
                        <div class="batch-modal__body">
                            <!-- 基本信息卡片 -->
                            <div class="info-card">
                                <h3>基本信息</h3>
                                <div class="info-grid">
                                    <!-- 第一行 -->
                                    <div class="info-row">
                                        <div class="info-item">
                                            <label>工单号：</label>
                                            <span id="detail-order-number">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label>产品型号：</label>
                                            <span id="detail-product-model">-</span>
                                        </div>
                                    </div>
                                    <!-- 第二行 - 组装数量和备注 -->
                                    <div class="info-row">
                                        <div class="info-item">
                                            <label>组装数量：</label>
                                            <span id="detail-complete-product-quantity">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label>备注：</label>
                                            <span id="detail-complete-product-remark">-</span>
                                        </div>
                                    </div>
                                    <!-- 第三行 -->
                                    <div class="info-row">
                                        <div class="info-item">
                                            <label>单板组装人员：</label>
                                            <span id="detail-tester">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label>单板组装时间：</label>
                                            <span id="detail-board-time">-</span>
                                        </div>
                                    </div>
                                    <!-- 第四行 -->
                                    <div class="info-row">
                                        <div class="info-item">
                                            <label>外壳组装人员：</label>
                                            <span id="detail-shell-tester">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label>外壳组装时间：</label>
                                            <span id="detail-shell-time">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 绑定信息卡片 -->
                            <div class="info-card">
                                <h3>绑定信息</h3>
                                <div class="binding-info">
                                    <div class="board-list">
                                        <div class="board-item">
                                            <label>外壳SN</label>
                                            <span id="detail-shell-sn">-</span>
                                        </div>                                   
                                        <div class="board-item">
                                            <label>板子A</label>
                                            <span id="detail-board-a">-</span>
                                        </div>
                                        <div class="board-item">
                                            <label>板子B</label>
                                            <span id="detail-board-b">-</span>
                                        </div>
                                        <div class="board-item">
                                            <label>板子C</label>
                                            <span id="detail-board-c">-</span>
                                        </div>
                                        <div class="board-item">
                                            <label>板子D</label>
                                            <span id="detail-board-d">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化事件监听
        initEventListeners();
    }

    function initEventListeners() {
        // 输入框回车事件
        const inputs = ['sn-input', 'order-number-input'];
        inputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        searchBatch();
                    }
                });
            }
        });

        // 态框关闭按钮事件
        const modal = document.getElementById('detail-modal');
        if (modal) {
            const closeBtn = modal.querySelector('.close');
            if (closeBtn) {
                closeBtn.onclick = () => modal.style.display = 'none';
            }
            window.onclick = (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            };
        }

        // 添加分页大小选择器的事件监听
        const pageSizeSelect = document.querySelector('.page-size select');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                changePageSize(e.target.value);
            });
        }

        // 添加触摸事件监听器时使用 passive 选项
        const touchElements = document.querySelectorAll('.scrollable');
        touchElements.forEach(element => {
            element.addEventListener('touchstart', handler, { passive: true });
            element.addEventListener('touchmove', handler, { passive: true });
        });

        // 添加表格中复选框的变化监听
        const tbody = document.getElementById('results-tbody');
        if (tbody) {
            tbody.addEventListener('change', (e) => {
                if (e.target.type === 'checkbox') {
                    // 更新全选框状态
                    const selectAll = document.getElementById('selectAll');
                    const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
                    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                    selectAll.checked = allChecked;
                    
                    // 更新导出按钮文本
                    updateBatchExportButtonText();
                }
            });
        }
    }

    async function searchBatch() {
        const sn = document.getElementById('sn-input').value.trim();
        const orderNumber = document.getElementById('order-number-input').value.trim();
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;
        
        if (!sn && !orderNumber && !startDate && !endDate) {
            showToast('请提供外壳SN号、工单号或日期范围', 'warning');
            return;
        }

        if (startDate && endDate && startDate > endDate) {
            showToast('开始日期不能大于结束日期', 'warning');
            return;
        }

        try {
            const params = new URLSearchParams();
            if (sn) params.append('shellSN', sn);
            if (orderNumber) params.append('orderNumber', orderNumber);
            if (startDate) {
                // 直接使用YYYY-MM-DD格式，不进行时区转换
                params.append('startDate', startDate);
            }
            if (endDate) {
                // 直接使用YYYY-MM-DD格式，不进行时区转换
                params.append('endDate', endDate);
            }
            
            // 添加排序参数
            if (pageState.sortField) {
                params.append('sortField', pageState.sortField);
                params.append('sortOrder', pageState.sortOrder);
            }

            showToast('正在查询...', 'info');

            const response = await fetch(`/api/batch-query/search?${params.toString()}`);
            const data = await response.json();

            if (data.success) {
                if (data.data && data.data.length > 0) {
                    pageState.currentPage = 1;
                    updateResultsTable(data.data);
                    const tableSection = document.querySelector('.table-section');
                    if (tableSection) {
                        tableSection.style.display = 'block';
                    }
                    showToast(`查询成功，共找到 ${data.data.length} 条记录`, 'success');
                } else {
                    showToast('未找到匹配的记录', 'warning');
                    const tableSection = document.querySelector('.table-section');
                    if (tableSection) {
                        tableSection.style.display = 'none';
                    }
                }
            } else {
                showToast(data.message || '查询失败', 'error');
                const tableSection = document.querySelector('.table-section');
                if (tableSection) {
                    tableSection.style.display = 'none';
                }
            }
        } catch (error) {
            Logger.error('查询失败:', error);
            showToast('查询失败，请稍后重试', 'error');
            const tableSection = document.querySelector('.table-section');
            if (tableSection) {
                tableSection.style.display = 'none';
            }
        }
    }

    function resetForm() {
        const form = document.querySelector('.query-form');
        if (form) {
            form.reset();
        }

        pageState.currentPage = 1;
        pageState.data = [];
        pageState.totalItems = 0;
        pageState.totalPages = 0;
        // 重置排序状态
        pageState.sortField = null;
        pageState.sortOrder = 'asc';

        const tbody = document.getElementById('results-tbody');
        if (tbody) {
            tbody.innerHTML = '';
        }

        const tableSection = document.querySelector('.table-section');
        if (tableSection) {
            tableSection.style.display = 'none';
        }

        updatePagination();
        showToast('查询条件已重置', 'info');
        
        // 更新表头排序图标
        updateSortIcons();
    }

    // 添加重置模态框位置的函数
    function resetDetailModalPosition() {
        const modalContent = document.querySelector('#detail-modal .batch-modal__content');
        if (modalContent) {
            modalContent.style.transform = 'translate(-50%, -50%)';
            modalContent.style.top = '50%';
            modalContent.style.left = '50%';
        }
    }
    
    // 添加排序函数
    function sortByBatch(field) {
        // 如果点击了当前排序字段，切换排序顺序
        if (pageState.sortField === field) {
            pageState.sortOrder = pageState.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果点击了新字段，设置为升序
            pageState.sortField = field;
            pageState.sortOrder = 'asc';
        }
        
        // 更新表头排序图标
        updateSortIcons();
        
        // 如果已经有数据，重新查询
        if (pageState.data.length > 0) {
            searchBatch();
        }
    }
    
    // 更新表头排序图标
    function updateSortIcons() {
        const headers = document.querySelectorAll('.sortable i');
        headers.forEach(icon => {
            // 重置所有图标
            icon.className = 'fas fa-sort';
        });
        
        // 如果有排序字段，设置对应列的图标
        if (pageState.sortField) {
            const activeHeader = document.querySelector(`th[onclick="sortByBatch('${pageState.sortField}')"] i`);
            if (activeHeader) {
                activeHeader.className = `fas fa-sort-${pageState.sortOrder === 'asc' ? 'up' : 'down'}`;
            }
        }
    }

    // 修改 showDetail 函数,添加ESC监听
    function showDetail(data) {
        try {
            // 获取基本信息容器
            const infoGrid = document.querySelector('.info-grid');
            if (infoGrid) {
                // 清空现有内容
                infoGrid.innerHTML = `
                    <!-- 第一行 -->
                    <div class="info-row">
                        <div class="info-item">
                            <label>工单号：</label>
                            <span>${data.orderNumber || '-'}</span>
                        </div>
                        <div class="info-item">
                            <label>产品型号：</label>
                            <span>${data.productModel || '-'}</span>
                        </div>
                    </div>
                    <!-- 第二行 - 组装数量和备注 -->
                    <div class="info-row">
                        <div class="info-item">
                            <label>组装数量：</label>
                            <span>${data.complete_product_quantity || '-'}</span>
                        </div>
                        <div class="info-item">
                            <label>备注：</label>
                            <span>${data.complete_product_remark || '-'}</span>
                        </div>
                    </div>
                    <!-- 第三行 -->
                    <div class="info-row">
                        <div class="info-item">
                            <label>单板组装人员：</label>
                            <span>${data.tester || '-'}</span>
                        </div>
                        <div class="info-item">
                            <label>单板组装时间：</label>
                            <span>${formatDate(data.boardTime) || '-'}</span>
                        </div>
                    </div>
                    <!-- 第四行 -->
                    <div class="info-row">
                        <div class="info-item">
                            <label>外壳组装人员：</label>
                            <span>${data.complete_tester_name || '-'}</span>
                        </div>
                        <div class="info-item">
                            <label>外壳组装时间：</label>
                            <span>${formatDate(data.shellTime) || '-'}</span>
                        </div>
                    </div>
                `;
            }

            // 获取绑定信息容器
            const boardList = document.querySelector('.board-list');
            if (boardList) {
                // 清空现有内容
                boardList.innerHTML = '';
                
                // 修改板子信息顺序，将外壳SN放在最前面
                const boardItems = [
                    { label: '外壳SN：', value: data.shellSN },  // 移到最前面
                    { label: '板子A：', value: data.boardA },
                    { label: '板子B：', value: data.boardB },
                    { label: '板子C：', value: data.boardC },
                    { label: '板子D：', value: data.boardD }
                ];

                // 添加板子信息
                boardItems.forEach(item => {
                    if (item.value) {  // 只有当值存在且不为空时才显示
                        boardList.innerHTML += `
                            <div class="board-item">
                                <label>${item.label}</label>
                                <span>${item.value}</span>
                            </div>
                        `;
                    }
                });
            }

            // 显示模态框前重置位置
            resetDetailModalPosition();
            
            // 显示模态框
            const modal = document.getElementById('detail-modal');
            if (modal) {
                modal.style.display = 'block';
                
                // 添加ESC键监听
                document.addEventListener('keydown', handleDetailEsc);
                
                // 添加拖动功能
                const modalContent = modal.querySelector('.batch-modal__content');
                const modalHeader = modal.querySelector('.batch-modal__header');
                
                let isDragging = false;
                let currentX;
                let currentY;
                let initialX;
                let initialY;
                let xOffset = 0;
                let yOffset = 0;

                modalHeader.addEventListener('mousedown', dragStart);
                document.addEventListener('mousemove', drag);
                document.addEventListener('mouseup', dragEnd);

                function dragStart(e) {
                    if (e.target === modalHeader || e.target.parentElement === modalHeader) {
                        initialX = e.clientX - xOffset;
                        initialY = e.clientY - yOffset;
                        isDragging = true;
                        modalContent.classList.add('dragging');
                    }
                }

                function drag(e) {
                    if (isDragging) {
                        e.preventDefault();
                        
                        currentX = e.clientX - initialX;
                        currentY = e.clientY - initialY;

                        // 限制拖动范围，防止模态框被拖出视口
                        const modalRect = modalContent.getBoundingClientRect();
                        const viewportWidth = window.innerWidth;
                        const viewportHeight = window.innerHeight;

                        // 限制左右范围
                        if (currentX < -(viewportWidth - modalRect.width) / 2) {
                            currentX = -(viewportWidth - modalRect.width) / 2;
                        }
                        if (currentX > (viewportWidth - modalRect.width) / 2) {
                            currentX = (viewportWidth - modalRect.width) / 2;
                        }

                        // 限制上下范围
                        if (currentY < -(viewportHeight - modalRect.height) / 2) {
                            currentY = -(viewportHeight - modalRect.height) / 2;
                        }
                        if (currentY > (viewportHeight - modalRect.height) / 2) {
                            currentY = (viewportHeight - modalRect.height) / 2;
                        }

                        xOffset = currentX;
                        yOffset = currentY;

                        setTranslate(currentX, currentY, modalContent);
                    }
                }

                function dragEnd(e) {
                    initialX = currentX;
                    initialY = currentY;
                    isDragging = false;
                    modalContent.classList.remove('dragging');
                }

                function setTranslate(xPos, yPos, el) {
                    el.style.transform = `translate(${xPos}px, ${yPos}px)`;
                }

                // 定义cleanup函数
                const cleanup = () => {
                    modalHeader.removeEventListener('mousedown', dragStart);
                    document.removeEventListener('mousemove', drag);
                    document.removeEventListener('mouseup', dragEnd);
                    document.removeEventListener('keydown', handleDetailEsc);
                };

                // 将cleanup函数存储在modal元素上
                modal._cleanup = cleanup;

                // 添加到关闭按钮事件中
                const closeBtn = modal.querySelector('.close');
                if (closeBtn) {
                    closeBtn.onclick = closeDetailModal;
                }

                // 添加点击模态框外部关闭
                modal.onclick = (e) => {
                    if (e.target === modal) {
                        closeDetailModal();
                    }
                };
            }
        } catch (error) {
            Logger.error('显示详情时出错:', error);
            showToast('显示详情失败，请稍后重试', 'error');
        }
    }

    function formatDate(dateString) {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    function showToast(message, type = 'info') {
        // 创建容器（如果不存在）
        let toastContainer = document.querySelector('.batch-query__toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'batch-query__toast-container';
            document.body.appendChild(toastContainer);
        }
        
        // 创建新的 toast 元素
        const toast = document.createElement('div');
        toast.className = `batch-query__toast batch-query__toast--${type}`;
        
        // 设置图标
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="fas fa-check-circle"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-times-circle"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-circle"></i>';
                break;
            case 'info':
                icon = '<i class="fas fa-info-circle"></i>';
                break;
        }
        
        // 设置内容
        toast.innerHTML = `
            <div class="batch-query__toast-content">
                ${icon}
                <span>${message}</span>
            </div>
        `;
        
        // 添加到容器
        toastContainer.appendChild(toast);
        
        // 触发动画
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });
        
        // 设置自动移除
        setTimeout(() => {
            toast.classList.remove('show');
            toast.classList.add('hide');
            
            // 动画结束后移除元素
            setTimeout(() => {
                toast.remove();
                // 如果容器为空，也移除容器
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            }, 300);
        }, 3000);
    }

    function updateResultsTable(data) {
        pageState.data = data;
        pageState.totalItems = data.length;
        pageState.totalPages = Math.ceil(data.length / pageState.pageSize);
        
        const startIndex = (pageState.currentPage - 1) * pageState.pageSize;
        const endIndex = startIndex + pageState.pageSize;
        const pageData = data.slice(startIndex, endIndex);
        
        const tbody = document.getElementById('results-tbody');
        if (!tbody) return;

        // 更新表头
        const thead = document.querySelector('.table-header');
        if (thead) {
            thead.innerHTML = `
                ${columnState.checkbox ? `
                    <th class="table-cell-fixed" style="width: 50px;">
                        <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                    </th>
                ` : ''}
                ${columnState.orderNumber ? `
                    <th class="table-cell-wide sortable" style="width: 180px;" onclick="sortByBatch('orderNumber')">
                        工单号 <i class="fas fa-sort${pageState.sortField === 'orderNumber' ? 
                              pageState.sortOrder === 'asc' ? '-up' : '-down' : ''}"></i>
                    </th>` : ''}
                ${columnState.productModel ? `
                    <th class="table-cell-wide sortable" style="width: 180px;" onclick="sortByBatch('productModel')">
                        产品型号 <i class="fas fa-sort${pageState.sortField === 'productModel' ? 
                              pageState.sortOrder === 'asc' ? '-up' : '-down' : ''}"></i>
                    </th>` : ''}
                ${columnState.productCode ? `
                    <th class="table-cell-wide sortable" style="width: 180px;" onclick="sortByBatch('productCode')">
                        产品编码 <i class="fas fa-sort${pageState.sortField === 'productCode' ? 
                              pageState.sortOrder === 'asc' ? '-up' : '-down' : ''}"></i>
                    </th>` : ''}
                ${columnState.serialNumber ? `
                    <th class="table-cell-wide sortable" style="width: 180px;" onclick="sortByBatch('serialNumber')">
                        SN号 <i class="fas fa-sort${pageState.sortField === 'serialNumber' ? 
                              pageState.sortOrder === 'asc' ? '-up' : '-down' : ''}"></i>
                    </th>` : ''}
                ${columnState.status ? `
                    <th class="table-cell-normal sortable" style="width: 100px;" onclick="sortByBatch('status')">
                        状态 <i class="fas fa-sort${pageState.sortField === 'status' ? 
                              pageState.sortOrder === 'asc' ? '-up' : '-down' : ''}"></i>
                    </th>` : ''}
                ${columnState.tester ? `
                    <th class="table-cell-normal sortable" style="width: 100px;" onclick="sortByBatch('tester')">
                        组装人员 <i class="fas fa-sort${pageState.sortField === 'tester' ? 
                              pageState.sortOrder === 'asc' ? '-up' : '-down' : ''}"></i>
                    </th>` : ''}
                ${columnState.createdAt ? `
                    <th class="table-cell-normal sortable" style="width: 160px;" onclick="sortByBatch('createdAt')">
                        组装时间 <i class="fas fa-sort${pageState.sortField === 'createdAt' ? 
                              pageState.sortOrder === 'asc' ? '-up' : '-down' : ''}"></i>
                    </th>` : ''}
                <th class="table-cell-action" style="width: 120px;">操作</th>
            `;
        }

        // 更新表格内容
        tbody.innerHTML = pageData.map(item => `
            <tr class="table-row">
                ${columnState.checkbox ? `
                    <td class="table-cell-center">
                        <input type="checkbox" class="row-checkbox">
                    </td>
                ` : ''}
                ${columnState.orderNumber ? `<td class="table-cell-center">${item.orderNumber || '-'}</td>` : ''}
                ${columnState.productModel ? `<td class="table-cell-center">${item.productModel || '-'}</td>` : ''}
                ${columnState.productCode ? `<td class="table-cell-center">${item.productCode || '-'}</td>` : ''}
                ${columnState.serialNumber ? `<td class="table-cell-center">${item.shellSN || '-'}</td>` : ''}
                ${columnState.status ? `
                    <td class="table-cell-center status-cell">
                        <span class="status-tag ${item.status === '已绑定外壳' ? 'status-active' : 'status-inactive'}">
                            ${item.status}
                        </span>
                    </td>
                ` : ''}
                ${columnState.tester ? `<td class="table-cell-center">${item.complete_tester_name || '-'}</td>` : ''}
                ${columnState.createdAt ? `<td class="table-cell-center">${formatDate(item.shellTime) || '-'}</td>` : ''}
                <td class="table-cell-center">
                    <button class="btn btn-detail" onclick='showDetail(${JSON.stringify(item)})'>
                        <i class="fas fa-eye"></i> 详情
                    </button>
                </td>
            </tr>
        `).join('');

        updatePagination();

        // 在表格更新完成后添加
        updateBatchExportButtonText();
    }

    function updatePagination() {
        const paginationControls = document.querySelector('.pagination-controls');
        if (!paginationControls) return;

        let html = `
            <button class="page-button" onclick="changePage(${pageState.currentPage - 1})" 
                    ${pageState.currentPage === 1 ? 'disabled' : ''}>
                上一页
            </button>
        `;
        
        for (let i = 1; i <= pageState.totalPages; i++) {
            if (
                i === 1 || 
                i === pageState.totalPages || 
                (i >= pageState.currentPage - 2 && i <= pageState.currentPage + 2)
            ) {
                html += `
                    <button class="page-button ${i === pageState.currentPage ? 'active' : ''}" 
                            onclick="changePage(${i})">
                        ${i}
                    </button>
                `;
            } else if (
                i === pageState.currentPage - 3 || 
                i === pageState.currentPage + 3
            ) {
                html += `<span>...</span>`;
            }
        }
        
        html += `
            <button class="page-button" onclick="changePage(${pageState.currentPage + 1})" 
                    ${pageState.currentPage === pageState.totalPages ? 'disabled' : ''}>
                下一页
            </button>
            <div class="page-jump">
                <span>跳至</span>
                <input type="text" class="page-input" value="${pageState.currentPage}" 
                       onchange="jumpToPage(this.value)">
                <span>页</span>
            </div>
            <span class="total-count">共 ${pageState.totalItems} 条</span>
        `;
        
        paginationControls.innerHTML = html;
    }

    function changePage(page) {
        if (page < 1 || page > pageState.totalPages || page === pageState.currentPage) {
            return;
        }
        
        pageState.currentPage = page;
        const startIndex = (page - 1) * pageState.pageSize;
        const endIndex = startIndex + pageState.pageSize;
        const pageData = pageState.data.slice(startIndex, endIndex);
        
        // 更新表格数据
        updateResultsTable(pageState.data);
        
        // 更新分页控件状态
        updatePaginationControls();
    }

    function updatePaginationControls() {
        const paginationControls = document.querySelector('.pagination-controls');
        if (!paginationControls) return;
        
        // 计算显示的页码范围
        let startPage = Math.max(1, pageState.currentPage - 2);
        let endPage = Math.min(pageState.totalPages, startPage + 4);
        
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }
        
        let html = `
            <button class="page-button" onclick="changePage(${pageState.currentPage - 1})" 
                    ${pageState.currentPage === 1 ? 'disabled' : ''}>
                上一页
            </button>
        `;
        
        // 显示第一页
        if (startPage > 1) {
            html += `
                <button class="page-button" onclick="changePage(1)">1</button>
                ${startPage > 2 ? '<span>...</span>' : ''}
            `;
        }
        
        // 显示中间页码
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="page-button ${i === pageState.currentPage ? 'active' : ''}" 
                        onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        }
        
        // 显示最后一页
        if (endPage < pageState.totalPages) {
            html += `
                ${endPage < pageState.totalPages - 1 ? '<span>...</span>' : ''}
                <button class="page-button" onclick="changePage(${pageState.totalPages})">
                    ${pageState.totalPages}
                </button>
            `;
        }
        
        html += `
            <button class="page-button" onclick="changePage(${pageState.currentPage + 1})"
                    ${pageState.currentPage === pageState.totalPages ? 'disabled' : ''}>
                下一页
            </button>
            <div class="page-jump">
                <span>跳至</span>
                <input type="text" class="page-input" value="${pageState.currentPage}" 
                       onchange="jumpToPage(this.value)">
                <span>页</span>
            </div>
        `;
        
        paginationControls.innerHTML = html;
    }

    function jumpToPage(page) {
        const pageNum = parseInt(page);
        if (isNaN(pageNum)) return;
        changePage(Math.min(Math.max(1, pageNum), pageState.totalPages));
    }

    function changePageSize(size) {
        const newSize = parseInt(size);
        if (isNaN(newSize) || newSize === pageState.pageSize) return;
        
        pageState.pageSize = newSize;
        pageState.currentPage = 1;
        pageState.totalPages = Math.ceil(pageState.data.length / pageState.pageSize);
        
        updateResultsTable(pageState.data);
    }

    // 2. 修改列设置切换函数
    function toggleColumnSettings() {
        const modal = document.getElementById('column-settings-modal');
        if (!modal) return;

        // 检查所有列的状态来设置全选框
        const allChecked = BATCH_COLUMNS.every(col => columnState[col]);
        
        // 设置全选框状态
        const selectAll = document.getElementById('select-all');
        if (selectAll) {
            selectAll.checked = allChecked;
            selectAll.onchange = () => toggleAllColumns();
        }
        
        // 设置各列复选框状态
        BATCH_COLUMNS.forEach(col => {
            const checkbox = document.getElementById(`col-${col}`);
            if (checkbox) {
                checkbox.checked = columnState[col];
                checkbox.onchange = () => updateSelectAllState();
            }
        });

        modal.style.display = 'block';
    }

    // 3. 修改全选切换函数
    function toggleAllColumns() {
        const selectAll = document.getElementById('select-all');
        const isChecked = selectAll.checked;
        
        // 更新所有列复选框状态
        BATCH_COLUMNS.forEach(col => {
            const checkbox = document.getElementById(`col-${col}`);
            if (checkbox) {
                checkbox.checked = isChecked;
            }
            // 更新列状态(checkbox列除外)
            if (col !== 'checkbox') {
                columnState[col] = isChecked;
            }
        });
    }

    // 4. 修改全选状态更新函数
    function updateSelectAllState() {
        const selectAll = document.getElementById('select-all');
        const allChecked = BATCH_COLUMNS.every(col => {
            const checkbox = document.getElementById(`col-${col}`);
            return checkbox ? checkbox.checked : false;
        });
        selectAll.checked = allChecked;
    }

    // 5. 修改应用列设置函数
    function applyColumnSettings() {
        // 更新列状态
        BATCH_COLUMNS.forEach(col => {
            const checkbox = document.getElementById(`col-${col}`);
            if (checkbox) {
                columnState[col] = checkbox.checked;
            }
        });
        
        // 立即更新表格显示
        if (pageState.data.length > 0) {
            updateResultsTable(pageState.data);
        }
        
        closeColumnSettings();
        showToast('列设置已更新', 'success');
    }

    async function exportData() {
        // 获取选中的行数据
        const selectedRows = getSelectedBatchData();
        
        // 如果有选中的行，就使用选中的数据；否则使用所有数据
        const dataToExport = selectedRows.length > 0 ? selectedRows : pageState.data;

        if (!dataToExport || dataToExport.length === 0) {
            showToast('没有可导出的数据', 'warning');
            return;
        }

        try {
            // 定义导出的表头和对应的数据字段
            const exportConfig = [
                { header: '工单号', field: 'orderNumber' },
                { header: '产品型号', field: 'productModel' },
                { header: '产品编码', field: 'productCode' },
                { header: 'SN号', field: 'shellSN' },
                { header: '状态', field: 'status' },
                { header: '组装数量', field: 'complete_product_quantity' },
                { header: '单板组装人员', field: 'tester' },
                { header: '单板组装时间', field: 'boardTime', format: 'date' },
                { header: '外壳组装人员', field: 'complete_tester_name' },
                { header: '外壳组装时间', field: 'shellTime', format: 'date' },
                { header: '板子A', field: 'boardA' },
                { header: '板子B', field: 'boardB' },
                { header: '板子C', field: 'boardC' },
                { header: '板子D', field: 'boardD' },
                { header: '备注', field: 'complete_product_remark' }
            ];

            // 提取表头
            const headers = exportConfig.map(config => config.header);

            // 处理数据行
            const rows = dataToExport.map(item => {
                return exportConfig.map(config => {
                    let value = item[config.field];
                    // 如果是日期类型，需要格式化
                    if (config.format === 'date' && value) {
                        value = formatDate(value);
                    }
                    return value || '-';
                });
            });

            // 生成CSV内容
            const worksheet = [headers, ...rows];
            const csvContent = '\uFEFF' + worksheet.map(row => 
                row.map(cell => 
                    `"${(cell || '').toString().replace(/"/g, '""')}"`
                ).join(',')
            ).join('\n');

            // 创建并下载文件
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `产品批次查询_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 根据是否是选中导出显示不同的提示
            const exportType = selectedRows.length > 0 ? '选中' : '全部';
            showToast(`已成功导出${exportType}数据`, 'success');
        } catch (error) {
            Logger.error('导出失败:', error);
            showToast('导出失败，请稍后重试', 'error');
        }
    }

    function refreshTable() {
        if (!pageState.data || pageState.data.length === 0) {
            showToast('有数据可刷新', 'warning');
            return;
        }
        searchBatch();
    }

    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
        
        // 更新导出按钮的状态提示
        updateBatchExportButtonText();
    }

    function toggleAllColumns() {
        const selectAll = document.getElementById('select-all');
        const columnCheckboxes = document.querySelectorAll('.column-list input[type="checkbox"]:not(#select-all)');
        const isChecked = selectAll.checked;
        
        // 更新所有列的选中状态
        columnCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        
        // 更新columnState对象
        Object.keys(columnState).forEach(key => {
            if (key !== 'checkbox') { // 保持checkbox列始终显示
                columnState[key] = isChecked;
            }
        });
    }

    // 在列设置对话框打开时检查全选状态
    function updateSelectAllState() {
        const selectAll = document.getElementById('select-all');
        const columnCheckboxes = document.querySelectorAll('.column-list input[type="checkbox"]:not(#select-all)');
        const allChecked = Array.from(columnCheckboxes).every(checkbox => checkbox.checked);
        selectAll.checked = allChecked;
    }

    // 添加关闭详情模态框的函数
    function closeDetailModal() {
        const modal = document.getElementById('detail-modal');
        if (modal) {
            // 获取并执行cleanup函数(如果存在)
            const cleanup = modal._cleanup;
            if (typeof cleanup === 'function') {
                cleanup();
            }
            
            // 移除ESC事件监听器
            document.removeEventListener('keydown', handleDetailEsc);
            
            // 隐藏模态框
            modal.style.display = 'none';
            
            // 清除cleanup引用
            delete modal._cleanup;
        }
    }

    // ESC按键处理函数 - 简化版本
    function handleDetailEsc(e) {
        if (e.key === 'Escape') {
            closeDetailModal();
        }
    }

    // 添加一个获取选中行数据的辅助函数
    function getSelectedBatchData() {
        const tbody = document.getElementById('results-tbody');
        if (!tbody) return [];

        const selectedRows = [];
        const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
        const allData = pageState.data;

        checkboxes.forEach((checkbox, index) => {
            if (checkbox.checked) {
                // 获取当前页面的起始索引
                const startIndex = (pageState.currentPage - 1) * pageState.pageSize;
                // 计算实际数据索引
                const dataIndex = startIndex + index;
                if (allData[dataIndex]) {
                    selectedRows.push(allData[dataIndex]);
                }
            }
        });

        return selectedRows;
    }

    // 添加更新导出按钮文本的函数
    function updateBatchExportButtonText() {
        const selectedCount = getSelectedBatchData().length;
        const exportButton = document.querySelector('.btn-export');
        if (exportButton) {
            if (selectedCount > 0) {
                exportButton.innerHTML = `<i class="fas fa-download"></i> 导出选中(${selectedCount})`;
            } else {
                exportButton.innerHTML = `<i class="fas fa-download"></i> 导出数据`;
            }
        }
    }

    // 1. 添加回关闭模态框函数(之前被误删了)
    function closeColumnSettings() {
        const modal = document.getElementById('column-settings-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 确保函数可以全局访问
    window.sortByBatch = sortByBatch;
    window.updateSortIcons = updateSortIcons;
    window.initBatchQueryPage = initBatchQueryPage;
    window.searchBatch = searchBatch;
    window.resetForm = resetForm;
    window.resetDetailModalPosition = resetDetailModalPosition;
    window.showDetail = showDetail;
    window.closeDetailModal = closeDetailModal;
    window.handleDetailEsc = handleDetailEsc;
    window.toggleColumnSettings = toggleColumnSettings;
    window.closeColumnSettings = closeColumnSettings;
    window.applyColumnSettings = applyColumnSettings;
    window.toggleAllColumns = toggleAllColumns;
    window.updateSelectAllState = updateSelectAllState;
    window.toggleSelectAll = toggleSelectAll;
    window.exportData = exportData;
    window.refreshTable = refreshTable;
    window.changePage = changePage;
    window.jumpToPage = jumpToPage;
    window.changePageSize = changePageSize;
    window.formatDate = formatDate;
    window.showToast = showToast;

})();
  