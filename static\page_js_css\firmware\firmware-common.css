/* 固件管理模块共享样式 - 使用BEM命名规范 */

/* Element Plus全局样式微调（避免与现有样式冲突） */
.firmware-container .el-table {
    font-size: 14px;
    border-radius: 4px;
}

.firmware-container .el-button {
    font-size: 14px;
    border-radius: 4px;
}

.firmware-container .el-input {
    font-size: 14px;
}

.firmware-container .el-dialog {
    border-radius: 8px;
}

/* 固件状态通用样式 */
.firmware-status--active { 
    color: #67C23A; 
    font-weight: bold;
}

.firmware-status--pending { 
    color: #E6A23C; 
    font-weight: bold;
}

.firmware-status--rejected { 
    color: #F56C6C; 
    font-weight: bold;
}

.firmware-status--obsolete { 
    color: #F56C6C; 
    font-weight: bold;
}

/* 固件页面通用布局 */
.firmware-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 100px);
}

/* ===== 优化标题样式 ===== */
.firmware-page__header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.firmware-page__title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    position: relative;
    padding-left: 15px;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.firmware-page__title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 18px;
    background: linear-gradient(to bottom, #6777ef, #5566d4);
    border-radius: 2.5px;
}

/* ===== 优化搜索栏样式 ===== */
.firmware-page__search-bar {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    background: white;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.firmware-page__search-bar:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.firmware-page__search-input {
    flex: 1;
    max-width: 50%;
}

.firmware-page__search-input .el-input__inner {
    border-radius: 6px;
    transition: all 0.3s ease;
    padding-left: 40px;
}

.firmware-page__search-input .el-input__inner:hover {
    border-color: #c0c4cc;
}

.firmware-page__search-input .el-input__inner:focus {
    border-color: #6777ef;
    box-shadow: 0 0 0 2px rgba(103, 119, 239, 0.2);
}

.firmware-page__search-input .el-input__prefix {
    left: 12px;
    color: #909399;
}

/* ===== 优化按钮样式 ===== */
.firmware-page__action-buttons {
    display: flex;
    gap: 12px;
}

.firmware-page__action-button {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.firmware-page__action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.firmware-page__action-button--primary {
    background: linear-gradient(135deg, #6777ef, #5566d4);
    color: white;
}

.firmware-page__action-button--primary:hover {
    background: linear-gradient(135deg, #5566d4, #4455c3);
}

.firmware-page__action-button--success {
    background: linear-gradient(135deg, #67c23a, #5daf34);
    color: white;
}

.firmware-page__action-button--success:hover {
    background: linear-gradient(135deg, #5daf34, #4d9429);
}

.firmware-page__action-button--info {
    background: linear-gradient(135deg, #909399, #7d7f85);
    color: white;
}

.firmware-page__action-button--info:hover {
    background: linear-gradient(135deg, #7d7f85, #696b71);
}

.firmware-page__action-button--warning {
    background: linear-gradient(135deg, #e6a23c, #d48c23);
    color: white;
}

.firmware-page__action-button--warning:hover {
    background: linear-gradient(135deg, #d48c23, #c27b13);
}

.firmware-page__action-button--danger {
    background: linear-gradient(135deg, #f56c6c, #e74c4c);
    color: white;
}

.firmware-page__action-button--danger:hover {
    background: linear-gradient(135deg, #e74c4c, #d63b3b);
}

.firmware-page__tip {
    color: #5555ff; 
    font-size: 12px; 
    margin-left: 15px; 
    line-height: 32px;
}

/* 表格容器样式 */
.firmware-page__table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.firmware-page__table-container:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.firmware-container .el-table {
    border-radius: 0;
}

.firmware-container .el-table__header-wrapper {
    background-color: #fafafa;
}

.firmware-container .el-table th {
    background-color: #fafafa;
    font-weight: 600;
    color: #333;
}

/* 新增：进一步减小表格行高，实现更紧凑的视图 */
.firmware-container .el-table--small .el-table__cell {
    padding: 6px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .firmware-page {
        padding: 10px;
    }
    
    .firmware-page__search-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .firmware-page__search-input {
        max-width: 100%;
    }
    
    .firmware-page__action-buttons {
        justify-content: center;
    }
    
    .firmware-page__header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* 对话框样式优化 */
.firmware-container .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
}

.firmware-container .el-dialog__body {
    padding: 20px;
}

.firmware-container .el-form-item__label {
    font-weight: 500;
    color: #333;
}

/* ===== 现代化模态框样式 ===== */
/* 模态框基础样式 */
.firmware-modal {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15), 0 8px 10px rgba(0, 0, 0, 0.12);
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.firmware-modal .el-dialog__header {
    background-color: #fff;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    position: relative;
}

.firmware-modal .el-dialog__title {
    font-size: 17px;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 0.2px;
}

.firmware-modal .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
    background: linear-gradient(to bottom, #fafbfc 0%, #ffffff 100%);
}

.firmware-modal .el-dialog__footer {
    border-top: 1px solid #ebeef5;
    padding: 12px 20px;
    background-color: #fafbfc;
}

/* 表单分区样式 */
.firmware-modal__section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #ebeef5;
    transition: all 0.3s ease;
}

.firmware-modal__section:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.firmware-modal__section:last-child {
    margin-bottom: 0;
}

.firmware-modal__section-header {
    margin-bottom: 16px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
}

.firmware-modal__section-title {
    font-size: 15px;
    font-weight: 600;
    color: #2c3e50;
    position: relative;
    padding-left: 12px;
    letter-spacing: 0.3px;
}

.firmware-modal__section-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background: linear-gradient(to bottom, #6777ef, #5566d4);
    border-radius: 2px;
}

/* 表单行样式 */
.firmware-modal__row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 12px;
}

.firmware-modal__row .el-form-item {
    flex: 1;
    min-width: 240px;
    margin-bottom: 12px;
}

/* 表单项样式 */
.firmware-modal .el-form-item__label {
    font-size: 13px;
    color: #606266;
    padding-right: 12px;
    font-weight: 500;
}

.firmware-modal .el-form-item__content {
    line-height: 36px;
}

.firmware-modal .el-input__inner {
    height: 36px;
    line-height: 36px;
    font-size: 13px;
    border-radius: 6px;
    border-color: #dcdfe6;
    transition: all 0.25s ease;
}

.firmware-modal .el-input__inner:hover {
    border-color: #c0c4cc;
}

.firmware-modal .el-input__inner:focus {
    border-color: #6777ef;
    box-shadow: 0 0 0 2px rgba(103, 119, 239, 0.2);
}

.firmware-modal .el-textarea__inner {
    font-size: 13px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.25s ease;
}

.firmware-modal .el-textarea__inner:hover {
    border-color: #c0c4cc;
}

.firmware-modal .el-textarea__inner:focus {
    border-color: #6777ef;
    box-shadow: 0 0 0 2px rgba(103, 119, 239, 0.2);
}

/* 上传组件样式 */
.firmware-modal .el-upload {
    width: 100%;
}

.firmware-modal__upload-area {
    display: flex;
    align-items: center;
    gap: 12px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 24px;
    width: 100%;
    background-color: rgba(250, 250, 250, 0.6);
    transition: all 0.3s ease;
}

.firmware-modal__upload-area:hover {
    border-color: #6777ef;
    background-color: rgba(103, 119, 239, 0.05);
    transform: translateY(-2px);
}

.firmware-modal__upload-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    height: 38px;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 500;
    background-color: #6777ef;
    color: white;
    border-color: #6777ef;
    border-radius: 6px;
    transition: all 0.25s ease;
}

.firmware-modal__upload-button:hover {
    background-color: #5566d4;
    border-color: #5566d4;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(103, 119, 239, 0.25);
}

.firmware-modal__upload-tip {
    color: #909399;
    font-size: 13px;
    line-height: 1.5;
}

/* 文件上传区域样式 */
.firmware-modal .el-upload-dragger {
    width: 100%;
    height: auto;
    padding: 24px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background-color: rgba(250, 250, 250, 0.6);
    transition: all 0.3s ease;
}

.firmware-modal .el-upload-dragger:hover {
    border-color: #6777ef;
    background-color: rgba(103, 119, 239, 0.05);
    transform: translateY(-2px);
}

.firmware-modal .el-upload__text {
    color: #606266;
    font-size: 14px;
    margin-top: 12px;
}

/* 文件列表样式 */
.firmware-modal .el-upload-list {
    margin-top: 12px;
}

.firmware-modal .el-upload-list__item {
    transition: all 0.3s;
    font-size: 13px;
    border-radius: 4px;
    padding: 8px 12px;
}

.firmware-modal .el-upload-list__item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 按钮样式 */
.firmware-modal__footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.firmware-modal__button-submit {
    padding: 8px 20px;
    font-weight: 600;
    font-size: 14px;
    border-radius: 6px;
    background: linear-gradient(135deg, #6777ef, #5566d4);
    border-color: #6777ef;
    box-shadow: 0 2px 6px rgba(103, 119, 239, 0.4);
    transition: all 0.25s ease;
}

.firmware-modal__button-submit:hover {
    background: linear-gradient(135deg, #5566d4, #4455c3);
    border-color: #5566d4;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(103, 119, 239, 0.5);
}

.firmware-modal__button-cancel {
    padding: 8px 20px;
    font-size: 14px;
    border-radius: 6px;
    transition: all 0.25s ease;
}

.firmware-modal__button-cancel:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
}

/* 禁用输入框样式 */
.firmware-modal .el-input.is-disabled .el-input__inner {
    background-color: #f8f9fa;
    color: #909399;
    border-color: #e4e7ed;
}

/* 必填项星号样式 */
.firmware-modal .el-form-item.is-required .el-form-item__label:before {
    color: #f56c6c;
}

/* 旋转动画 */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 模态框动画 */
.firmware-modal .el-dialog {
    transform: translateY(-20px);
    opacity: 0;
    animation: dialogFadeIn 0.3s ease forwards;
}

@keyframes dialogFadeIn {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 表单项动画 */
.firmware-modal .el-form-item {
    opacity: 0;
    animation: formItemFadeIn 0.5s ease forwards;
    animation-delay: calc(var(--item-index, 0) * 0.05s);
}

@keyframes formItemFadeIn {
    to { opacity: 1; }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .firmware-modal__row {
        flex-direction: column;
        gap: 0;
    }
    
    .firmware-modal__row .el-form-item {
        width: 100%;
    }
    
    .firmware-modal .el-dialog__body {
        padding: 16px;
    }
    
    .firmware-modal__section {
        padding: 14px;
    }
}

/* 上传组件样式 */
.firmware-container .el-upload__tip {
    color: #666;
    font-size: 12px;
    margin-top: 8px;
}

/* 加载状态优化 */
.firmware-container .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.8);
}

/* 分页组件通用样式 */
.firmware-container .el-pagination {
    font-size: 14px;
}

.firmware-container .el-pagination .el-pager li {
    min-width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 13px;
}

.firmware-container .el-pagination .btn-prev,
.firmware-container .el-pagination .btn-next {
    min-width: 32px;
    height: 32px;
    line-height: 32px;
}

.firmware-container .el-pagination .el-pagination__sizes .el-select .el-input {
    width: 100px;
}

.firmware-container .el-pagination .el-pagination__jump .el-input {
    width: 50px;
} 