.marking-record-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.marking-record-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.marking-record-header h2 {
    font-size: 18px;
    color: #333;
    margin: 0;
    font-weight: 500;
}

.marking-record-content {
    min-height: 400px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
    .marking-record-container {
        padding: 15px;
    }
    
    .marking-record-header h2 {
        font-size: 16px;
    }
}

/* 打标记录查询样式 */
:root {
    /* 字体大小 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    
    /* 颜色 */
    --color-primary: #1976D2;
    --color-primary-light: #2196F3;
    --color-primary-dark: #1565C0;
    --color-text-primary: #1a1f36;
    --color-text-secondary: #606266;
    --color-text-tertiary: #909399;
    --color-border: #dcdfe6;
    --color-background: #f5f7fa;
    --color-white: #ffffff;
    --color-success: #67c23a;
    --color-warning: #e6a23c;
    --color-danger: #f56c6c;
    --color-info: #909399;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    
    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 基础容器样式 */
.marking-record {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: var(--spacing-xl);
    background-color: var(--color-white);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    color: var(--color-text-primary);
}

/* 标题样式 */
.marking-record__title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    position: relative;
    letter-spacing: 0.5px;
}

.marking-record__title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, 
        rgba(26, 31, 54, 0.8) 0%,
        rgba(26, 31, 54, 0.6) 50%,
        rgba(26, 31, 54, 0.2) 100%
    );
}

.marking-record__subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

/* 查询头部布局 */
.marking-record__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

/* 查询类型区域 */
.marking-record__type {
    flex: 1;
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

/* 查询类型选项 */
.marking-record__type-options {
    display: flex;
    gap: var(--spacing-xl);
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
}

.marking-record__type-option {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.marking-record__type-option:hover {
    color: var(--color-primary);
}

.marking-record__type-radio {
    margin-right: var(--spacing-sm);
    cursor: pointer;
}

/* 查询类型文本字体大小 */  
.marking-record__type-text {
    white-space: nowrap;
    font-size: var(--font-size-base);
}

/* 高级筛选按钮 */
.marking-record__filter {
    margin-left: 20px;
    display: flex;
    align-items: center;
}

.marking-record__filter-btn {
    padding: 8px 16px;
    background-color: var(--color-white);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    height: 32px;
    display: flex;
    align-items: center;
}

.marking-record__filter-btn:hover {
    background-color: var(--color-background);
    border-color: var(--color-primary-light);
    color: var(--color-primary);
}

.marking-record__filter-text {
    font-size: 14px;
    margin-right: 8px;
}

.marking-record__filter-icon {
    font-size: 12px;
    transition: transform 0.3s;
}

.marking-record__filter-btn.active .marking-record__filter-icon {
    transform: rotate(180deg);
}

/* 高级筛选展开区域 */
.marking-record__advanced-filter {
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.marking-record__date-range {
    display: flex;
    gap: 30px;
    align-items: center;
}

.marking-record__date-field {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
    background: var(--color-white);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
}

/* 高级筛选日期标签 */
.marking-record__date-label {
    color: var(--color-text-secondary);
    font-weight: 500;
    white-space: nowrap;
    min-width: 70px;
    font-size: var(--font-size-sm);
}

.marking-record__date-input {
    flex: 1;
    height: 36px;
    padding: 0 12px;
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    background-color: #fff;
    transition: all 0.3s ease;
}

.marking-record__date-input:hover {
    border-color: var(--color-primary-light);
}

.marking-record__date-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* 查询表单样式 */
.marking-record__form {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-end;
}

/* 表单组样式 */
.marking-record__form-group {
    flex: 1;
    min-width: 200px;
    position: relative;
}

/* 按钮容器 */
.marking-record__buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 标签样式 */
.marking-record__label {
    display: block;
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
    font-weight: 500;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

/* 输入框基础样式 */
.marking-record__input {
    width: 100%;
    height: 40px;
    padding: 0 var(--spacing-md);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    line-height: 2.5rem;
    color: var(--color-text-primary);
    background-color: #fff;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

/* 新增：复选框组特定样式 */
.marking-record__form-group--checkbox {
    display: flex;
    align-items: center; /* 垂直居中对齐复选框和文本 */
    flex-basis: auto; /* 根据内容自动调整宽度 */
    flex-grow: 0; /* 不拉伸 */
    margin-left: 15px; /* 与输入框保持一些间距 */
}

.marking-record__checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.marking-record__checkbox {
    margin-right: var(--spacing-sm);
    cursor: pointer;
    /* 可以根据需要调整复选框大小 */
    width: 16px; 
    height: 16px;
}

.marking-record__checkbox-text {
    white-space: nowrap;
}

.marking-record__input:hover {
    border-color: var(--color-primary-light);
}

/* 按钮样式 */
.marking-record__btn {
    height: 40px;
    padding: 0 var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid transparent;
}

.marking-record__btn--search {
    background-color: var(--color-primary);
    color: var(--color-white);
}

.marking-record__btn--search:hover {
    background-color: var(--color-primary-dark);
}

.marking-record__btn--reset {
    background-color: var(--color-white);
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border);
}

.marking-record__btn--reset:hover {
    background-color: var(--color-background);
    border-color: var(--color-text-secondary);
}

.marking-record__btn i {
    margin-right: 5px;
}

/* 结果表格样式 */
.marking-record__results {
    margin-top: 30px;
}

.marking-record__table-container {
    position: relative;
    overflow-x: auto !important;
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
}

.marking-record__table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
    table-layout: fixed;
    min-width: 1300px;
}

.marking-record__table th {
    background-color: var(--color-background);
    color: var(--color-text-primary);
    font-weight: 500;
    text-align: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    white-space: nowrap;
}

.marking-record__table td {
    padding: var(--spacing-md) var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
    color: var(--color-text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center; /* Center align text in cells */
}

.marking-record__table tr:hover {
    background-color: rgba(33, 150, 243, 0.05);
}

.marking-record__table tr:last-child td {
    border-bottom: none;
}

/* 状态标签 */
.marking-record__status {
    display: inline-block;
    padding: 2px 10px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    line-height: 1.5;
    text-align: center;
    min-width: 60px;
}

.marking-record__status--completed {
    background-color: rgba(103, 194, 58, 0.1);
    color: var(--color-success);
}

.marking-record__status--in-progress {
    background-color: rgba(230, 162, 60, 0.1);
    color: var(--color-warning);
}

.marking-record__status--pending {
    background-color: rgba(144, 147, 153, 0.1);
    color: var(--color-info);
}

/* 操作按钮 */
.marking-record__action-btn {
    color: var(--color-primary);
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-sm);
    padding: 4px 8px;
    transition: all 0.3s;
    border-radius: var(--radius-sm);
}

.marking-record__action-btn:hover {
    background-color: rgba(33, 150, 243, 0.1);
}

/* 特定于作废按钮的样式 */
.marking-record__action-btn--void {
    color: var(--color-danger); /* 使用预定义的危险颜色 */
}

.marking-record__action-btn--void:hover {
    background-color: rgba(245, 108, 108, 0.1); /* 鼠标悬停时用危险色的浅色背景 */
}

.marking-record__action-btn--void i {
    margin-right: 4px;
}

/* 已作废状态文本样式 */
.marking-record__status-text--voided {
    color: var(--color-text-tertiary); /* 使用预定义的次要文本颜色 */
    font-style: italic;
}

/* 分页样式 */
.marking-record__pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: var(--spacing-md) 0;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.marking-record__pagination-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.marking-record__page-size {
    width: 70px;
    height: 32px;
    padding: 0 var(--spacing-sm);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    color: var(--color-text-secondary);
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.marking-record__page-size:hover {
    border-color: var(--color-primary-light);
}

.marking-record__total {
    margin-left: 8px;
}

.marking-record__pagination-controls {
    display: flex;
    align-items: center;
    gap: 4px;
}

.marking-record__pagination-btn {
    min-width: 32px;
    height: 32px;
    padding: 0 4px;
    border: 1px solid var(--color-border);
    background-color: #fff;
    color: var(--color-text-secondary);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: var(--font-size-sm);
}

.marking-record__pagination-btn:hover:not(.disabled) {
    color: var(--color-primary);
    border-color: var(--color-primary-light);
    background-color: rgba(33, 150, 243, 0.1);
}

.marking-record__pagination-btn.disabled {
    color: var(--color-text-tertiary);
    cursor: not-allowed;
    background-color: var(--color-background);
}

.marking-record__pagination-btn.active {
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

/* 无数据提示 */
.marking-record__no-data {
    text-align: center;
    padding: 40px 0;
    color: var(--color-text-tertiary);
    font-size: var(--font-size-sm);
}

/* 表格加载指示器样式 */
.table-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--color-border);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .marking-record {
        padding: var(--spacing-md);
    }
    
    .marking-record__header {
        flex-direction: column;
    }
    
    .marking-record__filter {
        margin-left: 0;
        margin-top: var(--spacing-md);
        width: 100%;
    }
    
    .marking-record__filter-btn {
        width: 100%;
        justify-content: center;
    }
    
    .marking-record__date-range {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .marking-record__date-field {
        width: 100%;
    }
    
    .marking-record__form {
        flex-direction: column;
    }
    
    .marking-record__form-group {
        width: 100%;
    }
    
    .marking-record__type-options {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }
    
    .marking-record__type-option {
        margin-right: var(--spacing-md);
    }
    
    .marking-record__pagination {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .marking-record__pagination-info {
        width: 100%;
        justify-content: flex-start;
    }
    
    .marking-record__pagination-controls {
        width: 100%;
        justify-content: center;
    }
}

/* 导出按钮样式 */
.marking-record__btn--export {
    background-color: #67c23a; /* 绿色背景 */
    color: #ffffff; /* 白色文字 */
}

.marking-record__btn--export:hover {
    background-color: #5daf34; /* 鼠标悬停时深一点的绿色 */
    color: #ffffff;
} 