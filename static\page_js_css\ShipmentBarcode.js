// 使用IIFE创建模块作用域
(function() {
    // 将autoMode移入模块作用域
    let autoMode = false;
    let snList = [];
    let lastScanTime = 0;
    const SCAN_INTERVAL = 100; // 扫码枪输入间隔时间（毫秒）
    let isAddingToDatabase = false; // 防止重复提交

    // 在文件开头添加分页状态管理
    window.shipmentBarcodeState = {
        currentPage: 1,
        pageSize: 5,
        pageSizeOptions: [5, 10, 20, 50, 100],
        totalCount: 0,
        totalPages: 0
    };

    // 添加新的样式
    const style = document.createElement('style');
    style.textContent = `
        .shipment-barcode__list-title {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .title-text {
            font-weight: bold;
        }
        .count-text {
            color:rgb(109, 197, 227);
            font-weight: bold;
            margin-left: auto;
        }
    `;
    document.head.appendChild(style);

    function initShipmentBarcodePage() {
        const content = document.getElementById('content');
        content.innerHTML = createShipmentBarcodeHTML();
        
        // 初始化事件监听
        initEventListeners();
        
        // 设置出库时间为只读，不设置默认值
        const dateTimeInput = document.getElementById('shipment-datetime');
        dateTimeInput.readOnly = true; // 设置为只读
        // 不再设置默认时间
    }

    function createShipmentBarcodeHTML() {
        return `
            <section class="shipment-barcode">
                <header>
                    <h1 class="shipment-barcode__title">基本信息</h1>
                </header>
                
                <form class="shipment-barcode__form">
                    <div class="shipment-barcode__form-group">
                        <label class="shipment-barcode__label" for="box-number">箱单流水号</label>
                        <input type="text" id="box-number" class="shipment-barcode__input" placeholder="请输入箱单流水号" required>
                    </div>
                    
                    <div class="shipment-barcode__form-group">
                        <label class="shipment-barcode__label" for="shipment-number">出库单号</label>
                        <input type="text" id="shipment-number" class="shipment-barcode__input" placeholder="请输入出库单号" required>
                    </div>
                    
                    <div class="shipment-barcode__form-group">
                        <label class="shipment-barcode__label" for="customer-name">客户名称</label>
                        <input type="text" id="customer-name" class="shipment-barcode__input" placeholder="请输入客户名称" required>
                    </div>
                    
                    <div class="shipment-barcode__form-group">
                        <label class="shipment-barcode__label" for="work-order">批次号 <span style="color: #409eff; font-weight: normal; font-size: 12px;">{默认"无"，不做批次号比对}</span></label>
                        <input type="text" id="work-order" class="shipment-barcode__input" placeholder="默认为无，不做比对" value="无">
                    </div>
                    
                    <div class="shipment-barcode__form-group">
                        <label class="shipment-barcode__label" for="shipment-datetime">出库时间</label>
                        <input type="datetime-local" id="shipment-datetime" class="shipment-barcode__datetime" readonly>
                    </div>
                </form>

                <section class="shipment-barcode__sn-input-section">
                    <div class="shipment-barcode__auto-mode">
                        <label class="shipment-barcode__switch">
                            <input type="checkbox" id="auto-mode-toggle" class="shipment-barcode__switch-input">
                            <span class="shipment-barcode__switch-slider"></span>
                        </label>
                        <span class="shipment-barcode__mode-text">自动添加模式</span>
                        <span id="mode-status" class="shipment-barcode__mode-status"></span>
                        <div class="shipment-barcode__mode-tip">
                            <i class="fas fa-question-circle"></i>
                            <div class="shipment-barcode__mode-tip-content">
                                <h4>模式说明：</h4>
                                <p><strong>手动模式：</strong></p>
                                <ul>
                                    <li>需要手动点击"添加"按钮或按回车键确认</li>
                                    <li>适合手动输入SN号的场景</li>
                                    <li>可以在输入时检查和修正</li>
                                </ul>
                                <p><strong>自动模式：</strong></p>
                                <ul>
                                    <li>扫码后自动添加，无需手动确认</li>
                                    <li>适合使用扫码枪快速录入</li>
                                    <li>提高录入效率，减少操作步骤</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="shipment-barcode__customer-check">
                        <label class="shipment-barcode__switch">
                            <input type="checkbox" id="check-sn-customer-toggle" class="shipment-barcode__switch-input">
                            <span class="shipment-barcode__switch-slider"></span>
                        </label>
                        <span class="shipment-barcode__check-text">检查SN是否属于该客户定制</span>
                    </div>

                    <div id="auto-input" class="shipment-barcode__auto-input">
                        <div class="shipment-barcode__scan-title">扫描输入（自动添加）</div>
                        <div class="shipment-barcode__input-container">
                            <div class="shipment-barcode__input-wrapper">
                                <input type="text" id="auto-sn-input" class="shipment-barcode__sn-input" 
                                       placeholder="扫描条形码自动添加">
                            </div>
                        </div>
                        <div class="shipment-barcode__scan-hint">扫描后将自动添加到数据库</div>
                    </div>

                    <div class="shipment-barcode__manual-input">
                        <div class="shipment-barcode__manual-title">手动输入</div>
                        <div class="shipment-barcode__input-container">
                            <div class="shipment-barcode__input-wrapper">
                                <input type="text" id="sn-input" class="shipment-barcode__sn-input" 
                                       placeholder="输入产品SN号">
                            </div>
                            <button type="button" class="shipment-barcode__btn--add" id="add-sn-btn">
                                <i class="fas fa-plus"></i>
                                添加
                            </button>
                        </div>
                    </div>
                </div>

                <div class="shipment-barcode__list-header">
                    <h3 class="shipment-barcode__list-title">
                        <span class="title-text">产品SN号列表</span>
                        <span class="count-text">共计 <span id="sn-count">0</span> pcs</span>
                    </h3>
                    <div class="shipment-barcode__list-actions">
                        <button type="button" class="shipment-barcode__btn--import" id="import-btn">
                            <i class="fas fa-file-import"></i> 导入SN
                        </button>
                        <button type="button" class="shipment-barcode__btn--export" id="export-btn">
                            <i class="fas fa-file-excel"></i> 导出SN
                        </button>
                        <button type="button" class="shipment-barcode__btn--submit" id="submit-btn">完成录入</button>
                        <input type="file" id="file-input" accept=".xlsx,.xls" style="display: none">
                    </div>
                </div>
                
                <div class="shipment-barcode__sn-list" id="sn-list">
                    暂无SN号，请添加产品SN号
                </div>
            </section>
        `;
    }

    function initEventListeners() {
        const autoModeToggle = document.getElementById('auto-mode-toggle');
        const modeStatus = document.getElementById('mode-status');
        const autoInput = document.getElementById('auto-input');
        const manualInput = document.querySelector('.shipment-barcode__manual-input');
        const autoSnInput = document.getElementById('auto-sn-input');
        const snInput = document.getElementById('sn-input');
        const addSnBtn = document.getElementById('add-sn-btn');
        const submitBtn = document.getElementById('submit-btn');
        const exportBtn = document.getElementById('export-btn');
        const importBtn = document.getElementById('import-btn');
        const fileInput = document.getElementById('file-input');
        
        // 页面加载完成后，默认聚焦到手动输入框
        if(snInput) snInput.focus();
        
        if(autoModeToggle) {
            autoModeToggle.addEventListener('change', (e) => {
                autoMode = e.target.checked;
                
                // 更新模式状态显示
                modeStatus.textContent = autoMode ? '已启用' : '';
                modeStatus.style.display = autoMode ? 'inline' : 'none';
                
                // 切换输入区域显示
                if (autoMode) {
                    autoInput.classList.add('active');
                    manualInput.classList.add('hidden');
                    if(autoSnInput) autoSnInput.focus(); // 自动模式下聚焦到自动输入框
                    document.removeEventListener('keydown', handleAutoModeScan);
                } else {
                    autoInput.classList.remove('active');
                    manualInput.classList.remove('hidden');
                    if(snInput) snInput.focus(); // 手动模式下聚焦到手动输入框
                    document.removeEventListener('keydown', handleAutoModeScan);
                }
            });
        }

        // ****** 修改：手动添加 SN 按钮点击事件 ******
        if(addSnBtn) {
            addSnBtn.addEventListener('click', async () => {
                const sn = snInput.value.trim();
                if (!sn) {
                    Swal.fire('提示', '请输入SN号', 'info');
                    return;
                }

                // 进行SN信息检查（版本状态和客户定制开关）
                await checkSnInfo(sn);

                const customerCheckToggle = document.getElementById('check-sn-customer-toggle');
                const customerInput = document.getElementById('customer-name');
                let canProceed = true; // 默认可以继续

                if (customerCheckToggle && customerInput && customerCheckToggle.checked) {
                    const shipmentCustomerName = customerInput.value.trim();
                    if (!shipmentCustomerName) {
                        Swal.fire('提示', '请输入客户名称以进行SN客户验证。', 'info');
                        // 仅提示，不阻止
                    } else {
                        // 等待验证完成，并获取是否应阻止的结果
                        canProceed = await validateSnAgainstCustomer(sn, shipmentCustomerName);
                    }
                }

                // ** 只有在验证过程没有要求阻止时才添加 **
                if (canProceed) {
                    addSNToDatabase(sn);
                    snInput.value = '';
                    snInput.focus();
                } else {
                    // 如果验证要求阻止 (canProceed is false)，则聚焦并选中输入框
                    snInput.focus();
                    snInput.select();
                }
            });
        }

        // ****** 修改：手动输入框回车事件 ******
        if(snInput) {
            snInput.addEventListener('keypress', async (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const sn = snInput.value.trim();
                    if (!sn) { return; }

                    // 首先进行SN信息检查
                    await checkSnInfo(sn);

                    const customerCheckToggle = document.getElementById('check-sn-customer-toggle');
                    const customerInput = document.getElementById('customer-name');
                    let canProceed = true; // 默认可以继续

                    if (customerCheckToggle && customerInput && customerCheckToggle.checked) {
                        const shipmentCustomerName = customerInput.value.trim();
                        if (!shipmentCustomerName) {
                            Swal.fire('提示', '请输入客户名称以进行SN客户验证。', 'info');
                           // 仅提示，不阻止
                        } else {
                             // 等待验证完成，并获取是否应阻止的结果
                            canProceed = await validateSnAgainstCustomer(sn, shipmentCustomerName);
                        }
                    }

                   // ** 只有在验证过程没有要求阻止时才添加 **
                   if (canProceed) {
                        addSNToDatabase(sn);
                        snInput.value = '';
                        snInput.focus();
                    } else {
                         // 如果验证要求阻止 (canProceed is false)，则聚焦并选中输入框
                        snInput.focus();
                        snInput.select();
                    }
                }
            });
        }

        // ****** 修改：自动模式输入框回车事件 ******
        if(autoSnInput) {
            autoSnInput.addEventListener('keypress', async (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const sn = autoSnInput.value.trim();
                    if (!sn) { return; }

                    // 首先进行SN信息检查
                    await checkSnInfo(sn);

                    const customerCheckToggle = document.getElementById('check-sn-customer-toggle');
                    const customerInput = document.getElementById('customer-name');
                    let canProceed = true; // 默认可以继续

                    if (customerCheckToggle && customerInput && customerCheckToggle.checked) {
                        const shipmentCustomerName = customerInput.value.trim();
                        if (!shipmentCustomerName) {
                            Swal.fire('错误', '请先填写客户名称并确保其正确...', 'error');
                            autoSnInput.value = '';
                            autoSnInput.focus();
                            return; // **这里明确阻止**
                        }
                         // 等待验证完成，并获取是否应阻止的结果
                        canProceed = await validateSnAgainstCustomer(sn, shipmentCustomerName);
                    }

                    // ** 只有在验证过程没有要求阻止时才添加 **
                    if (canProceed) {
                        addSNToDatabase(sn);
                        autoSnInput.value = '';
                        autoSnInput.focus();
                    } else {
                        // 如果验证要求阻止 (canProceed is false)，则清空并聚焦，准备下次扫描
                         autoSnInput.value = '';
                         autoSnInput.focus();
                    }
                }
            });
        }

        // 提交表单
        if(submitBtn) submitBtn.addEventListener('click', handleSubmit);

        // 添加必填项验证
        validateRequiredFields();

        // 添加输入框失去焦点时的处理
        const inputFields = [
            'box-number',
            'shipment-number',
            'customer-name',
            'work-order',
            'sn-input',
            'auto-sn-input'
        ];

        inputFields.forEach(fieldId => {
            const input = document.getElementById(fieldId);
            if(input) {
                input.addEventListener('blur', function() {
                    this.value = sanitizeInput(this.value);
                    
                    // 当基本信息字段变化时，重新加载SN列表
                    if (['box-number', 'shipment-number', 'work-order'].includes(fieldId)) {
                        loadSNList();
                    }
                    
                    // 只有当点击的不是输入框和按钮时才重新聚焦到SN输入框
                    if ((fieldId === 'sn-input' && !autoMode) || (fieldId === 'auto-sn-input' && autoMode)) {
                        // 使用 requestAnimationFrame 确保在可能的点击事件之后执行聚焦
                        requestAnimationFrame(() => {
                             const activeElement = document.activeElement;
                             // 检查当前焦点元素是否是输入框、按钮或选择框
                             const isInteractiveElement = activeElement && (
                                 activeElement.tagName === 'INPUT' ||
                                 activeElement.tagName === 'BUTTON' ||
                                 activeElement.tagName === 'SELECT' ||
                                 activeElement.closest('.swal2-container') // 不在 SweetAlert 弹窗内时聚焦
                             );

                             // 如果焦点不在这些元素上，或者页面没有焦点，则重新聚焦
                             if (!isInteractiveElement) {
                                 if (autoMode && document.getElementById('auto-sn-input')) {
                                     document.getElementById('auto-sn-input').focus();
                                 } else if (!autoMode && document.getElementById('sn-input')) {
                                     document.getElementById('sn-input').focus();
                                 }
                             }
                        });
                    }
                });
            }
        });
        
        // 监听基本信息字段的变化，以便在变化时重新加载SN列表
        const boxNumInput = document.getElementById('box-number');
        const shipNumInput = document.getElementById('shipment-number');
        const workOrderInput = document.getElementById('work-order');
        if(boxNumInput) boxNumInput.addEventListener('change', loadSNList);
        if(shipNumInput) shipNumInput.addEventListener('change', loadSNList);
        if(workOrderInput) workOrderInput.addEventListener('change', loadSNList);
        
        // 初始加载SN列表
        loadSNList();

        // 添加导出按钮事件监听
        if(exportBtn) exportBtn.addEventListener('click', exportToExcel);

        // 添加导入按钮事件监听
        if(importBtn && fileInput) {
            importBtn.addEventListener('click', () => {
                fileInput.click();
            });
            fileInput.addEventListener('change', handleFileImport);
        }
    }

    function handleAutoModeScan(e) {
        const now = Date.now();
        if (now - lastScanTime < SCAN_INTERVAL) {
            // 扫码枪快速输入，忽略手动输入
            return;
        }
        lastScanTime = now;

        if (e.key === 'Enter') {
            const autoSnInput = document.getElementById('auto-sn-input');
            const sn = autoSnInput.value.trim();
            
            // 移除对lastProcessedSN的检查，所有输入都发送到服务器验证
            if (sn) {
                addSNToDatabase(sn);
                autoSnInput.value = '';
                autoSnInput.focus(); // 添加后重新聚焦
            }
        }
    }

    function addSNToDatabase(sn) {
        // 去除空格
        sn = sanitizeInput(sn);
        
        if (!sn) {
            // 在自动模式下，如果SN为空，不显示错误提示，直接返回
            if (autoMode) {
                return;
            }
            Swal.fire('错误', '请输入SN号', 'error');
            return;
        }
        
        // 获取必要的表单数据
        const boxNumber = sanitizeInput(document.getElementById('box-number').value);
        const shipmentNumber = sanitizeInput(document.getElementById('shipment-number').value);
        const customerName = sanitizeInput(document.getElementById('customer-name').value);
        
        // 验证必填字段
        if (!boxNumber || !shipmentNumber || !customerName) {
            Swal.fire('错误', '请先填写箱单流水号、出库单号和客户名称', 'error');
            return;
        }
        
        // 防止重复提交
        if (isAddingToDatabase) {
            Logger.warn('Add SN request already in progress, skipping duplicate.');
            return;
        }
        
        isAddingToDatabase = true;
        
        // 准备提交的数据
        const submitData = {
            boxNumber,
            shipmentNumber,
            customerName,
            snNumber: sn
        };
        
        // 发送到服务器
        fetch('/api/shipment-barcode/add-sn', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(submitData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 添加成功，重新加载SN列表
                loadSNList();
                
                // 如果有重复SN警告，显示确认对话框
                if (data.hasDuplicates) {
                    Swal.fire({
                        title: '注意',
                        text: data.warning,
                        icon: 'warning',
                        showCancelButton: false,
                        confirmButtonText: '确定'
                    });
                } else {
                    // 显示成功提示
                    const Toast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 2000,
                        timerProgressBar: true
                    });
                    
                    Toast.fire({
                        icon: 'success',
                        title: 'SN号添加成功'
                    });
                }
            } else {
                // 统一使用模态框错误提示，包含具体的SN号
                let errorMessage = data.message || 'SN号添加失败';
                
                // 如果错误信息中没有包含SN号，则添加SN号
                if (!errorMessage.includes(sn)) {
                    errorMessage = `${sn} ${errorMessage}`;
                }
                
                Swal.fire({
                    icon: 'error',
                    title: '错误',
                    text: errorMessage,
                    confirmButtonText: '确定'
                });
            }
        })
        .catch(error => {
            isAddingToDatabase = false;
            
            // 统一使用模态框错误提示，包含具体的SN号
            let errorMessage = error.message || 'SN号添加失败';
            
            // 如果错误信息中没有包含SN号，则添加SN号
            if (!errorMessage.includes(sn)) {
                errorMessage = `${sn} ${errorMessage}`;
            }
            
            Swal.fire({
                icon: 'error',
                title: '错误',
                text: errorMessage,
                confirmButtonText: '确定'
            });
        })
        .finally(() => {
            isAddingToDatabase = false;
        });
    }

    async function loadSNList() {
        try {
            const boxNumber = document.getElementById('box-number').value.trim();
            const shipmentNumber = document.getElementById('shipment-number').value.trim();
            const workOrder = document.getElementById('work-order').value.trim();
            
            if (!boxNumber || !shipmentNumber) {
                snList = [];
                updateSNList();
                return;
            }

            const response = await fetch(`/api/shipment-barcode/get-sn-list?boxNumber=${encodeURIComponent(boxNumber)}&shipmentNumber=${encodeURIComponent(shipmentNumber)}&workOrder=${encodeURIComponent(workOrder)}`);
            const data = await response.json();

            if (data.success) {
                // 更新全局 snList
                snList = data.data;
                updateSNList();
            } else {
                throw new Error(data.message || '获取SN列表失败');
            }
        } catch (error) {
            Logger.error('Error loading SN list:', error);
            Swal.fire('错误', error.message, 'error');
        }
    }

    function updateSNList() {
        const snListContainer = document.getElementById('sn-list');
        const snCountElement = document.getElementById('sn-count');
        
        // 更新SN号计数
        snCountElement.textContent = snList.length;
        window.shipmentBarcodeState.totalCount = snList.length;
        
        if (snList.length === 0) {
            snListContainer.innerHTML = '暂无SN号，请添加产品SN号';
            snListContainer.classList.remove('has-items');
            return;
        }

        // 计算分页
        const { currentPage, pageSize } = window.shipmentBarcodeState;
        const totalPages = Math.ceil(snList.length / pageSize);
        window.shipmentBarcodeState.totalPages = totalPages;

        // 计算当前页的数据
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, snList.length);
        const currentPageData = snList.slice(startIndex, endIndex);

        snListContainer.classList.add('has-items');
        const paginationHtml = `
            <div class="table-footer">
                <div class="table-length">
                    <div class="page-size-selector">
                        每页
                        <select id="pageSizeSelect">
                            ${window.shipmentBarcodeState.pageSizeOptions.map(size => 
                                `<option value="${size}" ${size === window.shipmentBarcodeState.pageSize ? 'selected' : ''}>
                                    ${size}
                                </option>`
                            ).join('')}
                        </select>
                        条
                    </div>
                    <div class="total-count">
                        共计 <span id="totalRecords">${snList.length}</span> 条
                    </div>
                </div>
                <div class="table-pagination">
                    <button class="btn btn-icon" id="firstPageBtn" ${currentPage === 1 ? 'disabled' : ''}>
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button class="btn btn-icon" id="prevPageBtn" ${currentPage === 1 ? 'disabled' : ''}>
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <div class="pagination-pages" id="paginationPages">
                        ${generatePaginationButtons(currentPage, totalPages)}
                    </div>
                    <button class="btn btn-icon" id="nextPageBtn" ${currentPage === totalPages ? 'disabled' : ''}>
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button class="btn btn-icon" id="lastPageBtn" ${currentPage === totalPages ? 'disabled' : ''}>
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        `;

        // 更新 DOM
        snListContainer.innerHTML = `
            <div class="table-section card">
                <table class="shipment-barcode__table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>产品SN号</th>
                            <th>版本状态</th>
                            <th>批次状态</th>
                            <th>录入时间</th>
                            <th>储存状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${currentPageData.map((item, index) => {
                            // 确保使用正确的属性名
                            const snNumber = item.sn_number || item.snNumber; // 后端返回的可能是 sn_number
                            const createdAt = item.created_at || item.createdAt; // 后端返回的可能是 created_at
                            const recordId = item.record_id || item.recordId; // 后端返回的可能是 record_id
                            const versionStatus = item.version_status || item.versionStatus || '未知'; // 版本状态
                            const batchStatus = item.batch_status || item.batchStatus || ''; // 批次状态
                            
                            // 根据版本状态设置样式
                            const versionStatusClass = versionStatus.includes('异常') ? 'version-status-error' : 'version-status-normal';
                            
                            // 根据批次状态设置样式
                            const getBatchStatusHtml = (status) => {
                                if (!status || status === '') {
                                    return '';  // 批次状态为空时不显示任何内容
                                } else if (status === '异常') {
                                    return `<span class="batch-status-error">${status}</span>`;
                                } else if (status === '正常') {
                                    return `<span class="batch-status-normal">${status}</span>`;
                                } else {
                                    return status;
                                }
                            };
                            
                            return `
                                <tr class="${index === 0 && currentPage === 1 ? 'new-item' : ''}">
                                    <td>${startIndex + index + 1}</td>
                                    <td>${snNumber}</td>
                                    <td><span class="${versionStatusClass}">${versionStatus}</span></td>
                                    <td>${getBatchStatusHtml(batchStatus)}</td>
                                    <td>${createdAt}</td>
                                    <td><span class="storage-status">完成</span></td>
                                    <td>
                                        <button type="button" class="shipment-barcode__btn--delete" onclick="deleteSN(${recordId})">
                                            删除
                                        </button>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
                ${paginationHtml}
            </div>
        `;

        // 添加事件监听器
        document.getElementById('pageSizeSelect').addEventListener('change', function() {
            const newSize = parseInt(this.value);
            window.shipmentBarcodeState.pageSize = newSize;
            window.shipmentBarcodeState.currentPage = 1;
            updateSNList();
        });

        document.getElementById('firstPageBtn').addEventListener('click', function() {
            if (currentPage !== 1) {
                window.shipmentBarcodeState.currentPage = 1;
                updateSNList();
            }
        });

        document.getElementById('prevPageBtn').addEventListener('click', function() {
            if (currentPage > 1) {
                window.shipmentBarcodeState.currentPage--;
                updateSNList();
            }
        });

        document.getElementById('nextPageBtn').addEventListener('click', function() {
            if (currentPage < totalPages) {
                window.shipmentBarcodeState.currentPage++;
                updateSNList();
            }
        });

        document.getElementById('lastPageBtn').addEventListener('click', function() {
            if (currentPage !== totalPages) {
                window.shipmentBarcodeState.currentPage = totalPages;
                updateSNList();
            }
        });

        // 为页码按钮添加事件监听
        document.querySelectorAll('.btn-page').forEach(button => {
            button.addEventListener('click', function() {
                const page = parseInt(this.textContent);
                if (page !== currentPage) {
                    window.shipmentBarcodeState.currentPage = page;
                    updateSNList();
                }
            });
        });
    }

    // 添加分页相关函数
    function generatePaginationButtons(currentPage, totalPages) {
        let buttons = '';
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);
        
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }

        if (startPage > 1) {
            buttons += '<span class="pagination-ellipsis">...</span>';
        }
        
        for (let i = startPage; i <= endPage; i++) {
            buttons += `
                <button class="btn btn-page ${i === currentPage ? 'active' : ''}" 
                        onclick="goToPage(${i})">
                    ${i}
                </button>
            `;
        }
        
        if (endPage < totalPages) {
            buttons += '<span class="pagination-ellipsis">...</span>';
        }

        return buttons;
    }

    function goToPage(page) {
        window.shipmentBarcodeState.currentPage = page;
        updateSNList();
    }

    function goToFirstPage() {
        goToPage(1);
    }

    function goToPrevPage() {
        const { currentPage } = window.shipmentBarcodeState;
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    }

    function goToNextPage() {
        const { currentPage, totalPages } = window.shipmentBarcodeState;
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    }

    function goToLastPage() {
        goToPage(window.shipmentBarcodeState.totalPages);
    }

    function changePageSize() {
        const newSize = parseInt(document.getElementById('pageSizeSelect').value);
        window.shipmentBarcodeState.pageSize = newSize;
        window.shipmentBarcodeState.currentPage = 1;
        updateSNList();
    }

    function deleteSN(id) {
        Swal.fire({
            title: '确认删除',
            text: '确定要删除这个SN号吗？',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                // 发送删除请求到服务器
                fetch(`/api/shipment-barcode/delete-sn/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 删除成功，重新加载SN列表
                        loadSNList();
                        
                        Swal.fire(
                            '已删除',
                            'SN号已成功删除',
                            'success'
                        );
                        
                        // 删除操作完成后重新聚焦到相应的输入框
                        setTimeout(() => {
                            if (autoMode) {
                                document.getElementById('auto-sn-input').focus();
                            } else {
                                document.getElementById('sn-input').focus();
                            }
                        }, 500);
                    } else {
                        throw new Error(data.message || '删除失败');
                    }
                })
                .catch(error => {
                    Swal.fire('错误', error.message, 'error');
                    
                    // 即使出错也重新聚焦
                    setTimeout(() => {
                        if (autoMode) {
                            document.getElementById('auto-sn-input').focus();
                        } else {
                            document.getElementById('sn-input').focus();
                        }
                    }, 500);
                });
            } else {
                // 用户取消删除，重新聚焦
                if (autoMode) {
                    document.getElementById('auto-sn-input').focus();
                } else {
                    document.getElementById('sn-input').focus();
                }
            }
        });
    }

    function handleSubmit() {
        Swal.fire({
            title: '完成录入',
            text: '确认已完成所有SN号的录入？',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: '确认',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: '录入完成',
                    text: '所有SN号已成功录入系统',
                    icon: 'success'
                }).then(() => {
                    // 清空表单，准备下一次录入
                    document.getElementById('box-number').value = '';
                    document.getElementById('shipment-number').value = '';
                    document.getElementById('customer-name').value = '';
                    document.getElementById('sn-input').value = '';
                    document.getElementById('auto-sn-input').value = '';
                    
                    // 重置SN列表
                    snList = [];
                    updateSNList();
                    
                    // 重新聚焦到手动输入框
                    setTimeout(() => {
                        document.getElementById('sn-input').focus();
                    }, 100);
                });
            } else {
                // 用户取消，重新聚焦
                if (autoMode) {
                    document.getElementById('auto-sn-input').focus();
                } else {
                    document.getElementById('sn-input').focus();
                }
            }
        });
    }

    function validateRequiredFields() {
        const requiredFields = [
            { id: 'box-number', label: '箱单流水号' },
            { id: 'shipment-number', label: '出库单号' },
            { id: 'customer-name', label: '客户名称' }
        ];

        requiredFields.forEach(field => {
            const input = document.getElementById(field.id);
            const formGroup = input.closest('.shipment-barcode__form-group');
            
            // 添加必填项标记
            formGroup.classList.add('required');
            
            // 初始检查
            if (!input.value.trim()) {
                formGroup.classList.add('empty');
            }
            
            // 输入时检查
            input.addEventListener('input', function() {
                const value = this.value.trim();
                if (value) {
                    formGroup.classList.remove('empty');
                    this.classList.add('input-has-value');
                } else {
                    formGroup.classList.add('empty');
                    this.classList.remove('input-has-value');
                }
            });
            
            // 失去焦点时检查
            input.addEventListener('blur', function() {
                const value = this.value.trim();
                if (!value) {
                    formGroup.classList.add('empty');
                    this.classList.remove('input-has-value');
                }
            });
        });
    }

    // 添加一个工具函数用于处理输入值
    function sanitizeInput(value) {
        return value ? value.trim() : '';
    }

    // 修改导出Excel功能
    function exportToExcel() {
        const boxNumber = document.getElementById('box-number').value.trim();
        const shipmentNumber = document.getElementById('shipment-number').value.trim();
        const customerName = document.getElementById('customer-name').value.trim();
        const workOrder = document.getElementById('work-order').value.trim();
        
        if (!boxNumber || !shipmentNumber || !customerName) {
            Swal.fire('提示', '请填写完整的箱单号、出库单号和客户名称信息', 'warning');
            return;
        }
        
        if (snList.length === 0) {
            Swal.fire('提示', '暂无数据可导出', 'warning');
            return;
        }

        // 显示加载提示
        Swal.fire({
            title: '正在导出...',
            text: '请稍候',
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // 发送导出请求
        fetch('/api/shipment-barcode/export-excel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                boxNumber,
                shipmentNumber,
                customerName,
                workOrder
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('导出失败');
            }
            return response.blob();
        })
        .then(blob => {
            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `出货条码_${boxNumber}_${shipmentNumber}_${new Date().toISOString().split('T')[0]}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // 显示导出成功提示，并设置较长的显示时间
            Swal.fire({
                title: '导出成功',
                text: '文件已开始下载',
                icon: 'success',
                timer: 2000,  // 显示2秒
                showConfirmButton: false
            }).then(() => {
                // 提示关闭后重新聚焦到输入框
                if (autoMode) {
                    document.getElementById('auto-sn-input').focus();
                } else {
                    document.getElementById('sn-input').focus();
                }
            });
        })
        .catch(error => {
            Logger.error('Export error:', error);
            Swal.fire({
                title: '导出失败',
                text: '请稍后重试',
                icon: 'error',
                confirmButtonText: '确定'
            });
        });
    }

    // 添加文件导入处理函数
    function handleFileImport(event) {
        const file = event.target.files[0];
        if (!file) return;

        const boxNumber = document.getElementById('box-number').value.trim();
        const shipmentNumber = document.getElementById('shipment-number').value.trim();
        const customerName = document.getElementById('customer-name').value.trim();

        if (!boxNumber || !shipmentNumber || !customerName) {
            Swal.fire('提示', '请先填写箱单号、出库单号和客户名称信息', 'warning');
            event.target.value = ''; // 清空文件输入
            return;
        }

        // 显示加载提示
        Swal.fire({
            title: '正在导入...',
            text: '请稍候',
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        const formData = new FormData();
        formData.append('file', file);
        formData.append('boxNumber', boxNumber);
        formData.append('shipmentNumber', shipmentNumber);
        formData.append('customerName', customerName);

        // 发送导入请求
        fetch('/api/shipment-barcode/import-excel', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 导入成功后重新加载SN列表
                loadSNList();
                
                Swal.fire({
                    title: '导入成功',
                    text: `成功导入 ${data.importCount} 条记录`,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                throw new Error(data.message || '导入失败');
            }
        })
        .catch(error => {
            Logger.error('Import error:', error);
            Swal.fire({
                title: '导入失败',
                text: error.message || '请检查Excel文件格式是否正确',
                icon: 'error',
                confirmButtonText: '确定'
            });
        })
        .finally(() => {
            event.target.value = ''; // 清空文件输入
        });
    }

    // ****** 修改：可重用的验证函数，返回 Promise 以等待弹窗 ******
    /**
     * 调用后端 API 验证 SN 是否属于指定客户。
     * @param {string} snNumber 要验证的 SN 号。
     * @param {string} shipmentCustomerName 当前出库的客户名称。
     * @returns {Promise<boolean>} Promise 解析为 true 表示验证通过或无需提示，false 表示验证失败且用户已看到提示。
     */
    async function validateSnAgainstCustomer(snNumber, shipmentCustomerName) {
        Logger.log(`Calling validation API for SN: ${snNumber}, Customer: ${shipmentCustomerName}`);
        try {
            const response = await fetch('/api/work-order/validate-sn-customer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sn_number: snNumber,
                    shipment_customer_name: shipmentCustomerName
                })
            });

            if (!response.ok) {
                let errorMsg = `服务器错误 (${response.status})`;
                try {
                    const errData = await response.json();
                    errorMsg = errData.message || errorMsg;
                } catch (parseError) {
                     errorMsg = `服务器错误: ${response.statusText || response.status}`;
                 }
                // API 调用失败，弹窗并返回 false (阻止)
                await Swal.fire({ // await the popup
                    icon: 'error',
                    title: '验证接口调用失败',
                    text: errorMsg,
                    confirmButtonText: '确定'
                });
                return false; // API 错误，验证失败
            }

            const result = await response.json();
            Logger.log('Validation API Response:', result);

            if (result.success) {
                if (!result.valid) {
                    // 验证失败，弹出警告提示，并等待用户点击，然后返回 false
                    await Swal.fire({ // await the popup
                        icon: 'warning',
                        title: '请注意',
                        text: result.message,
                        confirmButtonText: '知道了'
                    });
                    // 用户点击 "知道了" 后，我们仍然认为验证失败，但不阻止添加
                    // 根据新需求，这里应该返回 true，因为仅提示不阻止
                    // return false; // 旧逻辑：阻止
                    return true; // 新逻辑：仅提示，不阻止
                } else {
                    // 验证通过
                    Logger.log(`SN ${snNumber} customer validation passed.`);
                    return true; // 验证通过
                }
            } else {
                // API 调用成功但后端报告逻辑错误，弹窗并返回 false (阻止)
                 await Swal.fire({ // await the popup
                    icon: 'error',
                    title: '验证异常',
                    text: result.message || 'SN客户验证时发生未知错误。',
                    confirmButtonText: '确定'
                });
                 return false; // API 报告错误，验证失败
            }
        } catch (error) {
            Logger.error('Error during SN validation fetch:', error);
            // 网络错误等，弹窗并返回 false (阻止)
            await Swal.fire({ // await the popup
                icon: 'error',
                title: '验证出错',
                text: `无法连接到服务器或发生错误: ${error.message}`,
                confirmButtonText: '确定'
            });
             return false; // 捕获到异常，验证失败
        }
        // 如果代码能执行到这里（理论上不太可能），默认算验证通过
        // return true;
    }
    // ****** 结束修改验证函数 ******

    /**
     * 检查SN号的版本状态并自动处理客户定制开关
     * @param {string} snNumber 要检查的SN号
     */
    async function checkSnInfo(snNumber) {
        try {
            // 先检查版本状态
            await checkVersionStatus(snNumber);
            
            // 再智能处理客户定制开关
            await handleCustomerCheckToggle(snNumber);
            
        } catch (error) {
            // 网络错误等，仅记录日志，不阻止用户操作
            Logger.error(`SN信息检查异常:`, error);
        }
    }
    
    /**
     * 检查SN号的版本状态和批次状态
     * @param {string} snNumber 要检查的SN号
     */
    async function checkVersionStatus(snNumber) {
        try {
            const workOrderInput = document.getElementById('work-order');
            const workOrder = workOrderInput ? workOrderInput.value.trim() : '无';
            
            const response = await fetch('/api/shipment-barcode/check-sn-info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sn: snNumber,
                    workOrder: workOrder
                })
            });

            if (!response.ok) {
                Logger.warn(`SN版本状态检查API调用失败: ${response.status}`);
                return;
            }

            const result = await response.json();
            
            // 处理版本状态提示
            if (result.success && result.version_status && result.version_status.message) {
                await Swal.fire({
                    icon: 'warning',
                    title: 'SN信息提示',
                    text: result.version_status.message,
                    confirmButtonText: '知道了',
                    timer: 5000,
                    timerProgressBar: true
                });
            }
            
            // 处理批次状态提示
            if (result.success && result.batch_status && result.batch_status.message) {
                await Swal.fire({
                    icon: 'warning',
                    title: 'SN信息提示',
                    text: result.batch_status.message,
                    confirmButtonText: '知道了',
                    timer: 5000,
                    timerProgressBar: true
                });
            }
            
        } catch (error) {
            Logger.error(`版本状态检查异常:`, error);
        }
    }
    
    /**
     * 智能处理客户定制开关（利用现有的验证API避免重复查询）
     * @param {string} snNumber 要检查的SN号
     */
    async function handleCustomerCheckToggle(snNumber) {
        try {
            const customerCheckToggle = document.getElementById('check-sn-customer-toggle');
            const customerInput = document.getElementById('customer-name');
            
            // 如果开关已经开启，无需检查
            if (customerCheckToggle && customerCheckToggle.checked) {
                return;
            }
            
            // 使用一个虚假的客户名称来调用验证API，获取SN对应的实际客户信息
            const response = await fetch('/api/work-order/validate-sn-customer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sn_number: snNumber,
                    shipment_customer_name: '_CHECK_CUSTOMER_'  // 特殊标记，用于获取客户信息
                })
            });

            if (response.ok) {
                const result = await response.json();
                
                // 如果验证失败且返回了客户信息，说明该SN有客户定制
                if (result.success && !result.valid && result.message) {
                    // 从错误消息中提取客户名称
                    const match = result.message.match(/工单客户\s*\[([^\]]+)\]/);
                    if (match && match[1] && customerCheckToggle) {
                        // 自动开启开关
                        customerCheckToggle.checked = true;
                        
                        // 显示提示
                        await Swal.fire({
                            icon: 'info',
                            title: 'SN信息提示',
                            text: `检测到客户定制产品，客户：${match[1]}，已自动开启客户定制检查功能`,
                            confirmButtonText: '知道了',
                            timer: 5000,
                            timerProgressBar: true
                        });
                        
                        Logger.log(`SN ${snNumber} 自动开启客户定制检查功能，客户：${match[1]}`);
                    }
                }
            }
            
        } catch (error) {
            Logger.error(`客户定制开关处理异常:`, error);
        }
    }

    // 将需要的函数挂载到window对象
    window.initShipmentBarcodePage = initShipmentBarcodePage;
    window.deleteSN = deleteSN;
    // 添加分页相关函数到全局作用域
    window.goToPage = goToPage;
    window.goToFirstPage = goToFirstPage;
    window.goToPrevPage = goToPrevPage;
    window.goToNextPage = goToNextPage;
    window.goToLastPage = goToLastPage;
    window.changePageSize = changePageSize;
    window.exportToExcel = exportToExcel;

    // 立即尝试执行初始化，以防脚本在 DOM 加载后运行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initShipmentBarcodePage);
    } else {
        // DOMContentLoaded 已经触发
        initShipmentBarcodePage();
    }
})(); 