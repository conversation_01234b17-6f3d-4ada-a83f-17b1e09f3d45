/**
 * SweetAlert 工具类
 * 封装了常用的提示框功能
 */
class SweetAlert {
    // 默认配置
    static defaultConfig = {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickOutside: false
    };

    /**
     * 成功提示
     * @param {string} message 提示信息
     * @param {string} title 标题
     * @param {object} options 自定义配置
     */
    static async success(message, title = '成功', options = {}) {
        return Swal.fire({
            icon: 'success',
            title: title,
            text: message,
            ...this.defaultConfig,
            ...options
        });
    }

    /**
     * 错误提示
     * @param {string} message 提示信息
     * @param {string} title 标题
     * @param {object} options 自定义配置
     */
    static async error(message, title = '错误', options = {}) {
        return Swal.fire({
            icon: 'error',
            title: title,
            text: message,
            ...this.defaultConfig,
            ...options
        });
    }

    /**
     * 警告提示
     * @param {string} message 提示信息
     * @param {string} title 标题
     * @param {object} options 自定义配置
     */
    static async warning(message, title = '警告', options = {}) {
        return Swal.fire({
            icon: 'warning',
            title: title,
            text: message,
            ...this.defaultConfig,
            ...options
        });
    }

    /**
     * 信息提示
     * @param {string} message 提示信息
     * @param {string} title 标题
     * @param {object} options 自定义配置
     */
    static async info(message, title = '提示', options = {}) {
        return Swal.fire({
            icon: 'info',
            title: title,
            text: message,
            ...this.defaultConfig,
            ...options
        });
    }

    /**
     * 确认对话框
     * @param {string} message 提示信息
     * @param {string} title 标题
     * @param {object} options 自定义配置
     */
    static async confirm(message, title = '确认', options = {}) {
        const result = await Swal.fire({
            icon: 'question',
            title: title,
            text: message,
            showCancelButton: true,
            ...this.defaultConfig,
            ...options
        });
        return result.isConfirmed;
    }

    /**
     * 自定义提示框
     * @param {object} options 自定义配置
     */
    static async custom(options = {}) {
        return Swal.fire({
            ...this.defaultConfig,
            ...options
        });
    }

    /**
     * 关闭所有提示框
     */
    static close() {
        Swal.close();
    }
}

// 导出工具类
window.SweetAlert = SweetAlert; 