/**
 * 配置管理器 - CPU控制器测试系统
 * 统一管理产品配置和M区映射配置，提供一致的访问接口
 * 
 * 功能特性：
 * - 集成M区映射配置和产品类型配置
 * - 提供统一的配置访问接口
 * - 支持配置验证和错误处理
 * - 自动同步配置状态
 */

class ConfigManager {
    constructor() {
        this.initialized = false;
        this.configs = new Map();
        this.currentProductType = 'all_config';
        this.logger = window.Logger;
        
        // 初始化配置
        this.initialize();
    }

    /**
     * 初始化配置管理器
     */
    initialize() {
        try {
            // 检查依赖配置是否已加载
            if (typeof window.M_AREA_CONFIGS === 'undefined') {
                throw new Error('M区配置(MAreaConfigs.js)未加载');
            }
            if (typeof window.CPU_PRODUCT_TYPE_CONFIGS === 'undefined') {
                throw new Error('产品类型配置(ProductTypeConfigs.js)未加载');
            }

            // 构建统一配置结构
            this.buildUnifiedConfigs();
            
            this.initialized = true;
            this.logger.info('[ConfigManager] 配置管理器初始化完成');
            this.logger.info(`[ConfigManager] 已加载${this.configs.size}个产品类型配置`);
            
        } catch (error) {
            this.logger.error('[ConfigManager] 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 构建统一的配置结构
     * 将M区配置与产品配置合并
     */
    buildUnifiedConfigs() {
        const productConfigs = window.CPU_PRODUCT_TYPE_CONFIGS || {};
        const mAreaConfigs = window.M_AREA_CONFIGS || {};

        // 合并所有产品类型的键
        const allConfigKeys = new Set([
            ...Object.keys(productConfigs),
            ...Object.keys(mAreaConfigs)
        ]);

        allConfigKeys.forEach(configKey => {
            const productConfig = productConfigs[configKey];
            const mAreaConfig = mAreaConfigs[configKey];

            // 构建统一配置对象
            const unifiedConfig = {
                // 基本信息 (优先使用productConfig的名称和描述)
                key: configKey,
                name: productConfig?.name || mAreaConfig?.name || '未知配置',
                description: productConfig?.description || mAreaConfig?.description || '无描述',
                
                // M区配置 (如果存在)
                mAreaMapping: mAreaConfig?.mAreaMapping || [],
                mAreaRange: mAreaConfig?.mAreaRange || { start: 0, end: 0 },
                controlledTestCount: mAreaConfig?.controlledTestCount || 0,
                
                // 产品配置 (如果存在)
                enabledTestCount: productConfig?.enabledTestCount || 0,
                enabledTests: productConfig?.enabledTests || [],
                testMapping: productConfig?.testMapping || [],
                // 新增：产品型号
                productModels: productConfig?.productModels || null,
                
                // 配置状态
                isValid: this.validateConfig(mAreaConfig, productConfig),
                hasProductConfig: !!productConfig,
                hasMAreaConfig: !!mAreaConfig
            };

            this.configs.set(configKey, unifiedConfig);
        });
    }

    /**
     * 验证配置的完整性
     * @param {Object} mAreaConfig - M区配置
     * @param {Object} productConfig - 产品配置
     * @returns {boolean} 是否有效
     */
    validateConfig(mAreaConfig, productConfig) {
        // 只要有一个配置存在即可认为基本有效
        if (!mAreaConfig && !productConfig) {
            return false;
        }

        // M区配置验证 (如果存在)
        const mAreaValid = !mAreaConfig || (
                          Array.isArray(mAreaConfig.mAreaMapping) &&
                          typeof mAreaConfig.mAreaRange === 'object');

        // 产品配置验证 (如果存在)
        const productValid = !productConfig || (
            Array.isArray(productConfig.enabledTests) && 
            Array.isArray(productConfig.testMapping)
        );

        return mAreaValid && productValid;
    }

    /**
     * 获取指定产品类型的配置
     * @param {string} productType - 产品类型键名
     * @returns {Object|null} 配置对象
     */
    getConfig(productType) {
        if (!this.initialized) {
            this.logger.warn('[ConfigManager] 配置管理器未初始化');
            return null;
        }

        const config = this.configs.get(productType);
        if (!config) {
            this.logger.warn(`[ConfigManager] 未找到产品类型配置: ${productType}`);
            return null;
        }

        if (!config.isValid) {
            this.logger.error(`[ConfigManager] 产品类型配置无效: ${productType}`);
            return null;
        }

        return config;
    }

    /**
     * 获取当前产品类型的M区映射配置
     * @param {string} productType - 产品类型（可选，默认使用当前类型）
     * @returns {Array} M区映射数组
     */
    getMAreaMapping(productType = this.currentProductType) {
        const config = this.getConfig(productType);
        return config ? config.mAreaMapping : [];
    }

    /**
     * 获取当前产品类型的M区范围
     * @param {string} productType - 产品类型（可选）
     * @returns {Object} M区范围对象 {start, end}
     */
    getMAreaRange(productType = this.currentProductType) {
        const config = this.getConfig(productType);
        return config ? config.mAreaRange : { start: 0, end: 0 };
    }

    /**
     * 检查指定测试项目是否由M区控制（支持多M区组合）
     * @param {number} testIndex - 测试项目索引
     * @param {string} productType - 产品类型（可选）
     * @param {boolean} visualContext - 是否为视觉检测上下文（默认false）
     * @returns {Object|null} M区控制信息（标准化格式）或 null
     */
    getMAreaControlInfo(testIndex, productType = this.currentProductType, visualContext = false) {
        const mapping = this.getMAreaMapping(productType);
        const controlInfo = mapping.find(item => item.testIndex === testIndex);
        
        if (!controlInfo) return null;
        
        // 如果是自动测试上下文，跳过visualOnly项目
        if (!visualContext && controlInfo.visualOnly) {
            return null;
        }
        
        // 如果是视觉检测上下文，只处理visualOnly项目
        if (visualContext && !controlInfo.visualOnly) {
            return null;
        }
        
        // 标准化返回格式，统一单M区和多M区组合的接口
        if (controlInfo.mIndex !== undefined) {
            // 单M区模式
            return {
                testIndex: controlInfo.testIndex,
                testName: controlInfo.testName,
                testCode: controlInfo.testCode,
                mode: 'single',
                mIndex: controlInfo.mIndex,
                mIndices: [controlInfo.mIndex], // 为了统一处理，转换为数组
                combineMode: 'SINGLE',
                visualOnly: controlInfo.visualOnly || false
            };
        } else if (controlInfo.mIndices !== undefined) {
            // 多M区组合模式
            return {
                testIndex: controlInfo.testIndex,
                testName: controlInfo.testName,
                testCode: controlInfo.testCode,
                mode: 'combined',
                mIndices: controlInfo.mIndices,
                combineMode: controlInfo.combineMode,
                description: controlInfo.description,
                visualOnly: controlInfo.visualOnly || false
            };
        }
        
        return null;
    }
    
    /**
     * 专门用于自动测试的M区控制信息获取（排除visualOnly项目）
     * @param {number} testIndex - 测试项目索引
     * @param {string} productType - 产品类型（可选）
     * @returns {Object|null} M区控制信息或null
     */
    getAutoTestMAreaControlInfo(testIndex, productType = this.currentProductType) {
        return this.getMAreaControlInfo(testIndex, productType, false);
    }
    
    /**
     * 专门用于视觉检测的M区控制信息获取（只返回visualOnly项目）
     * @param {number} testIndex - 测试项目索引
     * @param {string} productType - 产品类型（可选）
     * @returns {Object|null} M区控制信息或null
     */
    getVisualTestMAreaControlInfo(testIndex, productType = this.currentProductType) {
        return this.getMAreaControlInfo(testIndex, productType, true);
    }

    /**
     * 根据M区索引获取测试项目信息
     * @param {number} mIndex - M区索引
     * @param {string} productType - 产品类型（可选）
     * @returns {Object|null} 测试项目信息
     */
    getTestInfoByMIndex(mIndex, productType = this.currentProductType) {
        const mapping = this.getMAreaMapping(productType);
        const testInfo = mapping.find(item => 
            item.mIndex === mIndex || 
            (Array.isArray(item.mIndices) && item.mIndices.includes(mIndex))
        );
        return testInfo || null;
    }

    /**
     * 验证指定测试项目的M区组合判断
     * @param {number} testIndex - 测试项目索引
     * @param {Array} mAreaValues - M区数据数组
     * @param {string} productType - 产品类型（可选）
     * @param {boolean} visualContext - 是否为视觉检测上下文（新增）
     * @returns {Object} 验证结果 {success, result, reason, details}
     */
    validateCombinedMAreaTest(testIndex, mAreaValues, productType = this.currentProductType, visualContext = false) {
        const controlInfo = this.getMAreaControlInfo(testIndex, productType, visualContext);
        
        if (!controlInfo) {
            return {
                success: false,
                result: null,
                reason: '测试项目未配置M区控制',
                details: null
            };
        }

        // 检查M区数据数组的有效性
        if (!Array.isArray(mAreaValues)) {
            return {
                success: false,
                result: 'fail',
                reason: 'M区数据格式无效',
                details: null
            };
        }

        // 获取相关M区值并验证
        const results = [];
        const missingIndices = [];
        
        for (const mIndex of controlInfo.mIndices) {
            if (mIndex >= mAreaValues.length) {
                missingIndices.push(mIndex);
                continue;
            }
            
            const mValue = mAreaValues[mIndex];
            const passed = (mValue === 1);
            
            results.push({
                mIndex: mIndex,
                value: mValue,
                expected: 1,
                passed: passed,
                description: `M${mIndex}=${mValue} (${passed ? '通过' : '失败'})`
            });
        }
        
        // 检查数据完整性
        if (missingIndices.length > 0) {
            return {
                success: false,
                result: 'fail',
                reason: `M区数据不足，缺少M${missingIndices.join('、M')}`,
                details: results
            };
        }

        // 根据组合模式判断最终结果
        let finalResult;
        let reason;
        
        switch (controlInfo.combineMode) {
            case 'AND':
                const allPass = results.every(r => r.passed);
                finalResult = allPass ? 'pass' : 'fail';
                reason = allPass ? 
                    `所有M区值都为1` : 
                    `需要M${controlInfo.mIndices.join('、M')}都为1，实际: ${results.map(r => `M${r.mIndex}=${r.value}`).join('、')}`;
                break;
                
            case 'SINGLE':
                const singleResult = results[0];
                finalResult = singleResult.passed ? 'pass' : 'fail';
                reason = singleResult.description;
                break;
                
            default:
                finalResult = 'fail';
                reason = `未知的组合模式: ${controlInfo.combineMode}`;
        }

        return {
            success: true,
            result: finalResult,
            reason: reason,
            details: results,
            controlInfo: controlInfo
        };
    }

    /**
     * 获取所有可用的产品类型列表
     * @returns {Array} 产品类型列表
     */
    getAvailableProductTypes() {
        if (!this.initialized) {
            return [];
        }

        return Array.from(this.configs.values()).map(config => ({
            key: config.key,
            name: config.name,
            description: config.description,
            controlledTestCount: config.controlledTestCount,
            enabledTestCount: config.enabledTestCount,
            isValid: config.isValid,
            hasProductConfig: config.hasProductConfig
        }));
    }

    /**
     * 设置当前产品类型
     * @param {string} productType - 产品类型键名
     * @returns {boolean} 是否设置成功
     */
    setCurrentProductType(productType) {
        const config = this.getConfig(productType);
        if (!config) {
            this.logger.error(`[ConfigManager] 无法设置无效的产品类型: ${productType}`);
            return false;
        }

        const oldType = this.currentProductType;
        this.currentProductType = productType;
        
        this.logger.info(`[ConfigManager] 产品类型已切换: ${oldType} -> ${productType}`);
        this.logger.info(`[ConfigManager] M区控制测试项目数量: ${config.controlledTestCount}`);
        
        return true;
    }

    /**
     * 获取当前产品类型
     * @returns {string} 当前产品类型键名
     */
    getCurrentProductType() {
        return this.currentProductType;
    }

    /**
     * 验证M区数据值
     * @param {Array} mAreaValues - M区数据数组
     * @param {string} productType - 产品类型（可选）
     * @returns {Object} 验证结果 {valid, errors, mapping}
     */
    validateMAreaData(mAreaValues, productType = this.currentProductType) {
        const config = this.getConfig(productType);
        if (!config) {
            return { valid: false, errors: ['配置无效'], mapping: [] };
        }

        const errors = [];
        const mapping = [];
        const mAreaMapping = config.mAreaMapping;

        // 检查M区数据长度
        const maxMIndex = Math.max(...mAreaMapping.map(item => item.mIndex));
        if (!Array.isArray(mAreaValues) || mAreaValues.length <= maxMIndex) {
            errors.push(`M区数据长度不足，需要至少${maxMIndex + 1}个值，实际${mAreaValues.length}个`);
        }

        // 验证每个M区映射
        mAreaMapping.forEach(item => {
            const mValue = mAreaValues[item.mIndex];
            const result = (mValue === 1) ? 'pass' : 'fail';
            
            mapping.push({
                testIndex: item.testIndex,
                mIndex: item.mIndex,
                testName: item.testName,
                mValue: mValue,
                expectedValue: 1,
                result: result,
                valid: !isNaN(mValue)
            });

            if (isNaN(mValue)) {
                errors.push(`M${item.mIndex}值无效: ${mValue}`);
            }
        });

        return {
            valid: errors.length === 0,
            errors: errors,
            mapping: mapping,
            config: config
        };
    }

    /**
     * 生成M区测试统计信息（支持多M区组合）
     * @param {Array} mAreaValues - M区数据数组
     * @param {string} productType - 产品类型（可选）
     * @returns {Object} 统计信息
     */
    generateMAreaStatistics(mAreaValues, productType = this.currentProductType) {
        if (!Array.isArray(mAreaValues)) {
            return {
                valid: false,
                errors: ['M区数据格式无效'],
                summary: '数据格式错误'
            };
        }

        const config = this.getConfig(productType);
        if (!config || !config.mAreaMapping) {
            return {
                valid: false,
                errors: ['配置无效或无M区映射'],
                summary: '配置错误'
            };
        }

        const results = [];
        const errors = [];
        let passCount = 0;
        let failCount = 0;

        // 逐个测试项目进行M区验证
        config.mAreaMapping.forEach(mapping => {
            const validation = this.validateCombinedMAreaTest(mapping.testIndex, mAreaValues, productType);
            
            if (!validation.success) {
                errors.push(`${mapping.testName}: ${validation.reason}`);
                failCount++;
                results.push({
                    test: mapping.testName,
                    mRef: `配置错误`,
                    result: 'fail',
                    details: validation.reason
                });
            } else {
                if (validation.result === 'pass') {
                    passCount++;
                } else {
                    failCount++;
                }

                // 生成M区引用描述
                let mRef;
                if (validation.controlInfo.mode === 'combined') {
                    // 多M区组合模式
                    mRef = validation.details.map(d => `M${d.mIndex}=${d.value}`).join('+');
                } else {
                    // 单M区模式
                    const detail = validation.details[0];
                    mRef = `M${detail.mIndex}=${detail.value}`;
                }

                results.push({
                    test: mapping.testName,
                    mRef: mRef,
                    result: validation.result,
                    details: validation.reason,
                    mode: validation.controlInfo.mode
                });
            }
        });

        const totalCount = config.mAreaMapping.length;

        return {
            valid: errors.length === 0,
            errors: errors,
            totalTests: totalCount,
            passedTests: passCount,
            failedTests: failCount,
            passRate: totalCount > 0 ? Math.round((passCount / totalCount) * 100) : 0,
            details: results,
            summary: `M区通信测试(${config.name}): 通过${passCount}/失败${failCount} (${totalCount}项)`
        };
    }

    /**
     * 获取配置管理器状态信息
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            initialized: this.initialized,
            configCount: this.configs.size,
            currentProductType: this.currentProductType,
            availableTypes: Array.from(this.configs.keys()),
            currentConfig: this.getConfig(this.currentProductType)
        };
    }
}

// 创建全局配置管理器实例
let configManagerInstance = null;

/**
 * 获取配置管理器实例（单例模式）
 * @returns {ConfigManager} 配置管理器实例
 */
const getConfigManager = () => {
    if (!configManagerInstance) {
        configManagerInstance = new ConfigManager();
    }
    return configManagerInstance;
};

// 导出配置管理器
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        ConfigManager,
        getConfigManager
    };
} else {
    // 浏览器环境
    window.ConfigManager = ConfigManager;
    window.getConfigManager = getConfigManager;
}

// 自动初始化（仅在浏览器环境）
if (typeof window !== 'undefined') {
    // 等待DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            // 延迟初始化，确保其他脚本已加载
            setTimeout(() => {
                try {
                    getConfigManager();
                } catch (error) {
                    Logger.warn('[ConfigManager] 自动初始化失败，请手动调用getConfigManager():', error.message);
                }
            }, 100);
        });
    } else {
        // DOM已加载，立即尝试初始化
        setTimeout(() => {
            try {
                getConfigManager();
            } catch (error) {
                Logger.warn('[ConfigManager] 自动初始化失败，请手动调用getConfigManager():', error.message);
            }
        }, 100);
    }
}
