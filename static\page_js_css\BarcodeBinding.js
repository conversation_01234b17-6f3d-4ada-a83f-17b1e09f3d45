// 在文件开头添加语音提示函数
function playVoicePrompt(type, errorMessage = '') {
    let message = '';
    
    switch (type) {
        case 'board':
            message = '单板绑定完成';
            break;
        case 'shell':
            message = '绑定完成';
            break;
        case 'error':
            // 只有在明确是SN格式错误时才播放特定提示
            if (errorMessage && (
                errorMessage.includes('SN 长度不正确') ||
                errorMessage.includes('未获取到工单 SN 长度信息')
            )) {
                message = '绑定失败、SN错误';
            } else {
                message = '绑定失败';
            }
            break;
        default:
            return;
    }
    
    // 使用系统语音合成
    const speech = new SpeechSynthesisUtterance(message);
    speech.lang = 'zh-CN';
    speechSynthesis.speak(speech);
}

// 在文件开头添加显示消息的函数
function showAutoCloseMessage(message, duration = 3000) {
    const modal = document.createElement('div');
    modal.className = 'auto-close-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="success-icon">✓</div>
            <div class="message-text">${message}</div>
        </div>
    `;
    
    // 更新样式
    const style = document.createElement('style');
    style.textContent = `
        .auto-close-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 25px 40px;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            z-index: 1000;
            animation: fadeInOut 3s ease-in-out;
        }
        
        .modal-content {
            text-align: center;
            min-width: 200px;
        }
        
        .success-icon {
            color: #4CAF50;
            font-size: 48px;
            margin-bottom: 15px;
            animation: scaleIn 0.3s ease-out;
        }
        
        .message-text {
            color: #333;
            font-size: 16px;
            margin: 0;
            font-weight: 500;
        }
        
        .progress-info {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; transform: translate(-50%, -40%); }
            10% { opacity: 1; transform: translate(-50%, -50%); }
            80% { opacity: 1; transform: translate(-50%, -50%); }
            100% { opacity: 0; transform: translate(-50%, -40%); }
        }
        
        @keyframes scaleIn {
            0% { transform: scale(0); }
            70% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(modal);
    
    setTimeout(() => {
        modal.remove();
    }, duration);
    
    return modal;
}

// 在文件开头添加防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 添加全局变量存储当前工单的 SN 长度
if (typeof window.barcodeBindingState === 'undefined') {
    window.barcodeBindingState = {
        currentSnLength: null
    };
}

function initBarcodeBindingPage() {
    const content = document.getElementById('barcode-binding-content');
    if (!content) {
        Logger.error('无法找到 barcode-binding-content 元素');
        return;
    }

    content.innerHTML = `
        <div class="barcode-binding-page">
            <form id="testForm" novalidate>
                <div class="basic-info-card">
                    <h2>基本信息</h2>
                    <div class="form-grid">
                        <div class="form-group" data-field="tester">
                            <label for="tester">测试人员</label>
                            <input type="text" id="tester" readonly>
                        </div>
                        <div class="form-group" data-field="orderNumber">
                            <label for="orderNumber">加工单号</label>
                            <input type="text" id="orderNumber" placeholder="输入加工单号" required>
                        </div>
                        <div class="form-group" data-field="productionQuantity">
                            <label for="productionQuantity">生产数量</label>
                            <input type="number" id="productionQuantity" placeholder="输入生产数量" min="0" required>
                        </div>
                        <div class="form-group" data-field="productCode">
                            <label for="productCode">产品编码</label>
                            <input type="text" id="productCode" placeholder="输入产品编码" required>
                        </div>
                        <div class="form-group" data-field="productModel">
                            <label for="productModel">产品型号</label>
                            <input type="text" id="productModel" placeholder="输入产品型号" required>
                        </div>
                        <div class="form-group" data-field="memo">
                            <label for="memo">备注</label>
                            <input type="text" id="memo" placeholder="输入备注信息">
                        </div>
                    </div>
                </div>
            </form>

            <!-- 扫码绑定系统 -->
            <div class="scan-container">
                <!-- 添加主标签页导航 -->
                <div class="main-tabs-nav">
                    <button class="main-tab-button active" onclick="switchMainTab('new-binding')">新板子绑定</button>
                    <button class="main-tab-button" onclick="switchMainTab('rebind')">更换绑定</button>
                </div>

                <!-- 新板子绑定标签页 -->
                <div id="new-binding-tab" class="main-tab-content active">
                    <div class="section">
                        <div class="tabs-nav">
                            <button class="tab-button active" onclick="switchTab('controller')">控制器</button>
                            <button class="tab-button" onclick="switchTab('io-module')">IO模块</button>
                        </div>
                        
                        <!-- 控制器标签页 -->
                        <div id="controller-tab" class="tab-content active">
                            <div>
                                <label for="type">选择类型：</label>
                                <select id="type" onchange="changeType()">
                                    <option value="1">类型1 (4个板子)</option>
                                    <option value="2">类型2 (3个板子)</option>
                                    <option value="3">类型3 (2个板子)</option>
                                </select>
                                <div class="auto-bind-toggle">
                                    <label class="switch">
                                        <input type="checkbox" id="controllerAutoBindToggle">
                                        <span class="slider round"></span>
                                    </label>
                                    <span class="toggle-label">开启自动绑定控制器</span>
                                </div>
                            </div>

                            <div class="scan-item horizontal-layout">
                                <div class="form-group half-width">
                                    <label for="controller-pcba-sn-length">PCBA序列号长度位数</label>
                                    <input type="number" id="controller-pcba-sn-length" placeholder="输入PCBA序列号长度">
                                </div>
                                <div class="form-group half-width">
                                    <label for="controller-char-counter">字符计算</label>
                                    <input type="text" id="controller-char-counter" placeholder="输入内容自动计算长度" oninput="updateCharCount('controller')">
                                </div>
                            </div>

                            <!-- 类型1的输入框组 -->
                            <div id="type1" class="scan-group">
                                <div class="scan-item">
                                    <input type="text" id="code1_1" placeholder="扫描板子A">
                                </div>
                                <div class="scan-item">
                                    <input type="text" id="code1_2" placeholder="扫描板子B">
                                </div>
                                <div class="scan-item">
                                    <input type="text" id="code1_3" placeholder="扫描板子C">
                                </div>
                                <div class="scan-item">
                                    <input type="text" id="code1_4" placeholder="扫描板子D">
                                </div>
                                <button id="bind1" class="scan-button" onclick="bindCodes(1)" disabled>控制器单板绑定</button>
                            </div>

                            <!-- 类型2的输入框组 -->
                            <div id="type2" class="scan-group">
                                <div class="scan-item">
                                    <input type="text" id="code2_1" placeholder="扫描板子A">
                                </div>
                                <div class="scan-item">
                                    <input type="text" id="code2_2" placeholder="扫描板子B">
                                </div>
                                <div class="scan-item">
                                    <input type="text" id="code2_3" placeholder="扫描板子C">
                                </div>
                                <button id="bind2" class="scan-button" onclick="bindCodes(2)" disabled>控制器单板绑定</button>
                            </div>

                            <!-- 类型3的输入框组 -->
                            <div id="type3" class="scan-group">
                                <div class="scan-item">
                                    <input type="text" id="code3_1" placeholder="扫描板子A">
                                </div>
                                <div class="scan-item">
                                    <input type="text" id="code3_2" placeholder="扫描板子B">
                                </div>
                                <button id="bind3" class="scan-button" onclick="bindCodes(3)" disabled>控制器单板绑定</button>
                            </div>

                            <!-- 添加查询区域到控制器单板绑定按钮下方 -->
                            <div class="query-section">
                                <div class="form-group">
                                    <label for="querySN">输入任意单板序列号</label>
                                    <div class="auto-bind-toggle">
                                        <label class="switch">
                                            <input type="checkbox" id="autoBindToggle">
                                            <span class="slider round"></span>
                                        </label>
                                        <span class="toggle-label">开启自动绑定外壳</span>
                                    </div>
                                    <div class="query-input-group">
                                        <input type="text" id="querySN" placeholder="优先用单板A序列号">
                                        <button class="scan-button" onclick="queryBoardInfo()">查询</button>
                                    </div>
                                </div>
                                <!-- 查询结果显示区域 -->
                                <div id="queryResult" class="query-result" style="display:none;">
                                    <h4>查询结果：</h4>
                                    <div class="result-content">
                                        <div class="result-item">
                                            <span class="label">组合ID：</span>
                                            <span id="assemblyId"></span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">测试人员：</span>
                                            <span id="testerName"></span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">产品型号：</span>
                                            <span id="board_product_model"></span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">板子A：</span>
                                            <span id="boardA"></span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">板子B：</span>
                                            <span id="boardB"></span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">板子C：</span>
                                            <span id="boardC"></span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">板子D：</span>
                                            <span id="boardD"></span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">外壳N：</span>
                                            <span id="shellSN"></span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">绑定时间：</span>
                                            <span id="assembledTime"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- IO模块标签页 -->
                        <div id="io-module-tab" class="tab-content">
                            <div>
                                <label for="io-type">选择类型：</label>
                                <select id="io-type" onchange="changeIOType()">
                                    <option value="A">类型A</option>
                                    <option value="B">类型B</option>
                                </select>
                                <div class="auto-bind-toggle">
                                    <label class="switch">
                                        <input type="checkbox" id="ioAutoBindToggle">
                                        <span class="slider round"></span>
                                    </label>
                                    <span class="toggle-label">开启自动绑定IO模块</span>
                                </div>
                            </div>
                            <div class="scan-item horizontal-layout">
                                <div class="form-group half-width">
                                    <label for="io-pcba-sn-length">PCBA序列号长度位数</label>
                                    <input type="number" id="io-pcba-sn-length" placeholder="输入PCBA序列号长度">
                                </div>
                                <div class="form-group half-width">
                                    <label for="io-char-counter">字符计算</label>
                                    <input type="text" id="io-char-counter" placeholder="输入内容自动计算长度" oninput="updateCharCount('io')">
                                </div>
                            </div>

                            <!-- 类型A的输入框组 -->
                            <div id="typeA" class="scan-group">
                                <div class="scan-item">
                                    <label for="codeA_1">扫描板子A</label>
                                    <input type="text" id="codeA_1" placeholder="扫描板子A">
                                </div>
                                <div class="scan-item">
                                    <label for="codeA_2">扫描外壳SN</label>
                                    <input type="text" id="codeA_2" placeholder="扫描SN">
                                </div>
                                <button id="bindA" class="scan-button" onclick="bindIOCodes('A')" disabled>IO外壳绑定</button>
                            </div>

                            <!-- 类型B的输入框组 -->
                            <div id="typeB" class="scan-group">
                                <div class="scan-item">
                                    <label for="codeB_1">扫描板子A</label>
                                    <input type="text" id="codeB_1" placeholder="扫描板子A">
                                </div>
                                <div class="scan-item">
                                    <label for="codeB_2">扫描板子B</label>
                                    <input type="text" id="codeB_2" placeholder="扫描板子B">
                                </div>
                                <div class="scan-item">
                                    <label for="codeB_3">扫描外壳SN</label>
                                    <input type="text" id="codeB_3" placeholder="扫描SN">
                                </div>
                                <button id="bindB" class="scan-button" onclick="bindIOCodes('B')" disabled>IO外壳绑定</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 更换绑定标签页 -->
                <div id="rebind-tab" class="main-tab-content">
                    <div class="section">
                        <h3>更换绑定</h3>
                        <div class="search-group">
                            <input type="text" id="rebindSN" placeholder="输入任意序列号或SN号">
                            <button class="scan-button search" onclick="searchForRebind()">查询</button>
                        </div>
                        <div id="rebindInfo" style="display:none;">
                            <div class="status">当前状态: <span id="bindStatus" class="pending">等待扫描新板子</span></div>
                            <div class="bound-info">
                                <div class="select-all">
                                    <label>
                                        <input type="checkbox" id="selectAllBoards" onclick="toggleSelectAll()">
                                        全选
                                    </label>
                                </div>
                                <div id="currentBoards"></div>
                            </div>
                            <div class="action-buttons">
                                <button class="scan-button" onclick="prepareRebind()">更所选板子</button>
                                <button class="scan-button danger" onclick="unbindAll()">一键解绑</button>
                            </div>
                            <div class="board-group" id="newBoardGroup" style="display:none;">
                                <h4>扫描新板子</h4>
                                <div id="newBoardInputs"></div>
                                <div class="action-buttons">
                                    <button class="scan-button" onclick="confirmRebind()">确认更换绑定</button>
                                    <button class="scan-button danger" onclick="cancelRebind()">取消绑定</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 确保在页面内容加载完成后立即获取用户信息
    getCurrentUser();
    initFormValidation();
    initScanBindingSystem();

    // 初始化查询输入框的自动查询功能
    initQueryAutoSearch();

    // 找到产品编码输入框并添加事件监听
    const productCodeInput = document.getElementById('productCode');
    if (productCodeInput) {
        productCodeInput.addEventListener('input', async function() {
            const code = this.value.trim();
            if (code) {
                try {
                    const response = await fetch(`/api/product-mapping/get-model?code=${encodeURIComponent(code)}`);
                    const data = await response.json();
                    
                    if (data.success && data.model) {
                        // 自动填充产品型号
                        const modelInput = document.getElementById('productModel');
                        if (modelInput) {
                            modelInput.value = data.model;
                            modelInput.classList.add('input-has-value');
                        }
                        // 产品编码有效，加绿色背景
                        this.classList.add('input-has-value');
                    } else {
                        // 产品编码无效，移除绿色背景
                        this.classList.remove('input-has-value');
                        // 清空产品型号
                        const modelInput = document.getElementById('productModel');
                        if (modelInput) {
                            modelInput.value = '';
                            modelInput.classList.remove('input-has-value');
                        }
                    }
                } catch (error) {
                    Logger.error('获取产品型号失败:', error);
                }
            } else {
                // 输入框为空时移除绿色背景
                this.classList.remove('input-has-value');
                // 清空产品型号
                const modelInput = document.getElementById('productModel');
                if (modelInput) {
                    modelInput.value = '';
                    modelInput.classList.remove('input-has-value');
                }
            }
        });

        // 添加扫码输入支持
        productCodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault(); // 防止表单提交
                // 触发input事件以执行自动填充
                this.dispatchEvent(new Event('input'));
                // 可以在这里添加扫码后的其他处理逻辑
            }
        });
    }

    // 将查询逻辑抽取为独立函数
    async function queryOrderInfo(orderNumber) {
        // 重置 SN 长度
        window.barcodeBindingState.currentSnLength = null;
        
        // 清空相关字段
        const fieldsToReset = ['productionQuantity', 'productCode', 'productModel'];
        fieldsToReset.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.value = '';
                element.classList.remove('input-has-value');
            }
        });

        if (!orderNumber) return;

        try {
            const response = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNumber)}`);
            const data = await response.json();

            if (data.success && data.order) {
                // 填充工单信息
                const orderNumberInput = document.getElementById('orderNumber');
                orderNumberInput.classList.add('input-has-value');

                const productionQuantityInput = document.getElementById('productionQuantity');
                if (productionQuantityInput) {
                    productionQuantityInput.value = data.order.ord_productionQuantity;
                    productionQuantityInput.classList.add('input-has-value');
                }

                const productCodeInput = document.getElementById('productCode');
                if (productCodeInput) {
                    productCodeInput.value = data.order.ord_productCode;
                    productCodeInput.classList.add('input-has-value');
                }

                const productModelInput = document.getElementById('productModel');
                if (productModelInput) {
                    productModelInput.value = data.order.ord_productModel;
                    productModelInput.classList.add('input-has-value');
                }

                // 设置 SN 长度
                window.barcodeBindingState.currentSnLength = data.order.ord_snlenth;
            } else {
                document.getElementById('orderNumber').classList.remove('input-has-value');
                await Swal.fire({
                    title: '错误',
                    text: data.message || '获取工单信息失败',
                    icon: 'error',
                    confirmButtonText: '确定'
                });
            }
        } catch (error) {
            Logger.error('Error fetching order info:', error);
            document.getElementById('orderNumber').classList.remove('input-has-value');
            await Swal.fire({
                title: '错误',
                text: '获取工单信息时发生错误',
                icon: 'error',
                confirmButtonText: '确定'
            });
        }
    }

    // 修改工单号输入框的事件监听
    const orderNumberInput = document.getElementById('orderNumber');
    if (orderNumberInput) {
        // 使用防抖包装查询函数，设置500ms延迟
        const debouncedQuery = debounce(queryOrderInfo, 500);
        
        orderNumberInput.addEventListener('input', function() {
            const orderNumber = this.value.trim();
            debouncedQuery(orderNumber);
        });

        // 保留扫码输入的回车处理
        orderNumberInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // 触发防抖查询
                debouncedQuery(this.value.trim());
            }
        });
    }

    // 添加消息提示函数（如果还没有的话）
    function showMessage(message, type = 'info', persistent = false, duration = 3000) {
        // 确保消息容器存在
        let messageContainer = document.querySelector('.message-container');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.className = 'message-container';
            document.body.appendChild(messageContainer);
        }

        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;

        // 如果是持续显示的消息，添加关闭按钮
        if (persistent) {
            const closeButton = document.createElement('button');
            closeButton.className = 'close-message';
            closeButton.innerHTML = '×';
            closeButton.onclick = () => messageDiv.remove();
            messageDiv.appendChild(closeButton);
        } else {
            // 自动消失
            setTimeout(() => messageDiv.remove(), duration);
        }

        // 添加到消息容器
        messageContainer.appendChild(messageDiv);

        // 移除之前的相同类型消息
        const existingMessages = messageContainer.querySelectorAll(`.message.${type}`);
        existingMessages.forEach(msg => {
            if (msg !== messageDiv) {
                msg.remove();
            }
        });
    }
}

// 修改查询输入框的自动查询功能
function initQueryAutoSearch() {
    const queryInput = document.getElementById('querySN');
    const autoBindToggle = document.getElementById('autoBindToggle');
    let searchTimer = null;

    if (queryInput) {
        queryInput.addEventListener('input', async () => {
            // 清除之前的定时器
            if (searchTimer) {
                clearTimeout(searchTimer);
            }

            // 当输入长度达到15个字符时自动触发查询
            if (queryInput.value.length >= 15) {
                searchTimer = setTimeout(async () => {
                    const searchButton = queryInput.nextElementSibling;
                    if (searchButton && !searchButton._searching) {
                        try {
                            searchButton._searching = true;
                            queryInput.disabled = true;
                            searchButton.disabled = true;
                            await queryBoardInfo();
                        } catch (error) {
                            Logger.error('自动查询失败:', error);
                        } finally {
                            setTimeout(() => {
                                searchButton._searching = false;
                                queryInput.disabled = false;
                                searchButton.disabled = false;
                            }, 2000);
                        }
                    }
                }, 1000);
            }
        });
    }
}

// 修改获取当前用户的函数
async function getCurrentUser() {
    try {
        const response = await fetch('/api/user/info');
        const result = await response.json();
        if (result.success) {
            const testerInput = document.getElementById('tester');
            testerInput.value = result.username;
            // 添加 readonly 属性
            testerInput.setAttribute('readonly', true);
            // 可以添加一些样式来表明这是只读字段
            testerInput.style.backgroundColor = '#f5f5f5';
            testerInput.style.cursor = 'not-allowed';
        } else {
            Logger.error('获取用户信息失败:', result.message);
        }
    } catch (error) {
        Logger.error('获取用户信息失败:', error);
    }
}

// 保留原有的表单验证函数
function initFormValidation() {
    const form = document.getElementById('testForm');
    form.querySelectorAll('input').forEach(input => {
        input.addEventListener('blur', () => {
            validateInput(input);
        });
        input.addEventListener('input', () => {
            if (input.classList.contains('is-invalid')) {
                validateInput(input);
            }
        });
    });
}

function validateInput(input) {
    if (!input.checkValidity()) {
        input.classList.add('is-invalid');
    } else {
        input.classList.remove('is-invalid');
    }
}

// 添加扫码绑定系统的功能
function initScanBindingSystem() {
    // 模拟数据库
    window.mockDatabase = {
        'SN001': {
            type: 1,
            codes: ['CODE1', 'CODE2', 'CODE3', 'CODE4', 'SN001']
        },
        'SN002': {
            type: 2,
            codes: ['CODE5', 'CODE6', 'CODE7', 'SN002']
        }
    };

    changeType();

    // 只为扫码输入框添加输入完成检测
    const scanInputs = document.querySelectorAll('.scan-group input[type="text"]');

    scanInputs.forEach(input => {
        input.addEventListener('input', function() {
            const container = this.closest('.scan-item');
            if (this.value.length >= 15) {
                container.classList.add('completed');
            } else {
                container.classList.remove('completed');
            }
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                const container = this.closest('.scan-item');
                container.classList.remove('completed');
            }
        });
    });
}

function changeType() {
    const type = document.getElementById('type').value;
    const autoBindToggle = document.getElementById('controllerAutoBindToggle');
    document.querySelectorAll('.scan-group').forEach(group => {
        group.style.display = 'none';
    });
    
    // 显示选中类型的输入组
    const currentGroup = document.getElementById('type' + type);
    currentGroup.style.display = 'block';
    
    // 为当前组的所有输入框添加事件监听
    const inputs = currentGroup.querySelectorAll('input');
    
    // 创建一个Set用于存储当前输入的序列号
    const serialNumbers = new Set();
    
    inputs.forEach((input, index) => {
        input.removeEventListener('input', input._inputHandler);
        
        let autoClickTimer = null;
        let validationTimer = null;
        
        input._inputHandler = () => {
            const currentValue = input.value.trim();
            
            // 当输入长度达到15个字符时进行延迟验证
            if (currentValue.length >= 15) {
                // 清除之前的验证定时器
                if (validationTimer) {
                    clearTimeout(validationTimer);
                }
                
                // 设置延迟验证，500ms后执行
                validationTimer = setTimeout(() => {
                    // CPU检查是否为"扫描板子A"输入框
                    const isBoardAInput = (input.id === `code${type}_1`);
                    if (isBoardAInput) {
                        const lengthInput = document.getElementById('controller-pcba-sn-length');
                        const expectedLength = lengthInput.value ? parseInt(lengthInput.value) : null;

                        // 如果设置了预期长度，则进行验证
                        if (expectedLength && currentValue.length !== expectedLength) {
                            SweetAlert.error('序列号错误');
                            input.value = ''; // 清空错误输入
                            input.focus();
                            return; // 阻止后续操作
                        }
                    }

                    // 检查是否与其他输入框的值重复
                    let isDuplicate = false;
                    inputs.forEach((otherInput) => {
                        if (otherInput !== input && otherInput.value.trim() === currentValue) {
                            isDuplicate = true;
                        }
                    });

                    if (isDuplicate) {
                        // 如果重复，清空输入框并显示提示
                        input.value = '';
                        SweetAlert.warning('序列号重复，请重新扫描');
                        input.focus();
                        return;
                    }

                    // 不管是否开启自动绑定，都自动跳转到下一个输入框
                    if (index < inputs.length - 1) {
                        setTimeout(() => {
                            const nextInput = inputs[index + 1];
                            if (nextInput) {
                                nextInput.focus();
                                nextInput.select();
                            }
                        }, 1200);
                    }
                    
                    // 只有在开启自动绑定时才自动触发绑定
                    if (index === inputs.length - 1 && autoBindToggle.checked) {
                        if (autoClickTimer) {
                            clearTimeout(autoClickTimer);
                        }
                        autoClickTimer = setTimeout(() => {
                            const bindButton = document.getElementById(`bind${type}`);
                            if (bindButton && !bindButton.disabled && !bindButton._clicking) {
                                bindButton._clicking = true;
                                bindButton.click();
                                inputs.forEach(input => input.disabled = true);
                                setTimeout(() => {
                                    bindButton._clicking = false;
                                    inputs.forEach(input => input.disabled = false);
                                }, 2000);
                            }
                        }, 1000);
                    }
                }, 500); // 500ms延迟验证
            }
            
            checkComplete(type);
        };
        
        input.addEventListener('input', input._inputHandler);
    });
    
    clearInputs();
}

function clearInputs() {
    document.querySelectorAll('.scan-group input[type="text"]').forEach(input => {
        input.value = '';
        const container = input.closest('.scan-item');
        container.classList.remove('completed');
    });
    
    document.querySelectorAll('button[id^="bind"]').forEach(button => {
        button.disabled = true;
        button.style.backgroundColor = '#cccccc';
        button.style.cursor = 'not-allowed';
    });
    
    // 清除所有completed类
    document.querySelectorAll('.completed').forEach(element => {
        element.classList.remove('completed');
    });
}

function checkComplete(type) {
    const inputs = document.querySelectorAll(`#type${type} input`);
    const button = document.getElementById(`bind${type}`);
    let isComplete = true;

    inputs.forEach(input => {
        if (!input.value.trim()) {
            isComplete = false;
        }
    });

    button.disabled = !isComplete;
    
    // 更新按钮样式
    if (isComplete) {
        button.style.backgroundColor = '#4CAF50'; // 绿色
        button.style.cursor = 'pointer';
    } else {
        button.style.backgroundColor = '#cccccc'; // 灰色
        button.style.cursor = 'not-allowed';
    }
}

// 添加获取绑定进度的函数
async function getBindingProgress(orderNumber) {
    if (!orderNumber) return null;
    
    try {
        const response = await fetch(`/api/barcode-binding/binding_progress?orderNumber=${encodeURIComponent(orderNumber)}`);
        const result = await response.json();
        
        if (result.success) {
            return result.data;
        } else {
            Logger.error('获取绑定进度失败:', result.message);
            return null;
        }
    } catch (error) {
        Logger.error('获取绑定进度请求失败:', error);
        return null;
    }
}

// 添加一个工具函数来生成进度消息
function generateProgressMessage(type, progress) {
    if (!progress) return '';
    
    let count, message;
    const {boardCount, shellCount, totalQuantity} = progress;
    
    switch (type) {
        case 'board':
            count = boardCount;
            message = '已绑定单板';
            break;
        case 'shell':
            count = shellCount;
            message = '已绑定外壳';
            break;
        case 'io':
            count = shellCount;
            message = '已绑定IO模块';
            break;
        default:
            return '';
    }
    
    // 检查是否完成
    if (count >= totalQuantity) {
        // 播报完成提示
        const speech = new SpeechSynthesisUtterance(`工单绑定完成，共计${count}个`);
        speech.lang = 'zh-CN';
        // 延迟播放完成提示，避免与绑定成功提示冲突
        setTimeout(() => {
            speechSynthesis.speak(speech);
        }, 1000);
        
        return `
            <div class="progress-info">
                <div>${message}: ${count} / 总量: ${totalQuantity}</div>
                <div style="color: #4CAF50; margin-top: 5px;">工单绑定完成，共计${count}个</div>
            </div>
        `;
    }
    
    return `
        <div class="progress-info">
            ${message}: ${count} / 总量: ${totalQuantity}
        </div>
    `;
}

// 修改控制器绑定函数
async function bindCodes(type) {
    // 获取基本信息
    const tester = document.getElementById('tester').value.trim();
    const orderNumber = document.getElementById('orderNumber').value.trim();
    const productQuantity = document.getElementById('productionQuantity').value.trim();
    const productCode = document.getElementById('productCode').value.trim();
    const productModel = document.getElementById('productModel').value.trim();
    
    // 逐个验证基本信息
    if (!tester) {
        SweetAlert.error('请填写测试人员');
        document.getElementById('tester').focus();
        return;
    }
    if (!orderNumber) {
        SweetAlert.error('请填写加工单号');
        document.getElementById('orderNumber').focus();
        return;
    }
    if (!productQuantity) {
        SweetAlert.error('请填写生产数量');
        document.getElementById('productionQuantity').focus();
        return;
    }
    if (!productCode) {
        SweetAlert.error('请填写产品编码');
        document.getElementById('productCode').focus();
        return;
    }
    if (!productModel) {
        SweetAlert.error('请填写产品型号');
        document.getElementById('productModel').focus();
        return;
    }
    
    // 获取当前类型的所有输入框值
    const inputs = document.querySelectorAll(`#type${type} input`);
    const codes = Array.from(inputs).map(input => input.value.trim());
    
    // 准备请求数据
    const data = {
        tester: tester,
        productModel: productModel,
        orderNumber: orderNumber,
        productQuantity: parseInt(productQuantity),
        productCode: productCode,
        type: type,
        code1: codes[0],
        code2: codes[1],
        code3: codes[2],
        code4: codes[3]
    };
    
    Logger.log('提交的数据:', data);
    
    try {
        // 先获取当前绑定进度
        const progress = await getBindingProgress(orderNumber);
        
        const response = await fetch('/api/barcode-binding/bind_controller', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            playVoicePrompt('board');
            
            // 获取最新的绑定进度
            const updatedProgress = await getBindingProgress(orderNumber);
            
            // 使用新的工具函数生成进度消息
            const progressMessage = generateProgressMessage('board', updatedProgress);
            
            showAutoCloseMessage(`
                <div>${result.message}</div>
                ${progressMessage}
            `);
            
            clearInputs();
            
            // 不管是否开启了自动绑定，都自动聚焦到第一个输入框
            setTimeout(() => {
                const targetInputId = `code${type}_1`;
                const targetInput = document.getElementById(targetInputId);
                if (targetInput) {
                    targetInput.focus(); 
                    targetInput.select();
                }
            }, 2100);
        } else {
            playVoicePrompt('error', result.message);
            SweetAlert.error(result.message);
        }
    } catch (error) {
        playVoicePrompt('error', error.message);
        Logger.error('绑定请求失败:', error);
        SweetAlert.error('绑定失败，请稍后重试');
    }
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAllBoards');
    const checkboxes = document.querySelectorAll('.board-select');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 修改搜索绑定信息的函数
async function searchForRebind() {
    const sn = document.getElementById('rebindSN').value.trim();
    if (!sn) {
        SweetAlert.error('请输入序列号');
        return;
    }

    try {
        const response = await fetch(`/api/barcode-binding/search?sn=${encodeURIComponent(sn)}`);
        const result = await response.json();
        
        if (!result.success) {
            SweetAlert.error(result.message || '未找到对应的绑定信息');
            return;
        }

        const data = result.data;
        const currentBoards = document.getElementById('currentBoards');
        document.getElementById('newBoardGroup').style.display = 'none';
        document.getElementById('newBoardInputs').innerHTML = '';
        
        // 更新状态信息
        const statusContainer = document.querySelector('.status');
        const hasShell = data.product_sn || data.has_shell;
        
        // 构建状态显示HTML
        let statusHtml = `
            <div>当前态: <span id="bindStatus" class="${hasShell ? 'success' : 'pending'}">
                ${hasShell ? '已绑定外壳' : '未绑定外壳'}
            </span></div>
            <div>加工单号：${data.board_processing_order || '无'}</div>
            <div>产品型号：${data.board_product_model || '无'}</div>
            <div>单板组装时间：${data.created_at || '无'}</div>
            <div>外壳组装时间：${data.assembled_at || '无'}</div>
        `;
        
        statusContainer.innerHTML = statusHtml;
        
        // 构建板子信息显示
        const boardsInfo = [];
        // 根据板子类型确定显示的板子数量和顺序
        if (data.board_type === 1) { // 4块板
            if (data.board_a_sn) boardsInfo.push({ label: '板子A', sn: data.board_a_sn, position: '1' });
            if (data.board_b_sn) boardsInfo.push({ label: '板子B', sn: data.board_b_sn, position: '2' });
            if (data.board_c_sn) boardsInfo.push({ label: '板子C', sn: data.board_c_sn, position: '3' });
            if (data.board_d_sn) boardsInfo.push({ label: '板子D', sn: data.board_d_sn, position: '4' });
        } else if (data.board_type === 2) { // 3块板
            if (data.board_a_sn) boardsInfo.push({ label: '板子A', sn: data.board_a_sn, position: '1' });
            if (data.board_b_sn) boardsInfo.push({ label: '板子B', sn: data.board_b_sn, position: '2' });
            if (data.board_c_sn) boardsInfo.push({ label: '板子C', sn: data.board_c_sn, position: '3' });
        } else if (data.board_type === 3) { // 2块板
            if (data.board_a_sn) boardsInfo.push({ label: '板子A', sn: data.board_a_sn, position: '1' });
            if (data.board_b_sn) boardsInfo.push({ label: '板子B', sn: data.board_b_sn, position: '2' });
        }
        
        // 添加外壳SN（如果存在）
        if (data.product_sn) {
            boardsInfo.push({ label: '外壳SN', sn: data.product_sn, position: 'shell' });
        }

        // 显示板子信息
        let html = '<h4>当前绑定信息：</h4>';
        html += `<div class="bound-boards">
            ${boardsInfo.map(board => `
                <div>
                    <label>
                        <input type="checkbox" class="board-select" value="${board.position}" 
                               data-code="${board.sn}"> ${board.label}: ${board.sn}
                    </label>
                </div>
            `).join('')}
        </div>`;

        currentBoards.innerHTML = html;
        currentBoards.setAttribute('data-assembly-id', data.assembly_id);
        currentBoards.setAttribute('data-board-type', data.board_type);

        document.getElementById('rebindInfo').style.display = 'block';
    } catch (error) {
        Logger.error('搜索失败:', error);
        SweetAlert.error('搜索失败，请稍后重试');
    }
}

// 修改准备更换绑定的函数
function prepareRebind() {
    const selectedBoards = document.querySelectorAll('.board-select:checked');
    const newBoardInputs = document.getElementById('newBoardInputs');
    
    if (selectedBoards.length === 0) {
        SweetAlert.error('请选择需要更换的板子或外壳');
        return;
    }

    newBoardInputs.innerHTML = Array.from(selectedBoards).map((checkbox) => {
        const label = checkbox.closest('label').textContent.split(':')[0].trim();
        const isShell = checkbox.value === 'shell';
        return `
            <div class="scan-item">
                <input type="text" class="new-board-input" 
                       placeholder="扫描新${isShell ? '外壳' : label} (替换${label})" 
                       data-position="${checkbox.value}"
                       data-original="${checkbox.dataset.code}"
                       data-type="${isShell ? 'shell' : 'board'}">
            </div>
        `;
    }).join('');

    document.getElementById('newBoardGroup').style.display = 'block';
}

// 修改确认更换绑定函数
async function confirmRebind() {
    const newInputs = document.querySelectorAll('.new-board-input');
    const assemblyId = document.getElementById('currentBoards').getAttribute('data-assembly-id');
    
    let isComplete = true;
    let updatePromises = [];
    
    newInputs.forEach(input => {
        if (!input.value.trim()) {
            isComplete = false;
            return;
        }
        
        if (input.dataset.type === 'board') {
            // 对于板子类型的更新
            updatePromises.push(
                fetch('/api/barcode-binding/update_binding', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        assembly_id: assemblyId,
                        board_position: input.dataset.position,
                        new_sn: input.value.trim()
                    })
                }).then(response => response.json())
            );
        } else if (input.dataset.type === 'shell') {
            // 对于外壳类型的更新
            updatePromises.push(
                fetch('/api/barcode-binding/update_shell', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        assembly_id: assemblyId,
                        new_shell_sn: input.value.trim()
                    })
                }).then(response => response.json())
            );
        }
    });

    if (!isComplete) {
        await SweetAlert.error('请完成所有新板子和外壳的扫描');
        return;
    }

    try {
        const results = await Promise.all(updatePromises);
        const hasError = results.some(result => !result.success);
        
        if (hasError) {
            const errorMessages = results
                .filter(result => !result.success)
                .map(result => result.message)
                .join('\n');
            throw new Error(errorMessages);
        }

        SweetAlert.success('更绑定成功');

        // 刷新显示
        const sn = document.getElementById('rebindSN').value.trim();
        if (sn) {
            await searchForRebind();  // 重新查询更新显示
        }
        
        // 重置表单
        document.getElementById('newBoardGroup').style.display = 'none';
        document.getElementById('newBoardInputs').innerHTML = '';
        document.getElementById('selectAllBoards').checked = false;
        document.querySelectorAll('.board-select').forEach(checkbox => {
            checkbox.checked = false;
        });
        
    } catch (error) {
        Logger.error('更换绑定失败:', error);
        SweetAlert.error(error.message || '更换绑定失败，请稍后重试');
    }
}

// 修改除绑定的函
async function unbindAll() {
    const assemblyId = document.getElementById('currentBoards').getAttribute('data-assembly-id');
    if (!assemblyId) {
        SweetAlert.error('无效的绑定信息');
        return;
    }

    Swal.fire({
        title: '确定要解除所有绑定吗？',
        text: '此操作不可恢复。',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    }).then(async (result) => {
        if (result.isConfirmed) {
            try {
                const response = await fetch('/api/barcode-binding/unbind_all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        assembly_id: assemblyId
                    })
                });

                const result = await response.json();
                if (result.success) {
                    Swal.fire('成功', '已成功解除所有绑定', 'success');
                    resetRebindForm();
                } else {
                    Swal.fire('错误', result.message || '解绑失败', 'error');
                }
            } catch (error) {
                Logger.error('解绑失败:', error);
                Swal.fire('错误', '解绑失败，请稍后重试', 'error');
            }
        }
    });
}

function resetRebindForm() {
    document.getElementById('rebindSN').value = '';
    document.getElementById('rebindInfo').style.display = 'none';
    document.getElementById('newBoardGroup').style.display = 'none';
    document.getElementById('bindStatus').className = 'pending';
    document.getElementById('bindStatus').textContent = '等待扫描新板子';
    document.getElementById('selectAllBoards').checked = false;
}

// 添加取消绑定功能
function cancelRebind() {
    Swal.fire({
        title: '确认取消',
        text: '确定要取消本次绑定操作吗？',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById('newBoardGroup').style.display = 'none';
            document.getElementById('newBoardInputs').innerHTML = '';
            // 重置所有选中的复选框
            document.querySelectorAll('.board-select').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('selectAllBoards').checked = false;
        }
    });
}

// 添标签页切换函数
function switchTab(tabName) {
    // 更新签按钮状态
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    document.querySelector(`.tab-button[onclick="switchTab('${tabName}')"]`).classList.add('active');
    
    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    // 清空所有输入
    clearInputs();

    // 根据标签页类型初始化显示
    if (tabName === 'controller') {
        changeType();
    } else if (tabName === 'io-module') {
        changeIOType();
    }
}

// 修改 changeIOType 函数中的输入框处理逻辑
function changeIOType() {
    const type = document.getElementById('io-type').value;
    const autoBindToggle = document.getElementById('ioAutoBindToggle');
    document.querySelectorAll('#io-module-tab .scan-group').forEach(group => {
        group.style.display = 'none';
    });
    
    // 显示选中类型的输入组
    const currentGroup = document.getElementById('type' + type);
    currentGroup.style.display = 'block';
    
    // 为当前组的所有输入框添加事件监听
    const inputs = currentGroup.querySelectorAll('input');
    
    inputs.forEach((input, index) => {
        input.removeEventListener('input', input._inputHandler);
        
        let autoClickTimer = null;
        let validationTimer = null;
        
        input._inputHandler = () => {
            const currentValue = input.value.trim();
            
            // 当输入长度达到15个字符时进行延迟验证
            if (currentValue.length >= 15) {
                // 清除之前的验证定时器
                if (validationTimer) {
                    clearTimeout(validationTimer);
                }
                
                // 设置延迟验证，500ms后执行
                validationTimer = setTimeout(() => {
                    // IO模块检查是否为"扫描板子A"输入框
                    const isBoardAInput = (input.id === `code${type}_1`);
                    if (isBoardAInput) {
                        const lengthInput = document.getElementById('io-pcba-sn-length');
                        const expectedLength = lengthInput.value ? parseInt(lengthInput.value) : null;

                        // 如果设置了预期长度，则进行验证
                        if (expectedLength && currentValue.length !== expectedLength) {
                            SweetAlert.error('序列号错误');
                            input.value = ''; // 清空错误输入
                            input.focus();
                            return; // 阻止后续操作
                        }
                    }

                    // 检查是否与其他输入框的值重复
                    let isDuplicate = false;
                    inputs.forEach((otherInput) => {
                        if (otherInput !== input && otherInput.value.trim() === currentValue) {
                            isDuplicate = true;
                        }
                    });

                    if (isDuplicate) {
                        // 如果重复，清空输入框并显示提示
                        input.value = '';
                        SweetAlert.warning('序列号重复，请重新扫描');
                        input.focus();
                        return;
                    }

                    // 不管是否开启自动绑定，都自动跳转到下一个输入框
                    if (index < inputs.length - 1) {
                        setTimeout(() => {
                            const nextInput = inputs[index + 1];
                            if (nextInput) {
                                nextInput.focus();
                                nextInput.select();
                            }
                        }, 1200);
                    }
                    
                    // 只有在开启自动绑定时才自动触发绑定
                    if (index === inputs.length - 1 && autoBindToggle.checked) {
                        if (autoClickTimer) {
                            clearTimeout(autoClickTimer);
                        }
                        autoClickTimer = setTimeout(() => {
                            const bindButton = document.getElementById(`bind${type}`);
                            if (bindButton && !bindButton.disabled && !bindButton._clicking) {
                                bindButton._clicking = true;
                                bindButton.click();
                                inputs.forEach(input => input.disabled = true);
                                setTimeout(() => {
                                    bindButton._clicking = false;
                                    inputs.forEach(input => input.disabled = false);
                                }, 2000);
                            }
                        }, 1000);
                    }
                }, 500); // 500ms延迟验证
            }
            
            checkIOComplete(type);
        };
        
        input.addEventListener('input', input._inputHandler);
    });
    
    clearInputs();
}

function checkIOComplete(type) {
    const inputs = document.querySelectorAll(`#type${type} input`);
    const button = document.getElementById(`bind${type}`);
    let isComplete = true;

    inputs.forEach(input => {
        if (!input.value.trim()) {
            isComplete = false;
        }
    });

    button.disabled = !isComplete;
    
    if (isComplete) {
        button.style.backgroundColor = '#4CAF50';
        button.style.cursor = 'pointer';
    } else {
        button.style.backgroundColor = '#cccccc';
        button.style.cursor = 'not-allowed';
    }
}

// 修改IO模块绑定函数
async function bindIOCodes(type) {
    // 获取基本信息
    const tester = document.getElementById('tester').value.trim();
    const orderNumber = document.getElementById('orderNumber').value.trim();
    const productQuantity = document.getElementById('productionQuantity').value.trim();
    const productModel = document.getElementById('productModel').value.trim();
    const productCode = document.getElementById('productCode').value.trim();
    const memo = document.getElementById('memo').value.trim();

    // 逐个验证基本信息
    if (!tester) {
        SweetAlert.error('请填写测试人员');
        document.getElementById('tester').focus();
        return;
    }
    if (!orderNumber) {
        SweetAlert.error('请填写加工单号');
        document.getElementById('orderNumber').focus();
        return;
    }
    if (!productQuantity) {
        SweetAlert.error('请填写生产数量');
        document.getElementById('productionQuantity').focus();
        return;
    }
    if (!productCode) {
        SweetAlert.error('请填写产品编码');
        document.getElementById('productCode').focus();
        return;
    }
    if (!productModel) {
        SweetAlert.error('请填写产品型号');
        document.getElementById('productModel').focus();
        return;
    }

    // 获取当前类型的输入框值
    const inputs = document.querySelectorAll(`#type${type} input`);
    const codes = Array.from(inputs).map(input => input.value.trim());

    // 验证外壳 SN 长度和订货号
    const shellSn = type === 'A' ? codes[1] : codes[2];
    if (!await validateSnLength(shellSn)) {
        return;
    }

    // 准备请求数据
    const data = {
        tester: tester,
        order_number: orderNumber,
        product_quantity: parseInt(productQuantity),
        product_model: productModel,
        productCode: productCode,
        remark: memo || '',
        type: type,
        board_a: codes[0],  // 板子A
        board_b: type === 'B' ? codes[1] : null,  // 类型B才有板子B
        shell_sn: type === 'A' ? codes[1] : codes[2]  // IOSN（外壳号）
    };

    try {
        // 先获取当前绑定进度
        const progress = await getBindingProgress(orderNumber);
        
        const response = await fetch('/api/barcode-binding/bind_io', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            playVoicePrompt('shell');
            
            // 获取最新的绑定进度
            const updatedProgress = await getBindingProgress(orderNumber);
            
            // 使用新的工具函数生成进度消息
            const progressMessage = generateProgressMessage('io', updatedProgress);
            
            showAutoCloseMessage(`
                <div>IO模块绑定成功</div>
                ${progressMessage}
            `);
            
            clearInputs();
            
            // 不管是否开启了自动绑定，都自动聚焦到第一个输入框
            setTimeout(() => {
                const targetInputId = `code${type}_1`;
                const targetInput = document.getElementById(targetInputId);
                if (targetInput) {
                    targetInput.focus();
                    targetInput.select();
                }
            }, 2100);
        } else {
            playVoicePrompt('error', result.message);
            SweetAlert.error(result.message || '绑定失败');
        }
    } catch (error) {
        playVoicePrompt('error', error.message);
        Logger.error('绑定请求失败:', error);
        SweetAlert.error('绑定失败，请稍后重试');
    }
}

// 添加 SN 订货号验证函数
async function validateSnOrderNumber(sn, productCode) {
    try {
        const response = await fetch(`/api/products/validate-sn-order?sn=${encodeURIComponent(sn)}&productCode=${encodeURIComponent(productCode)}`);
        const result = await response.json();
        
        if (!result.success) {
            playVoicePrompt('error', result.message);
            SweetAlert.error(result.message);
            return false;
        }
        return true;
    } catch (error) {
        Logger.error('验证 SN 订货号失败:', error);
        SweetAlert.error('验证失败，请稍后重试');
        return false;
    }
}

// 修改 validateSnLength 函数，确保同时验证长度和订货号
async function validateSnLength(sn) {
    const expectedLength = window.barcodeBindingState.currentSnLength;
    if (!expectedLength) {
        playVoicePrompt('error', '未获取到工单 SN 长度信息');
        SweetAlert.error('未获取到工单 SN 长度信息');
        return false;
    }
    
    // 验证长度
    if (sn.length !== expectedLength) {
        const errorMessage = `SN 长度不正确，应为 ${expectedLength} 位`;
        playVoicePrompt('error', errorMessage);
        SweetAlert.error(errorMessage);
        return false;
    }

    // 获取产品编码
    const productCode = document.getElementById('productCode').value.trim();
    if (!productCode) {
        SweetAlert.error('未获取到产品编码');
        return false;
    }

    // 验证订货号
    return await validateSnOrderNumber(sn, productCode);
}

// 为外壳 SN 输入框添加实时验证
function addSnLengthValidation(inputElement) {
    inputElement.addEventListener('input', function() {
        const sn = this.value.trim();
        const expectedLength = window.barcodeBindingState.currentSnLength;
        
        if (expectedLength && sn.length > expectedLength) {
            this.value = sn.slice(0, expectedLength);
        }
        
        // 可选：添加视觉反馈
        if (expectedLength && sn.length === expectedLength) {
            this.classList.add('valid-length');
        } else {
            this.classList.remove('valid-length');
        }
    });
}

// 在页面初始化时添加验证
document.addEventListener('DOMContentLoaded', function() {
    // 为所有外壳 SN 输入框添加验证
    const shellSnInputs = [
        document.getElementById('shellSN'),                    // 控制器外壳 SN
        document.getElementById('codeA_2'),                   // IO 模块类型 A 外壳 SN
        document.querySelector('#typeB input:last-child')     // IO 模块类型 B 外壳 SN
    ];
    
    shellSnInputs.forEach(input => {
        if (input) {
            addSnLengthValidation(input);
        }
    });
});

// 修改查询函数
async function queryBoardInfo() {
    const querySN = document.getElementById('querySN');
    const autoBindToggle = document.getElementById('autoBindToggle');
    
    if (!querySN.value.trim()) {
        SweetAlert.error('请输入单板序列号');
        return;
    }

    try {
        const response = await fetch(`/api/barcode-binding/search?sn=${encodeURIComponent(querySN.value.trim())}`);
        const result = await response.json();

        const queryResult = document.getElementById('queryResult');
        
        if (result.success && result.data) {
            const data = result.data;
            
            // 清空查询输入框的内容
            querySN.value = '';
            
            // 清空之前的结果内容
            const resultContent = document.querySelector('.result-content');
            resultContent.innerHTML = '';
            
            // 添加状态显示
            const statusDiv = document.createElement('div');
            statusDiv.className = 'result-item status-item';
            statusDiv.innerHTML = `
                <span class="label">当前状态：</span>
                <span class="${data.has_shell ? 'success' : 'pending'}">
                    ${data.has_shell ? '已绑定外壳' : '未绑定外壳'}
                </span>
            `;
            resultContent.appendChild(statusDiv);
            
            // 修改基本字段的顺序
            const basicFields = [
                { key: 'assembly_id', label: '组合ID' },
                { key: 'board_processing_order', label: '加工单号' },
                { key: 'board_product_model', label: '产品型号' },
                { key: 'board_product_quantity', label: '生产数量' },
                { key: 'board_tester_name', label: '单板组装' },
                { key: 'board_a_sn', label: '板子A' },
                { key: 'board_b_sn', label: '板子B' },
                { key: 'board_c_sn', label: '板子C' },
                { key: 'board_d_sn', label: '板子D' },
                // 如果已绑定外壳，在这里添加外壳SN
                ...(data.has_shell ? [{ key: 'product_sn', label: '外壳SN' }] : []),
                { key: 'created_at', label: '单板绑定时间' },
                // 如果已绑定外壳，添加外壳绑定时间
                ...(data.has_shell ? [{ key: 'assembled_at', label: '外壳绑定时间' }] : [])
            ];
            
            basicFields.forEach(field => {
                if (data[field.key]) {  // 只显示有值的字段
                    const div = document.createElement('div');
                    div.className = 'result-item';
                    div.innerHTML = `
                        <span class="label">${field.label}：</span>
                        <span>${data[field.key]}</span>
                    `;
                    resultContent.appendChild(div);
                }
            });
            
            // 只有在未绑定外壳时才显示外壳绑定区域
            if (!data.has_shell) {
                const shellBindingDiv = document.createElement('div');
                shellBindingDiv.className = 'shell-binding-section';
                shellBindingDiv.innerHTML = `
                    <div class="form-group">
                        <label for="shellSN">外壳SN：</label>
                        <div class="binding-input-group">
                            <input type="text" id="shellSN" placeholder="输入外壳SN">
                            <button class="scan-button" onclick="bindShell('${data.assembly_id}')">控制器外壳绑定</button>
                        </div>
                    </div>
                `;
                resultContent.appendChild(shellBindingDiv);
                
                // 添加外壳SN输入框的自动绑定功能
                const shellSNInput = document.getElementById('shellSN');
                let bindTimer = null;

                shellSNInput.addEventListener('input', () => {
                    if (bindTimer) {
                        clearTimeout(bindTimer);
                    }

                    // 当输入长度达到15个字符时
                    if (shellSNInput.value.length >= 15) {
                        // 只在开启自动绑定时自动触发绑定
                        if (autoBindToggle.checked) {
                            bindTimer = setTimeout(() => {
                                bindShell(data.assembly_id);
                            }, 1000);
                        }
                    }
                });

                // 不管是否开启自动绑定，都自动聚焦到外壳SN输入框
                setTimeout(() => {
                    shellSNInput.focus();
                    shellSNInput.select();
                }, 100);
            }
            
            queryResult.style.display = 'block';
            return result;
        } else {
            SweetAlert.error(result.message || '查询失败');
            queryResult.style.display = 'none';
            throw new Error(result.message || '查询失败');
        }
    } catch (error) {
        Logger.error('查询失败:', error);
        SweetAlert.error('查询失败，请稍后重试');
        throw error;
    }
}

// 修改外壳绑定功能
async function bindShell(assemblyId) {
    const shellSN = document.getElementById('shellSN');
    const shellSnValue = shellSN.value.trim();
    
    if (!shellSnValue) {
        SweetAlert.error('请输入外壳SN');
        shellSN.focus();
        return;
    }

    // 验证 SN 长度和订货号 - 使用 await 等待验证结果
    if (!await validateSnLength(shellSnValue)) {
        shellSN.focus();
        return;
    }

    // 如果正在绑定中，直接返回
    if (shellSN.nextElementSibling._binding) {
        return;
    }

    // 获取基本信息
    const tester = document.getElementById('tester').value.trim();
    const orderNumber = document.getElementById('orderNumber').value.trim();
    const productQuantity = document.getElementById('productionQuantity').value.trim();
    const productModel = document.getElementById('productModel').value.trim();
    const productCode = document.getElementById('productCode').value.trim();
    const memo = document.getElementById('memo').value.trim();
    
    // 逐个验证基本信息
    if (!tester) {
        SweetAlert.error('请填写测试人员');
        document.getElementById('tester').focus();
        return;
    }
    if (!orderNumber) {
        SweetAlert.error('请填写加工单号');
        document.getElementById('orderNumber').focus();
        return;
    }
    if (!productQuantity) {
        SweetAlert.error('请填写生产数量');
        document.getElementById('productionQuantity').focus();
        return;
    }
    if (!productCode) {
        SweetAlert.error('请填写产品编码');
        document.getElementById('productCode').focus();
        return;
    }
    if (!productModel) {
        SweetAlert.error('请填写产品型号');
        document.getElementById('productModel').focus();
        return;
    }

    // 检查工单的组装前阶段是否完成
    try {
        const checkResponse = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNumber)}`);
        const checkData = await checkResponse.json();
        
        if (!checkData.success || !checkData.order) {
            SweetAlert.error('无法获取工单信息');
            return;
        }
        
        if (!checkData.order.assembly_stage_completed) {
            await Swal.fire({
                title: '无法绑定外壳',
                text: '该工单未完成组装前阶段外观检验，不允许绑定外壳',
                icon: 'warning',
                confirmButtonText: '确定'
            });
            return;
        }
    } catch (error) {
        Logger.error('检查工单状态失败:', error);
        SweetAlert.error('检查工单状态失败，请稍后重试');
        return;
    }

    try {
        // 先获取当前绑定进度
        const progress = await getBindingProgress(orderNumber);
        
        const response = await fetch('/api/barcode-binding/bind_shell', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                assembly_id: assemblyId,
                shell_sn: shellSnValue,
                tester: tester,
                order_number: orderNumber,
                product_quantity: parseInt(productQuantity),
                product_model: productModel,
                productCode: productCode,
                remark: memo || ''
            })
        });

        const result = await response.json();

        if (result.success) {
            playVoicePrompt('shell');
            
            // 获取最新的绑定进度
            const updatedProgress = await getBindingProgress(orderNumber);
            
            // 使用新的工具函数生成进度消息
            const progressMessage = generateProgressMessage('shell', updatedProgress);
            
            showAutoCloseMessage(`
                <div>外壳绑定成功</div>
                ${progressMessage}
            `);
            
            shellSN.value = '';
            const queryResult = document.getElementById('queryResult');
            queryResult.style.display = 'none';
            
            // 不管是否开启了自动绑定，都自动聚焦到查询输入框
            setTimeout(() => {
                const queryInput = document.getElementById('querySN');
                queryInput.focus();
                queryInput.select();
            }, 2100);
        } else {
            playVoicePrompt('error', result.message);
            SweetAlert.error(result.message || '绑定失败');
        }
    } catch (error) {
        playVoicePrompt('error', error.message);
        Logger.error('外壳绑定失败:', error);
        SweetAlert.error('绑定失败，请稍后重试');
    } finally {
        // 重置按钮状态
        if (shellSN.nextElementSibling) {
            shellSN.nextElementSibling._binding = false;
        }
    }
}

// 添加主标签页切换函数
function switchMainTab(tabName) {
    // 更新标签按钮状态
    document.querySelectorAll('.main-tab-button').forEach(button => {
        button.classList.remove('active');
    });
    document.querySelector(`.main-tab-button[onclick="switchMainTab('${tabName}')"]`).classList.add('active');
    
    // 更新内容显示
    document.querySelectorAll('.main-tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    // 清空所有输入
    clearInputs();
}

// 在文件末尾添加字符计数函数
function updateCharCount(type) {
    const inputId = `${type}-char-counter`;
    const lengthInputId = `${type}-pcba-sn-length`;
    
    const input = document.getElementById(inputId);
    const lengthInput = document.getElementById(lengthInputId);
    
    if (input && lengthInput) {
        const length = input.value.length;
        lengthInput.value = length;
        
        // 更新placeholder显示字符数
        input.placeholder = `输入内容自动计算长度 (当前: ${length})`;
    }
}
