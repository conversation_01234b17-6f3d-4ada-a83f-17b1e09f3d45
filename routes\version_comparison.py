# routes/version_comparison.py
from flask import Blueprint, request, jsonify
from models.firmware import Firmware, DownloadRecord
from models.test_result import CPUTest, CouplerTest
from models.modelwork_order import WorkOrder
from database.db_manager import DatabaseManager
from utils.firmware_utils import create_response, handle_db_error
from sqlalchemy import and_, or_, desc
from datetime import datetime
import logging

logger = logging.getLogger(__name__)
version_comparison_bp = Blueprint('version_comparison', __name__, url_prefix='/api/version-comparison')

# 创建数据库管理器实例
db_manager = DatabaseManager()


def get_work_order_by_sn(session, sn):
    """
    根据SN号获取工单号（用于基准版本查询）
    
    专门用于基准版本查询，只获取工单号用于后续查找固件标准版本
    
    Args:
        session: 数据库session
        sn: 产品序列号
        
    Returns:
        str: 工单号，如果未找到返回None
    """
    try:
        # 首先查询CPU控制器测试记录
        cpu_result = session.query(CPUTest.work_order).filter(
            CPUTest.pro_sn == sn
        ).order_by(CPUTest.test_time.desc()).first()
        
        if cpu_result:
            return cpu_result.work_order
        
        # 如果CPU表没有找到，查询耦合器/IO模块测试记录
        coupler_result = session.query(CouplerTest.work_order).filter(
            CouplerTest.pro_sn == sn
        ).order_by(CouplerTest.test_time.desc()).first()
        
        if coupler_result:
            return coupler_result.work_order
        
        return None
        
    except Exception as e:
        logger.error(f"获取SN号 {sn} 对应工单号失败: {str(e)}")
        return None


def get_customer_name_by_work_order(session, work_order):
    """
    根据工单号获取客户名称
    
    Args:
        session: 数据库session
        work_order: 工单号
        
    Returns:
        str: 客户名称，如果未找到返回None
    """
    try:
        work_order_record = session.query(WorkOrder).filter(
            WorkOrder.dbord_processOrderNo == work_order
        ).first()
        
        if work_order_record and work_order_record.dbord_customerName:
            return work_order_record.dbord_customerName
        
        return None
        
    except Exception as e:
        logger.error(f"获取工单号 {work_order} 对应客户名称失败: {str(e)}")
        return None


def get_test_record_info(session, sn):
    """
    根据SN号获取完整的测试记录信息（用于待比对版本查询）
    
    专门用于待比对版本查询，获取测试记录中的实际数据
    包括基本信息、测试状态和实际版本信息
    
    Args:
        session: 数据库session
        sn: 产品序列号
        
    Returns:
        dict: 包含完整测试记录信息的字典，如果未找到返回None
    """
    try:
        # 首先查询CPU控制器测试记录
        cpu_result = session.query(CPUTest).filter(
            CPUTest.pro_sn == sn
        ).order_by(CPUTest.test_time.desc()).first()
        
        if cpu_result:
            return {
                # 基本信息
                'basic_info': {
                    'sn': sn,
                    'work_order': cpu_result.work_order,
                    'product_model': cpu_result.pro_model,
                    'product_code': cpu_result.pro_code,
                    'test_status': cpu_result.test_status,
                    'test_time': cpu_result.test_time.strftime('%Y-%m-%d %H:%M:%S') if cpu_result.test_time else None,
                    'tester': cpu_result.tester,
                    'product_type': 'CPU控制器',
                    'batch_number': cpu_result.pro_batch or ''
                },
                # 实际测试的版本信息
                'version_info': {
                    'product_type': 'CPU控制器',
                    'software_version': cpu_result.sw_version or '',
                    'build_time': cpu_result.build_date or '',
                    'backplane_version': cpu_result.back_ver or '',
                    'io_version': cpu_result.high_speed_io_version or '',
                    'device_name': getattr(cpu_result, 'device_name', ''),
                    'serial': getattr(cpu_result, 'serial', ''),
                    'test_time': cpu_result.test_time.strftime('%Y-%m-%d %H:%M:%S') if cpu_result.test_time else '',
                    'tester': cpu_result.tester or ''
                }
            }
        
        # 如果CPU表没有找到，查询耦合器/IO模块测试记录
        coupler_result = session.query(CouplerTest).filter(
            CouplerTest.pro_sn == sn
        ).order_by(CouplerTest.test_time.desc()).first()
        
        if coupler_result:
            product_type = 'IO模块' if coupler_result.product_type == 'io_module' else '耦合器'
            return {
                # 基本信息
                'basic_info': {
                    'sn': sn,
                    'work_order': coupler_result.work_order,
                    'product_model': coupler_result.pro_model,
                    'product_code': coupler_result.pro_code,
                    'test_status': coupler_result.test_status,
                    'test_time': coupler_result.test_time.strftime('%Y-%m-%d %H:%M:%S') if coupler_result.test_time else None,
                    'tester': coupler_result.tester,
                    'product_type': product_type,
                    'batch_number': coupler_result.pro_batch or ''
                },
                # 实际测试的版本信息
                'version_info': {
                    'product_type': product_type,
                    'software_version': coupler_result.couplersw_version or '',
                    'build_time': coupler_result.coupler_date or '',
                    'backplane_version': '',  # 耦合器/IO模块没有背板版本
                    'io_version': '',  # 耦合器/IO模块没有高速IO版本
                    'test_time': coupler_result.test_time.strftime('%Y-%m-%d %H:%M:%S') if coupler_result.test_time else '',
                    'tester': coupler_result.tester or ''
                }
            }
        
        return None
        
    except Exception as e:
        logger.error(f"获取SN号 {sn} 测试记录信息失败: {str(e)}")
        return None


def get_firmware_version_info(session, work_order):
    """
    根据工单号获取固件版本信息
    
    查询流程：工单号 → download_record表 → firmware表 → 版本信息
    
    Args:
        session: 数据库session
        work_order: 工单号
        
    Returns:
        dict: 包含固件版本信息的字典，如果未找到返回None
    """
    try:
        # 第一步：通过工单号在download_record表中查找对应的固件流水号
        download_record = session.query(DownloadRecord).filter(
            DownloadRecord.work_order == work_order,
            DownloadRecord.is_deleted == False
        ).order_by(DownloadRecord.create_time.desc()).first()
        
        if not download_record:
            logger.warning(f"未找到工单号 {work_order} 对应的下载记录")
            return None
        
        # 第二步：根据固件流水号查询固件信息
        firmware = session.query(Firmware).filter(
            Firmware.serial_number == download_record.serial_number,
            Firmware.status == 'active',  # 仅查询已生效的固件
            Firmware.is_deleted == False
        ).first()
        
        if not firmware:
            logger.warning(f"未找到流水号 {download_record.serial_number} 对应的活跃固件")
            return None
        
        return {
            'serial_number': firmware.serial_number,
            'firmware_name': firmware.name,
            'software_version': firmware.version,
            'build_time': firmware.build_time or '',
            'backplane_version': firmware.backplane_version or '',
            'io_version': firmware.io_version or '',
            'developer': firmware.developer,
            'status': firmware.status,
            'approve_time': firmware.approve_time.strftime('%Y-%m-%d %H:%M:%S') if firmware.approve_time else None,
            'description': firmware.description
        }
        
    except Exception as e:
        logger.error(f"获取工单号 {work_order} 固件版本信息失败: {str(e)}")
        return None





@version_comparison_bp.route('/baseline/<sn>', methods=['GET'])
def get_baseline_version(sn):
    """
    获取基准版本信息（标准固件版本）
    
    目的：获取标准/预期的固件版本作为比对基准
    数据来源：固件管理系统中status为"active"的标准固件
    
    查询流程：
    SN号 → 测试记录表 → 工单号 → download_record表 → firmware表 → 标准版本信息
    
    Args:
        sn: 产品序列号
        
    Returns:
        JSON响应包含基准版本信息（来自固件管理系统的标准版本）
    """
    try:
        with db_manager.get_session() as session:
            # 第一步：根据SN号获取对应的工单号
            work_order = get_work_order_by_sn(session, sn)
            if not work_order:
                return create_response(False, f'未找到SN号 {sn} 对应的工单记录', code=404)
            
            # 第二步：根据工单号从固件管理系统获取标准固件版本信息
            firmware_info = get_firmware_version_info(session, work_order)
            if not firmware_info:
                # 如果没有找到活跃标准固件版本，尝试获取测试记录作为fallback
                test_record_info = get_test_record_info(session, sn)
                if test_record_info:
                    # 返回包含fallback数据的响应
                    fallback_data = {
                        'basic_info': {
                            'sn': sn,
                            'work_order': work_order,
                            'data_source': '测试记录（fallback）',
                            'description': '非最新版本（未找到活跃标准固件）'
                        },
                        'firmware_info': {
                            'firmware_name': '测试记录中的固件',
                            'developer': '未知',
                            'status': 'inactive'  # 明确标记为非活跃
                        },
                        'version_values': {
                            'software_version': test_record_info['version_info']['software_version'],
                            'build_time': test_record_info['version_info']['build_time'],
                            'backplane_version': test_record_info['version_info']['backplane_version'],
                            'io_version': test_record_info['version_info']['io_version']
                        }
                    }
                    
                    response_data = {
                        'fallback_data': fallback_data
                    }
                    return create_response(
                        False, 
                        f'未找到工单号 {work_order} 对应的活跃标准固件版本',
                        response_data,
                        code=404
                    )
                else:
                    return create_response(
                        False, 
                        f'未找到工单号 {work_order} 对应的活跃标准固件版本', 
                        code=404
                    )
            
            # 组合返回数据
            result_data = {
                'basic_info': {
                    'sn': sn,
                    'work_order': work_order,
                    'data_source': '固件管理系统',
                    'description': '标准/预期版本作为比对基准'
                },
                'firmware_info': firmware_info,
                'baseline_values': {
                    'software_version': firmware_info['software_version'],
                    'build_time': firmware_info['build_time'],
                    'backplane_version': firmware_info['backplane_version'],
                    'io_version': firmware_info['io_version']
                }
            }
            
            logger.info(f"成功获取SN号 {sn} 的基准版本信息（来自固件管理系统）")
            return create_response(True, '基准版本信息获取成功', result_data)
            
    except Exception as e:
        logger.error(f"获取基准版本信息失败: {str(e)}")
        return handle_db_error(e)


@version_comparison_bp.route('/target/<sn>', methods=['GET'])
def get_target_version(sn):
    """
    获取待比对版本信息（实际测试版本）
    
    目的：获取实际测试时的版本信息进行比对
    数据来源：测试记录表中的真实测试数据
    
    查询流程：
    SN号 → 测试记录表(CPUTest/CouplerTest) → 直接获取实际版本信息
    
    Args:
        sn: 产品序列号
        
    Returns:
        JSON响应包含待比对版本信息（来自测试记录的实际版本）
    """
    try:
        with db_manager.get_session() as session:
            # 直接从测试记录表获取完整的实际测试信息
            test_record_info = get_test_record_info(session, sn)
            if not test_record_info:
                return create_response(False, f'未找到SN号 {sn} 对应的测试记录', code=404)
            
            # 提取数据
            basic_info = test_record_info['basic_info']
            version_info = test_record_info['version_info']
            
            # 根据工单号获取客户名称
            customer_name = None
            if basic_info.get('work_order'):
                customer_name = get_customer_name_by_work_order(session, basic_info['work_order'])
            
            # 添加客户名称到基本信息
            basic_info['customer_name'] = customer_name or '未知客户'
            
            # 添加数据来源说明
            basic_info['data_source'] = '测试记录表'
            basic_info['description'] = '实际测试版本进行比对'
            
            # 组合返回数据
            result_data = {
                'basic_info': basic_info,
                'version_info': version_info,
                'version_values': {
                    'software_version': version_info['software_version'],
                    'build_time': version_info['build_time'],
                    'backplane_version': version_info['backplane_version'],
                    'io_version': version_info['io_version']
                }
            }
            
            logger.info(f"成功获取SN号 {sn} 的待比对版本信息（来自测试记录）")
            return create_response(True, '待比对版本信息获取成功', result_data)
            
    except Exception as e:
        logger.error(f"获取待比对版本信息失败: {str(e)}")
        return handle_db_error(e)


@version_comparison_bp.route('/compare', methods=['POST'])
def compare_versions():
    """
    执行版本比对
    
    Request Body:
        {
            "baseline": {
                "software_version": "v1.0.0",
                "build_time": "2024-01-01",
                "backplane_version": "v1.0.0",
                "io_version": "v1.0.0"
            },
            "target": {
                "sn": "SN123456",
                "software_version": "v1.0.1",
                "build_time": "2024-01-02",
                "backplane_version": "v1.0.0",
                "io_version": "v1.0.1"
            }
        }
        
    Returns:
        JSON响应包含比对结果
    """
    try:
        data = request.get_json()
        
        # 验证请求数据
        if not data or 'baseline' not in data or 'target' not in data:
            return create_response(False, '请求数据格式错误，需要包含baseline和target字段', code=400)
        
        baseline = data['baseline']
        target = data['target']
        
        # 定义比对字段
        comparison_fields = [
            {
                'field': 'software_version',
                'label': '软件版本',
                'baseline_value': baseline.get('software_version', ''),
                'target_value': target.get('software_version', '')
            },
            {
                'field': 'build_time',
                'label': '构建日期',
                'baseline_value': baseline.get('build_time', ''),
                'target_value': target.get('build_time', '')
            },
            {
                'field': 'backplane_version',
                'label': '背板总线版本',
                'baseline_value': baseline.get('backplane_version', ''),
                'target_value': target.get('backplane_version', '')
            },
            {
                'field': 'io_version',
                'label': '高速IO版本',
                'baseline_value': baseline.get('io_version', ''),
                'target_value': target.get('io_version', '')
            },
            {
                'field': 'work_order',
                'label': '工单号',
                'baseline_value': baseline.get('work_order', '无'),
                'target_value': target.get('work_order', '')
            }
        ]
        
        # 执行比对
        comparison_results = []
        all_matched = True
        
        for field_info in comparison_fields:
            baseline_val = field_info['baseline_value'].strip()
            target_val = field_info['target_value'].strip()
            
            # 工单号特殊比对逻辑
            if field_info['field'] == 'work_order':
                if baseline_val == '无':
                    # 基准值为"无"时，直接显示"正确"
                    is_match = True
                else:
                    # 基准值不为"无"时，正常比对
                    is_match = baseline_val == target_val
            else:
                # 其他字段正常比对
                is_match = baseline_val == target_val
            
            if not is_match:
                all_matched = False
            
            comparison_results.append({
                'field': field_info['field'],
                'label': field_info['label'],
                'baseline_value': baseline_val,
                'target_value': target_val,
                'is_match': is_match,
                'status': 'PASS' if is_match else 'FAIL'
            })
        
        # 生成比对总结
        matched_count = sum(1 for result in comparison_results if result['is_match'])
        total_count = len(comparison_results)
        
        result_data = {
            'comparison_results': comparison_results,
            'summary': {
                'all_matched': all_matched,
                'matched_count': matched_count,
                'total_count': total_count,
                'match_rate': round((matched_count / total_count) * 100, 2) if total_count > 0 else 0,
                'overall_status': 'PASS' if all_matched else 'FAIL'
            },
            'comparison_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'target_sn': target.get('sn', '')
        }
        
        message = f'版本比对完成，{matched_count}/{total_count} 项匹配'
        logger.info(f"版本比对完成: {message}")
        
        return create_response(True, message, result_data)
        
    except Exception as e:
        logger.error(f"版本比对失败: {str(e)}")
        return handle_db_error(e)


 