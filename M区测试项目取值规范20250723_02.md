# M区测试项目取值规范详细配置文档

**文档版本**: 2.0  
**创建日期**: 2025-07-23  
**适用系统**: CPU控制器多产品类型多M区动态控制测试系统  
**配置文件**: MAreaConfigs.js + ProductTypeConfigs.js

## 测试项目基础索引对照表

| 索引 | 测试项目名称 | 代码标识 | 类别 |
|------|-------------|----------|------|
| 0 | RS485_通信 | rs485_1 | 通信 |
| 1 | RS232通信 | rs232 | 通信 |
| 2 | CANbus通信 | canbus | 通信 |
| 3 | EtherCAT通信 | ethercat | 通信 |
| 4 | Backplane Bus通信 | backplane_bus | 通信 |
| 5 | Body I/O输入输出 | body_io | 硬件 |
| 6 | Led数码管 | led_tube | 硬件 |
| 7 | Led灯珠 | led_bulb | 硬件 |
| 8 | U盘接口 | usb_drive | 接口 |
| 9 | SD卡 | sd_slot | 接口 |
| 10 | 调试串口 | debug_port | 接口 |
| 11 | 网口 | net_port | 接口 |
| 12 | 拨码开关 | dip_switch | 硬件 |
| 13 | 复位按钮 | reset_btn | 硬件 |

---

## 1. All配置 (all_config)

### 基本信息
- **配置名称**: All
- **描述**: RS485双路+RS232+CANbus+EtherCAT+Backplane+视觉检测
- **适用产品**: EC310、EC311、EC312、EC321、EC411、EC412、MC501、MC511、MC512、MC521、MC522、MC611、MC612、MC622
- **测试项目总数**: 14个
- **M区控制项目**: 9个（5个自动测试 + 4个视觉检测）

### 启用测试项目列表
```
[0, 1, 2, 3, 4, 5, 6, 7, 12, 8, 9, 10, 11, 13]
```

### M区取值配置

#### 自动测试项目（通信类）
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第1项 | RS485_通信 | M0+M1 | 组合值 | M0=1 AND M1=1 |
| 第2项 | RS232通信 | M2 | 单值 | M2=1 |
| 第3项 | CANbus通信 | M3 | 单值 | M3=1 |
| 第4项 | EtherCAT通信 | M4 | 单值 | M4=1 |
| 第5项 | Backplane Bus通信 | M5 | 单值 | M5=1 + 模块消息检查 |

#### 视觉检测项目（硬件类）
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第6项 | Body I/O输入输出 | M10 | 单值 | M10=1 |
| 第7项 | Led数码管 | M12+M13 | 组合值 | M12=1 AND M13=1 |
| 第8项 | Led灯珠 | M10 | 单值 | M10=1 |
| 第9项 | 拨码开关 | M11 | 单值 | M11=1 |

### M区地址范围
- **使用范围**: M0 ~ M13
- **总共**: 14个M区地址

---

## 2. 高速IO配置 (high_speed_io)

### 基本信息
- **配置名称**: 小型机、MC701
- **描述**: RS485三路+RS232+CANbus+EtherCAT+Backplane+视觉检测
- **适用产品**: MC701、EC201、EC202、EC203、EC205、EC206
- **测试项目总数**: 14个
- **M区控制项目**: 9个（5个自动测试 + 4个视觉检测）

### 启用测试项目列表
```
[0, 1, 2, 3, 4, 5, 6, 7, 12, 8, 9, 10, 11, 13]
```

### M区取值配置

#### 自动测试项目（通信类）
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第1项 | RS485_通信 | M0+M1+M2 | 组合值 | M0=1 AND M1=1 AND M2=1 |
| 第2项 | RS232通信 | M3 | 单值 | M3=1 |
| 第3项 | CANbus通信 | M4 | 单值 | M4=1 |
| 第4项 | EtherCAT通信 | M5 | 单值 | M5=1 |
| 第5项 | Backplane Bus通信 | M6 | 单值 | M6=1 + 模块消息检查 |

#### 视觉检测项目（硬件类）
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第6项 | Body I/O输入输出 | M10 | 单值 | M10=1 |
| 第7项 | Led数码管 | M12+M13 | 组合值 | M12=1 AND M13=1 |
| 第8项 | Led灯珠 | M10 | 单值 | M10=1 |
| 第9项 | 拨码开关 | M11 | 单值 | M11=1 |

### M区地址范围
- **使用范围**: M0 ~ M13
- **总共**: 14个M区地址

---

## 3. 冗余型配置 (redundant_type)

### 基本信息
- **配置名称**: 冗余型
- **描述**: 仅背板通信测试，不使用M区判断
- **适用产品**: EC431、EC451、EC400-0200N、EC401-0200N
- **测试项目总数**: 7个
- **M区控制项目**: 0个（按原有逻辑，不使用M区判断）

### 启用测试项目列表
```
[4, 6, 11, 12, 13, 9, 10]
```

### 测试项目配置
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第1项 | Backplane Bus通信 | 无 | 不使用M区 | 按原有逻辑 |
| 第2项 | Led数码管 | 无 | 不使用M区 | 手动设置 |
| 第3项 | 网口 | 无 | 不使用M区 | 手动设置 |
| 第4项 | 拨码开关 | 无 | 不使用M区 | 手动设置 |
| 第5项 | 复位按钮 | 无 | 不使用M区 | 手动设置 |
| 第6项 | SD卡 | 无 | 不使用M区 | 手动设置 |
| 第7项 | 调试串口 | 无 | 不使用M区 | 手动设置 |

### M区地址范围
- **使用范围**: 无
- **说明**: 冗余型配置不使用M区判断

---

## 4. 201无输入输出配置 (type_201_no_io)

### 基本信息
- **配置名称**: 201无输入输出
- **描述**: RS485四路+CANbus
- **适用产品**: EC201S-CPU1A410N 无本体IO
- **测试项目总数**: 6个
- **M区控制项目**: 2个

### 启用测试项目列表
```
[0, 2, 6, 11, 13, 9]
```

### M区取值配置

#### M区控制项目
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第1项 | RS485_通信 | M0+M1+M2+M3 | 组合值 | M0=1 AND M1=1 AND M2=1 AND M3=1 |
| 第2项 | CANbus通信 | M4 | 单值 | M4=1 |

#### 非M区控制项目
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第3项 | Led数码管 | 无 | 不使用M区 | 手动设置 |
| 第4项 | 网口 | 无 | 不使用M区 | 手动设置 |
| 第5项 | 复位按钮 | 无 | 不使用M区 | 手动设置 |
| 第6项 | SD卡 | 无 | 不使用M区 | 手动设置 |

### M区地址范围
- **使用范围**: M0 ~ M4
- **总共**: 5个M区地址

---

## 5. 201有输入输出配置 (type_201_with_io)

### 基本信息
- **配置名称**: 201有输入输出
- **描述**: RS485四路+CANbus+Backplane
- **适用产品**: EC201S-CPU1A410G0808D
- **测试项目总数**: 7个
- **M区控制项目**: 3个

### 启用测试项目列表
```
[0, 2, 6, 11, 13, 5, 9]
```

### M区取值配置

#### M区控制项目
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第1项 | RS485_通信 | M0+M1+M2+M3 | 组合值 | M0=1 AND M1=1 AND M2=1 AND M3=1 |
| 第2项 | CANbus通信 | M4 | 单值 | M4=1 |
| 第3项 | Backplane Bus通信 | M5 | 单值 | M5=1 + 模块消息检查 |

#### 非M区控制项目
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第4项 | Led数码管 | 无 | 不使用M区 | 手动设置 |
| 第5项 | 网口 | 无 | 不使用M区 | 手动设置 |
| 第6项 | 复位按钮 | 无 | 不使用M区 | 手动设置 |
| 第7项 | Body I/O输入输出 | 无 | 不使用M区 | 手动设置 |
| 第8项 | SD卡 | 无 | 不使用M区 | 手动设置 |

### M区地址范围
- **使用范围**: M0 ~ M5
- **总共**: 6个M区地址

---

## 6. 201五通信配置 (type_201_five_comm)

### 基本信息
- **配置名称**: 201五通信
- **描述**: RS485四路+RS232+CANbus+Backplane
- **适用产品**: EC201S-CPU1A500G0808D
- **测试项目总数**: 7个
- **M区控制项目**: 4个

### 启用测试项目列表
```
[0, 2, 6, 11, 13, 5, 9]
```

### M区取值配置

#### M区控制项目
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第1项 | RS485_通信 | M0+M1+M2+M3 | 组合值 | M0=1 AND M1=1 AND M2=1 AND M3=1 |
| 第2项 | RS232通信 | M4 | 单值 | M4=1 |
| 第3项 | CANbus通信 | M5 | 单值 | M5=1 |
| 第4项 | Backplane Bus通信 | M6 | 单值 | M6=1 + 模块消息检查 |

#### 非M区控制项目
| 项目 | 测试项目名称 | M区地址 | 取值模式 | 判断逻辑 |
|------|-------------|---------|----------|----------|
| 第5项 | Led数码管 | 无 | 不使用M区 | 手动设置 |
| 第6项 | 网口 | 无 | 不使用M区 | 手动设置 |
| 第7项 | 复位按钮 | 无 | 不使用M区 | 手动设置 |
| 第8项 | Body I/O输入输出 | 无 | 不使用M区 | 手动设置 |
| 第9项 | SD卡 | 无 | 不使用M区 | 手动设置 |

### M区地址范围
- **使用范围**: M0 ~ M6
- **总共**: 7个M区地址

---

## 特殊说明

### 1. RS485_通信项目特殊性
- **All配置**: 使用M0+M1（双路）
- **高速IO配置**: 使用M0+M1+M2（三路）
- **201系列配置**: 使用M0+M1+M2+M3（四路）
- **判断逻辑**: 所有指定M区值都必须为1（AND逻辑）

### 2. Backplane Bus通信双重验证
所有配置中的Backplane Bus通信都需要进行双重验证：
1. **M区值检查**: 指定M区值=1
2. **模块消息检查**: 检查是否存在slave=1的模块
3. **通过条件**: 两项检查都必须通过

### 3. 视觉检测项目
只有All配置和高速IO配置支持视觉检测，包含：
- Body I/O输入输出 (M10)
- Led数码管 (M12+M13组合)
- Led灯珠 (M10)
- 拨码开关 (M11)

### 4. M区数据格式
- **格式**: 逗号分隔的数值字符串
- **示例**: "0, 1, 1, 0, 1, 2, 0, 0, 1, 1, 1, 1, 1, 1"
- **索引**: M0对应索引0，M1对应索引1，以此类推

### 5. 配置文件位置
- **M区映射配置**: `/static/js/utils/MAreaConfigs.js`
- **产品类型配置**: `/static/js/utils/ProductTypeConfigs.js`
- **配置管理器**: `/static/js/utils/ConfigManager.js`

---

**维护说明**: 
1. 所有M区配置都支持动态切换
2. 系统自动根据产品类型加载相应的M区映射
3. 配置变更需要同时更新MAreaConfigs.js和ProductTypeConfigs.js
4. 测试执行时会自动验证M区配置的完整性