# 耦合器测试系统功能架构分析

## 系统概述

耦合器测试系统是一个基于Web的产品测试管理平台，专门用于耦合器模块的质量检测和数据记录。系统采用前后端分离架构，通过严格的验证流程确保测试数据的准确性和完整性。与IO模块测试系统相比，耦合器系统具有更多的测试项目和特定的业务逻辑。

## 1. 工单验证逻辑

### 1.1 工单号输入触发机制
耦合器系统采用防抖机制处理工单号输入，通过500ms延迟避免频繁的API调用。当用户在工单号输入框中输入内容时，系统会自动触发工单信息查询流程。

### 1.2 工单状态检查验证规则
系统对工单进行严格的状态验证：
- **存在性验证**：工单号必须在系统中存在
- **阶段完成验证**：工单必须完成测试前阶段外观检验（`test_stage_completed = true`）
- **PCBA检查配置**：根据工单配置设置PCBA绑定检查标志（`ord_requires_pcba_check`）

### 1.3 工单信息查询API调用流程
```
用户输入工单号 → 防抖处理(500ms) → API调用(/api/work-order/by-number) 
    ↓
状态验证(test_stage_completed) → 自动填充字段 → 设置PCBA检查标志
    ↓
验证失败 → 清空字段 → 用户提示
```

### 1.4 验证失败错误处理
当工单验证失败时，系统会：
- **清空相关字段**：生产数量、产品编码、产品型号、批次号、SN字节数
- **移除视觉状态**：清除输入框的绿色背景样式
- **用户提示**：显示具体的错误信息（如"该工单未完成测试前阶段外观检验"）
- **状态重置**：将PCBA检查标志重置为默认值（true）

### 1.5 自动填充机制
验证成功后，系统会自动填充以下字段：
- 生产数量（ord_productionQuantity）
- 产品编码（ord_productCode）
- 产品型号（ord_productModel）
- 批次号（ord_probatch）
- SN字节数（ord_snlenth）

同时为已填充的字段添加绿色背景样式，提供视觉反馈。

## 2. 检测状态逻辑

### 2.1 SN号输入检测流程
耦合器系统在产品SN号输入框失焦时触发检测流程，这种设计避免了输入过程中的频繁检查，提供更好的用户体验。

### 2.2 PCBA绑定状态检查机制
系统根据工单配置的PCBA检查标志（`requiresPcbaCheck`）决定检测策略：

**需要PCBA检查的情况：**
- 调用API `/api/coupler/check-sn` 检查SN号绑定状态
- 验证产品是否已完成PCBA绑定流程
- 未绑定时阻止继续测试流程

**无需PCBA检查的情况：**
- 跳过PCBA绑定验证
- 直接进入版本信息自动获取流程
- 在控制台输出"当前工单无需PCBA绑定检查，允许继续测试"

### 2.3 产品状态对检测流程的影响
系统支持三种产品状态，每种状态都会影响后续的版本信息获取：
- **新品(new)**：全新产品的标准测试流程
- **维修(used)**：维修后产品的复测流程
- **返工(refurbished)**：返工产品的重新测试流程

### 2.4 检测失败处理逻辑
当PCBA绑定检查失败时，系统执行以下处理：
- **用户提示**：显示"该SN号未绑定PCBA！"警告信息
- **输入清空**：清空SN号输入框内容
- **焦点重置**：自动将焦点重新设置到SN号输入框
- **流程中断**：阻止后续的版本信息获取流程

### 2.5 检测成功后续流程
PCBA绑定验证成功后，系统会：
- 自动触发版本信息获取流程
- 根据产品状态调用相应的版本查询API
- 为用户提供无缝的操作体验

## 3. 版本比较逻辑

### 3.1 自动版本信息获取触发条件
耦合器系统的版本信息获取有多个触发点：
- **SN号输入完成**：当SN号通过PCBA检查后自动触发
- **产品状态变化**：当用户改变产品状态时重新获取版本信息
- **状态支持验证**：仅支持new、used、refurbished三种状态

### 3.2 版本信息数据来源和获取方式
系统通过API `/api/coupler/get-version-info` 获取版本信息：
- **请求参数**：产品SN号（product_sn）和产品状态（product_status）
- **返回数据**：软件版本（software_version）和构建时间（build_time）
- **自动填充**：成功获取后自动填充到"自动获取版本"和"自动获取日期"字段

### 3.3 版本比较规则
系统采用严格的字符串精确匹配规则：

**版本一致性验证：**
- 自动获取版本 ≡ 手动输入的耦合器软件版本
- 自动获取日期 ≡ 手动输入的耦合器构建日期
- 任何字符差异都会导致验证失败

**验证时机：**
- 在表单提交时进行版本一致性检查
- 只有当自动获取的版本信息存在时才进行比较
- 如果自动获取为空，则不进行一致性验证

### 3.4 版本不一致错误处理
当版本比较失败时，系统会：
- **版本不一致**：显示"软件版本错误：自动获取版本与耦合器软件版本不一致！"
- **日期不一致**：显示"软件版本错误：自动获取日期与耦合器构建日期不一致！"
- **焦点定位**：自动将焦点设置到出错的输入字段
- **提交阻止**：阻止表单提交，要求用户修正错误

### 3.5 产品状态变化的版本管理
当产品状态发生变化时：
- **清空自动获取信息**：清除之前获取的版本和日期信息
- **移除视觉状态**：清除自动获取字段的绿色背景
- **重新获取**：如果已有SN号，则基于新状态重新获取版本信息
- **状态联动**：确保版本信息与当前产品状态保持一致

## 4. 测试提交逻辑

### 4.1 提交前数据验证步骤
耦合器系统采用多层次的数据验证机制：

**第一层：必填字段验证**
系统检查10个核心必填字段：
- 测试人员、加工单号、生产数量、产品编码、产品型号
- 产品状态、产品SN号、产品SN号字节数
- 耦合器软件版本、耦合器构建日期

**第二层：SN号长度验证**
- 验证产品SN号长度与预设字节数的精确匹配
- 检查字节数输入的有效性（非NaN）
- 长度不匹配时显示具体的错误信息

**第三层：版本一致性验证**
- 比较自动获取版本与手动输入的耦合器软件版本
- 比较自动获取日期与手动输入的耦合器构建日期
- 任何不一致都会阻止提交并定位到错误字段

### 4.2 表单数据收集和格式转换
系统将表单数据转换为后端所需的格式：

**基本信息转换：**
- 表单字段映射到数据库字段名
- 空值处理（使用'N/A'作为默认值）
- 产品状态映射：new→1, used→2, refurbished→3

**测试结果转换：**
耦合器系统包含5个测试项目：
- Backplane Bus通信、Body I/O输入输出、Led数码管、Led灯珠、网口
- 测试结果映射：通过→1, 不通过→2
- 整体测试状态判断：全部通过→'pass', 有失败→'ng'

**维修返工计数：**
- maintenance：产品状态为维修(2)时设为1，否则为0
- rework：产品状态为返工(3)时设为1，否则为0

### 4.3 测试结果默认状态和用户选择处理
**默认状态设计：**
- 所有测试项目默认选中"通过"状态
- 体现乐观测试理念，减少用户操作步骤
- 只有测试失败时用户才需要手动选择"不通过"

**用户选择处理：**
- 支持全选/取消全选功能
- 选择"不通过"时为对应行添加视觉标识（test-failed样式）
- 个别选择会影响全选状态的同步

### 4.4 提交成功后状态管理和界面重置
**字段保持策略：**
保留以下字段值以提高操作效率：
- 测试人员、加工单号、生产数量、产品编码、产品型号
- 产品状态、批次号、SN字节数、备注
- 耦合器软件版本、耦合器构建日期

**选择性清空：**
- 清空产品SN号（每次测试都不同）
- 清空自动获取的版本信息
- 重置所有测试项目为默认"通过"状态

**UI状态控制：**
- 暂时禁用提交按钮，直到输入新的SN号
- 自动将焦点设置到产品SN号输入框
- 恢复保留字段的绿色背景样式
- 移除测试失败的视觉标识

### 4.5 数据流转和API交互
**提交流程：**
```
数据验证 → 格式转换 → API调用(/api/coupler/submit-test) → 结果处理
    ↓
成功：状态重置 → 字段保持 → 焦点设置 → 用户提示
    ↓
失败：错误提示 → 保持当前状态 → 用户修正
```

**错误处理机制：**
- 网络错误：显示"请检查网络连接"
- 业务错误：显示后端返回的具体错误信息
- 验证错误：定位到具体字段并提供修正建议

## 功能模块间的逻辑关系和数据流转

### 模块间依赖关系
```
工单验证 → 检测状态 → 版本比较 → 测试提交
    ↓         ↓         ↓         ↓
前置条件   PCBA验证   版本一致   数据完整
    ↓         ↓         ↓         ↓
自动填充   版本获取   严格校验   智能重置
```

### 数据流转路径
1. **工单驱动流程**：工单号输入 → 工单验证 → 字段自动填充 → PCBA检查配置
2. **检测验证流程**：SN号输入 → PCBA绑定检查 → 版本信息获取 → 数据准备完成
3. **版本校验流程**：自动获取版本 → 用户手动输入 → 一致性比较 → 验证通过/失败
4. **提交处理流程**：数据收集 → 多层验证 → 格式转换 → API提交 → 状态重置

### 状态管理机制
- **全局状态**：使用`window.couplerState`管理页面级状态
- **PCBA检查标志**：`requiresPcbaCheck`控制检测流程
- **UI状态同步**：输入框样式、按钮状态、焦点管理
- **错误状态处理**：验证失败时的状态回滚和用户提示

## 核心API接口架构

### 耦合器专用API端点
- **工单查询**：`/api/work-order/by-number` - 获取工单详细信息
- **PCBA检查**：`/api/coupler/check-sn` - 验证SN号PCBA绑定状态
- **版本获取**：`/api/coupler/get-version-info` - 获取产品版本信息
- **测试提交**：`/api/coupler/submit-test` - 提交测试结果数据
- **用户信息**：`/api/coupler/get-current-user` - 获取当前登录用户
- **产品映射**：`/api/product-mapping/get-model` - 根据产品编码获取型号

### API调用策略
- **防抖调用**：工单号输入处理（500ms延迟）
- **失焦触发**：SN号PCBA检查（blur事件）
- **实时调用**：产品编码到型号的映射
- **状态联动**：产品状态变化时的版本信息重新获取

## 耦合器系统特有功能

### 产品编码自动映射
耦合器系统具有独特的产品编码到型号的自动映射功能：
- **实时查询**：用户输入产品编码时实时调用API获取对应型号
- **自动填充**：成功获取型号后自动填充产品型号字段
- **状态同步**：编码有效时添加绿色背景，无效时移除样式
- **联动清空**：编码清空或无效时同时清空产品型号

### 扩展的测试项目
相比IO模块，耦合器系统包含更多测试项目：
- Backplane Bus通信
- Body I/O输入输出  
- Led数码管（耦合器特有）
- Led灯珠
- 网口（耦合器特有）

### 消息提示系统
耦合器系统内置了完整的消息提示机制：
- **消息类型**：支持info、warning、error等不同类型
- **显示模式**：支持自动消失和持续显示两种模式
- **交互控制**：持续显示的消息提供关闭按钮
- **去重机制**：自动移除相同类型的重复消息

## 系统优势与特点

### 业务流程完整性
```
工单驱动 → 产品编码映射 → PCBA绑定验证 → 版本一致性校验 → 多项目测试 → 智能状态管理
```

### 数据准确性保障
1. **多层验证机制**：前端验证、API验证、业务逻辑验证
2. **严格的版本比较**：字符串精确匹配，不允许任何差异
3. **完整的字段验证**：10个必填字段的全面检查
4. **SN号长度精确验证**：与预设字节数的严格匹配

### 用户体验优化
1. **智能自动填充**：减少重复输入，提高操作效率
2. **防抖机制**：避免频繁API调用，提升系统性能
3. **状态保持**：提交后保留常用字段，支持批量操作
4. **视觉反馈**：绿色背景、错误提示、焦点管理等直观反馈

### 错误处理机制
1. **分类错误处理**：网络错误、业务错误、验证错误的不同处理策略
2. **用户友好提示**：中文错误信息，明确指出问题所在
3. **状态恢复机制**：错误后的快速状态恢复和用户引导
4. **焦点管理**：错误时自动定位到问题字段

## 技术实现特点

### 模块化设计
- **功能分离**：工单验证、检测状态、版本比较、测试提交各自独立
- **状态管理**：全局状态对象统一管理页面状态
- **事件驱动**：基于DOM事件的松耦合交互模式

### 性能优化
- **防抖处理**：减少不必要的API调用
- **失焦触发**：避免输入过程中的频繁检查
- **状态缓存**：合理利用全局状态减少重复计算

### 扩展性设计
- **API接口标准化**：统一的请求响应格式
- **配置化验证**：通过工单配置控制业务流程
- **组件化UI**：可复用的表单组件和验证逻辑
