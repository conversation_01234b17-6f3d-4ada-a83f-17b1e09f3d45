# PLC生产质量检验系统数据库架构分析

## 数据库概述

当前PLC生产质量检验系统使用的是一个关系型数据库（MySQL），采用了五个主要表来管理产品类型、工单、检验项目和检验记录。系统设计遵循了数据库规范化原则，使用外键约束确保数据完整性。

## 表结构与关系图

```
+----------------+       +---------------+        +------------------+
| product_types  |1------*| work_orders  |1------*| inspection_records|
+----------------+       +---------------+        +------------------+
        |                                                  |
        |                                                  |
        |                                                  |
        |                +------------------+              |
        +---------------*| inspection_items |*-------------+
                         +------------------+
                                  
                                  |
                                  |
+----------------------+          |
| inspection_attachments|*--------+
+----------------------+
```

## 详细表结构

### 1. product_types (产品类型表)

保存不同PLC产品类型的基本信息。

| 字段名 | 数据类型 | 说明 | 约束 |
|--------|----------|------|------|
| id | INT | 主键ID | PRIMARY KEY, AUTO_INCREMENT |
| type_code | VARCHAR(20) | 产品类型代码 | NOT NULL, UNIQUE |
| type_name | VARCHAR(50) | 产品类型名称 | NOT NULL |
| description | VARCHAR(200) | 产品描述 | 可为空 |
| created_at | TIMESTAMP | 创建时间 | DEFAULT CURRENT_TIMESTAMP |

当前系统定义了两种产品类型：五代PLC和四代PLC。

### 2. work_orders (工单表)

存储生产工单信息，与产品类型关联。

| 字段名 | 数据类型 | 说明 | 约束 |
|--------|----------|------|------|
| id | INT | 主键ID | PRIMARY KEY, AUTO_INCREMENT |
| work_order_no | VARCHAR(50) | 工单号 | NOT NULL, UNIQUE |
| product_type_id | INT | 关联产品类型ID | NOT NULL, FOREIGN KEY |
| status | VARCHAR(20) | 工单状态 | DEFAULT 'pending' |
| created_at | TIMESTAMP | 创建时间 | DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | 更新时间 | DEFAULT CURRENT_TIMESTAMP ON UPDATE |

**status字段**:
- pending: 待检验
- processing: 检验中
- completed: 已完成

### 3. inspection_items (检验项目表)

定义各产品类型在不同检验阶段的检验项目。

| 字段名 | 数据类型 | 说明 | 约束 |
|--------|----------|------|------|
| id | INT | 主键ID | PRIMARY KEY, AUTO_INCREMENT |
| product_type_id | INT | 关联产品类型ID | NOT NULL, FOREIGN KEY |
| stage | VARCHAR(20) | 检验阶段 | NOT NULL |
| item_no | INT | 项目编号 | NOT NULL |
| item_name | VARCHAR(200) | 项目名称 | NOT NULL |
| display_order | INT | 显示顺序 | DEFAULT 0 |
| is_required | BOOLEAN | 是否必检项 | DEFAULT TRUE |
| created_at | TIMESTAMP | 创建时间 | DEFAULT CURRENT_TIMESTAMP |

**stage字段**:
- assembly: 组装前阶段
- test: 测试前阶段
- packaging: 包装前阶段

**联合唯一键**:
- `uk_product_stage_no` (product_type_id, stage, item_no): 确保每个产品每个阶段的项目编号唯一

### 4. inspection_records (检验记录表)

记录检验人员对工单中检验项目的检查结果。

| 字段名 | 数据类型 | 说明 | 约束 |
|--------|----------|------|------|
| id | INT | 主键ID | PRIMARY KEY, AUTO_INCREMENT |
| work_order_id | INT | 关联工单ID | NOT NULL, FOREIGN KEY |
| inspection_item_id | INT | 关联检验项目ID | NOT NULL, FOREIGN KEY |
| inspector_role | VARCHAR(20) | 检验人员角色 | NOT NULL |
| is_checked | BOOLEAN | 是否通过 | NOT NULL, DEFAULT FALSE |
| remark | VARCHAR(500) | 备注说明 | 可为空 |
| checked_by | VARCHAR(50) | 检验人 | NOT NULL |
| checked_at | TIMESTAMP | 检验时间 | DEFAULT CURRENT_TIMESTAMP |

**inspector_role字段**:
- first: 首检
- self: 自检
- ipqc: IPQC

**联合唯一键**:
- `uk_record` (work_order_id, inspection_item_id, inspector_role): 确保每个工单的每个检验项目每类角色只有一条记录

### 5. inspection_attachments (检验附件表)

存储检验过程中上传的附件信息。

| 字段名 | 数据类型 | 说明 | 约束 |
|--------|----------|------|------|
| id | INT | 主键ID | PRIMARY KEY, AUTO_INCREMENT |
| work_order_id | INT | 关联工单ID | NOT NULL, FOREIGN KEY |
| stage | VARCHAR(20) | 检验阶段 | NOT NULL |
| inspector_role | VARCHAR(20) | 检验人员角色 | NOT NULL |
| file_name | VARCHAR(255) | 原始文件名 | NOT NULL |
| file_path | VARCHAR(255) | 存储路径 | NOT NULL |
| file_type | VARCHAR(50) | 文件类型 | NOT NULL |
| file_size | INT | 文件大小(字节) | NOT NULL |
| upload_time | TIMESTAMP | 上传时间 | DEFAULT CURRENT_TIMESTAMP |
| uploaded_by | VARCHAR(50) | 上传人 | NOT NULL |

## 索引优化

系统创建了多个索引以提高查询性能：

1. `idx_work_orders_product_type`: 工单表上的产品类型ID索引
2. `idx_work_orders_status`: 工单表上的状态索引
3. `idx_inspection_items_product_stage`: 检验项目表上的复合索引(产品类型ID, 阶段, 显示顺序)
4. `idx_inspection_records_work_order`: 检验记录表上的工单ID索引
5. `idx_inspection_records_item_role`: 检验记录表上的复合索引(检验项目ID, 检验角色)
6. `idx_attachments_work_order`: 附件表上的工单ID索引
7. `idx_attachments_stage_role`: 附件表上的复合索引(检验阶段, 检验角色)

## 数据流与关系

1. 每种**产品类型**(`product_types`)有多个**工单**(`work_orders`)(一对多关系)
2. 每种**产品类型**(`product_types`)关联多个**检验项目**(`inspection_items`)(一对多关系)
3. 每个**工单**(`work_orders`)可以有多条**检验记录**(`inspection_records`)(一对多关系)
4. 每个**检验项目**(`inspection_items`)可以有多条**检验记录**(`inspection_records`)(一对多关系)
5. 每个**工单**(`work_orders`)可以有多个**附件**(`inspection_attachments`)(一对多关系)

## 特点分析

1. **灵活性**: 通过`product_types`和`inspection_items`表的设计，系统可以灵活配置不同产品类型的检验项目，无需修改代码。

2. **可追溯性**: 通过`inspection_records`表，可以追踪每个工单、每个检验项目的检验历史，包括谁在什么时间检验了什么项目。

3. **多角色支持**: 系统通过`inspector_role`字段支持不同角色的检验，每个角色对同一个检验项目可以独立记录检验结果。

4. **文件管理**: 通过`inspection_attachments`表，系统可以关联和管理与检验过程相关的文件附件。

5. **性能优化**: 通过适当的索引设计，系统优化了常见查询场景的性能。

## 结论

这种数据库设计充分考虑了系统的灵活性、数据完整性和查询性能，适合PLC生产质量检验这种需要严格流程管理和质量追溯的场景。系统通过合理的表结构设计和关系定义，能够有效支持不同产品类型、不同检验阶段和不同检验角色的复杂业务需求。
