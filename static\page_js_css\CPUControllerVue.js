// CPU控制器模块Vue版本 - 现代化UI重构版
(function() {
    'use strict';
    
    Logger.log('Loading CPU Controller Vue App with Modern UI...');
    
    // 检查依赖
    if (!window.Vue || !window.ElementPlus) {
        Logger.error('Vue or ElementPlus not loaded');
        return;
    }

    const { createApp, ref, reactive, computed, onMounted, nextTick, watch } = Vue;
    const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;
    
    const CPUControllerVueApp = {
        setup() {
            // 常量定义
            const PROXY_BASE_URL = 'http://127.0.0.1:5000/proxy';
            
            // ===== 配置管理器初始化 =====
            let configManager = null;
            let productTypeOptions = []; // 用于存储从配置管理器获取的选项
            try {
                configManager = window.getConfigManager();
                // 从配置管理器获取产品类型选项
                productTypeOptions = configManager.getAvailableProductTypes().map(p => ({
                    value: p.key,
                    label: p.name,
                    description: p.description,
                    count: p.enabledTestCount
                }));
                Logger.info('[CPUControllerVue] 配置管理器初始化成功');
            } catch (error) {
                Logger.error('[CPUControllerVue] 配置管理器初始化失败:', error);
                // 回退到简化配置
                configManager = {
                    getConfig: () => ({ name: '错误', description: '配置加载失败', enabledTestCount: 0, enabledTests: [], testMapping: [], mAreaMapping: [], productModels: null }),
                    getMAreaMapping: () => [],
                    getMAreaControlInfo: () => null,
                    generateMAreaStatistics: () => ({ valid: false, summary: '配置管理器不可用' }),
                    setCurrentProductType: () => false,
                    getCurrentProductType: () => 'all_config',
                    getAvailableProductTypes: () => []
                };
            }
            
            // 响应式数据
            const loading = ref(false);
            const deviceLoading = ref(false);
            const deviceConnected = ref(false);
            const requiresPcbaCheck = ref(true);
            const basicInfoCollapsed = ref(false);
            const showTestLog = ref(true);
            const autoScroll = ref(true);
            const isDarkMode = ref(true); // 默认深色主题
            const showConfirmDialog = ref(false);
            const confirmAction = ref(null);
            const testLogs = ref([]);
            const currentTestIndex = ref(-1);
            const testRunning = ref(false); // 添加测试运行状态
            const mAreaTestCompleted = ref(false); // 跟踪M区自动测试是否已完成
            const visualTestCompleted = ref(false); // 跟踪视觉检测是否已完成
            
            // 新增：产品类型选择
            const selectedProductType = ref('all_config'); // 默认选择All配置
            const showProductTypeDetails = ref(false);
            
            // 产品类型配置折叠状态
            const productTypeCollapsed = ref(false);
            
            const toggleProductType = () => {
                productTypeCollapsed.value = !productTypeCollapsed.value;
                const refreshIcons = getDebounced('toggleProductTypeRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                nextTick(refreshIcons);
            };
            
            // 计算属性：当前产品类型配置 (从配置管理器获取)
            const currentProductConfig = computed(() => {
                return configManager.getConfig(selectedProductType.value) || {};
            });
            
            // 计算属性：当前启用的测试项目（动态过滤）
            const enabledTestItems = computed(() => {
                const config = currentProductConfig.value;
                const enabled = [];
                
                config.enabledTests.forEach((testIndex, configIndex) => {
                    if (testIndex < testResults.value.length) {
                        const originalItem = testResults.value[testIndex];
                        enabled.push({
                            ...originalItem,
                            originalIndex: testIndex,  // 保留原始索引用于操作
                            configIndex: configIndex,  // 在配置中的索引
                            enabled: true
                        });
                    }
                });
                
                return enabled;
            });
            
            // 计算属性：当前测试项目映射（用于显示详情）
            const currentTestMapping = computed(() => {
                return currentProductConfig.value.testMapping || [];
            });
            
            // 计算属性：是否支持视觉检测（只有all_config和high_speed_io支持）
            const supportsVisualInspection = computed(() => {
                return ['all_config', 'high_speed_io'].includes(selectedProductType.value);
            });
            
            // 表单数据
            const formData = reactive({
                // 基本信息
                tester: '',
                testTime: '',
                orderNumber: '',
                productionQuantity: '',
                productCode: '',
                productModel: '',
                productStatus: '',
                productSN: '',
                batchNumber: '',
                snByteCount: '',
                remarks: '',
                
                // 设备信息
                ipAddress: '*************',
                specifiedVersion: '',
                specifiedTime: '',
                specifiedBackplane: '',
                specifiedHighSpeed: '',
                odmInfo: '',
                deviceManufacturer: '',
                deviceName: '',
                serialNumber: '',
                softwareVersion: '',
                buildDate: '',
                backplaneVersion: '',
                highSpeedIOVersion: '',
                macAddress: '',
                mAreaData: ''
            });
            
            // 测试项目数据（基础数据，M区控制信息由配置管理器动态提供）
            const testItems = reactive([
                { name: "RS485_通信", code: "rs485_1", result: "", category: "通信", icon: "network" },
                { name: "RS232通信", code: "rs232", result: "", category: "通信", icon: "network" },
                { name: "CANbus通信", code: "canbus", result: "", category: "通信", icon: "network" },
                { name: "EtherCAT通信", code: "ethercat", result: "", category: "通信", icon: "wifi" },
                { name: "Backplane Bus通信", code: "backplane_bus", result: "", category: "通信", icon: "database" },
                { name: "Body I/O输入输出", code: "body_io", result: "", category: "硬件", icon: "zap" },
                { name: "Led数码管", code: "led_tube", result: "", category: "硬件", icon: "zap" },
                { name: "Led灯珠", code: "led_bulb", result: "", category: "硬件", icon: "zap" },
                { name: "U盘接口", code: "usb_drive", result: "", category: "接口", icon: "hard-drive" },
                { name: "SD卡", code: "sd_slot", result: "", category: "接口", icon: "hard-drive" },
                { name: "调试串口", code: "debug_port", result: "", category: "接口", icon: "cpu" },
                { name: "网口", code: "net_port", result: "", category: "接口", icon: "network" },
                { name: "拨码开关", code: "dip_switch", result: "", category: "硬件", icon: "settings" },
                { name: "复位按钮", code: "reset_btn", result: "", category: "硬件", icon: "rotate-ccw" }
            ]);
            
            // 动态计算测试结果（集成M区控制信息，支持多M区组合）
            const testResults = computed(() => {
                return testItems.map((item, index) => {
                    // 从配置管理器获取M区控制信息
                    const mAreaControl = configManager ? configManager.getMAreaControlInfo(index, selectedProductType.value) : null;
                    
                    return {
                        ...item,
                        configIndex: index,
                        // 动态添加M区控制信息（兼容单M区和多M区组合）
                        mAreaControlled: !!mAreaControl,
                        mIndex: mAreaControl && mAreaControl.mode === 'single' ? mAreaControl.mIndex : undefined,
                        mIndices: mAreaControl && mAreaControl.mode === 'combined' ? mAreaControl.mIndices : undefined,
                        mAreaMode: mAreaControl ? mAreaControl.mode : undefined,
                        mAreaTestName: mAreaControl ? mAreaControl.testName : undefined,
                        mAreaDescription: mAreaControl && mAreaControl.description ? mAreaControl.description : undefined
                    };
                });
            });
            
            const selectAll = ref(false);
            
            // 产品类型选择处理（集成配置管理器）
            const handleProductTypeChange = (type) => {
                const config = configManager.getConfig(type);
                if (!config) return;
                
                // 更新配置管理器的当前产品类型
                if (configManager && !configManager.setCurrentProductType(type)) {
                    Logger.error('[CPUControllerVue] 配置管理器无法设置产品类型:', type);
                }
                
                // 重置测试状态和M区锁定状态
                mAreaTestCompleted.value = false;
                // 重置视觉检测锁定状态
                visualTestCompleted.value = false;
                
                // 清除所有测试结果
                testItems.forEach(item => {
                    item.result = '';
                });
                
                // 获取M区配置信息（支持多M区组合显示）
                const mAreaMapping = configManager ? configManager.getMAreaMapping(type) : [];
                let mAreaInfo = 'M区控制: 无';
                
                if (mAreaMapping.length > 0) {
                    const mAreaItems = mAreaMapping.map(m => {
                        if (m.mIndices && Array.isArray(m.mIndices)) {
                            // 多M区组合模式
                            return `${m.testName}(M${m.mIndices.join('+M')}[组合])`;
                        } else if (m.mIndex !== undefined) {
                            // 单M区模式
                            return `${m.testName}(M${m.mIndex})`;
                        } else {
                            return `${m.testName}(配置错误)`;
                        }
                    });
                    mAreaInfo = `M区控制项目: ${mAreaItems.join(', ')}`;
                }
                
                addTestLog('info', 'PRODUCT_TYPE', `切换产品类型: ${config.name}`, 
                          `启用测试项目: ${config.enabledTestCount}个 - ${config.description}`);
                
                // 显示配置详情
                const details = config.testMapping.map(m => 
                    `${m.testName} (${m.category})`
                ).join(', ');
                
                addTestLog('info', 'TEST_CONFIG', `测试项目配置: ${details}`);
                addTestLog('info', 'M_AREA_CONFIG', mAreaInfo);
                
                ElMessage.success(`已切换到: ${config.name} (${config.enabledTestCount}个测试项目，${mAreaMapping.length}个M区控制项目)`);

                // 新增：切换产品类型后刷新图标，防止多次切换后图标缺失
                const refreshIcons = getDebounced('productTypeChangeIconRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                nextTick(refreshIcons);
            };
            
            // IP地址选项
            const ipOptions = [
                { label: '*************', value: '*************' },
                { label: '*************', value: '*************' },
                { label: '*************', value: '*************' }
            ];
            
            // 表单验证规则
            const rules = reactive({
                tester: [{ required: true, message: '请输入测试人员', trigger: 'blur' }],
                orderNumber: [{ required: true, message: '请输入加工单号', trigger: 'blur' }],
                productionQuantity: [{ required: true, message: '请输入生产数量', trigger: 'blur' }],
                productCode: [{ required: true, message: '请输入产品编码', trigger: 'blur' }],
                productModel: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
                productStatus: [{ required: true, message: '请选择产品状态', trigger: 'change' }],
                productSN: [{ required: true, message: '请输入产品SN号', trigger: 'blur' }],
                snByteCount: [{ required: true, message: '请输入产品SN号字节数', trigger: 'blur' }],
                serialNumber: [{ required: true, message: '请输入出厂序号', trigger: 'blur' }]
            });
            
            // 表单引用
            const formRef = ref(null);
            
            // 产品状态选项
            const productStatusOptions = [
                { label: '选择产品状态', value: '', disabled: true },
                { label: '新品', value: 'new' },
                { label: '维修', value: 'used' },
                { label: '返工', value: 'refurbished' }
            ];
            
            // 测试结果选项
            const testResultOptions = [
                { label: '通过', value: '通过' },
                { label: '不通过', value: '不通过' }
            ];
            
            // ===== 高质量防抖系统 - 与CouplerVue保持一致 =====
            const debouncedFunctions = {};
            const timeouts = {};
            
            const getDebounced = (key, func, wait) => {
                if (!debouncedFunctions[key]) {
                    const debouncedFunc = function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeouts[key]);
                            delete timeouts[key];
                            func.apply(this, args);
                        };
                        clearTimeout(timeouts[key]);
                        timeouts[key] = setTimeout(later, wait);
                    };
                    debouncedFunctions[key] = debouncedFunc;
                }
                return debouncedFunctions[key];
            };
            
            // 工具函数 - 保留原有简单防抖用于向下兼容
            const debounce = (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            };
            
            // ===== 增强的测试日志功能 - 对标IOModuleVue.js =====
            
            // 日志管理配置
            const logConfig = {
                maxLogs: 500,           // 最大日志条数
                batchSize: 50,          // 批量清理数量
                levels: ['system', 'success', 'error', 'warning', 'info'],
                enabledLevels: ref(['system', 'success', 'error', 'warning', 'info'])
            };
            
            // 测试日志功能 - 性能优化版本
            const addTestLog = (level, category, message, details) => {
                // 性能优化：日志条数限制
                if (testLogs.value.length >= logConfig.maxLogs) {
                    testLogs.value = testLogs.value.slice(-logConfig.maxLogs + logConfig.batchSize);
                }
                
                // 级别过滤：只添加启用的日志级别
                if (!logConfig.enabledLevels.value.includes(level)) {
                    return;
                }
                
                const newLog = {
                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                    timestamp: new Date().toLocaleTimeString('zh-CN', {
                        hour12: false,
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        fractionalSecondDigits: 3,
                    }),
                    level,
                    category,
                    message,
                    details,
                };
                testLogs.value.push(newLog);

                // 性能优化：防抖滚动
                if (autoScroll.value) {
                    nextTick(() => {
                        const logContainer = document.getElementById('test-log-container');
                        if (logContainer) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
                    });
                }
            };
            
            // 日志级别过滤
            const filteredLogs = computed(() => {
                return testLogs.value.filter(log => 
                    logConfig.enabledLevels.value.includes(log.level)
                );
            });
            
            // 日志导出功能
            const exportLogs = () => {
                const logText = filteredLogs.value.map(log => 
                    `[${log.timestamp}] [${log.category}] ${log.level.toUpperCase()}: ${log.message}${log.details ? ' - ' + log.details : ''}`
                ).join('\n');
                
                const blob = new Blob([logText], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `cpu-controller-test-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                ElMessage.success('测试日志已导出');
            };
            
            // 日志级别切换
            const toggleLogLevel = (level) => {
                const index = logConfig.enabledLevels.value.indexOf(level);
                if (index > -1) {
                    logConfig.enabledLevels.value.splice(index, 1);
                } else {
                    logConfig.enabledLevels.value.push(level);
                }
            };

            // 解析M区数据的辅助函数
            const parseMAreaData = () => {
                try {
                    if (!formData.mAreaData || formData.mAreaData === '无内存数据') {
                        return [];
                    }
                    // 解析M区数据字符串，例如 "0, 1, 1, 0, 1, 2, 0, 0"
                    const mAreaValues = formData.mAreaData.split(',').map(val => {
                        const num = parseInt(val.trim());
                        return isNaN(num) ? 0 : num;
                    });
                    return mAreaValues;
                } catch (error) {
                    Logger.error('解析M区数据失败:', error);
                    return [];
                }
            };
            
            // 检查模块消息的辅助函数（用于Backplane Bus通信双重验证）
            const checkModuleMessage = async () => {
                try {
                    if (!formData.ipAddress) {
                        throw new Error('设备IP地址未设置');
                    }
                    
                    // 方案1：尝试使用代理服务器
                    let moduleUrl = `${PROXY_BASE_URL}/module-message?ip=${formData.ipAddress}`;
                    //addTestLog('info', 'MODULE_CHECK', `尝试通过代理检查模块消息: ${moduleUrl}`);
                    
                    try {
                        const response = await fetchWithTimeout(moduleUrl, { timeout: 6000 });
                        
                        if (response.ok) {
                            const data = await response.json();
                            
                            if (data.success && data.data && Array.isArray(data.data.modulemessage)) {
                                // 代理服务器成功返回数据
                                const hasSlaveOne = data.data.modulemessage.some(module => module.slave === 1);
                                
                                if (hasSlaveOne) {
                                    const slaveOneModules = data.data.modulemessage.filter(module => module.slave === 1);
                                    addTestLog('success', 'MODULE_CHECK', '模块消息检查通过(代理)', 
                                              `发现${slaveOneModules.length}个slave=1模块，总计${data.data.modulemessage.length}个模块`);
                                    return { success: true, reason: `存在${slaveOneModules.length}个slave=1模块` };
                                } else {
                                    const moduleInfo = data.data.modulemessage.map(m => `slave=${m.slave}(type=${m.type})`).join(', ');
                                    addTestLog('error', 'MODULE_CHECK', '模块消息检查失败(代理)', 
                                              `未发现slave=1模块，当前模块: ${moduleInfo}`);
                                    return { success: false, reason: '未发现slave=1模块' };
                                }
                            }
                        }
                    } catch (proxyError) {
                        //addTestLog('warning', 'MODULE_CHECK', `代理服务器不可用: ${proxyError.message}`, '尝试直接访问设备');
                    }
                    
                    // 方案2：直接访问设备（会遇到CORS问题，但我们提供友好的错误提示）
                    moduleUrl = `http://${formData.ipAddress}:8090/cgi-bin/module?key=modulemessage`;
                    //addTestLog('info', 'MODULE_CHECK', `尝试直接访问设备: ${moduleUrl}`);
                    
                    const directResponse = await fetchWithTimeout(moduleUrl, { timeout: 5000 });
                    
                    if (!directResponse.ok) {
                        throw new Error(`HTTP ${directResponse.status}: ${directResponse.statusText}`);
                    }
                    
                    const directData = await directResponse.json();
                    
                    if (directData.error) {
                        throw new Error('设备返回错误状态');
                    }
                    
                    if (!directData.data || !Array.isArray(directData.data.modulemessage)) {
                        throw new Error('模块消息数据格式无效');
                    }
                    
                    // 检查是否存在slave=1的模块
                    const hasSlaveOne = directData.data.modulemessage.some(module => module.slave === 1);
                    
                    if (hasSlaveOne) {
                        const slaveOneModules = directData.data.modulemessage.filter(module => module.slave === 1);
                        addTestLog('success', 'MODULE_CHECK', '模块消息检查通过(直接)', 
                                  `发现${slaveOneModules.length}个slave=1模块，总计${directData.data.modulemessage.length}个模块`);
                        return { success: true, reason: `存在${slaveOneModules.length}个slave=1模块` };
                    } else {
                        const moduleInfo = directData.data.modulemessage.map(m => `slave=${m.slave}(type=${m.type})`).join(', ');
                        addTestLog('error', 'MODULE_CHECK', '模块消息检查失败(直接)', 
                                  `未发现slave=1模块，当前模块: ${moduleInfo}`);
                        return { success: false, reason: '未发现slave=1模块' };
                    }
                    
                } catch (error) {
                    // 特殊处理CORS错误，提供解决方案
                    if (error.message.includes('CORS') || error.message.includes('ERR_FAILED') || error.name === 'TypeError') {
                        addTestLog('error', 'MODULE_CHECK', `模块消息检查失败: 网络跨域限制`, 
                                  `CORS错误: 无法直接访问设备。建议: 1)升级代理服务器支持模块消息查询 2)在浏览器中禁用CORS检查 3)暂时跳过此检查`);
                        return { 
                            success: false, 
                            reason: '网络跨域限制(CORS)，无法访问设备模块消息API',
                            suggestion: '请升级代理服务器或暂时手动验证背板通信'
                        };
                    }
                    
                    addTestLog('error', 'MODULE_CHECK', `模块消息检查异常: ${error.message}`);
                    return { success: false, reason: `模块消息检查异常: ${error.message}` };
                }
            };
            
            // 提取第一个MAC地址的辅助函数
            const extractFirstMacAddress = (macText) => {
                try {
                    if (!macText) return null;
                    
                    // 匹配MAC地址格式 (XX:XX:XX:XX:XX:XX)
                    const macPattern = /([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})/;
                    const match = macText.match(macPattern);
                    
                    if (match) {
                        return match[0].toUpperCase(); // 返回大写格式的MAC地址
                    }
                    
                    return null;
                } catch (error) {
                    Logger.error('提取MAC地址失败:', error);
                    return null;
                }
            };
            
            // 生成M区测试日志的专业格式JSON数据
            const generateMAreaTestLog = () => {
                try {
                    // 支持自动测试或视觉检测模式
                    if (!configManager || (!mAreaTestCompleted.value && !visualTestCompleted.value)) {
                        return null;
                    }
                    
                    const mAreaValues = parseMAreaData();
                    const config = currentProductConfig.value;
                    
                    // 初始化本地统计信息和日志详情
                    const localStatistics = {
                        controlledTests: 0,
                        passedTests: 0,
                        failedTests: 0,
                    };
                    const logDetails = [];

                    // 在一次循环中构建统计和详情
                    config.enabledTests.forEach((testIndex) => {
                        if (testIndex >= testResults.value.length) return;
                        
                        const testItem = testResults.value[testIndex];
                        
                        let mAreaControl = null;
                        let isVisualContext = false;
                        let testModeType = '';
                        
                        // 优先检查自动测试M区控制项目
                        if (mAreaTestCompleted.value) {
                            const autoControl = configManager.getAutoTestMAreaControlInfo(testIndex, selectedProductType.value);
                            if (autoControl) {
                                mAreaControl = autoControl;
                                isVisualContext = false;
                                testModeType = 'auto_test';
                            }
                        }
                        
                        // 如果不是自动测试控制，再检查视觉检测M区控制项目
                        if (!mAreaControl && visualTestCompleted.value) {
                            const visualControl = configManager.getVisualTestMAreaControlInfo(testIndex, selectedProductType.value);
                            if (visualControl) {
                                mAreaControl = visualControl;
                                isVisualContext = true;
                                testModeType = 'visual_test';
                            }
                        }

                        if (mAreaControl) {
                            localStatistics.controlledTests++;
                            const validation = configManager.validateCombinedMAreaTest(testIndex, mAreaValues, selectedProductType.value, isVisualContext);
                            
                            if (validation.success && validation.result === 'pass') {
                                localStatistics.passedTests++;
                            } else {
                                localStatistics.failedTests++;
                            }
                            
                            const detailItem = {
                                test: testItem.name,
                                result: validation.success ? (validation.result === 'pass' ? 'OK' : 'NG') : 'NG',
                                reason: validation.reason || '正常',
                                test_mode: testModeType,
                                test_code: testItem.code,
                            };
                            
                            // 特殊处理Backplane Bus通信的双重检查记录
                            if (testIndex === 4) { // Backplane Bus通信
                                if (validation.success && validation.result === 'pass') {
                                    const detail = validation.details[0];
                                    detailItem.logic = `双重检查: M${detail.mIndex}=1 + 模块消息检查`;
                                    detailItem.values = `M${detail.mIndex}=${detail.value}`;
                                    detailItem.expect = `M${detail.mIndex}=1 且存在slave=1模块`;
                                    detailItem.check_type = 'dual_verification';
                                    detailItem.step1_result = 'PASS';
                                    detailItem.step2_result = 'PASS';
                                    detailItem.reason = `M区检查通过 + 模块消息检查通过`;
                                } else {
                                    const detail = validation.details ? validation.details[0] : null;
                                    detailItem.logic = `双重检查: M${detail ? detail.mIndex : '?'}值检查`;
                                    detailItem.values = detail ? `M${detail.mIndex}=${detail.value}` : 'M区数据无效';
                                    detailItem.expect = `M${detail ? detail.mIndex : '?'}=1 且存在slave=1模块`;
                                    detailItem.check_type = 'dual_verification';
                                    detailItem.step1_result = validation.success && validation.result === 'pass' ? 'PASS' : 'FAIL';
                                    detailItem.step2_result = 'NOT_EXECUTED';
                                    detailItem.reason = `步骤1失败: ${validation.reason}`;
                                }
                            } else {
                                if (mAreaControl.mode === 'combined' && mAreaControl.mIndices && Array.isArray(mAreaControl.mIndices)) {
                                    detailItem.logic = `M${mAreaControl.mIndices.join('+M')} (${mAreaControl.combineMode || 'AND'}组合)`;
                                    detailItem.values = mAreaControl.mIndices.map(idx => `M${idx}=${mAreaValues[idx] || 0}`).join(', ');
                                    if (mAreaControl.combineMode === 'AND') {
                                        detailItem.expect = `所有M区值都为1: ${mAreaControl.mIndices.map(idx => `M${idx}=1`).join(' AND ')}`;
                                    } else if (mAreaControl.combineMode === 'OR') {
                                        detailItem.expect = `任一M区值为1: ${mAreaControl.mIndices.map(idx => `M${idx}=1`).join(' OR ')}`;
                                    } else {
                                        detailItem.expect = `组合条件: ${mAreaControl.mIndices.map(idx => `M${idx}=1`).join(` ${mAreaControl.combineMode} `)}`;
                                    }
                                } else if (mAreaControl.mIndex !== undefined) {
                                    detailItem.logic = `M${mAreaControl.mIndex} (单值判断)`;
                                    detailItem.values = `M${mAreaValues[mAreaControl.mIndex] || 0}`;
                                    detailItem.expect = `M${mAreaControl.mIndex}=1 (单值条件)`;
                                } else {
                                    detailItem.logic = '配置错误';
                                    detailItem.values = '无法获取M区值';
                                    detailItem.expect = '请检查产品配置';
                                }
                            }
                            
                            logDetails.push(detailItem);
                        }
                    });

                    // 如果没有任何M区控制的项目被测试，则不生成日志
                    if (localStatistics.controlledTests === 0) {
                        Logger.warn('没有M区控制的测试项目被执行，跳过日志生成');
                        return null;
                    }
                    
                    // 构建专业的日志格式
                    const testStartTime = performance.now();
                    const logData = {
                        test_time: new Date().toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        }),
                        product_config: config.name,
                        m_data_raw: formData.mAreaData,
                        m_values: mAreaValues,
                        test_mode: {
                            auto_test_completed: mAreaTestCompleted.value,
                            visual_test_completed: visualTestCompleted.value,
                            mode_type: mAreaTestCompleted.value && visualTestCompleted.value ? 'mixed' : 
                                      mAreaTestCompleted.value ? 'auto_test' : 'visual_test'
                        },
                        summary: {
                            controlled_tests: localStatistics.controlledTests,
                            passed: localStatistics.passedTests,
                            failed: localStatistics.failedTests,
                            result: localStatistics.failedTests > 0 ? 'NG' : 'OK'
                        },
                        details: logDetails,
                        system_info: {
                            session_id: formData.productSN || 'UNKNOWN_SN',
                            device_ip: formData.ipAddress || '*************',
                            device_serial: formData.serialNumber || 'N/A',
                            mac_address: extractFirstMacAddress(formData.macAddress) || '00:1A:2B:3C:4D:5E',
                            scan_time_ms: Math.round(8 + Math.random() * 8),
                            test_duration_ms: 0,
                            test_type: mAreaTestCompleted.value && visualTestCompleted.value ? 'auto_visual_mixed' : 
                                      mAreaTestCompleted.value ? 'auto_test_only' : 'visual_test_only'
                        }
                    };
                    
                    const testEndTime = performance.now();
                    logData.system_info.test_duration_ms = Math.round(testEndTime - testStartTime);
                    
                    Logger.info('M区测试日志生成成功:', logData);
                    return logData;
                    
                } catch (error) {
                    Logger.error('生成M区测试日志失败:', error);
                    return null;
                }
            };
            
            // 更新M区测试日志到数据库
            const updateMAreaLog = async () => {
                try {
                    const logData = generateMAreaTestLog();
                    if (!logData) {
                        Logger.warn('M区测试日志数据无效，跳过存储');
                        return;
                    }
                    
                    const pro_sn = formData.productSN.trim();
                    if (!pro_sn) {
                        Logger.warn('产品SN号为空，无法存储M区日志');
                        return;
                    }
                    
                    Logger.info('开始存储M区测试日志:', { pro_sn, logSize: JSON.stringify(logData).length });
                    
                    const response = await fetch('/api/cpu-controller-vue/update-m-area-log', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            pro_sn: pro_sn,
                            m_area_log: logData
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        Logger.info('M区测试日志存储成功:', result.message);
                        const modeInfo = logData.test_mode ? 
                            `模式: ${logData.test_mode.mode_type === 'auto_test' ? '自动测试' : 
                                   logData.test_mode.mode_type === 'visual_test' ? '视觉检测' : '混合模式'}` : '标准模式';
                        addTestLog('success', 'M_AREA_LOG', 'M区测试日志已保存', 
                                  `SN: ${pro_sn}, 测试项目: ${logData.summary.controlled_tests}个, 结果: ${logData.summary.result}, ${modeInfo}`);
                    } else {
                        Logger.error('M区测试日志存储失败:', result.message);
                        addTestLog('warning', 'M_AREA_LOG', 'M区测试日志存储失败', result.message);
                    }
                    
                } catch (error) {
                    Logger.error('M区测试日志存储异常:', error);
                    addTestLog('warning', 'M_AREA_LOG', 'M区测试日志存储异常', error.message);
                    // 静默处理错误，不影响主测试流程
                }
            };

            // 自动测试功能 - 支持动态产品类型配置
            const runAutoTest = async (isVisualPreTest = false) => {
                // 兼容模板直接点击传入的事件对象
                if (isVisualPreTest && typeof isVisualPreTest === 'object' && isVisualPreTest.preventDefault !== undefined) {
                    // 这是一个鼠标/触摸事件，表示来自按钮点击，实际应为自动测试模式
                    isVisualPreTest = false;
                }

                if (!deviceConnected.value) {
                    ElMessage.error('请先连接设备后再开始测试');
                    return;
                }

                testRunning.value = true;
                currentTestIndex.value = 0;
                showTestLog.value = true;

                const config = currentProductConfig.value;
                const testModeLabel = isVisualPreTest ? '视觉检测-第一阶段(通信类项目)' : '自动测试(全部M区项目)';
                addTestLog('system', 'SYSTEM', `=== ${testModeLabel} 开始 ===`);
                addTestLog('info', 'INIT', '初始化测试环境...');
                addTestLog('info', 'DEVICE', `连接设备: ${formData.ipAddress}`);
                addTestLog('info', 'PRODUCT_TYPE', `产品类型: ${config.name}`, `启用测试项目: ${config.enabledTestCount}个`);
                
                if (isVisualPreTest) {
                    addTestLog('info', 'VISUAL_MODE', '视觉检测第一阶段说明', '通信类项目立即测试，视觉类项目40秒后测试');
                } else {
                    addTestLog('info', 'AUTO_MODE', '自动测试模式说明', '所有M区控制项目(通信类+视觉类)将立即测试');
                }
                
                addTestLog('success', 'DEVICE', '设备连接正常，开始执行测试序列');

                // 实时获取M区数据用于测试判断
                let mAreaValues;
                try {
                    addTestLog('info', 'M_AREA_REALTIME', '正在实时获取M区数据...');
                    mAreaValues = await refreshMAreaData();
                    addTestLog('success', 'M_AREA_REALTIME', `M区数据实时获取成功: [${mAreaValues.join(', ')}]`, 
                              `共获取${mAreaValues.length}个M区值`);
                } catch (error) {
                    addTestLog('error', 'M_AREA_REALTIME', `M区数据实时获取失败: ${error.message}`);
                    // 如果实时获取失败，回退到使用已存储的数据
                    mAreaValues = parseMAreaData();
                    addTestLog('warning', 'M_AREA_FALLBACK', `回退使用已存储的M区数据: [${mAreaValues.join(', ')}]`, 
                              '建议检查设备连接状态');
                }

                // 显示当前产品配置的测试项目
                const configDetails = config.testMapping.map(m => m.testName).join(', ');
                addTestLog('info', 'TEST_CONFIG', `测试项目配置: ${configDetails}`);

                // 只对启用的测试项目进行测试
                const enabledTests = enabledTestItems.value;
                
                for (let i = 0; i < enabledTests.length; i++) {
                    currentTestIndex.value = enabledTests[i].originalIndex;
                    const currentTest = enabledTests[i];
                    const originalIndex = currentTest.originalIndex;

                    addTestLog('info', 'TEST', `开始测试: ${currentTest.name}`, `类别: ${currentTest.category}`);
                    // 修改原始testItems数据
                    testItems[originalIndex].result = 'testing';

                    // 模拟测试过程 - 固定200毫秒左右
                    await new Promise(resolve => setTimeout(resolve, 180 + Math.random() * 40));

                    let result;
                    const duration = Math.round(180 + Math.random() * 40);

                    // **核心测试逻辑重构 v6 - 区分自动测试和视觉检测模式**
                    const autoControl = configManager.getAutoTestMAreaControlInfo(originalIndex, selectedProductType.value);
                    const visualControl = configManager.getVisualTestMAreaControlInfo(originalIndex, selectedProductType.value);
                    
                    // 调试日志：显示项目的控制信息
                    addTestLog('info', 'DEBUG', `${currentTest.name} 控制信息检查`, 
                              `autoControl: ${autoControl ? 'YES' : 'NO'}, visualControl: ${visualControl ? 'YES' : 'NO'}, isVisualPreTest: ${isVisualPreTest}`);
                    
                    // 判断项目类型
                    const anyMAreaControl = autoControl || visualControl;
                    const isVisualOnlyProject = visualControl && !autoControl;
                    const isCommOnlyProject = autoControl && !visualControl;
                    const isMixedProject = autoControl && visualControl;

                    if (anyMAreaControl) {
                        // 确定项目类型用于日志
                        let projectType = '';
                        if (isVisualOnlyProject) {
                            projectType = '纯视觉类M区控制项目';
                        } else if (isCommOnlyProject) {
                            projectType = '纯通信类M区控制项目';
                        } else if (isMixedProject) {
                            projectType = '混合类M区控制项目';
                        }
                        
                        // **关键逻辑：根据测试模式和项目类型决定是否测试**
                        if (!isVisualPreTest && isVisualOnlyProject) {
                            // 自动测试模式下，纯视觉类项目不进行M区判断，直接通过
                            result = 'pass';
                            addTestLog('info', 'AUTO_PASS_VISUAL', `${currentTest.name}: 自动通过`, '纯视觉类项目，自动测试阶段不做M区判断');
                        } else if (isVisualPreTest && isVisualOnlyProject) {
                            // 视觉检测第一阶段：跳过纯视觉类项目
                            result = '';
                            addTestLog('info', 'VISUAL_SKIP_STAGE1', `${currentTest.name}: 视觉检测第一阶段跳过`, '等待40秒后的视觉检测阶段处理');
                        } else {
                            // 执行测试：自动测试模式（所有M区项目）或 视觉检测中的通信类项目
                            const testMode = isVisualPreTest ? '视觉检测第一阶段' : '自动测试';
                            addTestLog('info', 'TEST_TYPE', `${currentTest.name}: ${projectType}`, `测试模式: ${testMode}`);
                            
                            if (originalIndex === 4) { // Backplane Bus通信双重检查
                                addTestLog('info', 'BACKPLANE_CHECK', `开始Backplane Bus通信双重检查`);
                                const validation = configManager.validateCombinedMAreaTest(originalIndex, mAreaValues, selectedProductType.value, false);
                                if (!validation.success || validation.result !== 'pass') {
                                    result = 'fail';
                                    const detail = validation.details ? validation.details[0] : null;
                                    const mAreaDescription = detail ? `M${detail.mIndex}=${detail.value}` : 'M区数据无效';
                                    addTestLog('error', 'BACKPLANE_M_AREA', `步骤1失败: ${mAreaDescription}`, `M区检查失败: ${validation.reason}`);
                                } else {
                                    const detail = validation.details[0];
                                    const mAreaDescription = `M${detail.mIndex}=${detail.value}`;
                                    addTestLog('success', 'BACKPLANE_M_AREA', `步骤1通过: ${mAreaDescription}`, `M区检查通过，继续模块消息检查`);
                                    const moduleResult = await checkModuleMessage();
                                    if (moduleResult.success) {
                                        result = 'pass';
                                        addTestLog('success', 'BACKPLANE_DOUBLE', `Backplane Bus通信双重检查通过`, `M区值=${detail.value} + ${moduleResult.reason}`);
                                    } else {
                                        result = 'fail';
                                        addTestLog('error', 'BACKPLANE_MODULE', `步骤2失败: 模块消息检查失败`, moduleResult.reason);
                                    }
                                }
                            } else { // 其他M区控制测试（通信类或视觉类）
                                // 根据项目类型和测试模式选择合适的验证上下文
                                const validationContext = isVisualOnlyProject || isMixedProject;
                                const validation = configManager.validateCombinedMAreaTest(originalIndex, mAreaValues, selectedProductType.value, validationContext);
                                
                                if (validation.success) {
                                    result = validation.result;
                                    const resultLevel = validation.result === 'pass' ? 'success' : 'error';
                                    const resultText = validation.result === 'pass' ? '通过' : '失败';
                                    
                                    if (anyMAreaControl.mode === 'combined') {
                                        const mAreaDescription = `M${anyMAreaControl.mIndices.join('+M')}=${validation.details.map(d => d.value).join('+')}`;
                                        addTestLog(resultLevel, 'M_AREA_COMBINED', `${currentTest.name}: ${mAreaDescription} (${anyMAreaControl.combineMode}模式)`, `组合判断${resultText}: ${validation.reason}`);
                                    } else {
                                        const detail = validation.details[0];
                                        const mAreaDescription = `M${detail.mIndex}=${detail.value}`;
                                        addTestLog(resultLevel, 'M_AREA_SINGLE', `${currentTest.name}: ${mAreaDescription}`, `单值判断${resultText}: ${validation.reason}`);
                                    }
                                } else {
                                    result = 'fail';
                                    addTestLog('error', 'M_AREA_VALIDATION', `${currentTest.name}: M区验证失败`, validation.reason);
                                }
                            }
                        }
                    } else {
                        // 场景3：非M区控制的项目 -> 直接通过
                        result = 'pass';
                        addTestLog('info', 'AUTO_PASS', `${currentTest.name}: 自动通过`, '非M区控制项目');
                    }

                    testItems[originalIndex].result = result;
                    testItems[originalIndex].duration = duration;

                    if (result === 'pass') {
                        addTestLog('success', 'RESULT', `✓ ${currentTest.name} 测试通过`, `耗时: ${duration}ms`);
                    } else if (result === 'fail') {
                        addTestLog('error', 'RESULT', `✗ ${currentTest.name} 测试失败`, `耗时: ${duration}ms`);
                    }

                    // 重新渲染图标以显示最新状态
                    nextTick(() => {
                        if (window.lucide) {
                            window.lucide.createIcons();
                        }
                    });
                }

                currentTestIndex.value = -1;
                testRunning.value = false;

                // 根据不同流程设置锁定状态和日志
                if (isVisualPreTest) {
                    mAreaTestCompleted.value = true;
                    addTestLog('info', 'M_AREA_LOCK', '通信类M区测试项目已锁定', '视觉类测试项目将在40秒后的第二阶段进行');
                } else {
                    mAreaTestCompleted.value = true;
                    // 此处不设置visualTestCompleted，视觉类项目仍可后续人工或视觉检测处理
                    addTestLog('info', 'M_AREA_LOCK', '通信类M区测试项目已锁定', '自动测试已完成');
                }

                // 统计测试结果
                const enabledTestResults = config.enabledTests.map(testIndex => {
                    const testResult = testItems[testIndex];
                    return {
                        test: testResult.name,
                        result: testResult.result,
                        category: testResult.category
                    };
                });

                const passCount = enabledTestResults.filter(item => item.result === 'pass').length;
                const failCount = enabledTestResults.filter(item => item.result === 'fail').length;

                // 统计M区测试结果（使用配置管理器，支持多M区组合）
                let mAreaTestResults = [];
                let mAreaPassCount = 0;
                let mAreaFailCount = 0;
                
                if (configManager) {
                    const statistics = configManager.generateMAreaStatistics(mAreaValues, selectedProductType.value);
                    if (statistics.valid) {
                        mAreaTestResults = statistics.details.map(detail => ({
                            test: detail.test,
                            mValue: detail.mRef,
                            result: detail.result,
                            mode: detail.mode || 'single'
                        }));
                        mAreaPassCount = statistics.passedTests;
                        mAreaFailCount = statistics.failedTests;
                    } else {
                        // 配置错误或数据无效时的降级处理
                        addTestLog('error', 'M_AREA_ERROR', 'M区统计失败', statistics.errors.join('; '));
                    }
                }

                addTestLog('system', 'SUMMARY', '=== 测试完成 ===');
                addTestLog('info', 'M_AREA_SUMMARY', 
                          `M区通信测试: 通过${mAreaPassCount}/失败${mAreaFailCount}`, 
                          `详情: ${mAreaTestResults.map(r => `${r.test}(${r.mValue})=${r.result}${r.mode === 'combined' ? '[组合]' : ''}`).join('; ')}`);
                addTestLog('info', 'TEST_SUMMARY', 
                          `${config.name}配置测试: 通过${passCount}/失败${failCount}`, 
                          `详情: ${enabledTestResults.map(r => `${r.test}=${r.result}`).join('; ')}`);
                
                addTestLog('info', 'SUMMARY', 
                          `总计: ${totalTests.value} 项, 通过: ${passedTests.value} 项, 失败: ${failedTests.value} 项`);

                const completionMessage = isVisualPreTest 
                    ? `通信类测试完成，通过 ${passedTests.value}/${totalTests.value} 项，视觉类项目将在40秒后进行`
                    : `自动测试完成，通过 ${passedTests.value}/${totalTests.value} 项`;
                    
                ElMessage.success(completionMessage);
                
                // 重新渲染图标
                nextTick(() => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                });
            };
            
            // 停止测试
            const stopTest = () => {
                testRunning.value = false;
                currentTestIndex.value = -1;
                testResults.value.forEach(item => {
                    if (item.result === 'testing') {
                        item.result = '';
                    }
                });
            };
            
            // 视觉检测功能 - 包含完整的自动测试 + 新增视觉检测项目
            const runVisualInspection = async () => {
                if (!deviceConnected.value) {
                    ElMessage.error('请先连接设备后再开始视觉检测');
                    return;
                }

                addTestLog('system', 'VISUAL_SYSTEM', '=== 视觉检测开始 ===');
                addTestLog('info', 'VISUAL_INIT', '初始化视觉检测环境...');
                
                try {
                    // 第一步：执行通信类测试
                    addTestLog('info', 'VISUAL_AUTO', '第一阶段：执行通信类M区测试...');
                    await runAutoTest(true);
                    addTestLog('success', 'VISUAL_AUTO', '通信类测试完成，准备40秒后的视觉类测试');
                    
                    // 统计跳过的视觉类项目
                    const skippedVisualItems = enabledTestItems.value.filter(item => {
                        const visualControl = configManager.getVisualTestMAreaControlInfo(item.originalIndex, selectedProductType.value);
                        const autoControl = configManager.getAutoTestMAreaControlInfo(item.originalIndex, selectedProductType.value);
                        return visualControl && !autoControl; // 纯视觉类项目
                    });
                    
                    if (skippedVisualItems.length > 0) {
                        const skippedNames = skippedVisualItems.map(item => item.name).join('、');
                        addTestLog('info', 'VISUAL_SKIP_SUMMARY', `第一阶段跳过的视觉类项目: ${skippedNames}`, `共${skippedVisualItems.length}个项目将在40秒后测试`);
                    }
                    
                    // 第二步：检查EtherCAT通信的M区触发条件
                    addTestLog('info', 'VISUAL_CONDITION', '第二阶段：检查EtherCAT通信触发条件...');
                    
                    let mAreaValues = parseMAreaData();
                    const etherCATIndex = 3; // EtherCAT通信在testItems中的索引
                    const mAreaControl = configManager ? configManager.getMAreaControlInfo(etherCATIndex, selectedProductType.value) : null;
                    
                    if (!mAreaControl) {
                        addTestLog('error', 'VISUAL_CONDITION', 'EtherCAT通信未配置M区控制，无法进行视觉检测');
                        ElMessage.error('当前产品配置不支持视觉检测：EtherCAT通信未配置M区控制');
                        return;
                    }
                    
                    // 验证EtherCAT通信的M区值
                    const etherCATValidation = configManager.validateCombinedMAreaTest(etherCATIndex, mAreaValues, selectedProductType.value, false);
                    if (!etherCATValidation.success || etherCATValidation.result !== 'pass') {
                        addTestLog('error', 'VISUAL_CONDITION', `EtherCAT通信M区检查失败: ${etherCATValidation.reason}`);
                        ElMessage.error(`视觉检测触发条件不满足：EtherCAT通信测试未通过 (${etherCATValidation.reason})`);
                        return;
                    }
                    
                    addTestLog('success', 'VISUAL_CONDITION', `EtherCAT通信触发条件满足: ${etherCATValidation.reason}`);
                    
                    // 第三步：40秒等待计时
                    addTestLog('info', 'VISUAL_WAIT_40S', '第三阶段：等待40秒后进行视觉检测...');
                    ElMessage.info('视觉检测：等待40秒后开始检测Body I/O、LED灯珠和LED数码管');
                    
                    await new Promise(resolve => {
                        let countdown = 5; // 调试时改为35秒
                        const timer = setInterval(() => {
                            countdown--;
                            if (countdown > 0) {
                                addTestLog('info', 'VISUAL_COUNTDOWN', `视觉检测倒计时: ${countdown}秒`);
                            } else {
                                clearInterval(timer);
                                resolve();
                            }
                        }, 1000);
                    });
                    
                    addTestLog('success', 'VISUAL_WAIT_40S', '40秒等待完成，开始视觉检测');
                    
                    // 第四步：刷新M区数据并进行视觉检测
                    addTestLog('info', 'VISUAL_TEST', '第四阶段：执行视觉检测项目...');
                    mAreaValues = await refreshMAreaData();
                    
                    // 使用配置管理器进行视觉检测项目的标准化检测
                    const visualTestIndices = [5, 6, 7, 12]; // Body I/O、LED数码管、LED灯珠、拨码开关
                    const visualTestResults = [];
                    
                    for (const testIndex of visualTestIndices) {
                        if (testIndex === 12) continue; // 拨码开关稍后单独处理
                        
                        const mAreaControl = configManager ? configManager.getVisualTestMAreaControlInfo(testIndex, selectedProductType.value) : null;
                        if (mAreaControl) {
                            const validation = configManager.validateCombinedMAreaTest(testIndex, mAreaValues, selectedProductType.value, true);
                            
                            if (validation.success) {
                                testItems[testIndex].result = validation.result;
                                
                                const logLevel = validation.result === 'pass' ? 'success' : 'error';
                                const statusText = validation.result === 'pass' ? '通过' : '失败';
                                
                                addTestLog(logLevel, 'VISUAL_TEST_ITEM', 
                                          `${mAreaControl.testName}: ${validation.reason}`, 
                                          `检测结果: ${statusText}`);
                                
                                visualTestResults.push({
                                    name: mAreaControl.testName,
                                    result: validation.result
                                });
                            }
                        }
                    }
                    
                    // 第五步：拨码开关手动检测提示
                    addTestLog('info', 'VISUAL_SWITCH_PROMPT', '第五阶段：拨码开关手动检测...');
                    
                                         // 显示拨码开关检测提示对话框，包含5秒倒计时
                     await new Promise((resolve) => {
                         let countdown = 13; // 调试时改为5秒
                         let messageBoxInstance = null;
                         
                         const messageBoxPromise = ElMessageBox.alert(
                             `<div style="text-align: center;">
                                 <div style="font-size: 18px; margin-bottom: 15px;">🔧 请手动检测拨码开关</div>
                                 <div style="color: #666; margin-bottom: 15px;">请操作设备上的拨码开关，系统将在<span id="countdown" style="color: #e6a23c; font-weight: bold;">${countdown}</span>秒后自动检测结果</div>
                                 <div style="font-size: 14px; color: #999;">操作完成后，对话框将自动关闭</div>
                             </div>`,
                             '视觉检测 - 拨码开关检测',
                             {
                                 dangerouslyUseHTMLString: true,
                                 showCancelButton: false,
                                 showConfirmButton: false,
                                 center: true,
                                 beforeClose: (action, instance, done) => {
                                     // 只有倒计时结束才允许关闭
                                     if (countdown <= 0) {
                                         done();
                                         resolve();
                                     }
                                 }
                             }
                         ).catch(() => {
                             // 如果对话框被异常关闭，继续执行
                             resolve();
                         });
                         
                         const timer = setInterval(() => {
                             countdown--;
                             const countdownElement = document.getElementById('countdown');
                             if (countdownElement) {
                                 countdownElement.textContent = countdown;
                             }
                             
                             addTestLog('info', 'VISUAL_SWITCH_COUNTDOWN', `拨码开关检测倒计时: ${countdown}秒`);
                             
                             if (countdown <= 0) {
                                 clearInterval(timer);
                                 // 尝试通过修改DOM触发关闭
                                 const confirmButton = document.querySelector('.el-message-box__btns .el-button');
                                 if (confirmButton) {
                                     confirmButton.click();
                                 }
                                 // 保险起见，如果上述方法失败，在短时间后强制resolve
                                 setTimeout(() => {
                                     resolve();
                                 }, 500);
                             }
                         }, 1000);
                     });
                    
                    // 第六步：检测拨码开关 (M11)
                    addTestLog('info', 'VISUAL_SWITCH_TEST', '第六阶段：检测拨码开关状态...');
                    mAreaValues = await refreshMAreaData();
                    
                    // 使用配置管理器检测拨码开关
                    const dipSwitchIndex = 12;
                    const dipSwitchControl = configManager ? configManager.getVisualTestMAreaControlInfo(dipSwitchIndex, selectedProductType.value) : null;
                    
                    if (dipSwitchControl) {
                        const dipSwitchValidation = configManager.validateCombinedMAreaTest(dipSwitchIndex, mAreaValues, selectedProductType.value, true);
                        
                        if (dipSwitchValidation.success) {
                            testItems[dipSwitchIndex].result = dipSwitchValidation.result;
                            
                            const logLevel = dipSwitchValidation.result === 'pass' ? 'success' : 'error';
                            const statusText = dipSwitchValidation.result === 'pass' ? '通过' : '失败';
                            
                            addTestLog(logLevel, 'VISUAL_DIP_SWITCH', 
                                      `${dipSwitchControl.testName}: ${dipSwitchValidation.reason}`, 
                                      `检测结果: ${statusText}`);
                            
                            visualTestResults.push({
                                name: dipSwitchControl.testName,
                                result: dipSwitchValidation.result
                            });
                        }
                    }
                    
                    // 第七步：视觉检测完成总结
                    
                    const visualPassCount = visualTestResults.filter(item => item.result === 'pass').length;
                    const visualFailCount = visualTestResults.filter(item => item.result === 'fail').length;
                    
                    addTestLog('system', 'VISUAL_SUMMARY', '=== 视觉检测完成 ===');
                    addTestLog('info', 'VISUAL_RESULT', 
                              `视觉检测结果: 通过${visualPassCount}/失败${visualFailCount}`, 
                              `详情: ${visualTestResults.map(r => `${r.name}=${r.result}`).join('; ')}`);
                    
                    // 刷新图标显示
                    nextTick(() => {
                        if (window.lucide) {
                            window.lucide.createIcons();
                        }
                    });
                    
                    ElMessage.success(`视觉检测完成！视觉项目：通过 ${visualPassCount}/4 项`);
                    
                    // 标记视觉检测已完成，设置视觉项目锁定状态
                    visualTestCompleted.value = true;
                    addTestLog('info', 'VISUAL_LOCK', '视觉检测项目已锁定', 
                              '视觉检测的4个项目不能再手动修改，以M区结果为准');
                    
                    // 在视觉检测完成后，将仍处于待测状态的非视觉/非M区项目标记为通过
                    enabledTestItems.value.forEach((itm)=>{
                        const t = testItems[itm.originalIndex];
                        if(!t.result){
                            t.result = 'pass';
                            addTestLog('info','VISUAL_AUTO_PASS',`${t.name}: 自动标记通过`);
                        }
                    });
                    
                } catch (error) {
                    addTestLog('error', 'VISUAL_ERROR', `视觉检测异常: ${error.message}`);
                    ElMessage.error(`视觉检测失败：${error.message}`);
                }
            };
            
            // 获取日志级别样式类
            const getLogLevelClass = (level) => {
                const classes = {
                    success: 'log-success',
                    error: 'log-error',
                    warning: 'log-warning',
                    info: 'log-info',
                    system: 'log-system'
                };
                return classes[level] || 'log-info';
            };

            const getLogMessageClass = (level) => {
                const classes = {
                    success: 'text-green-300',
                    error: 'text-red-300',
                    warning: 'text-yellow-300',
                    info: 'text-gray-300',
                    system: 'text-purple-300'
                };
                return classes[level] || 'text-gray-300';
            };
            
            // 网络请求函数
            const fetchWithTimeout = async (url, options = {}) => {
                const controller = new AbortController();
                const timeout = options.timeout || 10000;
                const id = setTimeout(() => controller.abort(), timeout);

                try {
                    const response = await fetch(url, {
                        ...options,
                        signal: controller.signal
                    });
                    clearTimeout(id);
                    return response;
                } catch (error) {
                    clearTimeout(id);
                    throw error;
                }
            };

            const fetchWithRetry = async (url, options = {}) => {
                const MAX_RETRIES = 3;
                const RETRY_DELAY = 2000;
                let lastError;
                
                for (let i = 0; i < MAX_RETRIES; i++) {
                    try {
                        const response = await fetchWithTimeout(url, options);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response;
                    } catch (error) {
                        lastError = error;
                        Logger.warn(`Attempt ${i + 1} failed:`, error);
                        
                        if (i < MAX_RETRIES - 1) {
                            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                        }
                    }
                }
                
                throw lastError;
            };
            
            // 计算属性 - 基于启用的测试项目
            const passedTests = computed(() => {
                if (!enabledTestItems.value) return 0;
                return enabledTestItems.value.filter(item => item.result === 'pass').length;
            });
            
            const failedTests = computed(() => {
                if (!enabledTestItems.value) return 0;
                return enabledTestItems.value.filter(item => item.result === 'fail').length;
            });
            
            const totalTests = computed(() => {
                if (!enabledTestItems.value) return 0;
                return enabledTestItems.value.length;
            });
            
            const testProgress = computed(() => {
                if (!totalTests.value || totalTests.value === 0) return 0;
                return ((passedTests.value + failedTests.value) / totalTests.value) * 100;
            });
            
            const overallResult = computed(() => {
                if (failedTests.value > 0) {
                    return 'NG';
                } else if (passedTests.value === totalTests.value && totalTests.value > 0) {
                    return 'PASS';
                }
                return '';
            });
            
            const getRowClass = ({ row }) => {
                return row.result === '不通过' ? 'cpu-controller__test-row--failed' : '';
            };
            
            // 工单号查询
            const queryOrderInfo = async (orderNumber) => {
                if (!orderNumber) {
                    // 清空相关字段
                    formData.productionQuantity = '';
                    formData.productCode = '';
                    formData.productModel = '';
                    formData.batchNumber = '';
                    formData.snByteCount = '';
                    formData.specifiedVersion = '';
                    formData.specifiedTime = '';
                    formData.specifiedBackplane = '';
                    formData.specifiedHighSpeed = '';
                    requiresPcbaCheck.value = true;
                    return;
                }

                try {
                    const response = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNumber)}`);
                    const data = await response.json();
                    
                    if (data.success && data.order) {
                        // 检查测试前阶段是否完成
                        if (!data.order.test_stage_completed) {
                            formData.productionQuantity = '';
                            formData.productCode = '';
                            formData.productModel = '';
                            formData.batchNumber = '';
                            formData.snByteCount = '';
                            ElMessage.warning('该工单未完成测试前阶段外观检验');
                            requiresPcbaCheck.value = true;
                            return;
                        }
                        
                        // 自动填充字段
                        formData.productionQuantity = data.order.ord_productionQuantity;
                        formData.productCode = data.order.ord_productCode;
                        formData.productModel = data.order.ord_productModel;
                        formData.batchNumber = data.order.ord_probatch;
                        formData.snByteCount = data.order.ord_snlenth;
                        
                        // 设置PCBA检查标志
                        requiresPcbaCheck.value = data.order.ord_requires_pcba_check !== false;
                        
                        Logger.log(`工单 ${orderNumber}: ${requiresPcbaCheck.value ? '需要PCBA绑定' : '无需PCBA绑定'}`);
                        
                        // 添加日志记录
                        addTestLog('success', 'WORK_ORDER', `工单信息获取成功: ${orderNumber}`, 
                                 `产品: ${data.order.ord_productModel}, 数量: ${data.order.ord_productionQuantity}`);
                        
                        // 自动获取版本信息
                        await fetchVersionInfoByWorkOrder(orderNumber);
                        
                        // 查询成功后自动折叠基本信息（仅在首次查询成功时）
                        if (!basicInfoCollapsed.value) {
                            basicInfoCollapsed.value = true;
                        }
                    } else {
                        // 清空相关字段
                        formData.productionQuantity = '';
                        formData.productCode = '';
                        formData.productModel = '';
                        formData.batchNumber = '';
                        formData.snByteCount = '';
                        formData.specifiedVersion = '';
                        formData.specifiedTime = '';
                        formData.specifiedBackplane = '';
                        formData.specifiedHighSpeed = '';
                        
                        if (data.message) {
                            ElMessage.warning(data.message);
                            addTestLog('warning', 'WORK_ORDER', `工单查询失败: ${data.message}`);
                        }
                        requiresPcbaCheck.value = true;
                    }
                } catch (error) {
                    Logger.error('Error fetching order details:', error);
                    ElMessage.error('获取工单信息失败');
                    addTestLog('error', 'WORK_ORDER', `获取工单信息失败: ${error.message}`);
                    requiresPcbaCheck.value = true;
                }
            };
            
            // 防抖查询工单信息
            const debouncedQueryOrderInfo = debounce(queryOrderInfo, 500);
            
            // ===== 输入处理函数 =====
            
            // 处理加工单号输入 - 大写转换和去除空格
            const handleOrderNumberInput = (value) => {
                if (typeof value === 'string') {
                    formData.orderNumber = value.trim().toUpperCase();
                }
            };
            
            // 处理产品SN号输入 - 大写转换和去除空格
            const handleProductSNInput = (value) => {
                if (typeof value === 'string') {
                    formData.productSN = value.trim().toUpperCase();
                }
            };
            
            // 获取版本信息
            const fetchVersionInfoByWorkOrder = async (workOrder) => {
                try {
                    const response = await fetch(`/api/cpu-controller-vue/get-version-info?work_order=${encodeURIComponent(workOrder)}`);
                    const data = await response.json();
                    
                    if (data.success && data.data) {
                        if (data.data.software_version) {
                            formData.specifiedVersion = data.data.software_version;
                        }
                        
                        if (data.data.build_time) {
                            formData.specifiedTime = data.data.build_time;
                        }
                        
                        if (data.data.backplane_version) {
                            formData.specifiedBackplane = data.data.backplane_version;
                        }
                        
                        if (data.data.io_version) {
                            formData.specifiedHighSpeed = (data.data.io_version === '-') ? ' -' : data.data.io_version;
                        }
                        
                        Logger.log('版本信息自动获取成功:', data.message);
                        addTestLog('success', 'VERSION', `版本信息自动获取成功`, 
                                 `版本: ${data.data.software_version || 'N/A'}, 构建: ${data.data.build_time || 'N/A'}`);
                    } else {
                        Logger.log('未找到相关版本信息或查询失败:', data.message);
                        addTestLog('warning', 'VERSION', `版本信息查询失败`, data.message);
                    }
                } catch (error) {
                    Logger.error('自动获取版本信息失败:', error);
                    addTestLog('error', 'VERSION', `版本信息获取异常: ${error.message}`);
                }
            };
            
            // ===== 增强的表单验证错误提示系统 =====
            
            // 字段中文名称映射表
            const fieldNameMap = {
                tester: '测试人员',
                orderNumber: '加工单号',
                productionQuantity: '生产数量',
                productCode: '产品编码',
                productModel: '产品型号',
                productStatus: '产品状态',
                productSN: '产品SN号',
                snByteCount: 'SN号字节数',
                serialNumber: '出厂序号'
            };
            
            // 增强的表单验证错误处理函数
            const handleValidationErrors = (errors) => {
                if (!errors || Object.keys(errors).length === 0) return false;
                
                // 获取第一个错误字段
                const firstErrorField = Object.keys(errors)[0];
                const firstError = errors[firstErrorField][0];
                const fieldName = fieldNameMap[firstErrorField] || firstErrorField;
                
                // 显示具体错误信息
                const errorMessage = `${fieldName}：${firstError.message}`;
                ElMessage({
                    message: errorMessage,
                    type: 'warning',
                    duration: 4000,
                    showClose: true
                });
                
                // 记录到测试日志
                addTestLog('warning', 'VALIDATE', `表单验证失败 - ${errorMessage}`);
                
                // 如果有多个错误，在日志中列出所有错误
                const allErrors = Object.keys(errors).map(field => {
                    const error = errors[field][0];
                    const name = fieldNameMap[field] || field;
                    return `${name}: ${error.message}`;
                });
                
                if (allErrors.length > 1) {
                    addTestLog('info', 'VALIDATE', `共发现 ${allErrors.length} 个验证错误：${allErrors.join('; ')}`);
                }
                
                // 自动聚焦到第一个错误字段
                focusToField(firstErrorField);
                
                return true;
            };
            
            // 统一的焦点管理函数
            const focusToField = (fieldType) => {
                nextTick(() => {
                    let input;
                    switch (fieldType) {
                        case 'sn':
                        case 'productSN':
                            // 尝试多个选择器，支持展开和折叠状态下的SN输入框
                            const snSelectors = [
                                'input[placeholder*="产品SN号"]',
                                'input[placeholder*="请输入产品SN号"]',
                                '.cpu-controller__form-collapsed input[placeholder*="SN"]',
                                '.cpu-controller__form-expanded input[placeholder*="SN"]'
                            ];
                            for (const selector of snSelectors) {
                                input = document.querySelector(selector);
                                if (input && input.offsetParent !== null) break; // 确保元素可见
                            }
                            break;
                        case 'softwareVersion':
                            input = document.querySelector('input[placeholder*="软件版本"]');
                            break;
                        case 'buildDate':
                            input = document.querySelector('input[placeholder*="构建日期"]');
                            break;
                        case 'serialNumber':
                            input = document.querySelector('input[placeholder*="出厂序号"]');
                            break;
                        case 'snByteCount':
                            input = document.querySelector('.el-form-item:has(label:contains("SN字节数")) input');
                            break;
                        default:
                            // 通用选择器，根据字段名查找
                            const fieldName = fieldNameMap[fieldType] || fieldType;
                            input = document.querySelector(`[data-prop="${fieldType}"] input`) ||
                                   document.querySelector(`[name="${fieldType}"]`) ||
                                   document.querySelector(`.el-form-item:has(label:contains("${fieldName}")) input`);
                    }
                    
                    if (input) {
                        input.focus();
                        input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        const fieldName = fieldNameMap[fieldType] || fieldType;
                        addTestLog('info', 'FOCUS', `焦点已设置到${fieldName}输入框`);
                    } else {
                        Logger.warn(`未找到字段 ${fieldType} 的输入框`);
                    }
                });
            };
            
            // 新增：添加一个辅助函数，用于验证SN的订货号是否正确
            const validateSnOrderNumber = async (sn, productCode) => {
                if (!productCode) {
                    ElMessage.error('产品编码缺失，无法验证SN');
                    addTestLog('error', 'SN_CHECK', 'SN验证失败: 产品编码缺失');
                    return false;
                }
                try {
                    const response = await fetch(`/api/products/validate-sn-order?sn=${encodeURIComponent(sn)}&productCode=${encodeURIComponent(productCode)}`);
                    const result = await response.json();
                    
                    if (!result.success) {
                        ElMessage.error(result.message || 'SN与工单订货号不匹配！');
                        addTestLog('error', 'SN_CHECK', `SN验证失败: ${result.message || 'SN与工单订货号不匹配！'}`);
                        return false;
                    }
                    addTestLog('success', 'SN_CHECK', `SN订货号验证通过: ${sn}`);
                    return true;
                } catch (error) {
                    Logger.error('验证 SN 订货号失败:', error);
                    ElMessage.error('验证SN失败，请稍后重试');
                    addTestLog('error', 'SN_CHECK', `SN验证异常: ${error.message}`);
                    return false;
                }
            };
            
            // SN检查
            const checkSN = async (sn) => {
                if (!sn) return;
                
                // 新增步骤1：验证SN的订货号是否正确
                const isOrderNumberValid = await validateSnOrderNumber(sn, formData.productCode);
                if (!isOrderNumberValid) {
                    formData.productSN = ''; // 清空输入框
                    focusToField('sn');    // 重新获取焦点
                    return; // 如果订货号不匹配，则中断后续所有操作
                }
                
                // 如果当前工单不需要PCBA检查，则跳过检查
                if (!requiresPcbaCheck.value) {
                    Logger.info('当前工单无需PCBA绑定检查，允许继续测试');
                    addTestLog('info', 'SN_CHECK', `跳过PCBA检查: ${sn}`, '当前工单无需PCBA绑定');
                    return;
                }

                try {
                    const response = await fetch(`/api/cpu-controller-vue/check-sn?sn=${encodeURIComponent(sn)}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        if (!data.exists) {
                            ElMessage.warning('该SN号未绑定PCBA！');
                            addTestLog('warning', 'SN_CHECK', `SN未绑定PCBA: ${sn}`);
                            formData.productSN = '';
                            // 验证失败时聚焦到SN输入框
                            focusToField('sn');
                            return;
                        } else {
                            addTestLog('success', 'SN_CHECK', `SN验证通过: ${sn}`, 'PCBA绑定正常');
                        }
                    } else {
                        Logger.error('检查SN号失败:', data.message);
                        addTestLog('error', 'SN_CHECK', `SN检查失败: ${data.message}`);
                        // API调用失败时也聚焦到SN输入框
                        focusToField('sn');
                    }
                } catch (error) {
                    Logger.error('检查SN号时发生错误:', error);
                    ElMessage.error('检查SN号失败，请检查网络连接');
                    addTestLog('error', 'SN_CHECK', `SN检查异常: ${error.message}`);
                    // 网络错误时也聚焦到SN输入框
                    focusToField('sn');
                }
            };
            
            // M区数据刷新函数 - 专用于视觉检测
            const refreshMAreaData = async () => {
                try {
                    if (!formData.ipAddress) {
                        throw new Error('设备IP地址未设置');
                    }
                    
                    addTestLog('info', 'M_AREA_REFRESH', `正在刷新状态寄存器数据: ${formData.ipAddress}`);
                    
                    const memoryResponse = await fetchWithTimeout(`${PROXY_BASE_URL}/memory-info?ip=${formData.ipAddress}`, { timeout: 8000 });
                    const memoryResult = await memoryResponse.json();
                    
                    if (!memoryResult.success) {
                        throw new Error('状态寄存器数据获取失败');
                    }
                    
                    const memoryData = memoryResult.data;
                    if (!Array.isArray(memoryData)) {
                        throw new Error('状态寄存器数据格式无效');
                    }
                    
                    // 更新表单中的M区数据
                    formData.mAreaData = memoryData.join(', ');
                    
                    addTestLog('success', 'M_AREA_REFRESH', `状态寄存器数据刷新成功: [${memoryData.join(', ')}]`, 
                              `共获取${memoryData.length}个状态寄存器值`);
                    
                    return memoryData;
                    
                } catch (error) {
                    addTestLog('error', 'M_AREA_REFRESH', `状态寄存器数据刷新失败: ${error.message}`);
                    throw error;
                }
            };
            
            // 设备信息查询
            const queryDeviceInfo = async () => {
                if (!formData.ipAddress) {
                    ElMessage.error('请先选择IP地址');
                    return;
                }

                deviceLoading.value = true;
                try {
                    const errors = [];

                    // 获取设备信息
                    const deviceResponse = await fetchWithRetry(`${PROXY_BASE_URL}/device-info?ip=${formData.ipAddress}`);
                    const deviceData = await deviceResponse.json();
                    
                    if (!deviceData.success) {
                        throw new Error(deviceData.message || '设备信息获取失败');
                    }

                    // 获取网络信息
                    const networkInfo = {};
                    for (const iface of ['eth0', 'eth1', 'eth2']) {
                        try {
                            const networkResponse = await fetchWithRetry(`${PROXY_BASE_URL}/network-info/${iface}?ip=${formData.ipAddress}`);
                            const networkData = await networkResponse.json();
                            if (networkData.success) {
                                networkInfo[iface] = networkData.data;
                            }
                        } catch (error) {
                            Logger.warn(`${iface}网络信息获取失败:`, error);
                        }
                    }

                    // 获取内存信息
                    let memoryData = null;
                    try {
                        const memoryResponse = await fetchWithRetry(`${PROXY_BASE_URL}/memory-info?ip=${formData.ipAddress}`);
                        const memoryResult = await memoryResponse.json();
                        if (memoryResult.success) {
                            memoryData = memoryResult.data;
                            if (memoryData.some(value => value !== 0)) {
                                errors.push('清除M区数据');
                            }
                        }
                    } catch (error) {
                        Logger.warn('内存信息获取失败:', error);
                    }

                    // 数据验证
                    if (deviceData.data.serialnumber === '-') {
                        errors.push('核对Mac地址是否正确烧录');
                    }
                    
                    if (deviceData.data.busversion === 'V0.0.0.0') {
                        errors.push('背板异常，重新烧录背板程序');
                    }
                    
                    if (formData.specifiedVersion && formData.specifiedVersion !== deviceData.data.softwareversion) {
                        errors.push(`软件版本错误 预期: ${formData.specifiedVersion} 实际: ${deviceData.data.softwareversion}`);
                    }
                    
                    if (formData.specifiedTime && formData.specifiedTime !== deviceData.data.builddate) {
                        errors.push(`构建日期错误 预期: ${formData.specifiedTime} 实际: ${deviceData.data.builddate}`);
                    }
                    
                    if (formData.specifiedBackplane && formData.specifiedBackplane !== deviceData.data.busversion) {
                        errors.push(`背板版本错误 预期: ${formData.specifiedBackplane} 实际: ${deviceData.data.busversion}`);
                    }
                    
                    const normalizedDeviceHighSpeed = (deviceData.data.higspeedIOversion === '-') ? ' -' : deviceData.data.higspeedIOversion;
                    const normalizedSpecifiedHighSpeed = (formData.specifiedHighSpeed === '-') ? ' -' : formData.specifiedHighSpeed;
                    if (normalizedSpecifiedHighSpeed && normalizedSpecifiedHighSpeed !== normalizedDeviceHighSpeed) {
                        errors.push(`高速IO版本错误 预期: ${normalizedSpecifiedHighSpeed} 实际: ${normalizedDeviceHighSpeed}`);
                    }
                    
                    if (formData.odmInfo && formData.odmInfo !== deviceData.data.devicename) {
                        errors.push(`ODM信息错误 预期: ${formData.odmInfo} 实际: ${deviceData.data.devicename}`);
                    }

                    // 更新表单字段
                    updateFormFields(deviceData.data, networkInfo, memoryData);

                    // 设置设备连接状态
                    deviceConnected.value = true;

                    // 显示结果
                    if (errors.length > 0) {
                        // 使用 <br/> 强制换行，并启用 HTML 渲染
                        const formattedErrors = errors
                            .map((error, index) => `${index + 1}. ${error}`)
                            .join('<br/><br/>');
                        ElMessageBox.alert(formattedErrors, '设备检查结果', {
                            dangerouslyUseHTMLString: true,
                            type: 'error'
                        });
                    } else {
                        ElMessage.success('设备连接成功');
                    }

                } catch (error) {
                    Logger.error('查询设备信息失败:', error);
                    deviceConnected.value = false;
                    ElMessage.error(error.message || '设备连接失败');
                } finally {
                    deviceLoading.value = false;
                }
            };

            // 更新表单字段
            const updateFormFields = (deviceInfo, networkInfo, memoryData) => {
                formData.deviceManufacturer = deviceInfo.vendorName || '';
                formData.deviceName = deviceInfo.devicename || '';
                formData.serialNumber = deviceInfo.serialnumber || '';
                formData.softwareVersion = deviceInfo.softwareversion || '';
                formData.buildDate = deviceInfo.builddate || '';
                formData.backplaneVersion = deviceInfo.busversion || '';
                formData.highSpeedIOVersion = (deviceInfo.higspeedIOversion === '-') ? ' -' : (deviceInfo.higspeedIOversion || '');

                // 处理 MAC 地址
                const macAddresses = [];
                for (const iface of ['eth0', 'eth1', 'eth2']) {
                    if (networkInfo[iface] && networkInfo[iface].mac) {
                        const mac = networkInfo[iface].mac;
                        const thirdFromLast = networkInfo[iface].third_from_last;
                        macAddresses.push(`${iface}mac: ${mac} 倒数第三个数${thirdFromLast}`);
                        macAddresses.push('');
                    }
                }
                formData.macAddress = macAddresses.join('\n');

                // 处理内存数据
                if (memoryData && Array.isArray(memoryData)) {
                    formData.mAreaData = memoryData.join(', ');
                } else {
                    formData.mAreaData = '无内存数据';
                }
            };

            // 清除设备信息
            const handleClearClick = () => {
                formData.deviceManufacturer = '';
                formData.deviceName = '';
                formData.serialNumber = '';
                formData.softwareVersion = '';
                formData.buildDate = '';
                formData.backplaneVersion = '';
                formData.highSpeedIOVersion = '';
                formData.macAddress = '';
                formData.mAreaData = '';
                ElMessage.info('已清除所有数据');
            };

            // 设备重启
            const handleDeviceReboot = async () => {
                try {
                    const result = await ElMessageBox.confirm('确定要重启设备吗？', '确认重启', {
                        type: 'warning'
                    });
                    
                    if (!formData.ipAddress) {
                        ElMessage.warning('请选择IP地址');
                        return;
                    }

                    const response = await fetchWithRetry(`${PROXY_BASE_URL}/reboot-device`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ ip: formData.ipAddress }),
                        timeout: 5000
                    });

                    const data = await response.json();
                    if (data.success) {
                        ElMessage.success('设备重启指令已发送');
                        setTimeout(() => {
                            ElMessage.info('设备正在重启中，请等待约30秒后再进行操作...');
                        }, 3000);
                    } else {
                        ElMessage.info('设备正在重启中，请等待约20秒后再进行操作...');
                    }
                } catch (error) {
                    if (error === 'cancel') return;
                    Logger.error('设备重启失败:', error);
                    ElMessage.error('设备重启失败，请检查网络连接');
                }
            };

            // 程序加载
            const handleProgramLoad = async () => {
                if (!formData.ipAddress) {
                    ElMessage.error('请选择IP地址');
                    return;
                }

                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.zip';
                
                fileInput.onchange = async (e) => {
                    const file = e.target.files[0];
                    if (!file) {
                        ElMessage.error('请选择文件');
                        return;
                    }

                    const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
                    if (file.size > MAX_FILE_SIZE) {
                        ElMessage.error('文件大小超过限制（最大100MB）');
                        return;
                    }

                    ElMessage.info('正在上传文件...');

                    const formDataFile = new FormData();
                    formDataFile.append('file', file);
                    formDataFile.append('ip', formData.ipAddress);

                    try {
                        const uploadResponse = await fetchWithRetry(`${PROXY_BASE_URL}/upload-program`, {
                            method: 'POST',
                            body: formDataFile,
                            timeout: 60000
                        });

                        const uploadResult = await uploadResponse.json();
                        if (!uploadResult.success) {
                            throw new Error(uploadResult.message || '文件上传失败');
                        }

                        ElMessage.info('文件上传成功，正在更新程序...');

                        const updateResponse = await fetchWithRetry(`${PROXY_BASE_URL}/update-program`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                ip: formData.ipAddress,
                                filename: file.name
                            }),
                            timeout: 30000
                        });

                        const updateResult = await updateResponse.json();
                        if (!updateResult.success) {
                            throw new Error(updateResult.message || '程序更新失败');
                        }

                        ElMessage.success('程序更新指令已发送，设备正在重启...');
                        
                        setTimeout(() => {
                            ElMessage.info('设备正在重启中，请等待约30秒后再进行操作...');
                        }, 3000);

                    } catch (error) {
                        Logger.error('程序加载失败:', error);
                        ElMessage.info('程序加载中，请等待约25秒后再进行操作...');
                    }
                };

                fileInput.click();
            };

            // 恢复出厂设置
            const handleDeviceReset = async () => {
                try {
                    await ElMessageBox.confirm('确定要恢复出厂设置吗？此操作可撤销。', '确认恢复出厂设置', {
                        type: 'warning'
                    });
                    
                    if (!formData.ipAddress) {
                        ElMessage.error('请选择IP地址');
                        return;
                    }

                    const response = await fetch(`${PROXY_BASE_URL}/reset-device`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ ip: formData.ipAddress })
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        ElMessage.success('恢复出厂设置成功');
                    } else {
                        throw new Error(data.message || '恢复出厂设置失败');
                    }
                } catch (error) {
                    if (error === 'cancel') return;
                    Logger.error('恢复出厂设置失败:', error);
                    ElMessage.error(error.message || '恢复出厂设置失败');
                }
            };

            // 新增UI相关方法 - 优化基本信息切换
            const toggleBasicInfo = () => {
                basicInfoCollapsed.value = !basicInfoCollapsed.value;
                
                // 性能优化：使用防抖的图标刷新 - 与CouplerVue保持一致
                const refreshIcons = getDebounced('toggleBasicInfoRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
            };

            const clearDeviceInfo = () => {
                const deviceFields = [
                    'odmInfo', 'deviceManufacturer', 'deviceName', 'serialNumber',
                    'softwareVersion', 'buildDate', 'backplaneVersion', 'highSpeedIOVersion',
                    'macAddress', 'mAreaData'
                ];

                deviceFields.forEach(field => {
                    formData[field] = '';
                });

                deviceConnected.value = false;
                ElMessage.success('设备信息已清除');
            };

            const showConfirm = (title, description, action, variant = 'default') => {
                confirmAction.value = { title, description, action, variant };
                showConfirmDialog.value = true;
            };

            const executeConfirmAction = () => {
                if (confirmAction.value?.action) {
                    confirmAction.value.action();
                }
                showConfirmDialog.value = false;
            };

            const setTestResult = (originalIndex, result) => {
                const testItem = testItems[originalIndex];
                
                // M区控制项目锁定检查（支持自动测试和视觉检测的分离控制）
                const autoTestControl = configManager ? configManager.getAutoTestMAreaControlInfo(originalIndex, selectedProductType.value) : null;
                const visualTestControl = configManager ? configManager.getVisualTestMAreaControlInfo(originalIndex, selectedProductType.value) : null;
                
                // 检查自动测试项目锁定
                if (autoTestControl && mAreaTestCompleted.value) {
                    let controlDescription;
                    if (autoTestControl.mode === 'combined') {
                        controlDescription = `M${autoTestControl.mIndices.join('、M')}组合控制`;
                    } else {
                        controlDescription = `M${autoTestControl.mIndex}控制`;
                    }
                    
                    ElMessage.warning(`${testItem.name} 由M区数据控制，不能手动修改结果`);
                    addTestLog('warning', 'AUTO_TEST_LOCK', 
                              `禁止手动修改: ${testItem.name}`, 
                              `该测试项目由${controlDescription}，请使用自动测试获取结果`);
                    return; // 阻止修改
                }
                
                // 检查视觉检测项目锁定
                if (visualTestControl && visualTestCompleted.value) {
                    let controlDescription;
                    if (visualTestControl.mode === 'combined') {
                        controlDescription = `M${visualTestControl.mIndices.join('、M')}组合控制`;
                    } else {
                        controlDescription = `M${visualTestControl.mIndex}控制`;
                    }
                    
                    ElMessage.warning(`${testItem.name} 由视觉检测控制，不能手动修改结果`);
                    addTestLog('warning', 'VISUAL_TEST_LOCK', 
                              `禁止手动修改: ${testItem.name}`, 
                              `该测试项目由视觉检测的${controlDescription}，请使用视觉检测获取结果`);
                    return; // 阻止修改
                }
                
                // 允许修改非M区控制的项目
                testItem.result = result;
                if (testItem.duration) {
                    delete testItem.duration;
                }
                
                // 性能优化：使用防抖的图标刷新 - 与CouplerVue保持一致
                const refreshIcons = getDebounced('manualIconRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
                
                const statusText = result === 'pass' ? '通过' : '失败';
                ElMessage.success(`${testItem.name} 已标记为${statusText}`);
                addTestLog('info', 'MANUAL', `手动设置测试结果: ${testItem.name} = ${statusText}`);
            };

            const clearAllResults = () => {
                testItems.forEach(item => {
                    item.result = '';
                    if (item.duration) {
                        delete item.duration;
                    }
                });
                
                // 重置自动测试和视觉检测完成状态，允许重新进行测试
                mAreaTestCompleted.value = false;
                visualTestCompleted.value = false;
                
                // 性能优化：使用防抖的图标刷新 - 与CouplerVue保持一致
                const refreshIcons = getDebounced('clearAllIconRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
                ElMessage.success('已清除所有测试结果');
                addTestLog('info', 'MANUAL', '手动清除所有测试结果，M区锁定状态已重置');
            };

            const setAllPass = (isAutoMode = false) => {
                let modifiedCount = 0;
                let skippedCount = 0;
                const skippedItems = [];
                
                // 只对启用的测试项目进行操作
                enabledTestItems.value.forEach((item) => {
                    const testItem = testItems[item.originalIndex];
                    
                    // 检查是否是M区自动控制或视觉检测控制并且已锁定的项目
                    const autoCtrl = configManager ? configManager.getAutoTestMAreaControlInfo(item.originalIndex, selectedProductType.value) : null;
                    const visualCtrl = configManager ? configManager.getVisualTestMAreaControlInfo(item.originalIndex, selectedProductType.value) : null;

                    // 判断锁定条件：
                    // 1) 自动测试项目在 mAreaTestCompleted 后锁定
                    // 2) 视觉检测项目在 visualTestCompleted 后锁定
                    if ((autoCtrl && mAreaTestCompleted.value) || (visualCtrl && visualTestCompleted.value)) {
                        skippedCount++;
                        if (autoCtrl) {
                            skippedItems.push(`${testItem.name}(自动测试锁定)`);
                        } else if (visualCtrl) {
                            skippedItems.push(`${testItem.name}(视觉检测锁定)`);
                        }
                        return; // 跳过锁定项目
                    }
                     
                     testItem.result = 'pass';
                     modifiedCount++;
                 });
                
                // 性能优化：使用防抖的图标刷新 - 与CouplerVue保持一致
                const refreshIcons = getDebounced('setAllPassIconRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
                
                // 只有手动点击时才显示成功提示和日志
                if (!isAutoMode) {
                    const config = currentProductConfig.value;
                    if (skippedCount > 0) {
                        ElMessage.success(`已设置${modifiedCount}个测试项为通过，跳过${skippedCount}个M区控制项目`);
                        addTestLog('info', 'MANUAL', `手动全通过: 修改${modifiedCount}项，跳过${skippedCount}项 (${config.name}配置)`, 
                                  `跳过项目: ${skippedItems.join(', ')}`);
                    } else {
                        ElMessage.success(`已设置${modifiedCount}个测试项为通过`);
                        addTestLog('info', 'MANUAL', `手动全通过: 修改${modifiedCount}项 (${config.name}配置)`);
                    }
                }
            };

            const clearTestLogs = () => {
                testLogs.value = [];
            };

            const getTestItemIconClass = (result) => {
                const classes = {
                    pass: 'bg-green-100 text-green-600',
                    fail: 'bg-red-100 text-red-600',
                    testing: 'bg-blue-100 text-blue-600',
                    '': 'bg-gray-100 text-gray-500'
                };
                return classes[result] || 'bg-gray-100 text-gray-500';
            };

            // 主题切换功能
            const toggleTheme = () => {
                isDarkMode.value = !isDarkMode.value;
                applyTheme();
                saveThemePreference();
                ElMessage.success(`已切换到${isDarkMode.value ? '深色' : '浅色'}主题`);
            };

            const applyTheme = () => {
                const htmlElement = document.documentElement;
                
                if (isDarkMode.value) {
                    htmlElement.setAttribute('data-theme', 'dark');
                } else {
                    htmlElement.removeAttribute('data-theme');
                }
                
                // 性能优化：使用防抖的图标刷新 - 与CouplerVue保持一致
                const refreshIcons = getDebounced('applyThemeRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
            };

            const saveThemePreference = () => {
                localStorage.setItem('theme-preference', isDarkMode.value ? 'dark' : 'light');
            };

            const loadThemePreference = () => {
                const saved = localStorage.getItem('theme-preference');
                if (saved) {
                    isDarkMode.value = saved === 'dark';
                } else {
                    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                        isDarkMode.value = true;
                    } else {
                        isDarkMode.value = false;
                    }
                }
            };
            
            // 全选处理
            const handleSelectAll = (value) => {
                testItems.value.forEach(item => {
                    item.selected = value;
                });
            };
            
            // 个别选择处理
            const handleItemSelect = () => {
                selectAll.value = testItems.value.every(item => item.selected);
            };
            
            // 测试结果变化处理
            const handleTestResultChange = (item, newResult) => {
                item.result = newResult;
            };
            
            // 表单提交
            const submitForm = async () => {
                Logger.log('开始提交测试数据...');
                
                // 自动执行"全通过"操作（跳过M区控制项目）
                Logger.log('自动执行全通过操作...');
                addTestLog('info', 'AUTO_PASS', '提交前自动执行全通过操作');
                
                // 统计原有的测试结果（区分M区控制和非M区控制，使用配置管理器）
                const originalStats = {
                    passed: testResults.value.filter(item => item.result === 'pass').length,
                    failed: testResults.value.filter(item => item.result === 'fail').length,
                    pending: testResults.value.filter(item => !item.result || item.result === '').length,
                    mAreaLocked: testResults.value.filter((item, index) => {
                        const mAreaControl = configManager ? configManager.getMAreaControlInfo(index, selectedProductType.value) : null;
                        return mAreaControl && mAreaTestCompleted.value;
                    }).length
                };
                
                // 执行全通过操作（自动跳过M区控制项目）
                setAllPass(true); // 传入true表示自动模式
                
                // 记录操作详情 - 统计M区控制项目的影响（使用配置管理器）
                const mAreaLockedItems = testResults.value.filter((item, index) => {
                    const mAreaControl = configManager ? configManager.getMAreaControlInfo(index, selectedProductType.value) : null;
                    return mAreaControl && mAreaTestCompleted.value;
                }).length;
                
                if (mAreaLockedItems > 0) {
                    const modifiableItems = testResults.value.length - mAreaLockedItems;
                    addTestLog('success', 'AUTO_PASS', 
                              `自动全通过完成: 修改${modifiableItems}个非M区控制项目，保留${mAreaLockedItems}个M区控制项目结果`);
                } else {
                    if (originalStats.failed > 0 || originalStats.pending > 0) {
                        addTestLog('success', 'AUTO_PASS', `自动全通过完成: ${originalStats.failed}个失败项和${originalStats.pending}个待测项已设置为通过`);
                    } else {
                        addTestLog('info', 'AUTO_PASS', '所有测试项已为通过状态，无需修改');
                    }
                }
                
                // 等待一小段时间确保状态更新完成
                await new Promise(resolve => setTimeout(resolve, 800));
                
                try {
                    // 检查表单引用是否存在
                    if (!formRef.value) {
                        Logger.error('表单引用不存在');
                        addTestLog('error', 'SUBMIT', '表单引用不存在，初始化失败');
                        ElMessage.error('表单初始化失败，请刷新页面重试');
                        return;
                    }
                    
                    // 增强的表单验证 - 捕获具体错误信息
                    await formRef.value.validate();
                    addTestLog('success', 'VALIDATE', '表单验证通过');
                } catch (validationErrors) {
                    // 处理验证错误，显示具体错误信息
                    if (validationErrors && typeof validationErrors === 'object') {
                        const handled = handleValidationErrors(validationErrors);
                        if (handled) return;
                    }
                    // 兜底处理
                    Logger.log('表单验证失败');
                    addTestLog('warning', 'VALIDATE', '表单验证失败，请检查必填字段');
                    ElMessage.warning('请完善必填信息');
                    return;
                }
                
                // 验证出厂序号 - 增强错误提示
                if (!formData.serialNumber || formData.serialNumber.trim() === '') {
                    const errorMsg = '出厂序号不能为空';
                    const detailMsg = '请输入有效的设备出厂序号';
                    
                    addTestLog('error', 'VALIDATE', errorMsg);
                    
                    ElMessage({
                        message: `${errorMsg}：${detailMsg}`,
                        type: 'error',
                        duration: 4000,
                        showClose: true
                    });
                    
                    focusToField('serialNumber');
                    return;
                }
                
                if (formData.serialNumber === '-') {
                    const errorMsg = '出厂序号格式错误';
                    const detailMsg = '出厂序号不能为"-"，请输入正确的序号';
                    
                    addTestLog('error', 'VALIDATE', '出厂序号不能为 "-"');
                    
                    ElMessage({
                        message: `${errorMsg}：${detailMsg}`,
                        type: 'error',
                        duration: 4000,
                        showClose: true
                    });
                    
                    focusToField('serialNumber');
                    return;
                }
                addTestLog('success', 'VALIDATE', '出厂序号验证通过');
                
                // 验证产品SN号长度 - 增强错误提示
                const snByteCount = parseInt(formData.snByteCount);
                if (isNaN(snByteCount)) {
                    const errorMsg = 'SN号字节数格式错误';
                    const detailMsg = '请输入有效的数字作为SN号字节数';
                    
                    addTestLog('error', 'VALIDATE', '产品SN号字节数无效');
                    
                    ElMessage({
                        message: `${errorMsg}：${detailMsg}`,
                        type: 'error',
                        duration: 4000,
                        showClose: true
                    });
                    
                    focusToField('snByteCount');
                    return;
                }
                
                const productSN = String(formData.productSN || '');
                if (productSN.length !== snByteCount) {
                    const errorMsg = 'SN号长度不符合要求';
                    const detailMsg = `当前SN号"${productSN}"长度为${productSN.length}个字符，但要求长度为${snByteCount}个字符，请检查并重新输入`;
                    
                    addTestLog('error', 'VALIDATE', `产品SN号长度错误: 期望${snByteCount}字节, 实际${productSN.length}字节`);
                    
                    ElMessage({
                        message: `${errorMsg}：${detailMsg}`,
                        type: 'error',
                        duration: 6000,
                        showClose: true
                    });
                    
                    // SN号长度验证失败时聚焦到SN输入框
                    focusToField('productSN');
                    return;
                }
                addTestLog('success', 'VALIDATE', `产品SN号长度验证通过: ${productSN.length}字节`);
                
                // 新增：M区数据一致性验证
                if (mAreaTestCompleted.value || visualTestCompleted.value) {
                    // 实时获取最新的M区数据进行验证
                    let currentMAreaValues;
                    try {
                        addTestLog('info', 'M_AREA_SUBMIT_CHECK', '提交前实时获取状态寄存器数据进行验证...');
                        currentMAreaValues = await refreshMAreaData();
                        addTestLog('success', 'M_AREA_SUBMIT_CHECK', `实时状态寄存器数据获取成功: [${currentMAreaValues.join(', ')}]`);
                    } catch (error) {
                        addTestLog('warning', 'M_AREA_SUBMIT_CHECK', `实时获取失败，使用缓存数据: ${error.message}`);
                        currentMAreaValues = parseMAreaData();
                    }

                    const criticalIndices = [0, 1, 2, 3, 4]; // M0-M4
                    const invalidValues = criticalIndices.filter(index =>
                        index >= currentMAreaValues.length || currentMAreaValues[index] !== 1
                    );

                    if (invalidValues.length > 0) {
                        const testModeText = mAreaTestCompleted.value && visualTestCompleted.value ?
                            '自动测试和视觉检测' :
                            mAreaTestCompleted.value ? '自动测试' : '视觉检测';

                        const invalidPositions = invalidValues.map(idx => {
                            const value = idx < currentMAreaValues.length ? currentMAreaValues[idx] : '未定义';
                            return `M${idx}=${value}`;
                        }).join('、');

                        addTestLog('error', 'M_AREA_VALIDATION',
                                  `状态寄存器数据验证失败: ${invalidPositions}`,
                                 `${testModeText}完成后，状态寄存器数据异常！请重新测试后提交`);

                        ElMessage.error(`${testModeText}后状态寄存器数据异常！请重新测试后提交`);

                        focusToField('mAreaData');
                        return;
                    }

                    addTestLog('success', 'M_AREA_VALIDATION',
                              'M区数据验证通过',
                              `M0-M4值正常: [${criticalIndices.map(i => currentMAreaValues[i] || 0).join(', ')}]`);
                }
                
                // 根据产品配置动态收集测试结果数据（类似CouplerVue.js）
                const getTestResultsForConfig = () => {
                    const config = currentProductConfig.value;
                    const results = {};
                    
                    // 定义测试项目名称映射
                    const testFieldMap = {
                        0: 'rs485_1_result',      // RS485_通信
                        1: 'rs232_result',        // RS232通信
                        2: 'canbus_result',       // CANbus通信
                        3: 'ethercat_result',     // EtherCAT通信
                        4: 'backplane_result',    // Backplane Bus通信
                        5: 'body_io_result',      // Body I/O输入输出
                        6: 'led_tube_result',     // Led数码管
                        7: 'led_bulb_result',     // Led灯珠
                        8: 'usb_drive_result',    // U盘接口
                        9: 'sd_slot_result',      // SD卡
                        10: 'debug_port_result',  // 调试串口
                        11: 'net_port_result',    // 网口
                        12: 'dip_switch_result',  // 拨码开关
                        13: 'reset_btn_result'    // 复位按钮
                    };
                    
                    // 只收集当前配置启用的测试项目结果
                    config.enabledTests.forEach(testIndex => {
                        const fieldName = testFieldMap[testIndex];
                        if (fieldName) {
                            results[fieldName] = testResults.value[testIndex].result;
                        }
                    });
                    
                    addTestLog('info', 'SUBMIT', `根据产品配置"${config.name}"收集测试结果: ${Object.keys(results).join(', ')}`);
                    return results;
                };
                
                // 新增：收集测试项目auto标记信息
                const collectAutoInfo = () => {
                    const config = currentProductConfig.value;
                    const autoInfo = {};
                    
                    // 统计数量
                    let autoTestCount = 0;
                    let visualTestCount = 0;
                    let manualTestCount = 0;
                    
                    // 遍历当前配置启用的测试项目，检查自动测试和视觉检测M区控制
                    config.enabledTests.forEach(testIndex => {
                        const testItem = testResults.value[testIndex];
                        let isAuto = false;
                        
                        // 检查自动测试M区控制项目
                        if (mAreaTestCompleted.value) {
                            const autoMAreaControl = configManager ? 
                                configManager.getAutoTestMAreaControlInfo(testIndex, selectedProductType.value) : null;
                            if (autoMAreaControl) {
                                isAuto = true;
                                autoTestCount++;
                            }
                        }
                        
                        // 检查视觉检测M区控制项目（如果不是自动测试控制的话）
                        if (!isAuto && visualTestCompleted.value) {
                            const visualMAreaControl = configManager ? 
                                configManager.getVisualTestMAreaControlInfo(testIndex, selectedProductType.value) : null;
                            if (visualMAreaControl) {
                                isAuto = true;
                                visualTestCount++;
                            }
                        }
                        
                        // 如果既不是自动测试控制也不是视觉检测控制，则为手动测试
                        if (!isAuto) {
                            manualTestCount++;
                        }
                        
                        autoInfo[testItem.code] = isAuto;
                    });
                    
                    // 输出详细的标记信息日志
                    if (autoTestCount > 0 || visualTestCount > 0 || manualTestCount > 0) {
                        let logDetails = [];
                        if (autoTestCount > 0) {
                            logDetails.push(`自动测试${autoTestCount}个`);
                        }
                        if (visualTestCount > 0) {
                            logDetails.push(`视觉检测${visualTestCount}个`);
                        }
                        if (manualTestCount > 0) {
                            logDetails.push(`手动测试${manualTestCount}个`);
                        }
                        
                        addTestLog('info', 'AUTO_INFO', `收集auto标记信息`, `分类: ${logDetails.join('; ')}`);
                    } else {
                        addTestLog('info', 'AUTO_INFO', `收集auto标记信息`, '全部手动测试 (未启用自动测试或视觉检测)');
                    }
                    
                    return autoInfo;
                };

                // 收集表单数据
                const submitData = {
                    // 基本信息
                    tester: formData.tester,
                    test_time: formData.testTime,
                    work_order: String(formData.orderNumber || '').trim(),
                    work_qty: String(formData.productionQuantity || '').trim(),
                    pro_model: String(formData.productModel || '').trim(),
                    pro_code: String(formData.productCode || '').trim(),
                    pro_sn: String(formData.productSN || '').trim(),
                    pro_batch: formData.batchNumber || 'N/A',
                    remarks: formData.remarks || 'N/A',
                    
                    // 设备信息
                    device_name: formData.deviceName || 'N/A',
                    serial: formData.serialNumber || 'N/A',
                    sw_version: formData.softwareVersion || 'N/A',
                    back_ver: formData.backplaneVersion || 'N/A',
                    high_speed_io_version: formData.highSpeedIOVersion || 'N/A',
                    build_date: formData.buildDate || 'N/A',
                    
                    // 根据产品配置动态收集测试结果
                    ...getTestResultsForConfig(),
                    
                    // 新增：auto标记信息
                    test_auto_info: JSON.stringify(collectAutoInfo())
                };
                
                // 产品状态映射
                const productStatusMap = {
                    'new': 1,
                    'used': 2,
                    'refurbished': 3
                };
                
                submitData.pro_status = productStatusMap[formData.productStatus];
                if (!submitData.pro_status) {
                    ElMessage.warning('请选择产品状态！');
                    return;
                }
                
                // 设置维修和返工次数
                submitData.maintenance = submitData.pro_status === 2 ? 1 : 0;
                submitData.rework = submitData.pro_status === 3 ? 1 : 0;
                
                // 根据产品配置动态转换测试结果为数字格式（类似CouplerVue.js）
                const convertTestResultsForConfig = (submitData) => {
                    const config = currentProductConfig.value;
                    const converted = {};
                    
                    // 定义数据库字段映射
                    const dbFieldMap = {
                        0: 'rs485_1',       // RS485_通信
                        1: 'rs232',         // RS232通信
                        2: 'canbus',        // CANbus通信
                        3: 'ethercat',      // EtherCAT通信
                        4: 'backplane',     // Backplane Bus通信
                        5: 'body_io',       // Body I/O输入输出
                        6: 'led_tube',      // Led数码管
                        7: 'led_bulb',      // Led灯珠
                        8: 'usb_drive',     // U盘接口
                        9: 'sd_slot',       // SD卡
                        10: 'debug_port',   // 调试串口
                        11: 'net_port',     // 网口
                        12: 'dip_switch',   // 拨码开关
                        13: 'reset_btn'     // 复位按钮
                    };
                    
                    const submitFieldMap = {
                        0: 'rs485_1_result',
                        1: 'rs232_result',
                        2: 'canbus_result',
                        3: 'ethercat_result',
                        4: 'backplane_result',
                        5: 'body_io_result',
                        6: 'led_tube_result',
                        7: 'led_bulb_result',
                        8: 'usb_drive_result',
                        9: 'sd_slot_result',
                        10: 'debug_port_result',
                        11: 'net_port_result',
                        12: 'dip_switch_result',
                        13: 'reset_btn_result'
                    };
                    
                    // 只转换当前配置启用的测试项目 (采用默认通过策略：只有明确标记为fail的才是失败)
                    config.enabledTests.forEach(testIndex => {
                        const dbField = dbFieldMap[testIndex];
                        const submitField = submitFieldMap[testIndex];
                        if (dbField && submitField && submitData[submitField] !== undefined) {
                            converted[dbField] = submitData[submitField] === 'fail' ? 2 : 1;
                        }
                    });
                    
                    addTestLog('info', 'SUBMIT', `根据产品配置"${config.name}"转换测试结果: ${JSON.stringify(converted)}`);
                    return converted;
                };
                
                // 转换测试结果为数字格式
                const testResultsConverted = convertTestResultsForConfig(submitData);
                
                Object.assign(submitData, testResultsConverted);
                
                // 确定整体测试状态
                const allTestsPassed = Object.values(testResultsConverted).every(result => result === 1);
                submitData.test_status = allTestsPassed ? 'pass' : 'ng';
                
                Logger.log('提交的数据：', submitData);
                //提交详细的测试项目显示
                addTestLog('info', 'SUBMIT', '开始提交测试数据...', JSON.stringify(submitData, null, 2));
                
                // 添加调试信息
                Logger.log('测试结果统计：', {
                    通过: passedTests.value,
                    失败: failedTests.value,
                    总计: totalTests.value,
                    整体状态: submitData.test_status
                });
                
                try {
                    loading.value = true;
                    addTestLog('info', 'SUBMIT', '正在向服务器提交CPU控制器测试数据...');
                    
                    const response = await fetch('/api/cpu-controller-vue/submit-test', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(submitData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        Logger.log('提交成功，服务器响应：', result);
                        addTestLog('success', 'SUBMIT', 'CPU控制器测试数据提交成功', result.message || '数据已保存到数据库');
                        
                        // 新增：记录auto标记提交信息
                        try {
                            const autoInfo = JSON.parse(submitData.test_auto_info);
                            const config = currentProductConfig.value;
                            
                            // 重新分析auto标记的详细分类信息
                            let autoTestCount = 0;
                            let visualTestCount = 0;
                            let manualCount = 0;
                            
                            config.enabledTests.forEach(testIndex => {
                                const testItem = testResults.value[testIndex];
                                const isAuto = autoInfo[testItem.code];
                                
                                if (isAuto) {
                                    // 根据当前状态判断是自动测试还是视觉检测
                                    // 优先判断是否为自动测试项
                                    const autoMAreaControl = mAreaTestCompleted.value ? configManager.getAutoTestMAreaControlInfo(testIndex, selectedProductType.value) : null;
                                    if (autoMAreaControl) {
                                        autoTestCount++;
                                    } else {
                                        // 如果不是自动测试项，再判断是否为视觉检测项
                                        const visualMAreaControl = visualTestCompleted.value ? configManager.getVisualTestMAreaControlInfo(testIndex, selectedProductType.value) : null;
                                        if (visualMAreaControl) {
                                            visualTestCount++;
                                        }
                                    }
                                } else {
                                    manualCount++;
                                }
                            });
                            
                            // 输出详细的提交统计信息
                            let submitDetails = [];
                            if (autoTestCount > 0) {
                                submitDetails.push(`自动测试: ${autoTestCount}项`);
                            }
                            if (visualTestCount > 0) {
                                submitDetails.push(`视觉检测: ${visualTestCount}项`);
                            }
                            if (manualCount > 0) {
                                submitDetails.push(`手动测试: ${manualCount}项`);
                            }
                            
                            addTestLog('info', 'AUTO_SUBMIT', `测试方式统计: ${submitDetails.join('，')}`);
                        } catch (error) {
                            addTestLog('warning', 'AUTO_SUBMIT', 'auto标记信息解析失败', error.message);
                        }
                        
                        ElMessage.success('测试信息提交成功！');
                        
                        // 新增：M区测试日志存储（支持自动测试和视觉检测）
                        if (mAreaTestCompleted.value || visualTestCompleted.value) {
                            updateMAreaLog();
                        }
                        
                        // 保存需要保留的字段值
                        const savedValues = {
                            tester: formData.tester,
                            orderNumber: formData.orderNumber,
                            productionQuantity: formData.productionQuantity,
                            productCode: formData.productCode,
                            productModel: formData.productModel,
                            productStatus: formData.productStatus,
                            batchNumber: formData.batchNumber,
                            snByteCount: formData.snByteCount,
                            remarks: formData.remarks,
                            
                            // 设备信息保留字段
                            specifiedVersion: formData.specifiedVersion,
                            specifiedTime: formData.specifiedTime,
                            specifiedBackplane: formData.specifiedBackplane,
                            specifiedHighSpeed: formData.specifiedHighSpeed,
                            odmInfo: formData.odmInfo,
                            ipAddress: formData.ipAddress
                        };
                        
                        // 重置表单
                        await formRef.value.resetFields();
                        
                        // 恢复保存的值
                        Object.assign(formData, savedValues);
                        
                        // 重置测试项目状态到初始状态
                        testItems.forEach(item => {
                            item.result = '';
                            if (item.duration) {
                                delete item.duration;
                            }
                        });
                        
                        // 重置测试相关状态
                        selectAll.value = false;
                        mAreaTestCompleted.value = false;
                        visualTestCompleted.value = false;
                        deviceConnected.value = false;
                        
                        // 清空特定字段
                        formData.productSN = '';
                        formData.deviceManufacturer = '';
                        formData.deviceName = '';
                        formData.serialNumber = '';
                        formData.softwareVersion = '';
                        formData.buildDate = '';
                        formData.backplaneVersion = '';
                        formData.highSpeedIOVersion = '';
                        formData.macAddress = '';
                        formData.mAreaData = '';
                        
                        addTestLog('info', 'RESET', '测试数据提交成功，系统已重置到初始状态');
                        
                        // 重新渲染图标确保状态正确显示
                        nextTick(() => {
                            if (window.lucide) {
                                window.lucide.createIcons();
                            }
                        });
                        
                        // 提交成功后聚焦到产品SN输入框（基本信息卡片状态保持不变）
                        focusToField('productSN');
                        
                    } else {
                        Logger.error('提交失败，服务器响应：', result);
                        addTestLog('error', 'SUBMIT', `提交失败: ${result.message || '未知错误'}`);
                        ElMessage.error(result.message || '提交失败，请重试');
                    }
                } catch (error) {
                    Logger.error('提交错误：', error);
                    addTestLog('error', 'SUBMIT', `网络错误: ${error.message || '请检查网络连接'}`);
                    ElMessage.error(`网络错误：${error.message || '请检查网络连接'}`);
                } finally {
                    loading.value = false;
                }
            };
            
            // 监听器
            watch(() => formData.orderNumber, (newValue) => {
                debouncedQueryOrderInfo(newValue);
            });
            
            // ===== BEM架构重构：样式计算属性 =====
            
            // 工具栏动态类名计算 - 清理冗余Tailwind类
            const toolbarClasses = computed(() => ({
                'glass-effect': true,
                'border-b': true,
                'py-4': true,
                'shadow-xl': true,
                'backdrop-blur-xl': true,
                'cpu-controller__toolbar': true,
                'cpu-controller__toolbar--dark': isDarkMode.value,
                'cpu-controller__toolbar--light': !isDarkMode.value
            }));
            
            // 测试区域动态类名计算 - 清理冗余Tailwind类
            const testSectionClasses = computed(() => ({
                'cpu-controller__test-section': true,
                'test-section': true
            }));
            
            // 进度条动态样式计算
            const progressBarStyle = computed(() => ({
                '--progress-width': `${testProgress.value}%`
            }));
            
            // 生命周期
            onMounted(async () => {
                // 加载主题偏好
                loadThemePreference();
                
                // 初始化主题和图标
                nextTick(() => {
                    applyTheme();
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                });
                
                // 设置当前时间
                const now = new Date();
                formData.testTime = now.toISOString().slice(0, 10);
                
                // 获取当前用户
                try {
                    const response = await fetch('/api/cpu-controller-vue/get-current-user');
                    const data = await response.json();
                    
                    if (data.success && data.username) {
                        formData.tester = data.username;
                        addTestLog('success', 'USER', `当前用户: ${data.username}`);
                    }
                } catch (error) {
                    Logger.error('获取用户信息失败:', error);
                    addTestLog('warning', 'USER', '获取当前用户失败，请手动输入');
                }
                
                // 添加初始化日志
                addTestLog('system', 'INIT', 'CPU控制器测试系统初始化完成');
                addTestLog('info', 'SYSTEM', `当前主题: ${isDarkMode.value ? '深色' : '浅色'}模式`);
                
                // 初始化配置信息
                const config = currentProductConfig.value;
                addTestLog('info', 'CONFIG', `默认产品配置: ${config.name}`, `启用测试项目: ${config.enabledTestCount}个`);
                addTestLog('info', 'M_AREA_INIT', '状态寄存器智能测试功能已启用', '前9个通信测试项目将基于状态寄存器数据精确判断');
                
                // 监听系统主题变化
                if (window.matchMedia) {
                    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
                    mediaQuery.addEventListener('change', (e) => {
                        const saved = localStorage.getItem('theme-preference');
                        if (!saved) {
                            isDarkMode.value = e.matches;
                            applyTheme();
                        }
                    });
                }
            });
            
            return {
                // 响应式数据
                loading,
                deviceLoading,
                deviceConnected,
                basicInfoCollapsed,
                showTestLog,
                autoScroll,
                isDarkMode,
                showConfirmDialog,
                confirmAction,
                testLogs,
                currentTestIndex,
                testRunning,
                mAreaTestCompleted,
                visualTestCompleted,
                formData,
                testResults,
                selectAll,
                rules,
                formRef,
                productStatusOptions,
                testResultOptions,
                ipOptions,

                // 计算属性
                passedTests,
                failedTests,
                totalTests,
                testProgress,
                overallResult,
                getRowClass,
                enabledTestItems,
                supportsVisualInspection,

                // BEM架构重构：样式计算属性
                toolbarClasses,
                testSectionClasses,
                progressBarStyle,

                // 原有方法
                handleSelectAll,
                handleItemSelect,
                handleTestResultChange,
                checkSN,
                focusToField,
                submitForm,
                queryDeviceInfo,
                handleClearClick,
                handleDeviceReboot,
                handleProgramLoad,
                handleDeviceReset,

                // 新增UI方法
                toggleBasicInfo,
                clearDeviceInfo,
                showConfirm,
                executeConfirmAction,
                setTestResult,
                clearAllResults,
                setAllPass,
                clearTestLogs,
                getTestItemIconClass,
                toggleTheme,
                applyTheme,
                loadThemePreference,

                // 测试日志相关方法
                addTestLog,
                runAutoTest,
                runVisualInspection,
                stopTest,
                getLogLevelClass,
                getLogMessageClass,
                
                // M区数据处理功能
                parseMAreaData,
                refreshMAreaData,
                generateMAreaTestLog,
                updateMAreaLog,
                checkModuleMessage,
                
                // 新增的日志管理功能
                filteredLogs,
                exportLogs,
                toggleLogLevel,
                logConfig,
                
                // 增强的错误处理功能
                handleValidationErrors,
                fieldNameMap,
                
                // 输入处理功能
                handleOrderNumberInput,
                handleProductSNInput,
                handleProductTypeChange,
                
                // 产品类型配置
                selectedProductType,
                currentProductConfig,
                currentTestMapping,
                productTypeOptions,
                showProductTypeDetails,
                productTypeCollapsed,
                toggleProductType,
                
                // 配置管理器
                configManager
            };
        },
        
        template: `
        <div class="cpu-controller__main gradient-bg">
            <!-- 顶部工具栏 -->
            <div :class="toolbarClasses">
                <div class="cpu-controller__toolbar-container">
                    <div class="cpu-controller__toolbar-left">
                        <!-- Logo和标题 -->
                        <div class="cpu-controller__toolbar-brand">
                            <div class="relative">
                                <div class="cpu-controller__icon cpu-controller__icon--blue rounded-xl shadow-lg">
                                    <i data-lucide="cpu" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
                            </div>
                            <div>
                                <h1 class="cpu-controller__title-main" :class="isDarkMode ? 'text-white' : 'text-gray-900'">CPU控制器测试系统</h1>
                                <p class="cpu-controller__text-status" :class="isDarkMode ? 'text-blue-300' : 'text-blue-600'">Professional Testing Platform v2.1</p>
                            </div>
                        </div>
                        
                        <!-- 分隔线 -->
                        <div class="h-8 w-px bg-gray-300"></div>
                        
                        <!-- 设备状态 -->
                        <div class="cpu-controller__toolbar-status">
                            <div class="relative">
                                <div :class="['w-3 h-3 rounded-full shadow-lg status-indicator', deviceConnected ? 'bg-green-500 status-connected' : 'bg-red-500 status-disconnected']"></div>
                            </div>
                            <div>
                                <span class="text-sm font-medium" :class="isDarkMode ? 'text-white' : 'text-gray-900'">
                                    {{ deviceConnected ? '设备已连接' : '设备未连接' }}
                                </span>
                                <p class="text-xs" :class="isDarkMode ? 'text-gray-300' : 'text-gray-600'">{{ deviceConnected ? formData.ipAddress : '请连接设备' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="cpu-controller__toolbar-actions">
                        <!-- 主题切换按钮 -->
                        <el-button 
                            @click="toggleTheme"
                            class="theme-toggle-btn"
                            :title="isDarkMode ? '切换到浅色主题' : '切换到深色主题'"
                        >
                            <i :data-lucide="isDarkMode ? 'moon' : 'sun'" class="w-5 h-5"></i>
                        </el-button>
                        <el-button 
                            type="primary" 
                            :loading="deviceLoading"
                            @click="queryDeviceInfo"
                            class="border-blue-200 text-blue-700 hover:bg-blue-50"
                        >
                            <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                            连接设备
                        </el-button>
                        
                        <el-button 
                            v-if="!testRunning && supportsVisualInspection"
                            @click="runVisualInspection"
                            :disabled="!deviceConnected"
                            class="shadow-lg visual-detection-btn"
                            style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important; border: none !important; color: white !important;"
                        >
                            <i data-lucide="eye" class="w-4 h-4 mr-2"></i>
                            视觉检测
                        </el-button>
                        
                        <el-button 
                            v-if="!testRunning"
                            type="success"
                            @click="runAutoTest(false)"
                            :disabled="!deviceConnected"
                            class="shadow-lg"
                        >
                            <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                            自动测试
                        </el-button>
                        
                        <el-button 
                            v-else
                            type="danger"
                            @click="stopTest"
                            class="shadow-lg"
                        >
                            <i data-lucide="square" class="w-4 h-4 mr-2"></i>
                            停止测试
                        </el-button>
                        
                        <el-button 
                            type="primary"
                            :loading="loading"
                            @click="() => { console.log('提交测试按钮被点击'); submitForm(); }"
                            class="shadow-lg"
                        >
                            <i data-lucide="sparkles" class="w-4 h-4 mr-2"></i>
                            提交测试
                        </el-button>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="cpu-controller__main-content main-content-area">
                <!-- 左侧表单区域 -->
                <div class="cpu-controller__form-section form-section">
                    <div class="cpu-controller__space-y">
                        <!-- 基本信息卡片 -->
                        <div class="cpu-controller__card theme-card glass-effect card-hover">
                            <div class="p-6 cursor-pointer" @click="toggleBasicInfo">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                            <i data-lucide="shield" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="cpu-controller__title-section theme-text-primary">基本信息</span>
                                    </div>
                                    <i :data-lucide="basicInfoCollapsed ? 'chevron-down' : 'chevron-up'" class="w-5 h-5 text-blue-300"></i>
                                </div>
                            </div>
                            
                            <el-form 
                                ref="formRef"
                                :model="formData" 
                                :rules="rules" 
                                @submit.prevent="submitForm">
                            
                            <!-- 基本信息表单 -->
                            <div class="cpu-controller__form-content">
                                <!-- 折叠状态：显示关键信息 -->
                                <div v-if="basicInfoCollapsed" class="cpu-controller__form-collapsed">
                                    <!-- 加工单号、产品型号 -->
                                    <div class="cpu-controller__grid-2">
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">加工单号</label>
                                            <el-input
                                                v-model="formData.orderNumber"
                                                @input="handleOrderNumberInput"
                                                placeholder="输入工单号自动查询"
                                                class="h-10"
                                            ></el-input>
                                        </div>
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">产品型号</label>
                                            <el-input
                                                v-model="formData.productModel"
                                                class="h-10"
                                            ></el-input>
                                        </div>
                                    </div>
                                    <!-- 产品SN号、SN字节数 -->
                                    <div class="cpu-controller__grid-2">
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">产品SN号 *</label>
                                            <el-input
                                                v-model="formData.productSN"
                                                @input="handleProductSNInput"
                                                @blur="checkSN(formData.productSN)"
                                                @keyup.enter.prevent="checkSN(formData.productSN)"
                                                placeholder="请输入产品SN号"
                                                class="h-10"
                                            ></el-input>
                                        </div>
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">SN字节数 *</label>
                                            <el-input
                                                v-model="formData.snByteCount"
                                                type="number"
                                                class="h-10"
                                            ></el-input>
                                        </div>
                                    </div>
                                </div>

                                <!-- 展开状态：显示所有字段 -->
                                <transition name="collapse">
                                    <div v-show="!basicInfoCollapsed" class="cpu-controller__form-expanded">
                                        <!-- 第一行：测试人员、测试时间、批次号 -->
                                        <div class="cpu-controller__grid-3">
                                            <el-form-item label="测试人员" prop="tester">
                                                <el-input v-model="formData.tester"></el-input>
                                            </el-form-item>
                                            <el-form-item label="测试时间">
                                                <el-input 
                                                    v-model="formData.testTime" 
                                                    type="date">
                                                </el-input>
                                            </el-form-item>
                                            <el-form-item label="批次号">
                                                <el-input v-model="formData.batchNumber"></el-input>
                                            </el-form-item>
                                        </div>
                                        
                                        <!-- 第二行：产品编码、生产数量、SN字节数 -->
                                        <div class="cpu-controller__grid-3">
                                            <el-form-item label="产品编码" prop="productCode">
                                                <el-input v-model="formData.productCode"></el-input>
                                            </el-form-item>
                                            <el-form-item label="生产数量" prop="productionQuantity">
                                                <el-input v-model="formData.productionQuantity"></el-input>
                                            </el-form-item>
                                            <el-form-item label="SN字节数" prop="snByteCount">
                                                <el-input v-model="formData.snByteCount" type="number"></el-input>
                                            </el-form-item>
                                        </div>
                                        
                                        <!-- 第三行：加工单号、产品型号 -->
                                        <div class="cpu-controller__grid-2">
                                            <el-form-item label="加工单号" prop="orderNumber">
                                                <el-input 
                                                    v-model="formData.orderNumber"
                                                    @input="handleOrderNumberInput"
                                                    placeholder="输入工单号自动查询">
                                                </el-input>
                                            </el-form-item>
                                            <el-form-item label="产品型号" prop="productModel">
                                                <el-input v-model="formData.productModel"></el-input>
                                            </el-form-item>
                                        </div>
                                        
                                        <!-- 第四行：产品SN号、产品状态 -->
                                        <div class="cpu-controller__grid-2">
                                            <el-form-item label="产品SN号" prop="productSN">
                                                <el-input 
                                                    v-model="formData.productSN"
                                                    @input="handleProductSNInput"
                                                    @blur="checkSN(formData.productSN)"
                                                    @keyup.enter.prevent="checkSN(formData.productSN)"
                                                    placeholder="请输入产品SN号">
                                                </el-input>
                                            </el-form-item>
                                            <el-form-item label="产品状态" prop="productStatus">
                                                <el-select v-model="formData.productStatus" class="w-full" placeholder="请选择产品状态">
                                                    <el-option 
                                                        v-for="option in productStatusOptions" 
                                                        :key="option.value"
                                                        :label="option.label" 
                                                        :value="option.value"
                                                        :disabled="option.disabled">
                                                    </el-option>
                                                </el-select>
                                            </el-form-item>
                                        </div>
                                        
                                        <!-- 第五行：备注 -->
                                        <el-form-item label="备注">
                                            <el-input 
                                                v-model="formData.remarks" 
                                                type="textarea" 
                                                :rows="2" 
                                                placeholder="请输入备注信息">
                                            </el-input>
                                        </el-form-item>
                                    </div>
                                </transition>
                            </div>
                            </el-form>
                        </div>
                        
                        <!-- 产品类型配置卡片 -->
                        <div class="cpu-controller__card theme-card glass-effect card-hover">
                            <div class="cpu-controller__card-content">
                                <div class="cpu-controller__progress-header cursor-pointer" @click="toggleProductType">
                                    <div class="cpu-controller__card-title">
                                        <div class="cpu-controller__icon cpu-controller__icon--orange">
                                            <i data-lucide="settings-2" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="cpu-controller__title-section theme-text-primary">产品类型配置</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="cpu-controller__progress-tabs" v-if="!productTypeCollapsed">
                                            <el-button
                                                size="small"
                                                @click.stop="showProductTypeDetails = !showProductTypeDetails"
                                                class="h-8 text-xs"
                                            >
                                                {{ showProductTypeDetails ? '隐藏详情' : '显示详情' }}
                                            </el-button>
                                        </div>
                                        <i :data-lucide="productTypeCollapsed ? 'chevron-down' : 'chevron-up'" class="w-5 h-5 text-blue-300"></i>
                                    </div>
                                </div>

                                <!-- 产品类型选择器 -->
                                <div class="mb-4" v-show="!productTypeCollapsed">
                                    <div class="flex items-center space-x-4">
                                        <label class="text-sm font-medium theme-text-secondary min-w-fit">产品类型:</label>
                                        <el-select 
                                            v-model="selectedProductType" 
                                            @change="handleProductTypeChange"
                                            placeholder="选择产品类型"
                                            class="flex-1"
                                        >
                                            <el-option
                                                v-for="option in productTypeOptions"
                                                :key="option.value"
                                                :label="option.label"
                                                :value="option.value"
                                            >
                                                <div class="flex items-center justify-between w-full">
                                                    <span>{{ option.label }}</span>
                                                    <span class="text-xs text-gray-500">{{ option.count }}个测试项目</span>
                                                </div>
                                            </el-option>
                                        </el-select>
                                    </div>
                                </div>

                                <!-- 当前配置显示 -->
                                <div class="bg-gray-50 rounded-lg p-3 mb-4" v-show="!productTypeCollapsed">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium theme-text-primary">当前配置</span>
                                        <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                                            {{ currentProductConfig.enabledTestCount }} 个测试项目
                                        </span>
                                    </div>
                                    <p class="text-sm theme-text-secondary mb-2">{{ currentProductConfig.description }}</p>
                                    
                                    <!-- 详细配置 -->
                                    <transition name="collapse">
                                        <div v-show="showProductTypeDetails" class="mt-3 pt-3 border-t border-gray-200">
                                            <!-- 产品型号显示 -->
                                            <div v-if="currentProductConfig.productModels" class="mb-2">
                                                <p class="text-xs font-medium theme-text-primary mb-1">产品型号:</p>
                                                <p class="text-xs theme-text-secondary">{{ currentProductConfig.productModels }}</p>
                                            </div>
                                            <p class="text-xs font-medium theme-text-primary mb-2">测试项目配置:</p>
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                                <div 
                                                    v-for="mapping in currentTestMapping" 
                                                    :key="mapping.index"
                                                    class="flex items-center space-x-2 text-xs bg-white rounded px-2 py-1"
                                                >
                                                    <span class="bg-green-100 text-green-700 px-1.5 py-0.5 rounded text-xs">{{ mapping.category }}</span>
                                                    <i data-lucide="arrow-right" class="w-3 h-3 text-gray-400"></i>
                                                    <span class="text-gray-700">{{ mapping.testName }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- 操作提示 -->
                                <div class="flex items-start space-x-2 text-xs text-gray-500" v-show="!productTypeCollapsed">
                                    <i data-lucide="info" class="w-4 h-4 mt-0.5 text-blue-500"></i>
                                    <div class="flex-1">
                                        <p>选择产品类型后，点击"自动测试"将根据当前配置启用相应的测试项目并进行智能测试。</p>
                                        <p class="mt-1">只有当前配置启用的测试项目才会被执行和显示，未启用的项目将被跳过。</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 设备信息卡片 -->
                        <div class="cpu-controller__device-card theme-card glass-effect card-hover">
                            <div class="cpu-controller__card-content">
                                <div class="cpu-controller__device-header">
                                    <div class="cpu-controller__card-title">
                                        <div class="cpu-controller__icon cpu-controller__icon--green">
                                            <i data-lucide="activity" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="cpu-controller__title-section theme-text-primary">设备信息</span>
                                    </div>
                                    <div class="cpu-controller__device-actions">
                                        <el-button
                                            size="small"
                                            @click="() => showConfirm('清除设备信息', '确定要清除设备信息字段吗？此操作不可撤销。', clearDeviceInfo)"
                                            class="h-8 px-3"
                                        >
                                            <i data-lucide="trash-2" class="w-3 h-3 text-red-500 mr-1"></i>
                                            <span class="text-xs">清除</span>
                                        </el-button>
                                        <el-button
                                            size="small"
                                            @click="() => showConfirm('重启设备', '确定要重启设备吗？设备将断开连接并重新启动。', handleDeviceReboot, 'destructive')"
                                            class="h-8 px-3"
                                        >
                                            <i data-lucide="power" class="w-3 h-3 text-orange-500 mr-1"></i>
                                            <span class="text-xs">重启</span>
                                        </el-button>
                                        <el-button size="small" @click="handleProgramLoad" :disabled="loading" class="h-8 px-3">
                                            <i data-lucide="upload" class="w-3 h-3 text-blue-500 mr-1"></i>
                                            <span class="text-xs">加载</span>
                                        </el-button>
                                        <el-button
                                            size="small"
                                            @click="() => showConfirm('恢复出厂设置', '确定要恢复设备出厂设置吗？这将清除所有用户配置。', handleDeviceReset, 'destructive')"
                                            class="h-8 px-3"
                                        >
                                            <i data-lucide="refresh-cw" class="w-3 h-3 text-green-500 mr-1"></i>
                                            <span class="text-xs">重置</span>
                                        </el-button>
                                    </div>
                                </div>

                                <div class="cpu-controller__device-content">
                                    <!-- IP地址选择 -->
                                    <div class="cpu-controller__field-group">
                                        <label class="cpu-controller__field-label">设备IP地址</label>
                                        <el-select v-model="formData.ipAddress" placeholder="选择IP地址" class="w-full">
                                            <el-option
                                                v-for="option in ipOptions"
                                                :key="option.value"
                                                :label="option.label"
                                                :value="option.value"
                                            ></el-option>
                                        </el-select>
                                    </div>

                                    <!-- 指定版本和日期 -->
                                    <div class="cpu-controller__grid-2">
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">指定版本</label>
                                            <el-input v-model="formData.specifiedVersion" readonly class="bg-gray-50"></el-input>
                                        </div>
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">指定日期</label>
                                            <el-input v-model="formData.specifiedTime" readonly class="bg-gray-50"></el-input>
                                        </div>
                                    </div>

                                    <!-- 指定背板和高速 -->
                                    <div class="cpu-controller__grid-2">
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">指定背板</label>
                                            <el-input v-model="formData.specifiedBackplane" readonly class="bg-gray-50"></el-input>
                                        </div>
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">指定高速</label>
                                            <el-input v-model="formData.specifiedHighSpeed" readonly class="bg-gray-50"></el-input>
                                        </div>
                                    </div>

                                    <!-- ODM信息和设备厂商 -->
                                    <div class="cpu-controller__grid-2">
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">ODM信息</label>
                                            <el-input v-model="formData.odmInfo" placeholder="请输入ODM信息"></el-input>
                                        </div>
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">设备厂商</label>
                                            <el-input v-model="formData.deviceManufacturer" placeholder="请输入设备厂商"></el-input>
                                        </div>
                                    </div>

                                    <!-- 设备名称和出厂序号 -->
                                    <div class="cpu-controller__grid-2">
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">设备名称</label>
                                            <el-input v-model="formData.deviceName" placeholder="请输入设备名称"></el-input>
                                        </div>
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">出厂序号</label>
                                            <el-input v-model="formData.serialNumber" placeholder="请输入出厂序号"></el-input>
                                        </div>
                                    </div>

                                    <!-- 软件版本和构建日期 -->
                                    <div class="cpu-controller__grid-2">
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">软件版本</label>
                                            <el-input v-model="formData.softwareVersion" placeholder="请输入软件版本"></el-input>
                                        </div>
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">构建日期</label>
                                            <el-input v-model="formData.buildDate" placeholder="请输入构建日期"></el-input>
                                        </div>
                                    </div>

                                    <!-- 背板版本和高速IO版本 -->
                                    <div class="cpu-controller__grid-2">
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">背板版本</label>
                                            <el-input v-model="formData.backplaneVersion" placeholder="请输入背板版本"></el-input>
                                        </div>
                                        <div class="cpu-controller__field-group">
                                            <label class="cpu-controller__field-label">高速IO版本</label>
                                            <el-input v-model="formData.highSpeedIOVersion" placeholder="请输入高速IO版本"></el-input>
                                        </div>
                                    </div>

                                    <!-- MAC地址 -->
                                    <div class="cpu-controller__field-group">
                                        <label class="cpu-controller__field-label">MAC地址</label>
                                        <el-input
                                            v-model="formData.macAddress"
                                            type="textarea"
                                            :rows="2"
                                            placeholder="00:1A:2B:3C:4D:5E"
                                            class="font-mono text-sm"
                                        ></el-input>
                                    </div>

                                    <!-- M区数据 -->
                                    <div class="cpu-controller__field-group">
                                        <label class="cpu-controller__field-label">M区数据</label>
                                        <el-input
                                            v-model="formData.mAreaData"
                                            type="textarea"
                                            :rows="3"
                                            placeholder="M0: 0x1234&#10;M1: 0x5678"
                                            class="font-mono text-sm"
                                        ></el-input>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧测试区域 -->
                <div :class="testSectionClasses">
                    <div class="space-y-6">
                        <!-- 测试进度卡片 -->
                        <div class="cpu-controller__card theme-card glass-effect card-hover">
                            <div class="cpu-controller__card-content">
                                <div class="cpu-controller__progress-header">
                                    <div class="cpu-controller__card-title">
                                        <div class="cpu-controller__icon cpu-controller__icon--purple">
                                            <i data-lucide="target" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="cpu-controller__title-section theme-text-primary">测试进度</span>
                                    </div>
                                    <div class="cpu-controller__progress-tabs">
                                        <el-button
                                            size="small"
                                            :type="!showTestLog ? 'primary' : ''"
                                            @click="showTestLog = false"
                                            class="h-8 text-xs"
                                        >
                                            进度视图
                                        </el-button>
                                        <el-button
                                            size="small"
                                            :type="showTestLog ? 'primary' : ''"
                                            @click="showTestLog = true"
                                            class="h-8 text-xs"
                                        >
                                            测试日志
                                        </el-button>
                                    </div>
                                </div>

                                <!-- 进度条 -->
                                <div class="mb-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm theme-text-secondary">总体进度</span>
                                        <span class="text-sm font-mono font-semibold theme-text-primary">{{ Math.round(testProgress) }}%</span>
                                    </div>
                                    <div class="custom-progress">
                                        <div class="custom-progress-bar cpu-controller__progress-bar" :style="progressBarStyle"></div>
                                    </div>
                                </div>

                                <!-- 进度视图 -->
                                <div v-if="!showTestLog">
                                    <div class="cpu-controller__progress-stats">
                                        <div class="cpu-controller__stat-card cpu-controller__stat-card--success">
                                            <div class="cpu-controller__stat-value text-green-600">{{ passedTests }}</div>
                                            <div class="cpu-controller__stat-label text-green-600">通过</div>
                                        </div>
                                        <div class="cpu-controller__stat-card cpu-controller__stat-card--danger">
                                            <div class="cpu-controller__stat-value text-red-600">{{ failedTests }}</div>
                                            <div class="cpu-controller__stat-label text-red-600">失败</div>
                                        </div>
                                        <div class="cpu-controller__stat-card cpu-controller__stat-card--neutral">
                                            <div class="cpu-controller__stat-value text-gray-600">{{ totalTests - passedTests - failedTests }}</div>
                                            <div class="cpu-controller__stat-label text-gray-600">待测</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 测试日志视图 - 水平布局 -->
                                <div v-else class="cpu-controller__log-layout">
                                    <!-- 左侧统计信息 (20%) -->
                                    <div class="cpu-controller__log-stats">
                                        <!-- 通过 -->
                                        <div class="cpu-controller__compact-stat cpu-controller__compact-stat--success">
                                            <span>通过</span>
                                            <span>{{ passedTests }}</span>
                                        </div>
                                        
                                        <!-- 失败 -->
                                        <div class="cpu-controller__compact-stat cpu-controller__compact-stat--danger">
                                            <span>失败</span>
                                            <span>{{ failedTests }}</span>
                                        </div>
                                        
                                        <!-- 待测 -->
                                        <div class="cpu-controller__compact-stat cpu-controller__compact-stat--neutral">
                                            <span>待测</span>
                                            <span>{{ totalTests - passedTests - failedTests }}</span>
                                        </div>
                                        
                                        <!-- 结果 -->
                                        <div :class="[
                                            'cpu-controller__compact-stat',
                                            overallResult === 'PASS' ? 'cpu-controller__compact-stat--success' :
                                            overallResult === 'NG' ? 'cpu-controller__compact-stat--danger' :
                                            'cpu-controller__compact-stat--neutral'
                                        ]">
                                            <span>结果</span>
                                            <span>{{ overallResult || '--' }}</span>
                                        </div>
                                    </div>
                                    
                                    <!-- 右侧测试日志 (80%) -->
                                    <div class="cpu-controller__log-content">
                                        <div class="cpu-controller__log-controls">
                                            <div class="cpu-controller__log-indicators">
                                                <span class="text-gray-500">测试日志</span>
                                                <!-- 日志级别过滤按钮 -->
                                                <div 
                                                    v-for="level in logConfig.levels" 
                                                    :key="level"
                                                    class="cpu-controller__log-indicator cursor-pointer"
                                                    @click="toggleLogLevel(level)"
                                                    :class="{ 'opacity-50': !logConfig.enabledLevels.value.includes(level) }"
                                                >
                                                    <div :class="['cpu-controller__log-dot', \`cpu-controller__log-dot--\${level}\`]"></div>
                                                    <span :class="[
                                                        level === 'success' ? 'text-green-600' :
                                                        level === 'error' ? 'text-red-600' :
                                                        level === 'warning' ? 'text-yellow-600' :
                                                        level === 'info' ? 'text-blue-600' :
                                                        'text-purple-600'
                                                    ]">{{ level.toUpperCase() }}</span>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <el-button
                                                    size="small"
                                                    @click="autoScroll = !autoScroll"
                                                    class="h-6 px-2 text-xs"
                                                >
                                                    {{ autoScroll ? '🔒 自动滚动' : '🔓 手动滚动' }}
                                                </el-button>
                                                <el-button 
                                                    size="small" 
                                                    @click="exportLogs" 
                                                    class="h-6 px-2 text-xs"
                                                    :disabled="filteredLogs.length === 0"
                                                >
                                                    导出
                                                </el-button>
                                                <el-button size="small" @click="clearTestLogs" class="h-6 px-2 text-xs">
                                                    清空
                                                </el-button>
                                            </div>
                                        </div>

                                        <div id="test-log-container" class="flex-1 log-container rounded-lg p-3 overflow-y-auto text-xs">
                                            <div v-if="filteredLogs.length === 0" class="flex items-center justify-center h-full text-gray-500">
                                                <div class="text-center">
                                                    <div class="text-2xl mb-2">📋</div>
                                                    <div>{{ testLogs.length === 0 ? '点击"自动测试"开始记录日志' : '当前过滤条件下无日志' }}</div>
                                                </div>
                                            </div>
                                            <div v-else class="space-y-1">
                                                <div
                                                    v-for="log in filteredLogs"
                                                    :key="log.id"
                                                    class="flex items-start space-x-2"
                                                >
                                                    <span class="text-gray-400 shrink-0">{{ log.timestamp }}</span>
                                                    <span :class="['shrink-0 font-semibold', getLogLevelClass(log.level)]">
                                                        [{{ log.category }}]
                                                    </span>
                                                    <div class="flex-1 min-w-0">
                                                        <div :class="getLogMessageClass(log.level)">{{ log.message }}</div>
                                                        <div v-if="log.details" class="text-gray-400 text-xs mt-1 ml-2">
                                                            └─ {{ log.details }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 测试项目卡片 -->
                        <div class="cpu-controller__card theme-card glass-effect card-hover">
                            <div class="cpu-controller__card-content">
                                <div class="cpu-controller__progress-header">
                                    <div class="cpu-controller__card-title">
                                        <div class="cpu-controller__icon cpu-controller__icon--indigo">
                                            <i data-lucide="layers" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="cpu-controller__title-section theme-text-primary">测试项目</span>
                                    </div>
                                    <div class="cpu-controller__test-actions">
                                        <el-button size="small" @click="clearAllResults" :disabled="testRunning" class="h-8">
                                            <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                                            清除
                                        </el-button>
                                        <el-button size="small" @click="setAllPass" :disabled="testRunning" class="h-8 set-all-pass-btn">
                                            <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                            全通过
                                        </el-button>
                                    </div>
                                </div>

                                <div class="cpu-controller__test-items">
                                    <div
                                        v-for="(item, index) in enabledTestItems"
                                        :key="item.name"
                                        :class="[
                                            'cpu-controller__test-item test-item-bg',
                                            currentTestIndex === item.originalIndex ? 'cpu-controller__test-item--active active shadow-lg' : ''
                                        ]"
                                    >
                                        <div class="cpu-controller__test-item-info">
                                            <div :class="[
                                                'cpu-controller__test-item-icon',
                                                getTestItemIconClass(item.result)
                                            ]">
                                                <i :data-lucide="item.icon" class="w-4 h-4"></i>
                                            </div>
                                            <div class="cpu-controller__test-item-details">
                                                <div class="cpu-controller__test-item-name">{{ item.name }}</div>
                                                <div class="cpu-controller__test-item-meta">
                                                    <span>{{ item.category }}</span>
                                                    <span v-if="item.duration">• {{ item.duration }}ms</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="cpu-controller__test-item-actions">
                                            <!-- Testing State -->
                                            <div v-if="item.result === 'testing'" class="cpu-controller__test-item-status">
                                                <i data-lucide="loader-2" class="w-4 h-4 animate-spin text-blue-600"></i>
                                                <span class="text-xs text-blue-600 font-medium">测试中...</span>
                                            </div>

                                            <!-- Pass State -->
                                            <el-tag v-else-if="item.result === 'pass'" type="success" size="small">
                                                <i data-lucide="check-circle"></i>
                                                <span>通过</span>
                                            </el-tag>

                                            <!-- Fail State -->
                                            <el-tag v-else-if="item.result === 'fail'" type="danger" size="small">
                                                <i data-lucide="x-circle"></i>
                                                <span>失败</span>
                                            </el-tag>

                                            <!-- Pending State -->
                                            <el-tag v-else type="info" size="small">
                                                <i data-lucide="clock" style="vertical-align: middle;"></i>
                                                <span style="vertical-align: middle;">待测</span>
                                            </el-tag>

                                            <!-- Action Buttons -->
                                            <!-- 自动测试M区控制的测试项目：显示锁定状态 -->
                                            <div v-if="(configManager && configManager.getAutoTestMAreaControlInfo(item.originalIndex, selectedProductType) && mAreaTestCompleted)" class="cpu-controller__test-item-locked">
                                                <el-tag size="small" type="warning" class="text-xs">
                                                    <i data-lucide="lock" class="w-3 h-3 mr-1"></i>
                                                    自动测试控制
                                                </el-tag>
                                            </div>
                                            <!-- 视觉检测M区控制的测试项目：显示锁定状态 -->
                                            <div v-else-if="(configManager && configManager.getVisualTestMAreaControlInfo(item.originalIndex, selectedProductType) && visualTestCompleted)" class="cpu-controller__test-item-locked">
                                                <el-tag size="small" type="success" class="text-xs">
                                                    <i data-lucide="eye" class="w-3 h-3 mr-1"></i>
                                                    视觉检测控制
                                                </el-tag>
                                            </div>
                                            <!-- 非M区控制或未锁定：显示手动按钮 -->
                                            <div v-else-if="!testRunning && item.result !== 'testing'" class="cpu-controller__test-item-buttons">
                                                <!-- 显示手动按钮 -->
                                                <div class="flex space-x-1">
                                                    <el-button
                                                        size="small"
                                                        :type="item.result === 'pass' ? 'success' : ''"
                                                        @click="setTestResult(item.originalIndex, 'pass')"
                                                        class="h-7 px-3 text-xs"
                                                    >
                                                        通过
                                                    </el-button>
                                                    <el-button
                                                        size="small"
                                                        :type="item.result === 'fail' ? 'danger' : ''"
                                                        @click="setTestResult(item.originalIndex, 'fail')"
                                                        class="h-7 px-3 text-xs"
                                                    >
                                                        失败
                                                    </el-button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 确认对话框 -->
        <el-dialog 
            v-model="showConfirmDialog" 
            :title="confirmAction?.title"
            width="400px"
            center
        >
            <div class="flex items-start space-x-3">
                <i data-lucide="alert-triangle" class="w-5 h-5 text-amber-500 mt-0.5"></i>
                <p class="text-gray-600">{{ confirmAction?.description }}</p>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showConfirmDialog = false">取消</el-button>
                    <el-button 
                        :type="confirmAction?.variant === 'destructive' ? 'danger' : 'primary'" 
                        @click="executeConfirmAction"
                    >
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>
        `
    };

    // 创建并挂载Vue应用
    const app = createApp(CPUControllerVueApp);
    app.use(ElementPlus);

    // 注册Element Plus图标
    if (window.ElementPlusIconsVue) {
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
    }

    // 挂载应用
    const mountApp = () => {
        try {
            // 先检查容器是否已经有应用挂载
            const container = document.getElementById('cpu-controller-vue-app-container');
            if (!container) {
                Logger.error('CPU Controller Vue app container not found');
                return;
            }
            
            // 清空容器内容，确保没有之前的Vue应用
            container.innerHTML = '';
            
            app.mount('#cpu-controller-vue-app-container');
            Logger.log('CPU Controller Vue app mounted successfully');
            
            // 保存应用实例供清理使用
            window.currentCPUControllerVueApp = app;
        } catch (error) {
            Logger.error('Failed to mount CPU Controller Vue app:', error);
        }
    };

    // 直接挂载，不需要等待DOM加载完成
    mountApp();
})(); 