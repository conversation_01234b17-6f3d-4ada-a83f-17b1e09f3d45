/* 
 * 公共动画系统 - 基于CouplerVue.css标准
 * 命名空间: module-* 避免动画冲突
 * 版本: v1.0
 * 最后更新: 2024-12-19
 */

/* === 模块级卡片悬停动画 - 基于CouplerVue标准 === */
.module-card-hover-effect {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.module-card-hover-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s ease;
    pointer-events: none;
    z-index: 1;
}

.module-card-hover-effect:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--module-card-hover-shadow, rgba(59, 130, 246, 0.3));
    border-color: var(--module-card-hover-border, rgba(59, 130, 246, 0.4));
}

.module-card-hover-effect:hover::before {
    left: 100%;
}

/* === 模块级按钮3D交互动画 === */
.module-button-3d-effect {
    position: relative !important;
    overflow: hidden !important;
    transform-style: preserve-3d !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.module-button-3d-effect:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 8px 25px -8px var(--module-card-hover-shadow, rgba(59, 130, 246, 0.3)) !important;
}

.module-button-3d-effect:active {
    transform: translateY(1px) scale(0.98) !important;
    transition: all 0.1s ease !important;
}

/* === 模块级进度条动画系统 === */
.module-progress-container {
    height: 16px;
    border-radius: 8px;
    background: linear-gradient(90deg, 
        rgba(59, 130, 246, 0.1), 
        rgba(139, 92, 246, 0.1)
    );
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.module-progress-bar {
    height: 100%;
    border-radius: 8px;
    background: linear-gradient(90deg, 
        var(--module-accent-cyan, #00f5ff), 
        #3b82f6, 
        var(--module-accent-purple, #8b5cf6)
    );
    position: relative;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.module-progress-bar::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: module-shimmer 2.5s ease-in-out infinite;
    border-radius: 8px;
    z-index: 1;
}

/* === 模块级状态指示器动画 === */
.module-status-indicator {
    position: relative;
}

.module-status-indicator::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    animation: module-ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.module-status-connected::after {
    background: #10b981;
    opacity: 0.75;
}

.module-status-disconnected::after {
    background: #ef4444;
    opacity: 0.75;
}

/* === 模块级折叠动画 === */
.module-collapse-enter-active,
.module-collapse-leave-active {
    transition: all 0.5s ease;
    overflow: hidden;
}

.module-collapse-enter-from,
.module-collapse-leave-to {
    max-height: 0;
    opacity: 0;
}

.module-collapse-enter-to,
.module-collapse-leave-from {
    max-height: 1000px;
    opacity: 1;
}

/* === 模块级玻璃效果 === */
.module-glass-effect {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* === 模块级背景渐变效果 === */
.module-gradient-bg {
    background: var(--module-bg-primary);
    position: relative;
    overflow: hidden;
    height: 100vh;
}

.module-gradient-bg::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--module-bg-overlay);
    pointer-events: none;
}

/* === 模块级脉冲动画 === */
.module-pulse-effect {
    animation: module-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* === 关键帧动画定义 === */
@keyframes module-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes module-ping {
    75%, 100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes module-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

@keyframes module-fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* === 动画类映射系统 - 保持现有类名，避免破坏性修改 === */

/* 通用卡片悬停动画映射 - 复制完整样式 */
.card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s ease;
    pointer-events: none;
    z-index: 1;
}

.card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--module-card-hover-shadow, rgba(59, 130, 246, 0.3));
    border-color: var(--module-card-hover-border, rgba(59, 130, 246, 0.4));
}

.card-hover:hover::before {
    left: 100%;
}

/* 玻璃效果映射 */
.glass-effect {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 背景渐变映射 */
.gradient-bg {
    background: var(--module-bg-primary);
    position: relative;
    overflow: hidden;
    height: 100vh;
}

.gradient-bg::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--module-bg-overlay);
    pointer-events: none;
}

/* 状态指示器映射 */
.status-indicator {
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    animation: module-ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.status-connected::after {
    background: #10b981;
    opacity: 0.75;
}

.status-disconnected::after {
    background: #ef4444;
    opacity: 0.75;
}

/* 折叠动画映射 */
.collapse-enter-active,
.collapse-leave-active {
    transition: all 0.5s ease;
    overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
    max-height: 0;
    opacity: 0;
}

.collapse-enter-to,
.collapse-leave-from {
    max-height: 1000px;
    opacity: 1;
}

/* 进度条样式映射 */
.custom-progress {
    height: 16px;
    border-radius: 8px;
    background: linear-gradient(90deg, 
        rgba(59, 130, 246, 0.1), 
        rgba(139, 92, 246, 0.1)
    );
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-progress-bar {
    height: 100%;
    border-radius: 8px;
    background: linear-gradient(90deg, 
        var(--module-accent-cyan, #00f5ff), 
        #3b82f6, 
        var(--module-accent-purple, #8b5cf6)
    );
    position: relative;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.custom-progress-bar::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: module-shimmer 2.5s ease-in-out infinite;
    border-radius: 8px;
    z-index: 1;
}

/* 脉冲动画映射 */
.animate-pulse-dot {
    animation: module-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* === 模块级基础过渡动画 === */
.module-base-transition {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* === 基础过渡映射 - 适用于所有基础组件 === */
.module-card,
.module-toolbar,
.module-test-item,
.module-stat-card,
.module-icon,
.module-button {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
} 