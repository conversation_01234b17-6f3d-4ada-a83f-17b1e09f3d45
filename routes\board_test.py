from flask import Blueprint, jsonify, request

board_test_bp = Blueprint('board_test', __name__)

@board_test_bp.route('/start-test', methods=['POST'])
def start_test():
    data = request.get_json()
    # 处理单板测试启动逻辑
    return jsonify({'status': 'success'})

@board_test_bp.route('/get-test-results', methods=['GET'])
def get_test_results():
    # 获取测试结果
    return jsonify({
        'status': 'success',
        'results': {
            'rs485_1': 'pass',
            'rs485_2': 'pass',
            'can': 'fail'
        }
    })
