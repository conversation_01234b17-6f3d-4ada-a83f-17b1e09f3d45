function createBasicInfoForm() {
    return `
        <div class="basic-info-card">
            <h2>基本信息</h2>
            <div class="form-grid">
                <!-- 第一行：测试人员、测试时间、加工单号、生产数量 -->
                <div class="form-row full-width">
                    <div class="form-group required">
                        <label for="tester">测试人员</label>
                        <input id="tester" readonly>
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label for="testTime">测试时间</label>
                        <input id="testTime" type="datetime-local">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group required">
                        <label for="orderNumber">加工单号</label>
                        <input id="orderNumber" placeholder="输入加工单号">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group required">
                        <label for="productionQuantity">生产数量</label>
                        <input id="productionQuantity" type="number" min="0" placeholder="输入生产数量" oninput="this.value = this.value < 0 ? 0 : this.value">
                        <div class="error-message"></div>
                    </div>
                </div>
                
                <!-- 第二行：产品编码、产品型号、产品状态、产品批次号 -->
                <div class="form-row full-width">
                    <div class="form-group required col-1">
                        <label for="productCode">产品编码</label>
                        <input id="productCode" placeholder="输入产品编码">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group required col-2">
                        <label for="productModel">产品型号</label>
                        <input id="productModel" placeholder="输入产品型号">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group required">
                        <label for="productStatus">产品状态</label>
                        <select id="productStatus">
                            <option value="">选择产品状态</option>
                            <option value="new">新品</option>
                            <option value="used">维修</option>
                            <option value="refurbished">返工</option>
                        </select>
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label for="batchNumber">产品批次号</label>
                        <input id="batchNumber" placeholder="输入产品批次号">
                        <div class="error-message"></div>
                    </div>
                </div>
                
                <!-- 第三行：产品SN号、产品SN号位数、备注 -->
                <div class="form-row full-width">
                    <div class="form-group required col-1">
                        <label for="productSN">产品SN号</label>
                        <input id="productSN" placeholder="输入产品SN号">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group required col-2">
                        <label for="snByteCount">产品SN号位数</label>
                        <input id="snByteCount" type="number" min="0" placeholder="输入SN号位数" oninput="this.value = this.value < 0 ? 0 : this.value">
                        <div class="error-message"></div>
                    </div>
                    <div class="form-group">
                        <label for="remarks">备注</label>
                        <input id="remarks" placeholder="输入备注">
                        <div class="error-message"></div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 添加一个新的函数来处理表单的响应式布局
function handleFormResponsiveness() {
    const formGrid = document.querySelector('.form-grid');
    if (formGrid) {
        if (window.innerWidth <= 768) {
            formGrid.style.gridTemplateColumns = '1fr';
        } else {
            formGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
        }
    }
}

// 在窗口大小改变时调用此函数
window.addEventListener('resize', handleFormResponsiveness);

// 添加表单验证函数
function validateField(field, fieldName) {
    const value = field.value.trim();
    const formGroup = field.closest('.form-group');
    const errorMessage = formGroup.querySelector('.error-message');
    
    if (formGroup.classList.contains('required') && !value) {
        formGroup.classList.add('error');
        errorMessage.textContent = `${fieldName}不能为空`;
        return false;
    }
    
    // 特殊字段的验证
    if (field.id === 'productSN') {
        const snByteCount = parseInt(document.getElementById('snByteCount').value);
        if (!isNaN(snByteCount) && value.length !== snByteCount) {
            formGroup.classList.add('error');
            errorMessage.textContent = `产品SN号长度必须为${snByteCount}位`;
            return false;
        }
    }
    
    // 验证通过，清除错误状态
    formGroup.classList.remove('error');
    errorMessage.textContent = '';
    return true;
}

// 添加实时验证
function setupFormValidation() {
    const form = document.getElementById('testForm');
    if (!form) return;

    // 获取基本信息卡片中的所有输入框和选择框
    const basicInfoInputs = form.querySelectorAll('.basic-info-card input, .basic-info-card select');
    
    // 为每个输入框添加值变化检测
    basicInfoInputs.forEach(input => {
        // 初始检查值状态
        if (input.value && input.value.trim()) {
            input.classList.add('input-has-value');
        }

        // 添加输入事件监听
        input.addEventListener('input', function() {
            if (this.value.trim()) {
                this.classList.add('input-has-value');
            } else {
                this.classList.remove('input-has-value');
            }
        });

        // 添加change事件监听（主要用于select元素）
        input.addEventListener('change', function() {
            if (this.value.trim()) {
                this.classList.add('input-has-value');
            } else {
                this.classList.remove('input-has-value');
            }
        });
    });

    // 保持原有的必填字段验证逻辑
    const requiredFields = {
        'tester': '测试人员',
        'orderNumber': '加工单号',
        'productionQuantity': '生产数量',
        'productCode': '产品编码',
        'productModel': '产品型号',
        'productStatus': '产品状态',
        'productSN': '产品SN号',
        'snByteCount': '产品SN号位数'
    };

    // 为所有必填字段添加验证
    Object.entries(requiredFields).forEach(([fieldId, fieldName]) => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', () => {
                validateField(field, fieldName);
            });

            field.addEventListener('input', () => {
                const formGroup = field.closest('.form-group');
                if (formGroup.classList.contains('error')) {
                    formGroup.classList.remove('error');
                    formGroup.querySelector('.error-message').textContent = '';
                }
            });
        }
    });

    // 特殊处理SN号字节数和SN号的关联验证
    const snByteCount = document.getElementById('snByteCount');
    const productSN = document.getElementById('productSN');
    if (snByteCount && productSN) {
        snByteCount.addEventListener('change', () => {
            if (productSN.value) {
                validateField(productSN, '产品SN号');
            }
        });
    }
}

// 在页面加载时初始化表单验证
document.addEventListener('DOMContentLoaded', () => {
    setupFormValidation();
    handleFormResponsiveness();
});

// 修改 createBasicInfoForm 函数，在恢复保存的值时添加样式
function restoreFormValues(savedValues) {
    if (!savedValues) return;

    Object.entries(savedValues).forEach(([key, value]) => {
        const element = document.getElementById(key);
        if (element && value) {
            element.value = value;
            if (value.trim()) {
                element.classList.add('input-has-value');
            }
        }
    });
}
