from flask import Blueprint, jsonify, request
from database.db_manager import DatabaseManager
from services.assembly_service import AssemblyService
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)
batch_query_bp = Blueprint('batch_query', __name__)

@batch_query_bp.route('/search', methods=['GET'])
def search_batch():
    try:
        shell_sn = request.args.get('shellSN')
        order_number = request.args.get('orderNumber')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        
        # 添加排序参数
        sort_field = request.args.get('sortField')
        sort_order = request.args.get('sortOrder', 'asc')
        
        if not shell_sn and not order_number and not start_date and not end_date:
            return jsonify({
                'success': False,
                'message': '请提供外壳SN号、工单号或日期范围'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            service = AssemblyService(session)
            
            # 处理日期范围查询
            if start_date or end_date:
                try:
                    # 转换日期字符串为datetime对象，并设置时间秒
                    if start_date:
                        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                        start_datetime = start_datetime.replace(hour=0, minute=0, second=0)
                    else:
                        start_datetime = None

                    if end_date:
                        end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                        end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                    else:
                        end_datetime = None

                    # 调用service层的日期范围查询方法
                    results = service.search_by_date_range(start_datetime, end_datetime)
                except ValueError as e:
                    return jsonify({
                        'success': False,
                        'message': f'日期格式错误：{str(e)}'
                    }), 400

            # 根据查询参数选择查询方法
            elif order_number:
                results = service.search_by_order_number(order_number)
            else:
                # 单个SN查询逻辑保持不变
                result = service.search_assembly(shell_sn)
                if not result:
                    return jsonify({
                        'success': False,
                        'message': '未找到相关信息'
                    }), 404
                results = [result]

            if not results:
                return jsonify({
                    'success': False,
                    'message': '未找到相关信息'
                }), 404
                
            # 格式化返回数据
            response_data = []
            for result in results:
                response_data.append({
                    'orderNumber': result.get('board_processing_order'),
                    'productModel': result.get('board_product_model'),
                    'productCode': result.get('complete_procode'),
                    'status': '已绑定外壳' if result.get('has_shell') else '未绑定外壳',
                    'createdAt': result.get('created_at'),
                    'tester': result.get('board_tester_name'),
                    'boardA': result.get('board_a_sn'),
                    'boardB': result.get('board_b_sn'),
                    'boardC': result.get('board_c_sn'),
                    'boardD': result.get('board_d_sn'),
                    'shellSN': result.get('product_sn'),
                    'boardTime': result.get('created_at'),
                    'shellTime': result.get('assembled_at'),
                    'complete_tester_name': result.get('complete_tester_name'),
                    'complete_product_remark': result.get('complete_product_remark'),
                    'complete_product_quantity': result.get('complete_product_quantity')
                })
            
            # 如果有排序字段，对结果进行排序
            if sort_field:
                # 映射前端字段名到数据结构中的字段名
                field_mapping = {
                    'orderNumber': 'orderNumber',
                    'productModel': 'productModel',
                    'productCode': 'productCode',
                    'status': 'status',
                    'serialNumber': 'shellSN',
                    'tester': 'tester',
                    'createdAt': 'shellTime',
                    'boardTime': 'boardTime',
                    'shellTime': 'shellTime'
                }
                
                actual_field = field_mapping.get(sort_field)
                
                if actual_field:
                    logger.info(f"正在根据 {actual_field} 字段 {sort_order} 排序")
                    reverse = sort_order.lower() == 'desc'
                    
                    # 特殊处理日期字段
                    if actual_field in ['shellTime', 'boardTime', 'createdAt']:
                        # 对于可能为None的日期字段，使用空字符串替代以避免排序错误
                        response_data.sort(key=lambda x: x.get(actual_field) or '', reverse=reverse)
                    else:
                        # 常规字段排序
                        response_data.sort(key=lambda x: x.get(actual_field) or '', reverse=reverse)
                else:
                    logger.warning(f"未知的排序字段: {sort_field}")
            
            return jsonify({
                'success': True,
                'data': response_data
            })
            
    except Exception as e:
        logger.error("查询失败: %s", str(e), exc_info=True)
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500 