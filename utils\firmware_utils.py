# utils/firmware_utils.py
import hashlib
import os
from werkzeug.utils import secure_filename
from datetime import datetime
import logging

# 导入配置
from config import FIRMWARE_UPLOAD_BASE_PATH

logger = logging.getLogger(__name__)

class FirmwareUtils:
    """固件工具类"""
    
    ALLOWED_EXTENSIONS = {'.bin', '.hex', '.zip', '.tar.gz', '.rar', '.7z'}
    MAX_FILE_SIZE = 500 * 1024 * 1024  # 500MB
    
    @staticmethod
    def validate_file(file):
        """验证上传文件"""
        try:
            if not file or not file.filename:
                return False, "请选择文件"
            
            # 检查文件扩展名
            filename = secure_filename(file.filename)
            ext = os.path.splitext(filename)[1].lower()
            if ext not in FirmwareUtils.ALLOWED_EXTENSIONS:
                return False, f"不支持的文件格式，支持格式: {', '.join(FirmwareUtils.ALLOWED_EXTENSIONS)}"
            
            # 检查文件大小
            file.seek(0, os.SEEK_END)
            size = file.tell()
            file.seek(0)
            if size > FirmwareUtils.MAX_FILE_SIZE:
                return False, f"文件大小超限，最大支持 {FirmwareUtils.MAX_FILE_SIZE // 1024 // 1024}MB"
            
            return True, "验证通过"
            
        except Exception as e:
            logger.error(f"文件验证失败: {str(e)}")
            return False, "文件验证失败"
    
    @staticmethod
    def calculate_file_hash(file_path):
        """计算文件SHA256哈希值"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {str(e)}")
            return None
    
    @staticmethod
    def generate_file_path(serial_number, original_filename):
        """生成文件存储路径"""
        try:
            now = datetime.now()
            year = now.strftime('%Y')
            month = now.strftime('%m')
            
            # 构建存储路径: /var/data/firmware/年/月/流水号/文件名
            relative_path = f"{year}/{month}/{serial_number}"
            full_dir = os.path.join(FIRMWARE_UPLOAD_BASE_PATH, relative_path)
            
            # 确保目录存在
            os.makedirs(full_dir, exist_ok=True)
            
            filename = secure_filename(original_filename)
            file_path = os.path.join(full_dir, filename)
            
            # 返回绝对路径和相对路径（相对于FIRMWARE_UPLOAD_BASE_PATH）
            return file_path, relative_path + "/" + filename
            
        except Exception as e:
            logger.error(f"生成文件路径失败: {str(e)}")
            return None, None
    
    @staticmethod
    def save_uploaded_file(file, serial_number):
        """保存上传的文件"""
        try:
            logger.info(f"开始保存文件: {file.filename}, 流水号: {serial_number}")
            
            # 验证文件
            is_valid, message = FirmwareUtils.validate_file(file)
            if not is_valid:
                logger.error(f"文件验证失败: {message}")
                raise Exception(message)
            
            logger.info("文件验证通过")
            
            # 生成文件路径
            file_path, relative_path = FirmwareUtils.generate_file_path(
                serial_number, file.filename
            )
            
            if not file_path:
                logger.error("生成文件路径失败")
                raise Exception("生成文件路径失败")
            
            logger.info(f"生成文件路径: {file_path}")
            
            # 保存文件
            file.save(file_path)
            logger.info("文件保存完成")
            
            # 计算文件哈希
            file_hash = FirmwareUtils.calculate_file_hash(file_path)
            if not file_hash:
                logger.error("计算文件哈希失败")
                raise Exception("计算文件哈希失败")
            
            logger.info(f"文件哈希计算完成: {file_hash}")
            
            # 获取文件大小（MB）
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            file_size_rounded = round(file_size, 2)
            
            logger.info(f"文件大小: {file_size_rounded}MB")
            
            result = (file_path, file_hash, file_size_rounded)
            logger.info(f"返回结果: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            raise Exception(f"保存文件失败: {str(e)}")
    
    @staticmethod
    def export_to_excel(data_list, sheet_name, filename=None):
        """导出数据到Excel"""
        try:
            import pandas as pd
            
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{sheet_name}_{timestamp}.xlsx"
            
            # 创建DataFrame
            df = pd.DataFrame(data_list)
            
            # 导出到Excel
            export_path = os.path.join("temp", filename)
            os.makedirs("temp", exist_ok=True)
            
            with pd.ExcelWriter(export_path, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 设置列宽
                worksheet = writer.sheets[sheet_name]
                for idx, col in enumerate(df.columns):
                    worksheet.set_column(idx, idx, max(len(col), 15))
            
            return export_path
            
        except ImportError:
            logger.error("缺少pandas依赖，无法导出Excel")
            return None
        except Exception as e:
            logger.error(f"导出Excel失败: {str(e)}")
            return None
    
    @staticmethod
    def validate_serial_number(serial_number):
        """验证ERP流水号格式"""
        try:
            if not serial_number or len(serial_number.strip()) == 0:
                return False, "ERP流水号不能为空"
            
            serial_number = serial_number.strip()
            
            if len(serial_number) > 20:
                return False, "ERP流水号长度不能超过20位"
            
            # 检查是否包含特殊字符（根据实际需求调整）
            import re
            if not re.match(r'^[A-Za-z0-9_-]+$', serial_number):
                return False, "ERP流水号只能包含字母、数字、下划线和短横线"
            
            return True, "验证通过"
            
        except Exception as e:
            logger.error(f"流水号验证失败: {str(e)}")
            return False, "流水号验证失败"
    
    @staticmethod
    def validate_firmware_data(data):
        """验证固件数据"""
        try:
            required_fields = ['serialNumber', 'name', 'version', 'developer', 'description']
            
            # 检查必要字段
            for field in required_fields:
                if field not in data or not data[field] or len(str(data[field]).strip()) == 0:
                    return False, f"字段 {field} 不能为空"
            
            # 验证流水号
            is_valid, message = FirmwareUtils.validate_serial_number(data['serialNumber'])
            if not is_valid:
                return False, message
            
            # 验证版本号长度
            if len(data['version'].strip()) > 20:
                return False, "版本号长度不能超过20位"
            
            # 验证固件名称长度
            if len(data['name'].strip()) > 50:
                return False, "固件名称长度不能超过50位"
            
            # 验证研发者长度
            if len(data['developer'].strip()) > 20:
                return False, "研发者姓名长度不能超过20位"
            
            return True, "验证通过"
            
        except Exception as e:
            logger.error(f"固件数据验证失败: {str(e)}")
            return False, "数据验证失败"
    
    @staticmethod
    def format_file_size(size_bytes):
        """格式化文件大小显示"""
        try:
            if size_bytes == 0:
                return "0B"
            
            size_names = ["B", "KB", "MB", "GB"]
            import math
            i = int(math.floor(math.log(size_bytes, 1024)))
            p = math.pow(1024, i)
            s = round(size_bytes / p, 2)
            return f"{s} {size_names[i]}"
            
        except Exception as e:
            logger.error(f"格式化文件大小失败: {str(e)}")
            return "未知大小"
    
    @staticmethod
    def get_file_extension(filename):
        """获取文件扩展名"""
        try:
            return os.path.splitext(filename)[1].lower()
        except Exception as e:
            logger.error(f"获取文件扩展名失败: {str(e)}")
            return ""

    @staticmethod
    def paginate_query(query, page, per_page):
        """分页查询工具函数"""
        try:
            total = query.count()
            offset = (page - 1) * per_page
            items = query.offset(offset).limit(per_page).all()
            
            return {
                'items': items,
                'total': total,
                'page': page,
                'per_page': per_page,
                'total_pages': (total + per_page - 1) // per_page
            }
        except Exception as e:
            raise Exception(f"分页查询失败: {str(e)}")

def create_response(success, message, data=None, code=200):
    """统一响应格式"""
    response = {
        'success': success,
        'message': message
    }
    if data is not None:
        response['data'] = data
    
    from flask import jsonify
    return jsonify(response), code

def handle_db_error(error):
    """统一数据库错误处理"""
    logger.error(f"数据库操作失败: {str(error)}")
    
    # 根据错误类型返回不同的响应
    if "IntegrityError" in str(type(error)):
        return create_response(False, "数据完整性错误，可能存在重复数据", code=400)
    elif "OperationalError" in str(type(error)):
        return create_response(False, "数据库操作错误", code=500)
    else:
        return create_response(False, "服务器内部错误", code=500)

def validate_datetime_range(start_date, end_date):
    """验证日期范围"""
    try:
        if start_date:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        else:
            start_dt = None
            
        if end_date:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        else:
            end_dt = None
            
        if start_dt and end_dt and start_dt > end_dt:
            return False, "开始日期不能大于结束日期"
            
        return True, None
        
    except ValueError as e:
        return False, "日期格式错误，应为 YYYY-MM-DD"
    except Exception as e:
        logger.error(f"日期验证失败: {str(e)}")
        return False, "日期验证失败" 