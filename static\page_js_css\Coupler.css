body {
    font-size: var(--font-size-base);
    font-family: Arial, sans-serif;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    background-color: #f9fafb;
}
.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 1rem;
    box-sizing: border-box;
}
h1 {
    font-size: clamp(var(--font-size-xl), 5vw, var(--font-size-4xl));
    font-weight: bold;
    margin-bottom: 1.5rem;
}
h2 {
    font-size: clamp(var(--font-size-lg), 3vw, var(--font-size-2xl));
    font-weight: 600;
    margin-bottom: 1rem;
}
.separator {
    border-top: 1px solid #e5e7eb;
    margin: 1.5rem 0;
}
.card-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    column-gap: 3.5rem;
    row-gap: 1.5rem;
    width: 100%;
}
.card {
    width: 100%;
    max-width: none;
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.0625rem 0.1875rem 0 rgba(0, 0, 0, 0.1), 0 0.0625rem 0.125rem 0 rgba(0, 0, 0, 0.06);
}
.card-header {
    background-color: #f9fafb;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}
.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin: 0;
}
.card-content {
    padding: 1rem;
    overflow-x: auto;
}
.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}
button {
    padding: 0.5rem 1rem;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: var(--font-size-sm);
}
button:hover {
    background-color: #e5e7eb;
}
table {
    min-width: 100%;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 0.5rem;
}
th, td {
    border: 1px solid #e5e7eb;
    padding: 0.75rem 1rem;
    text-align: left;
    background-color: #fff;
    white-space: nowrap;
}
thead th {
    background-color: #f3f4f6;
    border-bottom: 2px solid #d1d5db;
}
tbody tr:hover td {
    background-color: #f9fafb;
}
.submit-button {
    display: block;
    width: 100%;
    padding: 0.75rem;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: var(--font-size-base);
    margin-top: 1.5rem;
}
.submit-button:hover {
    background-color: #2563eb;
}
.full-width {
    grid-column: 1 / -1;
}
.checkbox-container {
    display: flex;
    align-items: center;
}
.checkbox-container input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
}

@media (min-width: 75rem) {
    .container {
        padding: 2rem;
    }
}

@media (max-width: 48rem) {
    .container {
        padding: 0.75rem;
    }
    
    h1 {
        font-size: var(--font-size-2xl);
        margin-bottom: 1rem;
    }
    
    h2 {
        font-size: var(--font-size-xl);
    }
    
    .card-grid {
        grid-template-columns: 1fr;
    }
    
    .card-content {
        padding: 0.75rem;
    }
    
    th, td {
        padding: 0.5rem 0.75rem;
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 30rem) {
    .container {
        padding: 0.5rem;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media print {
    .button-group,
    .submit-button {
        display: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    th, td {
        border-color: #000;
    }
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.25rem;
    font-size: var(--font-size-base);
}

.form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.version-info-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.version-hint {
    padding: 0.75rem;
    background-color: #f3f4f6;
    border-radius: 0.25rem;
    color: #4b5563;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

@media (max-width: 48rem) {
    .version-hint {
        text-align: center;
    }
    
    .form-row {
        flex-direction: column;
    }
}

.card.version-info-card {
    background: linear-gradient(135deg, #f8faff 0%, #e2e8f0 50%, #cbd5e1 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card.version-info-card .card-header {
    background: linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(241, 245, 249, 0.9));
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
    backdrop-filter: blur(8px);
}

.card.version-info-card .card-title {
    color: #334155;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.card.version-info-card .card-content {
    background: rgba(255, 255, 255, 0.7);
}

.card.version-info-card input,
.card.version-info-card select,
.card.version-info-card textarea {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.3);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.card.version-info-card input:focus,
.card.version-info-card select:focus,
.card.version-info-card textarea:focus {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.card.version-info-card label {
    color: #475569;
    font-weight: 500;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

.card.version-info-card .version-hint {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
}

/* 添加测试项目卡片样式 */
.card-content table {
    width: 100%;
    border-collapse: collapse;
}

.card-content th, 
.card-content td {
    border: 1px solid #e5e7eb;
    padding: 0.5rem;
    text-align: center;
    vertical-align: middle;
}

.card-content thead th {
    background-color: #f3f4f6;
    font-weight: 600;
}

.card-content tbody tr:hover {
    background-color: #f9fafb;
}

.card-content tbody tr {
    height: 40px;
}

.checkbox-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* 添加消息提示样式 */
.message-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 80vh;
    overflow-y: auto;
}

.message {
    position: relative;
    padding: 15px 40px 15px 20px;
    border-radius: 4px;
    color: white;
    animation: fadeIn 0.3s ease-out;
    max-width: 400px;
    min-width: 300px;
    word-wrap: break-word;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.message .close-message {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0 5px;
}

.message .close-message:hover {
    opacity: 0.8;
}

.message.success {
    background-color: rgba(76, 175, 80, 0.95);
}

.message.error {
    background-color: rgba(244, 67, 54, 0.95);
}

.message.info {
    background-color: rgba(33, 150, 243, 0.95);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

.message-overlay.active {
    display: block;
}

/* 添加 HSE 绿色输入框样式 */
.input-has-value {
    background-color: #E8F5E9 !important;  /* HSE绿色 */
    transition: background-color 0.3s ease;
}

/* 添加测试不通过的复选框样式 */
.checkbox-container.test-failed {
    background-color: rgba(252, 8, 8, 0.2); /* 红色背景，带透明度 */
    border-radius: 0.25rem;
    transition: background-color 0.3s ease;
}

/* 只读字段样式 - 专门针对版本信息卡片 */
.card.version-info-card .readonly-field {
    background-color: #aabcca !important; /* 灰蓝色背景 */
    color: #666 !important; /* 深灰色文字 */
    cursor: not-allowed !important; /* 禁用光标 */
    border: 1px solid #7f90f1 !important; /* 蓝紫色边框 */
    box-shadow: inset 0 2px 4px rgba(64, 165, 243, 0.2) !important; /* 蓝色内阴影 */
}

.card.version-info-card .readonly-field:focus {
    background-color: #bfd3e2 !important; /* 保持灰蓝色背景 */
    border-color: #7f90f1 !important; /* 保持蓝紫色边框 */
    box-shadow: inset 0 2px 4px rgba(64, 165, 243, 0.2) !important; /* 蓝色内阴影 */
}
