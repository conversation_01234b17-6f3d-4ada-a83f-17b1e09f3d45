# 测试日志存储方案 - 企业级事件日志归档设计

## 1. 背景与目标

当前，系统的测试日志（`addTestLog`）主要服务于前端UI，为测试人员提供实时的操作反馈。这类日志存在以下局限性：

- **临时性**: 日志随浏览器刷新或关闭而丢失，无法作为永久证据。
- **非结构化**: 日志是为人阅读设计的字符串，难以进行机器分析和数据挖掘。
- **功能单一**: 仅能用于实时查看，无法满足后续的质量审计、问题追溯和数据分析需求。

为了解决以上问题，我们需要设计一套**企业级的事件日志归档方案**，将每一次测试操作的核心信息作为永久记录，安全、高效地存储到后端数据库中。

**核心目标**:
- **永久归档**: 为每一次产品测试创建一个不可篡改的永久记录。
- **支持审计与追溯**: 当出现质量问题时，能快速回溯任何一次测试的完整上下文。
- **赋能数据分析**: 为后续的良率分析、故障趋势预测、性能瓶颈定位等提供高质量的数据源。
- **高性能与高可用**: 日志记录过程不能影响用户正常操作，且对系统性能影响降至最低。

## 2. 核心设计思想：事件日志 vs 过程日志

本方案的核心思想是明确区分两种不同目的的日志：

1.  **过程日志 (Process Log)**:
    - **定义**: 即当前UI上显示的日志。
    - **目的**: **实时反馈**，在操作过程中为用户提供即时信息。
    - **特点**: 面向人类阅读、非结构化、临时性。

2.  **事件日志 (Event Log)**:
    - **定义**: 本方案要设计的、存储在数据库中的日志。
    - **目的**: **结果归档**，为完成的业务事件创建一个永久的、结构化的记录。
    - **特点**: 面向机器解析、高度结构化、永久性。

我们将采用**"结果归档"**而非"过程记录"的原则，即：**只在一次完整的测试业务成功结束后，才触发一次性的、后台的、异步的日志归档操作。**

## 3. 技术方案详述

### 3.1 触发时机与前端实现

- **触发条件**: 在用户点击【提交测试】按钮，`submitForm` 函数成功收到后端（`/api/io-modulevue/submit-test`）的成功响应后。

- **实现方式**:
    1.  在 `submitForm` 函数的 `if (result.success)` 代码块内部，添加对一个新的日志记录函数的调用。
    2.  创建一个新的辅助函数 `logTestEventToDatabase()`。此函数负责组装数据并调用API。
    3.  这个调用必须是**异步**的，并且采用**"发射后不管" (fire and forget)** 的策略，即前端不需要等待日志API的返回结果，以免阻塞后续UI操作（如重置表单）。

#### 前端代码示例 (`IOModuleVue.js`):

```javascript
// ... existing code in submitForm ...

if (result.success) {
    ElMessage.success('测试信息提交成功！');
    addTestLog('success', 'SUBMIT', '测试数据提交成功', result.message || '数据已保存到数据库');

    // =================================================
    // ===          新增：调用事件日志归档           ===
    // =================================================
    logTestEventToDatabase('TEST_SUBMISSION_SUCCESS', '测试提交成功');
    
    // ... 后续的表单重置等操作 ...
}

// ...

// =================================================
// ===           新增：事件日志归档函数           ===
// =================================================
const logTestEventToDatabase = (eventType, message) => {
    // 1. 就地取材，组装结构化的JSON数据
    const eventDetails = {
        io_version: formData.ioVersion,
        io_build_date: formData.ioBuildDate,
        is_version_consistent: isVersionConsistent.value,
        auto_version_info: {
            version: formData.autoVersion,
            date: formData.autoDate
        },
        test_results: testItems.value.reduce((acc, item) => {
            acc[item.code] = item.result || '待测';
            return acc;
        }, {}),
        overall_result: overallResult.value,
        product_info: {
            model: formData.productModel,
            code: formData.productCode,
            status: formData.productStatus
        },
        remarks: formData.remarks
    };

    const logPayload = {
        module_name: 'IOModuleVue',
        event_type: eventType,
        tester: formData.tester,
        work_order: formData.orderNumber,
        product_sn: formData.productSN,
        message: message,
        details_json: JSON.stringify(eventDetails) // 确保发送的是字符串化的JSON
    };

    // 2. "发射后不管"的异步API调用
    fetch('/api/log/test-event', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(logPayload)
    }).catch(error => {
        // 在生产环境中，即使日志记录失败也不应打扰用户
        // 只在开发模式下打印错误，用于调试
        console.error('Failed to log test event to database:', error);
    });
};
```

### 3.2 后端API设计

- **API端点**: `POST /api/log/test-event`
- **请求体 (Body)**: 包含日志信息的JSON对象。
- **核心逻辑**:
    1.  接收前端发送的JSON数据。
    2.  进行基本的安全验证（如用户身份）。
    3.  将数据存入新创建的`test_event_logs`表中。
    4.  立即返回`200 OK`或`201 Created`，响应体可以为空，以尽快释放连接。

### 3.3 数据库表结构设计

为了存储这些结构化日志，推荐创建一个专用的数据表。

- **推荐表名**: `test_event_logs`
- **表结构**:

| 字段名 | 数据类型 | 约束/索引 | 说明 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT` | `PK`, `AI` | 日志唯一ID，主键，自增。 |
| `event_timestamp` | `DATETIME(3)` | `Default NOW(3)` | 事件时间戳，由数据库在插入时自动生成。 |
| `module_name` | `VARCHAR(50)` | | 产生日志的模块名 (如`IOModuleVue`)。 |
| `event_type` | `VARCHAR(50)` | | 事件类型 (如 `TEST_SUBMISSION_SUCCESS`)。 |
| `tester` | `VARCHAR(100)` | `INDEX` | 测试人员。 |
| `work_order` | `VARCHAR(100)`| `INDEX` | 关联的工单号，**关键索引字段**。 |
| `product_sn` | `VARCHAR(100)` | `INDEX` | 关联的产品SN，**关键索引字段**。 |
| `message` | `VARCHAR(255)`| | 对事件的简短文字描述。 |
| `details_json` | `JSON` | | **核心字段**，存储完整的、结构化的上下文信息。 |

**注意**: 强烈建议在 `work_order` 和 `product_sn` 字段上创建索引，这将极大地提升未来按工单和SN查询日志的性能。

## 4. 性能与可扩展性分析

### 4.1 性能影响
本方案对系统性能的影响**微乎其微**，主要得益于：

- **异步非阻塞**: 前端"发射后不管"，用户体验完全不受影响。
- **低频调用**: API只在每次成功提交后调用一次，频率极低。
- **轻量数据**: 单次传输的JSON数据量极小（通常为1-2 KB）。
- **高效数据库操作**: `INSERT`是数据库最高效的操作之一。即使在10个用户并发使用的场景下（约每分钟产生5-10次写入），对于现代数据库也毫无压力。

### 4.2 可扩展性
该方案具有良好的可扩展性：
- **模块扩展**: 可以轻松地将此日志记录逻辑复用到 `CouplerVue`、`CPUControllerVue` 等其他测试模块，只需在各自的提交成功逻辑中调用即可。
- **功能扩展**: 未来可以基于 `test_event_logs` 表开发丰富的数据看板和分析报表，例如：
    - 各产品型号的测试良率统计。
    - 特定时间段内的故障项排名。
    - 测试员工作量与效率分析。

## 5. 总结

本方案通过**分离过程日志与事件日志**，设计了一套在业务成功后**异步归档结构化数据**的机制。它以对现有系统**最小的性能影响**和**零代码侵入**为代价，实现了测试数据的**永久化、结构化、可追溯**，为未来的质量审计和大数据分析奠定了坚实的基础，是企业级应用日志管理的最佳实践。 