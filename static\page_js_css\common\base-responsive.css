/* 
 * 响应式设计系统 - 基于CouplerVue.css标准
 * 命名空间: module-* 避免响应式类污染
 * 版本: v1.0
 * 最后更新: 2024-12-19
 */

/* === 模块级滚动条样式优化 === */
.module-scrollbar-hidden {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.module-scrollbar-hidden::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
}

/* === 1400px 断点：大桌面屏幕优化 === */
@media (max-width: 1400px) {
    .module-grid-4 {
        grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
    
    .module-progress-stats {
        grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
}

/* === 1200px 断点：中等桌面屏幕 === */
@media (max-width: 1200px) {
    .module-grid-4,
    .module-grid-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
    
    .module-progress-stats {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
    
    /* 工具栏移动端优化 */
    .module-toolbar-container {
        margin-left: 1rem;
        margin-right: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .module-toolbar-left {
        justify-content: center;
    }
    
    .module-toolbar-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* === 768px 断点：平板设备 === */
@media (max-width: 768px) {
    /* 主要布局调整 */
    .module-main-content {
        flex-direction: column !important;
        height: auto !important;
    }
    
    .module-form-section,
    .module-test-section {
        width: 100% !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
    
    .module-test-section {
        border-left: none !important;
        border-top: 1px solid var(--module-toolbar-border) !important;
        padding-top: 1rem !important;
    }
    
    /* 网格系统移动端适配 */
    .module-grid-4,
    .module-grid-3,
    .module-grid-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
        gap: 0.75rem !important;
    }
    
    /* 统计卡片移动端适配 */
    .module-progress-stats {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        gap: 0.5rem !important;
    }
    
    /* 测试日志移动端适配 */
    .module-log-layout {
        flex-direction: column !important;
        height: auto !important;
        gap: 1rem;
    }
    
    .module-log-stats {
        width: 100% !important;
        display: grid !important;
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        gap: 0.5rem !important;
    }
    
    .module-log-content {
        width: 100% !important;
        height: 300px !important;
    }
    
    /* 测试项目移动端优化 */
    .module-test-item {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 1rem !important;
        padding: 1rem !important;
    }
    
    .module-test-item-actions {
        width: 100% !important;
        justify-content: space-between !important;
    }
    
    .module-test-item-buttons {
        gap: 0.25rem !important;
    }
    
    .module-test-item-buttons .el-button {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.5rem !important;
        height: auto !important;
    }
    
    /* 工具栏移动端完全重构 */
    .module-toolbar-container {
        margin-left: 0.5rem;
        margin-right: 0.5rem;
        padding: 1rem;
    }
    
    .module-toolbar-brand {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .module-toolbar-brand h1 {
        font-size: 1.125rem !important;
    }
    
    .module-toolbar-brand p {
        font-size: 0.75rem !important;
    }
    
    .module-toolbar-status {
        flex-direction: column;
        text-align: center;
        gap: 0.25rem;
    }
    
    .module-toolbar-actions {
        gap: 0.5rem;
    }
    
    .module-toolbar-actions .el-button {
        font-size: 0.75rem !important;
        padding: 0.5rem 0.75rem !important;
    }
    
    /* 卡片移动端优化 */
    .module-card-content,
    .module-form-content {
        padding: 1rem !important;
    }
    
    .module-card-title span {
        font-size: 1rem !important;
    }
    
    /* 页面滚动优化 */
    .module-main-container {
        overflow-y: auto !important;
        height: auto !important;
        min-height: 100vh !important;
    }
    
    /* 表单字段移动端优化 */
    .module-field-label {
        font-size: 0.8rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    /* 进度条移动端优化 */
    .custom-progress {
        height: 8px !important;
    }
    
    /* 统计卡片移动端优化 */
    .module-stat-card {
        padding: 0.75rem !important;
    }
    
    .module-stat-value {
        font-size: 1.25rem !important;
    }
    
    .module-stat-label {
        font-size: 0.6875rem !important;
    }
}

/* === 480px 断点：小屏手机 === */
@media (max-width: 480px) {
    /* 超小屏幕优化 */
    .module-toolbar-container {
        margin: 0.25rem;
        padding: 0.75rem;
    }
    
    .module-form-section,
    .module-test-section {
        padding: 0.75rem !important;
    }
    
    .module-card-content,
    .module-form-content {
        padding: 0.75rem !important;
    }
    
    .module-test-item {
        padding: 0.75rem !important;
    }
    
    /* 统计卡片改为单列 */
    .module-progress-stats {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }
    
    /* 日志统计改为单列 */
    .module-log-stats {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }
    
    /* 工具栏操作区域优化 */
    .module-toolbar-actions {
        flex-direction: column !important;
        width: 100%;
    }
    
    .module-toolbar-actions .el-button {
        width: 100% !important;
        justify-content: center !important;
    }
    
    /* 超小屏字体优化 */
    .module-toolbar-brand h1 {
        font-size: 1rem !important;
    }
    
    .module-card-title span {
        font-size: 0.875rem !important;
    }
    
    /* 测试项目按钮组优化 */
    .module-test-item-buttons {
        flex-direction: column !important;
        width: 100% !important;
        gap: 0.5rem !important;
    }
    
    .module-test-item-buttons .el-button {
        width: 100% !important;
        justify-content: center !important;
    }
}

/* === 模块级动画优化 - 移动端性能考虑 === */
@media (max-width: 768px) {
    /* 减少复杂动画以提升移动端性能 */
    .module-card:hover {
        transform: translateY(-2px) scale(1.01) !important;
    }
    
    .module-test-item:hover {
        transform: none !important;
    }
    
    /* 禁用卡片光扫效果以提升性能 */
    .card-hover::before {
        display: none !important;
    }
    
    /* 简化按钮悬停效果 */
    .module-button-theme .el-button:hover {
        transform: none !important;
    }
    
    .module-button-theme .el-button:active {
        transform: scale(0.98) !important;
    }
}

/* === 超宽屏幕优化（2K/4K显示器）=== */
@media (min-width: 1920px) {
    .module-grid-4 {
        grid-template-columns: repeat(6, minmax(0, 1fr)) !important;
    }
    
    .module-grid-3 {
        grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
    }
    
    .module-progress-stats {
        grid-template-columns: repeat(6, minmax(0, 1fr)) !important;
    }
    
    /* 超宽屏下的工具栏优化 */
    .module-toolbar-container {
        max-width: 1400px;
        margin-left: auto;
        margin-right: auto;
    }
}

/* === 打印样式优化 === */
@media print {
    .module-main-container {
        background: white !important;
        color: black !important;
    }
    
    .module-card {
        background: white !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }
    
    .module-toolbar-actions,
    .module-test-item-buttons {
        display: none !important;
    }
    
    .module-main-content {
        flex-direction: column !important;
        height: auto !important;
    }
    
    .module-form-section,
    .module-test-section {
        width: 100% !important;
        border: none !important;
        padding: 1rem 0 !important;
    }
} 