// 分页状态管理
function createPageState() {
    return {
        currentPage: 1,
        pageSize: 10,
        totalItems: 0,
        totalPages: 0,
        data: []
    };
}

// 更新分页控件
function updatePagination(pageState, onPageChange, onPageSizeChange) {
    const paginationControls = document.querySelector('.pagination-controls');
    let html = `
        <button class="page-button" onclick="changePage(${pageState.currentPage - 1}, ${onPageChange})" 
                ${pageState.currentPage === 1 ? 'disabled' : ''}>
            上一页
        </button>
    `;
    
    // 生成页码按钮
    for (let i = 1; i <= pageState.totalPages; i++) {
        if (
            i === 1 || 
            i === pageState.totalPages || 
            (i >= pageState.currentPage - 2 && i <= pageState.currentPage + 2)
        ) {
            html += `
                <button class="page-button ${i === pageState.currentPage ? 'active' : ''}" 
                        onclick="changePage(${i}, ${onPageChange})">
                    ${i}
                </button>
            `;
        } else if (
            i === pageState.currentPage - 3 || 
            i === pageState.currentPage + 3
        ) {
            html += `<span>...</span>`;
        }
    }
    
    html += `
        <button class="page-button" onclick="changePage(${pageState.currentPage + 1}, ${onPageChange})" 
                ${pageState.currentPage === pageState.totalPages ? 'disabled' : ''}>
            下一页
        </button>
        <div class="page-jump">
            <span>跳至</span>
            <input type="text" class="page-input" value="${pageState.currentPage}" 
                   onchange="jumpToPage(this.value, ${onPageChange}, ${pageState.totalPages})">
            <span>页</span>
        </div>
    `;
    
    paginationControls.innerHTML = html;
    
    // 更新每页显示数量的选择器
    const pageSizeSelect = document.querySelector('.page-size select');
    if (pageSizeSelect) {
        pageSizeSelect.value = pageState.pageSize;
        pageSizeSelect.onchange = (e) => onPageSizeChange(parseInt(e.target.value));
    }
}

// 页码切换函数
function changePage(page, onPageChange) {
    onPageChange(page);
}

// 跳转页面函数
function jumpToPage(page, onPageChange, totalPages) {
    const pageNum = parseInt(page);
    if (isNaN(pageNum)) return;
    onPageChange(Math.min(Math.max(1, pageNum), totalPages));
} 