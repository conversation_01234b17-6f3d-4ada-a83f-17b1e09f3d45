from flask import Blueprint, jsonify, request

document_center_bp = Blueprint('document_center', __name__)

@document_center_bp.route('/list', methods=['GET'])
def list_documents():
    # 获取文档列表
    return jsonify({
        'status': 'success',
        'data': [
            {'id': 1, 'name': '使用手册.pdf', 'type': 'pdf'},
            {'id': 2, 'name': '技术文档.docx', 'type': 'word'}
        ]
    })

@document_center_bp.route('/upload', methods=['POST'])
def upload_document():
    # 处理文档上传
    return jsonify({'status': 'success'})
