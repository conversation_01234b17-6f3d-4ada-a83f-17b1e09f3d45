# M区取值规范文档

**文档版本**: 1.0  
**创建日期**: 2025-07-23  
**适用系统**: CPU控制器多产品类型多M区动态控制测试系统  

## 概述

CPU控制器测试系统通过M区数据进行智能测试判断，支持6种产品类型配置，每种配置对应不同的测试项目组合和M区取值地址。系统支持单M区和多M区组合判断逻辑。

## 产品类型配置

### 1. All (全配置)
- **配置键**: `all_config`
- **描述**: 完整功能测试配置，包含所有14个测试项目
- **启用测试项目**: 14个
- **支持功能**: 自动测试 + 视觉检测

### 2. 高速IO
- **配置键**: `high_speed_io`
- **描述**: 高速IO测试配置
- **启用测试项目**: 约10-12个
- **支持功能**: 自动测试 + 视觉检测

### 3. 冗余型
- **配置键**: `redundant_type`
- **描述**: 冗余型测试配置
- **启用测试项目**: 特定测试项目组合
- **支持功能**: 仅自动测试

### 4. 201无输入输出
- **配置键**: `201_no_io`
- **描述**: 201型号无输入输出配置
- **启用测试项目**: 通信类测试为主
- **支持功能**: 仅自动测试

### 5. 201有输入输出
- **配置键**: `201_with_io`
- **描述**: 201型号有输入输出配置
- **启用测试项目**: 通信类 + 部分硬件测试
- **支持功能**: 仅自动测试

### 6. 201五通信
- **配置键**: `201_five_comm`
- **描述**: 201型号五通信配置
- **启用测试项目**: 5个通信测试项目
- **支持功能**: 仅自动测试

## 测试项目与M区映射

### 基础测试项目列表

| 索引 | 测试项目名称 | 代码标识 | 类别 | 图标 |
|------|-------------|----------|------|------|
| 0 | RS485_通信 | rs485_1 | 通信 | network |
| 1 | RS232通信 | rs232 | 通信 | network |
| 2 | CANbus通信 | canbus | 通信 | network |
| 3 | EtherCAT通信 | ethercat | 通信 | wifi |
| 4 | Backplane Bus通信 | backplane_bus | 通信 | database |
| 5 | Body I/O输入输出 | body_io | 硬件 | zap |
| 6 | Led数码管 | led_tube | 硬件 | zap |
| 7 | Led灯珠 | led_bulb | 硬件 | zap |
| 8 | U盘接口 | usb_drive | 接口 | hard-drive |
| 9 | SD卡 | sd_slot | 接口 | hard-drive |
| 10 | 调试串口 | debug_port | 接口 | cpu |
| 11 | 网口 | net_port | 接口 | network |
| 12 | 拨码开关 | dip_switch | 硬件 | settings |
| 13 | 复位按钮 | reset_btn | 硬件 | rotate-ccw |

## M区取值规范

### M区数据格式
- **数据格式**: 逗号分隔的数值字符串，例如: `"0, 1, 1, 0, 1, 2, 0, 0"`
- **数据类型**: 每个M区值为整数 (0, 1, 2等)
- **数组索引**: M0对应索引0，M1对应索引1，以此类推

### M区控制模式

#### 1. 单M区模式 (Single Mode)
- **配置结构**: `{ mode: 'single', mIndex: N }`
- **判断逻辑**: 单个M区值等于1为通过
- **示例**: M3 = 1 → 通过

#### 2. 多M区组合模式 (Combined Mode)
- **配置结构**: `{ mode: 'combined', mIndices: [N1, N2, ...], combineMode: 'AND|OR' }`
- **AND逻辑**: 所有指定M区值都为1才通过
- **OR逻辑**: 任一指定M区值为1即通过
- **示例**: M3 AND M5 = 1 → 通过

### 特殊M区控制逻辑

#### Backplane Bus通信 (索引4) - 双重验证
1. **第一步**: M区值检查 (单M区或组合模式)
2. **第二步**: 模块消息检查 (检查是否存在slave=1的模块)
3. **通过条件**: 两步检查都必须通过

```javascript
// 双重验证逻辑示例
if (mAreaCheck.success && mAreaCheck.result === 'pass') {
    const moduleResult = await checkModuleMessage();
    return moduleResult.success; // 最终结果
}
```

### 测试模式分类

#### 自动测试模式项目
- **控制方式**: 通过 `configManager.getAutoTestMAreaControlInfo()` 获取
- **执行时机**: 点击"自动测试"按钮立即执行
- **典型项目**: 通信类测试项目 (RS485、RS232、CANbus等)

#### 视觉检测模式项目
- **控制方式**: 通过 `configManager.getVisualTestMAreaControlInfo()` 获取
- **执行时机**: 视觉检测流程中，40秒延时后执行
- **典型项目**: 硬件类测试项目 (Body I/O、LED等)

#### 混合模式项目
- **特征**: 同时具有自动测试和视觉检测M区控制配置
- **执行逻辑**: 根据当前测试模式选择相应的M区配置

## 配置文件位置

### 主要配置管理器
- **文件位置**: 通过 `window.getConfigManager()` 获取
- **配置方法**: 
  - `getConfig(productType)` - 获取产品配置
  - `getMAreaControlInfo(testIndex, productType)` - 获取M区控制信息
  - `getAutoTestMAreaControlInfo(testIndex, productType)` - 获取自动测试M区控制
  - `getVisualTestMAreaControlInfo(testIndex, productType)` - 获取视觉检测M区控制
  - `validateCombinedMAreaTest(testIndex, mAreaValues, productType, isVisualContext)` - 验证M区测试

### Vue.js主文件
- **文件位置**: `/static/page_js_css/CPUControllerVue.js`
- **关键函数**: 
  - `parseMAreaData()` (406-422行) - 解析M区数据
  - `runAutoTest()` (754-1001行) - 自动测试执行
  - `runVisualInspection()` (1014-1239行) - 视觉检测执行

## M区数据验证流程

### 1. 数据获取
```javascript
// 实时获取M区数据
const mAreaValues = await refreshMAreaData();
// 格式: [0, 1, 1, 0, 1, 2, 0, 0, ...]
```

### 2. 配置查询
```javascript
// 获取测试项目的M区控制配置
const mAreaControl = configManager.getMAreaControlInfo(testIndex, productType);
```

### 3. 验证执行
```javascript
// 执行M区验证
const validation = configManager.validateCombinedMAreaTest(
    testIndex, 
    mAreaValues, 
    productType, 
    isVisualContext
);
```

### 4. 结果判断
- **validation.success**: 验证是否成功执行
- **validation.result**: 'pass' 或 'fail'
- **validation.reason**: 验证结果说明
- **validation.details**: 详细的M区值信息

## 锁定机制

### 自动测试锁定
- **触发条件**: `mAreaTestCompleted.value = true`
- **影响范围**: 自动测试M区控制的项目不可手动修改
- **解锁方式**: 清除所有测试结果

### 视觉检测锁定
- **触发条件**: `visualTestCompleted.value = true`
- **影响范围**: 视觉检测M区控制的项目不可手动修改
- **解锁方式**: 清除所有测试结果

## 日志记录格式

### M区测试日志结构
```json
{
    "test_time": "2025-07-23 14:30:15",
    "product_config": "all_config",
    "m_data_raw": "0, 1, 1, 0, 1, 2, 0, 0",
    "m_values": [0, 1, 1, 0, 1, 2, 0, 0],
    "test_mode": {
        "auto_test_completed": true,
        "visual_test_completed": false,
        "mode_type": "auto_test"
    },
    "summary": {
        "controlled_tests": 5,
        "passed": 4,
        "failed": 1,
        "result": "NG"
    },
    "details": [
        {
            "test": "RS485_通信",
            "result": "OK",
            "reason": "M0=1, 条件满足",
            "test_mode": "auto_test",
            "test_code": "rs485_1",
            "logic": "M0 (单值判断)",
            "values": "M0=1",
            "expect": "M0=1 (单值条件)"
        }
    ]
}
```

## 注意事项

1. **M区数据格式**: 必须是逗号分隔的数值字符串
2. **索引对应**: M区索引从0开始，M0对应数组索引0
3. **验证上下文**: 自动测试和视觉检测可能使用不同的M区配置
4. **双重验证**: Backplane Bus通信需要M区检查+模块消息检查
5. **锁定状态**: M区控制的项目在对应模式完成后会被锁定
6. **配置动态**: 不同产品类型有不同的M区映射配置

---

**维护说明**: 本文档基于CPUControllerVue.js v2.1分析生成，如系统升级请及时更新此文档。