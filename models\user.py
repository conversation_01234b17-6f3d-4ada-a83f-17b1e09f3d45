from sqlalchemy import Column, Integer, String, TIMESTAMP, text
from database.db_manager import Base
from datetime import datetime

class User(Base):
    __tablename__ = 'user_table'
    
    user_id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(20), nullable=False)
    password = Column(String(64), nullable=False)
    created_time = Column(TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP'))
    last_login_time = Column(TIMESTAMP, nullable=True)
    
    def update_login_time(self):
        self.last_login_time = datetime.now() 