.container {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 1.25rem;
    box-sizing: border-box;
    flex: 1;
    display: flex;
    flex-direction: column;
}

h1 {
    font-size: clamp(var(--font-size-2xl), 5vw, var(--font-size-4xl));
    font-weight: bold;
    margin-bottom: 1rem;
}

.search-container {
    position: relative;
    margin-bottom: 1rem;
    width: 100%;
}

.search-icon {
    position: absolute;
    left: 0.625rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

input[type="text"] {
    width: 100%;
    padding: 0.5rem 0.75rem 0.5rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: var(--font-size-base);
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

th {
    background-color: #f3f4f6;
    font-weight: bold;
    font-size: var(--font-size-sm);
}

.overflow-x-auto {
    overflow-x: auto;
    width: 100%;
    flex: 1;
    overflow-y: auto;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    display: flex;
    flex-direction: column;
    font-size: var(--font-size-base);
}

.app-wrapper {
    flex: 1;
    display: flex;
}

.main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

#content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

#fault-code-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

@media (max-width: 48rem) {
    .container {
        padding: 1rem;
    }
    
    h1 {
        font-size: var(--font-size-2xl);
    }
    
    input[type="text"] {
        font-size: var(--font-size-sm);
    }
    
    th, td {
        padding: 0.5rem;
        font-size: var(--font-size-sm);
    }
    
    .search-container {
        flex-direction: column;
    }
    
    .search-container > * {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 30rem) {
    .container {
        padding: 0.75rem;
    }
    
    h1 {
        font-size: var(--font-size-xl);
    }
    
    .search-container {
        margin-bottom: 0.75rem;
    }
}

@media print {
    .search-container {
        display: none;
    }
    
    .overflow-x-auto {
        overflow: visible;
    }
}
