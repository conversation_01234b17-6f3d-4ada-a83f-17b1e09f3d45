from sqlalchemy.orm import Session
from sqlalchemy import or_
from models.assembly import BoardAssembly, CompleteProduct
from typing import List, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class AssemblyService:
    def __init__(self, session: Session):
        self.session = session
    
    def check_board_usage(self, board_sn: str) -> Optional[BoardAssembly]:
        """检查板子是否已被使用"""
        return self.session.query(BoardAssembly).filter(
            or_(
                BoardAssembly.board_a_sn == board_sn,
                BoardAssembly.board_b_sn == board_sn,
                BoardAssembly.board_c_sn == board_sn,
                BoardAssembly.board_d_sn == board_sn
            )
        ).first()
    
    def check_shell_usage(self, shell_sn: str) -> Optional[CompleteProduct]:
        """检查外壳是否已被使用"""
        return self.session.query(CompleteProduct).filter(
            CompleteProduct.product_sn == shell_sn
        ).first()
    
    def get_binding_progress(self, order_number: str) -> tuple[int, int, int]:
        """获取工单的绑定进度"""
        try:
            # 获取工单总数量
            # 先从板子组合表中查询
            board_assembly = self.session.query(BoardAssembly).filter(
                BoardAssembly.board_processing_order == order_number
            ).first()
            
            # 如果板子组合表中没有，再从完整产品表中查询
            if not board_assembly:
                complete_product = self.session.query(CompleteProduct).filter(
                    CompleteProduct.complete_processing_order == order_number
                ).first()
                
                if not complete_product:
                    # 如果数据库中没有该工单的记录，返回0,0,0，但不进行数量限制
                    return 0, 0, 0
                    
                total_quantity = complete_product.complete_product_quantity
            else:
                total_quantity = board_assembly.board_product_quantity
            
            # 统计已绑定的板子数量
            board_count = self.session.query(BoardAssembly).filter(
                BoardAssembly.board_processing_order == order_number
            ).count()
            
            # 统计已绑定的外壳数量
            shell_count = self.session.query(CompleteProduct).filter(
                CompleteProduct.complete_processing_order == order_number
            ).count()
            
            return board_count, shell_count, total_quantity
            
        except Exception as e:
            logger.error("获取绑定进度失败: %s", str(e), exc_info=True)
            raise ValueError(f"获取绑定进度失败：{str(e)}")
    
    def create_board_assembly(self, assembly_data: dict) -> BoardAssembly:
        """创建板子组合"""
        # 检查绑定数量是否超出工单数量
        order_number = assembly_data['orderNumber']
        board_count, _, total_quantity = self.get_binding_progress(order_number)
        
        # 只有当total_quantity大于0时才进行数量检查
        if total_quantity > 0 and board_count >= total_quantity:
            raise ValueError(f"已达到工单规定的数量({total_quantity})，不能继续绑定")
        
        board_sns = [
            assembly_data.get('code1'),
            assembly_data.get('code2'),
            assembly_data.get('code3'),
            assembly_data.get('code4')
        ]
        
        # 检查板子使用情况
        for sn in filter(None, board_sns):
            if self.check_board_usage(sn):
                raise ValueError(f"板子序列号 {sn} 已被使用")
        
        # 计算assembly_id
        assembly_id = BoardAssembly.calculate_assembly_id(board_sns)
        
        # 检查assembly_id是否已存在
        existing_assembly = self.session.query(BoardAssembly).filter(
            BoardAssembly.assembly_id == assembly_id
        ).first()
        
        if existing_assembly:
            raise ValueError("该组合已经存在，不能重复创建")
        
        assembly = BoardAssembly(
            assembly_id=assembly_id,
            board_type=assembly_data['type'],
            board_tester_name=assembly_data['tester'],
            board_product_model=assembly_data['productModel'],
            board_processing_order=assembly_data['orderNumber'],
            board_product_quantity=assembly_data['productQuantity'],
            board_procode=assembly_data['productCode'],
            board_a_sn=board_sns[0],
            board_b_sn=board_sns[1],
            board_c_sn=board_sns[2],
            board_d_sn=board_sns[3]
        )
        
        self.session.add(assembly)
        self.session.commit()
        return assembly
    
    def bind_shell(self, assembly_id: str, shell_data: dict) -> CompleteProduct:
        """绑定外壳"""
        # 检查绑定数量是否超出工单数量
        order_number = shell_data['order_number']
        _, shell_count, total_quantity = self.get_binding_progress(order_number)
        
        # 只有当total_quantity大于0时才进行数量检查
        if total_quantity > 0 and shell_count >= total_quantity:
            raise ValueError(f"已达到工单规定的数量({total_quantity})，不能继续绑定")
        
        # 检查assembly_id是否存在且未绑定外壳
        assembly = self.session.query(BoardAssembly).filter(
            BoardAssembly.assembly_id == assembly_id
        ).first()
        
        if not assembly:
            raise ValueError("未找到对应的板子组合")
            
        if self.check_shell_usage(shell_data['shell_sn']):
            raise ValueError("该外壳SN已被使用")
            
        # 检查是否已绑定外壳
        existing_shell = self.session.query(CompleteProduct).filter(
            CompleteProduct.assembly_id == assembly_id
        ).first()
        
        if existing_shell:
            raise ValueError("该组合已经绑定了外壳")
            
        complete_product = CompleteProduct(
            assembly_id=assembly_id,
            product_sn=shell_data['shell_sn'],
            complete_tester_name=shell_data['tester'],
            complete_processing_order=shell_data['order_number'],
            complete_product_quantity=shell_data['product_quantity'],
            complete_product_model=shell_data['product_model'],
            complete_product_remark=shell_data.get('remark', ''),
            complete_procode=shell_data.get('productCode', '')
        )
        
        self.session.add(complete_product)
        self.session.commit()
        return complete_product
    
    def search_assembly(self, sn: str) -> Optional[dict]:
        """查询组合信息"""
        try:
            # 先通过外壳SN查询完整产品信息
            complete_product = self.session.query(CompleteProduct).filter(
                CompleteProduct.product_sn == sn
            ).first()
            
            if complete_product:
                # 如果找到外壳，查询对应的板子组合信息
                assembly = self.session.query(BoardAssembly).filter(
                    BoardAssembly.assembly_id == complete_product.assembly_id
                ).first()
                
                if assembly:
                    data = self._format_assembly_data(assembly, complete_product)
                    return data
                
            # 如果通过外壳SN没找到，尝试通过板子SN查询
            assembly = self.session.query(BoardAssembly).filter(
                or_(
                    BoardAssembly.board_a_sn == sn,
                    BoardAssembly.board_b_sn == sn,
                    BoardAssembly.board_c_sn == sn,
                    BoardAssembly.board_d_sn == sn
                )
            ).first()
            
            if assembly:
                # 查询是否有关联的外壳
                complete_product = self.session.query(CompleteProduct).filter(
                    CompleteProduct.assembly_id == assembly.assembly_id
                ).first()
                
                data = self._format_assembly_data(assembly, complete_product)
                return data
                
            return None
            
        except Exception as e:
            logger.error("查询失败: %s", str(e), exc_info=True)
            raise ValueError(f"查询失败：{str(e)}")
    
    def _format_assembly_data(self, assembly: BoardAssembly, complete_product: Optional[CompleteProduct] = None) -> dict:
        """格式化组装数据"""
        data = {
            # 基本信息
            'board_processing_order': assembly.board_processing_order,
            'board_product_model': assembly.board_product_model,
            'board_tester_name': assembly.board_tester_name,
            'created_at': assembly.created_at.strftime('%Y-%m-%d %H:%M:%S') if assembly.created_at else None,
            
            # 板子信息
            'board_a_sn': assembly.board_a_sn,
            'board_b_sn': assembly.board_b_sn,
            'board_c_sn': assembly.board_c_sn,
            'board_d_sn': assembly.board_d_sn,
            
            # 其他信息
            'assembly_id': assembly.assembly_id,
            'board_type': assembly.board_type,
            'has_shell': bool(complete_product),
            'complete_procode': complete_product.complete_procode if complete_product else None
        }
        
        # 如果有外壳信息，添加外壳相关数据
        if complete_product:
            data.update({
                'product_sn': complete_product.product_sn,
                'assembled_at': complete_product.assembled_at.strftime('%Y-%m-%d %H:%M:%S') if complete_product.assembled_at else None,
                'complete_tester_name': complete_product.complete_tester_name,
                'complete_product_remark': complete_product.complete_product_remark,
                'complete_product_quantity': complete_product.complete_product_quantity,
                'complete_procode': complete_product.complete_procode
            })
        
        return data
    
    def update_board_binding(self, assembly_id: str, board_position: str, new_sn: str) -> BoardAssembly:
        """更新指定位置的板子序列号"""
        try:
            # 获取组合信息
            assembly = self.session.query(BoardAssembly).filter(
                BoardAssembly.assembly_id == assembly_id
            ).first()
            
            if not assembly:
                raise ValueError("未找到对应的板子组合")
            
            # 检查新序列号是否与当前组合中的其他板子序列号重复
            current_sns = [
                assembly.board_a_sn,
                assembly.board_b_sn,
                assembly.board_c_sn,
                assembly.board_d_sn
            ]
            
            position_mapping = {
                '1': 'board_a_sn',
                '2': 'board_b_sn',
                '3': 'board_c_sn',
                '4': 'board_d_sn'
            }
            
            board_column = position_mapping.get(board_position)
            if not board_column:
                raise ValueError("无效的板子位置")
            
            # 获取当前位置的原序列号
            original_sn = getattr(assembly, board_column)
            
            # 如果新序列号与原序列号相同，不需要更新
            if new_sn == original_sn:
                raise ValueError("新序列号与原序列号相同，无需更新")
            
            # 检查新序列号是否与当前组合中的其他位置重复
            for pos, sn in enumerate(current_sns):
                if sn == new_sn and position_mapping.get(str(pos + 1)) != board_column:
                    raise ValueError(f"新序列号与当前组合中的其他板子序列号重复")
            
            # 检查新序列号是否已被其他组合使用
            existing_board = self.session.query(BoardAssembly).filter(
                or_(
                    BoardAssembly.board_a_sn == new_sn,
                    BoardAssembly.board_b_sn == new_sn,
                    BoardAssembly.board_c_sn == new_sn,
                    BoardAssembly.board_d_sn == new_sn
                ),
                BoardAssembly.assembly_id != assembly_id
            ).first()
            
            if existing_board:
                raise ValueError(f"板子序列号 {new_sn} 已被其他组合使用")
            
            # 更新对应位置的板子序列号
            setattr(assembly, board_column, new_sn)
            
            # 提交更新
            self.session.commit()
            return assembly
            
        except Exception as e:
            self.session.rollback()
            raise ValueError(f"更新失败：{str(e)}")
    
    def update_shell_binding(self, assembly_id: str, new_shell_sn: str) -> CompleteProduct:
        """更新外壳SN"""
        try:
            # 检查新的外壳SN是否已被使用
            existing_shell = self.check_shell_usage(new_shell_sn)
            if existing_shell:
                raise ValueError("该外壳SN已被使用")
            
            # 获取现有的外壳绑定记录
            complete_product = self.session.query(CompleteProduct).filter(
                CompleteProduct.assembly_id == assembly_id
            ).first()
            
            if not complete_product:
                raise ValueError("未找到外壳绑定记录")
            
            # 更新外壳SN
            complete_product.product_sn = new_shell_sn
            
            self.session.commit()
            return complete_product
            
        except Exception as e:
            self.session.rollback()
            raise ValueError(f"更新失败：{str(e)}")
    
    def unbind_all(self, assembly_id: str) -> None:
        """解除所有绑定"""
        try:
            # 先删除外壳绑定记录（如果存在）
            complete_product = self.session.query(CompleteProduct).filter(
                CompleteProduct.assembly_id == assembly_id
            ).first()
            
            if complete_product:
                self.session.delete(complete_product)
            
            # 删除板子组合记录
            assembly = self.session.query(BoardAssembly).filter(
                BoardAssembly.assembly_id == assembly_id
            ).first()
            
            if not assembly:
                raise ValueError("未找到对应的板子组合")
            
            self.session.delete(assembly)
            self.session.commit()
            
        except Exception as e:
            self.session.rollback()
            raise ValueError(f"解绑失败：{str(e)}")
    
    def create_io_assembly(self, io_data: dict) -> tuple[BoardAssembly, CompleteProduct]:
        """创建IO模块组合并绑定外壳"""
        try:
            # 检查绑定数量是否超出工单数量
            order_number = io_data['order_number']
            _, shell_count, total_quantity = self.get_binding_progress(order_number)
            
            # 只有当total_quantity大于0时才进行数量检查
            if total_quantity > 0 and shell_count >= total_quantity:
                raise ValueError(f"已达到工单规定的数量({total_quantity})，不能继续绑定")
            
            # 检查板子是否已被使用
            board_sns = [io_data['board_a']]
            if io_data['board_b']:  # 如果是类型B，还要检查board_b
                board_sns.append(io_data['board_b'])
            
            for sn in board_sns:
                if self.check_board_usage(sn):
                    raise ValueError(f"板子序列号 {sn} 已被使用")
            
            # 检查外壳是否已被使用
            if self.check_shell_usage(io_data['shell_sn']):
                raise ValueError("该外壳SN已被使用")
            
            # 计算assembly_id
            assembly_id = BoardAssembly.calculate_assembly_id(board_sns)
            
            # 创建板子组合记录
            assembly = BoardAssembly(
                assembly_id=assembly_id,
                board_type=3 if io_data['type'] == 'A' else 4,  # 3=单板IO模块, 4=双板IO模块
                board_tester_name=io_data['tester'],
                board_product_model=io_data['product_model'],
                board_processing_order=io_data['order_number'],
                board_product_quantity=io_data['product_quantity'],
                board_a_sn=io_data['board_a'],
                board_b_sn=io_data['board_b'] if io_data['type'] == 'B' else None
            )
            
            # 创建外壳绑定记录
            complete_product = CompleteProduct(
                assembly_id=assembly_id,
                product_sn=io_data['shell_sn'],
                complete_tester_name=io_data['tester'],
                complete_processing_order=io_data['order_number'],
                complete_product_quantity=io_data['product_quantity'],
                complete_product_model=io_data['product_model'],
                complete_product_remark=io_data.get('remark', ''),
                complete_procode=io_data.get('productCode', '')
            )
            
            # 保存记录
            self.session.add(assembly)
            self.session.add(complete_product)
            self.session.commit()
            
            return assembly, complete_product
            
        except Exception as e:
            self.session.rollback()
            raise ValueError(f"IO模块绑定失败：{str(e)}")
    
    def search_by_order_number(self, order_number: str) -> List[dict]:
        """通过工单号查询所有相关组合信息"""
        try:
            results = []
            
            # 查询所有匹配工单号的板子组合
            assemblies = self.session.query(BoardAssembly).filter(
                BoardAssembly.board_processing_order == order_number
            ).all()
            
            for assembly in assemblies:
                # 查询关联的外壳信息
                complete_product = self.session.query(CompleteProduct).filter(
                    CompleteProduct.assembly_id == assembly.assembly_id
                ).first()
                
                data = self._format_assembly_data(assembly, complete_product)
                results.append(data)
            
            # 查询所有匹配工单号的完整产品
            complete_products = self.session.query(CompleteProduct).filter(
                CompleteProduct.complete_processing_order == order_number
            ).all()
            
            for complete_product in complete_products:
                # 检查是否已经包含在结果中
                if not any(r.get('assembly_id') == complete_product.assembly_id for r in results):
                    assembly = self.session.query(BoardAssembly).filter(
                        BoardAssembly.assembly_id == complete_product.assembly_id
                    ).first()
                    
                    if assembly:
                        data = self._format_assembly_data(assembly, complete_product)
                        results.append(data)
            
            return results
                
        except Exception as e:
            logger.error("工单号查询失败: %s", str(e), exc_info=True)
            raise ValueError(f"工单号查询失败：{str(e)}")
    
    def search_by_date_range(self, start_date: Optional[datetime], end_date: Optional[datetime]) -> List[dict]:
        """通过日期范围查询组合信息"""
        try:
            query = self.session.query(CompleteProduct)
            
            # 添加日期范围条件
            if start_date:
                query = query.filter(CompleteProduct.assembled_at >= start_date)
            if end_date:
                query = query.filter(CompleteProduct.assembled_at <= end_date)
            
            complete_products = query.all()
            
            results = []
            for complete_product in complete_products:
                assembly = self.session.query(BoardAssembly).filter(
                    BoardAssembly.assembly_id == complete_product.assembly_id
                ).first()
                
                if assembly:
                    data = self._format_assembly_data(assembly, complete_product)
                    results.append(data)
                
            return results
                
        except Exception as e:
            logger.error("日期范围查询失败: %s", str(e), exc_info=True)
            raise ValueError(f"日期范围查询失败：{str(e)}")