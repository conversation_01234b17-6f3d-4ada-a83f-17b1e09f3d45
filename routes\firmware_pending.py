# routes/firmware_pending.py
from flask import Blueprint, request, jsonify
from models.firmware import Firmware, ApprovalFlow
from database.db_manager import DatabaseManager
from utils.firmware_utils import create_response, handle_db_error
from sqlalchemy import and_, or_, desc, asc
from datetime import datetime
import logging
from jwt import decode as jwt_decode
from config import JWT_SECRET

firmware_pending_bp = Blueprint('firmware_pending', __name__, url_prefix='/api/firmware/pending')
logger = logging.getLogger(__name__)
db_manager = DatabaseManager()

@firmware_pending_bp.route('/list', methods=['GET'])
def get_pending_list():
    """获取待审核固件列表"""
    try:
        # 参数获取和验证
        page = max(1, int(request.args.get('page', 1)))
        per_page = min(2000, max(1, int(request.args.get('per_page', 20))))  # 增加最大限制到2000
        search = request.args.get('search', '').strip()
        sort_by = request.args.get('sort_by', 'create_time')
        sort_order = request.args.get('sort_order', 'desc')

        # 添加获取所有数据的特殊处理
        get_all = request.args.get('get_all', '').lower() == 'true'
        if get_all:
            per_page = 10000  # 设置一个非常大的值来获取所有数据
            logger.info("请求获取所有待审核固件数据，不进行分页限制")
        
        with db_manager.get_session() as session:
            # 构建查询
            query = session.query(Firmware).filter(
                Firmware.is_deleted == False,
                Firmware.status == 'pending'
            )
            
            # 搜索过滤
            if search:
                search_filter = or_(
                    Firmware.serial_number.ilike(f'%{search}%'),
                    Firmware.name.ilike(f'%{search}%'),
                    Firmware.version.ilike(f'%{search}%'),
                    Firmware.developer.ilike(f'%{search}%'),
                    Firmware.uploader.ilike(f'%{search}%'),
                    Firmware.source.ilike(f'%{search}%')
                )
                query = query.filter(search_filter)
            
            # 排序
            sort_field = getattr(Firmware, sort_by, Firmware.create_time)
            if sort_order.lower() == 'asc':
                query = query.order_by(asc(sort_field))
            else:
                query = query.order_by(desc(sort_field))
            
            # 分页
            total = query.count()
            offset = (page - 1) * per_page
            items = query.offset(offset).limit(per_page).all()
            
            # 转换为字典格式
            result_items = []
            for item in items:
                item_dict = item.to_dict()
                # 添加额外字段
                item_dict['oldSerialNumber'] = item.parent_sn or '无'
                result_items.append(item_dict)
            
            return create_response(True, '查询成功', {
                'items': result_items,
                'total': total,
                'page': page,
                'per_page': per_page,
                'total_pages': (total + per_page - 1) // per_page
            })
            
    except Exception as e:
        logger.error(f"获取待审核列表失败: {str(e)}")
        return handle_db_error(e)

@firmware_pending_bp.route('/<serial_number>/approve', methods=['POST'])
def approve_firmware(serial_number):
    """审核通过固件"""
    try:
        data = request.get_json()
        token = request.cookies.get('token')
        try:
            user_data = jwt_decode(token, JWT_SECRET, algorithms=['HS256'])
            approver_name = user_data.get('username', 'unknown')
        except:
            approver_name = 'unknown'
        notes = data.get('notes', '')
        
        if not serial_number:
            return create_response(False, '流水号不能为空', code=400)
        
        with db_manager.get_session() as session:
            # 查找待审核固件
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                status='pending',
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '待审核固件不存在', code=404)
            
            # 更新固件状态
            firmware.status = 'active'
            firmware.approver_name = approver_name
            firmware.approve_time = datetime.now()
            firmware.update_time = datetime.now()
            firmware.reject_reason = None  # 清空拒绝理由
            
            # 创建审核流水记录
            approval_flow = ApprovalFlow(
                serial_number=serial_number,
                action='approve',
                operator_name=approver_name,
                notes=notes or '审核通过'
            )
            session.add(approval_flow)
            
            session.commit()
            
            logger.info(f"固件审核通过: {serial_number} by {approver_name}")
            return create_response(True, '审核通过成功')
            
    except Exception as e:
        logger.error(f"审核通过失败: {str(e)}")
        return handle_db_error(e)

@firmware_pending_bp.route('/<serial_number>/reject', methods=['POST'])
def reject_firmware(serial_number):
    """审核拒绝固件"""
    try:
        data = request.get_json()
        token = request.cookies.get('token')
        try:
            user_data = jwt_decode(token, JWT_SECRET, algorithms=['HS256'])
            approver_name = user_data.get('username', 'unknown')
        except:
            approver_name = 'unknown'
        reject_reason = data.get('reason', '')
        
        if not serial_number:
            return create_response(False, '流水号不能为空', code=400)
        
        if not reject_reason:
            return create_response(False, '拒绝理由不能为空', code=400)
        
        with db_manager.get_session() as session:
            # 查找待审核固件
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                status='pending',
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '待审核固件不存在', code=404)
            
            # 更新固件状态
            firmware.status = 'rejected'
            firmware.approver_name = approver_name
            firmware.reject_reason = reject_reason
            firmware.update_time = datetime.now()
            
            # 创建审核流水记录
            approval_flow = ApprovalFlow(
                serial_number=serial_number,
                action='reject',
                operator_name=approver_name,
                notes=reject_reason
            )
            session.add(approval_flow)
            
            session.commit()
            
            logger.info(f"固件审核拒绝: {serial_number} by {approver_name}, 理由: {reject_reason}")
            return create_response(True, '审核拒绝成功')
            
    except Exception as e:
        logger.error(f"审核拒绝失败: {str(e)}")
        return handle_db_error(e)

# =============================================================================
# 批量审核功能 - 暂时停用 (2025-06-02)
# 原因：业务流程要求逐个审核，确保审核质量和责任追溯
# 暂时注释以下功能，如需恢复请联系开发团队
# =============================================================================

# @firmware_pending_bp.route('/batch-approve', methods=['POST'])
# def batch_approve_firmware():
#     """批量审核通过 - 功能已停用"""
#     try:
#         data = request.get_json()
#         serial_numbers = data.get('serialNumbers', [])
#         approver_name = data.get('approverName', 'unknown')
#         notes = data.get('notes', '')
#         
#         if not serial_numbers:
#             return create_response(False, '请选择要审核的固件', code=400)
#         
#         success_count = 0
#         failed_items = []
#         
#         with db_manager.get_session() as session:
#             for serial_number in serial_numbers:
#                 try:
#                     # 查找待审核固件
#                     firmware = session.query(Firmware).filter_by(
#                         serial_number=serial_number,
#                         status='pending',
#                         is_deleted=False
#                     ).first()
#                     
#                     if not firmware:
#                         failed_items.append({'serialNumber': serial_number, 'reason': '固件不存在'})
#                         continue
#                     
#                     # 更新固件状态
#                     firmware.status = 'active'
#                     firmware.approver_name = approver_name
#                     firmware.approve_time = datetime.now()
#                     firmware.update_time = datetime.now()
#                     firmware.reject_reason = None
#                     
#                     # 创建审核流水记录
#                     approval_flow = ApprovalFlow(
#                         serial_number=serial_number,
#                         action='approve',
#                         operator_name=approver_name,
#                         notes=notes or '批量审核通过'
#                     )
#                     session.add(approval_flow)
#                     
#                     success_count += 1
#                     
#                 except Exception as e:
#                     failed_items.append({'serialNumber': serial_number, 'reason': str(e)})
#                     continue
#             
#             session.commit()
#             
#             result_message = f'批量审核完成，成功: {success_count}个'
#             if failed_items:
#                 result_message += f'，失败: {len(failed_items)}个'
#             
#             logger.info(f"批量审核完成: 成功{success_count}个，失败{len(failed_items)}个")
#             return create_response(True, result_message, {
#                 'successCount': success_count,
#                 'failedItems': failed_items
#             })
#             
#     except Exception as e:
#         logger.error(f"批量审核失败: {str(e)}")
#         return handle_db_error(e)

# @firmware_pending_bp.route('/batch-reject', methods=['POST'])
# def batch_reject_firmware():
#     """批量审核拒绝 - 功能已停用"""
#     try:
#         data = request.get_json()
#         serial_numbers = data.get('serialNumbers', [])
#         approver_name = data.get('approverName', 'unknown')
#         reject_reason = data.get('rejectReason', '')
#         
#         if not serial_numbers:
#             return create_response(False, '请选择要审核的固件', code=400)
#         
#         if not reject_reason:
#             return create_response(False, '拒绝理由不能为空', code=400)
#         
#         success_count = 0
#         failed_items = []
#         
#         with db_manager.get_session() as session:
#             for serial_number in serial_numbers:
#                 try:
#                     # 查找待审核固件
#                     firmware = session.query(Firmware).filter_by(
#                         serial_number=serial_number,
#                         status='pending',
#                         is_deleted=False
#                     ).first()
#                     
#                     if not firmware:
#                         failed_items.append({'serialNumber': serial_number, 'reason': '固件不存在'})
#                         continue
#                     
#                     # 更新固件状态
#                     firmware.status = 'rejected'
#                     firmware.approver_name = approver_name
#                     firmware.reject_reason = reject_reason
#                     firmware.update_time = datetime.now()
#                     
#                     # 创建审核流水记录
#                     approval_flow = ApprovalFlow(
#                         serial_number=serial_number,
#                         action='reject',
#                         operator_name=approver_name,
#                         notes=reject_reason
#                     )
#                     session.add(approval_flow)
#                     
#                     success_count += 1
#                     
#                 except Exception as e:
#                     failed_items.append({'serialNumber': serial_number, 'reason': str(e)})
#                     continue
#             
#             session.commit()
#             
#             result_message = f'批量拒绝完成，成功: {success_count}个'
#             if failed_items:
#                 result_message += f'，失败: {len(failed_items)}个'
#             
#             logger.info(f"批量拒绝完成: 成功{success_count}个，失败{len(failed_items)}个")
#             return create_response(True, result_message, {
#                 'successCount': success_count,
#                 'failedItems': failed_items
#             })
#             
#     except Exception as e:
#         logger.error(f"批量拒绝失败: {str(e)}")
#         return handle_db_error(e)

# =============================================================================
# 注：如需重新启用批量审核功能，需要：
# 1. 取消上述代码注释
# 2. 前端恢复批量操作UI组件  
# 3. 完善批量操作的权限控制
# 4. 增强批量操作的安全验证机制
# =============================================================================

@firmware_pending_bp.route('/detail/<serial_number>', methods=['GET'])
def get_pending_detail(serial_number):
    """获取待审核固件详情"""
    try:
        with db_manager.get_session() as session:
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                status='pending',
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '待审核固件不存在', code=404)
            
            # 获取基本信息
            result = firmware.to_dict()
            
            # 获取审核流水
            approval_flows = session.query(ApprovalFlow).filter_by(
                serial_number=serial_number
            ).order_by(desc(ApprovalFlow.create_time)).all()
            result['approvalFlows'] = [a.to_dict() for a in approval_flows]
            
            # 如果有父版本，获取父版本信息
            if firmware.parent_sn:
                parent_firmware = session.query(Firmware).filter_by(
                    serial_number=firmware.parent_sn,
                    is_deleted=False
                ).first()
                if parent_firmware:
                    result['parentInfo'] = parent_firmware.to_dict()
            
            return create_response(True, '查询成功', result)
            
    except Exception as e:
        logger.error(f"获取待审核详情失败: {str(e)}")
        return handle_db_error(e)

@firmware_pending_bp.route('/stats', methods=['GET'])
def get_pending_stats():
    """获取待审核统计信息"""
    try:
        with db_manager.get_session() as session:
            # 总数统计
            total_pending = session.query(Firmware).filter_by(
                status='pending',
                is_deleted=False
            ).count()
            
            # 按来源统计
            source_stats = {}
            for source in ['新发布', '升级', '修改']:
                count = session.query(Firmware).filter_by(
                    status='pending',
                    source=source,
                    is_deleted=False
                ).count()
                source_stats[source] = count
            
            # 按开发者统计
            developer_query = session.query(
                Firmware.developer,
                session.query(Firmware).filter_by(
                    status='pending',
                    is_deleted=False
                ).filter(
                    Firmware.developer == Firmware.developer
                ).count().label('count')
            ).filter_by(
                status='pending',
                is_deleted=False
            ).group_by(Firmware.developer).all()
            
            developer_stats = {dev: count for dev, count in developer_query}
            
            return create_response(True, '查询成功', {
                'totalPending': total_pending,
                'sourceStats': source_stats,
                'developerStats': developer_stats
            })
            
    except Exception as e:
        logger.error(f"获取待审核统计失败: {str(e)}")
        return handle_db_error(e)

@firmware_pending_bp.route('/export', methods=['GET'])
def export_pending_list():
    """导出待审核固件列表"""
    try:
        # 这里可以实现导出功能
        return create_response(True, '导出功能待实现')
    except Exception as e:
        logger.error(f"导出待审核列表失败: {str(e)}")
        return handle_db_error(e) 