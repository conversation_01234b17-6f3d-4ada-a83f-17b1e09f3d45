// 售后查询页面脚本
(function() {
    // 全局状态管理
    if (typeof window.afterSalesPageState === 'undefined') {
        window.afterSalesPageState = {
            currentPage: 1,
            pageSize: 10,
            totalItems: 0,
            totalPages: 10,
            data: [],
            sortField: 'outboundTime',
            sortOrder: 'desc'
        };
    }

    // 列显示状态管理
    if (typeof window.afterSalesColumnState === 'undefined') {
        window.afterSalesColumnState = {
            serialNumber: true,
            productCode: true,
            productModel: true,
            softwareVersion: true,
            pcbaOrderNumber: true,
            isOutOfStock: true,
            customerName: true,
            outboundTime: true
        };
    }

    // 定义列配置常量
    const AFTER_SALES_COLUMNS = [
        'serialNumber', 'productCode', 'productModel', 'softwareVersion',
        'pcbaOrderNumber', 'isOutOfStock', 'customerName', 'outboundTime'
    ];

    // 初始化售后查询页面
    window.initAfterSalesQueryPage = function() {
        const afterSalesQueryContent = document.getElementById('after-sales-query-content');
        if (!afterSalesQueryContent) {
            Logger.error('无法找到 after-sales-query-content 元素');
            return;
        }

        afterSalesQueryContent.innerHTML = `
            <div class="after-sales-query">
                <!-- 1. 查询区域 -->
                <div class="query-section">
                    <form class="query-form">
                        <div class="form-item">
                            <label class="form-label">产品编码</label>
                            <input type="text" class="form-input" id="after-sales-product-code" placeholder="请输入产品编码">
                        </div>
                        <div class="form-item">
                            <label class="form-label">软件版本</label>
                            <input type="text" class="form-input" id="after-sales-software-version" placeholder="请输入软件版本">
                        </div>
                        <div class="form-item">
                            <label class="form-label">PCBA外协工单号</label>
                            <input type="text" class="form-input" id="after-sales-pcba-order" placeholder="请输入PCBA外协工单号或物料编码">
                        </div>
                    </form>
                    <div class="query-buttons">
                        <button class="btn btn-default" onclick="window.resetAfterSalesForm()">重置</button>
                        <button class="btn btn-primary" onclick="window.searchAfterSales()">查询</button>
                    </div>
                </div>

                <!-- 2. 工具栏区域 -->
                <div class="toolbar-section">
                    <div class="toolbar-left">
                        <button class="btn btn-export" onclick="window.exportAfterSalesData()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-default" onclick="window.toggleAfterSalesColumnSettings()">
                            <i class="fas fa-cog"></i> 列设置
                        </button>
                        <button class="btn btn-default" onclick="window.refreshAfterSalesTable()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 3. 表格区域 -->
                <div class="table-section">
                    <table class="data-table">
                        <thead>
                            <tr class="table-header">
                                <th class="table-cell-fixed">
                                    <input type="checkbox" id="after-sales-select-all" onclick="window.toggleAfterSalesSelectAll()">
                                </th>
                                <th class="sortable" onclick="sortByAfterSales('serialNumber')">
                                    SN号 <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" onclick="sortByAfterSales('productCode')">
                                    产品编码 <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" onclick="sortByAfterSales('productModel')">
                                    产品型号 <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" onclick="sortByAfterSales('softwareVersion')">
                                    软件版本 <i class="fas fa-sort"></i>
                                </th>
                                <th>物料编码/外协工单号</th>
                                <th class="sortable" onclick="sortByAfterSales('isOutOfStock')">
                                    是否出库 <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" onclick="sortByAfterSales('customerName')">
                                    客户名称 <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" onclick="sortByAfterSales('outboundTime')">
                                    出库时间 <i class="fas fa-sort"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="after-sales-results-tbody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 4. 分页区域 -->
                <div class="pagination-section">
                    <div class="page-size">
                        <span>每页</span>
                        <select class="form-select" onchange="window.changeAfterSalesPageSize(this.value)">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                        </select>
                        <span>条</span>
                    </div>
                    <div class="pagination-controls" id="after-sales-pagination-controls">
                        <!-- 分页控件将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 列设置弹窗 -->
                <div id="after-sales-column-settings-modal" class="modal column-modal">
                    <div class="modal-content column-modal__content">
                        <div class="modal-header">
                            <h3 class="modal-title">列设置</h3>
                            <span class="modal-close" onclick="window.closeAfterSalesColumnSettings()">&times;</span>
                        </div>
                        <div class="modal-body">
                            <div class="column-list">
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="after-sales-select-all-columns" onclick="window.toggleAllAfterSalesColumns()">
                                        <span>全选</span>
                                    </label>
                                </div>
                                <div class="column-divider"></div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-after-sales-serialNumber" checked>
                                        <span>SN号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-after-sales-productCode" checked>
                                        <span>产品编码</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-after-sales-productModel" checked>
                                        <span>产品型号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-after-sales-softwareVersion" checked>
                                        <span>软件版本</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-after-sales-pcbaOrderNumber" checked>
                                        <span>PCBA外协工单号</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-after-sales-isOutOfStock" checked>
                                        <span>是否出库</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-after-sales-customerName" checked>
                                        <span>客户名称</span>
                                    </label>
                                </div>
                                <div class="column-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="col-after-sales-outboundTime" checked>
                                        <span>出库时间</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" onclick="window.applyAfterSalesColumnSettings()">确定</button>
                            <button class="btn btn-default" onclick="window.closeAfterSalesColumnSettings()">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化事件监听
        initAfterSalesEventListeners();

        // 初始化分页控件
        updatePagination();

        // 更新排序图标
        updateSortIconsAfterSales();

        // 加载初始数据
        loadAfterSalesData();
    };

    // 初始化事件监听器
    function initAfterSalesEventListeners() {
        // 输入框回车事件
        const inputs = ['after-sales-product-code', 'after-sales-software-version', 'after-sales-pcba-order'];
        inputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        searchAfterSales();
                    }
                });
            }
        });

        // 列设置模态框事件
        const columnModal = document.getElementById('after-sales-column-settings-modal');
        if (columnModal) {
            // 点击模态框外部关闭
            window.onclick = (e) => {
                if (e.target === columnModal) {
                    columnModal.style.display = 'none';
                }
            };
        }

        // 添加分页大小选择器的事件监听
        const pageSizeSelect = document.querySelector('.page-size select');
        if (pageSizeSelect) {
            pageSizeSelect.value = afterSalesPageState.pageSize;
        }

        // 添加表格中复选框的变化监听
        const tbody = document.getElementById('after-sales-results-tbody');
        if (tbody) {
            tbody.addEventListener('change', (e) => {
                if (e.target.type === 'checkbox') {
                    // 更新全选框状态
                    const selectAll = document.getElementById('after-sales-select-all');
                    const checkboxes = tbody.querySelectorAll('input[type="checkbox"]');
                    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                    selectAll.checked = allChecked;

                    // 更新导出按钮文本
                    updateExportButtonText();
                }
            });
        }
    }

    // 搜索售后数据
    window.searchAfterSales = function() {
        const productCode = document.getElementById('after-sales-product-code').value.trim();
        const softwareVersion = document.getElementById('after-sales-software-version').value.trim();
        const pcbaOrderNumber = document.getElementById('after-sales-pcba-order').value.trim();

        // 重置到第一页
        afterSalesPageState.currentPage = 1;

        // 加载数据
        loadAfterSalesData(productCode, softwareVersion, pcbaOrderNumber);
    };

    // 重置表单
    window.resetAfterSalesForm = function() {
        // 重置表单内容
        document.getElementById('after-sales-product-code').value = '';
        document.getElementById('after-sales-software-version').value = '';
        document.getElementById('after-sales-pcba-order').value = '';

        // 重置分页和排序状态
        window.afterSalesPageState.currentPage = 1;
        window.afterSalesPageState.totalItems = 0;
        window.afterSalesPageState.totalPages = 10;
        window.afterSalesPageState.data = [];
        window.afterSalesPageState.sortField = 'outboundTime';
        window.afterSalesPageState.sortOrder = 'desc';

        // 清空表格内容
        const tableBody = document.getElementById('after-sales-results-tbody');
        tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center;">请输入查询条件</td></tr>';

        // 更新分页和排序图标
        updatePagination();
        updateSortIconsAfterSales();
    };

    // 刷新表格
    window.refreshAfterSalesTable = function() {
        const productCode = document.getElementById('after-sales-product-code').value.trim();
        const softwareVersion = document.getElementById('after-sales-software-version').value.trim();
        const pcbaOrderNumber = document.getElementById('after-sales-pcba-order').value.trim();

        loadAfterSalesData(productCode, softwareVersion, pcbaOrderNumber);
    };

    // 添加排序函数
    window.sortByAfterSales = function(field) {
        const state = window.afterSalesPageState;
        if (state.sortField === field) {
            state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            state.sortField = field;
            state.sortOrder = 'asc';
        }
        // 更新图标
        updateSortIconsAfterSales(field);
        // 重新加载第一页数据
        state.currentPage = 1;
        loadAfterSalesData(
            document.getElementById('after-sales-product-code').value.trim(),
            document.getElementById('after-sales-software-version').value.trim(),
            document.getElementById('after-sales-pcba-order').value.trim()
        );
    };

    // 添加更新排序图标函数
    function updateSortIconsAfterSales(activeField = window.afterSalesPageState.sortField) {
        const headers = document.querySelectorAll('#after-sales-query-content .data-table th.sortable');
        headers.forEach(header => {
            const icon = header.querySelector('i');
            if (!icon) return;

            const field = header.getAttribute('onclick').match(/sortByAfterSales\('([^']+)'\)/)[1];
            if (field === activeField) {
                icon.className = `fas fa-sort-${window.afterSalesPageState.sortOrder === 'asc' ? 'up' : 'down'}`;
            } else {
                icon.className = 'fas fa-sort';
            }
        });
    }

    // 添加静默查询函数 (添加排序参数)
    function silentAfterSalesSearch() {
        const productCode = document.getElementById('after-sales-product-code').value.trim();
        const softwareVersion = document.getElementById('after-sales-software-version').value.trim();
        const pcbaOrderNumber = document.getElementById('after-sales-pcba-order').value.trim();

        // 构建查询参数
        const params = new URLSearchParams();
        params.append('page', window.afterSalesPageState.currentPage);
        params.append('pageSize', window.afterSalesPageState.pageSize);
        // 添加排序参数
        if (window.afterSalesPageState.sortField) {
            params.append('sortField', window.afterSalesPageState.sortField);
            params.append('sortOrder', window.afterSalesPageState.sortOrder);
        }

        if (productCode) params.append('productCode', productCode);
        if (softwareVersion) params.append('softwareVersion', softwareVersion);
        if (pcbaOrderNumber) params.append('pcbaOrderNumber', pcbaOrderNumber);

        // 检查是否所有查询条件都为空，如果是，则不发送请求 (防止在初始空页面切换分页)
        if (!productCode && !softwareVersion && !pcbaOrderNumber) {
            // 可以选择更新UI提示或什么都不做
            Logger.log("Silent search skipped: No search criteria provided.");
            // 确保分页控件状态正确
            const tableBody = document.getElementById('after-sales-results-tbody');
            tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center;">请输入查询条件</td></tr>';
            window.afterSalesPageState.totalPages = 10; // 保持初始分页
            updatePagination();
            updateSortIconsAfterSales();
            return;
        }

        // 使用API调用但不显示加载状态
        fetch(`/api/after-sales-query/search?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderAfterSalesData(data.data, data.total);
                    // 不需要再次更新图标，因为renderAfterSalesData内部可能调用 applyColumnVisibility
                    // 排序图标在排序或加载时已更新
                } else {
                    // 静默失败处理，可以在控制台打印日志
                    Logger.error('Silent search failed:', data.message);
                    // 可以选择显示一个非侵入性的错误提示
                }
            })
            .catch(error => {
                Logger.error('分页查询失败:', error);
            });
    }

    // 加载售后数据
    function loadAfterSalesData(productCode = '', softwareVersion = '', pcbaOrderNumber = '') {
        // 显示加载中状态
        const tableBody = document.getElementById('after-sales-results-tbody');
        tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center;">加载中...</td></tr>';

        // 构建查询参数
        const params = new URLSearchParams();
        params.append('page', window.afterSalesPageState.currentPage);
        params.append('pageSize', window.afterSalesPageState.pageSize);
        // 添加排序参数
        if (window.afterSalesPageState.sortField) {
            params.append('sortField', window.afterSalesPageState.sortField);
            params.append('sortOrder', window.afterSalesPageState.sortOrder);
        }

        if (productCode) params.append('productCode', productCode);
        if (softwareVersion) params.append('softwareVersion', softwareVersion);
        if (pcbaOrderNumber) params.append('pcbaOrderNumber', pcbaOrderNumber);

        // 判断是否是初始加载（所有查询条件为空）
        if (!productCode && !softwareVersion && !pcbaOrderNumber) {
            window.afterSalesPageState.totalPages = 10;
            tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center;">请输入查询条件</td></tr>';
            updatePagination();
            updateSortIconsAfterSales();
            return;
        }

        // 使用真实的API调用
        fetch(`/api/after-sales-query/search?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderAfterSalesData(data.data, data.total);
                    updateSortIconsAfterSales();
                } else {
                    showError(data.message || '加载数据失败');
                }
            })
            .catch(error => {
                Logger.error('加载售后数据失败:', error);
                showError('网络错误，请稍后重试');
            });
    }

    // 渲染售后数据
    function renderAfterSalesData(data, total) {
        window.afterSalesPageState.data = data;
        window.afterSalesPageState.totalItems = total;
        window.afterSalesPageState.totalPages = Math.ceil(total / window.afterSalesPageState.pageSize);

        const tableBody = document.getElementById('after-sales-results-tbody');

        if (data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center;">暂无数据</td></tr>';
            updatePagination();
            return;
        }

        let html = '';
        data.forEach((item) => {
            // 安全地获取值，提供默认值 '-'
            const serialNumber = item.serialNumber || '-';
            const productCode = item.productCode || '-';
            const productModel = item.productModel || '-';
            const softwareVersion = item.softwareVersion || '-';
            const pcbaOrderNumber = item.pcbaOrderNumber || '无';
            const customerName = item.customerName || '-';
            const outboundTime = item.outboundTime || '-';
            const isOutOfStock = item.isOutOfStock;

            html += `
                <tr class="table-row">
                    <td>
                        <input type="checkbox" class="row-checkbox" value="${item.id}">
                    </td>
                    <td title="${serialNumber}">${serialNumber}</td>
                    <td title="${productCode}">${productCode}</td>
                    <td title="${productModel}">${productModel}</td>
                    <td title="${softwareVersion}">${softwareVersion}</td>
                    <td title="${pcbaOrderNumber}">${pcbaOrderNumber}</td>
                    <td>
                        <span class="status-tag status-${isOutOfStock ? 'yes' : 'no'}"
                              title="${isOutOfStock ? '是' : '否'}">
                            ${isOutOfStock ? '是' : '否'}
                        </span>
                    </td>
                    <td title="${customerName}">${customerName}</td>
                    <td title="${outboundTime}">${outboundTime}</td>
                </tr>
            `;
        });

        tableBody.innerHTML = html;

        // 绑定复选框事件
        document.querySelectorAll('.row-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateExportButtonText();

                // 更新全选框状态
                const allCheckboxes = document.querySelectorAll('.row-checkbox');
                const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
                document.getElementById('after-sales-select-all').checked = allChecked;
            });
        });

        // 更新分页控件
        updatePagination();

        // 应用列可见性设置
        applyColumnVisibility();
    }

    // 跳转到指定页
    window.jumpToAfterSalesPage = function(pageInput) {
        const page = parseInt(pageInput);
        if (isNaN(page) || page < 1 || page > window.afterSalesPageState.totalPages) {
            showToast('请输入有效的页码', 'warning');
            return;
        }

        window.changeAfterSalesPage(page);
    };

    // 更新分页控件
    function updatePagination() {
        const paginationControls = document.getElementById('after-sales-pagination-controls');
        if (!paginationControls) return;

        // 当前页和总页数
        const currentPage = window.afterSalesPageState.currentPage;
        const totalPages = window.afterSalesPageState.totalPages;
        
        // 自定义显示的页码数组，根据图片需求只显示 1,2,3,...,10
        let displayPages = [];
        
        // 始终显示页码1,2,3
        for (let i = 1; i <= Math.min(3, totalPages); i++) {
            displayPages.push(i);
        }
        
        // 如果总页数大于3，添加最后一页（10）
        if (totalPages > 3) {
            // 如果当前页大于3且不是最后一页，确保当前页被显示
            if (currentPage > 3 && currentPage < totalPages) {
                displayPages.push(currentPage);
            }
            // 添加最后一页
            displayPages.push(totalPages);
        }
        
        // 对页码数组去重并排序
        displayPages = [...new Set(displayPages)].sort((a, b) => a - b);

        // 上一页按钮
        let html = `
            <button class="page-button" onclick="window.changeAfterSalesPage(${currentPage - 1})"
                    ${currentPage === 1 ? 'disabled' : ''}>
                上一页
            </button>
        `;

        // 生成页码按钮
        let prevPage = 0;
        displayPages.forEach(page => {
            // 如果当前页码与前一个页码相差大于1，添加省略号
            if (page - prevPage > 1) {
                html += `<span>...</span>`;
            }
            
            // 添加页码按钮
            html += `
                <button class="page-button ${page === currentPage ? 'active' : ''}"
                        onclick="window.changeAfterSalesPage(${page})">
                    ${page}
                </button>
            `;
            
            prevPage = page;
        });

        // 下一页按钮
        html += `
            <button class="page-button" onclick="window.changeAfterSalesPage(${currentPage + 1})"
                    ${currentPage === totalPages ? 'disabled' : ''}>
                下一页
            </button>
            <div class="page-jump">
                <span>跳至</span>
                <input type="text" class="page-input" id="after-sales-page-jump-input" value="${currentPage}"
                       style="width: 50px;" onchange="window.jumpToAfterSalesPage(this.value)">
                <span>页</span>
            </div>
            <span class="total-count">共 ${window.afterSalesPageState.totalItems || 0} 条</span>
        `;

        paginationControls.innerHTML = html;
        
        // 添加页码输入框回车事件
        const pageInput = document.getElementById('after-sales-page-jump-input');
        if (pageInput) {
            pageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    window.jumpToAfterSalesPage(this.value);
                }
            });
        }
    }

    // 导出售后数据
    window.exportAfterSalesData = async function() {
        try {
            // 获取选中的行数据
            const selectedRows = getSelectedRowsData();
            let selectedIds = [];
            
            if (selectedRows.length > 0) {
                // 如果有选中的行，获取它们的ID
                selectedIds = selectedRows.map(row => row.id);
            }
            
            // 获取当前查询条件
            const productCode = document.getElementById('after-sales-product-code').value.trim();
            const softwareVersion = document.getElementById('after-sales-software-version').value.trim();
            const pcbaOrderNumber = document.getElementById('after-sales-pcba-order').value.trim();
            
            // 构建导出参数
            const params = new URLSearchParams();
            if (productCode) params.append('productCode', productCode);
            if (softwareVersion) params.append('softwareVersion', softwareVersion);
            if (pcbaOrderNumber) params.append('pcbaOrderNumber', pcbaOrderNumber);
            if (selectedIds.length > 0) params.append('selectedIds', JSON.stringify(selectedIds));
            
            // 生成导出URL
            const exportUrl = `/api/after-sales-query/export?${params.toString()}`;
            
            // 触发下载
            window.location.href = exportUrl;
            
            showToast('正在导出数据，请稍候...', 'info');
        } catch (error) {
            Logger.error('导出失败:', error);
            showToast('导出失败，请稍后重试', 'error');
        }
    };

    // 获取选中的行数据
    function getSelectedRowsData() {
        const selectedCheckboxes = document.querySelectorAll('#after-sales-results-tbody input[type="checkbox"]:checked');
        const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        // 如果没有选中的行，返回空数组
        if (selectedIds.length === 0) {
            return [];
        }

        // 从当前页面数据中过滤出选中的行
        return window.afterSalesPageState.data.filter(item => selectedIds.includes(item.id.toString()));
    }

    // 显示错误信息
    function showError(message) {
        const tableBody = document.getElementById('after-sales-results-tbody');
        tableBody.innerHTML = `<tr><td colspan="9" style="text-align: center; color: #f56c6c;">${message}</td></tr>`;
    }

    // 显示提示信息
    function showToast(message, type) {
        // 实际项目中可以使用弹窗库或自定义提示组件
        Logger.log(`[${type}] ${message}`);
        alert(message);
    }

    // 改变每页显示数量
    window.changeAfterSalesPageSize = function(size) {
        window.afterSalesPageState.pageSize = parseInt(size);
        window.afterSalesPageState.currentPage = 1;

        const productCode = document.getElementById('after-sales-product-code').value.trim();
        const softwareVersion = document.getElementById('after-sales-software-version').value.trim();
        const pcbaOrderNumber = document.getElementById('after-sales-pcba-order').value.trim();

        loadAfterSalesData(productCode, softwareVersion, pcbaOrderNumber);
    };

    // 列设置相关函数
    window.toggleAfterSalesColumnSettings = function() {
        const modal = document.getElementById('after-sales-column-settings-modal');
        modal.style.display = 'block';

        // 设置复选框状态
        for (const column of AFTER_SALES_COLUMNS) {
            const checkbox = document.getElementById(`col-after-sales-${column}`);
            if (checkbox) {
                checkbox.checked = window.afterSalesColumnState[column];
            }
        }

        // 设置全选复选框状态
        const allColumnsSelected = AFTER_SALES_COLUMNS.every(column => window.afterSalesColumnState[column]);
        document.getElementById('after-sales-select-all-columns').checked = allColumnsSelected;
    };

    window.closeAfterSalesColumnSettings = function() {
        document.getElementById('after-sales-column-settings-modal').style.display = 'none';
    };

    window.toggleAllAfterSalesColumns = function() {
        const allColumnsCheckbox = document.getElementById('after-sales-select-all-columns');
        const isChecked = allColumnsCheckbox.checked;

        for (const column of AFTER_SALES_COLUMNS) {
            const checkbox = document.getElementById(`col-after-sales-${column}`);
            if (checkbox) {
                checkbox.checked = isChecked;
            }
        }
    };

    window.applyAfterSalesColumnSettings = function() {
        for (const column of AFTER_SALES_COLUMNS) {
            const checkbox = document.getElementById(`col-after-sales-${column}`);
            if (checkbox) {
                window.afterSalesColumnState[column] = checkbox.checked;
            }
        }

        // 应用列设置
        applyColumnVisibility();

        // 关闭模态框
        closeAfterSalesColumnSettings();
    };

    // 应用列可见性
    function applyColumnVisibility() {
        const table = document.querySelector('.data-table');
        if (!table) return;

        const headerRow = table.querySelector('thead tr');
        const dataRows = table.querySelectorAll('tbody tr');

        // 跳过第一列（复选框）
        for (let i = 0; i < AFTER_SALES_COLUMNS.length; i++) {
            const column = AFTER_SALES_COLUMNS[i];
            const isVisible = window.afterSalesColumnState[column];

            // 设置表头列可见性
            if (headerRow.children[i + 1]) {
                headerRow.children[i + 1].style.display = isVisible ? '' : 'none';
            }

            // 设置数据列可见性
            dataRows.forEach(row => {
                if (row.children[i + 1]) {
                    row.children[i + 1].style.display = isVisible ? '' : 'none';
                }
            });
        }
    }

    // 切换全选
    window.toggleAfterSalesSelectAll = function() {
        const selectAll = document.getElementById('after-sales-select-all');
        const checkboxes = document.querySelectorAll('#after-sales-results-tbody input[type="checkbox"]');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        // 更新导出按钮文本
        updateExportButtonText();
    };

    // 更新导出按钮文本
    function updateExportButtonText() {
        const checkboxes = document.querySelectorAll('#after-sales-results-tbody input[type="checkbox"]:checked');
        const exportBtn = document.querySelector('.btn-export');

        if (checkboxes.length > 0) {
            exportBtn.innerHTML = `<i class="fas fa-download"></i> 导出所选 (${checkboxes.length})`;
        } else {
            exportBtn.innerHTML = `<i class="fas fa-download"></i> 导出数据`;
        }
    }

    // 修改切换页码函数
    window.changeAfterSalesPage = function(page) {
        const totalPages = window.afterSalesPageState.totalPages;
        // 确保页码在有效范围内
        if (page < 1) page = 1;
        if (page > totalPages) page = totalPages;

        if (page === window.afterSalesPageState.currentPage) {
            return; // 如果页码未改变，则不执行任何操作
        }

        window.afterSalesPageState.currentPage = page;
        
        // 更新跳转输入框的值
        const pageInput = document.getElementById('after-sales-page-jump-input');
        if(pageInput) {
            pageInput.value = page;
        }

        // 使用静默查询加载新页面数据
        silentAfterSalesSearch();
    };
})();
