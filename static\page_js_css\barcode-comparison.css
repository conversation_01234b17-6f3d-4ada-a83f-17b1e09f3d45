/* 基础变量定义 */
.barcode-container {
    --spacing-base: 1rem;
    --spacing-sm: 0.5rem;
    --spacing-lg: 1.5rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: clamp(1.25rem, 2vw, 1.5rem);
    
    padding: 0;
    background-color: #f7f7f7;
    height: 100%;
}

.barcode-container .barcode-content {
    height: 100%;
    padding: var(--spacing-lg);
}

.barcode-container .barcode-title {
    font-size: var(--font-size-xl);
    font-weight: 500;
    margin-bottom: var(--spacing-lg);
}

.barcode-container .main-content {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    height: calc(100% - 3.75rem);
    overflow: auto;
}

.barcode-container .operation-area {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .barcode-container .operation-area {
        grid-template-columns: 1fr;
    }
    
    .barcode-container .results-panel {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .barcode-container .barcode-content {
        padding: var(--spacing-sm);
    }
    
    .barcode-container .main-content {
        padding: 1rem;
    }
}

.barcode-container .form-group {
    margin-bottom: 1rem;
}

.barcode-container .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-size: var(--font-size-sm);
}

.barcode-container .search-box {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    width: 100%;
}

/* 输入框样式 */
.barcode-container input[type="text"] {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e8e8e8;
    border-radius: 0.25rem;
    font-size: var(--font-size-sm);
    transition: border-color 0.3s;
}

/* 按钮样式 */
.barcode-container .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.25rem;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.3s;
    gap: 0.375rem;
}

/* 结果面板 */
.barcode-container .results-panel {
    margin-top: 1.5rem;
    width: 100%;
}

.barcode-container .work-order-info {
    width: 100%;
}

.barcode-container .panel-title {
    font-size: var(--font-size-lg);
    font-weight: 500;
    margin-bottom: 1rem;
    color: #333;
}

/* 表格样式 */
.barcode-container table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: var(--font-size-base);
    table-layout: fixed;
}

.barcode-container th,
.barcode-container td {
    padding: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 设置列 */
.barcode-container th:nth-child(1) { width: 5%; }  /* ID */
.barcode-container th:nth-child(2) { width: 20%; } /* 工单号 */
.barcode-container th:nth-child(3) { width: 25%; } /* 产品型号 */
.barcode-container th:nth-child(4) { width: 30%; } /* SN号 */
.barcode-container th:nth-child(5) { width: 10%; } /* 测试结果 */
.barcode-container th:nth-child(6) { width: 10%; } /* 比对结果 */

/* 图标尺寸 */
.barcode-container .icon-search,
.barcode-container .icon-compare,
.barcode-container .icon-export {
    width: 1rem;
    height: 1rem;
    fill: currentColor;
}

/* 其他样式保持不变，但单位转换为 rem */
/* ... */

/* 以下补充原有的具体样式 */
.barcode-container .btn-search {
    background: #2a2a2a;
    color: white;
    min-width: 100px;
    height: 2.75rem;
}

.barcode-container .btn-search:hover {
    background: #404040;
}

.barcode-container .btn-compare {
    background: #2a2a2a;
    color: white;
    width: 100%;
    margin-bottom: 0.75rem;
}

.barcode-container .btn-compare:hover {
    background: #404040;
}

.barcode-container .btn-export {
    background: #52c41a;
    border: 2px solid #52c41a;
    color: white;
    min-width: 100px;
    height: 2.75rem;
    margin: 0;
}

.barcode-container .btn-export:hover {
    background: #73d13d;
    border-color: #73d13d;
}

/* 操作指南样式 */
.barcode-container .operation-guide {
    background: #fafafa;
    border-radius: 0.25rem;
    padding: 1rem;
    position: relative;
}

/* 添加垂直分割线 */
.barcode-container .operation-guide::before {
    content: '';
    position: absolute;
    left: -0.75rem;
    top: 0;
    height: 100%;
    width: 2px;
    background-color: #2a2a2a;
    border-radius: 1px;
}

.barcode-container .operation-guide h3 {
    font-size: var(--font-size-lg);
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: #333;
}

.barcode-container .operation-guide ol {
    padding-left: 1rem;
    color: #666;
    font-size: var(--font-size-sm);
}

.barcode-container .operation-guide li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

/* 表格悬停效果 */
.barcode-container tr:hover td {
    background: #fafafa;
}

/* 响应式���局 */
@media (max-width: 768px) {
    .barcode-container .operation-area {
        grid-template-columns: 1fr;
    }
    
    .barcode-container .results-panel {
        grid-template-columns: 1fr;
    }
    
    .barcode-container .search-box {
        flex-direction: column;
    }
    
    .barcode-container .btn-search {
        width: 100%;
    }
    
    .barcode-container table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .barcode-container th,
    .barcode-container td {
        white-space: nowrap;
    }
    
    .barcode-container .btn-export {
        height: 2.75rem;
    }
    
    .barcode-container .operation-guide::before {
        left: 0;
        top: -0.75rem;
        width: 100%;
        height: 3px;
    }
}

@media (max-width: 480px) {
    .barcode-container .barcode-content {
        padding: var(--spacing-sm);
    }
    
    .barcode-container .main-content {
        padding: 1rem;
    }
    
    .barcode-container .btn-search {
        width: 100%;
    }
}

/* 添加新的表单项样式 */
.barcode-container .form-item {
    margin-top: 1rem;
}

.barcode-container .form-item label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-size: var(--font-size-sm);
}

.barcode-container .form-item input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e8e8e8;
    border-radius: 0.25rem;
    font-size: var(--font-size-sm);
    transition: border-color 0.3s;
}

.barcode-container .form-item input:focus {
    border-color: #2a2a2a;
    outline: none;
}

/* 调整搜索框上边距 */
.barcode-container .search-box {
    margin-bottom: 1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .barcode-container .form-item {
        margin-top: 0.75rem;
    }
}

/* 修改表单项布局样式 */
.barcode-container .form-items-row {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.barcode-container .form-item {
    margin-top: 0; /* 覆盖之前的margin-top */
}

/* 设置各个输入框的宽度 */
.barcode-container .form-item--barcode1,
.barcode-container .form-item--barcode2 {
    flex: 1;  /* 使用flex: 1让两个条码输入框平分剩余空间 */
}

.barcode-container .form-item--snbytes {
    min-width: 200px;  /* 设置最小宽度 */
    max-width: 200px;  /* 设置最大宽度 */
    flex: none;  /* 防止被flex拉伸 */
}

/* 调整number类型输入框的样式 */
.barcode-container .form-item--snbytes input[type="number"] {
    appearance: textfield;
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    width: 100%;
    text-align: center;
    padding: 0.5rem;
    font-size: 1rem;
    min-width: 80px;  /* 确保输入框本身也有最小宽度 */
}

/* 去除number类型输入框的上下箭头 */
.barcode-container .form-item--snbytes input[type="number"]::-webkit-outer-spin-button,
.barcode-container .form-item--snbytes input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .barcode-container .form-items-row {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .barcode-container .form-item--barcode1,
    .barcode-container .form-item--barcode2,
    .barcode-container .form-item--snbytes {
        width: 100%;  /* 在移动端时占满宽度 */
    }
}

/* 更新比对状态相关样式 */
.barcode-container tr.bg-blue-100 td {
    background-color: #e6f7ff !important; /* 浅蓝色背景 - 已比对记录 */
}

.barcode-container tr.bg-green-100 td {
    background-color: #52c41a !important; /* 使用深绿色背景 */
    color: #ffffff !important; /* 文字改为白色以提高可读性 */
}

/* 优化动画效果 */
.barcode-container tbody tr {
    transition: all 0.3s ease;
}

/* 添加行移动的动画效果 */
@keyframes moveRow {
    from {
        opacity: 0.7;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.barcode-container tr.comparing {
    animation: moveRow 0.3s ease-out;
}

/* 输入框焦点样式 */
.barcode-container .form-item input:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

/* 更新动画效果 */
@keyframes highlightNewRow {
    0% {
        background-color: #e6f7ff;
        transform: translateY(-5px);
    }
    50% {
        background-color: #e6f7ff;
        transform: translateY(0);
    }
    100% {
        background-color: #e6f7ff;
    }
}

.barcode-container tr.comparing {
    animation: highlightNewRow 0.5s ease-out;
}

/* 保表格容器有固定高度和滚动行为 */
.barcode-container .work-order-info {
    max-height: calc(100vh - 400px);
    overflow-y: auto;
    scroll-behavior: smooth;
}

/* 优化滚动条样式 */
.barcode-container .work-order-info::-webkit-scrollbar {
    width: 6px;
}

.barcode-container .work-order-info::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.barcode-container .work-order-info::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.barcode-container .work-order-info::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 添加进度条相关样式 */
.barcode-container .progress-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.25rem;
}

.barcode-container .progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    color: #333;
    font-size: var(--font-size-sm);
}

.barcode-container .progress-bar {
    height: 0.5rem;
    background: #e9ecef;
    border-radius: 0.25rem;
    overflow: hidden;
}

.barcode-container .progress-bar__fill {
    height: 100%;
    background: #1890ff;
    border-radius: 0.25rem;
    transition: width 0.3s ease;
}

/* 进度条动画效果 */
@keyframes progressPulse {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.6;
    }
}

.barcode-container .progress-bar__fill {
    animation: progressPulse 2s ease-in-out infinite;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .barcode-container .progress-section {
        padding: 0.75rem;
    }
}

/* 添加操作行样式 */
.barcode-container .action-row {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
    width: 100%;
}

/* 状态片样式 */
.barcode-container .compare-status-card {
    flex: 1;
    height: 2.75rem;
    padding: 0 1rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

/* 调整导出按钮样式 */
.barcode-container .btn-export {
    background: #52c41a;
    border: 2px solid #52c41a;
    color: white;
    min-width: 100px;
    height: 2.75rem;
    margin: 0;
}

/* 状态样式 */
.barcode-container .compare-status-card.status-success {
    background: #f6ffed;
    border: 2px solid #b7eb8f;
    color: #52c41a;
}

.barcode-container .compare-status-card.status-error {
    background: #fff2f0;
    border: 2px solid #ffccc7;
    color: #ff4d4f;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .barcode-container .action-row {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .barcode-container .compare-status-card,
    .barcode-container .btn-export {
        width: 100%;
    }
    
    .barcode-container .btn-export {
        height: 2.75rem;
    }
}

/* 添加分页相关样式 */
.barcode-container .pagination-section {
    margin-top: 1rem;
    padding: 1rem;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.barcode-container .pagination-info {
    color: #666;
    font-size: var(--font-size-sm);
}

.barcode-container .pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.barcode-container .btn-page {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid #d9d9d9;
    border-radius: 0.25rem;
    background: white;
    color: #666;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.3s;
}

.barcode-container .btn-page:hover:not(:disabled) {
    border-color: #1890ff;
    color: #1890ff;
}

.barcode-container .btn-page:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.barcode-container .page-info {
    color: #666;
    font-size: var(--font-size-sm);
}

.barcode-container .icon-arrow {
    width: 1rem;
    height: 1rem;
    fill: currentColor;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .barcode-container .pagination-section {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }
}

/* 优化输入框和标签样式，添加层次感 */
.barcode-container .form-item {
    margin-top: 0;
    position: relative;
}

.barcode-container .form-item label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* 工单号输入框样式 */
.barcode-container #workOrderInput {
    flex: 1;
    width: 100%;
    height: 2.75rem;
    padding: 0.75rem 1rem;
    font-size: 1.125rem;
    border: 2px solid #e8e8e8;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

/* 条码输入框样式优化 */
.barcode-container .form-items-row {
    display: flex;
    gap: 1.25rem;
    margin-top: 1.25rem;
}

/* 条码1和条码2输入框样式 */
.barcode-container .form-item--barcode1,
.barcode-container .form-item--barcode2 {
    width: 42%;
}

.barcode-container .form-item--barcode1 input,
.barcode-container .form-item--barcode2 input {
    height: 2.75rem;
    padding: 0.75rem 1rem;
    font-size: 1.125rem;
    font-family: 'Consolas', monospace;
    letter-spacing: 0.05em;
    border: 2px solid #e8e8e8;
    border-radius: 0.375rem;
    background-color: #fafafa;
    transition: all 0.3s ease;
}

/* SN字节数输入框样式 */
.barcode-container .form-item--snbytes {
    width: 16%;
}

.barcode-container .form-item--snbytes input {
    height: 2.75rem;
    padding: 0.75rem;
    font-size: 1.125rem;
    font-weight: 500;
    text-align: center;
    border: 2px solid #e8e8e8;
    border-radius: 0.375rem;
    background-color: #f5f5f5;
    transition: all 0.3s ease;
}

/* 输入框焦点状态 */
.barcode-container input:focus {
    outline: none;
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    background-color: #fff;
}

/* 条码输入框焦点状态特殊样式 */
.barcode-container .form-item--barcode1 input:focus,
.barcode-container .form-item--barcode2 input:focus {
    background-color: #fff;
    border-color: #52c41a;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

/* 标题和状态文本样式优化 */
.barcode-container .barcode-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #262626;
    margin-bottom: 2rem;
    letter-spacing: 0.02em;
}

.barcode-container .status-text {
    font-size: 0.9375rem;
    font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .barcode-container .form-items-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .barcode-container .form-item--barcode1,
    .barcode-container .form-item--barcode2,
    .barcode-container .form-item--snbytes {
        width: 100%;
    }
    
    .barcode-container .form-item input {
        height: 2.75rem;
        font-size: 1.125rem;
    }
}

/* 工单号标签样式 */
.barcode-container .form-group > label {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

/* 条码1、条码2、产品SN号位数标签样式 */
.barcode-container .form-item--barcode1 label,
.barcode-container .form-item--barcode2 label,
.barcode-container .form-item--snbytes label {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

/* 比对状态卡片样式修改 */
.barcode-container .compare-status-card {
    flex: 1;
    height: 2.75rem;
    padding: 0 1rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

/* 保持状态样式不变 */
.barcode-container .compare-status-card.status-success {
    background: #f6ffed;
    border: 2px solid #b7eb8f;
    color: #52c41a;
}

.barcode-container .compare-status-card.status-error {
    background: #fff2f0;
    border: 2px solid #ffccc7;
    color: #ff4d4f;
}

/* 响应式布局中保持字体大小 */
@media (max-width: 768px) {
    .barcode-container .form-group > label,
    .barcode-container .form-item--barcode1 label,
    .barcode-container .form-item--barcode2 label,
    .barcode-container .form-item--snbytes label {
        font-size: 16px;
    }
}
