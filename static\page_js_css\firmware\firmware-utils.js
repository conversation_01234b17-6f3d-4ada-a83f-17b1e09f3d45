// 固件管理共享工具函数和API
window.FirmwareUtils = {
    // 状态映射
    statusMap: {
        'active': '已生效',
        'pending': '待审核',
        'rejected': '审核退回',
        'obsolete': '已作废'
    },
    
    statusColorMap: {
        'active': '#67C23A',
        'pending': '#E6A23C',
        'rejected': '#F56C6C',
        'obsolete': '#F56C6C'
    },
    
    // 日期格式化
    formatDate(dateString) {
        if (!dateString || dateString === '-') return '-';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '-';
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            Logger.error('Date format error:', error);
            return '-';
        }
    },
    
    // 计算天数差
    calculateDays(startDate, endDate) {
        if (!startDate || !endDate) return 0;
        try {
            const start = new Date(startDate);
            const end = new Date(endDate);
            if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0;
            return Math.ceil((end - start) / (1000 * 60 * 60 * 24));
        } catch (error) {
            Logger.error('Calculate days error:', error);
            return 0;
        }
    },
    
    // 导出Excel功能
    exportToExcel(data, filename) {
        if (!data || data.length === 0) {
            ElementPlus.ElMessage.warning('没有数据可导出');
            return;
        }
        
        Logger.log(`准备导出${filename}数据...`, data);
        ElementPlus.ElMessage.info(`正在导出${filename}...`);
        
        // 实际实现时可以使用 SheetJS 等库
        // 这里先用简单的CSV格式导出
        this.exportAsCSV(data, filename);
    },
    
    // 简单的CSV导出实现
    exportAsCSV(data, filename) {
        try {
            if (!data.length) return;
            
            // 获取表头
            const headers = Object.keys(data[0]);
            const csvContent = [
                headers.join(','),
                ...data.map(row => 
                    headers.map(header => {
                        let value = row[header];
                        if (Array.isArray(value)) {
                            value = value.map(v => v.model || v).join(';');
                        }
                        return `"${String(value || '').replace(/"/g, '""')}"`;
                    }).join(',')
                )
            ].join('\n');
            
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${filename}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            ElementPlus.ElMessage.success('导出成功');
        } catch (error) {
            Logger.error('CSV export error:', error);
            ElementPlus.ElMessage.error('导出失败');
        }
    },
    
    // 导出图片功能
    exportAsImage(elementRef, filename) {
        if (!elementRef) {
            ElementPlus.ElMessage.error('未找到要导出的元素');
            return;
        }
        
        if (window.html2canvas) {
            html2canvas(elementRef, {
                backgroundColor: '#ffffff',
                useCORS: true,
                scale: 2
            }).then(canvas => {
                const link = document.createElement('a');
                link.href = canvas.toDataURL('image/png');
                link.download = `${filename}.png`;
                link.click();
                ElementPlus.ElMessage.success('图片导出成功');
            }).catch(error => {
                Logger.error('Image export error:', error);
                ElementPlus.ElMessage.error('图片导出失败');
            });
        } else {
            ElementPlus.ElMessage.error('html2canvas未加载，无法导出图片');
        }
    },
    
    // 文件大小格式化
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // 验证文件类型
    validateFile(file, allowedTypes = [], maxSize = 50 * 1024 * 1024) {
        if (!file) {
            return { valid: false, message: '请选择文件' };
        }
        
        if (maxSize && file.size > maxSize) {
            return { 
                valid: false, 
                message: `文件大小超过限制，最大允许${this.formatFileSize(maxSize)}` 
            };
        }
        
        if (allowedTypes.length > 0) {
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
                return { 
                    valid: false, 
                    message: `不支持的文件类型，仅支持：${allowedTypes.join(', ')}` 
                };
            }
        }
        
        return { valid: true, message: '文件验证通过' };
    }
};

// 固件管理API封装
window.FirmwareAPI = {
    baseUrl: '/api/firmware',
    
    // 通用请求方法
    async request(url, options = {}) {
        try {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                },
                ...options
            };
            
            const response = await fetch(url, defaultOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            Logger.error('API request error:', error);
            throw error;
        }
    },
    
    // 获取所有固件
    async getAllFirmware(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `${this.baseUrl}/list${queryString ? '?' + queryString : ''}`;
        return await this.request(url);
    },
    
    // 获取待审核固件
    async getPendingFirmware() {
        return await this.request(`${this.baseUrl}/pending`);
    },
    
    // 获取作废固件
    async getObsoleteFirmware() {
        return await this.request(`${this.baseUrl}/obsolete`);
    },
    
    // 获取使用记录
    async getUsageRecords(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = `${this.baseUrl}/usage-records${queryString ? '?' + queryString : ''}`;
        return await this.request(url);
    },
    
    // 上传固件
    async uploadFirmware(formData) {
        return await this.request(`${this.baseUrl}/upload`, {
            method: 'POST',
            body: formData,
            headers: {} // 让浏览器自动设置Content-Type for FormData
        });
    },
    
    // 审核固件
    async approveFirmware(firmwareId, note = '') {
        return await this.request(`${this.baseUrl}/${firmwareId}/approve`, {
            method: 'POST',
            body: JSON.stringify({ note })
        });
    },
    
    // 拒绝固件
    async rejectFirmware(firmwareId, reason = '') {
        return await this.request(`${this.baseUrl}/${firmwareId}/reject`, {
            method: 'POST',
            body: JSON.stringify({ reason })
        });
    },
    
    // 下载固件
    async downloadFirmware(firmwareId, downloadInfo) {
        return await this.request(`${this.baseUrl}/${firmwareId}/download`, {
            method: 'POST',
            body: JSON.stringify(downloadInfo)
        });
    },
    
    // 更新固件版本
    async updateFirmware(firmwareId, formData) {
        return await this.request(`${this.baseUrl}/${firmwareId}/update`, {
            method: 'POST',
            body: formData,
            headers: {} // 让浏览器自动设置Content-Type for FormData
        });
    },
    
    // 获取版本历史
    async getVersionHistory(firmwareId) {
        return await this.request(`${this.baseUrl}/${firmwareId}/history`);
    }
};

Logger.log('Firmware utils and API loaded successfully'); 