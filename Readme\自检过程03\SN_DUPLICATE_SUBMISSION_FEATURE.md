# SN号重复提交检测功能

## 功能概述

实现了对同一个阶段同一个角色下相同SN号多次提交的检测和提示功能。当用户尝试录入已经提交过的SN号时，系统会弹窗提示用户该SN号的提交状态，并询问是否继续。

## 实现方案

### 1. 后端API增强

**文件**: `routes/quality_inspection.py`

**修改内容**:
- 在 `check_serial_number` 接口中新增 `stage` 和 `inspector_role` 参数
- 查询 `product_inspection_status` 表，检查指定SN号在当前阶段和角色下的提交状态
- 返回提交状态信息：`already_submitted`、`submitted_by`、`submitted_at`

**新增返回字段**:
```json
{
  "success": true,
  "exists": true,
  "product_id": 123,
  "is_rework": false,
  "already_submitted": "partial",  // 新增：提交类型 (partial/final/null)
  "submitted_by": "张三",          // 新增：提交人
  "submitted_at": "2025-01-01T10:00:00"  // 新增：提交时间
}
```

### 2. 前端逻辑增强

**文件**: `static/page_js_css/ScanSN.js`

**修改内容**:

1. **存储当前上下文**:
   - 在 `showScanSerialNumberUI` 函数中存储当前的 `stage` 和 `role`
   - 使用全局变量 `window.currentScanStage` 和 `window.currentScanRole`

2. **增强SN检查**:
   - 修改 `checkSerialNumber` 函数，支持传递 `stage` 和 `inspector_role` 参数
   - 在 `processScanedSN` 函数中调用时传递当前上下文信息

3. **重复提交提示**:
   - 检测到SN号已提交时，显示确认对话框
   - 区分部分提交和最终提交，显示不同的提示信息
   - 显示提交人和提交时间信息

## 用户体验流程

### 场景1：SN号未提交过
1. 用户扫描SN号
2. 系统检查通过，直接添加到扫描列表
3. 正常流程继续

### 场景2：SN号已部分提交
1. 用户扫描SN号
2. 系统检测到该SN在当前阶段和角色下已做部分提交
3. 弹出确认对话框：
   ```
   SN号重复提交提示
   
   该SN号已做部分提交，是否继续？
   
   提交人：张三
   提交时间：2025-01-01 10:00:00
   
   [继续录入] [取消]
   ```
4. 用户选择继续或取消

### 场景3：SN号已最终提交
1. 用户扫描SN号
2. 系统检测到该SN在当前阶段和角色下已做最终提交
3. 弹出确认对话框：
   ```
   SN号重复提交提示
   
   该SN号已做最终提交，是否继续？
   
   提交人：李四
   提交时间：2025-01-01 15:30:00
   
   [继续录入] [取消]
   ```
4. 用户选择继续或取消

## 技术细节

### 数据库查询
```sql
SELECT submission_type, inspector, inspection_time 
FROM product_inspection_status 
WHERE work_order_id = ? 
  AND product_id = ? 
  AND stage = ? 
  AND inspector_role = ?
```

### 前端状态管理
```javascript
// 存储当前扫描上下文
window.currentScanStage = stage;    // 如: "assembly"
window.currentScanRole = role;      // 如: "first"

// 调用检查接口时传递上下文
checkSerialNumber(workOrderId, serialNo, stage, role)
```

### 提示对话框配置
```javascript
SweetAlert.custom({
    title: 'SN号重复提交提示',
    html: message.replace(/\n/g, '<br>'),
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: '继续录入',
    cancelButtonText: '取消',
    confirmButtonColor: '#f59e0b',
    cancelButtonColor: '#64748b'
})
```

## 测试验证

### 测试用例

1. **正常流程测试**
   - 录入新SN号，验证正常添加到扫描列表

2. **部分提交重复测试**
   - 先部分提交某个SN号
   - 再次录入相同SN号，验证提示信息正确

3. **最终提交重复测试**
   - 先最终提交某个SN号
   - 再次录入相同SN号，验证提示信息正确

4. **跨阶段测试**
   - 在不同阶段录入相同SN号，验证不会误报

5. **跨角色测试**
   - 在不同角色录入相同SN号，验证不会误报

### 测试脚本
提供了 `test_sn_duplicate_check.py` 脚本用于API接口测试。

## 注意事项

1. **性能考虑**: 每次SN扫描都会查询数据库，在高频使用场景下需要注意性能
2. **用户体验**: 提示信息清晰明确，避免用户困惑
3. **数据一致性**: 确保提交状态查询与实际提交状态保持一致
4. **错误处理**: 网络异常或数据库错误时的降级处理

## 扩展性

该功能设计具有良好的扩展性：
- 可以轻松添加更多提交状态类型
- 可以扩展到其他类似的重复检测场景
- 提示信息可以根据业务需求进行定制
