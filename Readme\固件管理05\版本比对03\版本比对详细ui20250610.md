# 版本比对系统 UI 设计指南

## 1. 设计原则

### 1.1 简洁明了
- 界面元素简洁，减少视觉干扰
- 信息层次清晰，重点突出
- 操作流程直观，减少学习成本

### 1.2 高效可用
- 关键操作便捷可达
- 减少不必要的点击和页面跳转
- 提供清晰的反馈和状态指示

### 1.3 一致性
- 视觉元素保持一致
- 交互模式保持一致
- 术语和命名保持一致

### 1.4 响应式
- 适配不同屏幕尺寸
- 保持关键功能在各种设备上的可用性
- 优化不同设备的交互体验

## 2. 色彩系统

### 2.1 主色调
- **主色**：系统默认主题色 (#0f172a)
- **次要色**：灰色系 (#64748b)

### 2.2 功能色
- **成功色**：绿色 (#10b981)
- **错误色**：红色 (#ef4444)
- **警告色**：黄色 (#f59e0b)
- **信息色**：蓝色 (#3b82f6)

### 2.3 中性色
- **背景色**：浅灰 (#f8fafc)
- **卡片背景**：白色 (#ffffff)
- **分割线**：淡灰 (#e2e8f0)
- **文本主色**：深灰 (#1e293b)
- **文本次要色**：中灰 (#64748b)

## 3. 排版

### 3.1 字体
- **系统字体栈**：使用系统默认无衬线字体
\`\`\`css
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
\`\`\`

### 3.2 字号
- **页面标题**：24px (1.5rem)
- **卡片标题**：18px (1.125rem)
- **正文文本**：16px (1rem)
- **辅助文本**：14px (0.875rem)
- **小号文本**：12px (0.75rem)

### 3.3 字重
- **粗体**：600 (标题、重点内容)
- **中等**：500 (按钮文本、标签)
- **常规**：400 (正文文本)

## 4. 组件设计

### 4.1 卡片
- **外观**：圆角 8px，轻微阴影
- **内边距**：标题区域 16px，内容区域 16px-24px
- **间距**：卡片之间间距 24px

### 4.2 输入框
- **高度**：36px (默认), 32px (紧凑型)
- **圆角**：6px
- **边框**：1px 实线，颜色 #e2e8f0
- **聚焦状态**：边框颜色变为主题色，轻微阴影

### 4.3 按钮
- **主要按钮**：填充背景，白色文本
- **次要按钮**：边框样式，主题色文本
- **危险按钮**：红色背景或边框
- **尺寸**：
  - 默认：高度 36px，内边距水平 16px
  - 小号：高度 32px，内边距水平 12px
- **状态**：默认、悬停、激活、禁用

### 4.4 表格
- **表头**：背景色略深于表格主体
- **行高**：默认 48px，紧凑型 40px
- **分割线**：1px 实线，颜色 #e2e8f0
- **悬停效果**：行背景色变化
- **空状态**：居中显示提示信息和图标

### 4.5 徽章
- **尺寸**：小巧紧凑，高度 20px-24px
- **圆角**：圆形或半圆形
- **变体**：
  - 填充式：背景色为功能色，文本为白色
  - 轮廓式：边框为功能色，文本为功能色
  - 轻量式：背景色为功能色的淡色版，文本为功能色

## 5. 布局系统

### 5.1 网格系统
- **列数**：12列
- **间距**：24px (大屏), 16px (中屏), 12px (小屏)
- **断点**：
  - 小屏：< 768px
  - 中屏：768px - 1280px
  - 大屏：> 1280px

### 5.2 响应式布局
- **大屏**：三栏布局，比对记录表格占据全宽
- **中屏**：两栏布局，操作面板移至底部
- **小屏**：单栏布局，垂直堆叠所有元素

### 5.3 间距规范
- **组件内部间距**：8px, 16px, 24px
- **组件之间间距**：16px, 24px, 32px
- **区块之间间距**：32px, 48px, 64px

## 6. 交互设计

### 6.1 状态反馈
- **加载状态**：按钮显示加载动画，禁用点击
- **成功状态**：显示成功图标和消息
- **错误状态**：显示错误图标和消息
- **空状态**：显示提示信息和引导操作

### 6.2 表单交互
- **实时验证**：输入时或失焦时验证
- **错误提示**：在输入框下方显示错误信息
- **必填标记**：在标签旁显示星号或文本标记
- **禁用状态**：灰色显示，不可交互

### 6.3 表格交互
- **排序**：点击表头进行排序
- **筛选**：表头提供筛选功能
- **分页**：底部显示分页控件
- **行操作**：每行末尾提供操作按钮

## 7. 图标系统

### 7.1 功能图标
- **尺寸**：16px, 20px, 24px
- **风格**：线性图标，统一粗细
- **常用图标**：
  - 搜索图标：表示查询功能
  - 刷新图标：表示重置功能
  - 对勾图标：表示正确状态
  - 叉图标：表示错误状态
  - 历史图标：表示记录功能
  - 垃圾桶图标：表示删除功能

### 7.2 状态图标
- **成功图标**：绿色对勾
- **错误图标**：红色叉号
- **警告图标**：黄色感叹号
- **信息图标**：蓝色问号或信息符号

## 8. 可访问性

### 8.1 颜色对比度
- 文本与背景的对比度符合 WCAG 2.0 AA 标准
- 不仅依靠颜色传达信息，同时使用图标、文本等辅助手段

### 8.2 键盘可访问性
- 所有交互元素可通过键盘访问
- 提供清晰的焦点状态指示

### 8.3 屏幕阅读器支持
- 使用语义化 HTML 结构
- 为非文本内容提供替代文本
- 使用适当的 ARIA 属性增强可访问性

## 9. 界面示例

### 9.1 基准版本查询区域

\`\`\`
┌─────────────────────────────┐
│ 基准版本查询                │
│ 输入基准SN号                │
├─────────────────────────────┤
│ ┌───────────────┐ ┌──────┐ │
│ │ SN001         │ │ 查询 │ │
│ └───────────────┘ └──────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │ 基准信息               │ │
│ │ 型号: Model-A100       │ │
│ │ 软件: v2.1.3           │ │
│ │ 构建: 2024-01-15       │ │
│ │ 脉冲: v1.2.0           │ │
│ │ 背板: v3.0.1           │ │
│ └─────────────────────────┘ │
│                             │
│ ─────────────────────────── │
│                             │
│ 比对基准值                  │
│                             │
│ 软件版本                    │
│ ┌─────────────────────────┐ │
│ │ v2.1.3                 │ │
│ └─────────────────────────┘ │
│                             │
│ 构建日期                    │
│ ┌─────────────────────────┐ │
│ │ 2024-01-15             │ │
│ └─────────────────────────┘ │
│                             │
│ 背板总线版本                │
│ ┌─────────────────────────┐ │
│ │ v3.0.1                 │ │
│ └─────────────────────────┘ │
│                             │
│ 高速IO版本                  │
│ ┌─────────────────────────┐ │
│ │ v1.2.0                 │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
\`\`\`

### 9.2 待比对版本查询区域

\`\`\`
┌─────────────────────────────┐
│ 待比对版本查询              │
│ 输入待比对SN号              │
├─────────────────────────────┤
│ ┌───────────────┐ ┌──────┐ │
│ │ SN002         │ │ 比对 │ │
│ └───────────────┘ └──────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │ 待比对信息             │ │
│ │ 工单号: WO-2024-001    │ │
│ │ 产品型号: Model-A100   │ │
│ │ 软件版本: v2.1.3       │ │
│ │ 构建日期: 2024-01-15   │ │
│ │ 背板总线版本: v3.0.1   │ │
│ │ 高速IO版本: v1.2.0     │ │
│ │ 测试状态: 已完成       │ │
│ │ 测试时间: 2024-01-20   │ │
│ └─────────────────────────┘ │
│                             │
│ ─────────────────────────── │
│                             │
│ 比对结果 [✓ 版本正确]       │
│                             │
│ 软件版本: [✓ 正确]          │
│ 构建日期: [✓ 正确]          │
│ 背板总线版本: [✓ 正确]      │
│ 高速IO版本: [✓ 正确]        │
│                             │
└─────────────────────────────┘
\`\`\`

### 9.3 操作面板区域

\`\`\`
┌─────────────────────────────┐
│ 操作面板                    │
│ 系统操作和统计              │
├─────────────────────────────┤
│ ┌─────────────────────────┐ │
│ │    重置当前数据         │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │    清空比对记录         │ │
│ └─────────────────────────┘ │
│                             │
│ ─────────────────────────── │
│                             │
│ 统计信息                    │
│                             │
│ 总比对次数: [10]            │
│ 版本正确:   [8]             │
│ 版本错误:   [2]             │
│ 正确率:     [80%]           │
│                             │
│ ─────────────────────────── │
│                             │
│ 使用说明                    │
│                             │
│ 1. 输入基准SN号查询基准版本 │
│ 2. 输入待比对SN号进行比对   │
│ 3. 查看比对结果和历史记录   │
│                             │
└─────────────────────────────┘
\`\`\`

### 9.4 比对记录表格区域

\`\`\`
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 比对记录表格                                [10 条记录]        [📥 导出记录]   │
│ 记录所有版本比对的详细信息和结果                                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│ ┌─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┐                         │
│ │工单 │产品 │SN号 │软件 │构建 │背板 │高速IO│测试 │比对 │                         │
│ │号   │型号 │     │版本 │日期 │总线 │版本 │状态 │结果 │                         │
│ │     │     │     │     │     │版本 │     │     │     │                         │
│ ├─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┤                         │
│ │WO-  │Model│SN002│v2.1.│2024-│v3.0.│v1.2.│已完成│[✓正确]│                       │
│ │2024-│-A100│     │3    │01-15│1    │0    │     │     │                       │
│ │001  │     │     │     │     │     │     │     │     │                       │
│ ├─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┤                         │
│ │WO-  │Model│SN003│v2.1.│2024-│v3.0.│v1.1.│已完成│[✗错误]│                       │
│ │2024-│-A100│     │2    │01-10│0    │9    │     │     │                       │
│ │002  │     │     │     │     │     │     │     │     │                       │
│ ├─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┤                         │
│ │     │     │     │     │     │     │     │     │     │                       │
│ │     │     │     │     │     │     │     │     │     │                       │
│ └─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┘                         │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
\`\`\`

## 10. 响应式设计指南

### 10.1 大屏幕布局 (> 1280px)
- 三栏并排显示主要功能区域
- 表格显示完整列信息
- 间距宽松，视觉舒适

### 10.2 中屏幕布局 (768px - 1280px)
- 两栏布局，操作面板可移至底部
- 表格可能需要水平滚动查看全部列
- 适当减小间距和字体大小

### 10.3 小屏幕布局 (< 768px)
- 单栏垂直堆叠所有元素
- 表格采用响应式设计，隐藏次要列
- 紧凑布局，减小间距和组件大小
\`\`\`
