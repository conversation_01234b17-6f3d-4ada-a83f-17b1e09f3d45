# 项目开发规则总览

本文档旨在为项目的开发提供统一的规范和指导，确保代码质量、一致性和可维护性。

## 一、项目概览

此项目是一个基于 Flask 的 Web 应用程序，主要用于企业内部的生产管理、工单处理、质量控制等相关流程。

### 1.1 关键目录与文件

*   **主应用程序**:
    *   `[app.py](mdc:app.py)`: Flask 应用主文件，负责初始化、蓝图注册、JWT认证及核心路由。
*   **配置与依赖**:
    *   `[requirements.txt](mdc:requirements.txt)`: Python 包依赖列表 (如 Flask, SQLAlchemy, Gunicorn)。
*   **后端逻辑**:
    *   `[routes/](mdc:routes/)`: 包含 Flask 蓝图，按模块组织API端点 (例如 `[routes/work_order.py](mdc:routes/work_order.py)`, `[routes/product_management.py](mdc:routes/product_management.py)`)。
    *   `[models/](mdc:models/)`: 定义 SQLAlchemy ORM 数据模型 (例如 `[models/user.py](mdc:models/user.py)`, `[models/product.py](mdc:models/product.py)`)。  
    *   `[database/](mdc:database/)`:
        *   `[database/db_manager.py](mdc:database/db_manager.py)`: 管理数据库连接和会话。
        *   `[database/migrations/](mdc:database/migrations/)`: 可能包含数据库结构迁移脚本。
    *   `[services/](mdc:services/)`: 包含业务逻辑服务 (例如 `[services/assembly_service.py](mdc:services/assembly_service.py)`)。
    *   `[utils/](mdc:utils/)`: 存放工具函数和类 (例如 `[utils/product_mapping.py](mdc:utils/product_mapping.py)`)。
*   **前端**:
    *   `[templates/](mdc:templates/)`: 存放 HTML 模板。
        *   `[templates/index.html](mdc:templates/index.html)`: 应用主页面。
        *   `[templates/login.html](mdc:templates/login.html)`: 登录页面。
    *   `[static/](mdc:static/)`: 存放静态资源 (CSS, JavaScript, 图片)。
        *   `[static/style.css](mdc:static/style.css)` 和 `[static/script.js](mdc:static/script.js)`: 主要样式表和脚本。
        *   `[static/page_js_css/](mdc:static/page_js_css/)`: 针对特定页面的 CSS/JS 文件。
*   **运维与部署**:
    *   `[Operations/](mdc:Operations/)`: 包含运维脚本 (例如 `[Operations/db_backup.sh](mdc:Operations/db_backup.sh)`)。
    *   `[environment/](mdc:environment/)`: 包含环境设置和部署脚本 (例如 `[environment/deploy.sh](mdc:environment/deploy.sh)`)。
*   **文档**:
    *   `[各模块readme文档/](mdc:各模块readme文档/)`: 各模块的说明文档。
*   **日志**:
    *   `[app.log](mdc:app.log)`: 应用日志文件。

### 1.2 技术栈

*   **后端**:
    *   Python
    *   Flask (Web框架)
    *   SQLAlchemy (ORM)
    *   MySQL (数据库)
    *   Gunicorn (WSGI 服务器)
    *   Gevent (并发处理)
    *   PyJWT (认证)
    *   Whitenoise (静态文件服务)
*   **前端**:
    *   HTML, CSS, JavaScript
    *   (可能包含位于 `[static/lib/](mdc:static/lib/)` 的其他JS库)
*   **运维/部署**:
    *   Shell 脚本

## 二、通用编码标准

*   **语言一致性**:
    *   代码中的注释、日志信息以及前端面向用户的文本（如按钮文字、提示信息）应优先使用中文，以保持一致性。
    *   适用文件: `**/*.py`, `[static/page_js_css/**/*.js](mdc:static/page_js_css/**.js)`, `[static/page_js_css/**/*.css](mdc:static/page_js_css/**.css)`
*   **代码格式化**:
    *   建议使用统一的代码格式化工具（如前端Prettier，后端Black或Autopep8）确保代码风格统一。
*   **版本控制**:
    *   遵循良好的 Git 提交规范，编写清晰、有意义的提交信息。
*   **代码审查**:
    *   对于新模块或重要功能变更，应进行代码审查。
*   **文档**:
    *   为新模块或重要功能点编写必要的开发文档或 README，说明其设计、用法和注意事项。

## 三、后端开发规则

*   **SQLAlchemy ORM 使用**:
    *   充分利用 SQLAlchemy ORM 的优势，定义清晰的数据模型，并通过 ORM 进行数据库交互，以提高代码的可读性、可维护性，并减少 SQL 注入风险。
    *   应尽量避免编写原生 SQL 语句。仅在特定性能优化场景或复杂查询无法通过 ORM 有效表达时考虑使用，此时原生 SQL 应经过严格审查和测试。
    *   适用文件: `models/**/*.py`, `services/**/*.py`, `routes/**/*.py` (以及其他涉及数据库操作的Python文件)

*   **代码规范 (PEP 8)**:
    *   所有 Python 代码编写应严格遵循 PEP 8 编码规范。
    *   推荐使用自动化工具（如 Flake8, Pylint）进行检查，以确保代码风格的一致性和可读性。
    *   适用文件: `**/*.py`

*   **避免代码冗余 (DRY 原则)**:
    *   遵循"不要重复自己"(DRY - Don't Repeat Yourself) 原则。
    *   通过函数、类、模块等方式封装通用逻辑，避免代码冗余和膨胀，提高代码复用性和可维护性。
    *   适用文件: `**/*.py`

*   **Excel 导出**:
    *   后端使用 Pandas 和 `xlsxwriter` 库进行 Excel 文件导出。
    *   在修改导出逻辑时，请确保维持良好的数据准确性和格式化（如列宽、表头样式、数据类型等）。
    *   相关代码主要集中在处理数据导出功能的 Python 文件中。

## 四、前端开发规则

### 4.1 JavaScript (JS) 规范

#### 4.1.1 命名规范

*   **变量与常量**:
    *   普通变量和函数名使用驼峰式命名 (camelCase): `currentUser`, `calculateTotalAmount`。
    *   常量（不会被重新赋值的值）使用全大写蛇形命名 (UPPER_SNAKE_CASE): `MAX_USERS`, `API_BASE_URL`。
*   **函数名**:
    *   应具有描述性，清晰表达函数的功能。事件处理函数建议使用 `handle` 前缀 (例如 `handleSubmitForm`)。返回布尔值的函数建议使用 `is`, `has`, `should` 等前缀。
*   **类名 (若使用)**:
    *   使用帕斯卡命名 (PascalCase): `UserProfile`, `DataService`。

#### 4.1.2 全局污染防治

*   **模块化封装**: 优先使用 ES6 模块 (`export`/`import`)。若项目构建系统或目标浏览器环境不支持，可使用立即执行函数表达式 (IIFE) 创建私有作用域。
*   **谨慎使用全局变量**:
    *   若必须使用全局变量，应将所有全局相关的状态和功能组织在一个单一的、项目特定的全局命名空间对象下 (例如 `window.MyProjectApp.config`)，而不是直接在 `window` 对象上挂载多个独立的变量。
    *   当前项目中，部分前端页面状态通过直接挂载到 `window` 对象的全局变量 (如 `window.orderManagementState`) 进行管理。在添加新状态时，虽可暂时遵循此模式，但长远建议逐步向单一全局命名空间对象的方式过渡，以增强代码的组织性和减少潜在冲突。
    *   始终使用 `var`, `let`, 或 `const` 显式声明变量，避免产生隐式全局变量。
    *   适用文件: `[static/page_js_css/**/*.js](mdc:static/page_js_css/**.js)`

#### 4.1.3 DOM 操作

*   **缓存 DOM 查询**: 对于会被多次引用的 DOM 元素，应将其查询结果缓存到变量中，避免重复查询以提高性能。
*   **ID 与 Class 的选择**: ID 用于唯一标识页面上的元素，主要供 JavaScript 获取特定元素进行操作。Class 用于标识一组具有相同样式或行为的元素。
*   **事件委托**: 对于大量动态添加或具有相似事件处理逻辑的元素列表，应使用事件委托以提高性能并简化代码。
*   **减少 `innerHTML` 的滥用**: 虽然 `innerHTML` 在构建初始DOM结构时方便快捷，但应避免频繁用其更新小部分内容，这可能导致不必要的性能开销。对于局部更新，优先考虑使用 `textContent` (更新文本内容)、`setAttribute` (修改属性)、`classList` (操作类名) 或通过标准的 DOM API 创建和插入新元素。

#### 4.1.4 常量与配置

*   **集中管理**: 将模块相关的常量、配置（如 API 端点、默认设置、状态码等）集中定义在模块的顶部或专门的配置文件/对象中，便于管理和修改。

#### 4.1.5 注释与文档

*   **函数注释**: 为重要的函数或复杂逻辑添加 JSDoc 风格的注释，清晰说明函数的作用、参数类型、参数含义和返回值。
*   **代码块注释**: 对复杂的代码段、关键算法或非显而易见的逻辑决策点添加必要的注释以解释其工作原理。

#### 4.1.6 异步操作

*   **Promises 或 Async/Await**: 对于异步操作（如 API 请求），优先使用 Promises 或更现代的 `async/await` 语法，以避免回调地狱，使异步代码流程更清晰、易读和易于维护。

### 4.2 CSS 规范

#### 4.2.1 命名规范 (BEM)

*   严格遵循 BEM (Block, Element, Modifier) 命名约定。
    *   Block (块): 独立的、可复用的组件，例如 `.product-card`, `.user-profile`。
    *   Element (元素): Block 的一部分，不能脱离 Block 单独使用，使用双下划线 `__` 连接，例如 `.product-card__image`, `.product-card__title`。
    *   Modifier (修饰符): 用于表示 Block 或 Element 的不同状态或版本，使用双中划线 `--` 连接，例如 `.product-card--featured`, `.button--disabled`。
*   **模块前缀**: 可为新模块的 BEM 块名添加独特且简短的前缀，以进一步区分不同模块的样式，例如：新模块为 "UserSettings"，则块可以是 `.us-settings` 或 `.user-settings`。
*   项目中 CSS 样式已采用 BEM 风格的命名约定 (例如 `shipment-query__form-group`)，并利用 CSS 变量进行主题化。后续开发应遵循此规范。
*   适用文件: `[static/page_js_css/**/*.css](mdc:static/page_js_css/**.css)`

#### 4.2.2 全局污染防治

*   BEM 的结构化命名本身即为一种有效的避免样式冲突和全局污染的手段。
*   **避免宽泛的选择器**: 尽量避免直接使用标签选择器（`div`, `span`, `p` 等）定义全局样式。若必须使用，应将其限定在特定的父类或组件上下文中。
*   **限制ID选择器**: 避免使用 ID 选择器 (`#myElement`) 来定义主要样式，ID 主要用于 JavaScript 操作。ID 选择器具有高特异性，容易覆盖其他样式且难以复用。
*   **保持选择器扁平化**: 避免不必要的深层嵌套后代选择器，以降低选择器的复杂度和特异性。

#### 4.2.3 CSS 变量 (Custom Properties)

*   **模块化变量**: 类似于现有 CSS 中可能的模块特定前缀 (如 `--si-primary-color`)，为新模块的 CSS 变量也建议定义模块特定的前缀，例如 `--user-settings-primary-color`。
*   **全局与局部变量**: 可定义一些全局的、应用层面的基础颜色、字体、间距等CSS变量。模块内部可以继承或覆盖这些全局变量，也可以定义模块特有的局部变量。

#### 4.2.4 文件结构

*   **模块化 CSS**: 每个主要模块或大型组件应拥有其独立的 CSS 文件（例如 `user-settings.css`, `product-list.css`）。
*   **通用样式**: 将真正全局通用的样式（如CSS Reset, 基本排版样式, 辅助工具类等）组织在一个或几个核心的 CSS 文件中。

#### 4.2.5 可维护性

*   **避免 `!important`**: 尽量避免使用 `!important` 来覆盖样式，它通常是 CSS 特异性管理问题的标志，并会使调试和后续维护更加困难。应优先通过调整选择器特异性或源码顺序来解决样式冲突。
*   **样式分组**: 在编写 CSS 规则时，相关的样式属性（如布局相关、盒模型相关、字体相关、背景相关等）可以进行逻辑上的分组，并按一定顺序排列，以提高代码的可读性。
*   **注释**: 对复杂的选择器组合、使用了特殊技巧或非显而易见的样式规则添加注释，解释其目的或工作方式。