// 使用 window 对象来存储全局状态，避免重复声明
if (typeof window.ioModuleState === 'undefined') {
    window.ioModuleState = {
        isActive: false,
        resizeHandler: null,
        requiresPcbaCheck: true  // 默认需要检查
    };
}

// 添加版本信息表单创建函数
function createVersionInfoForm() {
    return `
        <div class="card version-info-card">
            <div class="card-header">
                <h2 class="card-title">版本信息</h2>
            </div>
            <div class="card-content">
                <div class="version-info-container">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="auto-version">自动获取版本</label>
                            <input type="text" id="auto-version" name="auto-version" placeholder="根据SN自动获取软件版本" readonly class="readonly-field">
                        </div>
                        <div class="form-group">
                            <label for="auto-date">自动获取日期</label>
                            <input type="text" id="auto-date" name="auto-date" placeholder="根据SN自动获取构建日期" readonly class="readonly-field">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="io-version">IO软件版本</label>
                            <input type="text" id="io-version" name="io-version" required>
                        </div>
                        <div class="form-group">
                            <label for="io-build-date">IO构建日期</label>
                            <input type="text" id="io-build-date" name="io-build-date" required>
                        </div>
                    </div>
                    <div class="version-hint">
                        手动查询IO软件版本和日期并填入、自动获取的和手动查询的一致才可提交
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 在文件开头添加防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

async function initIOModulePage() {
    window.ioModuleState.isActive = true;
    const ioModuleContent = document.getElementById('io-module-content');
    if (!ioModuleContent) {
        console.error('无法找到 io-module-content 元素');
        return;
    }
    
    ioModuleContent.innerHTML = `
        <div class="container">
               <form id="testForm">
                ${createBasicInfoForm()}

                <div class="separator"></div>

                <div class="card-grid">
                    ${createVersionInfoForm()}

                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">测试项目</h2>
                        </div>
                        <div class="card-content">
                            <table>
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="checkbox-container">
                                                <input type="checkbox" id="selectAll">
                                                <label for="selectAll">全选</label>
                                            </div>
                                        </th>
                                        <th>序号</th>
                                        <th>测试项目</th>
                                        <th>测试结果</th>
                                    </tr>
                                </thead>
                                <tbody id="testItemsBody">
                                    <!-- Test items will be dynamically inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <button type="submit" class="submit-button">提交测试信息</button>
            </form>
        </div>
    `;

    // Call setupFormValidation after form is created
    setupFormValidation();

    const testItems = [
        { name: "Backplane Bus通信", code: "backplane_bus" },
        { name: "Body I/O输入输出", code: "body_io" },
        { name: "Led灯珠", code: "led_bulb" }
    ];

    const testItemsBody = document.getElementById('testItemsBody');
    const selectAllCheckbox = document.getElementById('selectAll');

    // Populate test items
    testItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div class="checkbox-container">
                    <input type="checkbox" id="test-item-${index}" class="test-item-checkbox" data-code="${item.code}">
                </div>
            </td>
            <td>${index + 1}</td>
            <td>${item.name}</td>
            <td>
                <select id="test-result-${index}" data-code="${item.code}">
                    <option value="通过" selected>通过</option>
                    <option value="不通过">不通过</option>
                </select>
            </td>
        `;
        testItemsBody.appendChild(row);
    });

    // Handle select all checkbox
    selectAllCheckbox.addEventListener('change', (e) => {
        const checkboxes = document.querySelectorAll('.test-item-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = e.target.checked);
    });

    // Handle individual checkbox changes
    testItemsBody.addEventListener('change', (e) => {
        if (e.target.classList.contains('test-item-checkbox')) {
            const allChecked = [...document.querySelectorAll('.test-item-checkbox')]
                .every(checkbox => checkbox.checked);
            selectAllCheckbox.checked = allChecked;
        }
        
        if (e.target.tagName === 'SELECT') {
            const row = e.target.closest('tr');
            const checkboxContainer = row.querySelector('.checkbox-container');
            
            if (e.target.value === '不通过') {
                checkboxContainer.classList.add('test-failed');
            } else {
                checkboxContainer.classList.remove('test-failed');
            }
        }
    });

    // 添加表单输入框的回车键处理
    const form = document.getElementById('testForm');
    form.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault(); // 阻止默认的回车提交行为
            return false;
        }
    });

    // 添加版本信息输入框值变化检测
    const autoVersionInput = document.getElementById('auto-version');
    const autoDateInput = document.getElementById('auto-date');
    const ioVersionInput = document.getElementById('io-version');
    const ioBuildDateInput = document.getElementById('io-build-date');

    // 自动获取版本输入框值变化检测
    autoVersionInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.add('input-has-value');
        } else {
            this.classList.remove('input-has-value');
        }
    });

    // 自动获取日期输入框值变化检测
    autoDateInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.add('input-has-value');
        } else {
            this.classList.remove('input-has-value');
        }
    });

    // IO软件版本输入框值变化检测
    ioVersionInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.add('input-has-value');
        } else {
            this.classList.remove('input-has-value');
        }
    });

    // IO构建日期输入框值变化检测
    ioBuildDateInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.add('input-has-value');
        } else {
            this.classList.remove('input-has-value');
        }
    });

    // 修改表单提交处理，只响应按钮点击
    document.getElementById('testForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // 检查是否是通过按钮点击触发的提交
        const submitButton = document.querySelector('.submit-button');
        if (e.submitter !== submitButton) {
            return;
        }

        // 验证必填字段
        const requiredFields = {
            'tester': '测试人员',
            'orderNumber': '加工单号',
            'productionQuantity': '生产数量',
            'productCode': '产品编码',
            'productModel': '产品型号',
            'productStatus': '产品状态',
            'productSN': '产品SN号',
            'snByteCount': '产品SN号字节数',
            'io-version': 'IO软件版本',
            'io-build-date': 'IO构建日期'
        };

        // 检查所有必填字段
        for (const [fieldId, fieldName] of Object.entries(requiredFields)) {
            const field = document.getElementById(fieldId);
            const value = field.value.trim();
            
            if (!value) {
                await SweetAlert.warning(`${fieldName}不能为空！`);
                field.focus();
                return;
            }
        }

        // 验证版本信息一致性
        const autoVersion = document.getElementById('auto-version').value.trim();
        const autoDate = document.getElementById('auto-date').value.trim();
        const ioVersion = document.getElementById('io-version').value.trim();
        const ioDate = document.getElementById('io-build-date').value.trim();

        // 如果自动获取的版本信息存在，则需要校验一致性
        if (autoVersion || autoDate) {
            if (autoVersion && ioVersion && autoVersion !== ioVersion) {
                await SweetAlert.error('软件版本错误：自动获取版本与IO软件版本不一致！');
                document.getElementById('io-version').focus();
                return;
            }
            
            if (autoDate && ioDate && autoDate !== ioDate) {
                await SweetAlert.error('软件版本错误：自动获取日期与IO构建日期不一致！');
                document.getElementById('io-build-date').focus();
                return;
            }
        }

        // 验证产品 SN 号
        const productSN = document.getElementById('productSN').value.trim();
        const snByteCount = parseInt(document.getElementById('snByteCount').value);
        
        if (isNaN(snByteCount)) {
            await SweetAlert.error('请输入有效的 SN 号字节数');
            return;
        }

        if (productSN.length !== snByteCount) {
            await SweetAlert.error(`产品 SN 号的长度必须为 ${snByteCount} 个字节，当前长度为 ${productSN.length} 个字节`);
            return;
        }

        // 先获取并打印产品状态值，用于调试
        const productStatus = document.getElementById('productStatus').value;
        console.log('产品状态值:', productStatus);
        
        // 收集表单数据
        const formData = {
            // 基本信息
            tester: document.getElementById('tester').value,
            test_time: document.getElementById('testTime').value,
            work_order: document.getElementById('orderNumber').value.trim(),
            work_qty: document.getElementById('productionQuantity').value.trim(),
            pro_model: document.getElementById('productModel').value.trim(),
            pro_code: document.getElementById('productCode').value.trim(),
            pro_sn: document.getElementById('productSN').value.trim(),
            pro_batch: document.getElementById('batchNumber').value || 'N/A',
            remarks: document.getElementById('remarks').value || 'N/A',
            
            // 版本信息
            io_version: document.getElementById('io-version').value || 'N/A',
            io_build_date: document.getElementById('io-build-date').value || 'N/A',
            
            // 测试结果 - 使用英文代码
            backplane_result: document.getElementById('test-result-0').value,
            body_io_result: document.getElementById('test-result-1').value,
            led_bulb_result: document.getElementById('test-result-2').value
        };

        // 转换测试结果为数字格式并检查是否全部通过
        const testResults = {
            backplane: formData.backplane_result === '通过' ? 1 : 2,
            body_io: formData.body_io_result === '通过' ? 1 : 2,
            led_bulb: formData.led_bulb_result === '通过' ? 1 : 2
        };

        // 添加测试结果到表单数据
        Object.assign(formData, testResults);

        // 修改产品状态映射
        const productStatusMap = {
            'new': 1,      // 新品
            'used': 2,     // 维修
            'refurbished': 3  // 返工
        };
        
        // 设置产品状态
        formData.pro_status = productStatusMap[productStatus];
        console.log('转换后的产品状态值:', formData.pro_status);
        
        if (!formData.pro_status) {
            await SweetAlert.warning('请选择产品状态！');
            return;
        }

        // 设置维修和返工次数
        formData.maintenance = formData.pro_status === 2 ? 1 : 0;
        formData.rework = formData.pro_status === 3 ? 1 : 0;

        // 在提交前打印数据，用于调试
        console.log('提交的数据：', formData);
        
        try {
            const response = await fetch('/api/io-module/submit-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                await SweetAlert.success('测试信息提交成功！');
                
                // 保存需要保留的字段值
                const savedValues = {
                    tester: document.getElementById('tester').value,
                    orderNumber: document.getElementById('orderNumber').value,
                    productionQuantity: document.getElementById('productionQuantity').value,
                    productCode: document.getElementById('productCode').value,
                    productModel: document.getElementById('productModel').value,
                    productStatus: document.getElementById('productStatus').value,
                    batchNumber: document.getElementById('batchNumber').value,
                    snByteCount: document.getElementById('snByteCount').value,
                    remarks: document.getElementById('remarks').value,
                    ioVersion: document.getElementById('io-version').value,
                    ioBuildDate: document.getElementById('io-build-date').value
                };
                
                // 重置表单
                document.getElementById('testForm').reset();
                
                // 移除所有测试失败的背景色
                document.querySelectorAll('.checkbox-container.test-failed').forEach(container => {
                    container.classList.remove('test-failed');
                });
                
                // 恢复保存的值
                document.getElementById('tester').value = savedValues.tester;
                document.getElementById('orderNumber').value = savedValues.orderNumber;
                document.getElementById('productionQuantity').value = savedValues.productionQuantity;
                document.getElementById('productCode').value = savedValues.productCode;
                document.getElementById('productModel').value = savedValues.productModel;
                document.getElementById('productStatus').value = savedValues.productStatus;
                document.getElementById('batchNumber').value = savedValues.batchNumber;
                document.getElementById('snByteCount').value = savedValues.snByteCount;
                document.getElementById('remarks').value = savedValues.remarks;
                document.getElementById('io-version').value = savedValues.ioVersion;
                document.getElementById('io-build-date').value = savedValues.ioBuildDate;
                
                // 恢复基本信息输入框的绿色背景
                const basicInfoInputs = ['tester', 'orderNumber', 'productionQuantity', 'productCode', 'productModel', 
                                       'productStatus', 'batchNumber', 'snByteCount', 'remarks'];
                basicInfoInputs.forEach(id => {
                    const element = document.getElementById(id);
                    if (element && element.value.trim()) {
                        element.classList.add('input-has-value');
                    }
                });
                
                // 恢复版本信息输入框的绿色背景
                if (savedValues.ioVersion) {
                    document.getElementById('io-version').classList.add('input-has-value');
                }
                if (savedValues.ioBuildDate) {
                    document.getElementById('io-build-date').classList.add('input-has-value');
                }
                
                // 专门处理产品SN号输入框
                const productSNInput = document.getElementById('productSN');
                if (productSNInput) {
                    productSNInput.value = ''; // 确保值被清空
                    productSNInput.classList.remove('input-has-value'); // 移除绿色背景
                    
                    // 使用 setTimeout 确保在UI更新后设置焦点
                    setTimeout(() => {
                        productSNInput.focus(); // 聚焦到产品SN号输入框
                    }, 0);
                }
                
                // 暂时禁用提交按钮，直到用户输入新的SN
                const submitButton = document.querySelector('.submit-button');
                if (submitButton) {
                    submitButton.disabled = true;
                }

                // 添加一次性事件监听器，当用户输入时重新启用按钮
                productSNInput.addEventListener('input', function onFirstInput() {
                    if (submitButton) {
                        submitButton.disabled = false;
                    }
                    // 移除监听器，避免重复执行
                    productSNInput.removeEventListener('input', onFirstInput);
                });
                
            } else {
                await SweetAlert.error(result.message);
            }
        } catch (error) {
            await SweetAlert.error('请检查网络连接');
            console.error('提交错误：', error);
        }
    });

    // 修改响应式处理
    function handleResponsiveness() {
        if (!window.ioModuleState.isActive) return;
        
        const table = document.querySelector('table');
        if (!table) return;
        
        if (window.innerWidth <= 768) {
            table.classList.add('responsive-table');
        } else {
            table.classList.remove('responsive-table');
        }
    }

    // 如果已经存在之前的事件监听器，先移除它
    if (window.ioModuleState.resizeHandler) {
        window.removeEventListener('resize', window.ioModuleState.resizeHandler);
    }

    // 保存新的 resize handler 的引用
    window.ioModuleState.resizeHandler = handleResponsiveness;
    window.addEventListener('resize', window.ioModuleState.resizeHandler);
    
    // 初始调用一次
    handleResponsiveness();

    // 在页面初始化完成后获取并填充当前用户
    try {
        const response = await fetch('/api/io-module/get-current-user');
        const data = await response.json();
        
        if (data.success && data.username) {
            const testerInput = document.getElementById('tester');
            if (testerInput) {
                testerInput.value = data.username;
            }
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
    }

    // 将查询逻辑抽取为独立函数
    async function queryOrderInfo(orderNumber) {
        if (!orderNumber) {
            // 清空相关字段
            document.getElementById('productionQuantity').value = '';
            document.getElementById('productCode').value = '';
            document.getElementById('productModel').value = '';
            document.getElementById('batchNumber').value = '';    
            document.getElementById('snByteCount').value = '';    
            
            // 重置PCBA检查标志为默认值
            window.ioModuleState.requiresPcbaCheck = true;
            return;
        }

        try {
            const response = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNumber)}`);
            const data = await response.json();
            
            if (data.success && data.order) {
                // 新增判断：只有测试前阶段完成才允许后续操作
                if (!data.order.test_stage_completed) {
                    // 清空相关字段
                    document.getElementById('productionQuantity').value = '';
                    document.getElementById('productCode').value = '';
                    document.getElementById('productModel').value = '';
                    document.getElementById('batchNumber').value = '';
                    document.getElementById('snByteCount').value = '';
                    // 移除输入框的绿色背景
                    ['productionQuantity', 'productCode', 'productModel', 'batchNumber', 'snByteCount'].forEach(id => {
                        const element = document.getElementById(id);
                        element.classList.remove('input-has-value');
                    });
                    await Swal.fire({
                        title: '提示',
                        text: '该工单未完成测试前阶段外观检验',
                        icon: 'warning',
                        confirmButtonText: '确定'
                    });
                    // 重置PCBA检查标志为默认值
                    window.ioModuleState.requiresPcbaCheck = true;
                    return;
                }
                // 自动填充字段
                document.getElementById('productionQuantity').value = data.order.ord_productionQuantity;
                document.getElementById('productCode').value = data.order.ord_productCode;
                document.getElementById('productModel').value = data.order.ord_productModel;
                document.getElementById('batchNumber').value = data.order.ord_probatch;     
                document.getElementById('snByteCount').value = data.order.ord_snlenth;      
                
                // 添加输入框的绿色背景
                ['productionQuantity', 'productCode', 'productModel', 'batchNumber', 'snByteCount'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element && element.value.trim()) {
                        element.classList.add('input-has-value');
                    }
                });

                // 设置PCBA检查标志
                window.ioModuleState.requiresPcbaCheck = data.order.ord_requires_pcba_check !== false;
                
                // 可选：显示是否需要PCBA检查的提示
                const pcbaCheckStatus = window.ioModuleState.requiresPcbaCheck ? 
                    '需要PCBA绑定' : '无需PCBA绑定';
                console.log(`工单 ${orderNumber}: ${pcbaCheckStatus}`);
            } else {
                // 清空相关字段
                document.getElementById('productionQuantity').value = '';
                document.getElementById('productCode').value = '';
                document.getElementById('productModel').value = '';
                document.getElementById('batchNumber').value = '';    
                document.getElementById('snByteCount').value = '';    
                
                // 移除输入框的绿色背景
                ['productionQuantity', 'productCode', 'productModel', 'batchNumber', 'snByteCount'].forEach(id => {
                    const element = document.getElementById(id);
                    element.classList.remove('input-has-value');
                });
                
                // 显示提示信息
                if (data.message) {
                    await Swal.fire({
                        title: '提示',
                        text: data.message,
                        icon: 'warning',
                        confirmButtonText: '确定'
                    });
                }

                // 重置PCBA检查标志为默认值
                window.ioModuleState.requiresPcbaCheck = true;
            }
        } catch (error) {
            console.error('Error fetching order details:', error);
            await Swal.fire({
                title: '错误',
                text: '获取工单信息失败',
                icon: 'error',
                confirmButtonText: '确定'
            });

            // 重置PCBA检查标志为默认值
            window.ioModuleState.requiresPcbaCheck = true;
        }
    }

    // 添加工单号输入框的事件监听
    const orderNumberInput = document.getElementById('orderNumber');
    if (orderNumberInput) {
        // 使用防抖包装查询函数，设置300ms延迟
        const debouncedQuery = debounce(queryOrderInfo, 500);
        
        orderNumberInput.addEventListener('input', function() {
            const orderNumber = this.value.trim();
            debouncedQuery(orderNumber);
        });
    }

    // 添加自动获取版本信息的函数
    async function autoFetchVersionInfo(productSN) {
        // 获取当前产品状态
        const productStatus = document.getElementById('productStatus').value;
        
        // 支持新品、返工、维修三种状态
        if (!['new', 'used', 'refurbished'].includes(productStatus)) {
            console.log('产品状态不支持自动获取版本信息，跳过');
            return;
        }

        try {
            // 调用后端API获取版本信息，传递产品状态参数
            const response = await fetch(`/api/io-module/get-version-info?product_sn=${encodeURIComponent(productSN)}&product_status=${encodeURIComponent(productStatus)}`);
            const data = await response.json();
            
            if (data.success && data.data) {
                // 自动填充版本信息
                const autoVersionInput = document.getElementById('auto-version');
                const autoDateInput = document.getElementById('auto-date');
                
                if (autoVersionInput && data.data.software_version) {
                    autoVersionInput.value = data.data.software_version;
                    autoVersionInput.classList.add('input-has-value');
                }
                
                if (autoDateInput && data.data.build_time) {
                    autoDateInput.value = data.data.build_time;
                    autoDateInput.classList.add('input-has-value');
                }
                
                // 显示成功提示（可选）
                if (data.data.software_version || data.data.build_time) {
                    console.log(`版本信息自动获取成功 (${productStatus}模式):`, data.message);
                } else {
                    console.log(`版本信息为空 (${productStatus}模式):`, data.message);
                }
            } else {
                console.log('未找到相关版本信息或查询失败:', data.message);
            }
        } catch (error) {
            console.error('自动获取版本信息失败:', error);
        }
    }

    // 新增：添加一个辅助函数，用于验证SN的订货号是否正确
    async function validateSnOrderNumber(sn, productCode) {
        if (!productCode) {
            await SweetAlert.error('产品编码缺失，无法验证SN');
            return false;
        }
        try {
            const response = await fetch(`/api/products/validate-sn-order?sn=${encodeURIComponent(sn)}&productCode=${encodeURIComponent(productCode)}`);
            const result = await response.json();
            
            if (!result.success) {
                await SweetAlert.error(result.message || 'SN与工单订货号不匹配！');
                return false;
            }
            return true;
        } catch (error) {
            console.error('验证 SN 订货号失败:', error);
            await SweetAlert.error('验证SN失败，请稍后重试');
            return false;
        }
    }

    // 添加产品SN号输入框的失焦检查功能
    const productSNInput = document.getElementById('productSN');
    if (productSNInput) {
        productSNInput.addEventListener('blur', async function() {
            // 获取并清理输入值
            const sn = this.value.trim();
            if (!sn) return;  // 如果输入为空，直接返回
            
            // 新增步骤1：验证SN的订货号是否正确
            const productCode = document.getElementById('productCode').value.trim();
            const isOrderNumberValid = await validateSnOrderNumber(sn, productCode);
            if (!isOrderNumberValid) {
                this.value = ''; // 清空输入框
                this.focus();    // 重新获取焦点
                return; // 如果订货号不匹配，则中断后续所有操作
            }

            // 如果当前工单不需要PCBA检查，则跳过检查
            if (!window.ioModuleState.requiresPcbaCheck) {
                console.log('当前工单无需PCBA绑定检查，允许继续测试');
                // 继续执行版本信息自动获取逻辑
                await autoFetchVersionInfo(sn);
                return;
            }

            try {
                // 发送请求检查SN号是否已绑定PCBA
                const response = await fetch(`/api/io-module/check-sn?sn=${encodeURIComponent(sn)}`);
                const data = await response.json();
                
                if (data.success) {
                    if (!data.exists) {
                        // 如果SN号未绑定PCBA，显示警告并清空输入
                        await SweetAlert.warning('该SN号未绑定PCBA！');
                        this.value = ''; // 清空输入框
                        this.focus();    // 重新获取焦点
                        return;
                    } else {
                        // 如果已绑定PCBA，自动获取版本信息
                        await autoFetchVersionInfo(sn);
                    }
                } else {
                    // 如果检查过程出现错误，在控制台输出错误信息
                    console.error('检查SN号失败:', data.message);
                }
            } catch (error) {
                // 处理网络错误等异常情况
                console.error('检查SN号时发生错误:', error);
                await SweetAlert.error('检查SN号失败，请检查网络连接');
            }
        });
    }

    // 添加产品状态变化监听器，当状态改变时自动获取版本信息
    const productStatusSelect = document.getElementById('productStatus');
    if (productStatusSelect) {
        productStatusSelect.addEventListener('change', async function() {
            // 清空自动获取的版本信息（因为状态变化了，之前的信息可能不适用）
            const autoVersionInput = document.getElementById('auto-version');
            const autoDateInput = document.getElementById('auto-date');
            
            if (autoVersionInput) {
                autoVersionInput.value = '';
                autoVersionInput.classList.remove('input-has-value');
            }
            if (autoDateInput) {
                autoDateInput.value = '';
                autoDateInput.classList.remove('input-has-value');
            }
            
            // 当产品状态改变为支持的状态时，检查是否有产品SN号，如果有则自动获取版本信息
            if (['new', 'used', 'refurbished'].includes(this.value)) {
                const productSN = document.getElementById('productSN').value.trim();
                if (productSN) {
                    await autoFetchVersionInfo(productSN);
                }
            }
        });
    }
}

// 添加清理函数
function cleanupIOModule() {
    window.ioModuleState.isActive = false;
    // 移除 resize 事件监听器
    if (window.ioModuleState.resizeHandler) {
        window.removeEventListener('resize', window.ioModuleState.resizeHandler);
        window.ioModuleState.resizeHandler = null;
    }
}

// 导出这些函数
window.initIOModulePage = initIOModulePage;
window.cleanupIOModule = cleanupIOModule;
