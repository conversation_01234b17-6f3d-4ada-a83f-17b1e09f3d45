from sqlalchemy import Column, Integer, String, DateTime
from database.db_manager import Base
from datetime import datetime

class ProductManagement(Base):
    __tablename__ = 'ProductManagement'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    dbBasic_creator = Column(String(20), nullable=False)
    dbBasic_creationTime = Column(DateTime, nullable=True, default=datetime.now)
    dbBasic_productSeries = Column(String(60), nullable=True, default='N/A')
    dbBasic_productType = Column(String(60), nullable=False, default='N/A')
    dbBasic_productName = Column(String(60), nullable=False, default='N/A')
    dbBasic_productCode = Column(String(60), nullable=False, default='N/A')
    dbBasic_orderNumber = Column(String(60), nullable=True, default='N/A')
    dbsnLength = Column(Integer, nullable=True)
    dbBasic_remarks = Column(String(150), nullable=True, default='N/A')

    def to_dict(self):
        return {
            'id': self.id,
            'Basic_creator': self.dbBasic_creator,
            'Basic_creationTime': self.dbBasic_creationTime.strftime('%Y-%m-%d %H:%M:%S') if self.dbBasic_creationTime else None,
            'Basic_productSeries': self.dbBasic_productSeries,
            'Basic_productType': self.dbBasic_productType,
            'Basic_productName': self.dbBasic_productName,
            'Basic_productCode': self.dbBasic_productCode,
            'Basic_orderNumber': self.dbBasic_orderNumber,
            'snLength': self.dbsnLength,
            'Basic_remarks': self.dbBasic_remarks
        }

    @staticmethod
    def from_dict(data):
        creation_time = data.get('Basic_creationTime')
        if creation_time:
            try:
                # 尝试使用斜杠格式解析
                parsed_time = datetime.strptime(creation_time, '%Y/%m/%d %H:%M:%S')
            except ValueError:
                try:
                    # 如果失败，尝试使用连字符格式解析
                    parsed_time = datetime.strptime(creation_time, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    # 如果两种格式都失败，使用当前时间
                    parsed_time = datetime.now()
        else:
            parsed_time = datetime.now()
        
        return {
            'dbBasic_creator': data.get('Basic_creator'),
            'dbBasic_creationTime': parsed_time,
            'dbBasic_productSeries': data.get('Basic_productSeries', 'N/A'),
            'dbBasic_productType': data.get('Basic_productType', 'N/A'),
            'dbBasic_productName': data.get('Basic_productName', 'N/A'),
            'dbBasic_productCode': data.get('Basic_productCode', 'N/A'),
            'dbBasic_orderNumber': data.get('Basic_orderNumber', 'N/A'),
            'dbsnLength': int(data.get('snLength', 0)),
            'dbBasic_remarks': data.get('Basic_remarks', 'N/A')
        }

    @staticmethod
    def get_order_number_by_product_code(session, product_code):
        """根据产品编码获取订货号"""
        product = session.query(ProductManagement).filter_by(
            dbBasic_productCode=product_code
        ).first()
        return product.dbBasic_orderNumber if product else None 