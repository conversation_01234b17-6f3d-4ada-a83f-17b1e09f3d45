/* 售后查询页面样式 */
#after-sales-query-content .after-sales-query {
    width: 100%;
}

/* 添加主题变量 */
#after-sales-query-content {
    /* 主题变量 - 借鉴自ProductTestQuery的设计系统 */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;

    /* 添加字体相关变量 */
    font-family: -apple-system, BlinkMacSystemFont, 'Inter', sans-serif;
    
    /* 添加响应式尺寸变量 */
    --spacing-xs: 0.25rem; /* 4px */
    --spacing-sm: 0.5rem;  /* 8px */
    --spacing-md: 1rem;    /* 16px */
    --spacing-lg: 1.5rem;  /* 24px */
    --spacing-xl: 2rem;    /* 32px */
    
    /* 添加响应式字体大小变量 */
    --font-size-sm: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);    /* 12-14px */
    --font-size-base: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);    /* 14-16px */
    --font-size-lg: clamp(1rem, 0.95rem + 0.25vw, 1.125rem);      /* 16-18px */
}

/* 分页按钮样式 */
#after-sales-query-content .page-button {
    padding: 0.375rem 0.75rem; /* 6px 12px -> 0.375rem 0.75rem */
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--card));  /* 添加默认背景色 */
    color: hsl(var(--foreground));
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 0.125rem; /* 0 2px -> 0 0.125rem */
    min-width: 2rem; /* 32px -> 2rem */
    text-align: center;
}

#after-sales-query-content .page-button:hover:not(:disabled) {
    background-color: hsl(var(--accent));  /* 添加悬停背景色 */
}

#after-sales-query-content .page-button.active {
    background-color: hsl(var(--primary));  /* 当前页码的背景色 */
    color: hsl(var(--primary-foreground));
    border-color: transparent;
}

#after-sales-query-content .page-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#after-sales-query-content .page-input,
#after-sales-query-content .pagination-section .page-input {
    width: 3.125rem !important;  /* 50px -> 3.125rem */
    text-align: center;
    padding: 0.25rem 0.5rem; /* 4px 8px -> 0.25rem 0.5rem */
    border: 1px solid #d9d9d9;
    border-radius: 0.25rem; /* 4px -> 0.25rem */
}

#after-sales-query-content .page-size select.form-select {
    width: auto !important;  /* 让选择框宽度自适应内容 */
    min-width: 3.75rem; /* 60px -> 3.75rem */
    text-align: center;
    padding: 0.25rem 0.5rem; /* 4px 8px -> 0.25rem 0.5rem */
}

/* 分页控件容器样式 */
#after-sales-query-content .pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem; /* 8px -> 0.5rem */
    flex-wrap: wrap;
}

/* 在文件末尾添加总条数样式 */
#after-sales-query-content .total-count {
    margin-left: 1rem; /* 16px -> 1rem */
    color: hsl(var(--muted-foreground));
    font-size: var(--font-size-base);
}

/* 查询区域样式 */
#after-sales-query-content .query-section {
    background-color: hsl(var(--card));
    padding: var(--spacing-lg); /* 24px -> 1.5rem */
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    margin-bottom: var(--spacing-lg); /* 24px -> 1.5rem */
}

#after-sales-query-content .query-form {
    display: flex;
    flex-wrap: nowrap;
    gap: 1rem; /* 16px -> 1rem */
    margin-bottom: 1.75rem; /* 28px -> 1.75rem */
    align-items: flex-end;
}

#after-sales-query-content .form-item {
    flex: 1;
    min-width: 0;
    margin: 0;
}

#after-sales-query-content .form-label {
    display: block;
    margin-bottom: 0.5rem; /* 8px -> 0.5rem */
    font-size: var(--font-size-base);
    color: hsl(var(--foreground));
}

#after-sales-query-content .form-input,
#after-sales-query-content .form-select {
    width: 100%;
    padding: 0.625rem 0.875rem; /* 10px 14px -> 0.625rem 0.875rem */
    background-color: transparent;
    border: 1px solid hsl(var(--input));
    border-radius: var(--radius);
    font-size: var(--font-size-base);
    color: hsl(var(--foreground));
    transition: all 0.2s ease;
    min-width: 7.5rem; /* 120px -> 7.5rem */
}

#after-sales-query-content .form-input:hover,
#after-sales-query-content .form-select:hover {
    border-color: hsl(var(--ring));
}

#after-sales-query-content .form-input:focus,
#after-sales-query-content .form-select:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 0.125rem hsl(var(--background)), 0 0 0 0.25rem hsl(var(--ring)/0.1); /* 2px, 4px -> 0.125rem, 0.25rem */
}

#after-sales-query-content .query-buttons {
    display: flex;
    gap: 0.5rem; /* 8px -> 0.5rem */
    margin-left: 1rem; /* 16px -> 1rem */
    flex-shrink: 0;
}

#after-sales-query-content .btn {
    padding: 0.625rem 1rem; /* 10px 16px -> 0.625rem 1rem */
    border-radius: var(--radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

#after-sales-query-content .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
}

#after-sales-query-content .btn-primary:hover {
    opacity: 0.9;
}

#after-sales-query-content .btn-default {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
}

#after-sales-query-content .btn-default:hover {
    background-color: hsl(var(--accent));
}

#after-sales-query-content .btn-export {
    background-color: #67c23a;
    color: white;
}

#after-sales-query-content .btn-export:hover {
    opacity: 0.9;
}

/* 工具栏样式 */
#after-sales-query-content .toolbar-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem; /* 16px -> 1rem */
}

#after-sales-query-content .toolbar-left {
    display: flex;
    gap: 0.5rem; /* 8px -> 0.5rem */
}

#after-sales-query-content .toolbar-right {
    display: flex;
    gap: 0.5rem; /* 8px -> 0.5rem */
}

/* 表格区域样式 */
#after-sales-query-content .table-section {
    overflow-x: auto !important; /* 保持水平滚动 */
    overflow-y: hidden;          /* 禁用垂直滚动 */
    width: 100%;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    margin-bottom: 1.5rem; /* 24px -> 1.5rem */
    /* 增加滚动条样式，提高可用性 */
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

#after-sales-query-content .table-section::-webkit-scrollbar {
    height: 0.5rem;
    background-color: transparent;
}

#after-sales-query-content .table-section::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 0.25rem;
}

#after-sales-query-content .table-section::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.4);
}

#after-sales-query-content .data-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed; /* 关键：固定表格布局 */
    min-width: 2100px; /* 调整最小宽度，确保列不会被过度压缩 */
}

#after-sales-query-content .table-header th {
    background-color: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
    font-size: var(--font-size-base);
    font-weight: 500;
    padding: 0.75rem 1rem; /* 12px 16px -> 0.75rem 1rem */
    text-align: left;
    border-bottom: 1px solid hsl(var(--border));
    white-space: nowrap; /* 表头不换行 */
    overflow: visible; /* 确保文本不会被截断 */
    position: sticky; /* 在滚动时保持表头可见 */
    top: 0;
    z-index: 1;
}

#after-sales-query-content .table-row td {
    padding: 0.75rem 1rem; /* 12px 16px -> 0.75rem 1rem */
    border-bottom: 1px solid hsl(var(--border));
    font-size: var(--font-size-base);
    color: hsl(var(--foreground));
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#after-sales-query-content .table-row:hover {
    background-color: hsl(var(--accent));
}

/* 列宽设置 (使用 :nth-child) - 优化宽度分配 */
/* 1. 复选框 */
#after-sales-query-content .data-table th:nth-child(1),
#after-sales-query-content .data-table td:nth-child(1) {
    width: 40px;
    min-width: 40px;
    max-width: 40px;
    text-align: center;
}

/* 2. SN号 */
#after-sales-query-content .data-table th:nth-child(2),
#after-sales-query-content .data-table td:nth-child(2) {
    width: 120px;
    min-width: 120px;
}

/* 3. 产品编码 */
#after-sales-query-content .data-table th:nth-child(3),
#after-sales-query-content .data-table td:nth-child(3) {
    width: 80px;
    min-width: 80px;
}

/* 4. 产品型号 */
#after-sales-query-content .data-table th:nth-child(4),
#after-sales-query-content .data-table td:nth-child(4) {
    width: 140px;
    min-width: 120px;
}

/* 5. 软件版本 */
#after-sales-query-content .data-table th:nth-child(5),
#after-sales-query-content .data-table td:nth-child(5) {
    width: 70px;
    min-width: 70px;
}

/* 6. 板A_SN (原PCBA工单号列) */
#after-sales-query-content .data-table th:nth-child(6),
#after-sales-query-content .data-table td:nth-child(6) {
    width: 250px;
    min-width: 100px;
}

/* 7. 是否出库 */
#after-sales-query-content .data-table th:nth-child(7),
#after-sales-query-content .data-table td:nth-child(7) {
    width: 80px;
    min-width: 80px;
    text-align: center;
}

/* 8. 客户名称 */
#after-sales-query-content .data-table th:nth-child(8),
#after-sales-query-content .data-table td:nth-child(8) {
    width: 180px;
    min-width: 150px;
}

/* 9. 出库时间 */
#after-sales-query-content .data-table th:nth-child(9),
#after-sales-query-content .data-table td:nth-child(9) {
    width: 100px;
    min-width: 80px;
}

/* 分页区域样式 */
#after-sales-query-content .pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem; /* 24px -> 1.5rem */
}

#after-sales-query-content .page-size {
    display: flex;
    align-items: center;
    gap: 0.5rem; /* 8px -> 0.5rem */
}

#after-sales-query-content .pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem; /* 8px -> 0.5rem */
}

#after-sales-query-content .page-jump {
    display: flex;
    align-items: center;
    gap: 0.5rem; /* 8px -> 0.5rem */
}

#after-sales-query-content .page-input {
    width: 2.5rem; /* 40px -> 2.5rem */
    text-align: center;
    padding: 0.25rem 0.5rem; /* 4px 8px -> 0.25rem 0.5rem */
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
}

/* 状态标签样式 */
#after-sales-query-content .status-tag {
    display: inline-block;
    padding: 0.125rem 0.5rem; /* 2px 8px -> 0.125rem 0.5rem */
    border-radius: 0.25rem; /* 4px -> 0.25rem */
    font-size: var(--font-size-sm);
}

#after-sales-query-content .status-yes {
    background-color: #67c23a;
    color: white;
}

#after-sales-query-content .status-no {
    background-color: #f56c6c;
    color: white;
}

/* 操作按钮样式 */
#after-sales-query-content .action-buttons {
    display: flex;
    gap: 0.3125rem; /* 5px -> 0.3125rem */
}

#after-sales-query-content .action-btn {
    padding: 0.25rem 0.5rem; /* 4px 8px -> 0.25rem 0.5rem */
    border: none;
    border-radius: 0.25rem; /* 4px -> 0.25rem */
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: background-color 0.3s;
}

#after-sales-query-content .view-btn {
    background-color: #409eff;
    color: white;
}

#after-sales-query-content .view-btn:hover {
    background-color: #66b1ff;
}

/* 列设置模态框样式 */
#after-sales-query-content .column-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

#after-sales-query-content .column-modal__content {
    position: relative;
    top: 5%;
    margin: 0 auto;
    width: 90%;
    max-width: 37.5rem; /* 600px -> 37.5rem */
    background-color: #fff;
    border-radius: 0.5rem; /* 8px -> 0.5rem */
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15); /* 4px 12px -> 0.25rem 0.75rem */
    max-height: 80vh;
    overflow-y: auto;
}

#after-sales-query-content .modal-header {
    padding: 1rem 1.5rem; /* 16px 24px -> 1rem 1.5rem */
    background: #f7f7f7;
    border-radius: 0.5rem 0.5rem 0 0; /* 8px 8px 0 0 -> 0.5rem 0.5rem 0 0 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

#after-sales-query-content .modal-title {
    margin: 0;
    font-size: var(--font-size-lg);
    color: #333;
    font-weight: 500;
}

#after-sales-query-content .modal-close {
    font-size: 1.5rem; /* 24px -> 1.5rem */
    color: #999;
    cursor: pointer;
    padding: 0.25rem; /* 4px -> 0.25rem */
    line-height: 1;
}

#after-sales-query-content .modal-body {
    padding: 1.5rem; /* 24px -> 1.5rem */
    overflow-y: auto;
}

#after-sales-query-content .modal-footer {
    padding: 1rem 1.5rem; /* 16px 24px -> 1rem 1.5rem */
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem; /* 8px -> 0.5rem */
}

#after-sales-query-content .column-list {
    display: flex;
    flex-direction: column;
    gap: 1rem; /* 16px -> 1rem */
}

#after-sales-query-content .column-item {
    display: flex;
    align-items: center;
    padding: 0.25rem 0; /* 4px 0 -> 0.25rem 0 */
}

#after-sales-query-content .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem; /* 8px -> 0.5rem */
    cursor: pointer;
    font-size: var(--font-size-base);
}

#after-sales-query-content .column-divider {
    width: 100%;
    height: 1px;
    background-color: #eee;
    margin: 0.5rem 0; /* 8px 0 -> 0.5rem 0 */
}

/* 表格排序样式 */
#after-sales-query-content .sortable {
    cursor: pointer;
    user-select: none;
}

#after-sales-query-content .sortable i {
    margin-left: 0.5rem; /* 0.5rem */
    color: #9ca3af; /* 默认图标颜色 */
    font-size: 0.875rem; /* 0.875rem */
}

#after-sales-query-content .sortable:hover i {
    color: #4b5563; /* 悬停时图标颜色 */
}

#after-sales-query-content .sortable i.fa-sort-up,
#after-sales-query-content .sortable i.fa-sort-down {
    color: hsl(var(--primary)); /* 激活排序图标颜色 */
}

/* 响应式调整 */
/* 大屏幕布局 (1200px以上) */
@media screen and (min-width: 1201px) {
    #after-sales-query-content .query-form {
        flex-wrap: nowrap;
        gap: 1rem; /* 16px -> 1rem */
    }
}

/* 中等屏幕布局 (992px - 1200px) */
@media screen and (max-width: 1200px) {
    #after-sales-query-content .query-form {
        flex-wrap: nowrap;
        gap: 0.75rem; /* 12px -> 0.75rem */
    }

    #after-sales-query-content .form-label {
        font-size: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem); /* 13px -> 动态范围 */
    }

    #after-sales-query-content .form-input,
    #after-sales-query-content .form-select {
        padding: 0.5rem 0.75rem; /* 8px 12px -> 0.5rem 0.75rem */
        font-size: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem); /* 13px -> 动态范围 */
    }
}

/* 平板布局 (768px - 991px) */
@media screen and (max-width: 991px) {
    #after-sales-query-content .query-form {
        flex-wrap: wrap;
        gap: 0.75rem; /* 12px -> 0.75rem */
    }

    #after-sales-query-content .form-item {
        flex: 1 1 calc(33.333% - 0.75rem); /* 12px -> 0.75rem */
        min-width: 12.5rem; /* 200px -> 12.5rem */
    }

    #after-sales-query-content .query-buttons {
        margin-left: 0;
        padding-top: 0.5rem; /* 8px -> 0.5rem */
        width: 100%;
        justify-content: flex-end;
    }
}

/* 手机布局 (768px以下) */
@media screen and (max-width: 767px) {
    #after-sales-query-content .query-section {
        padding: 1rem; /* 16px -> 1rem */
    }

    #after-sales-query-content .query-form {
        gap: 0.5rem; /* 8px -> 0.5rem */
    }

    #after-sales-query-content .form-item {
        flex: 1 1 100%;
    }

    #after-sales-query-content .query-buttons {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }

    #after-sales-query-content .btn {
        flex: 1;
        margin: 0 0.25rem; /* 0 4px -> 0 0.25rem */
    }

    #after-sales-query-content .pagination-section {
        flex-direction: column;
        gap: 1rem; /* 16px -> 1rem */
    }

    #after-sales-query-content .pagination-controls {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem; /* 8px -> 0.5rem */
    }

    #after-sales-query-content .page-size {
        width: 100%;
        justify-content: center;
    }

    #after-sales-query-content .total-count {
        margin-left: 0.5rem; /* 8px -> 0.5rem */
    }
}

/* 确保表格在小屏幕上可以横向滚动 */
@media screen and (max-width: 1200px) {
    #after-sales-query-content .table-section {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    #after-sales-query-content .data-table {
        min-width: 56.25rem; /* 900px -> 56.25rem */
    }
}

/* 添加打印媒体查询 */
@media print {
    #after-sales-query-content {
        padding: 0;
    }

    #after-sales-query-content .query-section,
    #after-sales-query-content .toolbar-section {
        display: none;
    }

    #after-sales-query-content .table-section {
        overflow: visible;
    }

    #after-sales-query-content .pagination-section {
        display: none;
    }
}
