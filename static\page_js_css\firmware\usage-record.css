/* 使用记录页面CSS - 基于BEM命名规范 */

/* ===== 页面基本样式 ===== */
.usage-record {
    min-height: 100vh;
    background-color: #f5f5f5;
}

.usage-record__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.usage-record__header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.usage-record__header:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.usage-record__title {
    margin: 0;
    color: #303133;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.5;
    position: relative;
    padding-left: 16px;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.usage-record__title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 22px;
    background: linear-gradient(to bottom, #67C23A, #C2E7B0);
    border-radius: 3px;
}

.usage-record__stats {
    display: flex;
    gap: 15px;
}

.usage-record__stats .el-tag {
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.usage-record__stats .el-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
}

/* ===== 搜索栏样式 ===== */
.usage-record__search-bar {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.usage-record__search-input {
    flex: 1;
    min-width: 300px;
}

.usage-record__date-picker {
    min-width: 280px;
}

.usage-record__actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* ===== 表格容器样式 ===== */
.usage-record__table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.usage-record__work-order-link {
    font-weight: 600;
    text-decoration: none;
    color: #409eff;
}

.usage-record__work-order-link:hover {
    text-decoration: underline;
}

/* ===== 使用详情对话框样式 ===== */
.usage-record__detail-dialog .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
}

.usage-record__detail-dialog .el-dialog__header {
    background-color: #f9fafc;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
}

.usage-record__detail-dialog .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
}

.usage-record__detail-dialog .el-dialog__body {
    padding: 0;
}

.usage-record__detail-content {
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

.usage-record__detail-header {
    background-color: #f9fafc;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.usage-record__detail-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.usage-record__tag-primary {
    font-size: 14px !important;
    padding: 8px 12px !important;
    height: auto !important;
    line-height: 1.5 !important;
    font-weight: 600 !important;
}

.usage-record__detail-subtitle {
    color: #606266;
    font-size: 14px;
    margin-left: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.usage-record__detail-body {
    display: flex;
    padding: 20px;
    gap: 20px;
}

.usage-record__detail-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.usage-record__detail-right {
    flex: 1.2;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.usage-record__detail-panel {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #ebeef5;
}

.usage-record__detail-panel:last-child {
    margin-bottom: 0;
}

.usage-record__panel-header {
    padding: 15px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f9fafc;
    color: #303133;
    font-weight: 600;
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.usage-record__panel-content {
    padding: 15px;
}

/* 信息网格布局 */
.usage-record__info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.usage-record__info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.usage-record__info-label {
    color: #909399;
    font-size: 13px;
}

.usage-record__info-value {
    color: #303133;
    font-size: 14px;
}

.usage-record__info-highlight {
    color: #409eff;
    font-weight: 600;
    font-size: 16px;
}

/* 固件卡片样式 */
.usage-record__firmware-card {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f9fafc;
}

.usage-record__firmware-info {
    padding: 15px;
}

.usage-record__firmware-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px dashed #ebeef5;
}

.usage-record__firmware-item:last-child {
    border-bottom: none;
}

.usage-record__firmware-label {
    color: #909399;
    font-size: 13px;
}

.usage-record__firmware-value {
    color: #303133;
    font-size: 14px;
    font-weight: 500;
}

/* 添加固件值高亮样式，与 info-highlight 保持一致 */
.usage-record__firmware-value--highlight {
    color: #409eff;
    font-weight: 600;
    font-size: 16px;
}

.usage-record__download-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-weight: 600;
}

/* 备注区域 */
.usage-record__notes-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
}

.usage-record__notes-title {
    color: #303133;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.usage-record__notes-title::before {
    content: '';
    width: 3px;
    height: 14px;
    background: linear-gradient(to bottom, #6777ef, #5566d4);
    border-radius: 1.5px;
}

.usage-record__notes {
    background-color: #f9fafc;
    border-radius: 6px;
    border: 1px solid #ebeef5;
    padding: 12px;
    color: #606266;
    font-size: 13px;
    line-height: 1.5;
    min-height: 60px;
}

/* 底部按钮区域 */
.usage-record__detail-footer {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #ebeef5;
    background-color: #f9fafc;
}

.usage-record__button {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

/* ===== 录入序列号对话框样式 ===== */
.usage-record__entry-dialog .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
}

.usage-record__entry-dialog .el-dialog__header {
    background-color: #f9fafc;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
}

.usage-record__entry-dialog .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
}

.usage-record__entry-dialog .el-dialog__body {
    padding: 0;
}

.usage-record__entry-content {
    display: flex;
    flex-direction: column;
}

/* 工单信息卡片 */
.usage-record__entry-header {
    background-color: #f9fafc;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.usage-record__workorder-info {
    flex: 2;
}

.usage-record__workorder-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #ebeef5;
}

.usage-record__workorder-title {
    padding: 12px 15px;
    background-color: #f0f9eb;
    border-bottom: 1px solid #e1f3d8;
    color: #67c23a;
    font-weight: 600;
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.usage-record__workorder-details {
    padding: 15px;
}

.usage-record__workorder-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px dashed #ebeef5;
}

.usage-record__workorder-item:last-child {
    border-bottom: none;
}

.usage-record__workorder-label {
    color: #909399;
    font-size: 13px;
}

.usage-record__workorder-value {
    color: #303133;
    font-size: 14px;
    font-weight: 500;
}

/* 进度统计卡片 */
.usage-record__entry-progress {
    flex: 1;
    display: flex;
    gap: 15px;
    flex-direction: column;
}

.usage-record__progress-item {
    flex: 1;
}

.usage-record__progress-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #ebeef5;
    text-align: center;
    height: 100%;
}

.usage-record__progress-title {
    color: #909399;
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: 500;
}

.usage-record__progress-value {
    font-size: 24px;
    font-weight: 600;
    color: #409eff;
    display: flex;
    align-items: center;
    gap: 8px;
}

.usage-record__progress-remaining {
    color: #e6a23c;
}

.usage-record__progress-complete {
    color: #67c23a;
}

/* 表单区域 */
.usage-record__entry-body {
    padding: 20px;
}

.usage-record__entry-form {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #ebeef5;
    padding: 0;
}

.usage-record__form-group {
    padding: 15px;
    border-bottom: 1px solid #ebeef5;
}

.usage-record__form-group:last-child {
    border-bottom: none;
}

.usage-record__form-title {
    font-weight: 600;
    color: #303133;
    font-size: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.usage-record__form-tag {
    margin-left: 10px;
}

.usage-record__form-item {
    margin-bottom: 15px;
}

.usage-record__form-help {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
}

.usage-record__input:focus {
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 解除输入限制开关容器 */
.usage-record__switch-container {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.usage-record__switch-container:hover {
    border-color: #cbd5e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 开关样式 */
.usage-record__switch {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #dcdfe6;
    --el-switch-border-color: #dcdfe6;
    flex-shrink: 0;
}

.usage-record__switch .el-switch__core {
    border-radius: 12px;
    height: 22px;
    min-width: 44px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.usage-record__switch .el-switch__core:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.usage-record__switch .el-switch__core::after {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.usage-record__switch.is-checked .el-switch__core::after {
    box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
}

.usage-record__switch .el-switch__action {
    font-size: 11px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* 开关图标样式 */
.usage-record__switch-icon {
    color: #2769ec;
    font-size: 16px;
    transition: all 0.3s ease;
}

.usage-record__switch-icon:hover {
    color: #409eff;
    transform: scale(1.1);
}

/* 开关emoji样式 */
.usage-record__switch-emoji {
    font-size: 16px;
    transition: all 0.3s ease;
    cursor: default;
}

.usage-record__switch-emoji:hover {
    transform: scale(1.2);
    filter: brightness(1.2);
}

/* 帮助文本样式 */
.usage-record__switch-help {
    font-size: 12px;
    color: #718096;
    line-height: 1.4;
    flex: 1;
}

/* 底部按钮区域 */
.usage-record__entry-footer {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #ebeef5;
    background-color: #f9fafc;
}

.usage-record__button-cancel {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

.usage-record__button-submit {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-weight: 600;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-color: #4facfe;
    box-shadow: 0 2px 6px rgba(79, 172, 254, 0.4);
    transition: all 0.3s ease;
}

.usage-record__button-submit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(79, 172, 254, 0.5);
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 80%);
}

/* ===== 序列号明细对话框样式 ===== */
.usage-record__entry-detail-dialog .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
}

.usage-record__entry-detail-content {
    padding: 0;
}

.usage-record__entry-detail-header {
    background-color: #f9fafc;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
}

.usage-record__entry-detail-info {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    align-items: center;
}

.usage-record__entry-detail-workorder,
.usage-record__entry-detail-product,
.usage-record__entry-detail-count {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #606266;
}

.usage-record__entry-detail-value {
    font-weight: 600;
    color: #303133;
}

.usage-record__entry-detail-body {
    padding: 20px;
}

.usage-record__entry-detail-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.usage-record__type-tag {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.usage-record__type-tag--sn {
    background-color: #ecf5ff;
    color: #409eff;
    border: 1px solid #d9ecff;
}

.usage-record__type-tag--pcba {
    background-color: #f0f9eb;
    color: #67c23a;
    border: 1px solid #e1f3d8;
}

.usage-record__serial-value {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
    font-family: 'Courier New', monospace;
    word-break: break-all;
    font-weight: 600;
}

.usage-record__serial-sn {
    background-color: #ecf5ff;
    color: #409eff;
}

.usage-record__serial-pcba {
    background-color: #f0f9eb;
    color: #67c23a;
}

.usage-record__serial-empty {
    background-color: #f4f4f5;
    color: #909399;
}

.usage-record__time-value,
.usage-record__user-value {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    color: #606266;
}

.usage-record__entry-detail-footer {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #ebeef5;
    background-color: #f9fafc;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .usage-record__search-input {
        min-width: 100%;
        flex: 100%;
    }
    
    .usage-record__date-picker {
        min-width: 100%;
    }
    
    .usage-record__actions {
        flex-direction: column;
    }
    
    .usage-record__detail-body {
        flex-direction: column;
    }
    
    .usage-record__detail-left {
        margin-bottom: 20px;
    }
    
    .usage-record__info-grid {
        grid-template-columns: 1fr;
    }
    
    .usage-record__entry-header {
        flex-direction: column;
    }
    
    .usage-record__entry-progress {
        flex-direction: row;
        width: 100%;
    }
}

/* 修改: 添加"已入"和"剩余"数量卡片的新样式 */
.usage-record__entry-stats {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-top: 15px;
}

.usage-record__stat-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #ebeef5;
    transition: all 0.3s ease;
}

.usage-record__stat-box--entered {
    border-left: 4px solid #67c23a;
}

.usage-record__stat-box--remaining {
    border-left: 4px solid #e6a23c;
}

.usage-record__stat-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.usage-record__stat-label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 5px;
    text-align: center;
}

.usage-record__stat-value {
    font-size: 28px;
    font-weight: 700;
    text-align: center;
}

.usage-record__stat-value--entered {
    color: #67c23a;
}

.usage-record__stat-value--remaining {
    color: #e6a23c;
}

/* ===== 统计分析对话框样式 ===== */
.usage-record__statistics-dialog .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
}

.usage-record__statistics-content {
    padding: 20px;
}

.usage-record__statistics-header {
    margin-bottom: 20px;
    position: relative;
    padding-left: 15px;
}

.usage-record__statistics-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    position: relative;
}

.usage-record__statistics-title::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: linear-gradient(to bottom, #4facfe 0%, #00f2fe 100%);
    border-radius: 2px;
}

.usage-record__statistics-summary {
    margin-bottom: 30px;
}

.usage-record__statistics-section {
    margin-bottom: 20px;
}

.usage-record__stat-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    padding: 20px;
    height: 120px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.usage-record__stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.usage-record__stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 10px rgba(79, 172, 254, 0.3);
}

.usage-record__stat-icon i {
    font-size: 24px;
    color: white;
}

.usage-record__stat-content {
    flex: 1;
}

.usage-record__stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #303133;
    margin-bottom: 5px;
    line-height: 1.2;
}

.usage-record__stat-label {
    color: #909399;
    font-size: 14px;
}

.usage-record__ranking-panel {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #ebeef5;
}

.usage-record__ranking-header {
    padding: 15px;
    background-color: #f0f9eb;
    border-bottom: 1px solid #e1f3d8;
    color: #67c23a;
    font-weight: 600;
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.usage-record__ranking-body {
    padding: 15px;
}

.usage-record__ranking-list {
    max-height: 400px;
    overflow-y: auto;
}

.usage-record__ranking-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #ebeef5;
    transition: all 0.3s ease;
}

.usage-record__ranking-item:hover {
    background-color: #f9fafc;
    padding-left: 10px;
}

.usage-record__ranking-item:last-child {
    border-bottom: none;
}

.usage-record__ranking-number {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    color: white;
    flex-shrink: 0;
}

.usage-record__ranking-gold {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.usage-record__ranking-silver {
    background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
}

.usage-record__ranking-bronze {
    background: linear-gradient(135deg, #CD7F32 0%, #A56031 100%);
}

.usage-record__ranking-normal {
    background: linear-gradient(135deg, #909399 0%, #606266 100%);
}

.usage-record__ranking-info {
    flex: 1;
    min-width: 0;
}

.usage-record__ranking-name {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.usage-record__ranking-details {
    display: flex;
    gap: 15px;
}

.usage-record__ranking-stat {
    color: #909399;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.usage-record__ranking-bar {
    width: 120px;
    height: 8px;
    background-color: #f0f2f5;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.usage-record__ranking-progress {
    height: 100%;
    background: linear-gradient(to right, #4facfe, #00f2fe);
    border-radius: 4px;
}

.usage-record__button-export {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-weight: 600;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-color: #4facfe;
    box-shadow: 0 2px 6px rgba(79, 172, 254, 0.4);
    transition: all 0.3s ease;
}

.usage-record__button-export:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(79, 172, 254, 0.5);
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 80%);
}

/* 操作按钮样式优化 */
.firmware-page__action-button--danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    border-color: #f56565;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
}

.firmware-page__action-button--danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 101, 101, 0.4);
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 80%);
}

.usage-record__entry-detail-dialog .el-table {
    --el-table-header-bg-color: #f5f7fa;
}

.usage-record__detail-dialog .el-table {
    --el-table-header-bg-color: #f5f7fa;
} 