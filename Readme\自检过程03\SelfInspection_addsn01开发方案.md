# 质检工单SN号管理系统实现方案

## 目录

1. [系统概述](#系统概述)
2. [数据库结构](#数据库结构)
3. [功能实现](#功能实现)
4. [前端实现](#前端实现)
5. [后端实现](#后端实现)
6. [关键流程](#关键流程)
7. [注意事项](#注意事项)

## 系统概述

质检工单SN号管理系统是生产测试记录系统的重要组成部分，主要用于管理和跟踪产品在质检过程中的SN号。系统允许用户在不同的质检阶段（组装前、测试前、包装前）和不同的角色（首检、自检、IPQC）下，对产品进行SN号的扫描、添加和管理。

### 系统特点

- 支持多阶段质检流程：组装前、测试前、包装前
- 支持多角色质检：首检、自检、IPQC
- 自检过程需要记录每个产品的SN号
- 首检和IPQC只需要抽样部分产品
- 实时显示检验进度和状态
- 支持附件上传和管理
- 提供完整的质检记录查询功能

## 数据库结构

系统使用关系型数据库来存储工单、产品、质检项目和质检记录等信息。以下是主要的数据库表及其关系：

### 质检工单表 (quality_inspection_work_orders)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INT | 主键，工单ID |
| work_order_no | VARCHAR | 工单号，唯一标识 |
| product_type_id | INT | 外键，关联产品类型表 |
| status | VARCHAR | 工单状态（pending-待检验/processing-检验中/completed-已完成） |
| is_rework | BOOLEAN | 是否为返工工单 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 产品表 (products)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INT | 主键，产品ID |
| work_order_id | INT | 外键，关联工单表 |
| serial_no | VARCHAR | 产品SN号（不再是唯一标识） |
| is_rework | BOOLEAN | 是否为返工产品 |
| original_product_id | INT | 原始产品ID（如果是返工产品） |
| original_work_order_id | INT | 原始工单ID（如果是返工产品） |
| created_at | TIMESTAMP | 创建时间 |

### 产品类型表 (product_types)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INT | 主键，产品类型ID |
| type_code | VARCHAR | 产品类型代码，唯一标识 |
| type_name | VARCHAR | 产品类型名称 |
| description | VARCHAR | 产品类型描述 |
| created_at | TIMESTAMP | 创建时间 |

### 质检项目表 (inspection_items)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INT | 主键，质检项目ID |
| product_type_id | INT | 外键，关联产品类型表 |
| stage | VARCHAR | 质检阶段（assembly-组装前/test-测试前/packaging-包装前） |
| item_no | INT | 项目编号 |
| item_name | VARCHAR | 质检项目名称 |
| display_order | INT | 显示顺序 |
| is_required | BOOLEAN | 是否必检项 |
| created_at | TIMESTAMP | 创建时间 |

### 产品检验状态表 (product_inspection_status)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INT | 主键，检验状态ID |
| work_order_id | INT | 外键，关联工单表 |
| product_id | INT | 外键，关联产品表 |
| stage | VARCHAR | 检验阶段（assembly-组装前/test-测试前/packaging-包装前） |
| inspector_role | VARCHAR | 检验人员角色（first-首检/self-自检/ipqc-IPQC） |
| is_passed | BOOLEAN | 是否通过 |
| inspector | VARCHAR | 检验人 |
| inspection_time | TIMESTAMP | 检验时间 |

### 附件表 (inspection_attachments)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INT | 主键，附件ID |
| work_order_id | INT | 外键，关联工单表 |
| stage | VARCHAR | 质检阶段 |
| inspector_role | VARCHAR | 检验角色 |
| file_name | VARCHAR | 文件名 |
| file_path | VARCHAR | 文件路径 |
| file_type | VARCHAR | 文件类型 |
| file_size | INT | 文件大小(字节) |
| upload_time | TIMESTAMP | 上传时间 |
| uploaded_by | VARCHAR | 上传人员 |

### 数据库关系图

```
quality_inspection_work_orders (1) ------ (*) products
      |                                      |
      |                                      |
      v                                      v
product_types (1) ----- (*) inspection_items  product_inspection_status (*)
      |                                            ^
      |                                            |
      v                                            |
inspection_attachments (*) ----------------------- (*)
```

### 关系说明

1. 一个质检工单(quality_inspection_work_orders)可以包含多个产品(products)，一对多关系
2. 一个产品类型(product_types)可以有多个质检项目(inspection_items)，一对多关系
3. 一个质检工单(quality_inspection_work_orders)关联一个产品类型(product_types)，多对一关系
4. 产品检验状态(product_inspection_status)与质检工单(quality_inspection_work_orders)和产品(products)都是多对一关系
5. 附件(inspection_attachments)与质检工单(quality_inspection_work_orders)是多对一关系
6. 产品(products)可以通过original_product_id和original_work_order_id字段关联到原始产品，实现返工产品追溯

## 功能实现

质检工单SN号管理系统的核心功能是管理产品SN号与质检记录的关联。以下是主要功能的实现方式：

### 1. 工单创建与产品类型选择

1. 用户输入工单号并选择产品类型
2. 系统检查工单是否已存在
   - 如果不存在，创建新工单
   - 如果已存在，加载现有工单信息
3. 根据产品类型加载对应的质检项目

### 2. 质检项目管理

1. 系统根据产品类型和质检阶段（组装前、测试前、包装前）加载对应的质检项目
2. 用户可以查看每个阶段的质检项目列表
3. 质检项目按照角色（首检、自检、IPQC）分组显示

### 3. SN号扫描与管理

1. 用户在自检过程中需要扫描产品SN号
2. 系统提供SN号扫描界面，支持以下功能：
   - 手动输入或扫码枪扫描SN号
   - 验证SN号是否已存在于当前工单
   - 如果SN号不存在，根据工单类型有不同处理：
     - 普通工单：提示用户是否添加新产品
     - 返工工单：检查SN号是否存在于其他工单，如果存在则提示添加为返工产品，否则提示错误
   - 显示已扫描产品列表，支持删除操作
   - 支持批量扫描多个产品
   - 在产品列表中清晰标识返工产品
3. 扫描完成后，用户可以点击"开始检验"按钮，返回检验界面

### 4. 质检记录提交

1. 用户填写操作人员信息（自动填充当前登录用户名）
2. 用户勾选所有质检项目
3. 用户点击"提交自检"按钮
4. 如果未扫描SN号，系统显示SN号扫描界面
5. 扫描完成后，系统将质检记录与产品SN号关联保存
6. 系统更新质检状态，显示"已完成"标志
7. 对于返工产品，系统会记录其与原始产品的关联关系

### 5. 附件上传管理

1. 用户可以在任何阶段上传附件
2. 系统支持多种文件类型（图片、PDF等）
3. 上传的附件与工单、阶段和角色关联
4. 用户可以预览已上传的附件

### 6. 质检进度显示

1. 系统实时计算并显示质检进度
2. 进度基于已完成的质检项目数量
3. 当所有阶段的所有角色都完成质检时，整个流程完成

## 前端实现

前端实现主要基于HTML、CSS和JavaScript，使用了一些现代化的UI组件和交互方式。以下是主要的前端实现细节：

### 1. 页面结构

系统的前端页面主要包括以下几个部分：

1. **工单信息区域**：显示工单号、产品类型等基本信息
2. **质检阶段导航**：显示组装前、测试前、包装前三个阶段，支持展开/折叠
3. **角色选项卡**：每个阶段下包含首检、自检、IPQC三个选项卡
4. **质检项目列表**：显示当前阶段和角色下的质检项目列表
5. **操作区域**：包含操作人员输入框、重置按钮、上传附件按钮和提交按钮
6. **SN号扫描界面**：独立的界面，用于扫描和管理产品SN号

### 2. 关键JavaScript文件

系统的前端功能主要由以下JavaScript文件实现：

1. **SelfInspection.js**：主要的质检功能实现
   - 加载质检项目
   - 更新质检状态
   - 提交质检记录
   - 附件上传和管理
   - 进度计算和显示

2. **ScanSN.js**：SN号扫描和管理功能
   - 显示SN号扫描界面
   - 验证SN号
   - 添加新产品
   - 管理已扫描产品列表
   - 返回检验界面

### 3. SN号扫描界面实现

SN号扫描界面是一个模态窗口，通过JavaScript动态创建和管理。主要实现如下：

```javascript
// 显示SN号扫描界面
function showScanSerialNumberUI(stage, role) {
    // 创建扫描界面容器
    const scanContainer = document.createElement('div');
    scanContainer.className = 'scan-sn-container';

    // 设置界面内容
    scanContainer.innerHTML = `
        <div class="scan-sn-header">
            <h3>产品SN号扫描</h3>
            <div class="scan-sn-info">
                <span>工单号: ${document.getElementById('orderNo').value}</span>
                <span>阶段: ${getStageDisplayName(stage)}</span>
                <span>角色: ${getRoleDisplayName(role)}</span>
            </div>
        </div>

        <div class="scan-input-group">
            <input type="text" id="serialNoInput" placeholder="请输入或扫描SN号" autocomplete="off">
            <button class="scan-confirm-btn" onclick="processScanedSN('${stage}', '${role}')">确认</button>
        </div>

        <div class="scanned-products">
            <div class="scanned-products-header">
                <h4>已扫描产品 (<span id="scannedCount">0</span>)</h4>
                <button class="clear-all-btn" onclick="clearScannedProducts()">清空</button>
            </div>
            <div id="scannedProductList" class="scanned-product-list">
                <div class="no-products">暂无扫描产品</div>
            </div>
        </div>

        <div class="scan-actions">
            <button id="startInspectionBtn" class="btn-primary" onclick="startInspection('${stage}', '${role}')" disabled>开始检验</button>

            ${role === 'self' ? `
                <div class="self-inspection-note">
                    <i class="fas fa-info-circle"></i>自检过程需要记录每个产品的SN号
                </div>
            ` : `
                <div class="sampling-note">
                    <i class="fas fa-info-circle"></i>${role === 'first' ? '首检' : 'IPQC'}只需要抽样部分产品进行检验
                </div>
            `}
        </div>
    `;

    // 替换当前内容
    const contentContainer = document.getElementById(`${stage}-${role}`) || document.getElementById(`${stage}${role}`);
    if (contentContainer) {
        contentContainer.innerHTML = '';
        contentContainer.appendChild(scanContainer);

        // 设置焦点到输入框
        setTimeout(() => {
            const input = document.getElementById('serialNoInput');
            if (input) input.focus();
        }, 100);
    }
}
```

### 4. SN号验证和添加

当用户输入或扫描SN号后，系统会验证该SN号是否已存在于当前工单中：

```javascript
// 处理扫描的SN号
function processScanedSN(stage, role) {
    const serialNoInput = document.getElementById('serialNoInput');
    const serialNo = serialNoInput.value.trim();

    if (!serialNo) {
        showNotification('warning', 'SN号不能为空', '请输入或扫描有效的SN号');
        return;
    }

    // 检查是否已经扫描过该SN号
    if (currentScannedProducts.some(p => p.serial_no === serialNo)) {
        showNotification('warning', 'SN号重复', '该SN号已经扫描过');
        serialNoInput.value = '';
        serialNoInput.focus();
        return;
    }

    // 获取工单号
    const orderNo = document.getElementById('orderNo').value.trim();
    if (!orderNo) {
        showNotification('error', '工单号不能为空', '请先填写工单号');
        return;
    }

    // 检查SN号是否存在于工单中
    checkSerialNumber(inspectionData.currentWorkOrderId, serialNo)
        .then(data => {
            if (data.exists) {
                // SN号存在，添加到已扫描列表
                addScannedProduct(data.product);
            } else {
                // SN号不存在，询问是否添加
                SweetAlert.confirm(
                    `SN号 "${serialNo}" 不存在于当前工单中，是否添加新产品？`,
                    '添加新产品'
                ).then(confirmed => {
                    if (confirmed) {
                        // 添加新产品
                        addProductToWorkOrder(inspectionData.currentWorkOrderId, serialNo)
                            .then(data => {
                                if (data.success) {
                                    addScannedProduct(data.product);
                                } else {
                                    showNotification('error', '添加产品失败', data.message || '无法添加产品到工单');
                                }
                            });
                    }
                });
            }

            // 清空输入框并设置焦点
            serialNoInput.value = '';
            serialNoInput.focus();
        });
}
```

### 5. 返回检验界面

扫描完成后，用户点击"开始检验"按钮，系统会返回到检验界面：

```javascript
// 开始检验
function startInspection(stage, role) {
    if (currentScannedProducts.length === 0) {
        showNotification('warning', '请先扫描产品', '至少需要扫描一个产品才能开始检验');
        return;
    }

    // 返回到检验界面
    proceedToInspection(stage, role);
}

// 进入检验界面
function proceedToInspection(stage, role) {
    // 保存当前选中的产品ID列表
    inspectionData.selectedProductIds = currentScannedProducts.map(p => p.id);

    // 返回到检验界面
    switchTab(stage, role);

    // 确保操作按钮被正确渲染
    setTimeout(() => {
        renderActionButtons(stage, role);
    }, 500);

    // 通知用户已选择产品
    showNotification('success', `已选择${currentScannedProducts.length}个产品进行检验`);
}
```

### 6. 渲染操作按钮

为了确保返回检验界面后所有按钮都能正确显示，系统实现了一个专门的函数来渲染操作按钮：

```javascript
// 渲染操作按钮
function renderActionButtons(stage, role) {
    // 首先渲染全选按钮
    renderSelectAllButton(stage, role);

    // 然后渲染操作人员输入框
    renderOperatorInput(stage, role);

    // 最后渲染操作按钮和附件上传按钮
    renderActionAndAttachmentButtons(stage, role);
}
```

这种模块化的设计确保了即使在动态切换界面后，所有必要的UI元素都能被正确渲染。

## 后端实现

后端实现主要基于Python Flask框架，提供RESTful API接口供前端调用。以下是主要的后端实现细节：

### 1. API接口设计

系统的后端API主要包括以下几个部分：

1. **工单管理接口**
   - 创建工单
   - 获取工单信息
   - 更新工单状态

2. **产品管理接口**
   - 添加产品到工单
   - 检查SN号是否存在
   - 获取工单下的产品列表

3. **质检项目接口**
   - 获取产品类型的质检项目
   - 按阶段和角色分组获取质检项目

4. **质检记录接口**
   - 提交质检记录
   - 获取质检记录
   - 更新质检状态

5. **附件管理接口**
   - 上传附件
   - 获取附件列表
   - 下载附件

### 2. 关键API实现

#### 2.1 检查SN号是否存在

```python
@app.route('/api/quality-inspection/check-serial-number', methods=['GET'])
def check_serial_number():
    work_order_id = request.args.get('work_order_id')
    serial_no = request.args.get('serial_no')

    if not work_order_id or not serial_no:
        return jsonify({'success': False, 'message': '工单ID和SN号不能为空'}), 400

    try:
        # 查询数据库
        product = db.session.query(Product).filter(
            Product.work_order_id == work_order_id,
            Product.serial_no == serial_no
        ).first()

        if product:
            return jsonify({
                'success': True,
                'exists': True,
                'product': {
                    'id': product.id,
                    'serial_no': product.serial_no,
                    'work_order_id': product.work_order_id,
                    'status': product.status
                }
            })
        else:
            return jsonify({
                'success': True,
                'exists': False
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500
```

#### 2.2 添加产品到工单

```python
@app.route('/api/quality-inspection/work-order/<int:work_order_id>/products', methods=['POST'])
def add_product_to_work_order(work_order_id):
    data = request.json
    serial_no = data.get('serial_no')

    if not serial_no:
        return jsonify({'success': False, 'message': 'SN号不能为空'}), 400

    try:
        # 检查工单是否存在
        work_order = db.session.query(QualityInspectionWorkOrder).get(work_order_id)
        if not work_order:
            return jsonify({'success': False, 'message': '工单不存在'}), 404

        # 检查SN号是否已存在
        existing_product = db.session.query(Product).filter(
            Product.serial_no == serial_no
        ).first()

        if existing_product:
            return jsonify({'success': False, 'message': 'SN号已存在'}), 400

        # 创建新产品
        new_product = Product(
            work_order_id=work_order_id,
            serial_no=serial_no,
            status='active',
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        db.session.add(new_product)
        db.session.commit()

        return jsonify({
            'success': True,
            'product': {
                'id': new_product.id,
                'serial_no': new_product.serial_no,
                'work_order_id': new_product.work_order_id,
                'status': new_product.status
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500
```

#### 2.3 提交质检记录

```python
@app.route('/api/quality-inspection/records', methods=['POST'])
def save_inspection_records():
    data = request.json
    records_payload = data.get('records', [])
    product_ids = data.get('product_ids', [])
    is_final_submission = data.get('is_final_submission', False)

    if not records_payload:
        return jsonify({'success': False, 'message': '没有要保存的记录'}), 400

    if not product_ids:
        return jsonify({'success': False, 'message': '没有指定产品ID'}), 400

    # 从第一条记录获取必要信息
    if not records_payload[0]:
        return jsonify({'success': False, 'message': '记录数据无效'}), 400

    work_order_id = records_payload[0].get('work_order_id')
    inspector_role = records_payload[0].get('inspector_role')
    checked_by = records_payload[0].get('checked_by')
    inspection_item_id_for_stage = records_payload[0].get('inspection_item_id')

    if not all([work_order_id, inspector_role, checked_by, inspection_item_id_for_stage]):
        return jsonify({'success': False, 'message': '记录数据缺少必要字段'}), 400

    try:
        # 获取阶段信息
        item_for_stage = session.query(InspectionItem).filter_by(id=inspection_item_id_for_stage).first()
        if not item_for_stage:
            return jsonify({'success': False, 'message': f'检验项目ID {inspection_item_id_for_stage} 无效'}), 400
        stage = item_for_stage.stage

        # 验证产品ID
        for product_id in product_ids:
            product = session.query(Product).filter_by(id=product_id).first()
            if not product or product.work_order_id != int(work_order_id):
                return jsonify({'success': False, 'message': f'产品ID {product_id} 无效或不属于当前工单'}), 400

            # 检查产品是否已经被检验过
            if inspector_role == 'self':  # 只对自检角色进行此检查
                existing_status = session.query(ProductInspectionStatus).filter_by(
                    product_id=product_id,
                    stage=stage,
                    inspector_role=inspector_role
                ).first()

                if existing_status:
                    return jsonify({'success': False, 'message': f'产品 {product.serial_no} 已被其他人检验完成'}), 400

        # 为每个产品创建检验状态记录
        for product_id in product_ids:
            # 检查是否已存在记录
            existing_status = session.query(ProductInspectionStatus).filter_by(
                product_id=product_id,
                stage=stage,
                inspector_role=inspector_role
            ).first()

            if existing_status:
                # 更新现有记录
                existing_status.is_passed = True
                existing_status.inspector = checked_by
                existing_status.inspection_time = datetime.now()
            else:
                # 创建新记录
                new_status = ProductInspectionStatus(
                    work_order_id=work_order_id,
                    product_id=product_id,
                    stage=stage,
                    inspector_role=inspector_role,
                    is_passed=True,
                    inspector=checked_by,
                    inspection_time=datetime.now()
                )
                session.add(new_status)

        # 更新工单状态
        if is_final_submission:
            work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()
            if work_order and work_order.status == 'pending':
                work_order.status = 'processing'

        # 提交事务
        session.commit()

        # 构建响应数据
        response_data = {
            'success': True,
            'is_final_submission': is_final_submission,
            'products_completed': len(product_ids)
        }

        return jsonify(response_data)

    except Exception as e:
        session.rollback()
        error_message = f"保存记录时发生错误: {str(e)}"
        return jsonify({'success': False, 'message': error_message}), 500
```

### 3. 数据库模型

后端使用SQLAlchemy ORM来管理数据库模型，主要模型定义如下：

```python
class ProductType(Base):
    __tablename__ = 'product_types'

    id = Column(Integer, primary_key=True, autoincrement=True)
    type_code = Column(String(20), nullable=False, unique=True)
    type_name = Column(String(50), nullable=False)
    description = Column(String(200), nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.now)

    # 关系
    work_orders = relationship("QualityInspectionWorkOrder", back_populates="product_type")
    inspection_items = relationship("InspectionItem", back_populates="product_type")

class QualityInspectionWorkOrder(Base):
    __tablename__ = 'quality_inspection_work_orders'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_order_no = Column(String(50), nullable=False, unique=True)
    product_type_id = Column(Integer, ForeignKey('product_types.id'), nullable=False)
    status = Column(String(20), default='pending')
    is_rework = Column(Boolean, default=False, comment='是否为返工工单')
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    # 关系
    product_type = relationship("ProductType", back_populates="work_orders")
    inspection_attachments = relationship("InspectionAttachment", back_populates="work_order")
    products = relationship("Product", back_populates="work_order")
    inspection_statuses = relationship("ProductInspectionStatus", back_populates="work_order")

class Product(Base):
    __tablename__ = 'products'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_order_id = Column(Integer, ForeignKey('quality_inspection_work_orders.id'), nullable=False)
    serial_no = Column(String(50), nullable=False)  # 移除unique=True
    is_rework = Column(Boolean, default=False, comment='是否为返工产品')
    original_product_id = Column(Integer, nullable=True, comment='原始产品ID（如果是返工产品）')
    original_work_order_id = Column(Integer, nullable=True, comment='原始工单ID（如果是返工产品）')
    created_at = Column(DateTime, default=datetime.datetime.now)

    # 关系
    work_order = relationship("QualityInspectionWorkOrder", back_populates="products")
    inspection_statuses = relationship("ProductInspectionStatus", back_populates="product")

    # 联合唯一约束: 确保同一工单内SN号唯一
    __table_args__ = (
        UniqueConstraint('work_order_id', 'serial_no', name='uk_work_order_serial_no'),
    )

class InspectionItem(Base):
    __tablename__ = 'inspection_items'

    id = Column(Integer, primary_key=True, autoincrement=True)
    product_type_id = Column(Integer, ForeignKey('product_types.id'), nullable=False)
    stage = Column(String(20), nullable=False)  # assembly, test, packaging
    item_no = Column(Integer, nullable=False)
    item_name = Column(String(200), nullable=False)
    display_order = Column(Integer, default=0)
    is_required = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.datetime.now)

    # 关系
    product_type = relationship("ProductType", back_populates="inspection_items")

    # 联合唯一约束
    __table_args__ = (
        UniqueConstraint('product_type_id', 'stage', 'item_no', name='uk_product_stage_no'),
    )

class InspectionAttachment(Base):
    __tablename__ = 'inspection_attachments'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_order_id = Column(Integer, ForeignKey('quality_inspection_work_orders.id'), nullable=False)
    stage = Column(String(20), nullable=False)  # assembly, test, packaging
    inspector_role = Column(String(20), nullable=False)  # first, self, ipqc
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(255), nullable=False)
    file_type = Column(String(50), nullable=False)
    file_size = Column(Integer, nullable=False)
    upload_time = Column(DateTime, default=datetime.datetime.now)
    uploaded_by = Column(String(50), nullable=False)

    # 关系
    work_order = relationship("QualityInspectionWorkOrder", back_populates="inspection_attachments")

class ProductInspectionStatus(Base):
    __tablename__ = 'product_inspection_status'

    id = Column(Integer, primary_key=True, autoincrement=True)
    work_order_id = Column(Integer, ForeignKey('quality_inspection_work_orders.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    stage = Column(String(20), nullable=False, comment='检验阶段：assembly-组装前/test-测试前/packaging-包装前')
    inspector_role = Column(String(20), nullable=False, comment='检验人员角色：first-首检/self-自检/ipqc-IPQC')
    is_passed = Column(Boolean, nullable=False, default=True, comment='是否通过')
    inspector = Column(String(50), nullable=False, comment='检验人')
    inspection_time = Column(DateTime, default=datetime.datetime.now, comment='检验时间')

    # 关系
    work_order = relationship("QualityInspectionWorkOrder", back_populates="inspection_statuses")
    product = relationship("Product", back_populates="inspection_statuses")

    # 联合唯一约束: 确保每个产品在每个阶段每个角色只有一条记录
    __table_args__ = (
        UniqueConstraint('product_id', 'stage', 'inspector_role', name='uk_product_inspection'),
    )
```

### 4. 事务管理

为了确保数据一致性，系统在关键操作中使用了数据库事务：

1. **提交质检记录**：在一个事务中删除已有记录并添加新记录
2. **添加产品**：在一个事务中检查SN号是否存在并添加新产品
3. **上传附件**：在一个事务中保存文件和数据库记录

### 5. 错误处理

系统实现了全局的错误处理机制，确保API返回统一的错误格式：

```python
@app.errorhandler(Exception)
def handle_error(e):
    code = 500
    if isinstance(e, HTTPException):
        code = e.code
    return jsonify({
        'success': False,
        'message': str(e)
    }), code
```

### 6. 安全性考虑

系统实现了以下安全措施：

1. **输入验证**：所有API接口都进行了输入验证，防止无效数据
2. **SQL注入防护**：使用ORM和参数化查询，防止SQL注入
3. **事务管理**：使用事务确保数据一致性
4. **错误处理**：全局错误处理，防止敏感信息泄露

## 关键流程

### 1. 自检过程中的SN号管理流程

#### 普通工单自检流程

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  填写工单信息   │────▶│  勾选质检项目   │────▶│  点击提交自检   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  返回检验界面   │◀────│  点击开始检验   │◀────│  扫描产品SN号   │
│                 │     │                 │     │                 │
└────────┬────────┘     └─────────────────┘     └─────────────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  确认提交记录   │────▶│  保存质检记录   │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

#### 返工工单自检流程

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  填写工单信息   │────▶│  勾选返工工单   │────▶│  创建返工工单   │
│                 │     │  复选框         │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  勾选质检项目   │◀────│显示返工工单标识 │◀────│ 加载检验项目    │
│                 │     │                 │     │                 │
└────────┬────────┘     └─────────────────┘     └─────────────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  点击提交自检   │────▶│  扫描产品SN号   │────▶│ 验证SN号是否    │
│                 │     │                 │     │ 存在于其他工单  │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  返回检验界面   │◀────│  点击开始检验   │◀────│ 添加为返工产品  │
│                 │     │                 │     │ 并关联原始产品  │
└────────┬────────┘     └─────────────────┘     └─────────────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  确认提交记录   │────▶│  保存质检记录   │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

### 2. SN号验证和添加流程

#### 普通工单SN号验证流程

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  输入/扫描SN号  │────▶│  检查SN号格式   │────▶│ 检查是否已扫描  │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │ No  │                 │
│ 添加到已扫描列表│◀────│ SN号是否存在?   │────▶│ 询问是否添加新品│
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │ Yes                   │
         │                       │                       ▼
         │                       │             ┌─────────────────┐
         │                       │             │                 │
         │                       └────────────▶│ 添加新产品到工单│
         │                                     │                 │
         │                                     └────────┬────────┘
         │                                              │
         ▼                                              ▼
┌─────────────────┐                           ┌─────────────────┐
│                 │                           │                 │
│ 更新已扫描列表  │◀──────────────────────────│ 添加到已扫描列表│
│                 │                           │                 │
└─────────────────┘                           └─────────────────┘
```

#### 返工工单SN号验证流程

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  输入/扫描SN号  │────▶│  检查SN号格式   │────▶│ 检查是否已扫描  │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │ No  │                 │
│ 添加到已扫描列表│◀────│ SN号是否存在于  │────▶│ 检查SN号是否存在│
│                 │     │ 当前工单?       │     │ 于其他工单?     │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │ Yes                   │
         │                       │                       ▼
         │                       │             ┌─────────────────┐     ┌─────────────────┐
         │                       │             │                 │ No  │                 │
         │                       │             │ 是否存在于其他  │────▶│ 显示错误提示   │
         │                       │             │ 工单?           │     │                 │
         │                       │             └────────┬────────┘     └─────────────────┘
         │                       │                      │ Yes
         │                       │                      ▼
         │                       │             ┌─────────────────┐
         │                       │             │                 │
         │                       └────────────▶│ 询问是否添加为  │
         │                                     │ 返工产品        │
         │                                     └────────┬────────┘
         │                                              │
         │                                              ▼
         │                                     ┌─────────────────┐
         │                                     │                 │
         │                                     │ 添加返工产品并  │
         │                                     │ 关联原始产品    │
         │                                     └────────┬────────┘
         │                                              │
         ▼                                              ▼
┌─────────────────┐                           ┌─────────────────┐
│                 │                           │                 │
│ 更新已扫描列表  │◀──────────────────────────│ 添加到已扫描列表│
│ 并标识返工产品  │                           │                 │
└─────────────────┘                           └─────────────────┘
```

### 3. 质检记录提交流程

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  验证工单信息   │────▶│ 验证操作人员信息│────▶│ 验证质检项目状态│
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │ No  │                 │     │                 │
│  保存质检记录   │◀────│ 是否有选中产品? │◀────│ 确认提交对话框  │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └─────────────────┘
         │                       │ Yes
         │                       │
         │                       ▼
         │             ┌─────────────────┐
         │             │                 │
         │             │ 显示SN号扫描界面│
         │             │                 │
         │             └─────────────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  更新质检状态   │────▶│  显示成功提示   │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

## 注意事项

### 1. SN号管理

1. **唯一性验证**：系统需要确保每个SN号在数据库中是唯一的，防止重复录入
2. **批量扫描**：支持批量扫描多个产品SN号，提高工作效率
3. **错误处理**：当扫描到无效SN号时，系统应提供清晰的错误提示
4. **自动聚焦**：SN号输入框应自动获取焦点，方便连续扫描

### 2. 界面交互

1. **响应式设计**：界面应适应不同设备尺寸，支持移动设备操作
2. **状态反馈**：操作过程中应提供明确的状态反馈，如加载中、成功、失败等
3. **键盘支持**：支持键盘操作，如回车键确认，提高操作效率
4. **错误提示**：提供友好的错误提示，帮助用户快速定位问题

### 3. 数据一致性

1. **事务管理**：关键操作应使用数据库事务，确保数据一致性
2. **并发控制**：处理多用户同时操作同一工单的情况
3. **状态同步**：确保前后端状态同步，避免数据不一致
4. **缓存管理**：合理使用缓存，提高性能的同时确保数据一致性

### 4. 性能优化

1. **延迟加载**：对于大量数据，采用延迟加载策略
2. **批量操作**：支持批量提交质检记录，减少网络请求
3. **资源压缩**：压缩静态资源，减少加载时间
4. **数据库索引**：为常用查询字段创建索引，提高查询性能

### 5. 安全性

1. **输入验证**：所有用户输入都应进行验证，防止恶意输入
2. **权限控制**：不同角色应有不同的操作权限
3. **敏感信息保护**：敏感信息应加密存储
4. **日志记录**：记录关键操作日志，便于审计和问题排查

### 6. 可维护性

1. **模块化设计**：系统应采用模块化设计，便于维护和扩展
2. **代码注释**：代码应有充分的注释，便于理解和维护
3. **版本控制**：使用版本控制系统管理代码
4. **自动化测试**：编写自动化测试，确保代码质量

## 总结

质检工单SN号管理系统是一个完整的质量检验解决方案，它通过严格的SN号管理，确保每个产品都能被准确追踪和检验。系统的核心功能是将产品SN号与质检记录关联起来，特别是在自检过程中，需要记录每个产品的SN号。

系统采用了模块化的设计，前端使用HTML、CSS和JavaScript实现，后端使用Python Flask框架提供RESTful API。数据库设计考虑了各种实体之间的关系，确保数据的一致性和完整性。

### 自检过程的核心功能

自检过程是质检工单系统的核心功能之一，主要包括以下关键点：

1. **工单创建与管理**：
   - 支持创建普通工单和返工工单
   - 返工工单通过复选框标识，并显示醒目的返工标识
   - 工单信息包括工单号、产品类型等基本信息

2. **SN号管理**：
   - 普通工单可以添加新的SN号
   - 返工工单只能添加已存在于其他工单的SN号
   - 返工产品与原始产品建立关联关系，实现产品生命周期追踪
   - 在界面上清晰标识返工产品

3. **质检记录关联**：
   - 每个产品的检验状态与其SN号关联
   - 每个产品在每个阶段每个角色只有一条检验状态记录
   - 返工产品有独立的检验状态记录，同时保留与原始产品的关联
   - 支持按产品查询历史检验记录

4. **数据模型设计**：
   - 移除了产品表中SN号的唯一约束，改为工单ID和SN号的复合唯一约束
   - 添加了`is_rework`字段标识返工工单和返工产品
   - 添加了`original_product_id`和`original_work_order_id`字段追踪返工产品的来源
   - 新的`product_inspection_status`表记录每个产品在各阶段各角色的检验状态
   - 简化了数据结构，不再记录每个检验项目的结果，只记录阶段级别的检验状态

5. **数据库优化**：
   - 将原来的三张表(inspection_records、stage_completions、inspection_submissions)合并为一张product_inspection_status表
   - 大幅减少数据量，提高查询效率
   - 简化了数据访问逻辑，降低了代码复杂度
   - 保持了前端界面和交互逻辑基本不变

通过这个系统，质检人员可以：
1. 轻松管理工单和产品信息
2. 扫描和验证产品SN号
3. 完成各阶段的质检工作
4. 上传和管理附件
5. 查看质检进度和状态
6. 追踪产品的完整生命周期，包括返工历史

系统的实现充分考虑了用户体验、性能、安全性和可维护性，为生产测试记录提供了可靠的支持。特别是数据库优化和返工工单功能的实现，使系统能够支持更复杂的业务场景，满足实际生产环境中的需求，同时保持高效的性能和良好的可扩展性。

