#document-center-content {
    --dc-primary-color: #4f46e5;
    --dc-primary-hover: #4338ca;
    --dc-secondary-color: #64748b;
    --dc-background-color: #f8fafc;
    --dc-card-background: #ffffff;
    --dc-text-color: #334155;
    --dc-border-color: #e2e8f0;
    --dc-hover-bg: #f1f5f9;
    --dc-success-color: #10b981;
    --dc-danger-color: #ef4444;
    --dc-warning-color: #f59e0b;
    --dc-font-size-xs: 0.75rem;
    --dc-font-size-sm: 0.875rem;
    --dc-font-size-base: 1rem;
    --dc-font-size-lg: 1.125rem;
    --dc-font-size-xl: 1.375rem;
    --dc-font-size-2xl: 1.75rem;
    --dc-transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --dc-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --dc-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --dc-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    color: var(--dc-text-color);
    background-color: var(--dc-background-color);
    height: calc(100vh - 7.5rem);
    overflow-y: auto;
    padding: 1.5rem;
}

#document-center-content .dc-container {
    background-color: var(--dc-card-background);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--dc-shadow);
    transition: var(--dc-transition-base);
}

#document-center-content .dc-title {
    font-size: var(--dc-font-size-2xl);
    font-weight: 700;
    margin-bottom: 2rem;
    color: var(--dc-text-color);
    letter-spacing: -0.025em;
}

#document-center-content .dc-flex {
    display: flex;
}

#document-center-content .dc-flex-col {
    flex-direction: column;
}

#document-center-content .dc-gap-4 {
    gap: 1rem;
}

#document-center-content .dc-mb-6 {
    margin-bottom: 1.5rem;
}

#document-center-content .dc-search-wrapper {
    position: relative;
    flex-grow: 1;
}

#document-center-content .dc-search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dc-secondary-color);
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

#document-center-content .dc-search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--dc-border-color);
    border-radius: 0.5rem;
    appearance: none;
    font-size: var(--dc-font-size-base);
    background-color: var(--dc-card-background);
    transition: var(--dc-transition-base);
    box-shadow: var(--dc-shadow-sm);
}

#document-center-content .dc-search-input:focus {
    outline: none;
    border-color: var(--dc-primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

#document-center-content .dc-select-wrapper {
    position: relative;
    min-width: 120px;
}

#document-center-content .dc-select {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 1px solid var(--dc-border-color);
    border-radius: 0.5rem;
    appearance: none;
    font-size: var(--dc-font-size-base);
    background-color: var(--dc-card-background);
    transition: var(--dc-transition-base);
    box-shadow: var(--dc-shadow-sm);
    cursor: pointer;
}

#document-center-content .dc-select-wrapper::after {
    content: '';
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0.8rem;
    height: 0.8rem;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2364748b'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    pointer-events: none;
    opacity: 0.7;
}

#document-center-content .dc-select:hover {
    border-color: var(--dc-secondary-color);
}

#document-center-content .dc-select:focus {
    outline: none;
    border-color: var(--dc-primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

#document-center-content .dc-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: var(--dc-font-size-base);
    font-weight: 500;
    transition: var(--dc-transition-base);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--dc-shadow-sm);
}

#document-center-content .dc-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--dc-shadow);
}

#document-center-content .dc-btn-primary {
    background-color: var(--dc-primary-color);
    color: white;
}

#document-center-content .dc-btn-primary:hover {
    background-color: var(--dc-primary-hover);
}

#document-center-content .dc-btn-secondary {
    background-color: white;
    border: 1px solid var(--dc-border-color);
    color: var(--dc-text-color);
}

#document-center-content .dc-btn-secondary:hover {
    background-color: var(--dc-hover-bg);
    border-color: var(--dc-secondary-color);
}

#document-center-content .dc-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 1.5rem;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--dc-shadow);
}

#document-center-content .dc-table th,
#document-center-content .dc-table td {
    text-align: left;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--dc-border-color);
    transition: var(--dc-transition-base);
}

#document-center-content .dc-table th {
    background-color: var(--dc-hover-bg);
    font-size: var(--dc-font-size-sm);
    font-weight: 600;
    color: var(--dc-secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

#document-center-content .dc-table tbody tr {
    background-color: var(--dc-card-background);
    transition: var(--dc-transition-base);
}

#document-center-content .dc-table tbody tr:hover {
    background-color: var(--dc-hover-bg);
}

#document-center-content .dc-btn-icon {
    padding: 0.5rem;
    border-radius: 0.375rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: var(--dc-transition-base);
    color: var(--dc-secondary-color);
}

#document-center-content .dc-btn-icon:hover {
    background-color: var(--dc-hover-bg);
    color: var(--dc-primary-color);
}

#document-center-content .dc-btn-icon.delete-btn:hover {
    background-color: #fee2e2;
    color: var(--dc-danger-color);
}

#document-center-content .dc-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--dc-transition-base);
    backdrop-filter: blur(4px);
}

#document-center-content .dc-dialog-overlay.active {
    opacity: 1;
    visibility: visible;
}

#document-center-content .dc-dialog-content {
    background-color: var(--dc-card-background);
    padding: 2rem;
    border-radius: 1rem;
    max-width: 32rem;
    width: 100%;
    box-shadow: var(--dc-shadow-lg);
    transform: translateY(-20px) scale(0.95);
    transition: var(--dc-transition-base);
}

#document-center-content .dc-dialog-overlay.active .dc-dialog-content {
    transform: translateY(0) scale(1);
}

#document-center-content .dc-breadcrumb {
    display: flex;
    align-items: center;
    font-size: var(--dc-font-size-sm);
    color: var(--dc-secondary-color);
    padding: 0.5rem;
    background-color: var(--dc-hover-bg);
    border-radius: 0.5rem;
}

#document-center-content .dc-breadcrumb-btn {
    background: none;
    border: none;
    color: var(--dc-primary-color);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    font-weight: 500;
    transition: var(--dc-transition-base);
    border-radius: 0.25rem;
}

#document-center-content .dc-breadcrumb-btn:hover {
    background-color: rgba(79, 70, 229, 0.1);
}

#document-center-content .dc-loading {
    opacity: 0.5;
    pointer-events: none;
}

@media (max-width: 48rem) {
    #document-center-content {
        padding: 1rem;
    }
    
    #document-center-content .dc-container {
        padding: 1rem;
    }
    
    #document-center-content .dc-table th,
    #document-center-content .dc-table td {
        padding: 0.75rem;
    }
    
    #document-center-content .dc-flex {
        flex-direction: column;
    }
    
    #document-center-content .dc-gap-4 > * {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 30rem) {
    #document-center-content .dc-title {
        font-size: var(--dc-font-size-lg);
    }
    
    #document-center-content .dc-dialog-content {
        padding: 1.25rem;
    }
}

@media print {
    #document-center-content {
        height: auto;
        overflow: visible;
        padding: 0;
    }
    
    #document-center-content .dc-container {
        box-shadow: none;
    }
    
    #document-center-content .dc-dialog-overlay,
    #document-center-content .dc-action-buttons {
        display: none;
    }
}

/* 文件类型图标基础样式 */
#document-center-content .dc-icon {
    width: 1.5rem;
    height: 1.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: var(--dc-secondary-color);
}

/* 不同文件类型的图标颜色 */
#document-center-content .dc-icon-folder {
    color: var(--dc-warning-color);
}

#document-center-content .dc-icon-pdf {
    color: #ef4444;  /* 红色 */
}

#document-center-content .dc-icon-word {
    color: #2563eb;  /* 蓝色 */
}

#document-center-content .dc-icon-excel {
    color: #059669;  /* 绿色 */
}

#document-center-content .dc-icon-image {
    color: #7c3aed;  /* 紫色 */
}

#document-center-content .dc-icon-video {
    color: #db2777;  /* 粉色 */
}

/* 操作按钮图标 */
#document-center-content .dc-btn-icon {
    width: 2rem;
    height: 2rem;
    padding: 0.4rem;
    border-radius: 0.375rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: var(--dc-transition-base);
    color: var(--dc-secondary-color);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

#document-center-content .dc-btn-icon svg {
    width: 1.2rem;
    height: 1.2rem;
    transition: var(--dc-transition-base);
}

#document-center-content .dc-btn-icon:hover {
    background-color: var(--dc-hover-bg);
    color: var(--dc-primary-color);
}

#document-center-content .dc-btn-icon.preview-btn:hover {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--dc-primary-color);
}

#document-center-content .dc-btn-icon.download-btn:hover {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--dc-success-color);
}

#document-center-content .dc-btn-icon.delete-btn:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--dc-danger-color);
}

/* 文件夹按钮样式优化 */
#document-center-content .dc-folder-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 0.375rem;
    transition: var(--dc-transition-base);
    color: var(--dc-text-color);
    width: 100%;
    text-align: left;
}

#document-center-content .dc-folder-btn:hover {
    background-color: var(--dc-hover-bg);
}

#document-center-content .dc-folder-btn:active {
    background-color: var(--dc-border-color);
}

#document-center-content .dc-folder-btn .dc-icon {
    margin-right: 0.75rem;
    transition: var(--dc-transition-base);
}

#document-center-content .dc-folder-btn:hover .dc-icon {
    transform: scale(1.1);
}

#document-center-content .dc-folder-btn .dc-arrow {
    margin-left: auto;
    width: 1rem;
    height: 1rem;
    transition: transform 0.3s ease;
}

#document-center-content .dc-folder-btn:hover .dc-arrow {
    transform: translateX(4px);
}

/* 添加加载状态样式 */
#document-center-content .dc-folder-btn.loading {
    opacity: 0.7;
    pointer-events: none;
}

#document-center-content .dc-folder-btn.loading .dc-icon {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 表头样式 */
#document-center-content .dc-table th {
    background-color: var(--dc-hover-bg);
    font-size: var(--dc-font-size-sm);  /* 14px */
    font-weight: 600;
    color: var(--dc-secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1rem 1.5rem;
}

/* 文件夹和文件名称样式 */
#document-center-content .dc-folder-btn,
#document-center-content .dc-file-name {
    font-size: var(--dc-font-size-base);  /* 16px */
    font-weight: 500;
    color: var(--dc-text-color);
}

/* 文件夹按钮内的文本 */
#document-center-content .dc-folder-btn span,
#document-center-content .dc-file-name span {
    font-size: var(--dc-font-size-base);  /* 16px */
    font-weight: 500;
}

/* 表格其他字段（类型、大小、日期、作者）*/
#document-center-content .dc-table td {
    font-size: var(--dc-font-size-sm);  /* 14px */
    font-weight: 400;
    color: var(--dc-secondary-color);
    padding: 1rem 1.5rem;
}

/* 文件夹和文件所在的单元格特殊处理 */
#document-center-content .dc-table td:first-child {
    font-size: var(--dc-font-size-base);  /* 16px */
    font-weight: 500;
    color: var(--dc-text-color);
}

/* 响应式调整 */
@media (max-width: 48rem) {
    #document-center-content .dc-table th {
        font-size: var(--dc-font-size-xs);  /* 12px */
    }
    
    #document-center-content .dc-folder-btn,
    #document-center-content .dc-file-name,
    #document-center-content .dc-folder-btn span,
    #document-center-content .dc-file-name span,
    #document-center-content .dc-table td:first-child {
        font-size: var(--dc-font-size-sm);  /* 14px */
    }
    
    #document-center-content .dc-table td {
        font-size: var(--dc-font-size-xs);  /* 12px */
    }
}
