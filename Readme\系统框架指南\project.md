# 项目架构分析文档

本文档旨在分析当前项目的整体架构，特别是前端的实现结构和前后端通信机制。

## 一、 项目整体架构

本项目是一个基于 **Flask** 框架构建的 Web 应用程序，主要用于生产管理和追溯。其核心架构特点如下：

*   **后端技术栈**:
    *   **框架**: Flask
    *   **数据库**: 使用关系型数据库，通过 `DatabaseManager` (可能基于 SQLAlchemy 或类似 ORM)进行交互。数据库表结构定义在 `models/` 目录中，并通过 `20250512表创建.sql` 文件进行初始化。
    *   **用户认证**: 采用 JWT (JSON Web Tokens) 进行用户身份验证和会话管理。密码通过 SHA256 哈希加密。
*   **模块化设计**:
    *   后端通过 Flask **Blueprints（蓝图）** 将不同业务功能模块化，例如产品管理、工单管理、故障录入、条码管理等。每个蓝图在 `routes/` 目录下定义，并有独立的 URL 前缀 (通常是 `/api/...`)。
*   **API 驱动**:
    *   项目主要通过 RESTful API 接口（JSON格式）暴露后端功能，供前端调用。
*   **日志系统**:
    *   使用 Python 内置的 `logging` 模块，日志输出到 `app.log` 文件和控制台。

## 二、 前端架构

前端采用的是一种**基于 Hash 的前端路由实现的类单页应用 (SPA-like)**架构。它不依赖于大型前端框架（如 Vue, React, Angular），而是通过原生 JavaScript 配合 Flask 的模板引擎来实现动态页面和交互。

*   **核心技术栈**:
    *   **HTML**: 页面结构基础。
    *   **CSS**: 页面样式，包括全局样式和模块化样式。
    *   **JavaScript (Vanilla JS)**: 实现前端逻辑、DOM 操作、API 调用和动态内容加载。
    *   **模板引擎**: Flask 默认的 **Jinja2** 用于在服务器端渲染初始 HTML 骨架 (`index.html`, `login.html`)。
*   **核心文件**:
    *   `templates/index.html`: 主应用程序的骨架页面，承载所有动态加载的内容。
    *   `templates/login.html`: 用户登录页面。
    *   `static/style.css`: 全局 CSS 样式表，定义了整体布局、颜色、字体和基础组件样式。
    *   `static/script.js`: 前端的核心 JavaScript 文件，负责：
        *   前端路由（基于 URL Hash）。
        *   动态加载和卸载页面模块（CSS 和 JS）。
        *   主页面布局交互（侧边栏、头部、标签页）。
        *   用户状态管理（获取用户信息、处理登出）。
    *   `static/login.js`: 处理登录页面的特定逻辑。
*   **前端结构与特性**:
    1.  **主页面布局 (`index.html`)**:
        *   **侧边栏 (`sidebar-container`)**: 可折叠的导航菜单，通过 `data-page` 属性链接到不同功能模块。
        *   **固定头部 (`fixed-header`)**: 包含汉堡包菜单、面包屑导航、标签页视图 (`tags-view-container`)、以及全屏、语言切换、用户信息和登出等操作。
        *   **主内容区 (`app-main > #content`)**: 动态加载各个功能模块的视图内容。
    2.  **模块化**:
        *   每个主要功能模块拥有其独立的 CSS 和 JavaScript 文件，存放在 `static/page_js_css/` 目录下（例如 `Dashboard.js`, `Dashboard.css`, `IOModule.js`, `IOModule.css` 等）。
        *   `static/script.js` 中的 `updateContent(page)` 函数根据路由动态加载这些模块资源，并在加载完成后执行模块的初始化函数 (如 `initDashboard()`)。
    3.  **前端路由**:
        *   基于 `window.location.hash` 实现。当用户点击菜单或标签页时，URL的 Hash 部分会更新 (例如 `index.html#dashboard`)。
        *   `static/script.js` 通过监听 `hashchange` 事件或在页面加载时读取初始 Hash，来决定加载哪个模块的内容。
        *   `savePageState()` 和 `loadPageState()` 函数分别用于保存和恢复页面状态。
    4.  **UI 组件与库**:
        *   **Font Awesome**: 用于显示图标。
        *   **SweetAlert2**: 用于美观的弹窗提示和确认框。
        *   **ECharts**: （推测用于仪表盘等数据可视化，从 `app.py` 的 `dashboard_bp` 和相关库引用可知）。
        *   **html2canvas**: （可能用于页面截图或导出功能，从 `app.py` 路由可知）。
        *   `particleEffect.js`: 用于登录页面的动态粒子背景效果。
    5.  **样式管理**:
        *   使用 CSS 变量 (`:root` in `style.css`) 定义全局颜色、字体、尺寸等，方便主题和样式统一管理。
        *   包含响应式设计 (`@media` 查询) 以适应不同屏幕尺寸。
        *   提供打印样式 (`@media print`)。

## 三、 前后端通信

前端和后端之间主要通过 **AJAX** 请求进行异步通信。

*   **通信方式**:
    *   前端 JavaScript (主要在各模块的 JS 文件中，以及 `login.js`) 使用 `fetch` API 或 `XMLHttpRequest` (或其封装) 向后端 Flask 应用发起 HTTP 请求。
*   **数据格式**:
    *   请求体和响应体主要使用 **JSON** 格式。
*   **API 端点**:
    *   后端通过 Flask 蓝图定义的 `/api/...` 路由提供服务接口。
*   **认证机制**:
    *   用户登录成功后，后端会生成一个 **JWT Token**。
    *   此 Token 通常存储在客户端的 **Cookie** 中 (由 `app.py` 中的登录接口设置 `response.set_cookie('token', ...)` 实现)。
    *   前端在后续向需要授权的 API 端点发送请求时，浏览器会自动携带该 Cookie，后端通过解析 Cookie 中的 Token 来验证用户身份。
*   **典型通信流程**:
    1.  用户在前端界面执行操作（如点击按钮、填写表单后提交）。
    2.  前端 JavaScript 捕获该事件，收集用户输入的数据。
    3.  构造一个 AJAX 请求（例如 `POST /api/some-module/create` 或 `GET /api/some-module/list`），将数据作为 JSON 发送到后端对应的 API 端点。
    4.  Flask 后端接收请求，验证 JWT Token (如果需要授权)，解析请求数据。
    5.  后端执行相应的业务逻辑（如数据库增删改查、调用服务等）。
    6.  后端将处理结果（成功信息、错误信息、查询到的数据等）封装成 JSON 格式返回给前端。
    7.  前端 JavaScript 接收到响应数据。
    8.  根据响应结果更新前端界面（如显示成功/错误提示、刷新数据列表、跳转页面等）。

## 四、 总结

该项目采用 Flask 构建后端 API 服务，前端则通过原生 JavaScript 实现了一个类 SPA 的交互体验。这种架构在模块化、前后端分离方面做出了一定的努力，通过动态加载资源和基于 Hash 的路由，实现了无需页面整体刷新的用户体验。前后端通过 JSON 格式的 API 进行数据交换，并使用 JWT 进行用户认证。 