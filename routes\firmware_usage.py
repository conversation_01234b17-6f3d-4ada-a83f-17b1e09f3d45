# routes/firmware_usage.py - 简化版本
from flask import Blueprint, request, jsonify, send_file
from models.firmware import Firmware, DownloadRecord
from database.db_manager import DatabaseManager
from utils.firmware_utils import create_response, handle_db_error, validate_datetime_range
from sqlalchemy import and_, or_, desc, asc, func
from datetime import datetime, timedelta
import logging

usage_bp = Blueprint('firmware_usage', __name__, url_prefix='/api/firmware/usage')
logger = logging.getLogger(__name__)
db_manager = DatabaseManager()

@usage_bp.route('/list', methods=['GET'])
def get_usage_list():
    """获取使用记录列表（分页）"""
    try:
        # 参数获取和验证
        page = max(1, int(request.args.get('page', 1)))
        per_page = min(5000, max(1, int(request.args.get('per_page', 20))))  # 增加最大限制到2000
        search = request.args.get('search', '').strip()
        serial_number = request.args.get('serial_number', '').strip()
        work_order = request.args.get('work_order', '').strip()
        product_model = request.args.get('product_model', '').strip()
        user_name = request.args.get('user_name', '').strip()
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()
        sort_by = request.args.get('sort_by', 'create_time')
        sort_order = request.args.get('sort_order', 'desc')

        # 添加获取所有数据的特殊处理
        get_all = request.args.get('get_all', '').lower() == 'true'
        if get_all:
            per_page = 10000  # 设置一个非常大的值来获取所有数据
            logger.info("请求获取所有使用记录数据，不进行分页限制")
        
        with db_manager.get_session() as session:
            # 构建查询
            query = session.query(DownloadRecord).filter(
                DownloadRecord.is_deleted == False
            )
            
            # 默认限制最近6个月的数据（除非用户指定了时间范围）
            if not start_date and not end_date:
                six_months_ago = datetime.now() - timedelta(days=180)  # 6个月 ≈ 180天
                query = query.filter(DownloadRecord.create_time >= six_months_ago)
            
            # 搜索过滤
            if search:
                search_filter = or_(
                    DownloadRecord.serial_number.ilike(f'%{search}%'),
                    DownloadRecord.work_order.ilike(f'%{search}%'),
                    DownloadRecord.product_code.ilike(f'%{search}%'),
                    DownloadRecord.product_model.ilike(f'%{search}%'),
                    DownloadRecord.user_name.ilike(f'%{search}%'),
                    DownloadRecord.notes.ilike(f'%{search}%')
                )
                query = query.filter(search_filter)
            
            # 精确过滤
            if serial_number:
                query = query.filter(DownloadRecord.serial_number == serial_number)
            if work_order:
                query = query.filter(DownloadRecord.work_order.ilike(f'%{work_order}%'))
            if product_model:
                query = query.filter(DownloadRecord.product_model.ilike(f'%{product_model}%'))
            if user_name:
                query = query.filter(DownloadRecord.user_name.ilike(f'%{user_name}%'))
            
            # 日期范围过滤
            if start_date:
                try:
                    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(DownloadRecord.create_time >= start_dt)
                except ValueError:
                    return create_response(False, '开始日期格式错误', code=400)
            
            if end_date:
                try:
                    end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                    query = query.filter(DownloadRecord.create_time < end_dt)
                except ValueError:
                    return create_response(False, '结束日期格式错误', code=400)
            
            # 排序
            sort_field = getattr(DownloadRecord, sort_by, DownloadRecord.create_time)
            if sort_order.lower() == 'asc':
                query = query.order_by(asc(sort_field))
            else:
                query = query.order_by(desc(sort_field))
            
            # 分页
            total = query.count()
            offset = (page - 1) * per_page
            items = query.offset(offset).limit(per_page).all()
            
            # 为每条记录添加固件信息
            result_items = []
            for record in items:
                item_data = record.to_dict()
                # 获取关联的固件信息
                firmware = session.query(Firmware).filter_by(
                    serial_number=record.serial_number,
                    is_deleted=False
                ).first()
                if firmware:
                    item_data.update({
                        'firmwareName': firmware.name,
                        'firmwareVersion': firmware.version,
                        'developer': firmware.developer,
                        'versionRequirements': firmware.version_requirements,
                        'firmwareStatus': firmware.status
                    })
                result_items.append(item_data)
        
            return create_response(True, '查询成功', {
                'items': result_items,
                'total': total,
                'page': page,
                'per_page': per_page,
                'total_pages': (total + per_page - 1) // per_page
            })
        
    except Exception as e:
        logger.error(f"获取使用记录列表失败: {str(e)}")
        return handle_db_error(e)

@usage_bp.route('/add', methods=['POST'])
def add_usage_record():
    """添加使用记录"""
    try:
        data = request.get_json()
        
        # 验证必要字段
        required_fields = ['serialNumber', 'workOrder', 'productCode', 'productModel', 
                          'burnCount', 'softwareVersion', 'buildTime', 'backplaneVersion', 
                          'ioVersion', 'userName']
        
        for field in required_fields:
            if field not in data or not str(data[field]).strip():
                return create_response(False, f'字段 {field} 不能为空', code=400)
        
        # 处理构建时间
        try:
            if isinstance(data['buildTime'], str):
                build_time = datetime.strptime(data['buildTime'], '%Y-%m-%d %H:%M:%S')
            else:
                build_time = data['buildTime']
        except ValueError:
            return create_response(False, '构建时间格式错误，应为 YYYY-MM-DD HH:MM:SS', code=400)
        
        with db_manager.get_session() as session:
            # 验证固件存在且已生效
            firmware = session.query(Firmware).filter_by(
                serial_number=data['serialNumber'],
                status='active',
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '固件不存在或未生效', code=404)
            
            # 创建下载记录
            download_record = DownloadRecord(
                serial_number=data['serialNumber'],
                work_order=data['workOrder'],
                product_code=data['productCode'],
                product_model=data['productModel'],
                burn_count=int(data['burnCount']),
                software_version=data['softwareVersion'],
                build_time=build_time,
                backplane_version=data['backplaneVersion'],
                io_version=data['ioVersion'],
                user_name=data['userName'],
                notes=data.get('notes', '')
            )
            session.add(download_record)
            
            # 更新固件使用统计
            firmware.usage_count += int(data['burnCount'])
            firmware.update_time = datetime.now()
            
            session.commit()
            
            logger.info(f"使用记录添加成功: {data['workOrder']} - {data['serialNumber']}")
            return create_response(True, '使用记录添加成功')
            
    except Exception as e:
        logger.error(f"添加使用记录失败: {str(e)}")
        return handle_db_error(e)

@usage_bp.route('/record/<serial_number>', methods=['POST'])
def add_usage_record_by_serial(serial_number):
    """根据流水号添加使用记录（与前端数据管理器API路径匹配）"""
    try:
        data = request.get_json()
        
        # 验证必要字段
        required_fields = ['workOrder', 'productCode', 'productModel', 
                          'burnCount', 'softwareVersion', 'buildTime', 'backplaneVersion', 
                          'highSpeedIOVersion', 'userName']
        
        for field in required_fields:
            if field not in data or not str(data[field]).strip():
                return create_response(False, f'字段 {field} 不能为空', code=400)
        
        # 处理构建时间
        try:
            if isinstance(data['buildTime'], str):
                build_time = datetime.strptime(data['buildTime'], '%Y-%m-%d %H:%M:%S')
            else:
                build_time = data['buildTime']
        except ValueError:
            return create_response(False, '构建时间格式错误，应为 YYYY-MM-DD HH:MM:SS', code=400)
        
        with db_manager.get_session() as session:
            # 验证固件存在且已生效
            firmware = session.query(Firmware).filter_by(
                serial_number=serial_number,
                status='active',
                is_deleted=False
            ).first()
            
            if not firmware:
                return create_response(False, '固件不存在或未生效', code=404)
            
            # 创建使用记录
            download_record = DownloadRecord(
                serial_number=serial_number,
                work_order=data['workOrder'],
                product_code=data['productCode'],
                product_model=data['productModel'],
                burn_count=int(data['burnCount']),
                software_version=data['softwareVersion'],
                build_time=build_time,
                backplane_version=data['backplaneVersion'],
                io_version=data['highSpeedIOVersion'],  # 注意字段名映射
                user_name=data['userName'],
                notes=data.get('notes', '')
            )
            session.add(download_record)
            
            # 更新固件使用统计
            firmware.usage_count += int(data['burnCount'])
            firmware.update_time = datetime.now()
            
            session.commit()
            
            logger.info(f"使用记录添加成功: {data['workOrder']} - {serial_number}")
            return create_response(True, '使用记录保存成功', {
                'recordId': download_record.id,
                'serialNumber': serial_number,
                'usageCount': firmware.usage_count
            })
            
    except Exception as e:
        logger.error(f"添加使用记录失败: {str(e)}")
        return handle_db_error(e)

@usage_bp.route('/detail/<work_order>', methods=['GET'])
def get_usage_detail(work_order):
    """获取工单详细信息"""
    try:
        with db_manager.get_session() as session:
            records = session.query(DownloadRecord).filter_by(
                work_order=work_order,
                is_deleted=False
            ).all()
            
            if not records:
                return create_response(False, '工单记录不存在', code=404)
            
            # 获取工单相关的所有记录和固件信息
            result_items = []
            firmware_info = {}
            
            for record in records:
                item_data = record.to_dict()
                
                # 获取固件信息（缓存避免重复查询）
                if record.serial_number not in firmware_info:
                    firmware = session.query(Firmware).filter_by(
                        serial_number=record.serial_number,
                        is_deleted=False
                    ).first()
                    if firmware:
                        firmware_info[record.serial_number] = {
                            'name': firmware.name,
                            'version': firmware.version,
                            'developer': firmware.developer,
                            'versionRequirements': firmware.version_requirements,
                            'products': firmware.products,
                            'status': firmware.status,
                            'approveTime': firmware.approve_time.strftime('%Y-%m-%d %H:%M:%S') if firmware.approve_time else None
                        }
                
                # 添加固件信息
                if record.serial_number in firmware_info:
                    item_data.update(firmware_info[record.serial_number])
                
                result_items.append(item_data)
            
            # 统计信息
            total_burn_count = sum(record.burn_count for record in records)
            firmware_count = len(set(record.serial_number for record in records))
            
            return create_response(True, '查询成功', {
                'workOrder': work_order,
                'records': result_items,
                'statistics': {
                    'totalBurnCount': total_burn_count,
                    'firmwareCount': firmware_count,
                    'recordCount': len(result_items)
                }
            })
            
    except Exception as e:
        logger.error(f"获取工单详情失败: {str(e)}")
        return handle_db_error(e)

@usage_bp.route('/stats', methods=['GET'])
def get_usage_stats():
    """获取使用统计信息"""
    try:
        # 时间范围参数
        days = int(request.args.get('days', 30))  # 默认最近30天
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()
        
        with db_manager.get_session() as session:
            # 构建时间过滤条件
            query_filter = [DownloadRecord.is_deleted == False]
            
            if start_date and end_date:
                # 使用指定日期范围
                try:
                    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                    end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                    query_filter.extend([
                        DownloadRecord.create_time >= start_dt,
                        DownloadRecord.create_time < end_dt
                    ])
                    date_range = f"{start_date} 至 {end_date}"
                except ValueError:
                    return create_response(False, '日期格式错误', code=400)
            else:
                # 使用最近N天
                start_dt = datetime.now() - timedelta(days=days)
                query_filter.append(DownloadRecord.create_time >= start_dt)
                date_range = f"最近{days}天"
            
            # 基础统计
            base_query = session.query(DownloadRecord).filter(and_(*query_filter))
            
            total_records = base_query.count()
            total_burn_count = base_query.with_entities(func.sum(DownloadRecord.burn_count)).scalar() or 0
            unique_work_orders = base_query.with_entities(DownloadRecord.work_order).distinct().count()
            unique_firmware = base_query.with_entities(DownloadRecord.serial_number).distinct().count()
            
            # 按固件统计
            firmware_stats = session.query(
                DownloadRecord.serial_number,
                func.count(DownloadRecord.id).label('usage_count'),
                func.sum(DownloadRecord.burn_count).label('total_burn_count')
            ).filter(and_(*query_filter)).group_by(
                DownloadRecord.serial_number
            ).order_by(desc('usage_count')).limit(10).all()
            
            # 获取固件详细信息
            firmware_details = []
            for stat in firmware_stats:
                firmware = session.query(Firmware).filter_by(
                    serial_number=stat.serial_number,
                    is_deleted=False
                ).first()
                
                detail = {
                    'serialNumber': stat.serial_number,
                    'usageCount': stat.usage_count,
                    'totalBurnCount': stat.total_burn_count,
                    'firmwareName': firmware.name if firmware else '未知',
                    'firmwareVersion': firmware.version if firmware else '未知',
                    'developer': firmware.developer if firmware else '未知'
                }
                firmware_details.append(detail)
            
            # 按用户统计
            user_stats = session.query(
                DownloadRecord.user_name,
                func.count(DownloadRecord.id).label('usage_count'),
                func.sum(DownloadRecord.burn_count).label('total_burn_count')
            ).filter(and_(*query_filter)).group_by(
                DownloadRecord.user_name
            ).order_by(desc('usage_count')).limit(10).all()
            
            user_details = [
                {
                    'userName': stat.user_name,
                    'usageCount': stat.usage_count,
                    'totalBurnCount': stat.total_burn_count
                }
                for stat in user_stats
            ]
            
            # 按日期统计（最近7天）
            daily_stats = []
            for i in range(7):
                date = datetime.now().date() - timedelta(days=i)
                daily_count = session.query(DownloadRecord).filter(
                    and_(
                        DownloadRecord.is_deleted == False,
                        func.date(DownloadRecord.create_time) == date
                    )
                ).count()
                daily_burn = session.query(func.sum(DownloadRecord.burn_count)).filter(
                    and_(
                        DownloadRecord.is_deleted == False,
                        func.date(DownloadRecord.create_time) == date
                    )
                ).scalar() or 0
                
                daily_stats.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'usageCount': daily_count,
                    'burnCount': daily_burn
                })
            
            daily_stats.reverse()  # 按时间正序
            
            return create_response(True, '查询成功', {
                'dateRange': date_range,
                'summary': {
                    'totalRecords': total_records,
                    'totalBurnCount': int(total_burn_count),
                    'uniqueWorkOrders': unique_work_orders,
                    'uniqueFirmware': unique_firmware
                },
                'firmwareStats': firmware_details,
                'userStats': user_details,
                'dailyStats': daily_stats
            })
            
    except Exception as e:
        logger.error(f"获取使用统计失败: {str(e)}")
        return handle_db_error(e)

@usage_bp.route('/export', methods=['GET'])
def export_usage_records():
    """导出使用记录"""
    try:
        # 获取查询参数
        search = request.args.get('search', '').strip()
        serial_number = request.args.get('serial_number', '').strip()
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()
        
        with db_manager.get_session() as session:
            # 构建查询
            query = session.query(DownloadRecord).filter(
                DownloadRecord.is_deleted == False
            )
            
            # 应用过滤条件
            if search:
                search_filter = or_(
                    DownloadRecord.serial_number.ilike(f'%{search}%'),
                    DownloadRecord.work_order.ilike(f'%{search}%'),
                    DownloadRecord.product_code.ilike(f'%{search}%'),
                    DownloadRecord.product_model.ilike(f'%{search}%'),
                    DownloadRecord.user_name.ilike(f'%{search}%')
                )
                query = query.filter(search_filter)
            
            if serial_number:
                query = query.filter(DownloadRecord.serial_number == serial_number)
            
            if start_date:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(DownloadRecord.create_time >= start_dt)
            
            if end_date:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                query = query.filter(DownloadRecord.create_time < end_dt)
            
            # 获取所有记录
            records = query.order_by(desc(DownloadRecord.create_time)).all()
            
            # 准备导出数据
            export_data = []
            for record in records:
                # 获取固件信息
                firmware = session.query(Firmware).filter_by(
                    serial_number=record.serial_number,
                    is_deleted=False
                ).first()
                
                row_data = {
                    'ERP流水号': record.serial_number,
                    '固件名称': firmware.name if firmware else '未知',
                    '固件版本': firmware.version if firmware else '未知',
                    '研发者': firmware.developer if firmware else '未知',
                    '工单号': record.work_order,
                    '产品编码': record.product_code,
                    '产品型号': record.product_model,
                    '生产数量': record.burn_count,
                    '软件版本': record.software_version,
                    '构建时间': record.build_time.strftime('%Y-%m-%d %H:%M:%S') if record.build_time else '',
                    '背板版本': record.backplane_version,
                    '高速IO版本': record.io_version,
                    '使用人': record.user_name,
                    '使用时间': record.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    '备注': record.notes or ''
                }
                export_data.append(row_data)
            
            # 这里可以调用导出工具生成Excel文件
            # file_path = FirmwareUtils.export_to_excel(export_data, '固件使用记录')
            
            return create_response(True, f'共导出 {len(export_data)} 条记录', {
                'recordCount': len(export_data),
                'data': export_data[:100]  # 仅返回前100条预览
            })
            
    except Exception as e:
        logger.error(f"导出使用记录失败: {str(e)}")
        return handle_db_error(e)

@usage_bp.route('/delete/<int:record_id>', methods=['DELETE'])
def delete_usage_record(record_id):
    """删除使用记录（软删除）"""
    try:
        operator_name = request.args.get('operator', 'unknown')
        
        with db_manager.get_session() as session:
            record = session.query(DownloadRecord).filter_by(
                id=record_id,
                is_deleted=False
            ).first()
            
            if not record:
                return create_response(False, '使用记录不存在', code=404)
            
            # 软删除
            record.is_deleted = True
            
            # 更新固件使用统计
            firmware = session.query(Firmware).filter_by(
                serial_number=record.serial_number,
                is_deleted=False
            ).first()
            
            if firmware:
                firmware.usage_count = max(0, firmware.usage_count - record.burn_count)
                firmware.update_time = datetime.now()
            
            session.commit()
            
            logger.info(f"使用记录删除成功: {record_id} by {operator_name}")
            return create_response(True, '使用记录删除成功')
            
    except Exception as e:
        logger.error(f"删除使用记录失败: {str(e)}")
        return handle_db_error(e) 