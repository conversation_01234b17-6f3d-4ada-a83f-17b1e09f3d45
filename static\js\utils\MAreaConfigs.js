/**
 * M区测试映射配置
 * 每个产品类型定义其M区地址与测试项目的映射关系
 * 基于CPUControllerVue.js中实际的testItems数组：
 * [0]RS485_通信 [1]RS232通信 [2]CANbus通信 [3]EtherCAT通信 [4]Backplane Bus通信
 * 
 * 核心规则：
 * - RS485_通信测试项目使用多M区组合判断（AND模式）
 * - 其他通信测试项目使用单M区映射
 * - 配置支持两种模式：单M区(mIndex)和多M区组合(mIndices+combineMode)
 */
const M_AREA_CONFIGS = {
    // All配置：RS485_通信(M0+M1)，其他单M区映射 + 视觉检测项目
    'all_config': {
        name: 'All配置',
        description: 'RS485双路+RS232+CANbus+EtherCAT+Backplane+视觉检测',
        mAreaMapping: [
            // 通信测试项目 (自动测试覆盖)
            { 
                testIndex: 0, 
                mIndices: [0, 1], 
                testName: 'RS485_通信', 
                testCode: 'rs485_1',
                combineMode: 'AND',
                description: 'RS485_1(M0)和RS485_2(M1)都必须为1'
            },
            { testIndex: 1, mIndex: 2, testName: 'RS232通信', testCode: 'rs232' },
            { testIndex: 2, mIndex: 3, testName: 'CANbus通信', testCode: 'canbus' },
            { testIndex: 3, mIndex: 4, testName: 'EtherCAT通信', testCode: 'ethercat' },
            { testIndex: 4, mIndex: 5, testName: 'Backplane Bus通信', testCode: 'backplane_bus' },
            // 视觉检测项目 (仅视觉检测覆盖)
            { testIndex: 5, mIndex: 10, testName: 'Body I/O输入输出', testCode: 'body_io', visualOnly: true },
            { testIndex: 6, mIndices: [12, 13], testName: 'LED数码管', testCode: 'led_tube', visualOnly: true, 
              description: '需要M12和M13都为1', combineMode: 'AND' },
            { testIndex: 7, mIndex: 10, testName: 'LED灯珠', testCode: 'led_bulb', visualOnly: true },
            { testIndex: 12, mIndex: 11, testName: '拨码开关', testCode: 'dip_switch', visualOnly: true }
        ],
        mAreaRange: { start: 0, end: 13 },
        controlledTestCount: 5,  // 只计算自动测试控制的项目
        visualTestCount: 4       // 新增：视觉检测控制的项目数量
    },

    // 高速IO配置：RS485_通信(M0+M1+M2)，其他单M区映射 + 视觉检测项目
    'high_speed_io': {
        name: '高速IO配置',
        description: 'RS485三路+RS232+CANbus+EtherCAT+Backplane+视觉检测',
        mAreaMapping: [
            // 通信测试项目 (自动测试覆盖)
            { 
                testIndex: 0, 
                mIndices: [0, 1, 2], 
                testName: 'RS485_通信', 
                testCode: 'rs485_1',
                combineMode: 'AND',
                description: 'RS485_1(M0)、RS485_2(M1)、RS485_3(M2)都必须为1'
            },
            { testIndex: 1, mIndex: 3, testName: 'RS232通信', testCode: 'rs232' },
            { testIndex: 2, mIndex: 4, testName: 'CANbus通信', testCode: 'canbus' },
            { testIndex: 3, mIndex: 5, testName: 'EtherCAT通信', testCode: 'ethercat' },
            { testIndex: 4, mIndex: 6, testName: 'Backplane Bus通信', testCode: 'backplane_bus' },
            // 视觉检测项目 (仅视觉检测覆盖)
            { testIndex: 5, mIndex: 10, testName: 'Body I/O输入输出', testCode: 'body_io', visualOnly: true },
            { testIndex: 6, mIndices: [12, 13], testName: 'LED数码管', testCode: 'led_tube', visualOnly: true, 
              description: '需要M12和M13都为1', combineMode: 'AND' },
            { testIndex: 7, mIndex: 10, testName: 'LED灯珠', testCode: 'led_bulb', visualOnly: true },
            { testIndex: 12, mIndex: 11, testName: '拨码开关', testCode: 'dip_switch', visualOnly: true }
        ],
        mAreaRange: { start: 0, end: 13 },
        controlledTestCount: 5,  // 只计算自动测试控制的项目
        visualTestCount: 4       // 新增：视觉检测控制的项目数量
    },

    // 冗余型配置：不使用M区判断（按原有逻辑保持空映射或最小映射）
    'redundant_type': {
        name: '冗余型配置',
        description: '仅背板通信测试，不使用M区判断',
        mAreaMapping: [
            // 冗余型不使用M区判断，保留空映射或移除此配置
        ],
        mAreaRange: { start: 0, end: 0 },
        controlledTestCount: 0
    },

    // 201有输入输出配置：RS485_通信(M0+M1+M2+M3)，其他单M区映射
    'type_201_with_io': {
        name: '201有输入输出配置',
        description: 'RS485四路+CANbus+Backplane',
        mAreaMapping: [
            { 
                testIndex: 0, 
                mIndices: [0, 1, 2, 3], 
                testName: 'RS485_通信', 
                testCode: 'rs485_1',
                combineMode: 'AND',
                description: 'RS485_1(M0)、RS485_2(M1)、RS485_3(M2)、RS485_4(M3)都必须为1'
            },
            { testIndex: 2, mIndex: 4, testName: 'CANbus通信', testCode: 'canbus' },
            { testIndex: 4, mIndex: 5, testName: 'Backplane Bus通信', testCode: 'backplane_bus' }
        ],
        mAreaRange: { start: 0, end: 5 },
        controlledTestCount: 3
    },

    // 201无输入输出配置：RS485_通信(M0+M1+M2+M3)，CANbus单M区映射
    'type_201_no_io': {
        name: '201无输入输出配置',
        description: 'RS485四路+CANbus',
        mAreaMapping: [
            { 
                testIndex: 0, 
                mIndices: [0, 1, 2, 3], 
                testName: 'RS485_通信', 
                testCode: 'rs485_1',
                combineMode: 'AND',
                description: 'RS485_1(M0)、RS485_2(M1)、RS485_3(M2)、RS485_4(M3)都必须为1'
            },
            { testIndex: 2, mIndex: 4, testName: 'CANbus通信', testCode: 'canbus' }
        ],
        mAreaRange: { start: 0, end: 4 },
        controlledTestCount: 2
    },

    // 201五通信配置：RS485_通信(M0+M1+M2+M3)，其他单M区映射（移除CANbus通信）
    'type_201_five_comm': {
        name: '201五通信配置',
        description: 'RS485四路+RS232+Backplane',
        mAreaMapping: [
            {
                testIndex: 0,
                mIndices: [0, 1, 2, 3],
                testName: 'RS485_通信',
                testCode: 'rs485_1',
                combineMode: 'AND',
                description: 'RS485_1(M0)、RS485_2(M1)、RS485_3(M2)、RS485_4(M3)都必须为1'
            },
            { testIndex: 1, mIndex: 4, testName: 'RS232通信', testCode: 'rs232' },
            { testIndex: 4, mIndex: 5, testName: 'Backplane Bus通信', testCode: 'backplane_bus' }
        ],
        mAreaRange: { start: 0, end: 5 },
        controlledTestCount: 3
    }
};

/**
 * 获取所有可用的产品类型列表
 * @returns {Array} 产品类型列表
 */
const getAvailableProductTypes = () => {
    return Object.keys(M_AREA_CONFIGS).map(key => ({
        key,
        name: M_AREA_CONFIGS[key].name,
        description: M_AREA_CONFIGS[key].description,
        controlledTestCount: M_AREA_CONFIGS[key].controlledTestCount
    }));
};

/**
 * 验证M区配置的基本完整性（支持多M区组合）
 * @param {string} configKey - 配置键名
 * @returns {boolean} 是否有效
 */
const isValidMAreaConfig = (configKey) => {
    const config = M_AREA_CONFIGS[configKey];
    if (!config || 
        !Array.isArray(config.mAreaMapping) || 
        typeof config.mAreaRange !== 'object' ||
        typeof config.controlledTestCount !== 'number') {
        return false;
    }
    
    // 验证每个映射项的完整性
    return config.mAreaMapping.every(item => {
        // 必须有testIndex和testName
        if (typeof item.testIndex !== 'number' || !item.testName) {
            return false;
        }
        
        // 单M区模式：必须有mIndex
        if (item.mIndex !== undefined) {
            return typeof item.mIndex === 'number' && !item.mIndices;
        }
        
        // 多M区模式：必须有mIndices和combineMode（只支持AND模式）
        if (item.mIndices !== undefined) {
            return Array.isArray(item.mIndices) && 
                   item.mIndices.length > 0 && 
                   item.combineMode === 'AND' &&
                   !item.mIndex;
        }
        
        return false;
    });
};

// 导出配置和工具函数
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        M_AREA_CONFIGS,
        getAvailableProductTypes,
        isValidMAreaConfig
    };
} else {
    // 浏览器环境
    window.M_AREA_CONFIGS = M_AREA_CONFIGS;
    window.getAvailableProductTypes = getAvailableProductTypes;
    window.isValidMAreaConfig = isValidMAreaConfig;
}

// 配置加载完成日志
if (typeof Logger !== 'undefined') {
    Logger.info('[MAreaConfigs] M区映射配置已加载:', Object.keys(M_AREA_CONFIGS).length + '个产品类型');
}
