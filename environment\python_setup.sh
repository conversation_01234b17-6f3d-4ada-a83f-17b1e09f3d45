#!/bin/bash

# 加载配置
source "$(dirname "$0")/config.sh"

echo "开始配置Python环境..."
check_root

# 创建虚拟环境
echo "创建虚拟环境..."
"${PYTHON_BIN}" -m venv "${VENV_PATH}"
check_status "虚拟环境创建失败"

# 激活虚拟环境并安装依赖
echo "安装Python依赖..."
source "${VENV_PATH}/bin/activate"

# 升级pip
pip install --upgrade pip
check_status "pip升级失败"

# 安装 Gunicorn
echo "安装 Gunicorn..."
pip install --no-cache-dir gunicorn gevent
check_status "Gunicorn 安装失败"

# 在安装项目依赖之前添加验证
verify_python_environment() {
    echo "验证Python环境..."
    if ! command -v pkg-config >/dev/null 2>&1; then
        echo "错误: pkg-config 未安装"
        exit 1
    fi
    
    if ! pkg-config --libs mysqlclient >/dev/null 2>&1; then
        echo "错误: MySQL开发库未安装"
        exit 1
    fi
}

verify_python_environment

# 安装基础包
pip install --no-cache-dir wheel setuptools
check_status "基础包安装失败"

# 安装项目依赖
echo "安装项目依赖..."
pip install --no-cache-dir -r "${PROJECT_PATH}/requirements.txt"
check_status "依赖安装失败"

# 验证关键包的安装
verify_packages() {
    echo "验证包安装..."
    python3 -c "import pymysql; import flask; import sqlalchemy"
    check_status "关键包验证失败"
}

verify_packages

# 设置权限
echo "设置Python环境权限..."
chown -R "${APP_USER}:${APP_GROUP}" "${VENV_PATH}"
check_status "权限设置失败"

echo "Python环境配置完成！" 