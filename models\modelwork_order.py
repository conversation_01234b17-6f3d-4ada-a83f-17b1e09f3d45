from sqlalchemy import Column, Integer, String, DateTime, func, Boolean
from database.db_manager import Base
from datetime import datetime
from sqlalchemy.sql import text
import logging

logger = logging.getLogger(__name__)

class WorkOrder(Base):
    __tablename__ = 'workordermanagement'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    dbord_creator = Column(String(20), nullable=False)
    dbord_createTime = Column(DateTime, default=datetime.now)
    dbord_processOrderNo = Column(String(60), nullable=False, default='N/A')
    dbord_productionQuantity = Column(Integer, nullable=False)
    dbord_productCode = Column(String(60), nullable=False, default='N/A')
    dbord_productType = Column(String(60), nullable=False, default='N/A')
    dbord_productModel = Column(String(60), nullable=False, default='N/A')
    dbord_customerName = Column(String(100), nullable=True)
    orderproduct_type_id = Column(Integer, nullable=True, default=2)
    dbord_remark = Column(String(252), default='N/A')
    dbord_probatch = Column(String(60), nullable=False, default='N/A')
    dbord_snlenth = Column(Integer, nullable=True)
    dbord_requires_pcba_check = Column(Boolean, default=True)
    dbord_completed_flag = Column(Boolean, default=False)
    
    def get_production_status(self, session):
        """获取工单生产状态信息"""
        try:
            # 首先检查完成标记
            if self.dbord_completed_flag:
                # 获取实际完成数量，判断是否为手动标记
                order_no = self.dbord_processOrderNo
                target_quantity = self.dbord_productionQuantity
                
                completed_count = session.execute(
                    text("""
                        SELECT (
                            (SELECT COUNT(*) FROM coupler_table 
                             WHERE work_order = :order_no 
                             AND comparison_result = 'pass')
                            +
                            (SELECT COUNT(*) FROM cpu_table 
                             WHERE work_order = :order_no 
                             AND comparison_result = 'pass')
                        ) as total
                    """), 
                    {'order_no': order_no}
                ).scalar() or 0
                
                # 确定是否为手动标记完成
                manually_completed = completed_count < target_quantity
                
                return {
                    'status': '已完成',
                    'assembled': self.dbord_productionQuantity,
                    'tested': self.dbord_productionQuantity,
                    'packaged': completed_count,  # 使用实际数量
                    'target': self.dbord_productionQuantity,
                    'manually_completed': manually_completed  # 添加手动标记信息
                }

            order_no = self.dbord_processOrderNo
            target_quantity = self.dbord_productionQuantity

            # 统计已包装数量 - 使用 COUNT(*) 直接统计，不需要 UNION
            completed_count = session.execute(
                text("""
                    SELECT (
                        (SELECT COUNT(*) FROM coupler_table 
                         WHERE work_order = :order_no 
                         AND comparison_result = 'pass')
                        +
                        (SELECT COUNT(*) FROM cpu_table 
                         WHERE work_order = :order_no 
                         AND comparison_result = 'pass')
                    ) as total
                """), 
                {'order_no': order_no}
            ).scalar() or 0

            # 检查工单号是否存在
            exists_check = session.execute(
                text("""
                    SELECT EXISTS (
                        SELECT 1 FROM coupler_table WHERE work_order = :order_no
                        UNION
                        SELECT 1 FROM cpu_table WHERE work_order = :order_no
                        UNION
                        SELECT 1 FROM complete_products WHERE complete_processing_order = :order_no
                    )
                """),
                {'order_no': order_no}
            ).scalar()

            if not exists_check:
                return {
                    'status': '生产中',
                    'assembled': 0,
                    'tested': 0,
                    'packaged': 0,
                    'target': target_quantity
                }

            # 获取已组装数量
            assembled_count = session.execute(
                text("""
                    SELECT COUNT(*) 
                    FROM complete_products 
                    WHERE complete_processing_order = :order_no
                """),
                {'order_no': order_no}
            ).scalar() or 0

            # 获取已测试数量 - 直接统计数量
            tested_count = session.execute(
                text("""
                    SELECT (
                        (SELECT COUNT(*) FROM coupler_table 
                         WHERE work_order = :order_no)
                        +
                        (SELECT COUNT(*) FROM cpu_table 
                         WHERE work_order = :order_no)
                    ) as total
                """),
                {'order_no': order_no}
            ).scalar() or 0

            # 判断状态并更新完成标记
            if completed_count >= target_quantity:
                # 更新完成标记
                self.dbord_completed_flag = True
                session.commit()
                status = '已完成'
            else:
                status = '生产中'

            return {
                'status': status,
                'assembled': assembled_count,
                'tested': tested_count,
                'packaged': completed_count,
                'target': target_quantity
            }
        except Exception as e:
            logger.error(f"Error getting production status: {str(e)}")
            return {
                'status': '生产中',
                'assembled': 0,
                'tested': 0,
                'packaged': 0,
                'target': target_quantity
            }

    def to_dict(self):
        result = {
            'id': self.id,
            'ord_creator': self.dbord_creator,
            'ord_createTime': self.dbord_createTime.strftime('%Y-%m-%d %H:%M:%S') if self.dbord_createTime else None,
            'ord_processOrderNo': self.dbord_processOrderNo,
            'ord_productionQuantity': self.dbord_productionQuantity,
            'ord_productCode': self.dbord_productCode,
            'ord_productType': self.dbord_productType,
            'ord_productModel': self.dbord_productModel,
            'ord_customerName': self.dbord_customerName,
            'orderproduct_type_id': self.orderproduct_type_id,
            'ord_remark': self.dbord_remark,
            'ord_probatch': self.dbord_probatch,
            'ord_snlenth': self.dbord_snlenth,
            'ord_requires_pcba_check': self.dbord_requires_pcba_check,
            'ord_completed_flag': self.dbord_completed_flag
        }
        return result 