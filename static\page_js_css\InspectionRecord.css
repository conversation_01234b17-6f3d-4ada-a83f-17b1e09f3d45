/* 检验记录查询样式 */
:root {
    /* 字体大小 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    
    /* 颜色 */
    --color-primary: #1976D2;
    --color-primary-light: #2196F3;
    --color-primary-dark: #1565C0;
    --color-text-primary: #1a1f36;
    --color-text-secondary: #606266;
    --color-text-tertiary: #909399;
    --color-border: #dcdfe6;
    --color-background: #f5f7fa;
    --color-white: #ffffff;
    --color-success: #67c23a;
    --color-warning: #e6a23c;
    --color-danger: #f56c6c;
    --color-info: #909399;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    
    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

    /* 新增或确认：阶段标题背景色变量 */
    --inspection-record-assembly-header-bg: #e9f2fa; /* 浅蓝 */
    --inspection-record-test-header-bg: #fff4e6;    /* 浅橙 */
    --inspection-record-packaging-header-bg: #e6f7ec; /* 浅绿 */

    /* 新增：角色区域背景色变量 */
    --inspection-record-role-first-bg: #f0f2f5;  /* 稍深一点的灰色，用于首检 */
    --inspection-record-role-self-bg: #f8f9fa;   /* 更浅的灰色或接近白色，用于自检 */
    --inspection-record-role-ipqc-bg: #f0f2f5;   /* 稍深一点的灰色，用于IPQC */
}

/* 基础容器样式 */
.inspection-record {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: var(--spacing-xl);
    background-color: var(--color-white);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    color: var(--color-text-primary);
}

/* 标题样式 */
.inspection-record__title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    position: relative;
    letter-spacing: 0.5px;
}

.inspection-record__title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, 
        rgba(26, 31, 54, 0.8) 0%,
        rgba(26, 31, 54, 0.6) 50%,
        rgba(26, 31, 54, 0.2) 100%
    );
}

.inspection-record__subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

/* 查询头部布局 */
.inspection-record__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

/* 查询类型区域 */
.inspection-record__type {
    flex: 1;
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

/* 查询类型选项 */
.inspection-record__type-options {
    display: flex;
    gap: var(--spacing-xl);
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
}

.inspection-record__type-option {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.inspection-record__type-option:hover {
    color: var(--color-primary);
}

.inspection-record__type-radio {
    margin-right: var(--spacing-sm);
    cursor: pointer;
}

/* 查询类型文本字体大小 */  
.inspection-record__type-text {
    white-space: nowrap;
    font-size: var(--font-size-base);
}

/* 高级筛选按钮 */
.inspection-record__filter {
    margin-left: 20px;
    display: flex;
    align-items: center;
}

.inspection-record__filter-btn {
    padding: 8px 16px;
    background-color: var(--color-white);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    height: 32px;
    display: flex;
    align-items: center;
}

.inspection-record__filter-btn:hover {
    background-color: var(--color-background);
    border-color: var(--color-primary-light);
    color: var(--color-primary);
}

.inspection-record__filter-text {
    font-size: 14px;
    margin-right: 8px;
}

.inspection-record__filter-icon {
    font-size: 12px;
    transition: transform 0.3s;
}

.inspection-record__filter-btn.active .inspection-record__filter-icon {
    transform: rotate(180deg);
}

/* 高级筛选展开区域 */
.inspection-record__advanced-filter {
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.inspection-record__date-range {
    display: flex;
    gap: 30px;
    align-items: center;
}

.inspection-record__date-field {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
    background: var(--color-white);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
}

/* 高级筛选日期标签 */
.inspection-record__date-label {
    color: var(--color-text-secondary);
    font-weight: 500;
    white-space: nowrap;
    min-width: 70px;
    font-size: var(--font-size-sm);
}

.inspection-record__date-input {
    flex: 1;
    height: 36px;
    padding: 0 12px;
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    background-color: #fff;
    transition: all 0.3s ease;
}

.inspection-record__date-input:hover {
    border-color: var(--color-primary-light);
}

.inspection-record__date-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* 查询表单样式 */
.inspection-record__form {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-end;
}

/* 表单组样式 */
.inspection-record__form-group {
    flex: 1;
    min-width: 200px;
    position: relative;
}

/* 按钮容器 */
.inspection-record__buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 标签样式 */
.inspection-record__label {
    display: block;
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
    font-weight: 500;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

/* 输入框基础样式 */
.inspection-record__input {
    width: 100%;
    height: 40px;
    padding: 0 var(--spacing-md);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    line-height: 2.5rem;
    color: var(--color-text-primary);
    background-color: #fff;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.inspection-record__input:hover {
    border-color: var(--color-primary-light);
}

.inspection-record__input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.inspection-record__input::placeholder {
    color: var(--color-text-secondary);
}

/* 按钮样式 */
.inspection-record__btn {
    height: 40px;
    padding: 0 var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid transparent;
}

.inspection-record__btn--search {
    background-color: var(--color-primary);
    color: var(--color-white);
}

.inspection-record__btn--search:hover {
    background-color: var(--color-primary-dark);
}

.inspection-record__btn--reset {
    background-color: var(--color-white);
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border);
}

.inspection-record__btn--reset:hover {
    background-color: var(--color-background);
    border-color: var(--color-text-secondary);
}

.inspection-record__btn i {
    margin-right: 5px;
}

/* 导出按钮样式 */
.inspection-record__btn--export {
    background-color: #67c23a; /* 绿色背景 */
    color: #ffffff; /* 白色文字 */
}

.inspection-record__btn--export:hover {
    background-color: #5daf34; /* 鼠标悬停时深一点的绿色 */
    color: #ffffff;
}

/* 复选框样式 (如果需要，可以调整或添加更多特定样式) */
.inspection-record__checkbox {
    width: 16px; 
    height: 16px;
    cursor: pointer;
    vertical-align: middle; /* 确保与其他表头内容对齐 */
}

/* 结果表格样式 */
.inspection-record__results {
    margin-top: 30px;
}

.inspection-record__table-container {
    position: relative;
    overflow-x: auto !important;
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
}

.inspection-record__table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
    table-layout: fixed;
    min-width: 1200px;
}

.inspection-record__table th {
    background-color: var(--color-background);
    color: var(--color-text-primary);
    font-weight: 500;
    text-align: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    white-space: nowrap;
}

.inspection-record__table td {
    padding: var(--spacing-md) var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
    color: var(--color-text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}


.inspection-record__table tr:hover {
    background-color: rgba(33, 150, 243, 0.05);
}

.inspection-record__table tr:last-child td {
    border-bottom: none;
}

/* 状态标签 */
.inspection-record__status {
    display: inline-block;
    padding: 2px 10px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    line-height: 1.5;
    text-align: center;
    min-width: 60px;
}

.inspection-record__status--completed {
    background-color: rgba(103, 194, 58, 0.1);
    color: var(--color-success);
}

.inspection-record__status--in-progress {
    background-color: rgba(230, 162, 60, 0.1);
    color: var(--color-warning);
}

.inspection-record__status--pending {
    background-color: rgba(144, 147, 153, 0.1);
    color: var(--color-info);
}

/* 操作按钮 */
.inspection-record__action-btn {
    color: var(--color-primary);
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-sm);
    padding: 4px 8px;
    transition: all 0.3s;
    border-radius: var(--radius-sm);
}

.inspection-record__action-btn:hover {
    background-color: rgba(33, 150, 243, 0.1);
}

/* 分页样式 */
.inspection-record__pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: var(--spacing-md) 0;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.inspection-record__pagination-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.inspection-record__page-size {
    width: 70px;
    height: 32px;
    padding: 0 var(--spacing-sm);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    color: var(--color-text-secondary);
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.inspection-record__page-size:hover {
    border-color: var(--color-primary-light);
}

.inspection-record__total {
    margin-left: 8px;
}

.inspection-record__pagination-controls {
    display: flex;
    align-items: center;
    gap: 4px;
}

.inspection-record__pagination-btn {
    min-width: 32px;
    height: 32px;
    padding: 0 4px;
    border: 1px solid var(--color-border);
    background-color: #fff;
    color: var(--color-text-secondary);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: var(--font-size-sm);
}

.inspection-record__pagination-btn:hover:not(.disabled) {
    color: var(--color-primary);
    border-color: var(--color-primary-light);
    background-color: rgba(33, 150, 243, 0.1);
}

.inspection-record__pagination-btn.disabled {
    color: var(--color-text-tertiary);
    cursor: not-allowed;
    background-color: var(--color-background);
}

.inspection-record__pagination-btn.active {
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

/* 无数据提示 */
.inspection-record__no-data {
    text-align: center;
    padding: 40px 0;
    color: var(--color-text-tertiary);
    font-size: var(--font-size-sm);
}

/* 表格加载指示器样式 */
.table-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--color-border);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 详情模态框样式 */
.inspection-record__detail-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    padding-top: 20px;
    padding-bottom: 20px;
}

.inspection-record__detail-content {
    position: relative;
    width: 80%;
    max-width: 1000px;
    margin: 0 auto;
    background-color: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.inspection-record__detail-close {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 20px;
    color: var(--color-text-tertiary);
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
}

.inspection-record__detail-close:hover {
    color: var(--color-text-primary);
}

.inspection-record__detail-header {
    margin-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--color-border);
    padding-bottom: var(--spacing-md);
}

.inspection-record__detail-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    display: block;
    margin-bottom: var(--spacing-xs);
}

.inspection-record__detail-subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    display: block;
    margin-bottom: var(--spacing-md);
}

/* 新增：导出图片按钮样式 */
.inspection-record__btn--export-image {
    position: absolute;
    top: 20px;
    right: 90px;
    padding: 5px 10px;
    font-size: var(--font-size-xs);
    height: auto;
}

.inspection-record__btn--export-image i {
    margin-right: 4px;
}

/* 检验阶段样式 */
.inspection-record__stages {
    margin-bottom: var(--spacing-xl);
}

.inspection-record__stage {
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.inspection-record__stage-header {
    background-color: var(--color-background);
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.inspection-record__stage-title {
    font-weight: 500;
    display: flex;
    align-items: center;
}

.inspection-record__stage-icon {
    margin-right: var(--spacing-sm);
    transition: transform 0.3s;
}

.inspection-record__stage.collapsed .inspection-record__stage-icon {
    transform: rotate(-90deg);
}

.inspection-record__stage-body {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--color-border);
}

.inspection-record__stage.collapsed .inspection-record__stage-body {
    display: none;
}

/* 新增：为不同类型的阶段标题设置背景色 */
.inspection-record__stage--type-assembly .inspection-record__stage-header {
    background-color: var(--inspection-record-assembly-header-bg);
}
.inspection-record__stage--type-test .inspection-record__stage-header {
    background-color: var(--inspection-record-test-header-bg);
}
.inspection-record__stage--type-packaging .inspection-record__stage-header {
    background-color: var(--inspection-record-packaging-header-bg);
}

/* 角色区域基础样式调整 */
.inspection-record__role-section {
    padding: var(--spacing-md);
}

/* 新增：为不同类型的角色区域设置背景色 */
.inspection-record__role-section--role-first {
    background-color: var(--inspection-record-role-first-bg);
}
.inspection-record__role-section--role-self {
    background-color: var(--inspection-record-role-self-bg);
}
.inspection-record__role-section--role-ipqc {
    background-color: var(--inspection-record-role-ipqc-bg);
}

/* 新增：角色内的信息容器 */
.inspection-record__role-info-container {
    border: 1px solid #dee2e6;
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border-radius: var(--radius-sm);
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.inspection-record__info-item-inline {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-right: var(--spacing-lg);
}

.inspection-record__info-item-inline:last-child {
    margin-right: 0;
}

/* 检验项目表格 */
.inspection-record__items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-lg);
}

.inspection-record__items-table th {
    background-color: var(--color-background);
    padding: var(--spacing-sm);
    text-align: left;
    font-weight: 500;
    border-bottom: 1px solid var(--color-border);
}

.inspection-record__items-table td {
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
}

.inspection-record__items-table tr:last-child td {
    border-bottom: none;
}

/* 检验信息区域 */
.inspection-record__info-area {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.inspection-record__info-item {
    background-color: var(--color-background);
    border-radius: var(--radius-sm);
    padding: var(--spacing-md);
    flex: 1;
    min-width: 200px;
}

.inspection-record__info-label {
    color: var(--color-text-secondary);
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-xs);
}

.inspection-record__info-value {
    color: var(--color-text-primary);
    font-weight: 500;
}

/* 附件区域 */
.inspection-record__attachments {
    margin-top: var(--spacing-xl);
}

.inspection-record__attachments-title {
    font-weight: 500;
    margin-bottom: var(--spacing-md);
    color: var(--color-text-primary);
    display: block;
}

.inspection-record__attachment-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.inspection-record__attachment-item {
    background-color: var(--color-background);
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
    width: calc(50% - var(--spacing-sm));
    box-sizing: border-box;
    margin-bottom: var(--spacing-md);
}

.inspection-record__attachment-item:hover {
    background-color: rgba(33, 150, 243, 0.1);
}

.inspection-record__attachment-icon {
    font-size: 24px;
    color: var(--color-text-tertiary);
    margin-right: var(--spacing-md);
}

/* 新增：附件详情容器 */
.inspection-record__attachment-details {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
}

.inspection-record__attachment-name {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: var(--spacing-xs);
}

/* 新增：附件元信息（大小、时间、上传人） */
.inspection-record__attachment-meta {
    font-size: var(--font-size-xs);
    color: var(--color-text-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .inspection-record {
        padding: var(--spacing-md);
    }
    
    .inspection-record__header {
        flex-direction: column;
    }
    
    .inspection-record__filter {
        margin-left: 0;
        margin-top: var(--spacing-md);
        width: 100%;
    }
    
    .inspection-record__filter-btn {
        width: 100%;
        justify-content: center;
    }
    
    .inspection-record__date-range {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .inspection-record__date-field {
        width: 100%;
    }
    
    .inspection-record__form {
        flex-direction: column;
    }
    
    .inspection-record__form-group {
        width: 100%;
    }
    
    .inspection-record__type-options {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }
    
    .inspection-record__type-option {
        margin-right: var(--spacing-md);
    }
    
    .inspection-record__pagination {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .inspection-record__pagination-info {
        width: 100%;
        justify-content: flex-start;
    }
    
    .inspection-record__pagination-controls {
        width: 100%;
        justify-content: center;
    }
    
    .inspection-record__detail-content {
        width: 95%;
        margin: 20px auto;
        padding: var(--spacing-md);
    }
}
