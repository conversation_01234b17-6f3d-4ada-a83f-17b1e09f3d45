-- 创建数据库（如果不存在）20250512最新表结构
CREATE DATABASE IF NOT EXISTS kmlc_plc DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE kmlc_plc;

-- 产品类型表
CREATE TABLE product_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_code VARCHAR(20) NOT NULL UNIQUE COMMENT '产品类型代码',
    type_name VARCHAR(50) NOT NULL COMMENT '产品类型名称',
    description VARCHAR(200) COMMENT '产品描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品类型表';

-- 工单表
CREATE TABLE quality_inspection_work_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    work_order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '工单号',
    product_type_id INT NOT NULL COMMENT '产品类型ID',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '工单状态：pending-待处理/processing-处理中/completed-已完成',
    is_rework BOOLEAN DEFAULT FALSE COMMENT '是否为返工工单',
    assembly_stage_completed TINYINT(1) DEFAULT 0 COMMENT '组装前阶段是否完成：0-未完成/1-完成',
    test_stage_completed TINYINT(1) DEFAULT 0 COMMENT '测试前阶段是否完成：0-未完成/1-完成',
    packaging_stage_completed TINYINT(1) DEFAULT 0 COMMENT '包装前阶段是否完成：0-未完成/1-完成',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (product_type_id) REFERENCES product_types(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='质检工单表';

-- 产品表（新增）
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    work_order_id INT NOT NULL COMMENT '工单ID',
    serial_no VARCHAR(50) NOT NULL COMMENT 'SN号',
    is_rework BOOLEAN DEFAULT FALSE COMMENT '是否为返工产品',
    original_product_id INT NULL COMMENT '原始产品ID（如果是返工产品）',
    original_work_order_id INT NULL COMMENT '原始工单ID（如果是返工产品）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (work_order_id) REFERENCES quality_inspection_work_orders(id),
    UNIQUE KEY uk_work_order_serial_no (work_order_id, serial_no) COMMENT '确保同一工单内SN号唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品表';

-- 检验项目表
CREATE TABLE inspection_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_type_id INT NOT NULL COMMENT '产品类型ID',
    stage VARCHAR(20) NOT NULL COMMENT '检验阶段：assembly-组装前/test-测试前/packaging-包装前',
    item_no INT NOT NULL COMMENT '项目编号',
    item_name VARCHAR(200) NOT NULL COMMENT '项目名称',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必检项',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (product_type_id) REFERENCES product_types(id),
    UNIQUE KEY uk_product_stage_no (product_type_id, stage, item_no) COMMENT '确保每个产品每个阶段的项目编号唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='检验项目表';

-- 产品检验状态表（新表，替代原来的检验记录表、阶段完成表和提交记录表）
CREATE TABLE product_inspection_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    work_order_id INT NOT NULL COMMENT '工单ID',
    product_id INT NOT NULL COMMENT '产品ID',
    stage VARCHAR(20) NOT NULL COMMENT '检验阶段：assembly-组装前/test-测试前/packaging-包装前',
    inspector_role VARCHAR(20) NOT NULL COMMENT '检验人员角色：first-首检/self-自检/ipqc-IPQC',
    is_passed BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否通过',
    inspector VARCHAR(50) NOT NULL COMMENT '检验人',
    inspection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '检验时间',
    submission_type ENUM('partial', 'final') NOT NULL DEFAULT 'final' COMMENT '提交类型：partial-部分提交，final-最终提交',
    FOREIGN KEY (work_order_id) REFERENCES quality_inspection_work_orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    UNIQUE KEY uk_product_inspection (product_id, stage, inspector_role) COMMENT '确保每个产品在每个阶段每个角色只有一条记录'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品检验状态表';

-- 附件表
CREATE TABLE inspection_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    work_order_id INT NOT NULL COMMENT '工单ID',
    stage VARCHAR(20) NOT NULL COMMENT '检验阶段：assembly-组装前/test-测试前/packaging-包装前',
    inspector_role VARCHAR(20) NOT NULL COMMENT '检验人员角色：first-首检/self-自检/ipqc-IPQC',
    file_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(255) NOT NULL COMMENT '存储路径',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
    file_size INT NOT NULL COMMENT '文件大小(字节)',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    uploaded_by VARCHAR(50) NOT NULL COMMENT '上传人',
    FOREIGN KEY (work_order_id) REFERENCES quality_inspection_work_orders(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='检验附件表';

-- 创建索引优化查询性能
CREATE INDEX idx_qiw_product_type ON quality_inspection_work_orders(product_type_id);
CREATE INDEX idx_qiw_status ON quality_inspection_work_orders(status);
CREATE INDEX idx_qiw_is_rework ON quality_inspection_work_orders(is_rework) COMMENT '返工工单查询索引';
CREATE INDEX idx_inspection_items_product_stage ON inspection_items(product_type_id, stage, display_order);
CREATE INDEX idx_attachments_work_order ON inspection_attachments(work_order_id);
CREATE INDEX idx_attachments_stage_role ON inspection_attachments(stage, inspector_role);

-- 产品检验状态表索引
CREATE INDEX idx_pis_work_order ON product_inspection_status(work_order_id);
CREATE INDEX idx_pis_product ON product_inspection_status(product_id);
CREATE INDEX idx_pis_stage_role ON product_inspection_status(stage, inspector_role);
CREATE INDEX idx_pis_inspection_time ON product_inspection_status(inspection_time);
CREATE INDEX idx_pis_submission_type ON product_inspection_status(submission_type) COMMENT '提交类型查询索引';

-- 产品追踪相关索引
CREATE INDEX idx_products_work_order ON products(work_order_id);
CREATE INDEX idx_products_serial_no ON products(serial_no) COMMENT 'SN号查询索引';
CREATE INDEX idx_products_is_rework ON products(is_rework) COMMENT '返工产品查询索引';
CREATE INDEX idx_products_serial_no_created_at ON products(serial_no, created_at DESC) COMMENT '查询SN号最新记录索引';
CREATE INDEX idx_products_original_product ON products(original_product_id) COMMENT '原始产品查询索引';
CREATE INDEX idx_products_original_work_order ON products(original_work_order_id) COMMENT '原始工单查询索引';

-- 插入初始数据：产品类型 (假设 ID 1 是五代, ID 2 是四代, 与后面的项目插入对应)
INSERT INTO product_types (id, type_code, type_name, description) VALUES
(1, 'plc5', '五代PLC', '第五代可编程逻辑控制器'),
(2, 'plc4', '四代PLC', '第四代可编程逻辑控制器')
ON DUPLICATE KEY UPDATE type_code=VALUES(type_code), type_name=VALUES(type_name), description=VALUES(description); -- 如果ID已存在则更新，避免重复插入错误

-- 删除可能存在的旧检验项目数据，避免重复插入错误
DELETE FROM inspection_items WHERE product_type_id IN (1, 2);

-- 插入检验项目数据
-- 五代PLC检验项目 (product_type_id = 1)
INSERT INTO inspection_items (product_type_id, stage, item_no, item_name, display_order) VALUES
-- 组装前检验项目
(1, 'assembly', 1, '激光打标外壳型号/打标型号参数正确，符合工单要求', 1),
(1, 'assembly', 2, '激光打标面所有二维码能正常扫码，识别出的内容正确', 2),
(1, 'assembly', 3, '激光打标SN编码正确，不与历史记录重复', 3),
(1, 'assembly', 4, '激光打标SN字符内容与SN二维码内容比对一致', 4),
(1, 'assembly', 5, '外壳打标质量合格，丝印字符清晰，壳体无残缺/污渍', 5),
(1, 'assembly', 6, '覆膜型号、尺寸正确，无残缺/污渍', 6),
(1, 'assembly', 7, '覆膜色彩无差异，字符清晰可辨识', 7),
(1, 'assembly', 8, '灯盖丝印型号、尺寸正确，无残缺/污渍', 8),
(1, 'assembly', 9, '灯盖丝印色彩/透明度无差异，字符清晰可辨识', 9),
(1, 'assembly', 10, 'PCBA电路板型号与BOM一致', 10),
(1, 'assembly', 11, '接地片焊接牢固，无锈迹、氧化或污损', 11),
(1, 'assembly', 12, 'PCBA金手指干净无破损、无氧化、无污渍', 12),
(1, 'assembly', 13, '壳体卡扣全部正常卡住，无断裂脱落', 13),
-- 测试前检验项目
(1, 'test', 1, '数码管/LED能正常点亮', 1),
(1, 'test', 2, '外壳SN与PCBA模组正确绑定', 2),
(1, 'test', 3, '覆膜型号正确，粘贴可靠，无污损', 3),
(1, 'test', 4, '覆膜色彩无差异，字符清晰可辨识', 4),
(1, 'test', 5, '灯盖丝印型号正确，安装可靠，无污损', 5),
(1, 'test', 6, '灯盖丝印色彩/透明度无差异，字符清晰可辨识', 6),
(1, 'test', 7, '外壳锁扣全部正常卡住，无断裂脱落', 7),
(1, 'test', 8, '接地片无污损，正常露出，无卡住阻塞', 8),
(1, 'test', 9, '金手指连接器弹片无污损、变形、氧化', 9),
(1, 'test', 10, '产品整体外观无指纹/污渍，无划痕，无破损', 10),
(1, 'test', 11, '待测试产品标识正确，摆放/周转可靠，不易掉落', 11),
-- 包装前检验项目
(1, 'packaging', 1, '防尘贴/防尘塞/电池仓（电池）等正确粘贴/安装', 1),
(1, 'packaging', 2, '配件包内配件型号、数量正确', 2),
(1, 'packaging', 3, '成品灯盖/丝印/覆膜型号正确，安装粘贴合格', 3),
(1, 'packaging', 4, '金手指连接器弹片无污损、变形、氧化', 4),
(1, 'packaging', 5, '成品整体外观无指纹/污渍，表面无明显划痕', 5),
(1, 'packaging', 6, '蓝色锁扣正常卡住，蓝色滑扣下滑锁住', 6),
(1, 'packaging', 7, '壳体卡扣全部正常卡住，无断裂脱落', 7),
(1, 'packaging', 8, '铭牌标签内容参数正确，打印清晰，粘贴位置正确', 8),
(1, 'packaging', 9, '本工单待包装全部成品、配件检查合格', 9),
(1, 'packaging', 10, '待包装产品标识正确，摆放/周转可靠，不易掉落', 10);

-- 四代PLC检验项目 (product_type_id = 2)
INSERT INTO inspection_items (product_type_id, stage, item_no, item_name, display_order) VALUES
-- 组装前检验项目
(2, 'assembly', 1, '铭牌标签内容参数正确，打印清晰，粘贴位置正确', 1),
(2, 'assembly', 2, '铭牌标签所有二维码能正常扫码，识别出的内容正确', 2),
(2, 'assembly', 3, '铭牌标签SN编码正确，不与历史记录重复', 3),
(2, 'assembly', 4, '铭牌标签SN字符内容与SN二维码内容比对一致', 4),
(2, 'assembly', 5, '壳体丝印字符清晰，无残缺/污渍，无明显色差', 5),
(2, 'assembly', 6, '覆膜型号、尺寸正确，无残缺/污渍', 6),
(2, 'assembly', 7, '覆膜色彩无差异，字符清晰可辨识', 7),
(2, 'assembly', 8, '灯盖丝印型号、尺寸正确，无残缺/污渍', 8),
(2, 'assembly', 9, '灯盖丝印色彩无差异，字符清晰可辨识', 9),
(2, 'assembly', 10, 'PCBA电路板型号与BOM一致', 10),
(2, 'assembly', 11, '接地片焊接牢固，无锈迹、氧化或污损', 11),
(2, 'assembly', 12, '壳体卡扣全部正常卡住，无断裂脱落', 12),
-- 测试前检验项目
(2, 'test', 1, '数码管/LED能正常点亮', 1),
(2, 'test', 2, '外壳SN与PCBA模组正确绑定', 2),
(2, 'test', 3, '覆膜型号正确，粘贴可靠，无污损', 3),
(2, 'test', 4, '覆膜色彩无差异，字符清晰可辨识', 4),
(2, 'test', 5, '灯盖丝印型号正确，安装可靠，无污损', 5),
(2, 'test', 6, '灯盖丝印色彩无差异，字符清晰可辨识', 6),
(2, 'test', 7, '外壳锁扣全部正常卡住，无断裂脱落', 7),
(2, 'test', 8, '接地片无污损，正常露出，无卡住阻塞', 8),
(2, 'test', 9, '模块扩展接口能正常插拔，无堵塞', 9),
(2, 'test', 10, '产品整体外观无指纹/污渍，无划痕，无破损', 10),
(2, 'test', 11, '待测试产品标识正确，摆放/周转可靠，不易掉落', 11),
-- 包装前检验项目
(2, 'packaging', 1, '配件包内配件型号、数量正确', 1),
(2, 'packaging', 2, '产品灯盖/丝印/覆膜型号正确，安装粘贴合格', 2),
(2, 'packaging', 3, '模块扩展接口能正常插拔，无堵塞', 3),
(2, 'packaging', 4, '接地片无锈迹，无污损', 4),
(2, 'packaging', 5, '成品整体外观无指纹/污渍，表面无明显划痕', 5),
(2, 'packaging', 6, '橙色锁扣正常卡住，橙色滑扣在卡槽内，无脱落', 6),
(2, 'packaging', 7, '壳体卡扣全部正常卡住，无断裂脱落', 7),
(2, 'packaging', 8, '铭牌标签内容参数正确，打印清晰，粘贴位置正确', 8),
(2, 'packaging', 9, '本工单待包装全部成品、配件检查合格', 9),
(2, 'packaging', 10, '待包装产品标识正确，摆放/周转可靠，不易掉落', 10);
