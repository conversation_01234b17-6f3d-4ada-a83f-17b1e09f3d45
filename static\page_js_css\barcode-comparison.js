// 使用立即执行函数表达式(IIFE)创建闭包
(function() {
    // 将变量移到闭包内部
    let workOrderData = null;
    let compareTimeout = null;
    let lastComparedRow = null;
    let currentPage = 1;
    const pageSize = 5;  // 每页显示5条记录

    // 添加一个状态管理对象
    let compareStates = new Map(); // 用于存储所有行的比对状态

    // 初始化函数
    function initBarcodeComparison() {
        // 重置数据
        workOrderData = null;
        
        // 初始化页面
        const content = document.getElementById('content');
        content.innerHTML = `
            <main class="barcode-container">
                <div class="barcode-content">
                    <header>
                        <!-- <h1 class="barcode-title">条码比对系统</h1> -->
                    </header>
                    
                    <section class="main-content">
                        <div class="operation-area">
                            <section class="left-panel">
                                <form class="form-group">
                                    <label for="workOrderInput">工单号</label>
                                    <div class="search-box">
                                        <input type="text" id="workOrderInput" placeholder="请输入工单号">
                                        <button type="button" class="btn btn-search" onclick="searchWorkOrder()">
                                            <svg class="icon-search" viewBox="0 0 24 24">
                                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                            </svg>
                                            查询
                                        </button>
                                    </div>
                                    
                                    <div class="form-items-row">
                                        <div class="form-item form-item--barcode1">
                                            <label for="barcode1Input">条码1</label>
                                            <input type="text" id="barcode1Input" placeholder="请输入条码1">
                                        </div>
                                        
                                        <div class="form-item form-item--barcode2">
                                            <label for="barcode2Input">条码2</label>
                                            <input type="text" id="barcode2Input" placeholder="请输入条码2">
                                        </div>
                                        
                                        <div class="form-item form-item--snbytes">
                                            <label for="snBytesInput">产品SN号位数</label>
                                            <input type="number" id="snBytesInput" placeholder="产品SN号位数" min="0">
                                        </div>
                                    </div>
                                </form>

                                <div class="action-row">
                                    <div class="compare-status-card">
                                        <div class="status-icon"></div>
                                        <span class="status-text">等待开始比对...</span>
                                    </div>
                                    
                                    <button class="btn btn-export" onclick="exportData()">
                                        <svg class="icon-export" viewBox="0 0 24 24">
                                            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                                        </svg>
                                        导出
                                    </button>
                                </div>
                            </section>

                            <aside class="operation-guide">
                                <h3>操作说明</h3>
                                <ol>
                                    <li>在"工单查询"标签页中输入工单号并查询。</li>
                                    <li>在"条码比对"标签页中，输入或扫描两个条码。</li>
                                    <li>系统会自动进行比对，并在1秒后给出结果。</li>
                                    <li>比对结果会同时以视觉和语音的方式呈现。</li>
                                    <li>如果条码匹配成功与工单中的SN号一致，系统会更新进度条和完成数量。</li>
                                </ol>
                            </aside>
                        </div>

                        <section class="results-panel">
                            <article class="work-order-info">
                                <h2 class="panel-title">工单信息</h2>
                                <table id="workOrderTable">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>工单号</th>
                                            <th>产品型号</th>
                                            <th>SN号</th>
                                            <th>测试结果</th>
                                            <th>比对结果</th>
                                        </tr>
                                    </thead>
                                    <tbody id="workOrderBody">
                                    </tbody>
                                </table>
                            </article>
                        </section>
                    </section>
                </div>
            </main>
        `;

        // 绑定事件处理程序
        bindEvents();
    }

    async function searchWorkOrder() {
        const workOrderInput = document.getElementById('workOrderInput');
        const workOrderNumber = workOrderInput.value.trim();
        
        if (!workOrderNumber) {
            await SweetAlert.warning('请输入工单号');
            workOrderInput.focus();
            return;
        }

        try {
            // 先获取工单基本信息
            const basicResponse = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(workOrderNumber)}`);
            const basicData = await basicResponse.json();

            if (basicData.success && basicData.order) {
                // 新增判断：只有包装前阶段完成才允许后续操作
                if (!basicData.order.packaging_stage_completed) {
                    await SweetAlert.warning('该工单未完成包装前阶段外观检验');
                    return;
                }
                // 填充SN号位数
                const snByteCountInput = document.getElementById('snBytesInput');
                if (snByteCountInput && basicData.order.ord_snlenth) {
                    snByteCountInput.value = basicData.order.ord_snlenth;
                    snByteCountInput.classList.add('input-has-value');
                }
            }

            // 继续原有的查询逻辑
            const response = await fetch(`/api/barcode-comparison/search?orderNumber=${encodeURIComponent(workOrderNumber)}`);
            const data = await response.json();

            if (!data.success) {
                await SweetAlert.error(data.message || '未找到工单信息');
                return;
            }

            if (!Array.isArray(data.data) || data.data.length === 0) {
                await SweetAlert.info('工单中没有数据');
                return;
            }

            workOrderData = data.data;
            compareStates.clear(); // 清除所有状态
            currentPage = 1;
            displayWorkOrderInfo(data.data);
            updateProgress();

            await SweetAlert.success('工单数据加载成功');

        } catch (error) {
            console.error('查询工单失败:', error);
            await SweetAlert.error('查询工单失败，请检查网络连接');
        }
    }

    function displayWorkOrderInfo(data) {
        const tbody = document.getElementById('workOrderBody');
        tbody.innerHTML = '';

        // 先检查并移除已存在的进度条和分页
        const existingProgress = document.querySelector('.progress-section');
        if (existingProgress) {
            existingProgress.remove();
        }
        
        // 移除已存在的分页控件
        const existingPagination = document.querySelector('.pagination-section');
        if (existingPagination) {
            existingPagination.remove();
        }

        // 如果没有数据，直接返回
        if (!data || data.length === 0) {
            return;
        }

        // 添加新的进度条HTML
        const progressSection = document.createElement('div');
        progressSection.className = 'progress-section';
        progressSection.innerHTML = `
            <div class="progress-info">
                <span>进度：<span id="completedCount">0</span>/<span id="totalCount">${data.length}</span></span>
                <span id="progressPercentage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-bar__fill" style="width: 0%"></div>
            </div>
        `;
        
        // 将进度条插入到表格前面
        document.querySelector('.work-order-info').insertBefore(
            progressSection, 
            document.getElementById('workOrderTable')
        );

        // 添加分页控件
        const paginationSection = document.createElement('div');
        paginationSection.className = 'pagination-section';
        paginationSection.innerHTML = `
            <div class="pagination-info">
                显示 ${Math.min(pageSize, data.length)} 条，共 ${data.length} 条
            </div>
            <div class="pagination-controls">
                <button class="btn btn-page" onclick="prevPage()" ${currentPage === 1 ? 'disabled' : ''}>
                    <svg class="icon-arrow" viewBox="0 0 24 24">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                    </svg>
                    上一页
                </button>
                <span class="page-info">第 ${currentPage} 页</span>
                <button class="btn btn-page" onclick="nextPage()" ${currentPage >= Math.ceil(data.length / pageSize) ? 'disabled' : ''}>
                    下一页
                    <svg class="icon-arrow" viewBox="0 0 24 24">
                        <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
                    </svg>
                </button>
            </div>
        `;

        // 将分页控件添加到表格后面
        document.querySelector('.work-order-info').appendChild(paginationSection);

        // 显示当前页的数据
        displayCurrentPage();

        // 重新绑定事件（确保在DOM更新后绑定）
        bindEvents();
    }

    // 添加显示当前页数据的函数
    function displayCurrentPage() {
        const tbody = document.getElementById('workOrderBody');
        tbody.innerHTML = '';

        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, workOrderData.length);
        
        // 获取最新比对时间戳
        const latestTimestamp = Math.max(...Array.from(compareStates.values())
            .map(state => state.timestamp || 0));
        
        // 修改排序逻辑，参考React代码实现
        const pageData = [...workOrderData].sort((a, b) => {
            const stateA = compareStates.get(a.serialNumber) || { status: 'pending', timestamp: 0 };
            const stateB = compareStates.get(b.serialNumber) || { status: 'pending', timestamp: 0 };
            
            // 最新比对的记录放在最前面
            if (stateA.timestamp === latestTimestamp) return -1;
            if (stateB.timestamp === latestTimestamp) return 1;

            // 未比对的记录（pending）放在中间
            if (stateA.status === 'pending' && stateB.status === 'success') return -1;
            if (stateB.status === 'pending' && stateA.status === 'success') return 1;

            // 已比对的记录按时间戳倒序排列
            if (stateA.status === 'success' && stateB.status === 'success') {
                return stateB.timestamp - stateA.timestamp;
            }

            return 0;
        });

        // 显示排序后的数据
        for (let i = startIndex; i < endIndex; i++) {
            const item = pageData[i];
            const state = compareStates.get(item.serialNumber) || { status: 'pending', result: '待比对' };
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.id}</td>
                <td>${item.orderNumber}</td>
                <td>${item.productModel}</td>
                <td>${item.serialNumber}</td>
                <td>${item.testResult}</td>
                <td class="compare-result">${state.result}</td>
            `;

            // 根据状态添加不同的样式类
            if (state.timestamp === latestTimestamp) {
                row.classList.add('bg-green-100');  // 最新比对的记录（使用导出按钮颜色）
            } else if (state.status === 'success') {
                row.classList.add('bg-blue-100');  // 已比对的记录（蓝色背景）
            } else {
                row.classList.add('pending');  // 未比对的记录（默认背景）
            }

            tbody.appendChild(row);
        }

        // 更新分页信息
        updatePaginationInfo();
    }

    // 添加更新分页信息的函数
    function updatePaginationInfo() {
        const totalPages = Math.ceil(workOrderData.length / pageSize);
        const paginationInfo = document.querySelector('.pagination-info');
        const pageInfo = document.querySelector('.page-info');
        const prevButton = document.querySelector('.btn-page:first-child');
        const nextButton = document.querySelector('.btn-page:last-child');

        paginationInfo.textContent = `显示 ${Math.min(pageSize, workOrderData.length - (currentPage - 1) * pageSize)} 条，共 ${workOrderData.length} 条`;
        pageInfo.textContent = `第 ${currentPage} 页`;
        prevButton.disabled = currentPage === 1;
        nextButton.disabled = currentPage >= totalPages;
    }

    // 添加翻页函数
    function prevPage() {
        if (currentPage > 1) {
            currentPage--;
            displayCurrentPage();
        }
    }

    function nextPage() {
        const totalPages = Math.ceil(workOrderData.length / pageSize);
        if (currentPage < totalPages) {
            currentPage++;
            displayCurrentPage();
        }
    }

    function startComparison() {
        if (!workOrderData) {
            SweetAlert.warning('请确保已查询工单数据');
            return;
        }
    }

    // 添加 CSV 字段转义函数
    function escapeCSVField(field) {
        if (field === null || field === undefined) {
            return '';
        }
        
        field = String(field);
        
        // 如果字段包含逗号、双引号、换行符或回车符，需要用双引号包裹
        if (field.includes(',') || field.includes('"') || field.includes('\n') || field.includes('\r')) {
            // 将字段中的双引号替换为两个双引号
            field = field.replace(/"/g, '""');
            // 用双引号包裹整个字段
            return `"${field}"`;
        }
        
        return field;
    }

    // 格式化日期时间
    function formatDateTime(timestamp) {
        if (!timestamp) return '-';
        
        try {
            const date = new Date(timestamp);
            return new Intl.DateTimeFormat('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            }).format(date);
        } catch (e) {
            console.error('日期格式化错误:', e);
            return '-';
        }
    }

    async function exportData() {
        if (!workOrderData || workOrderData.length === 0) {
            await SweetAlert.warning('没有可导出的数据');
            return;
        }

        try {
            // 修改CSV表头，去除 "ID" 列
            const headers = ['序号', '工单号', '产品型号', 'SN号', '测试结果', '比对结果', '比对时间'];
            
            // 生成CSV内容
            const rows = workOrderData.map((item, index) => {
                const state = compareStates.get(item.serialNumber) || { status: 'pending', result: '未比对' };
                
                // 准备行数据并进行转义
                const rowData = [
                    escapeCSVField(index + 1), // 添加序号
                    escapeCSVField(item.orderNumber),
                    escapeCSVField(item.productModel),
                    escapeCSVField(item.serialNumber),
                    escapeCSVField(item.testResult),
                    escapeCSVField(state.result),
                    escapeCSVField(formatDateTime(state.timestamp))
                ];
                
                return rowData.join(',');
            });
            
            // 添加UTF-8 BOM并组合所有内容，使用 \r\n 作为换行符
            const csvContent = '\ufeff' + [headers.join(','), ...rows].join('\r\n');
            
            // 创建Blob对象
            const blob = new Blob([csvContent], { 
                type: 'text/csv;charset=utf-8;' 
            });
            
            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            
            // 设置文件名（使用格式化的时间戳）
            const timestamp = formatDateTime(new Date()).replace(/[/:]/g, '-');
            const filename = `工单数据_${timestamp}.csv`;
            
            // 设置下载属性
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            
            // 添加到文档、触发下载并清理
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理URL对象
            setTimeout(() => {
                URL.revokeObjectURL(url);
            }, 100);
            
            await SweetAlert.success('数据导出成功');

        } catch (error) {
            console.error('导出数据失败:', error);
            await SweetAlert.error('导出数据失败: ' + error.message);
        }
    }

    // 添加事件绑定
    function bindEvents() {
        const barcode1Input = document.getElementById('barcode1Input');
        const barcode2Input = document.getElementById('barcode2Input');
        const snBytesInput = document.getElementById('snBytesInput');

        // 移除现有的事件监听器（避免重复绑定）
        barcode1Input.removeEventListener('input', handleBarcodeInput);
        barcode2Input.removeEventListener('input', handleBarcodeInput);
        barcode1Input.removeEventListener('keypress', handleBarcode1KeyPress);
        barcode2Input.removeEventListener('keypress', handleBarcode2KeyPress);

        // 为两个输入框添加输入事件监听
        barcode1Input.addEventListener('input', handleBarcodeInput);
        barcode2Input.addEventListener('input', handleBarcodeInput);

        // 添加回车键监听
        barcode1Input.addEventListener('keypress', handleBarcode1KeyPress);
        barcode2Input.addEventListener('keypress', handleBarcode2KeyPress);

        // 修改 SN 字节数输入框的事件监听
        let snBytesTimer = null;
        snBytesInput.addEventListener('input', function() {
            // 清除之前的定时器
            if (snBytesTimer) {
                clearTimeout(snBytesTimer);
            }

            // 设置新的定时器，等待用户输入完成
            snBytesTimer = setTimeout(() => {
                const value = this.value.trim();
                // 只有当输入框失去焦点或按下回车键时才触发清空和聚焦操作
                if (value && !isNaN(value) && parseInt(value) > 0) {
                    // 清空条码输入框
                    barcode1Input.value = '';
                    barcode2Input.value = '';
                    // 聚焦到条码1输入框
                    barcode1Input.focus();
                }
            }, 2000); // 等待1秒，给用户足够的输入时间
        });

        // 添加回车键处理
        snBytesInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const value = this.value.trim();
                if (value && !isNaN(value) && parseInt(value) > 0) {
                    // 清空条码输入框
                    barcode1Input.value = '';
                    barcode2Input.value = '';
                    // 聚焦到条码1输入框
                    barcode1Input.focus();
                }
            }
        });

        // 添加失去焦点处理
        snBytesInput.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value && !isNaN(value) && parseInt(value) > 0) {
                // 清空条码输入框
                barcode1Input.value = '';
                barcode2Input.value = '';
                // 聚焦到条码1输入框
                barcode1Input.focus();
            }
        });
    }

    // 修改回车键处理函数
    function handleBarcode1KeyPress(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const barcode1Input = document.getElementById('barcode1Input');
            const barcode1 = barcode1Input.value;
            const snBytesInput = document.getElementById('snBytesInput').value;
            
            // 如果没有输入SN字节数，不进行长度检查
            if (!snBytesInput) {
                document.getElementById('barcode2Input').focus();
                return;
            }
            
            const snBytes = parseInt(snBytesInput);
            if (isNaN(snBytes)) {
                updateStatusCard('请输入有效的SN字节数', 'error');
                return;
            }
            
            // 只在条码长度不足时才提示错误
            if (barcode1.length < snBytes) {
                return; // 条码扫描可能还未完成，直接返回
            }
            
            // 如果长度超过，才提示错误
            if (barcode1.length > snBytes) {
                barcode1Input.value = '';
                updateStatusCard('条码1长度与SN字节数不匹配', 'error');
                return;
            }
            
            // 如果长度匹配，自动聚焦到条码2输入框
            document.getElementById('barcode2Input').focus();
        }
    }

    function handleBarcode2KeyPress(e) {
        if (e.key === 'Enter') {
            e.preventDefault(); // 只阻止回车默认行为，不做其他处理
        }
    }

    // 修改 handleBarcodeInput 函数
    function handleBarcodeInput(immediate = false) {
        // 清除之前的定时器
        if (compareTimeout) {
            clearTimeout(compareTimeout);
        }

        const barcode1Input = document.getElementById('barcode1Input');
        const barcode2Input = document.getElementById('barcode2Input');
        // 去除所有空格（包括制表符、换行符等）
        const barcode1 = barcode1Input.value.replace(/\s+/g, '');
        const barcode2 = barcode2Input.value.replace(/\s+/g, '');
        const snBytes = parseInt(document.getElementById('snBytesInput').value);

        // 自动更新输入框的值（去除空格后的值）
        barcode1Input.value = barcode1;
        barcode2Input.value = barcode2;

        // 检查是否输入了SN字节数
        if (!snBytes) {
            updateStatusCard('请先输入SN字节数', 'error');
            return;
        }

        // 当条码1输入完成时，立即检查是否在工单中存在
        if (document.activeElement === barcode1Input && barcode1.length === snBytes) {
            // 检查条码1是否在工单中
            if (!workOrderData) {
                SweetAlert.warning('请先查询工单信息');
                return;
            }
            const matchedRow = workOrderData.find(item => item.serialNumber === barcode1);
            if (!matchedRow) {
                updateCompareResult(null, '未测试', 'error');
                // 未测试时保留条码1的值，只聚焦
                barcode1Input.focus();
                return;
            }
            // 条码1验证通过，聚焦到条码2
            barcode2Input.focus();
            return;
        }

        // 当条码2达到指定长度时，自动进行比对
        if (barcode1 && barcode2 && document.activeElement === barcode2Input && 
            barcode2.length === snBytes) {
            // 设置定时器进行比对
            compareTimeout = setTimeout(() => {
                compareBarcode(barcode1, barcode2);
                compareTimeout = null;
            }, 100); // 缩短延时，提高响应速度
        }
    }

    // 条码比对函数
    function compareBarcode(barcode1, barcode2) {
        if (!workOrderData) {
            SweetAlert.warning('请先查询工单信息');
            return;
        }

        // 第一步：先比对条码1和工单数据
        const matchedRow = workOrderData.find(item => item.serialNumber === barcode1);
        
        // 如果条码1在工单中找不到匹配，直接返回"未测试"
        if (!matchedRow) {
            updateCompareResult(null, '未测试', 'error');
            document.getElementById('barcode1Input').focus();
            return;
        }

        // 第二步：如果条码1匹配工单，再比对条码1和条码2
        if (barcode1 === barcode2) {
            // 更新数据库中的比对结果
            updateComparisonInDatabase(barcode1, 'PASS')
                .then(() => {
                    updateCompareResult(matchedRow, 'PASS', 'success');
                    document.getElementById('barcode1Input').value = '';
                    document.getElementById('barcode2Input').value = '';
                    document.getElementById('barcode1Input').focus();
                })
                .catch(error => {
                    console.error('更新比对结果失败:', error);
                    SweetAlert.error('更新比对结果失败');
                });
        } else {
            updateCompareResult(null, '条码错误', 'error');
            document.getElementById('barcode2Input').focus();
        }
    }

    // 添加获取当前用户的函数
    async function getCurrentUser() {
        try {
            const response = await fetch('/api/user/info');
            const data = await response.json();
            return data.success ? data.username : null;
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }

    // 修改更新数据库的函数
    async function updateComparisonInDatabase(serialNumber, result) {
        try {
            // 获取当前用户
            const currentUser = await getCurrentUser();
            if (!currentUser) {
                throw new Error('无法获取当前用户信息');
            }

            const response = await fetch('/api/barcode-comparison/update-comparison-result', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    serialNumber,
                    result,
                    comparisonUser: currentUser
                })
            });

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.message);
            }

            return data;
        } catch (error) {
            console.error('更新比对结果失败:', error);
            throw error;
        }
    }

    // 更新比对结果
    function updateCompareResult(row, result, status) {
        if (row && status === 'success') {
            const currentTime = Date.now();
            
            // 更新当前行的状态
            compareStates.set(row.serialNumber, {
                status: 'success',
                result: result,
                timestamp: currentTime
            });

            // 更新之前所有 comparing 状态的记录为 success
            for (const [sn, state] of compareStates.entries()) {
                if (state.status === 'comparing') {
                    compareStates.set(sn, {
                        ...state,
                        status: 'success'
                    });
                }
            }

            // 保存当前数据行作为最后比对的
            lastComparedRow = row;

            // 重新排序并显示所有数据
            currentPage = 1; // 回到第一页
            displayCurrentPage();

            // 更新进度
            updateProgress();

            // 在比对成功后，延迟2秒将状态文本恢复为初始状态
            setTimeout(() => {
                updateStatusCard('等待开始比对...', '');
            }, 2000);
        }

        // 更新状态卡片
        updateStatusCard(result, status);

        // 播放提示音
        playResultSound(status === 'success');
    }

    // 播放提示音
    function playResultSound(success) {
        const audio = new Audio();
        audio.src = success ? '/static/sounds/success.mp3' : '/static/sounds/error.mp3';
        audio.play().catch(e => console.log('播放提示音失败:', e));
    }

    // 添加更新进度的函数
    function updateProgress() {
        const totalCount = workOrderData.length;
        const successCount = Array.from(compareStates.values())
            .filter(state => state.status === 'success' || state.status === 'comparing')
            .length;
        
        const percentage = Math.round((successCount / totalCount) * 100);
        
        document.querySelector('.progress-bar__fill').style.width = `${percentage}%`;
        document.getElementById('completedCount').textContent = successCount;
        document.getElementById('progressPercentage').textContent = `${percentage}%`;

        // 检查是否全部完成
        if (successCount === totalCount && totalCount > 0) {
            setTimeout(async () => {
                const message = `共计${totalCount}个，已全部测试完成！`;
                await SweetAlert.success(message);
                speak(message);
            }, 500);
        }
    }

    // 添加状态卡片更新函数
    function updateStatusCard(result, status) {
        const statusCard = document.querySelector('.compare-status-card');
        const statusText = statusCard.querySelector('.status-text');
        
        statusCard.className = 'compare-status-card';
        statusCard.classList.add(`status-${status}`);
        
        let displayText = '等待开始比对...';
        
        if (status === 'success') {
            displayText = '已测试，条码正确！';
            speak('已测试，条码正确');
        } else if (status === 'error') {
            displayText = result;
            // 只对特定错误消息进行语音播报
            if (result === '未测试' || result === '条码错误') {
                speak(result);
            }
        }
        
        statusText.textContent = displayText;
    }

    // 修改语音播报函数
    let isSpeaking = false;
    let speakQueue = [];

    function speak(text, priority = 'normal') {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN';
            utterance.rate = 1.0;  // 修改这里：将语速改为1.0（正常速度）
            utterance.volume = 3.0;

            if (priority === 'high' || !isSpeaking) {
                // 取消当前播报
                window.speechSynthesis.cancel();
                speakQueue = [];
                isSpeaking = true;

                utterance.onend = () => {
                    isSpeaking = false;
                    if (speakQueue.length > 0) {
                        const nextText = speakQueue.shift();
                        speak(nextText);
                    }
                };

                window.speechSynthesis.speak(utterance);
            } else {
                // 将普通优先级的语音加入队列
                speakQueue.push(text);
            }
        }
    }

    // 将需要的函数暴露到全局作用域
    window.initBarcodeComparison = initBarcodeComparison;
    window.searchWorkOrder = searchWorkOrder;
    window.startComparison = startComparison;
    window.exportData = exportData;
    window.prevPage = prevPage;
    window.nextPage = nextPage;

    // 添加工单号输入框的事件监听
    const orderNumberInput = document.getElementById('workOrderInput');
    if (orderNumberInput) {
        orderNumberInput.addEventListener('input', async function() {
            const orderNumber = this.value.trim();
            
            // 清空SN号位数字段
            const snByteCountInput = document.getElementById('snBytesInput');
            if (snByteCountInput) {
                snByteCountInput.value = '';
                snByteCountInput.classList.remove('input-has-value');
            }

            if (!orderNumber) return;

            try {
                const response = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNumber)}`);
                const data = await response.json();

                if (data.success && data.order) {
                    // 只填充SN号位数
                    if (snByteCountInput && data.order.ord_snlenth) {
                        snByteCountInput.value = data.order.ord_snlenth;
                        snByteCountInput.classList.add('input-has-value');
                    }

                    // 工单号有效，添加绿色背景
                    this.classList.add('input-has-value');
                } else {
                    // 工单号无效，移除绿色背景
                    this.classList.remove('input-has-value');
                }
            } catch (error) {
                console.error('获取工单信息失败:', error);
                this.classList.remove('input-has-value');
            }
        });
    }
})();

