-- 固件管理系统数据库表结构
-- 生成时间: 2025-05-30
-- MySQL版本: 8.0+

-- 固件主表（核心表）
CREATE TABLE `firmware` (
  `serial_number` VARCHAR(30) NOT NULL COMMENT '唯一ERP流水号',
  `name` VARCHAR(80) NOT NULL COMMENT '固件名称',
  `version` VARCHAR(50) NOT NULL COMMENT '版本号',
  `status` ENUM('active','pending','rejected','obsolete') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `source` ENUM('新发布', '升级', '修改') NOT NULL DEFAULT '新发布' COMMENT '来源类型',
  `products` JSON NOT NULL COMMENT '适用产品型号["型号A","型号B"]',
  `version_requirements` TEXT COMMENT '版本使用要求',
  `description` TEXT NOT NULL COMMENT '变更内容',
  `developer` VARCHAR(20) NOT NULL COMMENT '研发者',
  `uploader` VARCHAR(20) NOT NULL COMMENT '上传者姓名',
  `approver_name` VARCHAR(20) COMMENT '审核者姓名',
  `parent_sn` VARCHAR(30) COMMENT '父版本流水号',
  
  -- 技术规格信息
  `build_time` VARCHAR(50) COMMENT '构建时间',
  `backplane_version` VARCHAR(50) COMMENT '背板总线版本',
  `io_version` VARCHAR(50) COMMENT '高速IO版本',
  `download_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '下载次数统计',
  `usage_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用次数统计',
  `reject_reason` TEXT COMMENT '拒绝理由',
  `is_deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '软删除标记',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `approve_time` DATETIME COMMENT '审核通过时间',
  `obsolete_time` DATETIME COMMENT '作废时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`serial_number`),
  INDEX `idx_parent_sn` (`parent_sn`),
  INDEX `idx_status` (`status`),
  INDEX `idx_source` (`source`),
  INDEX `idx_approve_time` (`approve_time`),
  INDEX `idx_status_approve_time` (`status`, `approve_time`),
  INDEX `idx_developer` (`developer`),
  INDEX `idx_uploader` (`uploader`),
  CONSTRAINT `fk_firmware_parent` 
    FOREIGN KEY (`parent_sn`) REFERENCES `firmware`(`serial_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='固件主表';

-- 固件文件表
CREATE TABLE `firmware_file` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `serial_number` VARCHAR(30) NOT NULL COMMENT '关联固件',
  `file_path` VARCHAR(500) NOT NULL COMMENT '本地存储路径',
  `original_filename` VARCHAR(255) COMMENT '原始文件名',
  `file_hash` CHAR(64) NOT NULL COMMENT 'SHA256校验值',
  `file_size` DECIMAL(5,2) NOT NULL COMMENT '文件大小(MB)',
  `upload_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `is_deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '软删除标记',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT `fk_file_firmware` 
    FOREIGN KEY (`serial_number`) REFERENCES `firmware`(`serial_number`),
  INDEX `idx_file_hash` (`file_hash`),
  INDEX `idx_serial_time` (`serial_number`, `upload_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='固件文件表';

-- 下载记录表
CREATE TABLE `download_record` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `serial_number` VARCHAR(30) NOT NULL COMMENT '固件流水号',
  `work_order` VARCHAR(50) NOT NULL COMMENT '工单号',
  `product_code` VARCHAR(50) NOT NULL COMMENT '产品编码',
  `product_model` VARCHAR(50) NOT NULL COMMENT '产品型号',
  `burn_count` INT NOT NULL COMMENT '烧录数量',
  `software_version` VARCHAR(50) NOT NULL COMMENT '软件版本',
  `build_time` VARCHAR(30) NOT NULL COMMENT '构建时间',
  `backplane_version` VARCHAR(50) NOT NULL COMMENT '背板版本',
  `io_version` VARCHAR(50) NOT NULL COMMENT '高速IO版本',
  `user_name` VARCHAR(20) NOT NULL COMMENT '使用人姓名',
  `notes` TEXT COMMENT '备注信息',
  `is_deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '软删除标记',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT `fk_download_firmware` 
    FOREIGN KEY (`serial_number`) REFERENCES `firmware`(`serial_number`),
  INDEX `idx_work_order` (`work_order`),
  INDEX `idx_serial_time` (`serial_number`, `create_time`),
  INDEX `idx_product_code` (`product_code`),
  INDEX `idx_product_model` (`product_model`),
  INDEX `idx_user_name` (`user_name`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='下载记录表';

-- 审核流水表
CREATE TABLE `approval_flow` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `serial_number` VARCHAR(30) NOT NULL COMMENT '固件流水号',
  `action` ENUM('submit','approve','reject','obsolete') NOT NULL COMMENT '操作类型',
  `operator_name` VARCHAR(20) NOT NULL COMMENT '操作人姓名',
  `notes` TEXT COMMENT '备注',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT `fk_flow_firmware` 
    FOREIGN KEY (`serial_number`) REFERENCES `firmware`(`serial_number`),
  INDEX `idx_flow_time` (`create_time`),
  INDEX `idx_operator_name` (`operator_name`),
  INDEX `idx_action_time` (`action`, `create_time`),
  INDEX `idx_serial_action` (`serial_number`, `action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审核流水表';

-- 固件序列号录入明细表
CREATE TABLE `firmware_serial_detail` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `work_order` VARCHAR(50) NOT NULL COMMENT '工单号(外键关联下载记录表)',
  `serial_type` ENUM('SN','PCBA') NOT NULL COMMENT '序列号类型',
  `firmware_sn` VARCHAR(50) DEFAULT 'N/A' COMMENT 'SN号(唯一)',
  `firmware_pcba` VARCHAR(120) DEFAULT 'N/A' COMMENT 'PCBA序列号',
  `firmware_hash` CHAR(64) DEFAULT 'N/A' COMMENT 'PCBA序列号HASH256值',
  `serial_digits` INT NOT NULL COMMENT '序列号位数要求',
  `input_user` VARCHAR(20) NOT NULL COMMENT '录入人员',
  `input_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间',
  `is_deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '软删除标记',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 唯一约束：SN号全局唯一
  UNIQUE KEY `uk_firmware_sn` (`firmware_sn`),
  
  -- 索引设计
  INDEX `idx_work_order` (`work_order`),
  INDEX `idx_firmware_hash` (`firmware_hash`),
  INDEX `idx_work_order_type` (`work_order`, `serial_type`),
  
  -- 注释：移除外键约束，因为work_order在download_record表中不是唯一的
  -- 一个工单可能对应多条下载记录，通过应用层逻辑维护数据一致性
  -- CONSTRAINT `fk_serial_work_order` 
  --   FOREIGN KEY (`work_order`) REFERENCES `download_record`(`work_order`),
    
  -- 业务约束：确保数据逻辑正确性
  CONSTRAINT `chk_serial_data_logic` CHECK (
    (serial_type = 'SN' AND firmware_sn != NULL AND firmware_pcba = 'N/A' AND firmware_hash = 'N/A') OR
    (serial_type = 'PCBA' AND firmware_pcba != 'N/A' AND firmware_hash != 'N/A' AND firmware_sn = 'N/A')
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='固件序列号录入明细表';

-- 索引优化说明
/*
1. 固件表索引:
   - 主键: serial_number (唯一标识)
   - idx_parent_sn: 加速版本树查询
   - idx_status: 加速状态筛选
   - idx_source: 加速来源类型筛选
   - idx_approve_time: 加速时间排序
   - idx_status_approve_time: 复合索引优化状态+时间查询
   - idx_developer: 加速研发者筛选
   - idx_uploader: 加速上传者筛选

2. 文件表索引:
   - idx_file_hash: 加速文件校验查询
   - idx_serial_time: 加速固件文件时间查询

3. 下载记录索引:
   - idx_work_order: 工单查询优化
   - idx_serial_time: 固件下载记录时间查询
   - idx_product_code: 产品编码查询
   - idx_product_model: 产品型号查询
   - idx_user_name: 使用人查询
   - idx_create_time: 时间范围查询优化

4. 审核流水索引:
   - idx_flow_time: 时间范围查询优化
   - idx_operator_name: 操作人查询
   - idx_action_time: 操作类型+时间查询
   - idx_serial_action: 固件+操作类型查询

5. 固件序列号录入明细表索引:
   - uk_firmware_sn: SN号全局唯一约束
   - idx_work_order: 工单号查询优化
   - idx_firmware_hash: PCBA哈希值查询

   - idx_work_order_type: 工单号+类型复合查询优化

*/

-- 字段说明补充
/*
固件主表新增字段功能说明:
- source: 支持前端显示"新发布/升级/修改"标签
- approver_name: 审核者姓名，避免频繁JOIN用户表
- download_count: 下载次数统计，支持前端实时显示
- usage_count: 使用次数统计，支持前端统计分析
- reject_reason: 拒绝理由，支持审核拒绝时的详细说明
- approve_time: 审核通过时间，支持生效时间显示和排序
- obsolete_time: 作废时间，用于计算版本生效天数

固件文件表新增字段:
- original_filename: 原始文件名，保留用户上传时的文件名
- upload_time: 文件上传时间，独立于固件创建时间

下载记录表新增字段:
- notes: 备注信息，支持用户录入额外说明

审核流水表新增字段:
- operator_name: 操作人姓名，避免频繁JOIN查询
*/

-- 固件序列号录入明细表字段说明
/*
固件序列号录入明细表字段功能说明:
- work_order: 工单号，外键关联download_record表，一个工单可对应多条序列号记录
- serial_type: 序列号类型枚举，区分SN号和PCBA序列号
- firmware_sn: SN号存储，设置全局唯一约束，默认值'N/A'
- firmware_pcba: PCBA序列号明文存储，便于查询验证，默认值'N/A'
- firmware_hash: PCBA序列号的SHA256哈希值，提供安全存储，默认值'N/A'
- serial_digits: 记录序列号位数要求，用于验证长度规范
- input_user: 录入操作人员，追溯录入责任
- input_time: 录入时间戳，记录具体录入时刻
- 业务约束: 通过CHECK约束确保SN和PCBA数据的互斥性和完整性

数据安全设计:
- SN号：直接明文存储，设置唯一约束防重复
- PCBA序列号：明文+哈希值双重存储，既能查询又保证安全
- 软删除：支持数据追溯，保证历史记录完整性
- 外键约束：确保数据关联完整性，防止孤立记录

业务流程支持:
- 支持FG工单录入SN号，非FG工单录入PCBA序列号
- 支持按工单统计录入进度
- 支持序列号重复性检查
- 支持录入人员操作追溯
*/
