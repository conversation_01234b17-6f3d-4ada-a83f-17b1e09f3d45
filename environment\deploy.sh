#!/bin/bash

# 加载配置
source "$(dirname "$0")/config.sh"

echo "开始部署流程..."
check_root

# 添加环境检查
check_environment() {
    echo "检查Python版本..."
    if ! command -v "${PYTHON_BIN}" &> /dev/null; then
        echo "错误: Python ${PYTHON_VERSION} 未安装"
        exit 1
    fi
    
    # 验证Python版本
    installed_version=$("${PYTHON_BIN}" --version 2>&1 | cut -d' ' -f2)
    if [ "$installed_version" != "${PYTHON_VERSION}" ]; then
        echo "警告: Python版本不匹配 (已安装: ${installed_version}, 配置: ${PYTHON_VERSION})"
    fi
    
    echo "检查端口可用性..."
    if lsof -i:${SERVER_PORT} || lsof -i:${DB_PORT}; then
        echo "错误: 端口已被占用"
        exit 1
    fi
}

# 在部署开始前调用检查
check_environment

# 在部署开始前添加
source "$(dirname "$0")/check_environment.sh"
check_system_requirements
check_network

# 部署开始时间
start_time=$(date +%s)

# 创建备份目录
backup_dir="${PROJECT_PATH}/backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"

# 错误处理函数
handle_error() {
    echo "错误: $1"
    echo "开始回滚..."
    if [ -d "$backup_dir" ]; then
        cp -r "$backup_dir"/* "${PROJECT_PATH}/"
    fi
    exit 1
}

# 备份现有配置
if [ -d "${PROJECT_PATH}" ]; then
    echo "备份现有配置..."
    # 创建临时备份目录
    temp_backup="${PROJECT_PATH}_backup_temp"
    mkdir -p "$temp_backup"
    
    # 复制文件到临时目录
    cp -r "${PROJECT_PATH}"/* "$temp_backup/"
    
    # 移动临时目录到最终备份目录
    mv "$temp_backup" "$backup_dir"
fi

# 系统初始化
echo "步骤 1/6: 系统初始化"
bash "$(dirname "$0")/system_init.sh" || handle_error "系统初始化失败"

# MySQL配置（移到Python环境配置之前）
echo "步骤 2/6: 配置MySQL"
bash "$(dirname "$0")/mysql_setup.sh" || handle_error "MySQL配置失败"

# 在MySQL配置后添加验证
echo "验证MySQL配置..."
if ! mysql -u root -p"${DB_PASSWORD}" -e "SELECT 1;" >/dev/null 2>&1; then
    handle_error "MySQL配置验证失败"
fi

# Python环境配置
echo "步骤 3/6: 配置Python环境"
bash "$(dirname "$0")/python_setup.sh" || handle_error "Python环境配置失败"

# 部署应用
echo "步骤 4/6: 部署应用"
bash "$(dirname "$0")/app_deploy.sh" || handle_error "应用部署失败"

# 配置服务
echo "步骤 5/6: 配置服务"
bash "$(dirname "$0")/service_setup.sh" || handle_error "服务配置失败"

# 验证部署
echo "步骤 6/6: 验证部署"
echo "等待服务启动..."
sleep 5

if curl -s "http://${GUNICORN_HOST}:${GUNICORN_PORT}" > /dev/null; then
    echo "应用成功部署并运行！"
else
    handle_error "应用未能正确启动，请检查日志"
fi

# 计算部署耗时
end_time=$(date +%s)
duration=$((end_time - start_time))
echo "部署完成！总耗时: ${duration} 秒"

# 显示重要信息
echo "
部署信息摘要:
- 应用地址: http://${GUNICORN_HOST}:${GUNICORN_PORT}
- 数据库地址: ${DB_HOST}:${DB_PORT}
- 日志位置: ${LOG_PATH}
- 配置文件: ${PROJECT_PATH}/gunicorn_config.py
- Supervisor配置: /etc/supervisor/conf.d/${PROJECT_NAME}.conf
" 

# 添加备份管理函数
manage_backups() {
    local backup_dir="$1"
    local max_count=${BACKUP_MAX_COUNT}
    
    # 如果启用压缩
    if [ "${BACKUP_COMPRESS}" = true ]; then
        cd "${backup_dir}/.." || exit 1
        tar -czf "${backup_dir}.tar.gz" "$(basename "${backup_dir}")"
        rm -rf "${backup_dir}"
    fi
    
    # 清理旧备份
    local old_backups
    old_backups=$(ls -dt "${PROJECT_PATH}"/backup_* | tail -n +$((max_count + 1)))
    if [ -n "${old_backups}" ]; then
        echo "清理旧备份..."
        echo "${old_backups}" | xargs rm -rf
    fi
} 

# 添加更多验证步骤
verify_deployment() {
    echo "验证部署..."
    
    # 验证MySQL
    if ! mysql -u root -p"${DB_PASSWORD}" -e "SELECT 1;" >/dev/null 2>&1; then
        return 1
    fi
    
    # 验证Python环境
    if ! "${PYTHON_BIN}" -c "import pymysql" >/dev/null 2>&1; then
        return 1
    fi
    
    # 验证应用
    if ! curl -s "http://${GUNICORN_HOST}:${GUNICORN_PORT}/health" >/dev/null; then
        return 1
    fi
    
    return 0
}

# 在部署完成后调用
if verify_deployment; then
    echo "部署验证成功"
else
    handle_error "部署验证失败"
fi 