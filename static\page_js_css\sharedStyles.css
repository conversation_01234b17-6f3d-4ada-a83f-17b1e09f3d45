.form-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    padding: 1.25rem;
    border-radius: 0.75rem;
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

@media (max-width: 48rem) {
    .form-grid {
        grid-template-columns: 1fr;
    }
}

.form-group {
    flex: 1;
    min-width: 0;
    position: relative;
    margin-bottom: 0.25rem;
}

label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: #374151;
    font-size: var(--font-size-base);
    transition: all 0.2s ease;
}

input, select, textarea {
    width: 100%;
    padding: 0.625rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background-color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-base);
    transition: all 0.3s ease;
    box-shadow: 
        0 1px 2px rgba(0, 0, 0, 0.05),
        inset 0 1px 2px rgba(255, 255, 255, 0.5);
}

input[type="datetime-local"] {
    min-width: 100%;
}

.basic-info-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        inset 0 2px 4px rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.basic-info-card:hover {
    box-shadow: 
        0 6px 8px -1px rgba(0, 0, 0, 0.1),
        0 4px 6px -1px rgba(0, 0, 0, 0.06),
        inset 0 2px 4px rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.basic-info-card h2 {
    font-size: clamp(var(--font-size-lg), 3vw, var(--font-size-xl));
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #1f2937;
    position: relative;
    padding-bottom: 0.5rem;
}

.basic-info-card h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 3rem;
    height: 2px;
    background: linear-gradient(to right, #3b82f6, transparent);
    border-radius: 1px;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-group input,
    .form-group select {
        font-size: var(--font-size-sm);
    }
    
    .basic-info-card {
        padding: 1rem;
    }
}

.product-details {
    display: flex;
    gap: 10px; /* 控制输入框之间的间距 */
}

.batch-number, .remarks {
    flex: 1; /* 使输入框均匀分布 */
}

.form-row {
    display: flex;
    gap: 1rem;
    width: 100%;
    padding: 0.25rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.form-row:hover {
    background: rgba(255, 255, 255, 0.6);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-row .form-group {
    flex: 1;
    min-width: 0; /* 防止flex项溢出 */
}

/* 响应式布局 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }
    
    .form-row .form-group {
        width: 100%;
    }
}

/* 输入框焦点状态 */
.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

/* 输入框悬停状态 */
.form-group input:hover,
.form-group select:hover {
    border-color: #9ca3af;
    background-color: #ffffff;
}

/* 只读输入框样式 */
.form-group input[readonly] {
    background-color: #f3f4f6;
    border-color: #d1d5db;
    cursor: not-allowed;
    opacity: 0.8;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-group {
    animation: fadeIn 0.3s ease-out forwards;
}

/* 必填项标签样式 */
.form-group.required label::after {
    content: '';
    margin-left: 4px;
    opacity: 0.8;
    color: #dc2626;
    transition: opacity 0.3s ease;
}

/* 显示错误状态时的必填标记 */
.form-group.required.error label::after {
    content: '※';
    opacity: 1;
}

/* 输入框错误状态 */
.form-group.error input,
.form-group.error select {
    border-color: #dc2626;
    background-color: #fff5f5;
    box-shadow: 0 0 0 1px #dc2626;
}

/* 输入框错误状态的焦点效果 */
.form-group.error input:focus,
.form-group.error select:focus {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* 输入框错误状态的悬停效果 */
.form-group.error input:hover,
.form-group.error select:hover {
    border-color: #ef4444;
}

/* 错误提示文本 */
.form-group .error-message {
    display: none;
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    transition: all 0.3s ease;
}

/* 显示错误消息 */
.form-group.error .error-message {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

/* 必填项标的颜色变化 */
.form-group.error label {
    color: #dc2626;
}

/* 添加输入框验证的过渡效果 */
.form-group input,
.form-group select {
    transition: all 0.3s ease;
}

/* 输入正确时的状态 */
.form-group.valid input,
.form-group.valid select {
    border-color: #059669;
    background-color: #f0fdf4;
}

/* 输入正确时的焦点效果 */
.form-group.valid input:focus,
.form-group.valid select:focus {
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

/* 确保错误状态的优先级高于其他状态 */
.form-group.error input,
.form-group.error select {
    border-color: #dc2626 !important;
    box-shadow: 0 0 0 1px #dc2626 !important;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 添加必填项的星号提示 */
.form-group.required label::before {
    content: '*';
    color: #dc2626;
    margin-right: 4px;
    opacity: 0.8;
}

/* 非必填项的标签 */
.form-group.optional label {
    color: #6b7280;
}

/* 输入框验证状态的过渡 */
.form-group {
    position: relative;
    transition: all 0.3s ease;
}

/* 输入框底部的错误提示线条 */
.form-group.error::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #dc2626;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.form-group.error:focus-within::after {
    transform: scaleX(1);
}

/* 添加输入框值存在时的样式 */
input.input-has-value,
select.input-has-value,
textarea.input-has-value {
    background-color: #E8F5E9;
    border-color: #4CAF50;
    transition: all 0.3s ease;
}

/* 保持焦点样式 */
input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 确保错误状态样式优先级更高 */
.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    background-color: #fff5f5 !important;
    border-color: #dc2626 !important;
    box-shadow: 0 0 0 1px #dc2626 !important;
}

/* 只读输入框样式 */
input[readonly] {
    background-color: #f3f4f6 !important;
    border-color: #d1d5db !important;
    cursor: not-allowed;
    opacity: 0.8;
}

/* 输入框过渡动画 */
input, select, textarea {
    transition: all 0.3s ease;
}

/* 自定义宽度行的样式 */
.custom-width-row {
    display: flex;
    gap: 1rem;
    width: 100%;
}

.width-25 {
    flex: 0 0 25% !important;
    max-width: 25%;
}

.width-50 {
    flex: 0 0 50% !important;
    max-width: 50%;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .custom-width-row {
        flex-direction: column;
    }
    
    .width-25,
    .width-50 {
        flex: 0 0 100% !important;
        max-width: 100%;
    }
}

/* 基本信息卡片的布局样式 */
.basic-info-card .form-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.basic-info-card .form-row {
    display: flex;
    gap: 1rem;
    width: 100%;
}

/* 第三行特殊布局样式 */
.basic-info-card .custom-width-row {
    display: flex;
    gap: 1rem;
    width: 100%;
}

.basic-info-card .custom-width-row .width-25 {
    flex: 0 0 25% !important;
    max-width: 25%;
}

.basic-info-card .custom-width-row .width-50 {
    flex: 0 0 50% !important;
    max-width: 50%;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .basic-info-card .form-row,
    .basic-info-card .custom-width-row {
        flex-direction: column;
    }
    
    .basic-info-card .form-group,
    .basic-info-card .custom-width-row .width-25,
    .basic-info-card .custom-width-row .width-50 {
        flex: 0 0 100% !important;
        max-width: 100%;
    }
}

.form-row {
    display: flex;
    gap: 1rem;
    width: 100%;
}

.form-group {
    flex: 1;
    min-width: 0;
}

.full-width {
    width: 100%;
}

/* 添加列宽控制类 */
.form-row .form-group.col-1 {
    flex: 0 0 25%;  /* 固定宽度 */
    min-width: 0;
}

.form-row .form-group.col-2 {
    flex: 0 0 25%;  /* 固定宽度 */
    min-width: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .form-row .form-group.col-1,
    .form-row .form-group.col-2 {
        flex: 0 0 100%;
    }
}
