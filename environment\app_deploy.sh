#!/bin/bash

# 加载配置
source "$(dirname "$0")/config.sh"

echo "开始部署应用..."
check_root

# 检查部署环境
echo "检查部署环境..."
if [ ! -d "/root/deploy_temp" ]; then
    echo "错误: 部署源目录不存在"
    exit 1
fi

# 检查必要的源文件和目录
echo "检查源文件..."
required_files=(
    "app.py"
    "requirements.txt"
    "check_versions.py"
)

required_dirs=(
    "static"
    "templates"
    "models"
    "routes"
    "services"
    "database"
    "INDEX"
    "utils"
)

for file in "${required_files[@]}"; do
    if [ ! -f "/root/deploy_temp/$file" ]; then
        echo "错误: 源文件 $file 不存在"
        exit 1
    fi
done

for dir in "${required_dirs[@]}"; do
    if [ ! -d "/root/deploy_temp/$dir" ]; then
        echo "警告: 源目录 $dir 不存在，将创建空目录"
        mkdir -p "/root/deploy_temp/$dir"
    fi
done

# 复制项目文件
echo "复制项目文件..."
for file in "${required_files[@]}"; do
    cp -f "/root/deploy_temp/$file" "${PROJECT_PATH}/" || check_status "复制 $file 失败"
done

for dir in "${required_dirs[@]}"; do
    cp -r "/root/deploy_temp/$dir" "${PROJECT_PATH}/" || check_status "复制 $dir 失败"
done

# 创建gunicorn配置文件
echo "创建Gunicorn配置..."
cat > "${PROJECT_PATH}/gunicorn_config.py" << EOF
import multiprocessing

# Server socket
bind = '${GUNICORN_HOST}:${GUNICORN_PORT}'
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = 'gevent'
worker_connections = 1000
threads = ${GUNICORN_THREADS}

# Timeout
timeout = 120
keepalive = 2

# Logging
accesslog = '${ACCESS_LOG}'
errorlog = '${ERROR_LOG}'
loglevel = 'info'

# Process naming
proc_name = '${PROJECT_NAME}'

# Server mechanics
daemon = False
pidfile = '${PROJECT_PATH}/gunicorn.pid'

# SSL config
# keyfile = ''
# certfile = ''

# Server hooks
def on_starting(server):
    pass

def on_reload(server):
    pass

def on_exit(server):
    pass
EOF
check_status "Gunicorn配置创建失败"

# 设置权限
echo "设置应用权限..."
chown -R "${APP_USER}:${APP_GROUP}" "${PROJECT_PATH}"
chmod -R 755 "${PROJECT_PATH}"
check_status "权限设置失败"

echo "应用部署完成！" 