function initDashboard() {
    const content = document.getElementById('content');
    content.innerHTML = `
        <div id="dashboard-content">
            <div class="dashboard-header">
                <h2>数据看板</h2>
                <div class="dashboard-actions">
                    <button class="btn-refresh" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <div class="date-range">
                        <select id="timeRange" onchange="changeDateRange(this.value)">
                            <option value="today">今日</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="stats-cards">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">
                            <span id="totalTests">0</span>
                            <small>台</small>
                        </div>
                        <div class="stat-label">测试总量</div>
                        <div class="stat-trend increase">
                            <i class="fas fa-arrow-up"></i>
                            <span id="testIncrease">0%</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">
                            <span id="passRate">0</span>
                            <small>%</small>
                        </div>
                        <div class="stat-label">测试通过率</div>
                        <div class="stat-trend">
                            <span id="passRateChange">-</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">
                            <span id="faultCount">0</span>
                            <small>台</small>
                        </div>
                        <div class="stat-label">故障数量</div>
                        <div class="stat-trend decrease">
                            <i class="fas fa-arrow-down"></i>
                            <span id="faultDecrease">0%</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-industry"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">
                            <span id="productionCount">0</span>
                            <small>台</small>
                        </div>
                        <div class="stat-label">生产总量</div>
                        <div class="stat-trend">
                            <span id="productionChange">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-grid">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>产品型号分布</h3>
                    </div>
                    <div id="productBarChart" style="height: 300px;"></div>
                </div>

                <div class="chart-card">
                    <div class="chart-header">
                        <h3>最新测试记录</h3>
                        <a href="#" class="view-more">查看更多 <i class="fas fa-arrow-right"></i></a>
                    </div>
                    <div class="recent-tests-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>产品型号</th>
                                    <th>序列号</th>
                                    <th>测试结果</th>
                                    <th>测试时间</th>
                                </tr>
                            </thead>
                            <tbody id="recentTestsList">
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="chart-grid">
                <!-- 生产效率分析 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>生产效率分析</h3>
                        <div class="time-selector">
                            <button class="time-btn active">日</button>
                            <button class="time-btn">周</button>
                            <button class="time-btn">月</button>
                        </div>
                    </div>
                    <div id="efficiencyChart" style="height: 300px;"></div>
                </div>

                <!-- 产品组装追踪 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>产品组装追踪</h3>
                        <div class="chart-actions">
                            <button class="btn-refresh" onclick="refreshAssemblyData()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div id="assemblyChart" style="height: 300px;"></div>
                </div>
            </div>

            <!-- 质量控制面板 -->
            <div class="chart-grid">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>关键指标监控</h3>
                    </div>
                    <div class="kpi-grid">
                        <div class="kpi-item">
                            <div class="kpi-title">平均测试时长</div>
                            <div class="kpi-value" id="avgTestTime">--</div>
                            <div class="kpi-chart" id="testTimeChart"></div>
                        </div>
                        <div class="kpi-item">
                            <div class="kpi-title">首次通过率</div>
                            <div class="kpi-value" id="firstPassRate">--%</div>
                            <div class="kpi-chart" id="firstPassChart"></div>
                        </div>
                        <div class="kpi-item">
                            <div class="kpi-title">返工率</div>
                            <div class="kpi-value" id="reworkRate">--%</div>
                            <div class="kpi-chart" id="reworkChart"></div>
                        </div>
                    </div>
                </div>

                <!-- 实时生产状态 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>实时生产状态</h3>
                        <span class="status-time">更新于: <span id="lastUpdateTime">--:--</span></span>
                    </div>
                    <div class="production-status">
                        <div class="status-grid">
                            <div class="status-item">
                                <i class="fas fa-cog spinning"></i>
                                <span>测试中</span>
                                <div class="status-count" id="testingCount">0</div>
                            </div>
                            <div class="status-item">
                                <i class="fas fa-tools"></i>
                                <span>组装中</span>
                                <div class="status-count" id="assemblingCount">0</div>
                            </div>
                            <div class="status-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>待处理故障</span>
                                <div class="status-count" id="pendingFaults">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 使用 setTimeout 确保 DOM 完全渲染后再初始化图表
    setTimeout(() => {
        // 初始化所有图表
        initCharts();

        // 加载实时数据
        loadDashboardData();

        // 设置自动刷新
        startAutoRefresh();
    }, 100);
}

// 添加页面加载完成的处理
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.hash === '#dashboard' || window.location.hash === '') {
        initDashboard();
    }
});

async function loadDashboardData() {
    // 注释掉实际的数据请求
    /*
    try {
        const timeRange = document.getElementById('timeRange').value;
        const response = await fetch(`/api/dashboard/stats?range=${timeRange}`);
        const result = await response.json();
        // ... 原有的数据处理代码
    } catch (error) {
        Logger.error('Error loading dashboard data:', error);
        showToast('加载数据失败，请稍后重试', 'error');
    }
    */

    // 使用静态数据
    const mockData = {
        totalTests: 120,
        passRate: 99.7,
        faultCount: 1,
        productionCount: 114,
        trends: {
            tests: { increase: 15.2 },
            passRate: { increase: 2.1 },
            faults: { decrease: 8.5 }
        }
    };

    // 更新统计卡片
    const totalTestsElement = document.getElementById('totalTests');
    const passRateElement = document.getElementById('passRate');
    const faultCountElement = document.getElementById('faultCount');
    const productionCountElement = document.getElementById('productionCount');

    // 检查元素是否存在再设置值
    if (totalTestsElement) totalTestsElement.textContent = mockData.totalTests;
    if (passRateElement) passRateElement.textContent = mockData.passRate;
    if (faultCountElement) faultCountElement.textContent = mockData.faultCount;
    if (productionCountElement) productionCountElement.textContent = mockData.productionCount;

    // 更新趋势指标
    updateTrendIndicator('testIncrease', mockData.trends.tests);
    updateTrendIndicator('passRateChange', mockData.trends.passRate);
    updateTrendIndicator('faultDecrease', mockData.trends.faults);

    // 图表保持现有的静态数据
}

function updateTrendIndicator(elementId, trend) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const [direction, value] = Object.entries(trend)[0];
    const isPositive = direction === 'increase';

    element.innerHTML = `
        <i class="fas fa-arrow-${isPositive ? 'up' : 'down'}"></i>
        ${value}%
    `;
    element.className = `stat-trend ${direction}`;
}

function updateCharts(data) {
    if (!window.dashboardCharts) return;

    // 更新趋势图表
    const trendChart = window.dashboardCharts.trend;
    if (trendChart) {
        trendChart.setOption({
            series: [{
                name: '测试数量',
                data: data.distribution.map(item => item.total)
            }, {
                name: '通过数量',
                data: data.distribution.map(item => item.passed)
            }],
            xAxis: {
                data: data.distribution.map(item => item.type)
            }
        });
    }

    // 更新产品分布图表
    const productChart = window.dashboardCharts.product;
    if (productChart) {
        productChart.setOption({
            series: [{
                data: data.distribution.map(item => ({
                    value: item.total,
                    name: item.type,
                    itemStyle: {
                        color: getProductColor(item.type)
                    }
                }))
            }]
        });
    }
}

function getProductColor(type) {
    const colors = {
        'CPU': '#409EFF',
        'IO': '#67C23A',
        'Coupler': '#E6A23C'
    };
    return colors[type] || '#909399';
}

function changeDateRange(range) {
    loadDashboardData();
}

// 添加自动刷新
let refreshInterval;

function startAutoRefresh() {
    stopAutoRefresh();
    refreshInterval = setInterval(loadDashboardData, 60000); // 每分钟刷新一次
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
}

// 在页面加载和离开时处理自动刷新
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        stopAutoRefresh();
    } else {
        startAutoRefresh();
    }
});

// 初始化时启动自动刷新
startAutoRefresh();

function initCharts() {
    try {
        // 确保容器元素存在
        const productBarChartEl = document.getElementById('productBarChart');
        const efficiencyChartEl = document.getElementById('efficiencyChart');
        const assemblyChartEl = document.getElementById('assemblyChart');

        if (!productBarChartEl || !efficiencyChartEl || !assemblyChartEl) {
            Logger.error('Chart containers not found, retrying...');
            setTimeout(initCharts, 100);
            return;
        }

        // 销毁可能存在的旧实例
        if (window.dashboardCharts) {
            Object.values(window.dashboardCharts).forEach(chart => {
                chart && chart.dispose();
            });
        }

        // 初始化图表实例
        const productBarChart = echarts.init(productBarChartEl);
        const efficiencyChart = echarts.init(efficiencyChartEl);
        const assemblyChart = echarts.init(assemblyChartEl);

        // 初始化产品型号柱状图
        productBarChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['CPU控制器', 'IO模块', '耦合器'],
                axisLabel: {
                    interval: 0,
                    rotate: 0
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '数量',
                    type: 'bar',
                    barWidth: '40%',
                    data: [
                        {value: 320, itemStyle: {color: '#409EFF'}},
                        {value: 240, itemStyle: {color: '#67C23A'}},
                        {value: 180, itemStyle: {color: '#E6A23C'}}
                    ]
                }
            ]
        });

        // 初始化效率图表
        efficiencyChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: ['生产效率', '目标效率', '实际产出'],
                top: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['8:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']
            },
            yAxis: [
                {
                    type: 'value',
                    name: '效率(%)',
                    max: 100
                },
                {
                    type: 'value',
                    name: '产出(台)',
                    splitLine: {
                        show: false
                    }
                }
            ],
            series: [
                {
                    name: '生产效率',
                    type: 'line',
                    smooth: true,
                    data: [85, 90, 88, 95, 87, 92, 89],
                    itemStyle: {
                        color: '#409EFF'
                    }
                },
                {
                    name: '目标效率',
                    type: 'line',
                    smooth: true,
                    data: [90, 90, 90, 90, 90, 90, 90],
                    itemStyle: {
                        color: '#67C23A'
                    },
                    lineStyle: {
                        type: 'dashed'
                    }
                },
                {
                    name: '实际产出',
                    type: 'bar',
                    yAxisIndex: 1,
                    data: [42, 45, 44, 47, 43, 46, 44],
                    itemStyle: {
                        color: '#E6A23C'
                    }
                }
            ]
        });

        // 初始化组装追踪图表
        assemblyChart.setOption({
            tooltip: {
                trigger: 'item'
            },
            legend: {
                top: '5%',
                left: 'center'
            },
            series: [
                {
                    name: '组装状态',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 335, name: '已完成', itemStyle: { color: '#67C23A' } },
                        { value: 310, name: '组装中', itemStyle: { color: '#409EFF' } },
                        { value: 234, name: '待组装', itemStyle: { color: '#E6A23C' } },
                        { value: 135, name: '异常', itemStyle: { color: '#F56C6C' } },
                        { value: 148, name: '其他', itemStyle: { color: '#909399' } }
                    ]
                }
            ]
        });

        // 保存图表实例
        window.dashboardCharts = {
            product: productBarChart,
            efficiency: efficiencyChart,
            assembly: assemblyChart
        };

        // 添加窗口大小改变时的自适应
        window.addEventListener('resize', handleResize);

        Logger.log('Charts initialized successfully');
    } catch (error) {
        Logger.error('Error initializing charts:', error);
        // 如果出错，尝试重新初始化
        setTimeout(initCharts, 100);
    }
}

// 清理函数
window.cleanupDashboard = function() {
    // 停止自动刷新
    stopAutoRefresh();

    // 销毁图表实例
    if (window.dashboardCharts) {
        Object.values(window.dashboardCharts).forEach(chart => {
            chart && chart.dispose();
        });
        window.dashboardCharts = null;
    }

    // 移除resize事件监听
    window.removeEventListener('resize', handleResize);
};

// 添加resize处理函数
const handleResize = () => {
    if (window.dashboardCharts) {
        Object.values(window.dashboardCharts).forEach(chart => {
            chart && chart.resize();
        });
    }
};

function analyzeData(data) {
    const insights = [];

    // 分析通过率趋势
    if (data.passRate < 90) {
        insights.push({
            type: 'warning',
            message: `通过率低于90%，建议检查测试流程`,
            action: '查看详情'
        });
    }

    // 分析故障模式
    const faultPattern = analyzeFaultPattern(data.faultTypes);
    if (faultPattern.significant) {
        insights.push({
            type: 'alert',
            message: `发现显著故障模式：${faultPattern.description}`,
            action: '查看分析'
        });
    }

    renderInsights(insights);
}

function renderInsights(insights) {
    const container = document.createElement('div');
    container.className = 'insights-container';
    // ... 渲染智能提示UI
}

function exportDashboardData(format = 'excel') {
    const data = collectDashboardData();

    if (format === 'excel') {
        const worksheet = XLSX.utils.json_to_sheet(data);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Dashboard");
        XLSX.writeFile(workbook, `dashboard_report_${new Date().toISOString().split('T')[0]}.xlsx`);
    } else if (format === 'pdf') {
        // ... PDF导出逻辑
    }
}

function initEfficiencyChart() {
    const efficiencyChart = echarts.init(document.getElementById('efficiencyChart'));
    efficiencyChart.setOption({
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['生产效率', '目标效率', '实际产出'],
            top: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: ['8:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']
        },
        yAxis: [
            {
                type: 'value',
                name: '效率(%)',
                max: 100
            },
            {
                type: 'value',
                name: '产出(台)',
                splitLine: {
                    show: false
                }
            }
        ],
        series: [
            {
                name: '生产效率',
                type: 'line',
                smooth: true,
                data: [85, 90, 88, 95, 87, 92, 89],
                itemStyle: {
                    color: '#409EFF'
                }
            },
            {
                name: '目标效率',
                type: 'line',
                smooth: true,
                data: [90, 90, 90, 90, 90, 90, 90],
                itemStyle: {
                    color: '#67C23A'
                },
                lineStyle: {
                    type: 'dashed'
                }
            },
            {
                name: '实际产出',
                type: 'bar',
                yAxisIndex: 1,
                data: [42, 45, 44, 47, 43, 46, 44],
                itemStyle: {
                    color: '#E6A23C'
                }
            }
        ]
    });

    window.dashboardCharts.efficiency = efficiencyChart;
}