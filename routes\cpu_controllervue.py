"""
CPU控制器Vue版本后端API
遵循Vue版本重构最佳实践规范

功能特点：
1. 完全独立的蓝图，不影响原版本
2. 使用ORM操作替代原生SQL，充分发挥ORM优势
3. 详细的日志标记 [CPU Vue API] 便于调试
4. 优化的错误处理和参数验证
5. 更好的事务管理和数据一致性保证
"""

from flask import Blueprint, jsonify, request, current_app
from INDEX.device import CPUInfoManager
import os
from werkzeug.utils import secure_filename
import requests
from jwt import decode
from database.db_manager import DatabaseManager
from datetime import datetime
from models.test_result import CPUTest, FaultEntry
from models.assembly import CompleteProduct
from models.firmware import DownloadRecord
from sqlalchemy.exc import SQLAlchemyError
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 创建蓝图 - 使用不同的URL前缀避免冲突
cpu_controllervue_bp = Blueprint('cpu_controllervue', __name__)

def get_current_user():
    """获取当前用户信息"""
    token = request.cookies.get('token')
    if token:
        try:
            payload = decode(token, current_app.config['JWT_SECRET'], algorithms=["HS256"])
            return payload.get('username')
        except Exception as e:
            logger.warning(f"[CPU Vue API] Token解析失败: {str(e)}")
            return None
    return None

def validate_required_fields(data, required_fields):
    """验证必需字段"""
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None or str(data[field]).strip() == '':
            missing_fields.append(field)
    return missing_fields

def safe_int_convert(value, default=0):
    """安全的整数转换"""
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def safe_str_convert(value, default='N/A'):
    """安全的字符串转换"""
    if value is None:
        return default
    return str(value).strip() if str(value).strip() else default

# API路由
@cpu_controllervue_bp.route('/get-current-user', methods=['GET'])
def get_current_user_route():
    """获取当前用户信息"""
    try:
        logger.info("[CPU Vue API] 获取当前用户信息")
        username = get_current_user()
        
        return jsonify({
            'success': True,
            'username': username
        })
    except Exception as e:
        logger.error(f"[CPU Vue API] 获取用户信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取用户信息失败'
        }), 500

@cpu_controllervue_bp.route('/device-info', methods=['GET'])
def get_device_info():
    """获取设备信息"""
    try:
        ip = request.args.get('ip')
        if not ip:
            logger.warning("[CPU Vue API] IP地址参数缺失")
            return jsonify({'success': False, 'message': 'IP地址不能为空'}), 400

        logger.info(f"[CPU Vue API] 获取设备信息: IP={ip}")
        
        # 将请求转发到本地代理
        proxy_url = f"http://127.0.0.1:5000/proxy/device-info?ip={ip}"
        response = requests.get(proxy_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"[CPU Vue API] 设备信息获取成功: IP={ip}")
            return result
        else:
            logger.error(f"[CPU Vue API] 代理服务器请求失败: status={response.status_code}")
            return jsonify({'success': False, 'message': '代理服务器请求失败'})

    except requests.Timeout:
        logger.error(f"[CPU Vue API] 设备信息获取超时: IP={ip}")
        return jsonify({'success': False, 'message': '设备连接超时'})
    except Exception as e:
        logger.error(f"[CPU Vue API] 设备信息获取失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取设备信息失败: {str(e)}'})

@cpu_controllervue_bp.route('/check-sn', methods=['GET'])
def check_sn():
    """检查SN号是否已经绑定PCBA (ORM优化版本)"""
    try:
        sn = request.args.get('sn')
        if not sn:
            logger.warning("[CPU Vue API] SN检查: SN号参数缺失")
            return jsonify({
                'success': False,
                'message': '请提供SN号'
            }), 400
        
        sn = sn.strip()
        logger.info(f"[CPU Vue API] 检查SN号: {sn}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            try:
                # 使用ORM查询complete_products表
                result = session.query(CompleteProduct).filter(
                    CompleteProduct.product_sn == sn
                ).first()
                
                if result:
                    logger.info(f"[CPU Vue API] SN号已绑定PCBA: {sn}")
                    return jsonify({
                        'success': True,
                        'exists': True,
                        'message': 'SN号已绑定PCBA'
                    })
                else:
                    logger.info(f"[CPU Vue API] SN号未绑定PCBA: {sn}")
                    return jsonify({
                        'success': True,
                        'exists': False,
                        'message': 'SN号未绑定PCBA'
                    })
                    
            except SQLAlchemyError as e:
                logger.error(f"[CPU Vue API] SN检查数据库错误: {str(e)}")
                session.rollback()
                return jsonify({
                    'success': False,
                    'message': '数据库查询失败'
                }), 500
                
    except Exception as e:
        logger.error(f"[CPU Vue API] SN检查异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500

@cpu_controllervue_bp.route('/get-version-info', methods=['GET'])
def get_version_info():
    """根据加工单号获取软件版本相关信息 (ORM优化版本)"""
    try:
        work_order = request.args.get('work_order') or request.args.get('product_sn')
        
        if not work_order:
            logger.warning("[CPU Vue API] 版本信息查询: 加工单号参数缺失")
            return jsonify({
                'success': False,
                'message': '请提供加工单号'
            }), 400
        
        work_order = work_order.strip()
        logger.info(f"[CPU Vue API] 获取版本信息: 加工单号={work_order}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            try:
                # 使用ORM查询download_record表，获取最新的记录
                result = session.query(DownloadRecord).filter(
                    DownloadRecord.work_order == work_order
                ).order_by(
                    DownloadRecord.create_time.desc()
                ).first()
                
                if result:
                    version_data = {
                        'software_version': result.software_version or '',
                        'build_time': result.build_time or '',
                        'backplane_version': result.backplane_version or '',
                        'io_version': result.io_version or ''
                    }
                    
                    logger.info(f"[CPU Vue API] 版本信息获取成功: {work_order}")
                    return jsonify({
                        'success': True,
                        'data': version_data,
                        'message': '版本信息获取成功'
                    })
                else:
                    logger.info(f"[CPU Vue API] 未找到版本信息: {work_order}")
                    return jsonify({
                        'success': True,
                        'data': {
                            'software_version': '',
                            'build_time': '',
                            'backplane_version': '',
                            'io_version': ''
                        },
                        'message': '未找到相关版本信息'
                    })
                    
            except SQLAlchemyError as e:
                logger.error(f"[CPU Vue API] 版本信息查询数据库错误: {str(e)}")
                session.rollback()
                return jsonify({
                    'success': False,
                    'message': '数据库查询失败'
                }), 500
                
    except Exception as e:
        logger.error(f"[CPU Vue API] 版本信息查询异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'查询版本信息失败：{str(e)}'
        }), 500

@cpu_controllervue_bp.route('/submit-test', methods=['POST'])
def submit_test():
    """提交CPU测试数据 (ORM优化版本)"""
    try:
        data = request.get_json()
        logger.info(f"[CPU Vue API] 接收测试数据提交请求")
        
        # 参数验证
        required_fields = ['tester', 'work_order', 'work_qty', 'pro_model', 'pro_code', 'pro_sn', 'pro_status']
        missing_fields = validate_required_fields(data, required_fields)
        
        if missing_fields:
            logger.warning(f"[CPU Vue API] 缺少必需字段: {missing_fields}")
            return jsonify({
                'success': False,
                'message': f'缺少必需字段: {", ".join(missing_fields)}'
            }), 400
        
        pro_status = safe_int_convert(data.get('pro_status'))
        if pro_status not in [1, 2, 3]:
            logger.warning(f"[CPU Vue API] 无效的产品状态: {pro_status}")
            return jsonify({
                'success': False,
                'message': '产品状态必须为1(新品)、2(维修)或3(返工)'
            }), 400

        pro_sn = safe_str_convert(data['pro_sn'], '').strip()
        work_order = safe_str_convert(data['work_order'], '').strip()
        
        logger.info(f"[CPU Vue API] 处理测试数据: SN={pro_sn}, 工单={work_order}, 状态={pro_status}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            try:
                # 动态处理测试结果（前端只传递启用的测试项目）
                all_test_fields = [
                    'rs485_1', 'rs485_2', 'rs232', 'canbus', 'ethercat',
                    'backplane', 'body_io', 'led_tube', 'led_bulb', 'usb_drive',
                    'sd_slot', 'debug_port', 'net_port', 'dip_switch', 'reset_btn'
                ]
                test_results_numeric = {}
                
                for field in all_test_fields:
                    if field in data:
                        # 如果前端传递了该字段，使用前端的值
                        field_value = data.get(field)
                        # 检查是否为有效值：只有1和2才是有效的测试结果，其他值(包括空字符串)都设为0
                        if field_value in [1, 2, '1', '2']:
                            test_results_numeric[field] = safe_int_convert(field_value, 0)
                        else:
                            # 空字符串或其他无效值设为0(未测试)
                            test_results_numeric[field] = 0
                    else:
                        # 如果前端没有传递该字段，说明该测试项目未启用，设置为0(未测试)
                        test_results_numeric[field] = 0
                
                logger.info(f"[CPU Vue API] 动态测试结果: {test_results_numeric}")
                
                # 收集失败的测试项目（值为2表示失败）
                failed_tests = [k for k, v in test_results_numeric.items() if v == 2]
                test_status = 'pass' if not failed_tests else 'ng'
                
                # 统计有效测试项目（非0的项目）
                active_tests = [k for k, v in test_results_numeric.items() if v != 0]
                logger.info(f"[CPU Vue API] 测试结果统计: 总项目{len(all_test_fields)}个, 启用{len(active_tests)}个, 失败{len(failed_tests)}个, 状态={test_status}")
                
                # 查询现有记录
                existing_record = session.query(CPUTest).filter(
                    CPUTest.pro_sn == pro_sn
                ).order_by(CPUTest.test_time.desc()).first()
                
                # 计算维修和返工次数
                if existing_record:
                    if pro_status == 1:  # 新品不能重复SN
                        logger.warning(f"[CPU Vue API] 新品SN号重复: {pro_sn}")
                        return jsonify({
                            'success': False,
                            'message': f'产品SN号 {pro_sn} 已存在，新品不能使用重复的SN号'
                        }), 400
                    
                    # 更新现有记录
                    maintenance_count = existing_record.maintenance + (1 if pro_status == 2 else 0)
                    rework_count = existing_record.rework + (1 if pro_status == 3 else 0)
                    
                    # 更新记录字段
                    existing_record.tester = safe_str_convert(data['tester'])
                    existing_record.work_order = work_order
                    existing_record.work_qty = safe_int_convert(data['work_qty'])
                    existing_record.pro_model = safe_str_convert(data['pro_model'])
                    existing_record.pro_code = safe_str_convert(data['pro_code'])
                    existing_record.pro_status = pro_status
                    existing_record.pro_batch = safe_str_convert(data.get('pro_batch'))
                    existing_record.remarks = safe_str_convert(data.get('remarks'))
                    existing_record.maintenance = maintenance_count
                    existing_record.rework = rework_count
                    existing_record.device_name = safe_str_convert(data.get('device_name'))
                    existing_record.serial = safe_str_convert(data.get('serial'))
                    existing_record.sw_version = safe_str_convert(data.get('sw_version'))
                    existing_record.build_date = safe_str_convert(data.get('build_date'))
                    existing_record.back_ver = safe_str_convert(data.get('back_ver'))
                    existing_record.high_speed_io_version = safe_str_convert(data.get('high_speed_io_version'))
                    existing_record.test_status = test_status
                    existing_record.test_time = datetime.now()
                    
                    # 新增：处理auto标记信息
                    existing_record.test_auto_info = safe_str_convert(data.get('test_auto_info', '{}'))
                    
                    # 新增：处理M区测试日志
                    existing_record.m_area_test_log = data.get('m_area_test_log')
                    
                    # 更新测试结果
                    for field, value in test_results_numeric.items():
                        setattr(existing_record, field, value)
                    
                    # 维修/返工时清除比对相关字段
                    if pro_status in [2, 3]:
                        existing_record.comparison_time = None
                        existing_record.comparison_result = None
                        existing_record.comparison_user = None
                    
                    action_msg = "更新"
                    logger.info(f"[CPU Vue API] 更新现有记录: SN={pro_sn}, 维修{maintenance_count}次, 返工{rework_count}次")
                
                else:
                    # 创建新记录
                    if pro_status == 1:  # 新品
                        maintenance_count = 0
                        rework_count = 0
                    elif pro_status == 2:  # 维修
                        maintenance_count = 1
                        rework_count = 0
                    else:  # 返工
                        maintenance_count = 0
                        rework_count = 1
                    
                    # 创建新的CPU测试记录
                    new_record = CPUTest(
                        tester=safe_str_convert(data['tester']),
                        work_order=work_order,
                        work_qty=safe_int_convert(data['work_qty']),
                        pro_model=safe_str_convert(data['pro_model']),
                        pro_code=safe_str_convert(data['pro_code']),
                        pro_status=pro_status,
                        pro_sn=pro_sn,
                        pro_batch=safe_str_convert(data.get('pro_batch')),
                        remarks=safe_str_convert(data.get('remarks')),
                        maintenance=maintenance_count,
                        rework=rework_count,
                        device_name=safe_str_convert(data.get('device_name')),
                        serial=safe_str_convert(data.get('serial')),
                        sw_version=safe_str_convert(data.get('sw_version')),
                        build_date=safe_str_convert(data.get('build_date')),
                        back_ver=safe_str_convert(data.get('back_ver')),
                        high_speed_io_version=safe_str_convert(data.get('high_speed_io_version')),
                        test_status=test_status,
                        test_auto_info=safe_str_convert(data.get('test_auto_info', '{}')),  # 新增：auto标记信息
                        m_area_test_log=data.get('m_area_test_log'),  # 新增：M区测试日志
                        **test_results_numeric
                    )
                    
                    session.add(new_record)
                    action_msg = "插入"
                    logger.info(f"[CPU Vue API] 创建新记录: SN={pro_sn}, 维修{maintenance_count}次, 返工{rework_count}次")
                
                # 如果有失败的测试项目，插入故障记录
                if failed_tests:
                    logger.info(f"[CPU Vue API] 插入故障记录: {failed_tests}")
                    
                    # 使用ORM创建故障记录
                    fault_record = FaultEntry(
                        tester=safe_str_convert(data['tester']),
                        work_order=work_order,
                        work_qty=safe_int_convert(data['work_qty']),
                        pro_model=safe_str_convert(data['pro_model']),
                        pro_code=safe_str_convert(data['pro_code']),
                        pro_status=pro_status,
                        pro_sn=pro_sn,
                        pro_batch=safe_str_convert(data.get('pro_batch')),
                        remarks=safe_str_convert(data.get('remarks')),
                        rework=rework_count,
                        maintenance=maintenance_count,
                        
                        # 故障类型字段 (0=无故障, 2=有故障)
                        burn_err=0,
                        power_err=0,
                        backplane_err=2 if 'backplane' in failed_tests else 0,
                        body_io_err=2 if 'body_io' in failed_tests else 0,
                        led_tube_err=2 if 'led_tube' in failed_tests else 0,
                        led_bulb_err=2 if 'led_bulb' in failed_tests else 0,
                        net_port_err=2 if 'net_port' in failed_tests else 0,
                        rs485_1_err=2 if 'rs485_1' in failed_tests else 0,
                        rs485_2_err=2 if 'rs485_2' in failed_tests else 0,
                        rs232_err=2 if 'rs232' in failed_tests else 0,
                        canbus_err=2 if 'canbus' in failed_tests else 0,
                        ethercat_err=2 if 'ethercat' in failed_tests else 0,
                        usb_drive_err=2 if 'usb_drive' in failed_tests else 0,
                        sd_slot_err=2 if 'sd_slot' in failed_tests else 0,
                        debug_port_err=2 if 'debug_port' in failed_tests else 0,
                        dip_switch_err=2 if 'dip_switch' in failed_tests else 0,
                        reset_btn_err=2 if 'reset_btn' in failed_tests else 0,
                        other_err='N/A'
                    )
                    
                    session.add(fault_record)
                    logger.info(f"[CPU Vue API] 故障记录ORM插入成功: 失败项目={len(failed_tests)}个, 故障总数={fault_record.fault_count}")

                # 提交事务
                session.commit()
                
                # 构建返回消息
                msg_parts = [f'测试信息{action_msg}成功']
                if failed_tests:
                    msg_parts.append(f'故障项目: {", ".join(failed_tests)}')
                if maintenance_count > 0:
                    msg_parts.append(f'维修{maintenance_count}次')
                if rework_count > 0:
                    msg_parts.append(f'返工{rework_count}次')
                
                success_message = '，'.join(msg_parts)
                logger.info(f"[CPU Vue API] 测试数据处理完成: {success_message}")
                
                return jsonify({
                    'success': True,
                    'message': success_message
                })
                
            except SQLAlchemyError as e:
                logger.error(f"[CPU Vue API] 数据库操作错误: {str(e)}")
                session.rollback()
                return jsonify({
                    'success': False,
                    'message': '数据库操作失败，请重试'
                }), 500
        
    except Exception as e:
        logger.error(f"[CPU Vue API] 测试数据提交异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交失败：{str(e)}'
        }), 500

@cpu_controllervue_bp.route('/update-m-area-log', methods=['POST'])
def update_m_area_log():
    """更新指定SN的M区测试日志"""
    try:
        data = request.get_json()
        logger.info(f"[CPU Vue API] 接收M区日志更新请求")
        
        # 参数验证
        pro_sn = data.get('pro_sn')
        m_area_log = data.get('m_area_log')
        
        if not pro_sn:
            logger.warning("[CPU Vue API] M区日志更新: SN号参数缺失")
            return jsonify({
                'success': False,
                'message': '请提供SN号'
            }), 400
            
        if not m_area_log:
            logger.warning("[CPU Vue API] M区日志更新: 日志数据缺失")
            return jsonify({
                'success': False,
                'message': '请提供M区日志数据'
            }), 400
        
        pro_sn = pro_sn.strip()
        logger.info(f"[CPU Vue API] 更新M区日志: SN={pro_sn}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            try:
                # 查询最新的CPU测试记录
                cpu_record = session.query(CPUTest).filter(
                    CPUTest.pro_sn == pro_sn
                ).order_by(CPUTest.test_time.desc()).first()
                
                if not cpu_record:
                    logger.warning(f"[CPU Vue API] 未找到SN对应的CPU测试记录: {pro_sn}")
                    return jsonify({
                        'success': False,
                        'message': f'未找到SN号 {pro_sn} 对应的测试记录'
                    }), 404
                
                # 更新M区测试日志
                cpu_record.m_area_test_log = m_area_log
                session.commit()
                
                logger.info(f"[CPU Vue API] M区日志更新成功: SN={pro_sn}")
                return jsonify({
                    'success': True,
                    'message': 'M区测试日志更新成功'
                })
                
            except SQLAlchemyError as e:
                logger.error(f"[CPU Vue API] M区日志更新数据库错误: {str(e)}")
                session.rollback()
                return jsonify({
                    'success': False,
                    'message': '数据库操作失败'
                }), 500
                
    except Exception as e:
        logger.error(f"[CPU Vue API] M区日志更新异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新失败：{str(e)}'
        }), 500

@cpu_controllervue_bp.route('/get-m-area-log', methods=['GET'])
def get_m_area_log():
    """获取指定SN的M区测试日志"""
    try:
        pro_sn = request.args.get('pro_sn')
        
        if not pro_sn:
            logger.warning("[CPU Vue API] M区日志查询: SN号参数缺失")
            return jsonify({
                'success': False,
                'message': '请提供SN号'
            }), 400
        
        pro_sn = pro_sn.strip()
        logger.info(f"[CPU Vue API] 查询M区日志: SN={pro_sn}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            try:
                # 查询最新的CPU测试记录
                cpu_record = session.query(CPUTest).filter(
                    CPUTest.pro_sn == pro_sn
                ).order_by(CPUTest.test_time.desc()).first()
                
                if not cpu_record:
                    logger.info(f"[CPU Vue API] 未找到SN对应的CPU测试记录: {pro_sn}")
                    return jsonify({
                        'success': False,
                        'message': f'未找到SN号 {pro_sn} 对应的测试记录'
                    }), 404
                
                # 返回M区测试日志
                m_area_log = cpu_record.m_area_test_log
                has_log = m_area_log is not None and m_area_log != {}
                
                logger.info(f"[CPU Vue API] M区日志查询完成: SN={pro_sn}, 有日志={has_log}")
                return jsonify({
                    'success': True,
                    'has_log': has_log,
                    'log_data': m_area_log if has_log else None,
                    'test_time': cpu_record.test_time.strftime('%Y-%m-%d %H:%M:%S') if cpu_record.test_time else None,
                    'tester': cpu_record.tester
                })
                
            except SQLAlchemyError as e:
                logger.error(f"[CPU Vue API] M区日志查询数据库错误: {str(e)}")
                session.rollback()
                return jsonify({
                    'success': False,
                    'message': '数据库查询失败'
                }), 500
                
    except Exception as e:
        logger.error(f"[CPU Vue API] M区日志查询异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500 