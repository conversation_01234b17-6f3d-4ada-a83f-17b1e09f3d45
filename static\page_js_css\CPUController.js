const PROXY_BASE_URL = 'http://127.0.0.1:5000/proxy';

// 在文件开头添加防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 修改全局状态管理，添加 PCBA 检查标志
if (typeof window.cpuControllerState === 'undefined') {
    window.cpuControllerState = {
        requiresPcbaCheck: true  // 默认需要检查
    };
}

async function initCPUControllerPage() {
    const cpuControllerContent = document.getElementById('cpu-controller-content');
    cpuControllerContent.innerHTML = `
        <style>
            /* 保持原有样式 ... */
            
            .quick-button {
                padding: 6px 12px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.3s;
                margin-left: 10px;
            }

            .quick-button:hover {
                background-color: #45a049;
            }

            .quick-button:active {
                background-color: #3d8b40;
            }

            .card-header {
                display: flex;
                align-items: center;
                justify-content: flex-start;
            }

            .card-title {
                margin-right: auto;
            }

            .header-buttons {
                display: flex;
                gap: 10px;
            }
        </style>
        <div class="container">
            <form id="testForm">
                ${createBasicInfoForm()}

                <div class="separator"></div>

                <div class="card-grid">
                    <div class="card device-info-card">
                        <div class="card-header">
                            <h2 class="card-title">设备信息</h2>
                        </div>
                        <div class="card-content">
                            <div class="device-info-grid">
                                <div class="form-group full-width">
                                    <label for="ipAddress">IP 地址</label>
                                    <select id="ipAddress">
                                        <option value="*************">*************</option>
                                        <option value="*************">*************</option>
                                        <option value="*************">*************</option>
                                    </select>
                                </div>
                                <div class="form-group-row">
                                    <div class="form-group">
                                        <label for="specifiedVersion">指定版本</label>
                                        <input id="specifiedVersion" placeholder="根据SN自动获取软件版本" readonly class="readonly-field">
                                    </div>
                                    <div class="form-group">
                                        <label for="specifiedTime">指定日期</label>
                                        <input id="specifiedTime" placeholder="根据SN自动获取构建日期" readonly class="readonly-field">
                                    </div>
                                </div>
                                <div class="form-group-row">
                                    <div class="form-group">
                                        <label for="specifiedBackplane">指定背板</label>
                                        <input id="specifiedBackplane" placeholder="根据SN自动获取背板版本" readonly class="readonly-field">
                                    </div>
                                    <div class="form-group">
                                        <label for="specifiedHighSpeed">指定高速</label>
                                        <input id="specifiedHighSpeed" placeholder="根据SN自动获取高速IO版本" readonly class="readonly-field">
                                    </div>
                                </div>
                                <div class="form-group-row">
                                    <div class="form-group">
                                        <label for="odmInfo">ODM信息</label>
                                        <input id="odmInfo" placeholder="输入ODM信息">
                                    </div>
                                    <div class="form-group">
                                        <label for="deviceManufacturer">设备厂商</label>
                                        <input id="deviceManufacturer" placeholder="厂商信息">
                                    </div>
                                </div>
                                <div class="form-group-row">
                                    <div class="form-group">
                                        <label for="deviceName">设备名称</label>
                                        <input id="deviceName">
                                    </div>
                                    <div class="form-group">
                                        <label for="serialNumber">出厂序号</label>
                                        <input id="serialNumber">
                                    </div>
                                </div>
                                <div class="form-group-row">
                                    <div class="form-group">
                                        <label for="softwareVersion">软件版本</label>
                                        <input id="softwareVersion">
                                    </div>
                                    <div class="form-group">
                                        <label for="buildDate">构建日期</label>
                                        <input id="buildDate" >
                                    </div>
                                </div>
                                <div class="form-group-row">
                                    <div class="form-group">
                                        <label for="backplaneVersion">背板总线版本</label>
                                        <input id="backplaneVersion" >
                                    </div>
                                    <div class="form-group">
                                        <label for="highSpeedIOVersion">高速IO版本</label>
                                        <input id="highSpeedIOVersion">
                                    </div>
                                </div>
                                <div class="form-group full-width">
                                    <label for="macAddress">MAC地址</label>
                                    <textarea id="macAddress" rows="3"></textarea>
                                </div>
                                <div class="form-group full-width">
                                    <label for="mAreaData">M区数据</label>
                                    <textarea id="mAreaData"  rows="3"></textarea>
                                </div>
                            </div>
                            <div class="button-group">
                                <button type="button">查询</button>
                                <button type="button">清除</button>
                                <button type="button">重启</button>
                                <button type="button">程序加载</button>
                                <button type="button">恢复出厂设置</button>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">测试项目</h2>
                            <div class="header-buttons">
                                <button type="button" id="quickQuery" class="quick-button">快捷查询</button>
                                <button type="button" id="quickSubmit" class="quick-button">快捷提交</button>
                                <button type="button" id="quickReset" class="quick-button">快捷恢复出厂设置</button>
                                <button type="button" id="quickProgramLoad" class="quick-button">快捷程序加载</button>
                            </div>
                        </div>
                        <div class="card-content">
                            <table>
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="checkbox-container">
                                                <input type="checkbox" id="selectAll">
                                                <label for="selectAll">全选</label>
                                            </div>
                                        </th>
                                        <th>序号</th>
                                        <th>测试项目</th>
                                        <th>测试结果</th>
                                    </tr>
                                </thead>
                                <tbody id="testItemsBody">
                                    <!-- 测试项目将在这里动态插入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <button type="submit" class="submit-button">提交测试信息</button>
            </form>
        </div>
    `;

    const form = document.getElementById('testForm');
    
    // 创建一个处理提交的函数，这样可以用于添加和移除事件监听
    async function handleSubmit(e) {
        e.preventDefault();
        
        // 检查是否是通过按钮点击触发的提交
        const submitButton = document.querySelector('.submit-button');
        if (e.submitter !== submitButton) {
            return;
        }

        // 验证必填字段
        const requiredFields = {
            'tester': '测试人员',
            'orderNumber': '加工单号',
            'productionQuantity': '生产数',
            'productCode': '产品编码',
            'productModel': '产品型号',
            'productStatus': '产品状态',
            'productSN': '产品SN号',
            'snByteCount': '产品SN号字节数',
            'serialNumber': '出厂序号'
        };

        // 检查所有必填字段
        for (const [fieldId, fieldName] of Object.entries(requiredFields)) {
            const field = document.getElementById(fieldId);
            const value = field.value.trim();
            
            if (!value) {
                await SweetAlert.warning(`${fieldName}不能为空！`);
                field.focus();
                return;
            }
        }

        // 特别检查出厂序号不能为 "-"
        const serialNumber = document.getElementById('serialNumber').value.trim();
        if (serialNumber === '-') {
            await SweetAlert.warning('出厂序号不能为 "-"');
            document.getElementById('serialNumber').focus();
            return;
        }

        // 验证产品 SN 号
        const productSN = document.getElementById('productSN').value.trim();
        const snByteCount = parseInt(document.getElementById('snByteCount').value);
        
        if (isNaN(snByteCount)) {
            await SweetAlert.error('请输入有效的 SN 号字节数');
            return;
        }

        if (productSN.length !== snByteCount) {
            await SweetAlert.error(`产品 SN 号的长度必须为 ${snByteCount} 个字节，当前长度为 ${productSN.length} 个字节`);
            return;
        }

        // 先获取并打印产品状态值，用于调试
        const productStatus = document.getElementById('productStatus').value;
        console.log('产品状态值:', productStatus);
        
        // 收集表单数据
        const formData = {
            // 基本信息
            tester: document.getElementById('tester').value,
            test_time: document.getElementById('testTime').value,
            work_order: document.getElementById('orderNumber').value.trim(),
            work_qty: document.getElementById('productionQuantity').value.trim(),
            pro_model: document.getElementById('productModel').value.trim(),
            pro_code: document.getElementById('productCode').value.trim(),
            pro_sn: productSN,
            pro_batch: document.getElementById('batchNumber').value || 'N/A',
            remarks: document.getElementById('remarks').value || 'N/A',
            
            // 设备信息 - 修改这里的字段名
            device_name: document.getElementById('deviceName').value || 'N/A',
            serial: document.getElementById('serialNumber').value || 'N/A',
            sw_version: document.getElementById('softwareVersion').value || 'N/A',
            back_ver: document.getElementById('backplaneVersion').value || 'N/A',
            high_speed_io_version: document.getElementById('highSpeedIOVersion').value || 'N/A',  // 确保字段名完全匹配

            // 修改构建日期的处理方式，直接使用原始值
            'build_date': document.getElementById('buildDate').value || 'N/A',
            
            // 测试结果 - 使用英文代码
            rs485_1_result: document.getElementById(`test-result-0`).value,
            rs485_2_result: document.getElementById(`test-result-1`).value,
            rs232_result: document.getElementById(`test-result-2`).value,
            canbus_result: document.getElementById(`test-result-3`).value,
            ethercat_result: document.getElementById(`test-result-4`).value,
            backplane_result: document.getElementById(`test-result-5`).value,
            body_io_result: document.getElementById(`test-result-6`).value,
            led_tube_result: document.getElementById(`test-result-7`).value,
            led_bulb_result: document.getElementById(`test-result-8`).value,
            usb_drive_result: document.getElementById(`test-result-9`).value,
            sd_slot_result: document.getElementById(`test-result-10`).value,
            debug_port_result: document.getElementById(`test-result-11`).value,
            net_port_result: document.getElementById(`test-result-12`).value,
            dip_switch_result: document.getElementById(`test-result-13`).value,
            reset_btn_result: document.getElementById(`test-result-14`).value
        };

        // 修改产品状态映射
        const productStatusMap = {
            'new': 1,      // 新品
            'used': 2,     // 维修
            'refurbished': 3  // 返工
        };
        
        // 设置产品状态
        formData.pro_status = productStatusMap[productStatus];
        console.log('转换后的产品状态值:', formData.pro_status);
        
        if (!formData.pro_status) {
            console.log('产品状态映射失败');
            await SweetAlert.warning('请选择产品状态！');
            return;
        }

        // 设置维修和返工次数
        formData.maintenance = formData.pro_status === 2 ? 1 : 0;
        formData.rework = formData.pro_status === 3 ? 1 : 0;

        // 转换测试结果为数字格式并检查是否全部通过
        const testResults = {
            rs485_1: formData.rs485_1_result === '通过' ? 1 : 2,
            rs485_2: formData.rs485_2_result === '通过' ? 1 : 2,
            rs232: formData.rs232_result === '通过' ? 1 : 2,
            canbus: formData.canbus_result === '通过' ? 1 : 2,
            ethercat: formData.ethercat_result === '通过' ? 1 : 2,
            backplane: formData.backplane_result === '通过' ? 1 : 2,
            body_io: formData.body_io_result === '通过' ? 1 : 2,
            led_tube: formData.led_tube_result === '通过' ? 1 : 2,
            led_bulb: formData.led_bulb_result === '通过' ? 1 : 2,
            usb_drive: formData.usb_drive_result === '通过' ? 1 : 2,
            sd_slot: formData.sd_slot_result === '通过' ? 1 : 2,
            debug_port: formData.debug_port_result === '通过' ? 1 : 2,
            net_port: formData.net_port_result === '通过' ? 1 : 2,
            dip_switch: formData.dip_switch_result === '通过' ? 1 : 2,
            reset_btn: formData.reset_btn_result === '通过' ? 1 : 2
        };

        // 添加测试结果到表单数据
        Object.assign(formData, testResults);

        // 确定整体测试状态
        const allTestsPassed = Object.values(testResults).every(result => result === 1);
        formData.test_status = allTestsPassed ? 'pass' : 'ng';

        // 在提交前打印数据，用于调试
        console.log('提交的数据：', formData);
        
        try {
            const response = await fetch('/api/cpu-controller/submit-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                await SweetAlert.success('测试信息提交成功！');
                
                // 保存需要保留的字段值
                const savedValues = {
                    // 基本信息卡片需要保留的字段
                    tester: document.getElementById('tester').value,
                    orderNumber: document.getElementById('orderNumber').value,
                    productionQuantity: document.getElementById('productionQuantity').value,
                    productCode: document.getElementById('productCode').value,
                    productModel: document.getElementById('productModel').value,
                    productStatus: document.getElementById('productStatus').value,
                    batchNumber: document.getElementById('batchNumber').value,
                    snByteCount: document.getElementById('snByteCount').value,
                    remarks: document.getElementById('remarks').value,
                    
                    // 设备信息卡片需要保留的字段
                    specifiedVersion: document.getElementById('specifiedVersion').value,
                    specifiedTime: document.getElementById('specifiedTime').value,
                    specifiedBackplane: document.getElementById('specifiedBackplane').value,
                    specifiedHighSpeed: document.getElementById('specifiedHighSpeed').value,
                    odmInfo: document.getElementById('odmInfo').value,
                    ipAddress: document.getElementById('ipAddress').value
                };
                
                // 重置表单
                document.getElementById('testForm').reset();
                
                // 移除所有测试失败的背景色
                document.querySelectorAll('.checkbox-container.test-failed').forEach(container => {
                    container.classList.remove('test-failed');
                });
                
                // 恢复基本信息字段的值和样式
                const basicInfoFields = [
                    'tester', 'orderNumber', 'productionQuantity', 'productCode', 'productModel',
                    'productStatus', 'batchNumber', 'snByteCount', 'remarks'
                ];
                
                basicInfoFields.forEach(fieldId => {
                    const element = document.getElementById(fieldId);
                    if (element && savedValues[fieldId]) {
                        element.value = savedValues[fieldId];
                        if (savedValues[fieldId].trim()) {
                            element.classList.add('input-has-value');
                        }
                    }
                });

                // 恢复设备信息字段的值（不添加样式）
                const deviceInfoFields = ['specifiedVersion', 'specifiedTime', 'specifiedBackplane', 'specifiedHighSpeed', 'odmInfo', 'ipAddress'];
                deviceInfoFields.forEach(fieldId => {
                    const element = document.getElementById(fieldId);
                    if (element && savedValues[fieldId]) {
                        element.value = savedValues[fieldId];
                    }
                });

                // 清除他设备信息字段的值和样式
                const clearDeviceInfoFields = [
                    'deviceManufacturer', 'deviceName', 'serialNumber',
                    'softwareVersion', 'buildDate', 'backplaneVersion',
                    'highSpeedIOVersion', 'macAddress', 'mAreaData'
                ];
                clearDeviceInfoFields.forEach(fieldId => {
                    const element = document.getElementById(fieldId);
                    if (element) {
                        element.value = ''; // 清空值
                        element.classList.remove('input-has-value'); // 移除绿色背景
                    }
                });

                // 专门处理产品SN号输入框
                const productSNInput = document.getElementById('productSN');
                if (productSNInput) {
                    productSNInput.value = ''; // 清空值
                    productSNInput.classList.remove('input-has-value'); // 移除绿色背景
                    
                    // 使用 setTimeout 确保在UI更新后设置焦点
                    setTimeout(() => {
                        productSNInput.focus(); // 聚焦到产品SN号输入框
                    }, 0);
                }

                // 暂时禁用所有提交按钮，直到用户输入新的SN
                const submitButton = document.querySelector('.submit-button');
                const quickSubmitButton = document.getElementById('quickSubmit');

                if (submitButton) submitButton.disabled = true;
                if (quickSubmitButton) quickSubmitButton.disabled = true;

                // 添加一次性事件监听器，当用户输入时重新启用按钮
                productSNInput.addEventListener('input', function onFirstInput() {
                    if (submitButton) submitButton.disabled = false;
                    if (quickSubmitButton) quickSubmitButton.disabled = false;
                    
                    // 移除监听器，避免重复执行
                    productSNInput.removeEventListener('input', onFirstInput);
                });
                
            } else {
                await SweetAlert.error(result.message);
            }
        } catch (error) {
            await SweetAlert.error('请检查网络连接');
            console.error('提交错误：', error);
        }
    }

    // 先移除可能存在的提交事件监听器
    form.removeEventListener('submit', handleSubmit);
    
    // 克隆和替换表单
    const newForm = form.cloneNode(true);
    form.parentNode.replaceChild(newForm, form);
    
    // 获取新表的引用 - 只声明一次
    const formRef = document.getElementById('testForm');
    
    // 添加事件监听器
    formRef.addEventListener('submit', handleSubmit);
    
    // 添加表单输入框的回车键处理
    formRef.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
    });

        // Call setupFormValidation after form is created
        setupFormValidation();
        
    // 初始化测试项目
    const testItems = [
        { name: "RS485_1通信", code: "rs485_1" },
        { name: "RS485_2通信", code: "rs485_2" },
        { name: "RS232通信", code: "rs232" },
        { name: "CANbus通信", code: "canbus" },
        { name: "EtherCAT通信", code: "ethercat" },
        { name: "Backplane Bus通信", code: "backplane_bus" },
        { name: "Body I/O入输出", code: "body_io" },
        { name: "Led数码管", code: "led_tube" },
        { name: "Led灯珠", code: "led_bulb" },
        { name: "U盘接口", code: "usb_drive" },
        { name: "SD", code: "sd_slot" },
        { name: "调试串口", code: "debug_port" },
        { name: "网口", code: "net_port" },
        { name: "拨码开", code: "dip_switch" },
        { name: "复位按钮", code: "reset_btn" }
    ];

    initializeTestItems(testItems);

    // 在页面初始化完成后获取并填充当前用户
    try {
        const response = await fetch('/api/cpu-controller/get-current-user');
        const data = await response.json();
        
        if (data.success && data.username) {
            const testerInput = document.getElementById('tester');
            if (testerInput) {
                testerInput.value = data.username;
            }
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
    }

    // 修改工单查询函数
    async function queryOrderInfo(orderNumber) {
        if (!orderNumber) {
            // 清空相关字段
            const fieldsToReset = ['productionQuantity', 'productCode', 'productModel', 'batchNumber', 'snByteCount'];
            fieldsToReset.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.value = '';
                    element.classList.remove('input-has-value');
                }
            });
            
            // 清空版本信息字段
            document.getElementById('specifiedVersion').value = '';
            document.getElementById('specifiedTime').value = '';
            document.getElementById('specifiedBackplane').value = '';
            document.getElementById('specifiedHighSpeed').value = '';
            
            // 重置PCBA检查标志为默认值
            window.cpuControllerState.requiresPcbaCheck = true;
            return;
        }

        try {
            const response = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNumber)}`);
            const data = await response.json();

            if (data.success && data.order) {
                // 新增判断：只有测试前阶段完成才允许后续操作
                if (!data.order.test_stage_completed) {
                    // 清空并移除绿色背景
                    const fieldsToReset = ['productionQuantity', 'productCode', 'productModel', 'batchNumber', 'snByteCount'];
                    fieldsToReset.forEach(fieldId => {
                        const element = document.getElementById(fieldId);
                        if (element) {
                            element.value = '';
                            element.classList.remove('input-has-value');
                        }
                    });
                    // 工单号无效，移除绿色背景
                    const orderNumberInput = document.getElementById('orderNumber');
                    if (orderNumberInput) {
                        orderNumberInput.classList.remove('input-has-value');
                    }
                    await Swal.fire({
                        title: '提示',
                        text: '该工单未完成测试前阶段外观检验',
                        icon: 'warning',
                        confirmButtonText: '确定'
                    });
                    // 重置PCBA检查标志为默认值
                    window.cpuControllerState.requiresPcbaCheck = true;
                    return;
                }
                // 自动填充字段
                const fieldsToFill = {
                    'productionQuantity': data.order.ord_productionQuantity,
                    'productCode': data.order.ord_productCode,
                    'productModel': data.order.ord_productModel,
                    'batchNumber': data.order.ord_probatch,
                    'snByteCount': data.order.ord_snlenth
                };

                // 填充字段并添加绿色背景
                for (const [fieldId, value] of Object.entries(fieldsToFill)) {
                    const element = document.getElementById(fieldId);
                    if (element && value) {
                        element.value = value;
                        element.classList.add('input-has-value');
                    }
                }

                // 设置PCBA检查标志
                window.cpuControllerState.requiresPcbaCheck = data.order.ord_requires_pcba_check !== false;
                
                // 可选：显示是否需要PCBA检查的提示
                const pcbaCheckStatus = window.cpuControllerState.requiresPcbaCheck ? 
                    '需要PCBA绑定' : '无需PCBA绑定';
                console.log(`工单 ${orderNumber}: ${pcbaCheckStatus}`);

                // 工单号有效，添加绿色背景
                const orderNumberInput = document.getElementById('orderNumber');
                if (orderNumberInput) {
                    orderNumberInput.classList.add('input-has-value');
                }

                // 自动获取版本信息
                await fetchVersionInfoByWorkOrder(orderNumber);
            } else {
                // 清空并移除绿色背景
                const fieldsToReset = ['productionQuantity', 'productCode', 'productModel', 'batchNumber', 'snByteCount'];
                fieldsToReset.forEach(fieldId => {
                    const element = document.getElementById(fieldId);
                    if (element) {
                        element.value = '';
                        element.classList.remove('input-has-value');
                    }
                });

                // 工单号无效，移除绿色背景
                const orderNumberInput = document.getElementById('orderNumber');
                if (orderNumberInput) {
                    orderNumberInput.classList.remove('input-has-value');
                }
                
                // 清空版本信息字段
                document.getElementById('specifiedVersion').value = '';
                document.getElementById('specifiedTime').value = '';
                document.getElementById('specifiedBackplane').value = '';
                document.getElementById('specifiedHighSpeed').value = '';
                
                // 显示提示信息
                if (data.message) {
                    await Swal.fire({
                        title: '提示',
                        text: data.message,
                        icon: 'warning',
                        confirmButtonText: '确定'
                    });
                }
                
                // 重置PCBA检查标志为默认值
                window.cpuControllerState.requiresPcbaCheck = true;
            }
        } catch (error) {
            console.error('获取工单信息失败:', error);
            
            // 清空版本信息字段
            document.getElementById('specifiedVersion').value = '';
            document.getElementById('specifiedTime').value = '';
            document.getElementById('specifiedBackplane').value = '';
            document.getElementById('specifiedHighSpeed').value = '';
            
            await Swal.fire({
                title: '错误',
                text: '获取工单信息失败',
                icon: 'error',
                confirmButtonText: '确定'
            });
            
            // 重置PCBA检查标志为默认值
            window.cpuControllerState.requiresPcbaCheck = true;
        }
    }

    // 修改工单号输入框的事件监听
    const orderNumberInput = document.getElementById('orderNumber');
    if (orderNumberInput) {
        // 使用防抖包装查询函数，设置500ms延迟
        const debouncedQuery = debounce(queryOrderInfo, 500);
        
        orderNumberInput.addEventListener('input', function() {
            const orderNumber = this.value.trim();
            debouncedQuery(orderNumber);
        });
    }

    // 找到产品编码输入框并添加事件监听
    const productCodeInput = document.getElementById('productCode');
    if (productCodeInput) {
        productCodeInput.addEventListener('input', async function() {
            const code = this.value.trim();
            if (code) {
                try {
                    const response = await fetch(`/api/product-mapping/get-model?code=${encodeURIComponent(code)}`);
                    const data = await response.json();
                    
                    if (data.success && data.model) {
                        // 自动填充产品型号
                        const modelInput = document.getElementById('productModel');
                        if (modelInput) {
                            modelInput.value = data.model;
                            modelInput.classList.add('input-has-value');
                        }
                        // 产品编码有效，添加绿色背景
                        this.classList.add('input-has-value');

                        // 获取并显示产品系列信息
                        const seriesResponse = await fetch(`/api/product-mapping/get-series?code=${encodeURIComponent(code)}`);
                        const seriesData = await seriesResponse.json();
                        if (seriesData.success && seriesData.series) {
                            // 可以在这里添加产品系列的显示逻辑
                            console.log('产品系列:', seriesData.series);
                        }
                    } else {
                        // 产品编码无效，移除绿色背景
                        this.classList.remove('input-has-value');
                        // 清空产品型号
                        const modelInput = document.getElementById('productModel');
                        if (modelInput) {
                            modelInput.value = '';
                            modelInput.classList.remove('input-has-value');
                        }
                    }
                } catch (error) {
                    console.error('获取产品型号失败:', error);
                }
            } else {
                // 输入框为空时移除绿色背景
                this.classList.remove('input-has-value');
                // 清空产品型号
                const modelInput = document.getElementById('productModel');
                if (modelInput) {
                    modelInput.value = '';
                    modelInput.classList.remove('input-has-value');
                }
            }
        });
    }

    // 定义按钮事件处理函数
    async function handleQueryClick() {
        const ipAddress = document.getElementById('ipAddress').value;
        try {
            // 收集所有错误和提示
            const errors = [];

            // 通过本地代理获取设备信息
            const deviceResponse = await fetch(`${PROXY_BASE_URL}/device-info?ip=${ipAddress}`);
            if (!deviceResponse.ok) {
                throw new Error('设备信息获取失败');
            }
            const deviceData = await deviceResponse.json();
            
            if (!deviceData.success) {
                throw new Error(deviceData.message || '设备信息获取失败');
            }

            // 获取网络信息
            const networkInfo = {};
            for (const interface of ['eth0', 'eth1', 'eth2']) {
                const networkResponse = await fetch(`${PROXY_BASE_URL}/network-info/${interface}?ip=${ipAddress}`);
                if (networkResponse.ok) {
                    const networkData = await networkResponse.json();
                    if (networkData.success) {
                        networkInfo[interface] = networkData.data;
                    }
                }
            }

            // 获内存信息
            let memoryData = null;
            const memoryResponse = await fetch(`${PROXY_BASE_URL}/memory-info?ip=${ipAddress}`);
            if (memoryResponse.ok) {
                const memoryResult = await memoryResponse.json();
                if (memoryResult.success) {
                    memoryData = memoryResult.data;
                    // 检查内存数据
                    if (memoryData.some(value => value !== 0)) {
                        errors.push('清除M区数据');
                    }
                }
            }

            // 检查序列号
            if (deviceData.data.serialnumber === '-') {
                errors.push('核对Mac地址是否正确烧录');
            }
            
            // 检查背板版本
            if (deviceData.data.busversion === 'V0.0.0.0') {
                errors.push('背板异常，重新烧录背板程序');
            }
            
            // 检查软件版本
            const specifiedVersion = document.getElementById('specifiedVersion').value.trim();
            if (specifiedVersion && specifiedVersion !== deviceData.data.softwareversion) {
                errors.push(`软件版本错误 预期: ${specifiedVersion} 实际: ${deviceData.data.softwareversion}`);
            }
            
            // 检查构建日期
            const specifiedTime = document.getElementById('specifiedTime').value.trim();
            if (specifiedTime && specifiedTime !== deviceData.data.builddate) {
                errors.push(`构建日期错误 预期: ${specifiedTime} 实际: ${deviceData.data.builddate}`);
            }
            
            // 检查背板版本
            const specifiedBackplane = document.getElementById('specifiedBackplane').value.trim();
            if (specifiedBackplane && specifiedBackplane !== deviceData.data.busversion) {
                errors.push(`背板版本错误 预期: ${specifiedBackplane} 实际: ${deviceData.data.busversion}`);
            }
            
            // 检查高速IO版本
            const specifiedHighSpeed = document.getElementById('specifiedHighSpeed').value.trim();
            // 对设备返回的高速IO版本进行统一处理：如果是"-"，在前面加空格以匹配显示格式
            const normalizedDeviceHighSpeed = (deviceData.data.higspeedIOversion === '-') ? ' -' : deviceData.data.higspeedIOversion;
            const normalizedSpecifiedHighSpeed = (specifiedHighSpeed === '-') ? ' -' : specifiedHighSpeed;
            if (normalizedSpecifiedHighSpeed && normalizedSpecifiedHighSpeed !== normalizedDeviceHighSpeed) {
                errors.push(`高速IO版本错误 预期: ${normalizedSpecifiedHighSpeed} 实际: ${normalizedDeviceHighSpeed}`);
            }
            
            // 检查ODM信息
            const odmInfo = document.getElementById('odmInfo').value.trim();
            if (odmInfo && odmInfo !== deviceData.data.devicename) {
                errors.push(`ODM信息错误 预期: ${odmInfo} 实际: ${deviceData.data.devicename}`);
            }

            // 更新表单字段
            updateFormFields(deviceData.data, networkInfo, memoryData);

            // 如果有错误，显示所有错误信息
            if (errors.length > 0) {
                // 使用 Swal.fire 来支持 HTML 格式显示
                await Swal.fire({
                    title: '设备检查结果',
                    html: errors.map(error => `• ${error}`).join('<br>'),
                    icon: 'error',
                    confirmButtonText: '确定'
                });
            } else {
                await SweetAlert.success('设备信息一致');
            }

        } catch (error) {
            console.error('查询设备信息失败:', error);
            await SweetAlert.error(error.message || '查询失败');
        }
    }

    function handleClearClick() {
        clearFormFields();
        SweetAlert.info('已清除所有数据');
    }

    // 获取所有按钮
    const queryButton = document.querySelector('.button-group button:nth-child(1)');
    const clearButton = document.querySelector('.button-group button:nth-child(2)');
    const rebootButton = document.querySelector('.button-group button:nth-child(3)');
    const loadProgramButton = document.querySelector('.button-group button:nth-child(4)');
    const resetButton = document.querySelector('.button-group button:nth-child(5)');

    // 移除旧的事件监听器
    if (queryButton) {
        const newQueryButton = queryButton.cloneNode(true);
        queryButton.parentNode.replaceChild(newQueryButton, queryButton);
        newQueryButton.addEventListener('click', handleQueryClick);
    }

    if (clearButton) {
        const newClearButton = clearButton.cloneNode(true);
        clearButton.parentNode.replaceChild(newClearButton, clearButton);
        newClearButton.addEventListener('click', handleClearClick);
    }

    if (rebootButton) {
        const newRebootButton = rebootButton.cloneNode(true);
        rebootButton.parentNode.replaceChild(newRebootButton, rebootButton);
        newRebootButton.addEventListener('click', handleDeviceReboot);
    }

    if (loadProgramButton) {
        const newLoadProgramButton = loadProgramButton.cloneNode(true);
        loadProgramButton.parentNode.replaceChild(newLoadProgramButton, loadProgramButton);
        newLoadProgramButton.addEventListener('click', handleProgramLoad);
    }

    if (resetButton) {
        const newResetButton = resetButton.cloneNode(true);
        resetButton.parentNode.replaceChild(newResetButton, resetButton);
        newResetButton.addEventListener('click', handleDeviceReset);
    }

    // 添加快捷程序加载按钮的事件监听
    const quickProgramLoadButton = document.getElementById('quickProgramLoad');
    if (quickProgramLoadButton) {
        const newQuickProgramLoadButton = quickProgramLoadButton.cloneNode(true);
        quickProgramLoadButton.parentNode.replaceChild(newQuickProgramLoadButton, quickProgramLoadButton);
        newQuickProgramLoadButton.addEventListener('click', handleProgramLoad);
    }

    // 添加快捷恢复出厂设置按钮的事件监听
    const quickResetButton = document.getElementById('quickReset');
    if (quickResetButton) {
        const newQuickResetButton = quickResetButton.cloneNode(true);
        quickResetButton.parentNode.replaceChild(newQuickResetButton, quickResetButton);
        newQuickResetButton.addEventListener('click', handleDeviceReset);
    }

    // 根据加工单号获取版本信息的函数
    async function fetchVersionInfoByWorkOrder(workOrder) {
        try {
            // 调用后端API获取版本信息
            const response = await fetch(`/api/cpu-controller/get-version-info?work_order=${encodeURIComponent(workOrder)}`);
            const data = await response.json();
            
            if (data.success && data.data) {
                // 自动填充版本信息到指定字段
                const specifiedVersionInput = document.getElementById('specifiedVersion');
                const specifiedTimeInput = document.getElementById('specifiedTime');
                const specifiedBackplaneInput = document.getElementById('specifiedBackplane');
                const specifiedHighSpeedInput = document.getElementById('specifiedHighSpeed');
                
                if (specifiedVersionInput && data.data.software_version) {
                    specifiedVersionInput.value = data.data.software_version;
                }
                
                if (specifiedTimeInput && data.data.build_time) {
                    specifiedTimeInput.value = data.data.build_time;
                }
                
                if (specifiedBackplaneInput && data.data.backplane_version) {
                    specifiedBackplaneInput.value = data.data.backplane_version;
                }
                
                if (specifiedHighSpeedInput && data.data.io_version) {
                    // 如果高速 IO 版本为 "-"，在前面添加一个空格以优化显示
                    specifiedHighSpeedInput.value = (data.data.io_version === '-') ? ' -' : data.data.io_version;
                }
                
                // 显示成功提示（可选）
                if (data.data.software_version || data.data.build_time || data.data.backplane_version || data.data.io_version) {
                    console.log('版本信息自动获取成功:', data.message);
                } else {
                    console.log('版本信息为空:', data.message);
                }
            } else {
                console.log('未找到相关版本信息或查询失败:', data.message);
                // 如果没有找到版本信息，清空相关字段
                document.getElementById('specifiedVersion').value = '';
                document.getElementById('specifiedTime').value = '';
                document.getElementById('specifiedBackplane').value = '';
                document.getElementById('specifiedHighSpeed').value = '';
            }
        } catch (error) {
            console.error('自动获取版本信息失败:', error);
            // 出错时也清空相关字段
            document.getElementById('specifiedVersion').value = '';
            document.getElementById('specifiedTime').value = '';
            document.getElementById('specifiedBackplane').value = '';
            document.getElementById('specifiedHighSpeed').value = '';
        }
    }

    // SN号检查逻辑（仅进行PCBA绑定检查，不再获取版本信息）
    const productSNInput = document.getElementById('productSN');
    if (productSNInput) {
        productSNInput.addEventListener('blur', async function() {
            const sn = this.value.trim();
            if (!sn) return;

            // 如果当前工单不需要PCBA检查，则跳过检查
            if (!window.cpuControllerState.requiresPcbaCheck) {
                console.log('当前工单无需PCBA绑定检查，允许继续测试');
                return;
            }

            try {
                const response = await fetch(`/api/cpu-controller/check-sn?sn=${encodeURIComponent(sn)}`);
                const data = await response.json();
                
                if (data.success) {
                    if (!data.exists) {
                        await SweetAlert.warning('该SN号未绑定PCBA！');
                        this.value = '';
                        this.focus();
                        return;
                    }
                } else {
                    console.error('检查SN号失败:', data.message);
                }
            } catch (error) {
                console.error('检查SN号时发生错误:', error);
                await SweetAlert.error('检查SN号失败，请检查网络连接');
            }
        });
    }
}

// 持原有的辅助函数...
function updateFormFields(deviceInfo, networkInfo, memoryData) {
    // 定义需要处理的字段和它们的值
    const fields = {
        'deviceManufacturer': deviceInfo.vendorName || '',
        'deviceName': deviceInfo.devicename || '',
        'serialNumber': deviceInfo.serialnumber || '',
        'softwareVersion': deviceInfo.softwareversion || '',
        'buildDate': deviceInfo.builddate || '',
        'backplaneVersion': deviceInfo.busversion || '',
        // 对高速IO版本进行统一处理：如果是"-"，在前面加空格以匹配显示格式
        'highSpeedIOVersion': (deviceInfo.higspeedIOversion === '-') ? ' -' : (deviceInfo.higspeedIOversion || '')
    };

    // 更新字段值并应用样式
    for (const [id, value] of Object.entries(fields)) {
        const element = document.getElementById(id);
        if (element) {
            element.value = value;
            // 如果有值，添加 HSE 绿色样式
            if (value) {
                element.classList.add('input-has-value');
            } else {
                element.classList.remove('input-has-value');
            }
        }
    }

    // 处理 MAC 地址
    const macAddresses = [];
    for (const interface of ['eth0', 'eth1', 'eth2']) {
        if (networkInfo[interface] && networkInfo[interface].mac) {
            const mac = networkInfo[interface].mac;
            const thirdFromLast = networkInfo[interface].third_from_last;
            macAddresses.push(`${interface}mac: ${mac} 倒数第三个数${thirdFromLast}`);
            macAddresses.push('');
        }
    }
    const macAddressElement = document.getElementById('macAddress');
    macAddressElement.value = macAddresses.join('\n');
    if (macAddresses.length > 0) {
        macAddressElement.classList.add('input-has-value');
    } else {
        macAddressElement.classList.remove('input-has-value');
    }

    // 处理内存数据
    const mAreaElement = document.getElementById('mAreaData');
    if (memoryData && Array.isArray(memoryData)) {
        mAreaElement.value = memoryData.join(', ');
        if (memoryData.length > 0) {
            mAreaElement.classList.add('input-has-value');
        } else {
            mAreaElement.classList.remove('input-has-value');
        }
    } else {
        mAreaElement.value = '无内存数据';
        mAreaElement.classList.remove('input-has-value');
    }
}

// 保持原有的其他函数不变...
function clearFormFields() {
    const fields = [
        'deviceName', 'serialNumber', 'softwareVersion', 
        'backplaneVersion', 'highSpeedIOVersion', 'buildDate',
        'deviceManufacturer', 'macAddress', 'mAreaData'
    ];
    
    fields.forEach(field => {
        const element = document.getElementById(field);
        if (element) {
            // 清除输入值
            element.value = '';
            // 移除 HSE 绿色样式
            element.classList.remove('input-has-value');
        }
    });
}

// 保持原有 showMessage 函数...
function showMessage(message, type = 'info', persistent = false, duration = 3000) {
    // 确保消息容器存在
    let messageContainer = document.querySelector('.message-container');
    if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.className = 'message-container';
        document.body.appendChild(messageContainer);
    }

    // 创建遮罩层（仅用于错误消息
    let overlay;
    if (type === 'error' && persistent) {
        overlay = document.createElement('div');
        overlay.className = 'message-overlay';
        document.body.appendChild(overlay);
        setTimeout(() => overlay.classList.add('active'), 0);
    }

    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    
    // 将换行符转换为 HTML 换行
    const formattedMessage = message.replace(/\n/g, '<br>');
    
    // 如果是持续显示的消息，添加关闭按钮
    if (persistent) {
        messageDiv.innerHTML = `
            ${formattedMessage}
            <button class="close-message">×</button>
        `;
        
        // 添加关闭按钮事件
        const closeButton = messageDiv.querySelector('.close-message');
        closeButton.addEventListener('click', () => {
            messageDiv.remove();
            if (overlay) {
                overlay.classList.remove('active');
                setTimeout(() => overlay.remove(), 300);
            }
            // 如果容器为空，移除容器
            if (!messageContainer.children.length) {
                messageContainer.remove();
            }
        });
    } else {
        messageDiv.innerHTML = formattedMessage;
        // 使用传入的持续时间
        setTimeout(() => {
            messageDiv.remove();
            // 如果容器为空，移除容器
            if (!messageContainer.children.length) {
                messageContainer.remove();
            }
        }, duration);
    }
    
    // 添加到消息容器
    messageContainer.appendChild(messageDiv);

    // 移除之前的成功消息（如果存在）
    if (type === 'success') {
        const existingSuccessMessages = messageContainer.querySelectorAll('.message.success');
        existingSuccessMessages.forEach(msg => {
            if (msg !== messageDiv) {
                msg.remove();
            }
        });
    }
}


async function handleDeviceReset() {
    const ipAddress = document.getElementById('ipAddress').value;
    if (!ipAddress) {
        showMessage('请输入IP地址', 'error');
        return;
    }

    // 替换 confirm 为 Swal.fire
    Swal.fire({
        title: '确认恢复出厂设置',
        text: '确定要恢复出厂设置吗？此操作可撤销。',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`${PROXY_BASE_URL}/reset-device`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ ip: ipAddress })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('恢复出厂设置成功', 'success');
                } else {
                    throw new Error(data.message || '恢复出厂设置失败');
                }
            })
            .catch(error => {
                console.error('恢复出厂设置失败:', error);
                showMessage(error.message || '恢复出厂设置失败', 'error');
            });
        }
    });
}

function initializeTestItems(testItems) {
    const testItemsBody = document.getElementById('testItemsBody');
    const selectAllCheckbox = document.getElementById('selectAll');

    // 清空现有内容
    testItemsBody.innerHTML = '';

    // 创建测试项目行
    testItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div class="checkbox-container">
                    <input type="checkbox" id="test-item-${index}" class="test-item-checkbox" data-code="${item.code}">
                </div>
            </td>
            <td>${index + 1}</td>
            <td>${item.name}</td>
            <td>
                <select id="test-result-${index}" data-code="${item.code}">
                    <option value="通过" selected>通过</option>
                    <option value="不通过">不通过</option>
                </select>
            </td>
        `;
        testItemsBody.appendChild(row);
    });

    // 添加快捷按钮事件监听器
    const quickQueryButton = document.getElementById('quickQuery');
    const quickSubmitButton = document.getElementById('quickSubmit');

    if (quickQueryButton) {
        quickQueryButton.addEventListener('click', () => {
            // 触发设备信息卡片中的查询按钮点击事件
            const queryButton = document.querySelector('.device-info-card .button-group button:first-child');
            if (queryButton) {
                queryButton.click();
            }
        });
    }

    if (quickSubmitButton) {
        quickSubmitButton.addEventListener('click', () => {
            // 触发页面底部的提交按钮点击事件
            const submitButton = document.querySelector('.submit-button');
            if (submitButton) {
                submitButton.click();
            }
        });
    }

    // 处理全选复选框
    selectAllCheckbox.addEventListener('change', (e) => {
        const checkboxes = document.querySelectorAll('.test-item-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = e.target.checked);
    });

    // 处理单个复选框和测试结果的变化
    testItemsBody.addEventListener('change', (e) => {
        // 处理复选框全选逻辑
        if (e.target.classList.contains('test-item-checkbox')) {
            const allChecked = [...document.querySelectorAll('.test-item-checkbox')]
                .every(checkbox => checkbox.checked);
            selectAllCheckbox.checked = allChecked;
        }
        
        // 处理测试结果选择
        if (e.target.tagName === 'SELECT') {
            const row = e.target.closest('tr');
            const checkboxContainer = row.querySelector('.checkbox-container');
            
            if (e.target.value === '不通过') {
                checkboxContainer.classList.add('test-failed');
            } else {
                checkboxContainer.classList.remove('test-failed');
            }
        }
    });
}

// 添加请求超时和重试机制
const FETCH_TIMEOUT = 10000; // 10秒超时
const MAX_RETRIES = 3; // 最大重试次数
const RETRY_DELAY = 2000; // 重试延迟时间（毫秒）

// 添加一个带超时的fetch函数
async function fetchWithTimeout(url, options = {}) {
    const controller = new AbortController();
    const timeout = options.timeout || FETCH_TIMEOUT;
    const id = setTimeout(() => controller.abort(), timeout);

    try {
        const response = await fetch(url, {
            ...options,
            signal: controller.signal
        });
        clearTimeout(id);
        return response;
    } catch (error) {
        clearTimeout(id);
        throw error;
    }
}

// 添加重试机制的fetch函数
async function fetchWithRetry(url, options = {}) {
    let lastError;
    
    for (let i = 0; i < MAX_RETRIES; i++) {
        try {
            const response = await fetchWithTimeout(url, options);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response;
        } catch (error) {
            lastError = error;
            console.warn(`Attempt ${i + 1} failed:`, error);
            
            if (error.name === 'AbortError') {
                console.warn('Request timed out');
            }
            
            if (i < MAX_RETRIES - 1) {
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
            }
        }
    }
    
    throw lastError;
}

// 修改查询函数，使用新的fetch机制
async function queryDeviceInfo(ipAddress) {
    try {
        const deviceResponse = await fetchWithRetry(
            `${PROXY_BASE_URL}/device-info?ip=${ipAddress}`,
            { timeout: FETCH_TIMEOUT }
        );
        return await deviceResponse.json();
    } catch (error) {
        throw new Error(`设备信息获取失败: ${error.message}`);
    }
}

// 修改网络信息查询函数
async function queryNetworkInfo(ipAddress, interface) {
    try {
        const response = await fetchWithRetry(
            `${PROXY_BASE_URL}/network-info/${interface}?ip=${ipAddress}`,
            { timeout: FETCH_TIMEOUT }
        );
        return await response.json();
    } catch (error) {
        console.warn(`${interface}网络信息获取失败:`, error);
        return { success: false };
    }
}

// 修改内存息查询函数
async function queryMemoryInfo(ipAddress) {
    try {
        const response = await fetchWithRetry(
            `${PROXY_BASE_URL}/memory-info?ip=${ipAddress}`,
            { timeout: FETCH_TIMEOUT }
        );
        return await response.json();
    } catch (error) {
        throw new Error(`内存信息获取失败: ${error.message}`);
    }
}


// 优化程序加载函数
async function handleProgramLoad() {
    const ipAddress = document.getElementById('ipAddress').value;
    if (!ipAddress) {
        showMessage('请输入IP地址', 'error');
        return;
    }

    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.zip';
    
    fileInput.onchange = async (e) => {
        const file = e.target.files[0];
        if (!file) {
            showMessage('请选择文件', 'error');
            return;
        }

        // 检查件大小
        const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
        if (file.size > MAX_FILE_SIZE) {
            showMessage('文件大小超过限制（最大100MB）', 'error');
            return;
        }

        showMessage('正在上传文件...', 'info');

        const formData = new FormData();
        formData.append('file', file);
        formData.append('ip', ipAddress);

        try {
            // 使用新的fetch机制上传文件
            const uploadResponse = await fetchWithRetry(
                `${PROXY_BASE_URL}/upload-program`,
                {
                    method: 'POST',
                    body: formData,
                    timeout: 60000 // 上传文件给更长的超时时间
                }
            );

            const uploadResult = await uploadResponse.json();
            if (!uploadResult.success) {
                throw new Error(uploadResult.message || '文件上传失败');
            }

            showMessage('文件上传成功，正在更新程序...', 'info');

            // 使用新的fetch机制更新程序
            const updateResponse = await fetchWithRetry(
                `${PROXY_BASE_URL}/update-program`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ip: ipAddress,
                        filename: file.name
                    }),
                    timeout: 30000
                }
            );

            const updateResult = await updateResponse.json();
            if (!updateResult.success) {
                throw new Error(updateResult.message || '程序更新失败');
            }

            showMessage('程序更新指令已发送，设备正在重启...', 'success', true);
            
            setTimeout(() => {
                showMessage('设备正在重启中，请等待约30秒后再进行操作...', 'info', true);
            }, 3000);

        } catch (error) {
            console.error('程序加载失败:', error);
            //showMessage(error.message || '程序加载失败', 'error', true);
            await SweetAlert.info('程序加载中，请等待约25秒后再进行操作...');
        }
    };

    fileInput.click();
}

// 优化设备重启函数
async function handleDeviceReboot() {
    const result = await SweetAlert.confirm('确定要重启设备吗？');
    if (!result) return;

    const ipAddress = document.getElementById('ipAddress').value;
    if (!ipAddress) {
        await SweetAlert.warning('请输入IP地址');
        return;
    }

    try {
        const response = await fetchWithRetry(
            `${PROXY_BASE_URL}/reboot-device`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ ip: ipAddress }),
                timeout: 5000
            }
        );

        const result = await response.json();
        if (result.success) {
            await SweetAlert.success('设备重启指令已发送');
            setTimeout(() => {
                SweetAlert.info('设备正在重启中，请等待约30秒后再进行操作...');
            }, 3000);
        } else {
            //await SweetAlert.error('设备重启失败：' + result.message);
            await SweetAlert.info('设备正在重启中，请等待约20秒后再进行操作...');
        }
    } catch (error) {
        console.error('设备重启失败:', error);
        await SweetAlert.error('设备重启失败，请检查网络连接');
    }
}
