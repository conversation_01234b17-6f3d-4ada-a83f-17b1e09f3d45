<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPU控制器测试系统 - Vue版本</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Element Plus -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <style>
        /* CSS变量主题系统 */
        :root {
            /* 浅色主题变量 */
            --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);
            --bg-overlay: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(139, 92, 246, 0.05) 50%, rgba(236, 72, 153, 0.05) 100%);
            --card-bg: rgba(255, 255, 255, 0.8);
            --card-border: rgba(37, 99, 235, 0.2);
            --card-hover-border: rgba(37, 99, 235, 0.4);
            --card-hover-shadow: rgba(37, 99, 235, 0.2);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #9ca3af;
            --toolbar-bg: rgba(255, 255, 255, 0.9);
            --toolbar-border: rgba(37, 99, 235, 0.2);
            --separator-color: rgba(37, 99, 235, 0.2);
            --accent-blue: #2563eb;
            --accent-blue-light: rgba(37, 99, 235, 0.1);
        }

        [data-theme="dark"] {
            /* 深色主题变量 */
            --bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
            --bg-overlay: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 50%, rgba(236, 72, 153, 0.1) 100%);
            --card-bg: rgba(30, 41, 59, 0.8);
            --card-border: rgba(59, 130, 246, 0.3);
            --card-hover-border: rgba(59, 130, 246, 0.4);
            --card-hover-shadow: rgba(59, 130, 246, 0.3);
            --text-primary: #ffffff;
            --text-secondary: #d1d5db;
            --text-tertiary: #9ca3af;
            --toolbar-bg: rgba(30, 41, 59, 0.9);
            --toolbar-border: rgba(59, 130, 246, 0.3);
            --separator-color: rgba(59, 130, 246, 0.2);
            --accent-blue: #3b82f6;
            --accent-blue-light: rgba(59, 130, 246, 0.1);
        }

        /* 全局过渡动画 */
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease !important;
        }

        /* 自定义样式 */
        .glass-effect {
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .gradient-bg {
            background: var(--bg-primary);
            position: relative;
        }
        
        .gradient-bg::before {
            content: '';
            position: absolute;
            inset: 0;
            background: var(--bg-overlay);
            pointer-events: none;
        }
        
        .card-hover {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .card-hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.5s ease;
            pointer-events: none;
        }
        
        .card-hover:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 25px 50px -12px var(--card-hover-shadow);
            border-color: var(--card-hover-border);
        }
        
        .card-hover:hover::before {
            left: 100%;
        }
        
        .animate-pulse-dot {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        /* 测试日志样式 */
        .log-container {
            background: #111827;
            color: #f3f4f6;
            font-family: 'Courier New', monospace;
        }
        
        .log-success { color: #34d399; }
        .log-error { color: #f87171; }
        .log-warning { color: #fbbf24; }
        .log-info { color: #60a5fa; }
        .log-system { color: #a78bfa; }
        
        /* 进度条自定义样式 */
        .custom-progress {
            height: 12px;
            border-radius: 6px;
            background: #e5e7eb;
        }
        
        .custom-progress-bar {
            height: 100%;
            border-radius: 6px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transition: width 0.3s ease;
        }
        
        /* 状态指示器 */
        .status-indicator {
            position: relative;
        }
        
        .status-indicator::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 50%;
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
        }
        
        .status-connected::after {
            background: #10b981;
            opacity: 0.75;
        }
        
        .status-disconnected::after {
            background: #ef4444;
            opacity: 0.75;
        }
        
        /* 卡片折叠动画 */
        .collapse-enter-active,
        .collapse-leave-active {
            transition: all 0.5s ease;
            overflow: hidden;
        }

        .collapse-enter-from,
        .collapse-leave-to {
            max-height: 0;
            opacity: 0;
        }

        .collapse-enter-to,
        .collapse-leave-from {
            max-height: 1000px;
            opacity: 1;
        }

        /* 强制修复 Element Plus el-tag 图标位置 */
        .el-tag.test-status-tag {
            display: inline-flex !important;
            align-items: center !important;
            flex-direction: row !important;
            white-space: nowrap !important;
        }

        .el-tag.test-status-tag * {
            display: inline !important;
            vertical-align: middle !important;
        }

        .el-tag.test-status-tag .lucide {
            display: inline-block !important;
            margin-right: 4px !important;
            margin-bottom: 0 !important;
            margin-top: 0 !important;
            vertical-align: middle !important;
            float: none !important;
        }

        /* 确保所有 el-tag 内的图标都水平排列 */
        .el-tag {
            display: inline-flex !important;
            align-items: center !important;
            flex-direction: row !important;
        }

        .el-tag svg,
        .el-tag i {
            display: inline-block !important;
            vertical-align: middle !important;
            margin-right: 4px !important;
            margin-bottom: 0 !important;
            float: none !important;
        }

        /* 主题卡片样式 */
        .theme-card {
            background: var(--card-bg) !important;
            border-color: var(--card-border) !important;
            color: var(--text-primary);
        }

        .theme-label {
            color: var(--text-secondary) !important;
        }

        .theme-separator {
            border-color: var(--separator-color) !important;
        }

        .theme-text-primary {
            color: var(--text-primary) !important;
        }

        .theme-text-secondary {
            color: var(--text-secondary) !important;
        }

        .theme-text-tertiary {
            color: var(--text-tertiary) !important;
        }

        /* Element Plus 输入框和选择框主题适配 */
        .el-input__wrapper,
        .el-select .el-input__wrapper {
            background-color: var(--card-bg) !important;
            border-color: var(--card-border) !important;
            box-shadow: 0 0 0 1px var(--accent-blue-light) inset !important;
        }

        .el-input__inner {
            color: var(--text-primary) !important;
        }

        .el-input__inner::placeholder {
            color: var(--text-tertiary) !important;
        }

        .el-textarea__inner {
            background-color: var(--card-bg) !important;
            border-color: var(--card-border) !important;
            color: var(--text-primary) !important;
        }

        /* 浅色主题特殊适配 */
        :root .el-input__wrapper,
        :root .el-select .el-input__wrapper,
        :root .el-textarea__inner {
            background-color: rgba(248, 250, 252, 0.8) !important;
        }

        /* 深色主题特殊适配 - 使用更高优先级 */
        [data-theme="dark"] .el-input__wrapper,
        [data-theme="dark"] .el-select .el-input__wrapper,
        [data-theme="dark"] .el-textarea__inner {
            background-color: rgba(51, 65, 85, 0.8) !important;
            border-color: rgba(71, 85, 105, 0.6) !important;
        }

        /* 深色主题下拉选择框输入框强制覆盖 */
        [data-theme="dark"] .el-select .el-input.is-focus .el-input__wrapper,
        [data-theme="dark"] .el-select .el-input__wrapper:hover,
        [data-theme="dark"] .el-select .el-input__wrapper {
            background-color: rgba(51, 65, 85, 0.9) !important;
            border-color: rgba(59, 130, 246, 0.4) !important;
            box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3) inset !important;
        }

        /* 按钮主题适配 */
        .el-button {
            background: var(--card-bg) !important;
            border-color: var(--card-border) !important;
            color: var(--text-primary) !important;
        }

        .el-button:hover {
            background: var(--accent-blue-light) !important;
            border-color: var(--card-hover-border) !important;
        }

        /* 主题切换按钮特殊样式 */
        .theme-toggle-btn {
            width: 40px !important;
            height: 40px !important;
            border-radius: 12px !important;
            padding: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: var(--accent-blue-light) !important;
            border: 2px solid var(--card-border) !important;
            color: var(--accent-blue) !important;
            position: relative !important;
            overflow: hidden !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        .theme-toggle-btn:hover {
            transform: scale(1.05) !important;
            box-shadow: 0 8px 25px -8px var(--card-hover-shadow) !important;
            border-color: var(--card-hover-border) !important;
        }

        .theme-toggle-btn .lucide {
            transition: all 0.3s ease !important;
        }

        /* 测试项目背景主题适配 */
        .test-item-bg {
            background: var(--card-bg) !important;
            border-color: var(--card-border) !important;
        }

        .test-item-bg:hover {
            border-color: var(--card-hover-border) !important;
        }

        .test-item-bg.active {
            background: var(--accent-blue-light) !important;
            border-color: var(--card-hover-border) !important;
        }

        /* 浅色主题测试项目特殊适配 */
        :root .test-item-bg {
            background: rgba(255, 255, 255, 0.6) !important;
            border-color: rgba(37, 99, 235, 0.2) !important;
        }

        :root .test-item-bg:hover {
            border-color: rgba(37, 99, 235, 0.4) !important;
        }

        :root .test-item-bg.active {
            background: rgba(37, 99, 235, 0.1) !important;
            border-color: rgba(37, 99, 235, 0.4) !important;
        }

        /* 深色主题测试项目特殊适配 */
        [data-theme="dark"] .test-item-bg {
            background: rgba(51, 65, 85, 0.6) !important;
            border-color: rgba(59, 130, 246, 0.3) !important;
        }

        [data-theme="dark"] .test-item-bg:hover {
            border-color: rgba(59, 130, 246, 0.5) !important;
        }

        [data-theme="dark"] .test-item-bg.active {
            background: rgba(59, 130, 246, 0.2) !important;
            border-color: rgba(59, 130, 246, 0.5) !important;
        }

        /* Dialog主题适配 */
        .el-dialog {
            background: var(--card-bg) !important;
            border: 1px solid var(--card-border) !important;
            backdrop-filter: blur(12px) !important;
        }

        .el-dialog__header {
            border-bottom: 1px solid var(--card-border) !important;
        }

        .el-dialog__title {
            color: var(--text-primary) !important;
        }

        .el-dialog__body {
            color: var(--text-secondary) !important;
        }

        /* 浅色主题Dialog特殊适配 */
        :root .el-dialog {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 1px solid rgba(37, 99, 235, 0.2) !important;
        }

        /* 深色主题Dialog特殊适配 */
        [data-theme="dark"] .el-dialog {
            background: rgba(15, 23, 42, 0.98) !important;
            border: 1px solid rgba(59, 130, 246, 0.4) !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2) !important;
        }

        [data-theme="dark"] .el-dialog__header {
            border-bottom-color: rgba(59, 130, 246, 0.3) !important;
        }

        /* Select下拉框主题适配 */
        .el-select-dropdown {
            background: var(--card-bg) !important;
            border: 1px solid var(--card-border) !important;
            backdrop-filter: blur(12px) !important;
        }

        .el-select-dropdown__item {
            color: var(--text-primary) !important;
        }

        .el-select-dropdown__item:hover {
            background: var(--accent-blue-light) !important;
        }

        /* 浅色主题下拉框特殊适配 */
        :root .el-select-dropdown {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 1px solid rgba(37, 99, 235, 0.2) !important;
        }

        :root .el-select-dropdown__item:hover {
            background: rgba(37, 99, 235, 0.1) !important;
        }

        /* 深色主题下拉框特殊适配 */
        [data-theme="dark"] .el-select-dropdown {
            background: rgba(15, 23, 42, 0.98) !important;
            border: 1px solid rgba(59, 130, 246, 0.4) !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2) !important;
        }

        [data-theme="dark"] .el-select-dropdown__item {
            color: var(--text-secondary) !important;
        }

        [data-theme="dark"] .el-select-dropdown__item:hover {
            background: rgba(59, 130, 246, 0.25) !important;
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .el-select-dropdown__item.selected {
            background: rgba(59, 130, 246, 0.3) !important;
            color: var(--text-primary) !important;
        }

        /* DatePicker主题适配 */
        .el-picker-panel {
            background: var(--card-bg) !important;
            border: 1px solid var(--card-border) !important;
            backdrop-filter: blur(12px) !important;
        }

        .el-date-picker__header {
            border-bottom: 1px solid var(--card-border) !important;
            color: var(--text-primary) !important;
        }

        .el-picker-panel__body {
            color: var(--text-primary) !important;
        }

        /* 浅色主题DatePicker特殊适配 */
        :root .el-picker-panel {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 1px solid rgba(37, 99, 235, 0.2) !important;
        }

        /* 深色主题DatePicker特殊适配 */
        [data-theme="dark"] .el-picker-panel {
            background: rgba(15, 23, 42, 0.98) !important;
            border: 1px solid rgba(59, 130, 246, 0.4) !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2) !important;
        }

        [data-theme="dark"] .el-picker-panel__content {
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .el-date-picker__header {
            color: var(--text-primary) !important;
            border-bottom-color: rgba(59, 130, 246, 0.3) !important;
        }

        /* Element Plus 标签组件深色主题适配 */
        [data-theme="dark"] .el-tag {
            border-color: var(--card-border) !important;
        }

        /* 深色主题下待测状态标签样式 */
        [data-theme="dark"] .el-tag--info {
            background: rgba(51, 65, 85, 0.8) !important;
            border-color: rgba(71, 85, 105, 0.6) !important;
            color: var(--text-secondary) !important;
        }

        /* 深色主题下成功状态标签样式 */
        [data-theme="dark"] .el-tag--success {
            background: rgba(5, 46, 22, 0.8) !important;
            border-color: rgba(22, 101, 52, 0.6) !important;
            color: #86efac !important;
        }

        /* 深色主题下失败状态标签样式 */
        [data-theme="dark"] .el-tag--danger {
            background: rgba(69, 10, 10, 0.8) !important;
            border-color: rgba(127, 29, 29, 0.6) !important;
            color: #fca5a5 !important;
        }

        /* 深色主题下警告状态标签样式 */
        [data-theme="dark"] .el-tag--warning {
            background: rgba(69, 26, 3, 0.8) !important;
            border-color: rgba(146, 64, 14, 0.6) !important;
            color: #fcd34d !important;
        }


    </style>
</head>
<body>
    <div id="app">
        <!-- 主应用容器 -->
        <div class="min-h-screen gradient-bg">
            <!-- 顶部工具栏 -->
            <div class="glass-effect border-b px-6 py-4 shadow-xl backdrop-blur-xl" 
                 :style="{ background: isDarkMode ? 'rgba(30, 41, 59, 0.9)' : 'rgba(255, 255, 255, 0.9)', borderColor: 'var(--toolbar-border)' }">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-6">
                        <!-- Logo和标题 -->
                        <div class="flex items-center space-x-3">
                            <div class="relative">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                                    <i data-lucide="cpu" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
                            </div>
                            <div>
                                <h1 class="text-xl font-bold" :class="isDarkMode ? 'text-white' : 'text-gray-900'">CPU控制器测试系统</h1>
                                <p class="text-sm" :class="isDarkMode ? 'text-blue-300' : 'text-blue-600'">Professional Testing Platform v2.1</p>
                            </div>
                        </div>
                        
                        <!-- 分隔线 -->
                        <div class="h-8 w-px bg-gray-300"></div>
                        
                        <!-- 设备状态 -->
                        <div class="flex items-center space-x-3">
                            <div class="relative">
                                <div :class="['w-3 h-3 rounded-full shadow-lg status-indicator', deviceConnected ? 'bg-green-500 status-connected' : 'bg-red-500 status-disconnected']"></div>
                            </div>
                            <div>
                                <span class="text-sm font-medium" :class="isDarkMode ? 'text-white' : 'text-gray-900'">
                                    {{ deviceConnected ? '设备已连接' : '设备未连接' }}
                                </span>
                                <p class="text-xs" :class="isDarkMode ? 'text-gray-300' : 'text-gray-600'">{{ deviceConnected ? formData.ipAddress : '请连接设备' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="flex items-center space-x-3">
                        <!-- 主题切换按钮 -->
                        <el-button 
                            @click="toggleTheme"
                            class="theme-toggle-btn"
                            :title="isDarkMode ? '切换到浅色主题' : '切换到深色主题'"
                        >
                            <i :data-lucide="isDarkMode ? 'moon' : 'sun'" class="w-5 h-5"></i>
                        </el-button>
                        <el-button 
                            type="primary" 
                            :loading="deviceLoading"
                            @click="queryDeviceInfo"
                            class="border-blue-200 text-blue-700 hover:bg-blue-50"
                        >
                            <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                            连接设备
                        </el-button>
                        
                        <el-button 
                            v-if="!testRunning"
                            type="success"
                            @click="runAutoTest"
                            :disabled="!deviceConnected"
                            class="shadow-lg"
                        >
                            <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                            自动测试
                        </el-button>
                        
                        <el-button 
                            v-else
                            type="danger"
                            @click="stopTest"
                            class="shadow-lg"
                        >
                            <i data-lucide="square" class="w-4 h-4 mr-2"></i>
                            停止测试
                        </el-button>
                        
                        <el-button 
                            type="primary"
                            :loading="loading"
                            @click="handleSubmit"
                            class="shadow-lg"
                        >
                            <i data-lucide="sparkles" class="w-4 h-4 mr-2"></i>
                            提交测试
                        </el-button>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="flex h-[calc(100vh-89px)]">
                <!-- 左侧表单区域 -->
                <div class="w-1/2 p-6 overflow-y-auto">
                    <div class="space-y-6">
                        <!-- 基本信息卡片 -->
                        <div class="theme-card glass-effect rounded-xl shadow-xl card-hover">
                            <div class="p-6 cursor-pointer" @click="toggleBasicInfo">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                            <i data-lucide="shield" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="text-lg font-semibold theme-text-primary">基本信息</span>
                                    </div>
                                    <i :data-lucide="basicInfoCollapsed ? 'chevron-down' : 'chevron-up'" class="w-5 h-5 text-blue-300"></i>
                                </div>
                            </div>
                            
                            <!-- 基本信息表单 -->
                            <div class="px-6 pb-6">
                                <!-- 折叠状态：只显示SN号输入框 -->
                                <div v-if="basicInfoCollapsed" class="space-y-4">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">产品SN号 *</label>
                                            <el-input
                                                v-model="formData.productSN"
                                                placeholder="请输入产品SN号"
                                                class="h-10"
                                            ></el-input>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">SN字节数 *</label>
                                            <el-input
                                                v-model="formData.snByteCount"
                                                type="number"
                                                placeholder="字节数"
                                                class="h-10"
                                            ></el-input>
                                        </div>
                                    </div>
                                </div>

                                <!-- 展开状态：显示所有字段 -->
                                <transition name="collapse">
                                    <div v-show="!basicInfoCollapsed" class="space-y-4">
                                        <!-- 测试人员和时间 -->
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">测试人员 *</label>
                                                <el-input v-model="formData.tester" placeholder="请输入测试人员"></el-input>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">测试时间 *</label>
                                                <el-date-picker 
                                                    v-model="formData.test_time" 
                                                    type="datetime" 
                                                    placeholder="选择测试时间"
                                                    class="w-full"
                                                ></el-date-picker>
                                            </div>
                                        </div>
                                        
                                        <!-- 工单号和生产数量 -->
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">加工单号 *</label>
                                                <el-input 
                                                    v-model="formData.orderNumber" 
                                                    placeholder="输入工单号自动查询"
                                                    @input="debounceOrderQuery"
                                                ></el-input>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">生产数量</label>
                                                <el-input v-model="formData.productionQuantity" placeholder="请输入生产数量"></el-input>
                                            </div>
                                        </div>
                                        
                                        <!-- 产品编码和型号 -->
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">产品编码 *</label>
                                                <el-input v-model="formData.productCode" placeholder="请输入产品编码"></el-input>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">产品型号 *</label>
                                                <el-input v-model="formData.productModel" placeholder="请输入产品型号"></el-input>
                                            </div>
                                        </div>
                                        
                                        <!-- 产品状态、SN号、字节数 -->
                                        <div class="grid grid-cols-3 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">产品状态 *</label>
                                                <el-select v-model="formData.productStatus" placeholder="选择状态" class="w-full">
                                                    <el-option label="新品" value="新品"></el-option>
                                                    <el-option label="维修" value="维修"></el-option>
                                                    <el-option label="返工" value="返工"></el-option>
                                                </el-select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">产品SN号 *</label>
                                                <el-input v-model="formData.productSN" placeholder="请输入产品SN号"></el-input>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">SN字节数 *</label>
                                                <el-input v-model="formData.snByteCount" type="number" placeholder="字节数"></el-input>
                                            </div>
                                        </div>
                                        
                                        <!-- 批次号 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">批次号</label>
                                            <el-input v-model="formData.batchNumber" placeholder="请输入批次号"></el-input>
                                        </div>
                                        
                                        <!-- 备注 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                            <el-input 
                                                v-model="formData.remarks" 
                                                type="textarea" 
                                                :rows="2" 
                                                placeholder="请输入备注信息"
                                            ></el-input>
                                        </div>
                                    </div>
                                </transition>
                            </div>
                        </div>

                        <!-- 设备信息卡片 -->
                        <div class="theme-card glass-effect rounded-xl shadow-xl card-hover">
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                            <i data-lucide="activity" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="text-lg font-semibold theme-text-primary">设备信息</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <el-button
                                            size="small"
                                            @click="() => showConfirm('清除设备信息', '确定要清除所有设备信息字段吗？此操作不可撤销。', clearDeviceInfo)"
                                            class="h-8"
                                        >
                                            <i data-lucide="trash-2" class="w-3 h-3 text-red-500"></i>
                                        </el-button>
                                        <el-button
                                            size="small"
                                            @click="() => showConfirm('重启设备', '确定要重启设备吗？设备将断开连接并重新启动。', restartDevice, 'destructive')"
                                            :disabled="!deviceConnected"
                                            class="h-8"
                                        >
                                            <i data-lucide="power" class="w-3 h-3 text-orange-500"></i>
                                        </el-button>
                                        <el-button size="small" @click="loadProgram" :disabled="!deviceConnected || loading" class="h-8">
                                            <i data-lucide="upload" class="w-3 h-3 text-blue-500"></i>
                                        </el-button>
                                        <el-button
                                            size="small"
                                            @click="() => showConfirm('恢复出厂设置', '确定要恢复设备出厂设置吗？这将清除所有用户配置。', factoryReset, 'destructive')"
                                            :disabled="!deviceConnected"
                                            class="h-8"
                                        >
                                            <i data-lucide="refresh-cw" class="w-3 h-3 text-green-500"></i>
                                        </el-button>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <!-- IP地址选择 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">设备IP地址</label>
                                        <el-select v-model="formData.ipAddress" placeholder="选择IP地址" class="w-full">
                                            <el-option
                                                v-for="ip in ipAddresses"
                                                :key="ip"
                                                :label="ip"
                                                :value="ip"
                                            ></el-option>
                                        </el-select>
                                    </div>

                                    <!-- 指定版本和日期 -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">指定版本</label>
                                            <el-input v-model="formData.specifiedVersion" readonly class="bg-gray-50"></el-input>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">指定日期</label>
                                            <el-input v-model="formData.specifiedTime" readonly class="bg-gray-50"></el-input>
                                        </div>
                                    </div>

                                    <!-- 指定背板和高速 -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">指定背板</label>
                                            <el-input v-model="formData.specifiedBackplane" readonly class="bg-gray-50"></el-input>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">指定高速</label>
                                            <el-input v-model="formData.specifiedHighSpeed" readonly class="bg-gray-50"></el-input>
                                        </div>
                                    </div>

                                    <!-- ODM信息和设备厂商 -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">ODM信息</label>
                                            <el-input v-model="formData.odmInfo" placeholder="请输入ODM信息"></el-input>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">设备厂商</label>
                                            <el-input v-model="formData.deviceManufacturer" placeholder="请输入设备厂商"></el-input>
                                        </div>
                                    </div>

                                    <!-- 设备名称和出厂序号 -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">设备名称</label>
                                            <el-input v-model="formData.deviceName" placeholder="请输入设备名称"></el-input>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">出厂序号</label>
                                            <el-input v-model="formData.serialNumber" placeholder="请输入出厂序号"></el-input>
                                        </div>
                                    </div>

                                    <!-- 软件版本和构建日期 -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">软件版本</label>
                                            <el-input v-model="formData.softwareVersion" placeholder="请输入软件版本"></el-input>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">构建日期</label>
                                            <el-input v-model="formData.buildDate" placeholder="请输入构建日期"></el-input>
                                        </div>
                                    </div>

                                    <!-- 背板版本和高速IO版本 -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">背板版本</label>
                                            <el-input v-model="formData.backplaneVersion" placeholder="请输入背板版本"></el-input>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">高速IO版本</label>
                                            <el-input v-model="formData.highSpeedIOVersion" placeholder="请输入高速IO版本"></el-input>
                                        </div>
                                    </div>

                                    <!-- MAC地址 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">MAC地址</label>
                                        <el-input
                                            v-model="formData.macAddress"
                                            type="textarea"
                                            :rows="2"
                                            placeholder="00:1A:2B:3C:4D:5E"
                                            class="font-mono text-sm"
                                        ></el-input>
                                    </div>

                                    <!-- M区数据 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">M区数据</label>
                                        <el-input
                                            v-model="formData.mAreaData"
                                            type="textarea"
                                            :rows="3"
                                            placeholder="M0: 0x1234&#10;M1: 0x5678"
                                            class="font-mono text-sm"
                                        ></el-input>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧测试区域 -->
                <div class="w-1/2 border-l p-6 overflow-y-auto" :style="{ borderColor: 'var(--toolbar-border)' }">
                    <div class="space-y-6">
                        <!-- 测试进度卡片 -->
                        <div class="theme-card glass-effect rounded-xl shadow-xl card-hover">
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                                            <i data-lucide="target" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="text-lg font-semibold theme-text-primary">测试进度</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <el-button
                                            size="small"
                                            :type="!showTestLog ? 'primary' : ''"
                                            @click="showTestLog = false"
                                            class="h-8 text-xs"
                                        >
                                            进度视图
                                        </el-button>
                                        <el-button
                                            size="small"
                                            :type="showTestLog ? 'primary' : ''"
                                            @click="showTestLog = true"
                                            class="h-8 text-xs"
                                        >
                                            测试日志
                                        </el-button>
                                    </div>
                                </div>

                                <!-- 进度条 -->
                                <div class="mb-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm theme-text-secondary">总体进度</span>
                                        <span class="text-sm font-mono font-semibold theme-text-primary">{{ Math.round(testProgress) }}%</span>
                                    </div>
                                    <div class="custom-progress">
                                        <div class="custom-progress-bar" :style="{ width: testProgress + '%' }"></div>
                                    </div>
                                </div>

                                <!-- 进度视图 -->
                                <div v-if="!showTestLog">
                                    <div class="grid grid-cols-3 gap-4 text-center">
                                        <div class="p-4 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl border border-green-200">
                                            <div class="text-2xl font-bold text-green-600">{{ passedTests }}</div>
                                            <div class="text-xs text-green-600 font-medium">通过</div>
                                        </div>
                                        <div class="p-4 bg-gradient-to-br from-red-100 to-rose-100 rounded-xl border border-red-200">
                                            <div class="text-2xl font-bold text-red-600">{{ failedTests }}</div>
                                            <div class="text-xs text-red-600 font-medium">失败</div>
                                        </div>
                                        <div class="p-4 bg-gradient-to-br from-gray-100 to-slate-100 rounded-xl border border-gray-200">
                                            <div class="text-2xl font-bold text-gray-600">{{ totalTests - passedTests - failedTests }}</div>
                                            <div class="text-xs text-gray-600 font-medium">待测</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 测试日志视图 - 水平布局 -->
                                <div v-else class="flex space-x-4 h-64">
                                    <!-- 左侧统计信息 (20%) - 4行垂直布局 -->
                                    <div class="w-[20%] space-y-2">
                                        <!-- 通过 -->
                                        <div class="p-2 bg-gradient-to-br from-green-100 to-emerald-100 rounded-lg border border-green-200 text-center">
                                            <div class="text-lg font-bold text-green-600">{{ passedTests }}</div>
                                            <div class="text-xs text-green-600 font-medium">通过</div>
                                        </div>
                                        
                                        <!-- 失败 -->
                                        <div class="p-2 bg-gradient-to-br from-red-100 to-rose-100 rounded-lg border border-red-200 text-center">
                                            <div class="text-lg font-bold text-red-600">{{ failedTests }}</div>
                                            <div class="text-xs text-red-600 font-medium">失败</div>
                                        </div>
                                        
                                        <!-- 待测 -->
                                        <div class="p-2 bg-gradient-to-br from-gray-100 to-slate-100 rounded-lg border border-gray-200 text-center">
                                            <div class="text-lg font-bold text-gray-600">{{ totalTests - passedTests - failedTests }}</div>
                                            <div class="text-xs text-gray-600 font-medium">待测</div>
                                        </div>
                                        
                                        <!-- 结果 -->
                                        <div :class="[
                                            'p-2 rounded-lg border text-center',
                                            overallResult === 'PASS' ? 'bg-gradient-to-br from-green-100 to-emerald-100 border-green-200' :
                                            overallResult === 'NG' ? 'bg-gradient-to-br from-red-100 to-rose-100 border-red-200' :
                                            'bg-gradient-to-br from-gray-100 to-slate-100 border-gray-200'
                                        ]">
                                            <div :class="[
                                                'text-lg font-bold',
                                                overallResult === 'PASS' ? 'text-green-600' :
                                                overallResult === 'NG' ? 'text-red-600' :
                                                'text-gray-600'
                                            ]">{{ overallResult || '--' }}</div>
                                            <div :class="[
                                                'text-xs font-medium',
                                                overallResult === 'PASS' ? 'text-green-600' :
                                                overallResult === 'NG' ? 'text-red-600' :
                                                'text-gray-600'
                                            ]">结果</div>
                                        </div>
                                    </div>
                                    
                                    <!-- 右侧测试日志 (80%) -->
                                    <div class="w-[80%] flex flex-col h-full">
                                        <div class="flex items-center justify-between text-xs mb-2">
                                            <div class="flex items-center space-x-3">
                                                <span class="text-gray-500">测试日志</span>
                                                <div class="flex items-center space-x-1">
                                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                                    <span class="text-green-600">SUCCESS</span>
                                                </div>
                                                <div class="flex items-center space-x-1">
                                                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                                    <span class="text-red-600">ERROR</span>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <el-button
                                                    size="small"
                                                    @click="autoScroll = !autoScroll"
                                                    class="h-6 px-2 text-xs"
                                                >
                                                    {{ autoScroll ? '🔒 自动滚动' : '🔓 手动滚动' }}
                                                </el-button>
                                                <el-button size="small" @click="clearTestLogs" class="h-6 px-2 text-xs">
                                                    清空
                                                </el-button>
                                            </div>
                                        </div>

                                        <div id="test-log-container" class="flex-1 log-container rounded-lg p-3 overflow-y-auto text-xs">
                                            <div v-if="testLogs.length === 0" class="flex items-center justify-center h-full text-gray-500">
                                                <div class="text-center">
                                                    <div class="text-2xl mb-2">📋</div>
                                                    <div>点击"自动测试"开始记录日志</div>
                                                </div>
                                            </div>
                                            <div v-else class="space-y-1">
                                                <div
                                                    v-for="log in testLogs"
                                                    :key="log.id"
                                                    class="flex items-start space-x-2"
                                                >
                                                    <span class="text-gray-400 shrink-0">{{ log.timestamp }}</span>
                                                    <span :class="['shrink-0 font-semibold', getLogLevelClass(log.level)]">
                                                        [{{ log.category }}]
                                                    </span>
                                                    <div class="flex-1 min-w-0">
                                                        <div :class="getLogMessageClass(log.level)">{{ log.message }}</div>
                                                        <div v-if="log.details" class="text-gray-400 text-xs mt-1 ml-2">
                                                            └─ {{ log.details }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 测试项目卡片 -->
                        <div class="theme-card glass-effect rounded-xl shadow-xl card-hover">
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-lg flex items-center justify-center">
                                            <i data-lucide="layers" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="text-lg font-semibold theme-text-primary">测试项目</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <el-button size="small" @click="clearAllResults" :disabled="testRunning" class="h-8">
                                            <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                                            清除
                                        </el-button>
                                        <el-button size="small" @click="setAllPass" :disabled="testRunning" class="h-8">
                                            <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                            全通过
                                        </el-button>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <div
                                        v-for="(item, index) in testResults"
                                        :key="item.name"
                                        :class="[
                                            'flex items-center justify-between p-4 rounded-xl border transition-all duration-300 test-item-bg',
                                            currentTestIndex === index ? 'active shadow-lg' : ''
                                        ]"
                                    >
                                        <div class="flex items-center space-x-3">
                                            <div :class="[
                                                'p-2 rounded-lg',
                                                getTestItemIconClass(item.result)
                                            ]">
                                                <i :data-lucide="item.icon" class="w-4 h-4"></i>
                                            </div>
                                            <div>
                                                <div class="font-medium text-sm theme-text-primary">{{ item.name }}</div>
                                                <div class="text-xs theme-text-secondary flex items-center space-x-2">
                                                    <span>{{ item.category }}</span>
                                                    <span v-if="item.duration">• {{ item.duration }}ms</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="flex items-center space-x-3">
                                            <!-- Testing State -->
                                            <div v-if="item.result === 'testing'" class="flex items-center space-x-2">
                                                <i data-lucide="loader-2" class="w-4 h-4 animate-spin text-blue-600"></i>
                                                <span class="text-xs text-blue-600 font-medium">测试中...</span>
                                            </div>

                                            <!-- Pass State -->
                                            <el-tag v-else-if="item.result === 'pass'" type="success" size="small" class="test-status-tag">
                                                <i data-lucide="check-circle" class="w-3 h-3" style="display: inline-block !important; margin-right: 4px !important; vertical-align: middle !important;"></i><span style="display: inline !important; vertical-align: middle !important;">通过</span>
                                            </el-tag>

                                            <!-- Fail State -->
                                            <el-tag v-else-if="item.result === 'fail'" type="danger" size="small" class="test-status-tag">
                                                <i data-lucide="x-circle" class="w-3 h-3" style="display: inline-block !important; margin-right: 4px !important; vertical-align: middle !important;"></i><span style="display: inline !important; vertical-align: middle !important;">失败</span>
                                            </el-tag>

                                            <!-- Pending State -->
                                            <el-tag v-else type="info" size="small" class="test-status-tag">
                                                <i data-lucide="clock" class="w-3 h-3" style="display: inline-block !important; margin-right: 4px !important; vertical-align: middle !important;"></i><span style="display: inline !important; vertical-align: middle !important;">待测</span>
                                            </el-tag>

                                            <!-- Action Buttons -->
                                            <div v-if="!testRunning && item.result !== 'testing'" class="flex space-x-2">
                                                <el-button
                                                    size="small"
                                                    :type="item.result === 'pass' ? 'success' : ''"
                                                    @click="setTestResult(index, 'pass')"
                                                    class="h-7 px-3 text-xs"
                                                >
                                                    通过
                                                </el-button>
                                                <el-button
                                                    size="small"
                                                    :type="item.result === 'fail' ? 'danger' : ''"
                                                    @click="setTestResult(index, 'fail')"
                                                    class="h-7 px-3 text-xs"
                                                >
                                                    失败
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 确认对话框 -->
        <el-dialog 
            v-model="showConfirmDialog" 
            :title="confirmAction?.title"
            width="400px"
            center
        >
            <div class="flex items-start space-x-3">
                <i data-lucide="alert-triangle" class="w-5 h-5 text-amber-500 mt-0.5"></i>
                <p class="text-gray-600">{{ confirmAction?.description }}</p>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showConfirmDialog = false">取消</el-button>
                    <el-button 
                        :type="confirmAction?.variant === 'destructive' ? 'danger' : 'primary'" 
                        @click="executeConfirmAction"
                    >
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <script>
        const { createApp, ref, reactive, computed, onMounted, nextTick } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            setup() {
                // 响应式数据
                const loading = ref(false);
                const deviceLoading = ref(false);
                const testRunning = ref(false);
                const deviceConnected = ref(false);
                const currentTestIndex = ref(-1);
                const showConfirmDialog = ref(false);
                const basicInfoCollapsed = ref(false);
                const showTestLog = ref(false);
                const autoScroll = ref(true);
                const isDarkMode = ref(true); // 默认深色主题

                const confirmAction = ref(null);
                const testLogs = ref([]);
                const ipAddresses = ['*************', '*************', '*************', '*************', '*************'];
                
                // 表单数据
                const formData = reactive({
                    tester: '张工程师',
                    test_time: new Date(),
                    orderNumber: '',
                    productionQuantity: '',
                    productCode: '',
                    productModel: '',
                    productStatus: '',
                    productSN: '',
                    snByteCount: '',
                    batchNumber: '',
                    remarks: '',
                    ipAddress: '',
                    // 设备信息字段
                    specifiedVersion: '',
                    specifiedTime: '',
                    specifiedBackplane: '',
                    specifiedHighSpeed: '',
                    odmInfo: '',
                    deviceManufacturer: '',
                    deviceName: '',
                    serialNumber: '',
                    softwareVersion: '',
                    buildDate: '',
                    backplaneVersion: '',
                    highSpeedIOVersion: '',
                    macAddress: '',
                    mAreaData: '',
                });
                
                // 测试项目数据
                const testItems = [
                    { name: "RS485_1通信", result: "", category: "通信", icon: "network" },
                    { name: "RS485_2通信", result: "", category: "通信", icon: "network" },
                    { name: "RS232通信", result: "", category: "通信", icon: "network" },
                    { name: "CANbus通信", result: "", category: "通信", icon: "network" },
                    { name: "EtherCAT通信", result: "", category: "通信", icon: "wifi" },
                    { name: "Backplane Bus通信", result: "", category: "通信", icon: "database" },
                    { name: "Body I/O入输出", result: "", category: "硬件", icon: "zap" },
                    { name: "Led数码管", result: "", category: "硬件", icon: "zap" },
                    { name: "Led灯珠", result: "", category: "硬件", icon: "zap" },
                    { name: "U盘接口", result: "", category: "接口", icon: "hard-drive" },
                    { name: "SD卡槽", result: "", category: "接口", icon: "hard-drive" },
                    { name: "调试串口", result: "", category: "接口", icon: "cpu" },
                    { name: "网口", result: "", category: "接口", icon: "network" },
                    { name: "拨码开关", result: "", category: "硬件", icon: "settings" },
                    { name: "复位按钮", result: "", category: "硬件", icon: "rotate-ccw" },
                ];
                
                const testResults = ref([...testItems]);
                
                // 计算属性
                const passedTests = computed(() => 
                    testResults.value.filter(item => item.result === 'pass').length
                );
                
                const failedTests = computed(() => 
                    testResults.value.filter(item => item.result === 'fail').length
                );
                
                const totalTests = computed(() => testResults.value.length);
                
                const testProgress = computed(() => 
                    ((passedTests.value + failedTests.value) / totalTests.value) * 100
                );
                
                const overallResult = computed(() => {
                    if (failedTests.value > 0) {
                        return 'NG';
                    } else if (passedTests.value === totalTests.value && totalTests.value > 0) {
                        return 'PASS';
                    }
                    return '';
                });
                
                // 方法
                const debounceOrderQuery = (() => {
                    let timeout;
                    return (value) => {
                        clearTimeout(timeout);
                        timeout = setTimeout(() => {
                            if (value.trim()) {
                                queryOrderInfo(value);
                            }
                        }, 800);
                    };
                })();
                
                const queryOrderInfo = async (orderNumber) => {
                    try {
                        // 模拟API调用
                        await new Promise(resolve => setTimeout(resolve, 500));
                        
                        formData.productCode = 'PC-CPU-001';
                        formData.productModel = 'CPU控制器标准版';
                        formData.productionQuantity = '100';
                        
                        ElMessage.success('工单查询成功，已自动填充产品信息');
                        basicInfoCollapsed.value = true;
                    } catch (error) {
                        console.error('工单查询失败:', error);
                    }
                };
                
                const queryDeviceInfo = async () => {
                    if (!formData.ipAddress) {
                        ElMessage.error('请先选择IP地址');
                        return;
                    }

                    deviceLoading.value = true;
                    try {
                        // 模拟设备查询
                        await new Promise(resolve => setTimeout(resolve, 1500));

                        // 模拟设备信息 - 完整字段
                        formData.specifiedVersion = 'V2.1.0';
                        formData.specifiedTime = '2024-01-15';
                        formData.specifiedBackplane = 'BP-001';
                        formData.specifiedHighSpeed = 'HS-V1.2';
                        formData.odmInfo = 'ODM-12345';
                        formData.deviceManufacturer = '某某科技有限公司';
                        formData.deviceName = 'CPU控制器-标准版';
                        formData.serialNumber = 'SN202401150001';
                        formData.softwareVersion = 'SW-V2.1.0-20240115';
                        formData.buildDate = '2024-01-15 14:30:25';
                        formData.backplaneVersion = 'BP-V1.0.3';
                        formData.highSpeedIOVersion = 'HSIO-V1.2.1';
                        formData.macAddress = '00:1A:2B:3C:4D:5E\n00:1A:2B:3C:4D:5F';
                        formData.mAreaData = 'M0: 0x1234\nM1: 0x5678\nM2: 0xABCD\nM3: 0xEF01';

                        deviceConnected.value = true;
                        ElMessage.success('设备连接成功');
                    } catch (error) {
                        deviceConnected.value = false;
                        ElMessage.error('设备连接失败');
                    } finally {
                        deviceLoading.value = false;
                    }
                };
                
                const addTestLog = (level, category, message, details) => {
                    const newLog = {
                        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                        timestamp: new Date().toLocaleTimeString('zh-CN', {
                            hour12: false,
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            fractionalSecondDigits: 3,
                        }),
                        level,
                        category,
                        message,
                        details,
                    };
                    testLogs.value.push(newLog);

                    // 自动滚动到底部
                    if (autoScroll.value) {
                        nextTick(() => {
                            const logContainer = document.getElementById('test-log-container');
                            if (logContainer) {
                                logContainer.scrollTop = logContainer.scrollHeight;
                            }
                        });
                    }
                };

                const runAutoTest = async () => {
                    if (!deviceConnected.value) {
                        ElMessage.error('请先连接设备后再开始测试');
                        return;
                    }

                    testRunning.value = true;
                    currentTestIndex.value = 0;
                    showTestLog.value = true;

                    addTestLog('system', 'SYSTEM', '=== 自动测试开始 ===');
                    addTestLog('info', 'INIT', '初始化测试环境...');
                    addTestLog('info', 'DEVICE', `连接设备: ${formData.ipAddress}`);
                    addTestLog('success', 'DEVICE', '设备连接正常，开始执行测试序列');

                    for (let i = 0; i < testResults.value.length; i++) {
                        currentTestIndex.value = i;
                        const currentTest = testResults.value[i];

                        addTestLog('info', 'TEST', `开始测试: ${currentTest.name}`, `类别: ${currentTest.category}`);
                        testResults.value[i].result = 'testing';

                        // 模拟测试过程
                        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

                        const result = Math.random() > 0.15 ? 'pass' : 'fail';
                        const duration = Math.round(1000 + Math.random() * 2000);

                        testResults.value[i].result = result;
                        testResults.value[i].duration = duration;

                        if (result === 'pass') {
                            addTestLog('success', 'RESULT', `✓ ${currentTest.name} 测试通过`, `耗时: ${duration}ms`);
                        } else {
                            addTestLog('error', 'RESULT', `✗ ${currentTest.name} 测试失败`, `耗时: ${duration}ms`);
                        }

                        // 重新渲染图标以显示最新状态
                        nextTick(() => {
                            lucide.createIcons();
                        });
                    }

                    currentTestIndex.value = -1;
                    testRunning.value = false;

                    addTestLog('system', 'SUMMARY', '=== 测试完成 ===');
                    addTestLog('info', 'SUMMARY', `总计: ${totalTests.value} 项, 通过: ${passedTests.value} 项, 失败: ${failedTests.value} 项`);

                    ElMessage.success(`测试完成，通过 ${passedTests.value}/${totalTests.value} 项`);
                    
                    // 重新渲染图标
                    nextTick(() => {
                        lucide.createIcons();
                    });
                };
                
                const stopTest = () => {
                    testRunning.value = false;
                    currentTestIndex.value = -1;
                    testResults.value.forEach(item => {
                        if (item.result === 'testing') {
                            item.result = '';
                        }
                    });
                };
                
                const handleSubmit = async () => {
                    if (!validateForm()) return;
                    
                    loading.value = true;
                    try {
                        // 模拟提交
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        ElMessage.success('测试记录已保存');
                    } catch (error) {
                        ElMessage.error('提交失败，请检查网络连接');
                    } finally {
                        loading.value = false;
                    }
                };
                
                const validateForm = () => {
                    const requiredFields = [
                        { field: 'tester', name: '测试人员' },
                        { field: 'orderNumber', name: '加工单号' },
                        { field: 'productCode', name: '产品编码' },
                        { field: 'productModel', name: '产品型号' },
                        { field: 'productStatus', name: '产品状态' },
                        { field: 'productSN', name: '产品SN号' },
                        { field: 'snByteCount', name: 'SN号字节数' },
                    ];
                    
                    for (const { field, name } of requiredFields) {
                        if (!formData[field]) {
                            ElMessage.error(`${name}为必填项`);
                            return false;
                        }
                    }
                    
                    return true;
                };
                
                const executeConfirmAction = () => {
                    if (confirmAction.value?.action) {
                        confirmAction.value.action();
                    }
                    showConfirmDialog.value = false;
                };

                const showConfirm = (title, description, action, variant = 'default') => {
                    confirmAction.value = { title, description, action, variant };
                    showConfirmDialog.value = true;
                };

                // 新增方法
                const toggleBasicInfo = () => {
                    basicInfoCollapsed.value = !basicInfoCollapsed.value;
                    nextTick(() => {
                        lucide.createIcons();
                    });
                };

                const clearDeviceInfo = () => {
                    const deviceFields = [
                        'specifiedVersion',
                        'specifiedTime',
                        'specifiedBackplane',
                        'specifiedHighSpeed',
                        'odmInfo',
                        'deviceManufacturer',
                        'deviceName',
                        'serialNumber',
                        'softwareVersion',
                        'buildDate',
                        'backplaneVersion',
                        'highSpeedIOVersion',
                        'macAddress',
                        'mAreaData',
                    ];

                    deviceFields.forEach(field => {
                        formData[field] = '';
                    });

                    deviceConnected.value = false;
                    ElMessage.success('设备信息已清除');
                };

                const restartDevice = async () => {
                    if (!deviceConnected.value) {
                        ElMessage.error('设备未连接');
                        return;
                    }
                    try {
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        deviceConnected.value = false;
                        ElMessage.success('设备重启指令已发送');
                    } catch (error) {
                        ElMessage.error('设备重启失败');
                    }
                };

                const loadProgram = async () => {
                    if (!deviceConnected.value) {
                        ElMessage.error('设备未连接');
                        return;
                    }
                    loading.value = true;
                    try {
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        ElMessage.success('程序加载成功');
                    } catch (error) {
                        ElMessage.error('程序加载失败');
                    } finally {
                        loading.value = false;
                    }
                };

                const factoryReset = async () => {
                    if (!deviceConnected.value) {
                        ElMessage.error('设备未连接');
                        return;
                    }
                    try {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        ElMessage.success('设备已恢复到出厂默认配置');
                    } catch (error) {
                        ElMessage.error('恢复出厂设置失败');
                    }
                };

                const clearTestLogs = () => {
                    testLogs.value = [];
                };

                const clearAllResults = () => {
                    testResults.value.forEach(item => {
                        item.result = '';
                        delete item.duration;
                    });
                    // 重新渲染图标
                    nextTick(() => {
                        lucide.createIcons();
                    });
                };

                const setAllPass = () => {
                    testResults.value.forEach(item => {
                        item.result = 'pass';
                    });
                    // 重新渲染图标
                    nextTick(() => {
                        lucide.createIcons();
                    });
                };

                const setTestResult = (index, result) => {
                    // Update the test result
                    testResults.value[index].result = result;

                    // Clear duration if manually setting result
                    if (testResults.value[index].duration) {
                        delete testResults.value[index].duration;
                    }

                    // Ensure icons are properly rendered after state change
                    nextTick(() => {
                        lucide.createIcons();
                    });

                    // Provide user feedback
                    const testName = testResults.value[index].name;
                    const statusText = result === 'pass' ? '通过' : '失败';
                    ElMessage.success(`${testName} 已标记为${statusText}`);
                };

                const getLogLevelClass = (level) => {
                    const classes = {
                        success: 'log-success',
                        error: 'log-error',
                        warning: 'log-warning',
                        info: 'log-info',
                        system: 'log-system'
                    };
                    return classes[level] || 'log-info';
                };

                const getLogMessageClass = (level) => {
                    const classes = {
                        success: 'text-green-300',
                        error: 'text-red-300',
                        warning: 'text-yellow-300',
                        info: 'text-gray-300',
                        system: 'text-purple-300'
                    };
                    return classes[level] || 'text-gray-300';
                };

                const getTestItemIconClass = (result) => {
                    const classes = {
                        pass: 'bg-green-100 text-green-600',
                        fail: 'bg-red-100 text-red-600',
                        testing: 'bg-blue-100 text-blue-600',
                        '': 'bg-gray-100 text-gray-500'
                    };
                    return classes[result] || 'bg-gray-100 text-gray-500';
                };
                
                // 生命周期
                onMounted(() => {
                    // 加载用户主题偏好
                    loadThemePreference();
                    
                    // 初始化主题和图标
                    nextTick(() => {
                        applyTheme();
                        lucide.createIcons();
                    });
                    
                    // 设置默认IP
                    formData.ipAddress = '*************';
                    
                    // 监听系统主题变化
                    if (window.matchMedia) {
                        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
                        mediaQuery.addEventListener('change', (e) => {
                            // 只有在用户没有手动设置过主题时才跟随系统
                            const saved = localStorage.getItem('theme-preference');
                            if (!saved) {
                                isDarkMode.value = e.matches;
                                applyTheme();
                            }
                        });
                    }
                });

                // 主题切换功能
                const toggleTheme = () => {
                    isDarkMode.value = !isDarkMode.value;
                    applyTheme();
                    saveThemePreference();
                    
                    // 提供用户反馈
                    ElMessage.success(`已切换到${isDarkMode.value ? '深色' : '浅色'}主题`);
                };

                const applyTheme = () => {
                    const htmlElement = document.documentElement;
                    
                    if (isDarkMode.value) {
                        htmlElement.setAttribute('data-theme', 'dark');
                    } else {
                        htmlElement.removeAttribute('data-theme');
                    }
                    
                    // 应用主题类到所有相关元素
                    nextTick(() => {
                        applyThemeClasses();
                        lucide.createIcons();
                    });
                };

                const applyThemeClasses = () => {
                    // 修改所有label标签颜色
                    const labels = document.querySelectorAll('label');
                    labels.forEach(label => {
                        label.classList.add('theme-label');
                    });

                    // 修改分隔线颜色
                    const separators = document.querySelectorAll('.h-8.w-px');
                    separators.forEach(sep => {
                        sep.classList.add('theme-separator');
                    });

                    // 确保测试项目使用正确的主题类
                    const testItems = document.querySelectorAll('.test-item-bg');
                    testItems.forEach(item => {
                        // 强制刷新样式，触发CSS重新计算
                        item.style.display = 'none';
                        item.offsetHeight; // 触发重排
                        item.style.display = '';
                    });

                    // 修复任何残留的旧样式类
                    const oldStyleItems = document.querySelectorAll('.bg-white\\/50, .bg-slate-700\\/50, .border-gray-200, .border-blue-400\\/20');
                    oldStyleItems.forEach(item => {
                        item.classList.remove('bg-white/50', 'bg-slate-700/50', 'border-gray-200', 'border-blue-400/20');
                        if (!item.classList.contains('test-item-bg')) {
                            item.classList.add('test-item-bg');
                        }
                    });
                };

                const saveThemePreference = () => {
                    localStorage.setItem('theme-preference', isDarkMode.value ? 'dark' : 'light');
                };

                const loadThemePreference = () => {
                    const saved = localStorage.getItem('theme-preference');
                    if (saved) {
                        isDarkMode.value = saved === 'dark';
                    } else {
                        // 检测系统主题偏好
                        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                            isDarkMode.value = true;
                        } else {
                            isDarkMode.value = false;
                        }
                    }
                };
                
                return {
                    // 响应式数据
                    loading,
                    deviceLoading,
                    testRunning,
                    deviceConnected,
                    currentTestIndex,
                    showConfirmDialog,
                    basicInfoCollapsed,
                    showTestLog,
                    autoScroll,
                    isDarkMode,
                    confirmAction,
                    formData,
                    testResults,
                    testLogs,
                    ipAddresses,

                    // 计算属性
                    passedTests,
                    failedTests,
                    totalTests,
                    testProgress,
                    overallResult,

                    // 方法
                    debounceOrderQuery,
                    queryDeviceInfo,
                    runAutoTest,
                    stopTest,
                    handleSubmit,
                    executeConfirmAction,
                    showConfirm,
                    toggleBasicInfo,
                    clearDeviceInfo,
                    restartDevice,
                    loadProgram,
                    factoryReset,
                    clearTestLogs,
                    clearAllResults,
                    setAllPass,
                    setTestResult,
                    getLogLevelClass,
                    getLogMessageClass,
                    getTestItemIconClass,
                    addTestLog,
                    // 主题相关方法
                    toggleTheme,
                    applyTheme,
                    loadThemePreference,
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
