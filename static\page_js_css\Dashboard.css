.dashboard-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;
}

.dashboard-item {
    flex: 1;
    min-width: 300px;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 16px;
}

.dashboard-actions {
    display: flex;
    gap: 16px;
}

.btn-refresh {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
}

.btn-refresh:hover {
    background: #66b1ff;
}

.btn-refresh i {
    transition: transform 0.3s;
}

.btn-refresh:active i {
    transform: rotate(180deg);
}

.date-range select {
    padding: 8px 16px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: white;
    cursor: pointer;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    padding: 0 16px;
}

.stat-card {
    background: linear-gradient(135deg, var(--card-gradient-start) 0%, var(--card-gradient-end) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-card.primary {
    --card-gradient-start: #4481eb;
    --card-gradient-end: #04befe;
    border-left: 4px solid var(--primary-color);
}

.stat-card.success {
    --card-gradient-start: #43e97b;
    --card-gradient-end: #38f9d7;
    border-left: 4px solid #67C23A;
}

.stat-card.warning {
    --card-gradient-start: #f6d365;
    --card-gradient-end: #fda085;
    border-left: 4px solid #E6A23C;
}

.stat-card.info {
    --card-gradient-start: #6b8dd6;
    --card-gradient-end: #8e37d7;
    border-left: 4px solid #909399;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.stat-card.primary .stat-icon { 
    background: rgba(64, 158, 255, 0.1);
    color: var(--primary-color);
}

.stat-card.success .stat-icon {
    background: rgba(103, 194, 58, 0.1);
    color: #67C23A;
}

.stat-card.warning .stat-icon {
    background: rgba(230, 162, 60, 0.1);
    color: #E6A23C;
}

.stat-card.info .stat-icon {
    background: rgba(144, 147, 153, 0.1);
    color: #909399;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    line-height: 1;
    margin-bottom: 8px;
    display: flex;
    align-items: baseline;
}

.stat-value small {
    font-size: 14px;
    margin-left: 4px;
    opacity: 0.7;
}

.stat-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
}

.stat-trend {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-trend.increase { color: #67C23A; }
.stat-trend.decrease { color: #F56C6C; }

.chart-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin: 24px 16px;
}

.chart-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.chart-header h3 {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0;
}

.chart-actions {
    display: flex;
    gap: 8px;
}

.btn-chart-type {
    padding: 4px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-chart-type:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-chart-type.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.recent-tests-table {
    overflow-x: auto;
}

.recent-tests-table table {
    width: 100%;
    border-collapse: collapse;
}

.recent-tests-table th,
.recent-tests-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ebeef5;
}

.recent-tests-table th {
    font-weight: 600;
    color: #909399;
    background: #f5f7fa;
}

.recent-tests-table tr:hover {
    background: #f5f7fa;
}

.view-more {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.view-more:hover {
    opacity: 0.8;
}

@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .chart-grid {
        grid-template-columns: 1fr;
    }
}

.chart-card.loading {
    position: relative;
}

.chart-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: '\f110';
    font-size: 2em;
    color: var(--primary-color);
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#trendChart {
    height: 400px;
    width: 100%;
}

#faultPieChart {
    height: 400px;
    width: 100%;
}

#productBarChart {
    height: 400px;
    width: 100%;
}

/* 添加数值变化动画 */
.stat-value span {
    display: inline-block;
    transition: all 0.3s ease-out;
}

/* 添加渐变背景 */
.stat-card {
    background: linear-gradient(135deg, var(--card-gradient-start) 0%, var(--card-gradient-end) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-card.primary {
    --card-gradient-start: #4481eb;
    --card-gradient-end: #04befe;
}

.stat-card.success {
    --card-gradient-start: #43e97b;
    --card-gradient-end: #38f9d7;
}

.stat-card.warning {
    --card-gradient-start: #f6d365;
    --card-gradient-end: #fda085;
}

.stat-card.info {
    --card-gradient-start: #6b8dd6;
    --card-gradient-end: #8e37d7;
}

/* 添加悬浮效果 */
.chart-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 添加数据加载动画 */
@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

.loading-shimmer {
    animation: shimmer 2s infinite linear;
    background: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
    background-size: 1000px 100%;
}

/* 新增样式 */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;
}

.kpi-item {
    background: linear-gradient(145deg, #ffffff, #f5f5f5);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 5px 5px 15px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.kpi-item:hover {
    transform: translateY(-5px);
}

.kpi-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.kpi-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.kpi-chart {
    height: 60px;
}

.production-status {
    padding: 20px;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.status-item {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    background: linear-gradient(145deg, #ffffff, #f5f5f5);
    box-shadow: 5px 5px 15px rgba(0,0,0,0.05);
}

.status-item i {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.status-item .spinning {
    animation: spin 2s linear infinite;
}

.status-count {
    font-size: 24px;
    font-weight: 600;
    margin-top: 10px;
    color: #333;
}

.time-selector {
    display: flex;
    gap: 8px;
}

.time-btn {
    padding: 4px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    transition: all 0.3s;
}

.time-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}