from flask import Blueprint, jsonify, request
from database.db_manager import DatabaseManager
from sqlalchemy import func, text, union_all
from datetime import datetime, timedelta
import logging
from functools import lru_cache
from routes.product_test_query import ProductTestQueryBuilder

# 创建Blueprint时指定名称和导入名
dashboard_bp = Blueprint('dashboard', __name__)

# 添加调试日志
logger = logging.getLogger(__name__)

def calculate_growth_rate(current, previous):
    """计算增长率"""
    if not previous:
        return 0
    return round(((current - previous) / previous) * 100, 1)

# 使用lru_cache装饰器实现缓存
@lru_cache(maxsize=128)
def get_cached_stats(time_range, cache_time):
    """获取缓存的统计数据"""
    logger.debug(f"Calculating stats for {time_range}, cache_time: {cache_time}")
    db = DatabaseManager()
    with db.get_session() as session:
        # 设置时间范围
        now = datetime.now()
        if time_range == 'today':
            start_time = datetime.combine(now.date(), datetime.min.time())
            end_time = now
            compare_start = start_time - timedelta(days=1)
            compare_end = start_time
        elif time_range == 'week':
            start_time = datetime.combine(
                (now - timedelta(days=now.weekday())).date(),  # 本周一的日期
                datetime.min.time()  # 设置为0点
            )
            end_time = now
            compare_start = start_time - timedelta(days=7)
            compare_end = start_time
        else:  # month
            start_time = datetime.combine(
                datetime(now.year, now.month, 1).date(),  # 本月1号
                datetime.min.time()  # 设置为0点
            )
            end_time = now
            last_month = now.month - 1 if now.month > 1 else 12
            last_month_year = now.year if now.month > 1 else now.year - 1
            compare_start = datetime(last_month_year, last_month, 1)
            compare_end = start_time

        # 修改查询使用正确的表名
        current_query = text("""
            SELECT 
                total_tests,
                passed_tests,
                product_types
            FROM (
                SELECT 
                    (SELECT COUNT(*) FROM cpu_table 
                     WHERE test_time BETWEEN :start_time AND :end_time) +
                    (SELECT COUNT(*) FROM coupler_table 
                     WHERE test_time BETWEEN :start_time AND :end_time) as total_tests,
                    SUM(CASE WHEN test_status = 'pass' THEN 1 ELSE 0 END) as passed_tests,
                    COUNT(DISTINCT product_type) as product_types
                FROM (
                    SELECT test_status, product_type, pro_model, pro_sn, test_time 
                    FROM cpu_table 
                    WHERE test_time BETWEEN :start_time AND :end_time
                    UNION ALL
                    SELECT test_status, product_type, pro_model, pro_sn, test_time 
                    FROM coupler_table 
                    WHERE test_time BETWEEN :start_time AND :end_time
                ) as combined_tests
            ) as stats
        """)

        # 执行查询时传入参数
        current_stats = session.execute(
            current_query,
            {
                'start_time': start_time,
                'end_time': end_time
            }
        ).first()

        # 修改上一周期查询
        previous_query = text("""
            SELECT 
                COUNT(*) as total_tests,
                SUM(CASE WHEN test_status = 'pass' THEN 1 ELSE 0 END) as passed_tests
            FROM (
                SELECT test_status
                FROM cpu_table 
                WHERE test_time BETWEEN :compare_start AND :compare_end
                UNION ALL
                SELECT test_status
                FROM coupler_table 
                WHERE test_time BETWEEN :compare_start AND :compare_end
            ) as combined_tests
        """)

        previous_stats = session.execute(
            previous_query,
            {
                'compare_start': compare_start,
                'compare_end': compare_end
            }
        ).first()

        # 修改故障统计查询
        fault_stats = session.execute(text("""
            SELECT COUNT(*) as fault_count
            FROM faultentry_table 
            WHERE test_time BETWEEN :start_time AND :end_time 
            AND pro_status = 1
        """), {
            'start_time': start_time,
            'end_time': end_time
        }).first()

        # 修改上一周期故障统计查询
        previous_fault_stats = session.execute(text("""
            SELECT COUNT(*) as fault_count
            FROM faultentry_table 
            WHERE test_time BETWEEN :compare_start AND :compare_end 
            AND pro_status = 1
        """), {
            'compare_start': compare_start,
            'compare_end': compare_end
        }).first()

        # 计算各项指标
        total_tests = current_stats.total_tests or 0
        passed_tests = current_stats.passed_tests or 0
        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        fault_count = fault_stats.fault_count or 0

        # 计算增长率
        prev_total = previous_stats.total_tests or 0
        prev_passed = previous_stats.passed_tests or 0
        prev_fault_count = previous_fault_stats.fault_count or 0

        test_growth = calculate_growth_rate(total_tests, prev_total)
        pass_rate_growth = calculate_growth_rate(
            pass_rate, 
            (prev_passed / prev_total * 100) if prev_total > 0 else 0
        )
        fault_growth = calculate_growth_rate(fault_count, prev_fault_count)

        # 获取产品型号分布
        product_distribution = session.execute(text("""
            SELECT 
                pro_model as type,
                COUNT(*) as count,
                SUM(CASE WHEN test_status = 'pass' THEN 1 ELSE 0 END) as passed
            FROM (
                SELECT pro_model, test_status
                FROM cpu_table 
                WHERE test_time BETWEEN :start_time AND :end_time
                UNION ALL
                SELECT pro_model, test_status
                FROM coupler_table 
                WHERE test_time BETWEEN :start_time AND :end_time
            ) as combined_tests
            GROUP BY pro_model
        """), {
            'start_time': start_time,
            'end_time': end_time
        }).fetchall()

        # 获取故障类型分布
        fault_types = session.execute(text("""
            SELECT 
                fault_type,
                COUNT(*) as count
            FROM (
                SELECT 
                    CASE 
                        WHEN (backplane_err = 2 OR rs485_1_err = 2 OR rs485_2_err = 2 
                              OR rs232_err = 2 OR canbus_err = 2 OR ethercat_err = 2) 
                        THEN '通信故障'
                        WHEN (body_io_err = 2 OR net_port_err = 2 OR usb_drive_err = 2 
                              OR sd_slot_err = 2 OR debug_port_err = 2) 
                        THEN 'IO故障'
                        WHEN (power_err = 2 OR burn_err = 2) 
                        THEN '硬件故障'
                        WHEN (led_tube_err = 2 OR led_bulb_err = 2 
                              OR dip_switch_err = 2 OR reset_btn_err = 2) 
                        THEN '其他故障'
                        ELSE NULL
                    END as fault_type
                FROM faultentry_table
                WHERE test_time BETWEEN :start_time AND :end_time
                AND pro_status = 1
            ) as fault_data
            WHERE fault_type IS NOT NULL
            GROUP BY fault_type
        """), {
            'start_time': start_time,
            'end_time': end_time
        }).fetchall()

        # 构建返回数据
        response_data = {
            'success': True,
            'data': {
                'totalTests': total_tests,
                'passRate': round(pass_rate, 1),
                'faultCount': fault_count,
                'productionCount': passed_tests,
                'trends': {
                    'tests': {'increase' if test_growth >= 0 else 'decrease': abs(test_growth)},
                    'passRate': {'increase' if pass_rate_growth >= 0 else 'decrease': abs(pass_rate_growth)},
                    'faults': {'increase' if fault_growth >= 0 else 'decrease': abs(fault_growth)}
                },
                'distribution': [{
                    'type': item.type,
                    'total': item.count,
                    'passed': item.passed,
                    'rate': round(item.passed / item.count * 100 if item.count > 0 else 0, 1)
                } for item in product_distribution],
                'faultTypes': [{
                    'name': item.fault_type,
                    'value': item.count
                } for item in fault_types],
                'timeRange': time_range
            }
        }

        return response_data

@dashboard_bp.route('/stats', methods=['GET'])
def get_dashboard_stats():
    logger.debug("Received dashboard stats request")
    try:
        time_range = request.args.get('range', 'today')
        logger.debug(f"Processing request for time_range: {time_range}")
        
        # 验证数据库连接和表是否存在
        db = DatabaseManager()
        with db.get_session() as session:
            # 检查表是否存在
            for table in ['cpu_table', 'coupler_table']:
                logger.debug(f"Checking if table {table} exists")
                exists = session.execute(
                    text(f"SHOW TABLES LIKE '{table}'")
                ).fetchone()
                if not exists:
                    logger.error(f"Table {table} does not exist")
                    return jsonify({
                        'success': False,
                        'message': f'数据库表 {table} 不存在'
                    }), 500
        
        # 使用当前分钟作为缓存key
        current_minute = datetime.now().strftime('%Y%m%d%H%M')
        try:
            response_data = get_cached_stats(time_range, current_minute)
            logger.debug(f"Response data: {response_data}")
            return jsonify(response_data)
        except Exception as e:
            logger.error(f"Error in get_cached_stats: {str(e)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': f'获取统计数据失败：{str(e)}'
            }), 500

    except Exception as e:
        logger.error(f"获取仪表盘数据失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取数据失败：{str(e)}'
        }), 500

@dashboard_bp.route('/test', methods=['GET'])
def test_route():
    """测试路由是否正确注册"""
    return jsonify({
        'success': True,
        'message': 'Dashboard blueprint is working'
    }) 