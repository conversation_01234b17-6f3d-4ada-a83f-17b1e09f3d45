from flask import Blueprint, request, jsonify, send_file
from database.db_manager import DatabaseManager
from models.product import ProductManagement
from sqlalchemy import or_, desc, asc
import pandas as pd
from io import BytesIO
from datetime import datetime
from jwt import decode
from functools import wraps
import csv

product_management_bp = Blueprint('product_management', __name__)

# 获取当前用户装饰器
def get_current_user():
    token = request.cookies.get('token')
    if token:
        try:
            user_data = decode(token, 'your-jwt-secret', algorithms=["HS256"])
            return user_data['username']
        except:
            return None
    return None

@product_management_bp.route('/api/products', methods=['GET'])
def get_products():
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        search = request.args.get('search', '')
        sort_field = request.args.get('sortField', 'Basic_creationTime')
        sort_order = request.args.get('sortOrder', 'desc')
        
        db = DatabaseManager()
        with db.get_session() as session:
            query = session.query(ProductManagement)
            
            # 搜索
            if search:
                query = query.filter(or_(
                    ProductManagement.dbBasic_productCode.ilike(f'%{search}%'),
                    ProductManagement.dbBasic_productName.ilike(f'%{search}%'),
                    ProductManagement.dbBasic_productType.ilike(f'%{search}%')
                ))
            
            # 排序
            if sort_field:
                db_field = 'db' + sort_field if sort_field != 'id' else 'id'
                sort_column = getattr(ProductManagement, db_field)
                if sort_order == 'desc':
                    query = query.order_by(desc(sort_column))
                else:
                    query = query.order_by(asc(sort_column))
            
            total = query.count()
            products = query.offset((page - 1) * page_size).limit(page_size).all()
            
            return jsonify({
                'success': True,
                'data': [product.to_dict() for product in products],
                'total': total
            })
            
    except Exception as e:
        print(f"Error getting products: {str(e)}")  # 添加日志
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@product_management_bp.route('/api/products', methods=['POST'])
def add_product():
    try:
        data = request.get_json()
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'success': False,
                'message': '未获取到用户信息'
            }), 401

        # 使用当前用户名作为创建人员
        data['Basic_creator'] = current_user
        # 转换为数据库格式
        db_data = ProductManagement.from_dict(data)
        
        db = DatabaseManager()
        with db.get_session() as session:
            product = ProductManagement(**db_data)
            session.add(product)
            session.commit()
            session.refresh(product)  # 刷新以获取最新数据
            
            # 查询所有数据时添加默认排序
            products = session.query(ProductManagement)\
                .order_by(desc(ProductManagement.dbBasic_creationTime))\
                .all()
            return jsonify({
                'success': True,
                'message': '添加成功',
                'data': [p.to_dict() for p in products],
                'total': len(products)
            })
            
    except Exception as e:
        print(f"Error adding product: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@product_management_bp.route('/api/products/<int:id>', methods=['PUT'])
def update_product(id):
    try:
        data = request.get_json()
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'success': False,
                'message': '未获取到用户信息'
            }), 401

        # 获取排序参数
        sort_field = request.args.get('sortField', 'Basic_creationTime')
        sort_order = request.args.get('sortOrder', 'desc')
        
        # 更新时保留原创建人员
        db_data = ProductManagement.from_dict(data)
        
        db = DatabaseManager()
        with db.get_session() as session:
            product = session.query(ProductManagement).get(id)
            if not product:
                return jsonify({
                    'success': False,
                    'message': '产品不存在'
                }), 404
                
            for key, value in db_data.items():
                if key != 'dbBasic_creator':  # 不更新创建人员
                    setattr(product, key, value)
                
            session.commit()
            
            # 重新查询最新数据，应用排序
            db_field = 'db' + sort_field if sort_field != 'id' else 'id'
            sort_column = getattr(ProductManagement, db_field)
            
            if sort_order == 'desc':
                query = session.query(ProductManagement).order_by(desc(sort_column))
            else:
                query = session.query(ProductManagement).order_by(asc(sort_column))
                
            products = query.all()
            
            return jsonify({
                'success': True,
                'message': '更新成功',
                'data': [p.to_dict() for p in products],
                'total': len(products)
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@product_management_bp.route('/api/products/batch-delete', methods=['DELETE'])
def delete_products():
    try:
        print("Received delete request") # 调试日志
        data = request.get_json()
        print("Request data:", data) # 调试日志
        
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'message': '未提供要删除的产品ID'
            }), 400
            
        ids = data['ids']
        print("IDs to delete:", ids) # 调试日志
        
        if not ids:
            return jsonify({
                'success': False,
                'message': '请选择要删除的产品'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            products = session.query(ProductManagement).filter(
                ProductManagement.id.in_(ids)
            ).all()
            print("Found products:", products) # 调试日志
            
            if not products:
                return jsonify({
                    'success': False,
                    'message': '未找到要删除的产品'
                }), 404
                
            for product in products:
                session.delete(product)
            session.commit()
            
            return jsonify({
                'success': True,
                'message': f'成功删除 {len(products)} 个产品'
            })
            
    except Exception as e:
        print("Error:", str(e)) # 调试日志
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500

@product_management_bp.route('/api/products/export', methods=['GET'])
def export_products():
    try:
        selected_ids = request.args.get('ids')
        
        db = DatabaseManager()
        with db.get_session() as session:
            query = session.query(ProductManagement)
            
            if selected_ids:
                ids = [int(id_) for id_ in selected_ids.split(',')]
                query = query.filter(ProductManagement.id.in_(ids))
                
            products = query.all()
            if not products:
                return jsonify({
                    'success': False,
                    'message': '没有数据可导出'
                }), 404
                
            data = [product.to_dict() for product in products]
            
            # 创建 DataFrame 并重命名列
            df = pd.DataFrame(data)
            df = df.rename(columns={
                'id': '序号',
                'Basic_creator': '创建人员',
                'Basic_creationTime': '创建时间',
                'Basic_productSeries': '产品系列',
                'Basic_productType': '产品类型',
                'Basic_productName': '产品名称',
                'Basic_productCode': '产品编码',
                'Basic_orderNumber': '订货号',
                'snLength': 'SN长度',
                'Basic_remarks': '备注'
            })
            
            # 调整列的顺序
            columns_order = ['序号', '创建人员', '创建时间', '产品系列', '产品类型', 
                           '产品名称', '产品编码', '订货号', 'SN长度', '备注']
            df = df[columns_order]
            
            # 创建内存文件对象
            output = BytesIO()
            
            # 使用 xlsxwriter 引擎创建 Excel 文件
            with pd.ExcelWriter(output, engine='xlsxwriter', mode='xlsx') as writer:
                df.to_excel(writer, sheet_name='产品信息', index=False)
                
                # 获取 xlsxwriter 工作簿和工作表对象
                workbook = writer.book
                worksheet = writer.sheets['产品信息']
                
                # 创建标题格式
                header_format = workbook.add_format({
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'fg_color': '#D9D9D9',
                    'border': 1
                })
                
                # 创建单元格格式
                cell_format = workbook.add_format({
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1
                })
                
                # 设置列宽和格式
                for idx, col in enumerate(df.columns):
                    # 计算最大宽度（考虑中文字符）
                    max_length = max(
                        max(len(str(val)) * 1.5 if any('\u4e00' <= c <= '\u9fff' for c in str(val)) else len(str(val))
                            for val in df[col].astype(str)),
                        len(col) * 1.5 if any('\u4e00' <= c <= '\u9fff' for c in col) else len(col)
                    ) + 2
                    
                    # 设置列宽（最小8，最大50）
                    worksheet.set_column(idx, idx, min(max(8, max_length), 50))
                    
                    # 设置标题格式
                    worksheet.write(0, idx, col, header_format)
                    
                    # 设置数据行格式
                    for row in range(1, len(df) + 1):
                        worksheet.write(row, idx, df.iloc[row-1][col], cell_format)
                
                # 冻结首行
                worksheet.freeze_panes(1, 0)
            
            output.seek(0)
            
            # 生成更有意义的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if selected_ids:
                # 如果是选中导出，添加选中数量信息
                count = len(products)
                filename = f'已选择{count}个产品_{timestamp}.xlsx'
            else:
                # 如果是全部导出，添加总数信息
                total = len(products)
                filename = f'全部产品_{total}条_{timestamp}.xlsx'
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500

@product_management_bp.route('/api/products/import', methods=['POST'])
def import_products():
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '未找到文件'
            }), 400
            
        file = request.files['file']
        if not file.filename.endswith(('.xlsx', '.xls')):
            return jsonify({
                'success': False,
                'message': '文件格式不正确'
            }), 400

        # 获取当前用户
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'success': False,
                'message': '未获取到用户信息'
            }), 401
            
        # 读取Excel文件
        df = pd.read_excel(file)
        
        # 验证必填字段
        required_fields = ['产品类型', '产品名称', '产品编码']
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'Excel文件缺少必填字段：{", ".join(missing_fields)}'
            }), 400

        db = DatabaseManager()
        with db.get_session() as session:
            success_count = 0
            error_count = 0
            error_messages = []
            
            # 批量处理每一行数据
            for index, row in df.iterrows():
                try:
                    # 创建产品数据字典
                    product_data = {
                        'dbBasic_creator': current_user,
                        'dbBasic_creationTime': datetime.now(),
                        'dbBasic_productType': str(row.get('产品类型', 'N/A')),
                        'dbBasic_productName': str(row.get('产品名称', 'N/A')),
                        'dbBasic_productCode': str(row.get('产品编码', 'N/A')),
                        'dbBasic_productSeries': str(row.get('产品系列', 'N/A')),
                        'dbBasic_orderNumber': str(row.get('订货号', 'N/A')),
                        'dbBasic_remarks': str(row.get('备注', 'N/A'))
                    }

                    # 处理 SN 长度
                    sn_length = row.get('SN长度')
                    if pd.notna(sn_length):
                        try:
                            product_data['dbsnLength'] = int(float(sn_length))
                        except (ValueError, TypeError):
                            product_data['dbsnLength'] = None
                    else:
                        product_data['dbsnLength'] = None

                    # 验证必填字段不为空
                    if not all([
                        product_data['dbBasic_productType'].strip(),
                        product_data['dbBasic_productName'].strip(),
                        product_data['dbBasic_productCode'].strip()
                    ]):
                        raise ValueError('必填字段不能为空')

                    # 创建产品实例
                    product = ProductManagement(**product_data)
                    session.add(product)
                    success_count += 1
                    
                except Exception as e:
                    error_count += 1
                    error_messages.append(f'第 {index + 2} 行导入失败: {str(e)}')
                    continue
            
            if success_count > 0:
                try:
                    session.commit()
                except Exception as e:
                    return jsonify({
                        'success': False,
                        'message': f'保存数据时出错: {str(e)}'
                    }), 500
            
            # 返回导入结果
            result_message = f'成功导入 {success_count} 条记录'
            if error_count > 0:
                result_message += f'，失败 {error_count} 条'
                if error_messages:
                    result_message += f'\n详细错误：\n' + '\n'.join(error_messages[:5])
                    if len(error_messages) > 5:
                        result_message += f'\n...等共 {len(error_messages)} 条错误'
            
            return jsonify({
                'success': True if success_count > 0 else False,
                'message': result_message
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导入失败: {str(e)}'
        }), 500

@product_management_bp.route('/api/product-by-code', methods=['GET'])
def get_product_by_code():
    try:
        product_code = request.args.get('code', '').strip()
        if not product_code:
            return jsonify({
                'success': False,
                'message': '产品编码不能为空'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            product = session.query(ProductManagement).filter_by(
                dbBasic_productCode=product_code
            ).first()
            
            if product:
                return jsonify({
                    'success': True,
                    'product': {
                        'productCode': product.dbBasic_productCode,
                        'productName': product.dbBasic_productName,
                        'productType': product.dbBasic_productType,
                        'snLength': product.dbsnLength,
                        'orderNumber': product.dbBasic_orderNumber
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '未找到对应的产品信息'
                }), 404
                
    except Exception as e:
        print(f"Error getting product by code: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取产品信息失败'
        }), 500

@product_management_bp.route('/api/products/<int:id>', methods=['GET'])
def get_product(id):
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            product = session.query(ProductManagement).get(id)
            
            if not product:
                return jsonify({
                    'success': False,
                    'message': '产品不存在'
                }), 404
                
            return jsonify({
                'success': True,
                'product': product.to_dict()
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@product_management_bp.route('/api/products/validate-sn-order', methods=['GET'])
def validate_sn_order_number():
    try:
        sn = request.args.get('sn', '').strip()
        product_code = request.args.get('productCode', '').strip()
        
        if not sn or not product_code:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
            
        db = DatabaseManager()
        with db.get_session() as session:
            # 获取产品订货号
            order_number = ProductManagement.get_order_number_by_product_code(session, product_code)
            
            if not order_number:
                return jsonify({
                    'success': False,
                    'message': '未找到对应产品的订货号'
                }), 404
            
            # 提取 SN 的前几位进行比对
            sn_prefix = sn[:len(order_number)]
            
            if sn_prefix != order_number:
                return jsonify({
                    'success': False,
                    'message': 'SN号与订货号不匹配'
                }), 400
                
            return jsonify({
                'success': True,
                'message': 'SN号验证通过'
            })
            
    except Exception as e:
        print(f"Error validating SN order number: {str(e)}")
        return jsonify({
            'success': False,
            'message': '验证失败'
        }), 500 