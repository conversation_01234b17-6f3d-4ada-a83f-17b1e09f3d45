1、如果选择all，那么固定获取M区的数据为“   
● M0 = 1 → RS485_1通信测试 通过
● M1 = 1 → RS485_2通信测试 通过
● M2 = 1 → RS232通信测试 通过
● M3 = 1 → CANbus通信测试 通过
● M4 = 1 → EtherCAT通信测试 通过
● M5 = 1 → Backplane Bus通信 通过。
如果选择all，需要注意的是RS485_通信的判断，只有获取到M0、M1的值都为1，对应测试项目的“RS485_通信”才是通过，否则是“失败”

2、如果选择"高速脉冲"那么固定获取M区的数据为:
● M0 = 1 → RS485_1通信测试 通过
● M1 = 1 → RS485_2通信测试 通过
● M2 = 1 → RS485_3通信测试 通过
● M3 = 1 → RS232通信测试 通过
● M4 = 1 → CANbus通信测试 通过
● M5 = 1 → EtherCAT通信测试 通过
● M6 = 1 → Backplane Bus通信 通过
如果选择高速脉冲，需要注意的是RS485_通信的判断，只有获取到M0、M1、M2的值都为1，对应测试项目的“RS485_通信”才是通过，否则是“失败”


3、如果选择选择“201无输入输出”,那么固定获取M区的数据为： 
● M0 = 1 → RS485_1通信测试 通过
● M1 = 1 → RS485_2通信测试 通过
● M2 = 1 → RS485_3通信测试 通过
● M3 = 1 → RS485_4通信测试 通过
● M4 = 1 → CANbus通信测试 通过
如果选择高201无输入输出，需要注意的是RS485_通信的判断，只有获取到M0、M1、M2、M3的值都为1，对应测试项目的“RS485_通信”才是通过，否则是“失败”

4、如果选择选择"201有输入输出",那么固定获取M区的数据为： 
● M0 = 1 → RS485_1通信测试 通过
● M1 = 1 → RS485_2通信测试 通过
● M2 = 1 → RS485_3通信测试 通过
● M3 = 1 → RS485_4通信测试 通过
● M4 = 1 → CANbus通信测试 通过
● M5 = 1 → Backplane Bus通信 通过
如果选择高201有输入输出，需要注意的是RS485_通信的判断，只有获取到M0、M1、M2、M3的值都为1，对应测试项目的“RS485_通信”才是通过，否则是“失败”

5、如果选择“201五通信”,那么固定获取M区的数据为：
● M0 = 1 → RS485_1通信测试 通过
● M1 = 1 → RS485_2通信测试 通过
● M2 = 1 → RS485_3通信测试 通过
● M3 = 1 → RS485_4通信测试 通过
● M4 = 1 → RS232通信测试 通过
● M5 = 1 → Backplane Bus通信 通过
如果选择201五通信，需要注意的是RS485_通信的判断，只有获取到M0、M1、M2、M3的值都为1，对应测试项目的“RS485_通信”才是通过，否则是“失败”


6、冗余型不用M区取值，不用处理

## 更新说明
- 2025年8月14日：201五通信配置已移除CANbus通信测试项目，Backplane Bus通信改为使用M5地址