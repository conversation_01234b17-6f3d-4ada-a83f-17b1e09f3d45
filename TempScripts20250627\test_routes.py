#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路由测试脚本 - 检查固件管理相关路由是否正确注册
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

def test_routes():
    """测试应用路由注册"""
    try:
        from app import app
        
        print("=== Flask应用路由检查 ===")
        print(f"应用名称: {app.name}")
        print(f"调试模式: {app.debug}")
        
        # 获取所有路由
        routes = []
        for rule in app.url_map.iter_rules():
            methods = ','.join(sorted(rule.methods - {'HEAD', 'OPTIONS'}))
            routes.append((rule.rule, methods, rule.endpoint))
        
        # 按路径排序
        routes.sort(key=lambda x: x[0])
        
        print("\n=== 所有注册的路由 ===")
        firmware_routes = []
        other_routes = []
        
        for route, methods, endpoint in routes:
            if '/api/firmware/' in route:
                firmware_routes.append((route, methods, endpoint))
            else:
                other_routes.append((route, methods, endpoint))
        
        print("\n【固件管理路由】:")
        if firmware_routes:
            for route, methods, endpoint in firmware_routes:
                print(f"  {route:<50} {methods:<20} {endpoint}")
        else:
            print("  ❌ 未发现固件管理路由!")
        
        print(f"\n【其他路由】(总计 {len(other_routes)} 个):")
        for route, methods, endpoint in other_routes[:10]:  # 只显示前10个
            print(f"  {route:<50} {methods:<20} {endpoint}")
        
        if len(other_routes) > 10:
            print(f"  ... 还有 {len(other_routes) - 10} 个其他路由")
        
        print(f"\n=== 路由统计 ===")
        print(f"固件管理路由: {len(firmware_routes)} 个")
        print(f"其他路由: {len(other_routes)} 个")
        print(f"总路由数: {len(routes)} 个")
        
        return len(firmware_routes) > 0
        
    except Exception as e:
        print(f"❌ 路由检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_routes()
    sys.exit(0 if success else 1) 