/* IO模块Vue版本样式 - 现代化主题系统 */

/* IO模块专用CSS变量主题系统 - 避免全局污染 */
.io-module__main {
    /* === 新增：优化字体系统变量 === */
    --io-font-size-2xl: 1.5rem;      /* 24px - 系统主标题 */
    --io-font-size-xl: 1.25rem;      /* 20px - 功能标题 */
    --io-font-size-lg: 1.125rem;     /* 18px - 子标题 */
    --io-font-size-base: 1rem;       /* 16px - 中间层级 */
    --io-font-size-sm: 0.875rem;     /* 14px - 正文内容 */
    --io-font-size-xs: 0.8125rem;    /* 13px - 辅助信息和操作按钮 */
    --io-font-size-2xs: 0.75rem;     /* 12px - 最小文字 */
    
    /* === 新增：增强科技感配色变量 === */
    --io-accent-cyan: #00f5ff;       /* 电子青 */
    --io-accent-green: #00ff88;      /* 电子绿 */
    --io-accent-purple: #8b5cf6;     /* 科技紫 */
    --io-status-pending: #a78bfa;    /* 等待状态 */
    --io-status-processing: #fbbf24; /* 处理状态 */
    
    /* === 新增：增强视觉层次变量 === */
    --io-shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
    --io-shadow-intense: 0 8px 32px rgba(59, 130, 246, 0.4);
    --io-border-glow: rgba(59, 130, 246, 0.6);
    
    /* 浅色主题变量 */
    --io-bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);
    --io-bg-overlay: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(139, 92, 246, 0.05) 50%, rgba(236, 72, 153, 0.05) 100%);
    --io-card-bg: rgba(255, 255, 255, 0.8);
    --io-card-border: rgba(37, 99, 235, 0.35);
    --io-card-hover-border: rgba(37, 99, 235, 0.4);
    --io-card-hover-shadow: rgba(37, 99, 235, 0.2);
    --io-text-primary: #1f2937;
    --io-text-secondary: #6b7280;
    --io-text-tertiary: #9ca3af;
    --io-toolbar-bg: rgba(255, 255, 255, 0.9);
    --io-toolbar-border: rgba(37, 99, 235, 0.2);
    --io-separator-color: rgba(37, 99, 235, 0.2);
    --io-accent-blue: #2563eb;
    --io-accent-blue-light: rgba(37, 99, 235, 0.1);
}

[data-theme="dark"] .io-module__main {
    /* === 深色主题下的增强配色 === */
    --io-accent-cyan: #00d4ff;       /* 深色主题电子青 */
    --io-accent-green: #00f5a0;      /* 深色主题电子绿 */
    --io-shadow-glow: 0 0 20px rgba(0, 212, 255, 0.4);
    --io-shadow-intense: 0 8px 32px rgba(0, 212, 255, 0.5);
    --io-border-glow: rgba(0, 212, 255, 0.7);
    
    /* 深色主题变量 */
    --io-bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
    --io-bg-overlay: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 50%, rgba(236, 72, 153, 0.1) 100%);
    --io-card-bg: rgba(30, 41, 59, 0.8);
    --io-card-border: rgba(59, 130, 246, 0.4);
    --io-card-hover-border: rgba(59, 130, 246, 0.4);
    --io-card-hover-shadow: rgba(59, 130, 246, 0.3);
    --io-text-primary: #ffffff;
    --io-text-secondary: #d1d5db;
    --io-text-tertiary: #9ca3af;
    --io-toolbar-bg: rgba(30, 41, 59, 0.9);
    --io-toolbar-border: rgba(59, 130, 246, 0.3);
    --io-separator-color: rgba(59, 130, 246, 0.2);
    --io-accent-blue: #3b82f6;
    --io-accent-blue-light: rgba(59, 130, 246, 0.1);
}

/* ===== IO模块特有样式 - 保留不可替代的功能 ===== */

/* 1. IO模块特有：测试日志卡片（不可替代的特殊组件）*/
.io-module__log-card {
    background: var(--io-card-bg);
    border: 1px solid var(--io-card-border);
    backdrop-filter: blur(16px);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.io-module__log-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--io-card-hover-shadow);
    border-color: var(--io-card-hover-border);
}

.io-module__log-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.io-module__log-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 2. IO模块特有：日志内容显示区域 */
.io-module__log-content-display {
    width: 80%;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 3. IO模块特有：测试项目活跃状态 */
.io-module__test-item--active {
    background: var(--io-accent-blue-light);
    border-color: var(--io-card-hover-border);
    box-shadow: 0 8px 25px -8px var(--io-card-hover-shadow);
}

/* 4. IO模块特有：增强卡片动画效果 */
.io-module__card-enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.io-module__card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(0, 245, 255, 0.15), 
        rgba(59, 130, 246, 0.1), 
        transparent
    );
    transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    z-index: 1;
}

.io-module__card-enhanced:hover::before {
    left: 100%;
}

/* ===== BEM架构重构：内联样式提取 ===== */

/* 1. 工具栏背景样式 - 替代内联样式 */
.io-module__toolbar {
    background: var(--io-toolbar-bg);
    border-color: var(--io-toolbar-border);
}

.io-module__toolbar--dark {
    background: rgba(30, 41, 59, 0.9);
    border-color: var(--io-toolbar-border);
}

.io-module__toolbar--light {
    background: rgba(255, 255, 255, 0.9);
    border-color: var(--io-toolbar-border);
}

/* 2. 测试区域边框样式 - 替代内联样式 */
.io-module__test-section {
    border-color: var(--io-toolbar-border);
}

/* 3. 进度条动态宽度 - 替代内联样式 */
.io-module__progress-bar {
    width: var(--progress-width, 0%);
    transition: width 0.3s ease;
}

/* ===== BEM架构重构：Tailwind类提取 ===== */

/* 1. 主要布局容器类 */
.io-module__main {
    min-height: 100vh;
    background: var(--io-bg-primary);
    position: relative;
    overflow: hidden;
}

.io-module__main-content {
    display: flex;
    height: calc(100vh - 89px);
}

.io-module__form-section {
    width: 50%;
    padding-left: 1.5rem;
    padding-right: 0.75rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    overflow-y: auto;
}

.io-module__test-section {
    width: 50%;
    border-left: 1px solid var(--io-toolbar-border);
    padding-left: 0.75rem;
    padding-right: 1.5rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    overflow-y: auto;
}

/* 2. 工具栏布局类 */
.io-module__toolbar-container {
    max-width: none;
    margin-left: 1.5rem;
    margin-right: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.io-module__toolbar-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.io-module__toolbar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.io-module__toolbar-status {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.io-module__toolbar-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* 3. 卡片布局类 */
.io-module__card {
    background: var(--io-card-bg);
    border: 1px solid var(--io-card-border);
    backdrop-filter: blur(16px);
    border-radius: 0.75rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.io-module__card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--io-card-hover-shadow);
    border-color: var(--io-card-hover-border);
}

.io-module__card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.io-module__card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.io-module__card-content {
    padding: 1.5rem;
}

/* 4. 图标容器类 */
.io-module__icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.io-module__icon--blue {
    background: linear-gradient(135deg, #3b82f6, #4f46e5);
    box-shadow: var(--io-shadow-glow);
}

.io-module__icon--green {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.io-module__icon--purple {
    background: linear-gradient(135deg, #8b5cf6, #ec4899);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.io-module__icon--indigo {
    background: linear-gradient(135deg, #6366f1, #3b82f6);
    box-shadow: var(--io-shadow-glow);
}

.io-module__icon--orange {
    background: linear-gradient(135deg, #f97316, #ea580c);
}

/* 切换标签样式 */
.io-module__progress-tabs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 测试日志布局 */
.io-module__log-layout {
    display: flex;
    gap: 1rem;
    height: 16rem;
}

.io-module__log-stats {
    width: 20%;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.io-module__compact-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 0.5rem;
    font-size: var(--io-font-size-sm);
    font-weight: 500;
    border: 1px solid;
    transition: all 0.3s ease;
}

.io-module__compact-stat:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.io-module__compact-stat--success {
    background-color: rgba(16, 185, 129, 0.05);
    border-color: rgba(16, 185, 129, 0.2);
    color: #047857;
}

.io-module__compact-stat--danger {
    background-color: rgba(239, 68, 68, 0.05);
    border-color: rgba(239, 68, 68, 0.2);
    color: #991b1b;
}

.io-module__compact-stat--neutral {
    background-color: rgba(107, 114, 128, 0.05);
    border-color: rgba(107, 114, 128, 0.2);
    color: #374151;
}

[data-theme="dark"] .io-module__compact-stat--success {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: #6ee7b7;
}

[data-theme="dark"] .io-module__compact-stat--danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #fca5a5;
}

[data-theme="dark"] .io-module__compact-stat--neutral {
    background-color: rgba(107, 114, 128, 0.1);
    border-color: rgba(107, 114, 128, 0.3);
    color: #d1d5db;
}

.io-module__log-content {
    width: 80%;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.io-module__log-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
}

.io-module__log-indicators {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.io-module__log-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.io-module__log-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
}

.io-module__log-dot--success {
    background-color: #10b981;
}

.io-module__log-dot--error {
    background-color: #ef4444;
}

.io-module__log-dot--warning {
    background-color: #f59e0b;
}

.io-module__log-dot--info {
    background-color: #3b82f6;
}

.io-module__log-dot--system {
    background-color: #8b5cf6;
}

/* 5. 网格系统 */
.io-module__grid-2 {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
}

.io-module__grid-3 {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
}

.io-module__field-group {
    display: flex;
    flex-direction: column;
}

.io-module__field-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--io-text-secondary);
    margin-bottom: 0.25rem;
}

/* 6. 间距类 */
.io-module__space-y {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* 7. 表单布局类 */
.io-module__form-content {
    padding: 1.5rem;
    padding-bottom: 1.5rem;
}

.io-module__form-collapsed {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.io-module__form-expanded {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.io-module__device-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 8. 测试进度卡片 */
.io-module__progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.io-module__progress-stats {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
    text-align: center;
}

.io-module__stat-card {
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid;
}

.io-module__stat-card--success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
    border-color: rgba(34, 197, 94, 0.2);
}

.io-module__stat-card--danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));
    border-color: rgba(239, 68, 68, 0.2);
}

.io-module__stat-card--neutral {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.1), rgba(148, 163, 184, 0.1));
    border-color: rgba(156, 163, 175, 0.2);
}

.io-module__stat-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.io-module__stat-label {
    font-size: 0.75rem;
    font-weight: 500;
}

/* ===== 新增：优化字体类系统 ===== */

/* 主标题优化 - 系统标题使用 */
.io-module__title-main {
    font-size: var(--io-font-size-2xl);
    font-weight: 700;
    line-height: 1.2;
}

/* 功能标题优化 - 卡片标题使用 */
.io-module__title-section {
    font-size: var(--io-font-size-xl);
    font-weight: 600;
    line-height: 1.3;
}

/* 子标题优化 - 副标题使用 */
.io-module__title-sub {
    font-size: var(--io-font-size-lg);
    font-weight: 500;
    line-height: 1.4;
}

/* 操作按钮文字优化 */
.io-module__text-button {
    font-size: var(--io-font-size-xs);
    font-weight: 500;
    line-height: 1.2;
}

/* 日志文字优化 */
.io-module__text-log {
    font-size: var(--io-font-size-xs);
    font-family: 'SF Mono', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
    line-height: 1.4;
}

/* 状态指示文字优化 */
.io-module__text-status {
    font-size: var(--io-font-size-xs);
    font-weight: 500;
    line-height: 1.3;
}

/* 9. 测试项目卡片 */
.io-module__test-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.io-module__test-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid var(--io-card-border);
    transition: all 0.3s ease;
}

.io-module__test-item:hover {
    border-color: var(--io-card-hover-border);
}

.io-module__test-item-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.io-module__test-item-icon {
    padding: 0.5rem;
    border-radius: 0.5rem;
}

.io-module__test-item-details {
    display: flex;
    flex-direction: column;
}

.io-module__test-item-name {
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--io-text-primary);
}

.io-module__test-item-meta {
    font-size: 0.75rem;
    color: var(--io-text-secondary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.io-module__test-item-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.io-module__test-item-buttons {
    display: flex;
    gap: 0.5rem;
}

/* ===== IO模块专用过渡动画 - 性能优化，避免通配符 ===== */
.io-module__card,
.io-module__toolbar,
.io-module__test-item,
.io-module__stat-card,
.io-module__icon {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Element Plus 组件过渡动画限定 */
.io-module__main .el-button,
.io-module__main .el-input__wrapper,
.io-module__main .el-tag {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease !important;
}

/* ===== 保持现有功能兼容性的通用样式类 ===== */

/* 自定义样式 */
.glass-effect {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.gradient-bg {
    background: var(--io-bg-primary);
    position: relative;
    overflow: hidden;
    height: 100vh;
}

.gradient-bg::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--io-bg-overlay);
    pointer-events: none;
}

.card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s ease;
    pointer-events: none;
}

.card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px -12px var(--io-card-hover-shadow);
    border-color: var(--io-card-hover-border);
}

.card-hover:hover::before {
    left: 100%;
}

/* 状态指示器 */
.status-indicator {
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.status-connected::after {
    background: #10b981;
    opacity: 0.75;
}

.status-disconnected::after {
    background: #ef4444;
    opacity: 0.75;
}

/* 卡片折叠动画 */
.collapse-enter-active,
.collapse-leave-active {
    transition: all 0.5s ease;
    overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
    max-height: 0;
    opacity: 0;
}

.collapse-enter-to,
.collapse-leave-from {
    max-height: 1000px;
    opacity: 1;
}

/* 主题样式类 */
.theme-card {
    background: var(--io-card-bg) !important;
    border-color: var(--io-card-border) !important;
    color: var(--io-text-primary);
}

.theme-label {
    color: var(--io-text-secondary) !important;
}

.theme-separator {
    border-color: var(--io-separator-color) !important;
}

.theme-text-primary {
    color: var(--io-text-primary) !important;
}

.theme-text-secondary {
    color: var(--io-text-secondary) !important;
}

.theme-text-tertiary {
    color: var(--io-text-tertiary) !important;
}

/* 主题切换按钮特殊样式 */
.theme-toggle-btn {
    width: 40px !important;
    height: 40px !important;
    border-radius: 12px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: var(--io-accent-blue-light) !important;
    border: 2px solid var(--io-card-border) !important;
    color: var(--io-accent-blue) !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.theme-toggle-btn:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 8px 25px -8px var(--io-card-hover-shadow) !important;
    border-color: var(--io-card-hover-border) !important;
}

/* IO模块专用Element Plus输入框和选择框主题适配 - 限定作用域 */
.io-module__main .el-input__wrapper,
.io-module__main .el-select .el-input__wrapper {
    background-color: var(--io-card-bg) !important;
    border-color: var(--io-card-border) !important;
    box-shadow: 0 0 0 1px var(--io-card-border) inset !important;
    border-radius: 4px !important;
}

.io-module__main .el-input__inner {
    color: var(--io-text-primary) !important;
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

.io-module__main .el-input__inner::placeholder {
    color: var(--io-text-tertiary) !important;
}

.io-module__main .el-textarea__inner {
    background-color: var(--io-card-bg) !important;
    color: var(--io-text-primary) !important;
    outline: none !important;
    border: none !important;
    box-shadow: 0 0 0 1px var(--io-card-border) inset !important;
    border-radius: 4px !important;
}

/* IO模块浅色主题特殊适配 */
.io-module__main .el-input__wrapper,
.io-module__main .el-select .el-input__wrapper,
.io-module__main .el-textarea__inner {
    background-color: rgba(248, 250, 252, 0.8) !important;
}

/* IO模块深色主题特殊适配 - 使用更高优先级 */
[data-theme="dark"] .io-module__main .el-input__wrapper,
[data-theme="dark"] .io-module__main .el-select .el-input__wrapper,
[data-theme="dark"] .io-module__main .el-textarea__inner {
    background-color: rgba(51, 65, 85, 0.8) !important;
    border-color: rgba(71, 85, 105, 0.6) !important;
}

/* IO模块深色主题下只读输入框专门优化 */
[data-theme="dark"] .io-module__main .el-input[readonly] .el-input__wrapper,
[data-theme="dark"] .io-module__main .el-input.is-disabled .el-input__wrapper,
[data-theme="dark"] .io-module__main .bg-gray-50 .el-input__wrapper {
    background-color: rgba(15, 23, 42, 0.95) !important;
    border-color: rgba(30, 41, 59, 0.8) !important;
    color: rgba(148, 163, 184, 0.9) !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .io-module__main .el-input[readonly] .el-input__inner,
[data-theme="dark"] .io-module__main .el-input.is-disabled .el-input__inner,
[data-theme="dark"] .io-module__main .bg-gray-50 .el-input__inner {
    background-color: transparent !important;
    color: rgba(148, 163, 184, 0.9) !important;
    cursor: default !important;
}

/* 强制覆盖Tailwind的bg-gray-50类在深色主题下的显示 */
[data-theme="dark"] .io-module__main .bg-gray-50 {
    background-color: rgba(15, 23, 42, 0.95) !important;
}

/* IO模块输入框聚焦状态 */
.io-module__main .el-input__wrapper:focus,
.io-module__main .el-input__wrapper.is-focus,
.io-module__main .el-textarea__inner:focus,
.io-module__main .el-select .el-input__wrapper:focus,
.io-module__main .el-select .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px var(--io-accent-blue) !important;
    border-color: var(--io-accent-blue) !important;
    outline: none !important;
}

/* ===== 增强按钮交互反馈 ===== */
.io-module__main .el-button {
    background: var(--io-card-bg) !important;
    border-color: var(--io-card-border) !important;
    color: var(--io-text-primary) !important;
    position: relative !important;
    overflow: hidden !important;
    transform-style: preserve-3d !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.io-module__main .el-button:hover {
    background: var(--io-accent-blue-light) !important;
    border-color: var(--io-card-hover-border) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 8px 25px -8px var(--io-card-hover-shadow) !important;
}

.io-module__main .el-button:active {
    transform: translateY(1px) scale(0.98) !important;
    transition: all 0.1s ease !important;
}

/* 主要操作按钮特殊效果 */
.io-module__main .el-button--primary {
    background: linear-gradient(135deg, var(--io-accent-blue), var(--io-accent-purple)) !important;
    border: none !important;
    color: white !important;
    box-shadow: var(--io-shadow-glow) !important;
}

.io-module__main .el-button--primary:hover {
    background: linear-gradient(135deg, var(--io-accent-cyan), var(--io-accent-blue)) !important;
    box-shadow: var(--io-shadow-intense) !important;
}

.io-module__main .el-button--success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3) !important;
}

.io-module__main .el-button--success:hover {
    background: linear-gradient(135deg, var(--io-accent-green), #10b981) !important;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4) !important;
}

/* ===== 增强进度条样式 ===== */
.custom-progress {
    height: 16px;
    border-radius: 8px;
    background: linear-gradient(90deg, 
        rgba(59, 130, 246, 0.1), 
        rgba(139, 92, 246, 0.1)
    );
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-progress-bar {
    height: 100%;
    border-radius: 8px;
    background: linear-gradient(90deg, 
        var(--io-accent-cyan), 
        #3b82f6, 
        var(--io-accent-purple)
    );
    position: relative;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.custom-progress-bar::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 2.5s ease-in-out infinite !important;
    border-radius: 8px;
    z-index: 1;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 测试项目背景主题适配 */
.io-module__main .test-item-bg {
    background: var(--io-card-bg) !important;
    border-color: var(--io-card-border) !important;
}

.io-module__main .test-item-bg:hover {
    border-color: var(--io-card-hover-border) !important;
}

.io-module__main .test-item-bg.active {
    background: var(--io-accent-blue-light) !important;
    border-color: var(--io-card-hover-border) !important;
}

/* 深色主题测试项目特殊适配 */
[data-theme="dark"] .io-module__main .test-item-bg {
    background: rgba(51, 65, 85, 0.6) !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
}

[data-theme="dark"] .io-module__main .test-item-bg:hover {
    border-color: rgba(59, 130, 246, 0.5) !important;
}

[data-theme="dark"] .io-module__main .test-item-bg.active {
    background: rgba(59, 130, 246, 0.2) !important;
    border-color: rgba(59, 130, 246, 0.5) !important;
}

/* IO模块Element Plus 标签布局完全重写 - 图标在左侧文字在右侧 */
.io-module__main .el-tag {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex-direction: row !important;
    gap: 6px !important;
    padding: 2px 8px !important;
    white-space: nowrap !important;
    height: auto !important;
}

/* IO模块移除所有标签内元素的默认边距和尺寸 */
.io-module__main .el-tag *,
.io-module__main .el-tag > *,
.io-module__main .el-tag i,
.io-module__main .el-tag span,
.io-module__main .el-tag svg {
    margin: 0 !important;
    padding: 0 !important;
}

/* IO模块图标样式 - 确保在最左侧 */
.io-module__main .el-tag i[data-lucide],
.io-module__main .el-tag .lucide,
.io-module__main .el-tag svg,
.io-module__main .el-tag .el-icon {
    width: 12px !important;
    height: 12px !important;
    flex-shrink: 0 !important;
    display: inline-block !important;
}

/* IO模块文字样式 - 在图标右侧 */
.io-module__main .el-tag span {
    font-size: 12px !important;
    line-height: 1.2 !important;
    flex-shrink: 0 !important;
    display: inline-block !important;
}

/* IO模块深色主题下各种状态标签样式 */
[data-theme="dark"] .io-module__main .el-tag--info {
    background: rgba(51, 65, 85, 0.8) !important;
    border-color: rgba(71, 85, 105, 0.6) !important;
    color: var(--io-text-secondary) !important;
}

[data-theme="dark"] .io-module__main .el-tag--success {
    background: rgba(5, 46, 22, 0.8) !important;
    border-color: rgba(22, 101, 52, 0.6) !important;
    color: #86efac !important;
}

[data-theme="dark"] .io-module__main .el-tag--danger {
    background: rgba(69, 10, 10, 0.8) !important;
    border-color: rgba(127, 29, 29, 0.6) !important;
    color: #fca5a5 !important;
}

/* 新增：深色主题下待测统计卡片文字颜色优化 */
[data-theme="dark"] .io-module__main .io-module__stat-card--neutral .io-module__stat-value,
[data-theme="dark"] .io-module__main .io-module__stat-card--neutral .io-module__stat-label {
    color: var(--io-text-secondary) !important;
}

/* ===== 保持必要的兼容性样式（已在公共样式中但需要具体实现）===== */

/* 页面内容容器滚动条隐藏 */
.main-content-area {
    overflow: hidden;
}

/* 左右分栏滚动条样式优化 */
.form-section,
.test-section {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.form-section::-webkit-scrollbar,
.test-section::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
}

.form-section:hover::-webkit-scrollbar,
.test-section:hover::-webkit-scrollbar {
    display: none;
}

/* 顶部工具栏容器对齐优化 */
.toolbar-container {
    max-width: calc(100% - 48px);
    margin: 0 auto;
}

/* 测试日志样式 */
.log-container {
    background: #111827;
    color: #f3f4f6;
    font-family: 'Courier New', monospace;
}

.log-success { color: #34d399; }
.log-error { color: #f87171; }
.log-warning { color: #fbbf24; }
.log-info { color: #60a5fa; }
.log-system { color: #a78bfa; }

/* 加载状态优化 */
.el-loading-mask {
    background: rgba(99, 102, 241, 0.1);
    backdrop-filter: blur(10px);
}

.el-loading-spinner .el-loading-text {
    color: #6366f1;
    font-weight: 600;
}

/* 脉冲动画点 */
.animate-pulse-dot {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 响应式布局 */
@media (max-width: 1400px) {
    .io-module__grid-4 {
        grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
}

@media (max-width: 1200px) {
    .io-module__grid-4,
    .io-module__grid-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
    
    /* 工具栏移动端优化 */
    .io-module__toolbar-container {
        margin-left: 1rem;
        margin-right: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .io-module__toolbar-left {
        justify-content: center;
    }
    
    .io-module__toolbar-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    /* 主要布局调整 */
    .io-module__main-content {
        flex-direction: column !important;
        height: auto !important;
    }
    
    .io-module__form-section,
    .io-module__test-section {
        width: 100% !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
    
    .io-module__test-section {
        border-left: none !important;
        border-top: 1px solid var(--io-toolbar-border) !important;
        padding-top: 1rem !important;
    }
    
    /* 网格系统移动端适配 */
    .io-module__grid-4,
    .io-module__grid-3,
    .io-module__grid-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
        gap: 0.75rem !important;
    }
    
    /* 统计卡片移动端适配 */
    .io-module__progress-stats {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        gap: 0.5rem !important;
    }
    
    /* 测试日志移动端适配 */
    .io-module__log-layout {
        flex-direction: column !important;
        height: auto !important;
        gap: 1rem;
    }
    
    .io-module__log-stats {
        width: 100% !important;
        display: grid !important;
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        gap: 0.5rem !important;
    }
    
    .io-module__log-content {
        width: 100% !important;
        height: 300px !important;
    }
    
    /* 测试项目移动端优化 */
    .io-module__test-item {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 1rem !important;
        padding: 1rem !important;
    }
    
    .io-module__test-item-actions {
        width: 100% !important;
        justify-content: space-between !important;
    }
    
    .io-module__test-item-buttons {
        gap: 0.25rem !important;
    }
    
    .io-module__test-item-buttons .el-button {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.5rem !important;
        height: auto !important;
    }
}

@media (max-width: 480px) {
    /* 超小屏幕优化 */
    .io-module__toolbar-container {
        margin: 0.25rem;
        padding: 0.75rem;
    }
    
    .io-module__form-section,
    .io-module__test-section {
        padding: 0.75rem !important;
    }
    
    .io-module__card-content {
        padding: 0.75rem !important;
    }
    
    .io-module__test-item {
        padding: 0.75rem !important;
    }
    
    /* 统计卡片改为单列 */
    .io-module__progress-stats {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }
    
    /* 日志统计改为单列 */
    .io-module__log-stats {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }
}

/* 动画效果 */
@keyframes ping {
    75%, 100% {
        transform: scale(2);
        opacity: 0;
    }
} 

/* 修复完成：IOModuleVue.css样式重构 */
/* 
 * 问题解决报告：
 * 1. 移除了无效的@import依赖
 * 2. 替换所有空映射注释为具体的样式实现
 * 3. 直接在.io-module__main中定义所有CSS变量
 * 4. 添加完整的BEM架构样式
 * 5. 实现所有Element Plus组件适配
 * 6. 添加响应式设计支持
 * 7. 保留IO模块特有的功能组件
 * 
 * 现在样式已完全生效，与CouplerVue.css保持100%视觉一致性
 */ 

/* ===== 新增：IO模块表单组件主题适配 ===== */
.io-module__main .el-form-item__label {
    color: var(--io-text-secondary) !important;
}

/* 新增：深色主题下 Element Plus 下拉框面板和选项背景色 */
[data-theme="dark"] .el-select-dropdown,
[data-theme="dark"] .el-popper.is-light {
    background-color: #1e293b !important; /* 深色背景 */
    border: 1px solid rgba(71, 85, 105, 0.6) !important;
    color: #d1d5db !important;
}
[data-theme="dark"] .el-select-dropdown__item {
    color: #d1d5db !important;
}
[data-theme="dark"] .el-select-dropdown__item:hover {
    background-color: rgba(59, 130, 246, 0.1) !important;
}
[data-theme="dark"] .el-select-dropdown__item.is-selected {
    background-color: rgba(37, 99, 235, 0.15) !important;
    color: #ffffff !important;
}

/* 深色主题下 el-select 输入框(wrapper)背景修正 */
[data-theme="dark"] .io-module__main .el-select__wrapper {
    background-color: rgba(51, 65, 85, 0.8) !important;
    border-color: rgba(71, 85, 105, 0.6) !important;
    box-shadow: 0 0 0 1px rgba(71, 85, 105, 0.6) inset !important;
}
[data-theme="dark"] .io-module__main .el-select__placeholder {
    color: var(--io-text-tertiary) !important;
}
[data-theme="dark"] .io-module__main .el-select.is-disabled .el-select__wrapper,
[data-theme="dark"] .io-module__main .el-select.is-disabled .el-select__inner {
    background-color: rgba(15, 23, 42, 0.95) !important;
    border-color: rgba(30, 41, 59, 0.8) !important;
    color: rgba(148, 163, 184, 0.9) !important;
}

/* === IO模块版本信息顶部标签布局 === */
.io-top-label {
    display: flex !important;          /* 使用 flex 布局 */
    flex-direction: column !important; /* 垂直堆叠标签和输入框 */
    align-items: stretch !important;   /* 撑满父容器 */
    width: 100% !important;            /* 保持自适应宽度 */
}
.io-top-label .el-form-item__label {
    float: none !important; /* 取消左浮动 */
    display: block !important; /* 转为块级，置于上方 */
    margin: 0 0 4px 0 !important; /* 下方间距 */
    text-align: left !important; /* 左对齐 */
}
.io-top-label .el-form-item__content {
    margin-left: 0 !important; /* 去除默认左边距 */
} 