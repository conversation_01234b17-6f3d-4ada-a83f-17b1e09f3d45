/* 单板测试页面样式 */
:root {
  /* shadcn/ui 的基础配色 */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

.app-main #board-test-content {
    height: calc(100vh - var(--header-height) - 40px - 41px);
    display: flex;
    justify-content: center;
    align-items: stretch;
    padding: 0;
    box-sizing: border-box;
    overflow: auto;
    background-color: hsl(var(--background));
}

.app-main #board-test-content .container {
    width: 100%;
    max-width: none;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    border-radius: var(--radius);
    background-color: hsl(var(--background));
    border: 1px solid hsl(var(--border));
}

/* 添加标题样式 */
.app-main #board-test-content .container > h3 {
    margin: 0 0 1.25rem 0;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid hsl(var(--border));
    color: hsl(var(--foreground));
    font-size: 1.5rem;
    font-weight: 600;
}

.app-main #board-test-content .header {
    display: flex;
    align-items: center;
    background-color: hsl(var(--card));
    padding: 1rem;
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    gap: 1rem;
}

.app-main #board-test-content .header select {
    flex: 2;
    padding: 0.5rem 1rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-size: 0.9375rem;
    transition: all 0.2s;
}

.app-main #board-test-content .header select:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

.app-main #board-test-content .header button {
    flex: 1;
    padding: 0.5rem;
    border: none;
    border-radius: var(--radius);
    font-size: 0.9375rem;
    font-weight: 500;
    color: hsl(var(--primary-foreground));
    transition: opacity 0.2s;
    cursor: pointer;
}

.app-main #board-test-content .header .load-btn {
    background-color: hsl(var(--primary));
}

.app-main #board-test-content .header .start-btn {
    background-color: hsl(142.1 76.2% 36.3%);
}

.app-main #board-test-content .header .finish-btn {
    background-color: hsl(346.8 77.2% 49.8%);
}

.app-main #board-test-content .header button:hover {
    opacity: 0.9;
}

.app-main #board-test-content .content {
    display: flex;
    gap: 1.25rem;
    margin-top: 1.25rem;
    flex: 1;
    min-height: 0;
}

/* 修改左侧列的宽度 */
.app-main #board-test-content .left-column {
    width: 65%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    padding: 1rem;
}

/* 修改右侧列的宽度，确保与左侧对齐 */
.app-main #board-test-content .right-column {
    width: 35%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.app-main #board-test-content .test-item {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    margin: 0.5rem 0;
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    transition: all 0.2s;
}

.app-main #board-test-content .test-item:hover {
    border-color: hsl(var(--ring));
    background-color: hsl(var(--accent));
}

.app-main #board-test-content .test-item label {
    flex: 0 0 180px;
    color: hsl(var(--foreground));
    font-weight: 500;
    font-size: 0.9375rem;
    padding-right: 1rem;
}

.app-main #board-test-content .test-item input[type="text"] {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-size: 0.875rem;
    transition: all 0.2s;
}

.app-main #board-test-content .test-item input[type="text"]:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

.app-main #board-test-content .test-item input[type="checkbox"] {
    margin-left: 10px;
}

.app-main #board-test-content .test-item .result {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-left: 1.5rem;
    padding-left: 1.5rem;
    border-left: 1px solid hsl(var(--border));
}

.app-main #board-test-content .test-item .result label {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    flex: 0 0 auto;
    padding-right: 0;
    cursor: pointer;
    font-size: 0.875rem;
}

.app-main #board-test-content .test-item .result input[type="checkbox"] {
    appearance: none;
    width: 1rem;
    height: 1rem;
    border: 2px solid hsl(var(--border));
    border-radius: calc(var(--radius) * 0.5);
    cursor: pointer;
    transition: all 0.2s;
}

.app-main #board-test-content .test-item .result input[type="checkbox"]:checked {
    background-color: hsl(var(--primary));
    border-color: hsl(var(--primary));
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3E%3C/svg%3E");
}

.app-main #board-test-content .result label.pass {
    color: #2e7d32;
    font-weight: 600;
}

.app-main #board-test-content .result label.ng {
    color: #d32f2f;
    font-weight: 600;
}

.app-main #board-test-content .device-info,
.app-main #board-test-content .test-results {
    background-color: hsl(var(--card));
    border-radius: var(--radius);
    padding: 1.25rem;
    border: 1px solid hsl(var(--border));
}

.app-main #board-test-content .device-info h3,
.app-main #board-test-content .test-results h3 {
    color: hsl(var(--foreground));
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid hsl(var(--border));
}

.app-main #board-test-content .device-info textarea,
.app-main #board-test-content .test-results textarea {
    width: 100%;
    min-height: 100px;
    padding: 0.75rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-size: 0.875rem;
    resize: vertical;
    transition: all 0.2s;
}

.app-main #board-test-content .device-info textarea:focus,
.app-main #board-test-content .test-results textarea:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-main #board-test-content .content {
        flex-direction: column;
    }

    .app-main #board-test-content .left-column,
    .app-main #board-test-content .right-column {
        width: 100%;
    }

    .app-main #board-test-content .test-item {
        flex-wrap: wrap;
    }

    .app-main #board-test-content .header {
        flex-direction: column;
    }

    .app-main #board-test-content .header select,
    .app-main #board-test-content .header button {
        width: 100%;
    }
}

/* 添加小屏幕的特殊处理 */
@media (max-width: 480px) {
    .app-main #board-test-content .header {
        flex-direction: column;
    }

    .app-main #board-test-content .header select,
    .app-main #board-test-content .header button {
        width: 100%;
        max-width: none;
    }

    .app-main #board-test-content .device-info,
    .app-main #board-test-content .test-results {
        padding: 0.75rem;
    }
}

/* 确保文本区域在小屏幕上也能正常显示 */
.app-main #board-test-content .device-info textarea,
.app-main #board-test-content .test-results textarea {
    width: 100%;
    min-height: 100px; /* 设置最小高度 */
    box-sizing: border-box;
}

/* 优化滚动条样式 */
.app-main #board-test-content .left-column::-webkit-scrollbar,
.app-main #board-test-content .right-column::-webkit-scrollbar {
    width: 6px;
}

.app-main #board-test-content .left-column::-webkit-scrollbar-thumb,
.app-main #board-test-content .right-column::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

/* 在文件末尾添加以下样式 */

.app-main #board-test-content .device-info textarea,
.app-main #board-test-content .test-results textarea {
    width: 100%;
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    resize: vertical;
}

.app-main #board-test-content .device-info,
.app-main #board-test-content .test-results {
    display: flex;
    flex-direction: column;
    height: 48%;
}

.app-main #board-test-content .device-info h3,
.app-main #board-test-content .test-results h3 {
    margin-bottom: 10px;
}

/* 在文件末尾添加以下样式 */

.app-main #board-test-content .test-item .result-input.pass {
    background-color: #d4edda;
}

.app-main #board-test-content .test-item .result-input.ng {
    background-color: #f8d7da;
}

.app-main #board-test-content .test-item .result {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.app-main #board-test-content .test-item .result input[type="checkbox"] {
    margin-right: 5px;
}

.app-main #board-test-content .test-item .result label {
    margin-right: 10px;
}

/* 示例：将固定的像素值转换为 rem 单位 */
.app-main #board-test-content {
    /* 原代码 */
    padding: 20px;
    /* 修改后 */
    padding: 1.25rem;
}

.app-main #board-test-content .header button {
    /* 原代码 */
    padding: 10px;
    font-size: 16px;
    /* 修改后 */
    padding: 0.625rem;
    font-size: 1rem;
}

/* 继续将其他固定像素值替换为 rem 单位 */

/* 示例：使用 clamp() 设置响应式字体大小 */
.app-main #board-test-content h3 {
    /* 原代码 */
    font-size: 16px;
    /* 修改后 */
    font-size: clamp(1rem, 2vw, 1.5rem);
}

/* 继续在其他需要调整的元素中应用 clamp() */

/* 状态样式 */
.app-main #board-test-content .test-item .result-input.pass {
    background-color: hsl(142.1 76.2% 36.3% / 0.1);
    border-color: hsl(142.1 76.2% 36.3%);
}

.app-main #board-test-content .test-item .result-input.ng {
    background-color: hsl(346.8 77.2% 49.8% / 0.1);
    border-color: hsl(346.8 77.2% 49.8%);
}

/* 滚动条美化 */
.app-main #board-test-content ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.app-main #board-test-content ::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
}

.app-main #board-test-content ::-webkit-scrollbar-thumb {
    background: #b0bec5;
    border-radius: 3px;
    border: 2px solid #f5f7fa;
}

.app-main #board-test-content ::-webkit-scrollbar-thumb:hover {
    background: #90a4ae;
}

