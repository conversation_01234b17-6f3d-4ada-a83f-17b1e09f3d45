:root {
  /* shadcn/ui 的基础配色 */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 1.5rem;
    background-color: hsl(var(--background));
}

h1 {
    color: hsl(var(--foreground));
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.project-record {
    margin-top: 2rem;
}

.grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr;
    margin-bottom: 2rem;
}

@media (min-width: 1024px) {
    .grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2.5rem;
    }
}

.card {
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    padding: 1.5rem;
}

.card-title {
    color: hsl(var(--card-foreground));
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid hsl(var(--border));
}

.scroll-area {
    height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary)) hsl(var(--muted));
}

.scroll-area::-webkit-scrollbar {
    width: 6px;
}

.scroll-area::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
}

.scroll-area::-webkit-scrollbar-thumb {
    background-color: hsl(var(--primary));
    border-radius: 3px;
}

.item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: var(--radius);
    transition: background-color 0.2s;
    border-bottom: 1px solid hsl(var(--border));
}

.item:last-child {
    border-bottom: none;
}

.item:hover {
    background-color: hsl(var(--accent));
}

.custom-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    margin-bottom: 0.75rem;
    background-color: hsl(var(--card));
    transition: all 0.2s;
}

.custom-item:hover {
    border-color: hsl(var(--ring));
    background-color: hsl(var(--accent));
}

.button {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    cursor: pointer;
    font-weight: 500;
    transition: opacity 0.2s;
}

.button:hover {
    opacity: 0.9;
}

.button-delete {
    background-color: hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.button-delete:hover {
    opacity: 0.9;
}

.flex {
    display: flex;
    gap: 0.75rem;
    align-items: stretch;
}

/* 优化故障品录入页面的复选框样式 */
#fault-entry-content input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    display: inline-block;
    box-sizing: border-box;
    width: 0.875rem;
    height: 0.875rem;
    border: 1px solid hsl(var(--border));
    border-radius: 0;
    cursor: pointer;
    position: relative;
    padding: 0;
    margin: 0;
    vertical-align: middle;
    transition: all 0.2s;
    background-color: hsl(var(--background));
}

#fault-entry-content input[type="checkbox"]:checked {
    background-color: hsl(var(--primary));
    border-color: hsl(var(--primary));
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.625rem;
}

#fault-entry-content input[type="checkbox"]:hover {
    border-color: hsl(var(--ring));
    background-color: hsl(var(--background));
}

#fault-entry-content input[type="checkbox"]:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

.input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-size: 0.95rem;
    transition: all 0.2s;
    height: 2.75rem;
}

.input:focus {
    outline: none;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

.submit-button {
    display: block;
    width: 100%;
    padding: 0.75rem;
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    margin-top: 1.5rem;
    transition: opacity 0.2s;
}

.submit-button:hover {
    opacity: 0.9;
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .grid {
        gap: 1rem;
    }
    
    .card {
        padding: 1rem;
    }
    
    .flex {
        flex-direction: column;
    }
    
    .button {
        width: 100%;
    }
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(5px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.custom-item {
    animation: fadeIn 0.2s ease-out;
}
