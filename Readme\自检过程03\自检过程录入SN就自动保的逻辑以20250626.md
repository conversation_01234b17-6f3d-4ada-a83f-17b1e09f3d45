好的，我已经仔细阅读了 `SelfInspection.js` 的代码以及相关的后端路由 `quality_inspection.py`。现在为您解答这两个问题。

### 1. 输入工单号后，系统如何自动获取产品型号？

这个功能实现的关键在于前端的事件监听和后端的API接口。

1.  **监听输入框事件**：在 `SelfInspection.js` 的 `initPage` 函数中，代码为“工单号”输入框（ID为 `orderNo`）添加了一个 `blur` 事件监听器。这意味着当您输入完工单号，然后鼠标点击其他地方使输入框失去焦点时，会触发一个函数。

2.  **调用加载函数**：这个事件会调用 `loadWorkOrderData(orderNo)` 函数，并将您输入的工单号传递进去。

3.  **请求工单详情API**：
    *   `loadWorkOrderData` 函数首先会向后端的 `/api/sn-print-record/get-work-order-details` 接口发起一个`fetch`网络请求。
    *   这个后端接口（可能在另一个服务或蓝图中定义）负责根据工单号查询“工单管理系统”的数据库。

4.  **填充产品型号**：
    *   当后端成功找到工单并返回其详细信息后，前端会从返回的JSON数据中解析出产品型号（`data.order.ord_productModel`）。
    *   最后，代码将获取到的产品型号字符串，自动填充到“产品型号”的输入框（ID为 `serialNo`）中。

所以，整个流程是：**用户输入工单 -> 输入框失去焦点 -> 前端JS捕获事件 -> 调用API获取工单详情 -> 后端查询并返回数据 -> 前端用返回的数据填充产品型号输入框**。

### 2. 录入SN后是否会自动保存？

是的，**SN（产品序列号）在添加后会立即自动保存到数据库中**，但这和最终“提交检验结果”是两个步骤。

这个过程的实现逻辑分散在前端的 `SelfInspection.js`、`ScanSN.js` 和后端的 `quality_inspection.py` 中。

1.  **触发SN扫描界面**：当您在某个检验阶段（如首检、自检）点击“提交检验”按钮时，如果尚未选择任何产品，系统会调用 `showScanSerialNumberUI` 函数，弹出一个专门用于扫描或录入SN的界面。这部分的UI逻辑主要由 `ScanSN.js` 文件处理。

2.  **添加产品到工单**：
    *   在SN录入界面，当您输入一个SN并确认添加时，前端会调用一个类似 `addProductToWorkOrder` 的函数。
    *   这个函数会立即向后端的 `POST /api/quality-inspection/work-order/<work_order_id>/products` 接口发起请求。

3.  **后端实时保存**：
    *   后端的 `quality_inspection.py` 文件中的 `add_product` 函数会处理这个请求。
    *   该函数会创建一个新的 `Product` 记录，将传入的SN号和当前工单ID关联起来，并立刻通过 `db.session.commit()` 将这条新记录保存到数据库中。

4.  **前端状态更新**：后端保存成功后，会返回新创建的产品信息（包括数据库中的唯一ID）。前端接收到这个信息后，会将这个产品ID添加到一个临时的本地列表 `inspectionData.selectedProductIds` 中，并更新UI，告诉您这个SN已经成功添加。

5.  **提交检验结果**：当您完成了所有需要检验的产品的SN录入后，点击最终的提交按钮（例如“部分提交”或“最终提交”）。这时，前端会将之前收集在 `inspectionData.selectedProductIds` 列表中的所有产品ID，连同检验项目的结果，一起提交给后端的 `/api/quality-inspection/records` 接口。这一步是保存**检验结果**，而不是保存SN本身。

**总结一下**：

*   **SN的保存是实时的**：每添加一个SN，它就会被立刻发送到后端并存入数据库。这可以看作是“自动保存”。
*   **检验结果的保存是批量的**：您可以在一次检验中添加多个SN，最后再统一提交这些SN对应的检验结果。

这种设计将“产品入库”和“产品检验”两个动作分开，确保了即使在检验过程中断，已经录入的SN信息也不会丢失。



问题2：自检过程完整的功能逻辑
好的，我将仔细分析 `ScanSN.js` 文件，以了解“自检过程”页面中SN号录入的实现细节。

为了全面理解其工作原理，我将分步解析整个流程，包括UI的动态构建、用户输入处理、后端验证逻辑以及前端状态管理。

首先，我需要阅读 `ScanSN.js` 文件中与SN扫描功能最相关的部分，特别是 `showScanSerialNumberUI` 和 `processScanedSN` 这两个核心函数，它们分别负责UI的创建和SN号的处理逻辑。
Read file: static/page_js_css/ScanSN.js

好的，我已经获取了 `showScanSerialNumberUI` 和 `processScanedSN` 函数的源码。

`showScanSerialNumberUI` 函数负责动态生成SN扫描界面的HTML，包括输入框、确认按钮、已扫描列表和开始检验按钮，并将其注入到指定的容器中。同时，它会为SN输入框绑定回车事件，方便快速录入。

`processScanedSN` 函数是核心处理器。当用户输入SN并点击确认（或按回车）后，此函数被触发。它会执行以下操作：
1.  获取并清理用户输入的SN号。
2.  检查SN号是否在本地的 `window.currentScannedProducts` 数组中，防止重复扫描。
3.  调用后端的 `checkSerialNumber` 接口来验证SN。

现在，为了理解完整的验证和处理流程，我需要继续分析 `checkSerialNumber` 的后续调用函数，包括 `addProductToWorkOrder` (添加新产品到工单) 和 `addToScannedList` (将SN更新到前端UI列表)。我将阅读这部分相关的代码。
Read file: static/page_js_css/ScanSN.js

好的，我已经分析了后端通信和数据处理部分。

关键逻辑如下：
1.  `checkSerialNumber` 函数向 `/api/quality-inspection/check-serial-number` 发送GET请求，检查SN号是否存在于当前工单、其他工单，或者是一个全新的SN。
2.  `processScanedSN` 根据 `checkSerialNumber` 的返回结果，决定是调用 `addProductToWorkOrder` (用于添加新产品或普通返工产品) 还是 `addReworkProduct` (用于处理已存在于其他工单的返工产品)。
3.  `addProductToWorkOrder` 向 `/api/quality-inspection/work-order/{id}/products` 发送POST请求，在后端创建产品与工单的关联。它还包含了处理SN号已存在等边界情况的逻辑。
4.  `addToScannedList` 函数负责将成功处理的SN号添加到一个全局数组 `window.currentScannedProducts` 中，并调用 `updateScannedProductList` 来刷新前端界面。

现在，为了完成整个流程的分析，我需要查看 `updateScannedProductList` 是如何将数据显示在界面上的，以及 `startInspection` 函数在用户点击“开始检验”按钮后会执行什么操作。我将阅读这最后一部分核心代码。
Read file: static/page_js_css/ScanSN.js

好的，通过对 `ScanSN.js` 文件的分步分析，我已经完整地梳理了“自检过程”页面SN号录入的实现机制。整个过程可以概括为以下五个核心步骤：

### 1. 动态构建UI界面 (`showScanSerialNumberUI`)

当流程进入需要扫描SN的环节时，此函数被调用。

*   **创建界面**: 它会动态生成一套完整的HTML结构，包含SN输入框、"确认"按钮、"已扫描产品"列表（包括计数和清空按钮）以及"开始检验"按钮。
*   **信息展示**: 界面上会清晰地显示当前操作的上下文信息，如工单号、检验阶段（组装前、测试前等）和角色（首检、自检、IPQC）。
*   **注入页面**: 生成的HTML会被注入到当前活动的内容容器中（例如 `id="inspection-content"` 的 `div` 中）。
*   **事件绑定**: 为SN输入框绑定了键盘回车事件，用户输入后按回车即可触发确认，提升了操作效率。

### 2. SN号录入与前端验证 (`processScanedSN`)

这是用户输入SN后的核心处理函数。

*   **获取输入**: 从 `id="sn-input"` 的输入框中获取用户输入的SN号，并进行 `.trim()` 清理。
*   **前端去重**: 在与后端交互前，会首先检查该SN号是否已存在于 `window.currentScannedProducts` 这个前端维护的数组中。如果存在，则直接提示“该SN号已扫描”，避免了不必要的后端请求。
*   **调用后端验证**: 如果是新的SN号，则调用 `checkSerialNumber` 函数，将工单ID和SN号发送到后端进行深度验证。

### 3. 后端深度验证与数据处理 (API调用)

前端通过 `fetch` 调用一系列后端API来完成SN的验证和数据持久化。

*   **`checkSerialNumber`**:
    *   **API**: `GET /api/quality-inspection/check-serial-number`
    *   **功能**: 这是验证的核心。后端会检查这个SN号：
        1.  是否已存在于当前工单。
        2.  是否是全新的SN号。
        3.  是否已存在于其他工单（这对于“返工工单”的判断至关重要）。
*   **`addProductToWorkOrder`**:
    *   **API**: `POST /api/quality-inspection/work-order/{work_order_id}/products`
    *   **功能**: 当 `checkSerialNumber` 确认SN号是合法的（例如，一个全新的SN或返工流程中的SN），此函数会被调用，将SN号与当前工单ID关联，并在数据库中创建一条新的产品记录。
*   **`addReworkProduct`**:
    *   **API**: `POST /api/quality-inspection/work-order/{work_order_id}/rework-product`
    *   **功能**: 专门处理返工逻辑。当一个SN号已存在于其他旧工单时，此接口会创建一条新的、关联到旧产品的返工产品记录。

### 4. 前端状态更新与列表渲染 (`addToScannedList` & `updateScannedProductList`)

后端成功处理SN号后，前端会同步更新状态和UI。

*   **`addToScannedList`**:
    *   **更新数据**: 将后端返回的产品信息（`product_id`, `serial_no`, `is_rework`）作为一个对象 `push` 到全局的 `window.currentScannedProducts` 数组中。
    *   **启用按钮**: 当第一个SN被成功添加后，此函数会找到“开始检验”按钮并解除其 `disabled` 状态，允许用户进入下一步。
*   **`updateScannedProductList`**:
    *   **渲染列表**: 此函数被调用后，会遍历 `window.currentScannedProducts` 数组。
    *   **生成HTML**: 为数组中的每一个产品动态创建一个 `div.scanned-product-item`，包含SN号和一个红色的 "×" 删除按钮。如果产品是返工件，还会额外显示一个“返工”的徽章。
    *   **更新UI**: 将生成的HTML列表渲染到 `id="scanned-product-list"` 的容器中，并同时更新“已扫描产品”的数量。

### 5. 开始检验 (`startInspection` & `proceedToInspection`)

当用户完成SN扫描并点击“开始检验”后，执行以下流程：

*   **`startInspection`**:
    *   **数量检查**: 首先检查已扫描的产品数量是否大于0。
    *   **自检逻辑**: 如果当前是“自检”角色，它会额外调用后端API (`/remaining-products`) 检查该工单是否还有未扫描的产品。如果有，会弹窗提醒用户，但允许用户选择“继续检验”。
*   **`proceedToInspection`**:
    *   **保存产品ID**: 将 `window.currentScannedProducts` 数组中所有产品的ID提取出来，保存到 `inspectionData.selectedProductIds` 中。
    *   **加载检验项**: 调用 `loadInspectionItems` 函数，将产品ID列表传递给它，以便为这些特定的产品加载对应的检验项目。
    *   **切换视图**: 最后，调用 `switchTab` 函数，隐藏当前的SN扫描界面，并显示检验项目列表界面，流程正式进入产品检验环节。

总结来说，这个功能实现了一个完整且健壮的闭环：**UI动态生成 → 前端初步校验 → 后端深度验证 → 数据库持久化 → 前端状态与UI同步 → 流程转向下一环节**。代码结构清晰，职责分离，并对普通工单和返工工单等不同业务场景做了兼容处理。




问题3：如何实现把1写入到表quality_inspection_work_orders的”assembly_stage_completed、test_stage_completed、packaging_stage_completed“字段的，执行逻辑是什么
好的，我来为您分析将 `1`（或布尔值 `True`）写入 `quality_inspection_work_orders` 表中 `assembly_stage_completed`、`test_stage_completed` 和 `packaging_stage_completed` 这三个字段的执行逻辑。

这个过程的核心完全在后端，由一个关键函数 `check_work_order_completion` 驱动，并且由前端的“最终提交”操作触发。

### 执行逻辑分解

1.  **触发点：前端最终提交**
    *   当用户在前端界面的某个阶段（如组装前），为某个角色（如首检）点击提交，并选择 **“最终提交”** 时，整个流程被启动。
    *   前端 `SelfInspection.js` 中的 `saveInspectionRecords` 函数会被调用，它会向后端API `POST /api/quality-inspection/records` 发送一个请求。
    *   这个请求的数据包中，除了检验项目结果和产品ID列表外，最关键的是包含了一个标志位：`is_final_submission: true`。

2.  **后端入口：保存检验记录**
    *   在 `routes/quality_inspection.py` 文件中，`save_inspection_records` 视图函数接收到这个请求。
    *   该函数首先会处理并保存本次提交的检验状态（`InspectionStatus`），将 `submission_type` 字段标记为 `'final'`。
    *   在所有数据保存操作的最后，它会调用一个内部函数：`check_work_order_completion(session, work_order.id)`。这是整个逻辑的核心。

3.  **核心逻辑：`check_work_order_completion` 函数**
    该函数在 `routes/quality_inspection.py` 的大约第395行，其执行步骤如下：

    *   **a. 获取工单对象**：函数首先根据传入的 `work_order_id` 从数据库中查询到对应的 `QualityInspectionWorkOrder` 实例。

    *   **b. 检查“组装”阶段是否完成**：
        *   它会执行一个数据库查询，检查在 `inspection_statuses` 表中，对于当前的 `work_order_id` 和 `'assembly'` 阶段，是否存在**所有三个角色**（`'first'`, `'self'`, `'ipqc'`）的最终提交记录（即 `submission_type == 'final'`）。
        *   如果这个条件满足（即首检、自检、IPQC都已最终提交），它就会执行：`work_order.assembly_stage_completed = True`。

    *   **c. 检查“测试”阶段是否完成**：
        *   接着，它会以完全相同的方式检查 `'test'` 阶段。
        *   如果测试阶段的三个角色都已最终提交，它就会执行：`work_order.test_stage_completed = True`。

    *   **d. 检查“包装”阶段是否完成**：
        *   最后，它会检查 `'packaging'` 阶段。
        *   如果包装阶段的三个角色都已最终提交，它就会执行：`work_order.packaging_stage_completed = True`。

    *   **e. 提交数据库事务**：在检查完所有阶段并更新了工单对象的相应属性后，这些变更会通过 `session.commit()` 被永久保存到数据库中。在数据库层面，布尔值 `True` 会被存储为整数 `1`。

### 总结

简单来说，执行逻辑是：

**前端最终提交 -> 后端API保存记录 -> 调用 `check_work_order_completion` -> 该函数独立检查每个阶段的所有角色是否都已最终提交 -> 如果是，则将对应阶段的 `_completed` 字段在数据库中更新为1。**

所以，并不是每次提交都会更新这些字段，而是只有当一次提交**恰好是某个阶段完成所需的最后一次“最终提交”**时，`check_work_order_completion` 函数的检查条件才会满足，从而触发对 `quality_inspection_work_orders` 表的更新。


好的，我们来深入分析一下这个核心逻辑。

将 `1` (即 `True`) 写入 `quality_inspection_work_orders` 表中 `*_stage_completed` 字段的逻辑，是由后端的 `routes/quality_inspection.py` 文件中的一个关键函数 **`check_work_order_completion`** 负责的。这个过程并非每次提交都会发生，而是有严格的触发条件和执行逻辑。

### 核心逻辑总结

一个阶段（如 `assembly_stage_completed`）被标记为完成（设置为`1`），必须满足以下 **唯一且必要** 的条件：

> **该阶段下的所有三个角色（首检、自检、IPQC）都必须已经完成了“最终提交”。**

只要有一个角色没有进行“最终提交”，该阶段的完成状态就不会被更新为`1`。

---
详细解释02：

### 详细执行逻辑分析

整个执行流程可以分为“触发点”和“执行体”两部分。

#### 1. 触发点：`save_inspection_records` 函数

所有逻辑的起点是前端点击“提交”按钮后调用的 `save_inspection_records` API。在这个函数内部，系统会检查前端传递过来的一个关键标志位：`is_final_submission`。

`check_work_order_completion` 函数 **只在 `is_final_submission` 为 `True` 的情况下才会被调用**。这意味着，只有当用户执行的是“最终提交”操作时，系统才会去检查是否要更新阶段的完成状态。部分提交或临时保存不会触发这个逻辑。

这是 `save_inspection_records` 函数中的关键代码：

```python
# routes/quality_inspection.py, 约 line 454

# ... (代码前面部分保存了检验记录) ...

# 5. 更新工单状态
if is_final_submission:  # <-- 关键触发条件
    work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()
    if work_order and work_order.status == 'pending':
        work_order.status = 'processing'

    # 检查是否所有阶段都已完成
    check_work_order_completion(session, work_order_id) # <-- 调用执行体

# 6. 提交事务
session.commit()
```

#### 2. 执行体：`check_work_order_completion` 函数

这个函数是实现您所提问题的核心，其执行逻辑非常严谨，可以分解为以下几个步骤：

**步骤 1：定义完成标准**
函数首先定义了所有必须完成的阶段和角色。

```python
# routes/quality_inspection.py, 约 line 489
required_stages = ['assembly', 'test', 'packaging']
required_roles = ['first', 'self', 'ipqc']
```

**步骤 2：收集“最终提交”的证据**
函数会查询 `product_inspection_status` 表，找出当前工单下 **所有** `submission_type` 为 `'final'` 的记录。它不关心是哪个产品，只关心哪些 `(阶段, 角色)` 组合已经有了最终提交。

```python
# routes/quality_inspection.py, 约 line 499
final_submissions = session.query(
    ProductInspectionStatus.stage,
    ProductInspectionStatus.inspector_role,
    # ...
).filter(
    ProductInspectionStatus.work_order_id == work_order_id,
    ProductInspectionStatus.submission_type == 'final'  # <-- 只查询最终提交的记录
).all()

# 然后将结果存入一个集合，例如 completed_combinations = {('assembly', 'first'), ('assembly', 'self'), ...}
```

**步骤 3：逐个阶段进行裁定**
这是最核心的判断逻辑。函数会遍历 `required_stages` (`['assembly', 'test', 'packaging']`)，并对每一个阶段进行裁定：

*   **对于 `assembly` 阶段**：它会检查 `completed_combinations` 集合中是否 **同时包含** `('assembly', 'first')`, `('assembly', 'self')` 和 `('assembly', 'ipqc')` 这三个元素。
*   **对于 `test` 阶段**：它会检查是否 **同时包含** `('test', 'first')`, `('test', 'self')` 和 `('test', 'ipqc')`。
*   **对于 `packaging` 阶段**：同理。

如果一个阶段的所有三个角色都满足了“最终提交”的条件，那么这个阶段就被判定为“已完成”。

**步骤 4：更新数据库字段**
在裁定完成后，函数会根据判定结果，更新 `QualityInspectionWorkOrder` 对象的相应字段。

```python
# routes/quality_inspection.py, 约 line 527
for stage, info in stages.items():
    current_value = getattr(work_order, info['field']) # info['field'] 是 'assembly_stage_completed' 等字段名
    
    # 如果该阶段被判定为完成，并且当前数据库值不为True
    if info['completed'] and not current_value:
        setattr(work_order, info['field'], True) # <-- 在这里将字段设置为 True (即 1)
        status_changed = True
        logger.info(f"工单 {work_order_id} 阶段 '{stage}' 标记为完成")

# ... 之后调用 session.commit() 将更改保存到数据库
```

通过 `setattr(work_order, 'assembly_stage_completed', True)` 这样的动态赋值方式，最终将 `1` 写入了数据库中对应的字段。

### 总结
简单来说，执行逻辑链是：
**用户点击“最终提交”** → 前端发送 `is_final_submission: true` → 后端 `save_inspection_records` 接收到请求 → 调用 `check_work_order_completion` → 该函数检查是否某个阶段（如`assembly`）下的三个角色（`first`, `self`, `ipqc`）都已最终提交 → 如果是，则将 `assembly_stage_completed` 字段更新为`1`。