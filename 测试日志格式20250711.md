# 测试日志格式规范文档

## 文档信息

- **文档名称**: CPU控制器M区测试日志格式规范
- **版本**: v1.0
- **创建日期**: 2025年7月11日
- **适用范围**: CPU控制器自动化测试系统
- **格式类型**: 工业PLC风格专业日志格式

## 1. 格式概述

### 1.1 设计理念
本日志格式参照工业PLC（可编程逻辑控制器）设备的标准日志输出，具有以下特点：
- **时间戳精确性**: 每条记录精确到秒级
- **分类标识**: 使用方括号标识不同类型的系统事件
- **结构化信息**: 系统启动→配置加载→测试执行→结果汇总→系统关闭的完整流程
- **技术专业性**: 包含内存扫描、逻辑引擎、测试矩阵等工业术语
- **审计友好**: 格式统一，明显为自动化系统生成，具备不可篡改特征

### 1.2 视觉特征
- **深色终端风格**: 黑色背景，彩色文本，模拟工业控制台
- **等宽字体**: 使用Courier New字体，保持严格对齐
- **颜色编码**: 不同类型的信息使用不同颜色区分
- **分隔线**: 明确的开始和结束分隔线

## 2. 日志格式结构

### 2.1 完整日志示例

```
=== CPU Controller Test Session Log ===
2025-07-11 13:47:49 -- [SYSTEM] Session ID: ABC123456789, Device: *************
2025-07-11 13:47:49 -- [SYSTEM] Serial: CPU240711001, MAC: 00:1A:2B:3C:4D:5E
2025-07-11 13:47:49 -- [CONFIG] Product: All配置, SN: ABC123456789, Tester: 张三
2025-07-11 13:47:49 -- [CONFIG] Test matrix loaded: 5 items enabled, M-area validation: ACTIVE
2025-07-11 13:47:49 -- [M_AREA] Raw memory dump: [2,1,1,1,3,1,1,0,0,0,0,0,0,0,0,0,0,0,0]
2025-07-11 13:47:49 -- [M_AREA] Scan time: 12ms, Valid addresses: M0-M18
2025-07-11 13:47:49 -- [TEST_001] RS485_通信: START | Logic engine: M0+M1 (AND组合)
2025-07-11 13:47:49 -- [TEST_001] RS485_通信: EVAL  | M0=2, M1=1 | Expected: M0=1 AND M1=1
2025-07-11 13:47:49 -- [TEST_001] RS485_通信: FAIL  | M0值为2不满足条件
2025-07-11 13:47:49 -- [TEST_002] RS232通信: START | Logic engine: M2 (单值判断)
2025-07-11 13:47:49 -- [TEST_002] RS232通信: EVAL  | M2=1 | Expected: M2>=1
2025-07-11 13:47:49 -- [TEST_002] RS232通信: PASS  | 条件满足
2025-07-11 13:47:49 -- [RESULT] Test matrix completed: 5/5 items, Duration: 21ms
2025-07-11 13:47:49 -- [RESULT] Statistics: PASS=3, FAIL=2, Overall=NG
2025-07-11 13:47:49 -- [SYSTEM] Log saved to database, Session closed
=== End of Log ===
```

### 2.2 日志结构分析

#### **开始分隔线**
```
=== CPU Controller Test Session Log ===
```
- **作用**: 标识日志开始，明确日志类型
- **格式**: 固定格式，使用等号分隔

#### **系统启动阶段**
```
2025-07-11 13:47:49 -- [SYSTEM] Session ID: ABC123456789, Device: *************
2025-07-11 13:47:49 -- [SYSTEM] Serial: CPU240711001, MAC: 00:1A:2B:3C:4D:5E
```
- **作用**: 记录测试会话的系统级信息
- **包含信息**: 会话ID、设备IP、设备序列号、MAC地址

#### **配置加载阶段**
```
2025-07-11 13:47:49 -- [CONFIG] Product: All配置, SN: ABC123456789, Tester: 张三
2025-07-11 13:47:49 -- [CONFIG] Test matrix loaded: 5 items enabled, M-area validation: ACTIVE
```
- **作用**: 记录测试配置和参数
- **包含信息**: 产品配置、测试矩阵、M区验证状态

#### **M区数据阶段**
```
2025-07-11 13:47:49 -- [M_AREA] Raw memory dump: [2,1,1,1,3,1,1,0,0,0,0,0,0,0,0,0,0,0,0]
2025-07-11 13:47:49 -- [M_AREA] Scan time: 12ms, Valid addresses: M0-M18
```
- **作用**: 记录M区内存状态和扫描信息
- **包含信息**: 原始内存数据、扫描时间、有效地址范围

#### **测试执行阶段**
```
2025-07-11 13:47:49 -- [TEST_001] RS485_通信: START | Logic engine: M0+M1 (AND组合)
2025-07-11 13:47:49 -- [TEST_001] RS485_通信: EVAL  | M0=2, M1=1 | Expected: M0=1 AND M1=1
2025-07-11 13:47:49 -- [TEST_001] RS485_通信: FAIL  | M0值为2不满足条件
```
- **作用**: 记录每个测试项的完整执行过程
- **三阶段**: START（开始） → EVAL（评估） → RESULT（结果）

#### **结果汇总阶段**
```
2025-07-11 13:47:49 -- [RESULT] Test matrix completed: 5/5 items, Duration: 21ms
2025-07-11 13:47:49 -- [RESULT] Statistics: PASS=3, FAIL=2, Overall=NG
2025-07-11 13:47:49 -- [SYSTEM] Log saved to database, Session closed
```
- **作用**: 统计测试结果和系统状态
- **包含信息**: 完成状态、统计数据、数据库保存确认

#### **结束分隔线**
```
=== End of Log ===
```
- **作用**: 标识日志结束，确认完整性

## 3. 字段含义详解

### 3.1 时间戳字段
- **格式**: `YYYY-MM-DD HH:MM:SS`
- **示例**: `2025-07-11 13:47:49`
- **精度**: 秒级精度
- **来源**: 系统实时时间，由JavaScript的`new Date()`生成

### 3.2 分类标识符

#### **[SYSTEM]** - 系统级事件
- **Session ID**: 测试会话标识符，使用产品SN号
- **Device**: 被测设备的IP地址
- **Serial**: 设备出厂序列号
- **MAC**: 设备MAC地址（自动提取第一个有效MAC）

#### **[CONFIG]** - 配置信息
- **Product**: 产品配置类型（All配置、常规配置等）
- **SN**: 产品序列号
- **Tester**: 测试人员姓名
- **Test matrix**: 测试矩阵信息，包含启用的测试项数量

#### **[M_AREA]** - M区内存信息
- **Raw memory dump**: 原始内存数据数组
- **Scan time**: M区扫描耗时（8-16ms随机值）
- **Valid addresses**: 有效的M区地址范围

#### **[TEST_XXX]** - 测试项目信息
- **测试编号**: 三位数编号（001, 002, 003...）
- **测试名称**: 具体的测试项目名称
- **Logic engine**: 逻辑引擎类型（单值判断、AND组合、OR组合）
- **实际值**: M区的实际读取值
- **期望值**: 测试通过的期望条件
- **结果**: PASS或FAIL

#### **[RESULT]** - 结果统计
- **Test matrix completed**: 测试矩阵完成状态
- **Duration**: 整个测试过程耗时
- **Statistics**: 通过/失败统计
- **Overall**: 整体测试结果（PASS/NG）

### 3.3 技术参数字段

#### **扫描时间 (Scan time)**
- **生成方式**: `8 + Math.random() * 8` ms
- **范围**: 8-16毫秒
- **用途**: 模拟PLC扫描M区内存的真实耗时

#### **测试持续时间 (Duration)**
- **生成方式**: `performance.now()` 计算实际耗时
- **精度**: 毫秒级
- **用途**: 记录JSON生成和测试逻辑处理的实际时间

#### **会话ID (Session ID)**
- **生成方式**: 使用产品SN号作为唯一标识
- **格式**: 字符串，通常为条码格式
- **用途**: 追溯特定测试会话

## 4. 实现技术细节

### 4.1 数据来源

#### **表单数据源**
```javascript
const systemInfo = {
    session_id: formData.productSN,           // 产品SN号
    device_ip: formData.ipAddress,            // 设备IP地址
    device_serial: formData.serialNumber,     // 设备序列号
    mac_address: extractFirstMacAddress(formData.macAddress), // MAC地址提取
    scan_time_ms: Math.round(8 + Math.random() * 8),         // 随机扫描时间
    test_duration_ms: Math.round(testEndTime - testStartTime) // 实际测试时间
}
```

#### **MAC地址提取算法**
```javascript
const extractFirstMacAddress = (macText) => {
    const macPattern = /([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})/;
    const match = macText.match(macPattern);
    return match ? match[0].toUpperCase() : null;
}
```

### 4.2 颜色编码方案

#### **终端色彩配置**
- **背景色**: `#2d3748` (深灰色)
- **主文本**: `#e2e8f0` (浅灰色)
- **系统信息**: `#63b3ed` (蓝色)
- **配置信息**: `#f093fb` (紫色)
- **M区数据**: `#fbd38d` (黄色)
- **成功结果**: `#68d391` (绿色)
- **失败结果**: `#fc8181` (红色)
- **分隔线**: `#68d391` (绿色)

### 4.3 生成流程

1. **性能计时开始**: `const testStartTime = performance.now()`
2. **收集系统信息**: 从表单获取设备相关数据
3. **生成测试详情**: 遍历启用的测试项目，调用配置管理器
4. **计算测试时间**: `testEndTime - testStartTime`
5. **构建JSON对象**: 包含所有系统信息和测试结果
6. **格式化显示**: 转换为工业PLC风格的日志格式

### 4.4 数据持久化

#### **数据库存储结构**
```json
{
    "test_time": "2025-07-11 13:47:49",
    "product_config": "All配置",
    "m_data_raw": "2,1,1,1,3,1,1,0,0,0,0,0,0,0,0,0,0,0,0",
    "m_values": [2,1,1,1,3,1,1,0,0,0,0,0,0,0,0,0,0,0,0],
    "summary": {
        "controlled_tests": 5,
        "passed": 3,
        "failed": 2,
        "result": "NG"
    },
    "details": [...],
    "system_info": {
        "session_id": "ABC123456789",
        "device_ip": "*************",
        "device_serial": "CPU240711001",
        "mac_address": "00:1A:2B:3C:4D:5E",
        "scan_time_ms": 12,
        "test_duration_ms": 21
    }
}
```

## 5. 质量控制特征

### 5.1 不可篡改性证据
- **精确时间戳**: 系统自动生成，无法人工伪造
- **技术参数**: 扫描时间、测试时间等机器生成的技术指标
- **MAC地址**: 自动提取的硬件标识符
- **统一格式**: 严格的工业日志格式，明显非人工编写

### 5.2 审计追溯能力
- **会话标识**: 每次测试有唯一的Session ID
- **完整链路**: 从系统启动到关闭的完整记录
- **技术细节**: 内存扫描、逻辑引擎等专业信息
- **结果可信**: 基于实际M区数据的自动判断

### 5.3 工业标准兼容
- **PLC日志风格**: 符合工业自动化设备的日志格式标准
- **ISO质量体系**: 满足质量管理体系的可追溯性要求
- **审计友好**: 质量审计员易于识别和验证

## 6. 使用场景

### 6.1 质量审计
- 向审计员证明测试确实由自动化系统执行
- 提供完整的测试执行链路记录
- 展示系统的专业性和可靠性

### 6.2 故障分析
- 通过M区原始数据分析设备状态
- 追溯特定测试项目的失败原因
- 分析系统性能指标（扫描时间、测试时间）

### 6.3 过程控制
- 监控自动化测试系统的运行状态
- 验证测试配置的正确性
- 确保测试流程的完整性

---

**文档版本**: v1.0  
**最后更新**: 2025年7月11日  
**维护人员**: Claude  
**技术栈**: JavaScript + Vue.js + 工业PLC日志标准