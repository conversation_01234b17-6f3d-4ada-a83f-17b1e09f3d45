/* 
 * 公共CSS变量系统 - 基于CouplerVue.css标准
 * 命名空间: module-* 避免全局污染
 * 版本: v1.0
 * 最后更新: 2024-12-19
 */

/* === 模块级CSS变量容器 - 严格命名空间，避免全局污染 === */
.module-theme-container {
    /* === 字体系统变量 - 基于CouplerVue标准 === */
    --module-font-size-2xl: 1.5rem;      /* 24px - 系统主标题 */
    --module-font-size-xl: 1.25rem;      /* 20px - 功能标题 */
    --module-font-size-lg: 1.125rem;     /* 18px - 子标题 */
    --module-font-size-base: 1rem;       /* 16px - 中间层级 */
    --module-font-size-sm: 0.875rem;     /* 14px - 正文内容 */
    --module-font-size-xs: 0.8125rem;    /* 13px - 辅助信息和操作按钮 */
    --module-font-size-2xs: 0.75rem;     /* 12px - 最小文字 */
    
    /* === 科技感配色变量 - 基于CouplerVue标准 === */
    --module-accent-cyan: #00f5ff;       /* 电子青 */
    --module-accent-green: #00ff88;      /* 电子绿 */
    --module-accent-purple: #8b5cf6;     /* 科技紫 */
    --module-status-pending: #a78bfa;    /* 等待状态 */
    --module-status-processing: #fbbf24; /* 处理状态 */
    
    /* === 深色主题下的增强配色 === */
    --module-accent-cyan-dark: #00d4ff;  /* 深色主题电子青 */
    --module-accent-green-dark: #00f5a0; /* 深色主题电子绿 */
    
    /* === 视觉层次变量 - 光效系统 === */
    --module-shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
    --module-shadow-intense: 0 8px 32px rgba(59, 130, 246, 0.4);
    --module-border-glow: rgba(59, 130, 246, 0.6);
    --module-shadow-glow-dark: 0 0 20px rgba(0, 212, 255, 0.4);
    --module-shadow-intense-dark: 0 8px 32px rgba(0, 212, 255, 0.5);
    --module-border-glow-dark: rgba(0, 212, 255, 0.7);
    
    /* === 主题颜色变量 - 浅色主题 === */
    --module-bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);
    --module-bg-overlay: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(139, 92, 246, 0.05) 50%, rgba(236, 72, 153, 0.05) 100%);
    --module-card-bg: rgba(255, 255, 255, 0.8);
    --module-card-border: rgba(37, 99, 235, 0.2);
    --module-card-hover-border: rgba(37, 99, 235, 0.4);
    --module-card-hover-shadow: rgba(37, 99, 235, 0.2);
    --module-text-primary: #1f2937;
    --module-text-secondary: #6b7280;
    --module-text-tertiary: #9ca3af;
    --module-toolbar-bg: rgba(255, 255, 255, 0.9);
    --module-toolbar-border: rgba(37, 99, 235, 0.2);
    --module-separator-color: rgba(37, 99, 235, 0.2);
    --module-accent-blue: #2563eb;
    --module-accent-blue-light: rgba(37, 99, 235, 0.1);
}

/* === 深色主题变量覆盖 === */
[data-theme="dark"] .module-theme-container {
    /* 深色主题背景 */
    --module-bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
    --module-bg-overlay: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 50%, rgba(236, 72, 153, 0.1) 100%);
    --module-card-bg: rgba(30, 41, 59, 0.8);
    --module-card-border: rgba(59, 130, 246, 0.3);
    --module-card-hover-border: rgba(59, 130, 246, 0.4);
    --module-card-hover-shadow: rgba(59, 130, 246, 0.3);
    --module-text-primary: #ffffff;
    --module-text-secondary: #d1d5db;
    --module-text-tertiary: #9ca3af;
    --module-toolbar-bg: rgba(30, 41, 59, 0.9);
    --module-toolbar-border: rgba(59, 130, 246, 0.3);
    --module-separator-color: rgba(59, 130, 246, 0.2);
    --module-accent-blue: #3b82f6;
    --module-accent-blue-light: rgba(59, 130, 246, 0.1);
    
    /* 深色主题增强配色 */
    --module-accent-cyan: var(--module-accent-cyan-dark);
    --module-accent-green: var(--module-accent-green-dark);
    --module-shadow-glow: var(--module-shadow-glow-dark);
    --module-shadow-intense: var(--module-shadow-intense-dark);
    --module-border-glow: var(--module-border-glow-dark);
}

/* === 变量映射系统 - 保持现有命名，避免破坏性修改 === */

/* CouplerVue变量映射 */
.coupler-controller__main {
    /* 继承公共主题容器 - CSS原生方式 */
    
    /* 字体系统映射 */
    --coupler-font-size-2xl: var(--module-font-size-2xl);
    --coupler-font-size-xl: var(--module-font-size-xl);
    --coupler-font-size-lg: var(--module-font-size-lg);
    --coupler-font-size-base: var(--module-font-size-base);
    --coupler-font-size-sm: var(--module-font-size-sm);
    --coupler-font-size-xs: var(--module-font-size-xs);
    --coupler-font-size-2xs: var(--module-font-size-2xs);
    
    /* 配色系统映射 */
    --coupler-accent-cyan: var(--module-accent-cyan);
    --coupler-accent-green: var(--module-accent-green);
    --coupler-accent-purple: var(--module-accent-purple);
    --coupler-status-pending: var(--module-status-pending);
    --coupler-status-processing: var(--module-status-processing);
    
    /* 光效系统映射 */
    --coupler-shadow-glow: var(--module-shadow-glow);
    --coupler-shadow-intense: var(--module-shadow-intense);
    --coupler-border-glow: var(--module-border-glow);
    
    /* 主题系统映射 */
    --coupler-bg-primary: var(--module-bg-primary);
    --coupler-bg-overlay: var(--module-bg-overlay);
    --coupler-card-bg: var(--module-card-bg);
    --coupler-card-border: var(--module-card-border);
    --coupler-card-hover-border: var(--module-card-hover-border);
    --coupler-card-hover-shadow: var(--module-card-hover-shadow);
    --coupler-text-primary: var(--module-text-primary);
    --coupler-text-secondary: var(--module-text-secondary);
    --coupler-text-tertiary: var(--module-text-tertiary);
    --coupler-toolbar-bg: var(--module-toolbar-bg);
    --coupler-toolbar-border: var(--module-toolbar-border);
    --coupler-separator-color: var(--module-separator-color);
    --coupler-accent-blue: var(--module-accent-blue);
    --coupler-accent-blue-light: var(--module-accent-blue-light);
}

/* IOModuleVue变量映射 */
.io-module__main {
    /* 继承公共主题容器 - CSS原生方式 */
    
    /* 字体系统映射 */
    --io-font-size-2xl: var(--module-font-size-2xl);
    --io-font-size-xl: var(--module-font-size-xl);
    --io-font-size-lg: var(--module-font-size-lg);
    --io-font-size-base: var(--module-font-size-base);
    --io-font-size-sm: var(--module-font-size-sm);
    --io-font-size-xs: var(--module-font-size-xs);
    --io-font-size-2xs: var(--module-font-size-2xs);
    
    /* 配色系统映射 */
    --io-accent-cyan: var(--module-accent-cyan);
    --io-accent-green: var(--module-accent-green);
    --io-accent-purple: var(--module-accent-purple);
    --io-status-pending: var(--module-status-pending);
    --io-status-processing: var(--module-status-processing);
    
    /* 光效系统映射 */
    --io-shadow-glow: var(--module-shadow-glow);
    --io-shadow-intense: var(--module-shadow-intense);
    --io-border-glow: var(--module-border-glow);
    
    /* 主题系统映射 */
    --io-bg-primary: var(--module-bg-primary);
    --io-bg-overlay: var(--module-bg-overlay);
    --io-card-bg: var(--module-card-bg);
    --io-card-border: var(--module-card-border);
    --io-card-hover-border: var(--module-card-hover-border);
    --io-card-hover-shadow: var(--module-card-hover-shadow);
    --io-text-primary: var(--module-text-primary);
    --io-text-secondary: var(--module-text-secondary);
    --io-text-tertiary: var(--module-text-tertiary);
    --io-toolbar-bg: var(--module-toolbar-bg);
    --io-toolbar-border: var(--module-toolbar-border);
    --io-separator-color: var(--module-separator-color);
    --io-accent-blue: var(--module-accent-blue);
    --io-accent-blue-light: var(--module-accent-blue-light);
    
    /* IO模块特有的RGB变量 */
    --io-accent-cyan-rgb: 0, 245, 255;
    --io-accent-blue-rgb: 59, 130, 246;
    --io-accent-purple-rgb: 139, 92, 246;
}

/* CPUControllerVue变量映射 */
.cpu-controller__main {
    /* 继承公共主题容器 - CSS原生方式 */
    
    /* 字体系统映射 */
    --cpu-font-size-2xl: var(--module-font-size-2xl);
    --cpu-font-size-xl: var(--module-font-size-xl);
    --cpu-font-size-lg: var(--module-font-size-lg);
    --cpu-font-size-base: var(--module-font-size-base);
    --cpu-font-size-sm: var(--module-font-size-sm);
    --cpu-font-size-xs: var(--module-font-size-xs);
    --cpu-font-size-2xs: var(--module-font-size-2xs);
    
    /* 配色系统映射 */
    --cpu-accent-cyan: var(--module-accent-cyan);
    --cpu-accent-green: var(--module-accent-green);
    --cpu-accent-purple: var(--module-accent-purple);
    --cpu-status-pending: var(--module-status-pending);
    --cpu-status-processing: var(--module-status-processing);
    
    /* 光效系统映射 */
    --cpu-shadow-glow: var(--module-shadow-glow);
    --cpu-shadow-intense: var(--module-shadow-intense);
    --cpu-border-glow: var(--module-border-glow);
    
    /* 主题系统映射 */
    --cpu-bg-primary: var(--module-bg-primary);
    --cpu-bg-overlay: var(--module-bg-overlay);
    --cpu-card-bg: var(--module-card-bg);
    --cpu-card-border: var(--module-card-border);
    --cpu-card-hover-border: var(--module-card-hover-border);
    --cpu-card-hover-shadow: var(--module-card-hover-shadow);
    --cpu-text-primary: var(--module-text-primary);
    --cpu-text-secondary: var(--module-text-secondary);
    --cpu-text-tertiary: var(--module-text-tertiary);
    --cpu-toolbar-bg: var(--module-toolbar-bg);
    --cpu-toolbar-border: var(--module-toolbar-border);
    --cpu-separator-color: var(--module-separator-color);
    --cpu-accent-blue: var(--module-accent-blue);
    --cpu-accent-blue-light: var(--module-accent-blue-light);
}

/* === 深色主题映射覆盖 === */
[data-theme="dark"] .io-module__main {
    --io-accent-cyan-rgb: 0, 212, 255;
    --io-accent-blue-rgb: 59, 130, 246;
    --io-accent-purple-rgb: 139, 92, 246;
    --io-accent-green-rgb: 0, 245, 160;
} 