from flask import Blueprint, jsonify, request, send_file
from database.db_manager import DatabaseManager
from sqlalchemy import or_, and_, text, literal, union_all, desc, distinct
from sqlalchemy.orm import aliased
from models.test_result import CPUTest, CouplerTest
from models.shipment import Order, SNRecord
from models.assembly import BoardAssembly, CompleteProduct
import logging
import json
from io import BytesIO
from datetime import datetime
import re
from functools import cmp_to_key # 导入用于自定义排序

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
after_sales_query_bp = Blueprint('after_sales_query', __name__)

# 新增函数：根据组合条件获取 SN 列表
def get_sn_list_by_combined_criteria(session, product_code, software_version, pcba_sn_part):
    sn_set = set()

    # 1. 根据产品编码/软件版本查询 SN
    if product_code or software_version:
        try:
            cpu_query = session.query(CPUTest.pro_sn)
            coupler_query = session.query(CouplerTest.pro_sn)

            if product_code:
                cpu_query = cpu_query.filter(CPUTest.pro_code.like(f'%{product_code}%'))
                coupler_query = coupler_query.filter(CouplerTest.pro_code.like(f'%{product_code}%'))
            if software_version:
                cpu_query = cpu_query.filter(CPUTest.sw_version.like(f'%{software_version}%'))
                coupler_query = coupler_query.filter(CouplerTest.couplersw_version.like(f'%{software_version}%'))

            # 使用 union 获取唯一的 SN
            # distinct() 放在 union() 之后，确保最终结果唯一
            product_info_query = cpu_query.union(coupler_query).distinct()
            results = product_info_query.all()
            count_product_info = 0
            for res in results:
                if res.pro_sn:
                    sn_set.add(res.pro_sn)
                    count_product_info += 1
            logger.info(f"通过产品信息找到 {count_product_info} 个唯一 SN") # 使用计数器
        except Exception as e:
            logger.error(f"根据产品信息查询 SN 失败: {str(e)}", exc_info=True)
            # 查询失败不应阻止其他查询，仅记录错误

    # 2. 根据 PCBA 板号部分内容查询 SN
    if pcba_sn_part:
        try:
            # 对于板子序列号的查询采用精确的包含匹配方式
            # 使用原生SQL的INSTR函数或数据库比较函数来提高性能
            # 也可用特定列LIKE、参数位置匹配等方法，但这里使用的字符串查找最通用
            
            # 1. 为了防止SQL注入，将用户输入的特殊字符转义处理
            # 去除输入中可能的转义和SQL通配符字符
            safe_pcba_sn_part = re.sub(r'[\\%_]', lambda m: '\\' + m.group(0), pcba_sn_part)
            
            logger.info(f"使用包含匹配查询PCBA板号，查询值: {safe_pcba_sn_part}")
            
            # 2. 直接查询BoardAssembly表和CompleteProduct表的关联
            # 使用子查询先找匹配的assembly_id，再通过join找对应的产品SN
            try:
                # 先查找包含指定PCBA板号的板子组合记录
                board_assemblies = session.query(BoardAssembly.assembly_id).filter(
                    or_(
                        BoardAssembly.board_a_sn.like(f'%{safe_pcba_sn_part}%'),
                        BoardAssembly.board_b_sn.like(f'%{safe_pcba_sn_part}%'),
                        BoardAssembly.board_c_sn.like(f'%{safe_pcba_sn_part}%'),
                        BoardAssembly.board_d_sn.like(f'%{safe_pcba_sn_part}%')
                    )
                ).limit(1000)  # 限制结果数量
                
                # 提取assembly_id列表
                assembly_ids = [row[0] for row in board_assemblies.all()]
                
                logger.info(f"通过PCBA板号查询找到 {len(assembly_ids)} 个匹配的assembly_id")
                
                # 如果没有找到匹配的assembly_id，直接返回
                if not assembly_ids:
                    logger.info("未找到匹配的板子组合记录")
                    return list(sn_set)
                
                # 根据assembly_id查询对应的产品SN
                complete_products = session.query(CompleteProduct.product_sn).filter(
                    CompleteProduct.assembly_id.in_(assembly_ids)
                ).all()
                
                # 提取product_sn并添加到结果集
                for row in complete_products:
                    product_sn = row[0]  # 直接使用索引访问值
                    if product_sn:
                        sn_set.add(product_sn)
                
                logger.info(f"通过PCBA板号匹配找到 {len(complete_products)} 个产品SN")
                
            except Exception as e:
                logger.error(f"查询板子组合关联产品SN失败: {str(e)}", exc_info=True)
                # 使用第二种方式重试
                
        except Exception as e:
            logger.error(f"根据PCBA板号查询SN失败: {str(e)}", exc_info=True)
            # 查询失败不应阻止其他查询，仅记录错误

    logger.info(f"合并后共找到 {len(sn_set)} 个唯一 SN")
    return list(sn_set)

# 新增函数：根据 SN 列表获取产品详细信息
def get_product_details_for_sns(session, sn_list):
    products_dict = {}
    if not sn_list:
        return products_dict

    valid_sn_list = [sn for sn in sn_list if sn]
    if not valid_sn_list:
        return products_dict

    try:
        # CPU 信息查询
        cpu_query = session.query(
            CPUTest.pro_sn.label('serialNumber'),
            CPUTest.pro_code.label('productCode'),
            CPUTest.pro_model.label('productModel'),
            CPUTest.sw_version.label('softwareVersion'),
            literal('CPU').label('productType')
        ).filter(CPUTest.pro_sn.in_(valid_sn_list))

        # 耦合器/IO 信息查询
        coupler_query = session.query(
            CouplerTest.pro_sn.label('serialNumber'),
            CouplerTest.pro_code.label('productCode'),
            CouplerTest.pro_model.label('productModel'),
            CouplerTest.couplersw_version.label('softwareVersion'),
            CouplerTest.product_type.label('productType')
        ).filter(CouplerTest.pro_sn.in_(valid_sn_list))

        # 合并结果 (这里假设一个 SN 只会明确对应一种类型，如果一个 SN 同时出现在两表需要额外处理)
        # 使用 union_all 因为我们是按 SN 分组，重复来自不同表的相同 SN 理论上不应发生或需要业务定义
        # 但为了安全起见，我们在 Python 端处理覆盖
        combined_query = cpu_query.union_all(coupler_query)
        results = combined_query.all()

        for result in results:
            sn = result.serialNumber
            # 如果一个 SN 来自多个查询，后来的会覆盖先来的 (可以根据业务调整)
            products_dict[sn] = {
                'serialNumber': sn,
                'productCode': result.productCode,
                'productModel': result.productModel,
                'softwareVersion': result.softwareVersion,
                'productType': result.productType
            }
        logger.info(f"为 {len(products_dict)} 个 SN 获取了产品详情")
        return products_dict

    except Exception as e:
        logger.error(f"为 SN 列表获取产品详情失败: {str(e)}", exc_info=True)
        return {} # 返回空字典

# 新增：自定义排序比较函数
def safe_compare(a, b):
    """ 安全比较两个值，处理 None 和不同类型 """
    # 将布尔值转换为整数处理
    if isinstance(a, bool): a = int(a)
    if isinstance(b, bool): b = int(b)

    a_is_none = a is None or a == '' or a == '-' or a == '无' # 扩展None的判断
    b_is_none = b is None or b == '' or b == '-' or b == '无'

    if a_is_none and b_is_none:
        return 0
    if a_is_none:
        return -1 # None 值排在前面
    if b_is_none:
        return 1 # None 值排在前面

    # 尝试日期时间比较
    if isinstance(a, str) and isinstance(b, str):
        try:
            dt_a = datetime.fromisoformat(a.replace(' ', 'T'))
            dt_b = datetime.fromisoformat(b.replace(' ', 'T'))
            if dt_a < dt_b: return -1
            if dt_a > dt_b: return 1
            return 0
        except ValueError:
            pass # 不是有效的日期时间格式，继续按字符串比较

    # 尝试数值比较
    try:
        num_a = float(a)
        num_b = float(b)
        if num_a < num_b: return -1
        if num_a > num_b: return 1
        return 0
    except (ValueError, TypeError):
        pass # 不是数值，继续按字符串比较

    # 默认按字符串比较
    try:
        if str(a) < str(b): return -1
        if str(a) > str(b): return 1
        return 0
    except Exception:
        return 0 # 无法比较时认为相等

@after_sales_query_bp.route('/search', methods=['GET'])
def search_after_sales():
    try:
        # 获取查询参数
        product_code = request.args.get('productCode', '').strip()
        software_version = request.args.get('softwareVersion', '').strip()
        pcba_order_number = request.args.get('pcbaOrderNumber', '').strip()
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        # 获取排序参数
        sort_field = request.args.get('sortField', 'outboundTime') # 默认按出库时间
        sort_order = request.args.get('sortOrder', 'desc')       # 默认降序

        # 记录查询参数
        logger.info(f"售后查询参数: productCode={product_code}, softwareVersion={software_version}, pcbaOrderNumber={pcba_order_number}, page={page}, pageSize={page_size}, sortField={sort_field}, sortOrder={sort_order}")

        # 检查是否至少有一个查询条件
        if not product_code and not software_version and not pcba_order_number:
            return jsonify({
                'success': True,
                'data': [],
                'total': 0
            })

        db = DatabaseManager()
        with db.get_session() as session:
            # 步骤 1: 根据所有条件获取最终的 SN 列表
            combined_sn_list = get_sn_list_by_combined_criteria(session, product_code, software_version, pcba_order_number)

            if not combined_sn_list:
                return jsonify({
                    'success': True,
                    'data': [],
                    'total': 0
                })

            # 步骤 2: 获取这些 SN 的产品基础信息
            products_dict = get_product_details_for_sns(session, combined_sn_list)

            # 步骤 3: 查询产品的出货信息
            shipment_dict = get_shipment_info(session, combined_sn_list)

            # 步骤 4: 查询产品的组装信息
            assembly_dict = get_assembly_details(session, combined_sn_list)

            # 步骤 5: 组合数据
            result_data = combine_product_shipment_data(products_dict, shipment_dict, assembly_dict)

            # 新增：后端排序逻辑
            if sort_field and sort_field in result_data[0]: # 检查字段是否存在于结果中
                try:
                    # 使用自定义比较函数进行排序
                    result_data.sort(
                        key=cmp_to_key(lambda item1, item2: safe_compare(item1.get(sort_field), item2.get(sort_field))),
                        reverse=(sort_order == 'desc')
                    )
                    logger.info(f"数据已按 {sort_field} {sort_order} 排序")
                except Exception as e:
                    logger.error(f"后端排序失败: {str(e)}", exc_info=True)
                    # 排序失败，返回未排序的数据或错误
                    pass # 继续执行，返回未排序的数据

            # 步骤 6: 处理分页
            total = len(result_data)
            start_idx = (page - 1) * page_size
            end_idx = min(start_idx + page_size, total)

            paged_data = result_data[start_idx:end_idx]

            return jsonify({
                'success': True,
                'data': paged_data,
                'total': total
            })

    except Exception as e:
        logger.error(f"售后查询失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500

@after_sales_query_bp.route('/export', methods=['GET'])
def export_after_sales():
    try:
        # 获取查询参数
        product_code = request.args.get('productCode', '').strip()
        software_version = request.args.get('softwareVersion', '').strip()
        pcba_order_number = request.args.get('pcbaOrderNumber', '').strip() # 现在作为 PCBA SN 部分查询条件

        # 获取选择的记录ID
        selected_ids_str = request.args.get('selectedIds', '')
        selected_ids = []

        if selected_ids_str:
            try:
                selected_ids = json.loads(selected_ids_str)
                logger.info(f"导出已选择的记录: {len(selected_ids)} 条")
            except json.JSONDecodeError:
                logger.error("解析选择的记录ID失败")

        # 检查是否至少有一个查询条件 (导出时也需要)
        if not product_code and not software_version and not pcba_order_number: # 修改验证逻辑
             return jsonify({
                 'success': False,
                 'message': '请至少输入产品编码、软件版本或PCBA板号作为查询条件才能导出' # 修改提示信息
             }), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 步骤 1: 根据所有条件获取最终的 SN 列表
            combined_sn_list = get_sn_list_by_combined_criteria(session, product_code, software_version, pcba_order_number)

            if not combined_sn_list:
                return jsonify({
                    'success': False,
                    'message': '没有找到匹配的记录，无法导出'
                }), 404

            # 步骤 2: 获取这些 SN 的产品基础信息
            products_dict = get_product_details_for_sns(session, combined_sn_list)

            # 步骤 3: 查询产品的出货信息
            shipment_dict = get_shipment_info(session, combined_sn_list)

            # 步骤 4: 查询产品的组装信息 (获取 board_a_sn 用于显示)
            assembly_dict = get_assembly_details(session, combined_sn_list)

            # 步骤 5: 组合数据 (此函数不变, 之前已修改)
            all_data = combine_product_shipment_data(products_dict, shipment_dict, assembly_dict)

            # 如果选择了特定记录，则进行过滤
            if selected_ids:
                filtered_data = [record for record in all_data if record.get('id') in selected_ids]
                if not filtered_data:
                    logger.warning("根据选择的ID未找到匹配记录，将导出所有查询结果")
                    export_data = all_data
                else:
                    export_data = filtered_data
            else:
                export_data = all_data

            # 检查最终是否有数据可导出
            if not export_data:
                 return jsonify({
                    'success': False,
                    'message': '没有数据可导出'
                }), 404

            # 创建Excel文件
            try:
                import xlsxwriter

                # 创建BytesIO对象
                output = BytesIO()
                workbook = xlsxwriter.Workbook(output)
                worksheet = workbook.add_worksheet("售后记录")

                # 定义表头样式
                header_format = workbook.add_format({
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1,
                    'bg_color': '#D9E1F2',
                    'text_wrap': True
                })

                # 定义数据样式
                data_format = workbook.add_format({
                    'align': 'left',
                    'valign': 'vcenter',
                    'border': 1
                })
                center_format = workbook.add_format({
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1
                })

                # 写入表头 (表头在上次修改中已更新)
                headers = ['序号', 'SN号', '产品编码', '产品型号', '软件版本',
                           '板A_SN(原PCBA工单号列)', '是否出库', '客户名称', '出库时间']

                for col, header in enumerate(headers):
                    worksheet.write(0, col, header, header_format)

                # 写入数据 (写入逻辑不变)
                for row, record in enumerate(export_data, start=1):
                    is_out_of_stock = "是" if record.get('isOutOfStock', False) else "否"
                    worksheet.write(row, 0, row, center_format)
                    worksheet.write(row, 1, record.get('serialNumber', ''), data_format)
                    worksheet.write(row, 2, record.get('productCode', ''), data_format)
                    worksheet.write(row, 3, record.get('productModel', ''), data_format)
                    worksheet.write(row, 4, record.get('softwareVersion', ''), data_format)
                    worksheet.write(row, 5, record.get('pcbaOrderNumber', '无'), data_format)
                    worksheet.write(row, 6, is_out_of_stock, center_format)
                    worksheet.write(row, 7, record.get('customerName', ''), data_format)
                    worksheet.write(row, 8, record.get('outboundTime', ''), data_format)

                # 调整列宽 (列宽在上次修改中已调整)
                column_widths = [8, 25, 20, 20, 15, 25, 10, 25, 20]
                for col, width in enumerate(column_widths):
                    worksheet.set_column(col, col, width)

                # 关闭工作簿
                workbook.close()

                # 设置文件指针到开始位置
                output.seek(0)

                # 生成文件名
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                filename = f"售后查询记录_{timestamp}.xlsx"

                return send_file(
                    output,
                    mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    as_attachment=True,
                    download_name=filename
                )

            except ImportError:
                logger.error("缺少xlsxwriter模块，无法创建Excel文件")
                return jsonify({
                    'success': False,
                    'message': '导出失败，服务器缺少必要的模块'
                }), 500

    except Exception as e:
        logger.error(f"导出售后记录失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500

def get_shipment_info(session, sn_list):
    """获取指定SN号列表的出货信息"""
    try:
        # 过滤SN列表，移除None和空值
        valid_sn_list = [sn for sn in sn_list if sn]

        if not valid_sn_list:
            return {}

        # 查询出货信息
        shipment_query = session.query(
            SNRecord.sn_number,
            Order.customer_name,
            Order.created_at
        ).join(
            Order, Order.order_id == SNRecord.order_id
        ).filter(
            SNRecord.sn_number.in_(valid_sn_list)
        )

        # 获取查询结果
        shipment_results = shipment_query.all()

        # 创建出货信息字典
        shipment_dict = {}
        for result in shipment_results:
            shipment_dict[result.sn_number] = {
                'customerName': result.customer_name,
                'outboundTime': result.created_at.strftime('%Y-%m-%d %H:%M:%S') if result.created_at else None
            }

        logger.info(f"找到出货记录 {len(shipment_dict)} 条")
        return shipment_dict

    except Exception as e:
        logger.error(f"查询出货信息失败: {str(e)}", exc_info=True)
        raise

def get_assembly_details(session, sn_list):
    """获取指定SN列表的组装信息(特别是board_a_sn)"""
    assembly_dict = {}
    if not sn_list:
        return assembly_dict

    valid_sn_list = [sn for sn in sn_list if sn]
    if not valid_sn_list:
        return assembly_dict

    try:
        # 查询 CompleteProduct (外壳) 并连接 BoardAssembly (板子)
        assembly_query = session.query(
            CompleteProduct.product_sn,  # 外壳SN (即产品最终SN)
            BoardAssembly.board_a_sn     # 板A的SN
        ).join(
            BoardAssembly, CompleteProduct.assembly_id == BoardAssembly.assembly_id
        ).filter(
            CompleteProduct.product_sn.in_(valid_sn_list) # 用产品最终SN作为查询条件
        )

        assembly_results = assembly_query.all()

        # 构建以产品SN为键，board_a_sn为值的字典
        for product_sn, board_a_sn in assembly_results:
            assembly_dict[product_sn] = board_a_sn

        logger.info(f"找到组装记录 {len(assembly_dict)} 条 (基于外壳SN)")
        return assembly_dict

    except Exception as e:
        logger.error(f"查询组装信息失败: {str(e)}", exc_info=True)
        # 在查询失败时返回空字典，避免中断主流程
        return {}

def combine_product_shipment_data(products_dict, shipment_dict, assembly_dict):
    """组合产品信息和出货信息"""
    result_data = []

    # 记录已处理的SN号，避免重复
    processed_sn = set()

    # 遍历产品信息
    for sn, product_info in products_dict.items():
        # 跳过已处理的SN
        if sn in processed_sn:
            continue

        processed_sn.add(sn)

        # 从组装信息字典中获取 board_a_sn，如果找不到则默认为 '无'
        board_a_sn = assembly_dict.get(sn, '无')

        # 创建完整的产品记录
        record = {
            'id': hash(sn) % 100000,  # 生成一个简单的ID用于前端识别
            'serialNumber': sn,
            'productCode': product_info.get('productCode', ''),
            'productModel': product_info.get('productModel', ''),
            'softwareVersion': product_info.get('softwareVersion', ''),
            'pcbaOrderNumber': board_a_sn,
        }

        # 添加出货信息
        if sn in shipment_dict:
            shipment_info = shipment_dict[sn]
            record['isOutOfStock'] = True
            record['customerName'] = shipment_info.get('customerName', '')
            record['outboundTime'] = shipment_info.get('outboundTime', '')
        else:
            record['isOutOfStock'] = False
            record['customerName'] = ''
            record['outboundTime'] = ''

        result_data.append(record)

    logger.info(f"组合后的记录数: {len(result_data)}")
    return result_data 